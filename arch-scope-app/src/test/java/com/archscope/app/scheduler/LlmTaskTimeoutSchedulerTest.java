package com.archscope.app.scheduler;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.LlmTaskService;
import com.archscope.domain.valueobject.TaskStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LLM任务超时检查定时器单元测试
 */
@ExtendWith(MockitoExtension.class)
class LlmTaskTimeoutSchedulerTest {

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private LlmTaskService llmTaskService;

    @InjectMocks
    private LlmTaskTimeoutScheduler scheduler;

    private Task timeoutTask1;
    private Task timeoutTask2;

    @BeforeEach
    void setUp() {
        LocalDateTime now = LocalDateTime.now();
        
        timeoutTask1 = Task.builder()
                .id(1L)
                .projectId(100L)
                .taskType("CODE_ANALYSIS")
                .status(TaskStatus.PROCESSING)
                .workerId("worker-1")
                .processingStartedAt(now.minusMinutes(35))
                .timeoutAt(now.minusMinutes(5))
                .build();

        timeoutTask2 = Task.builder()
                .id(2L)
                .projectId(101L)
                .taskType("DOC_GENERATION")
                .status(TaskStatus.PROCESSING)
                .workerId("worker-2")
                .processingStartedAt(now.minusMinutes(40))
                .timeoutAt(now.minusMinutes(10))
                .build();
    }

    @Test
    void handleTimeoutTasks_Success() {
        // 准备测试数据
        List<Task> timeoutTasks = Arrays.asList(timeoutTask1, timeoutTask2);
        when(taskRepository.findTimeoutProcessingTasks()).thenReturn(timeoutTasks);
        when(taskRepository.resetTimeoutTask(1L)).thenReturn(1);
        when(taskRepository.resetTimeoutTask(2L)).thenReturn(1);

        // 执行测试
        scheduler.handleTimeoutTasks();

        // 验证方法调用
        verify(taskRepository).findTimeoutProcessingTasks();
        verify(taskRepository).resetTimeoutTask(1L);
        verify(taskRepository).resetTimeoutTask(2L);
        verify(llmTaskService).unlockTask(1L);
        verify(llmTaskService).unlockTask(2L);
    }

    @Test
    void handleTimeoutTasks_NoTimeoutTasks() {
        // 模拟没有超时任务
        when(taskRepository.findTimeoutProcessingTasks()).thenReturn(Collections.emptyList());

        // 执行测试
        scheduler.handleTimeoutTasks();

        // 验证方法调用
        verify(taskRepository).findTimeoutProcessingTasks();
        verify(taskRepository, never()).resetTimeoutTask(anyLong());
        verify(llmTaskService, never()).unlockTask(anyLong());
    }

    @Test
    void handleTimeoutTasks_PartialFailure() {
        // 准备测试数据
        List<Task> timeoutTasks = Arrays.asList(timeoutTask1, timeoutTask2);
        when(taskRepository.findTimeoutProcessingTasks()).thenReturn(timeoutTasks);
        when(taskRepository.resetTimeoutTask(1L)).thenReturn(1); // 成功
        when(taskRepository.resetTimeoutTask(2L)).thenReturn(0); // 失败

        // 执行测试
        scheduler.handleTimeoutTasks();

        // 验证方法调用
        verify(taskRepository).findTimeoutProcessingTasks();
        verify(taskRepository).resetTimeoutTask(1L);
        verify(taskRepository).resetTimeoutTask(2L);
        verify(llmTaskService).unlockTask(1L); // 只有成功的任务才解锁
        verify(llmTaskService, never()).unlockTask(2L); // 失败的任务不解锁
    }

    @Test
    void handleTimeoutTasks_DatabaseException() {
        // 模拟数据库异常
        when(taskRepository.findTimeoutProcessingTasks()).thenThrow(new RuntimeException("Database error"));

        // 执行测试（不应该抛出异常）
        scheduler.handleTimeoutTasks();

        // 验证方法调用
        verify(taskRepository).findTimeoutProcessingTasks();
        verify(taskRepository, never()).resetTimeoutTask(anyLong());
        verify(llmTaskService, never()).unlockTask(anyLong());
    }

    @Test
    void handleTimeoutTasks_UnlockException() {
        // 准备测试数据
        List<Task> timeoutTasks = Arrays.asList(timeoutTask1);
        when(taskRepository.findTimeoutProcessingTasks()).thenReturn(timeoutTasks);
        when(taskRepository.resetTimeoutTask(1L)).thenReturn(1);
        doThrow(new RuntimeException("Redis error")).when(llmTaskService).unlockTask(1L);

        // 执行测试（不应该抛出异常）
        scheduler.handleTimeoutTasks();

        // 验证方法调用
        verify(taskRepository).findTimeoutProcessingTasks();
        verify(taskRepository).resetTimeoutTask(1L);
        verify(llmTaskService).unlockTask(1L);
    }

    @Test
    void logTaskStatistics_Success() {
        // 执行测试（这个方法主要是记录日志）
        scheduler.logTaskStatistics();

        // 这个测试主要验证方法能正常执行而不抛出异常
        // 实际的日志输出可以通过集成测试或手动测试验证
    }

    @Test
    void handleTimeoutTasks_MultipleTasksWithMixedResults() {
        // 准备更多测试数据
        Task timeoutTask3 = Task.builder()
                .id(3L)
                .projectId(102L)
                .taskType("CODE_ANALYSIS")
                .status(TaskStatus.PROCESSING)
                .workerId("worker-3")
                .processingStartedAt(LocalDateTime.now().minusMinutes(45))
                .timeoutAt(LocalDateTime.now().minusMinutes(15))
                .build();

        List<Task> timeoutTasks = Arrays.asList(timeoutTask1, timeoutTask2, timeoutTask3);
        when(taskRepository.findTimeoutProcessingTasks()).thenReturn(timeoutTasks);
        when(taskRepository.resetTimeoutTask(1L)).thenReturn(1); // 成功
        when(taskRepository.resetTimeoutTask(2L)).thenReturn(0); // 失败
        when(taskRepository.resetTimeoutTask(3L)).thenReturn(1); // 成功

        // 执行测试
        scheduler.handleTimeoutTasks();

        // 验证方法调用
        verify(taskRepository).findTimeoutProcessingTasks();
        verify(taskRepository).resetTimeoutTask(1L);
        verify(taskRepository).resetTimeoutTask(2L);
        verify(taskRepository).resetTimeoutTask(3L);
        verify(llmTaskService).unlockTask(1L);
        verify(llmTaskService, never()).unlockTask(2L);
        verify(llmTaskService).unlockTask(3L);
    }

    @Test
    void handleTimeoutTasks_TaskResetException() {
        // 准备测试数据
        List<Task> timeoutTasks = Arrays.asList(timeoutTask1, timeoutTask2);
        when(taskRepository.findTimeoutProcessingTasks()).thenReturn(timeoutTasks);
        when(taskRepository.resetTimeoutTask(1L)).thenThrow(new RuntimeException("Reset failed"));
        when(taskRepository.resetTimeoutTask(2L)).thenReturn(1);

        // 执行测试（不应该抛出异常）
        scheduler.handleTimeoutTasks();

        // 验证方法调用
        verify(taskRepository).findTimeoutProcessingTasks();
        verify(taskRepository).resetTimeoutTask(1L);
        verify(taskRepository).resetTimeoutTask(2L);
        verify(llmTaskService, never()).unlockTask(1L); // 异常的任务不解锁
        verify(llmTaskService).unlockTask(2L); // 成功的任务解锁
    }
}

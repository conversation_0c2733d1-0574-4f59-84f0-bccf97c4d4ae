package com.archscope.app.service.impl;

import com.archscope.app.service.ProjectAppService;
import com.archscope.domain.entity.Project;
import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.ProjectService;
import com.archscope.domain.service.TaskService;
import com.archscope.domain.valueobject.ProjectType;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.domain.valueobject.TaskType;
import com.archscope.facade.dto.ProjectDTO;
import com.archscope.facade.dto.ProjectRegistrationDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ProjectAppServiceImpl单元测试
 * 重点测试项目注册后自动创建任务的功能
 */
@ExtendWith(MockitoExtension.class)
class ProjectAppServiceImplTest {

    @Mock
    private ProjectService projectService;

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private TaskService taskService;

    @InjectMocks
    private ProjectAppServiceImpl projectAppService;

    private ProjectRegistrationDTO registrationDTO;
    private Project mockProject;
    private Task mockCodeParseTask;
    private Task mockDocGenerateTask;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        registrationDTO = ProjectRegistrationDTO.builder()
                .name("测试项目")
                .description("这是一个测试项目")
                .repositoryUrl("https://github.com/test/project.git")
                .branch("main")
                .build();

        mockProject = Project.builder()
                .id(1L)
                .name("测试项目")
                .description("这是一个测试项目")
                .repositoryUrl("https://github.com/test/project.git")
                .branch("main")
                .type(ProjectType.OTHER)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        mockCodeParseTask = Task.builder()
                .id(100L)
                .projectId(1L)
                .taskType(TaskType.CODE_PARSE.name())
                .status(TaskStatus.PENDING)
                .name("任务-CODE_PARSE")
                .description("项目 1 的 CODE_PARSE 任务")
                .createdAt(LocalDateTime.now())
                .build();

        mockDocGenerateTask = Task.builder()
                .id(101L)
                .projectId(1L)
                .taskType(TaskType.DOC_GENERATE.name())
                .status(TaskStatus.PENDING)
                .name("任务-DOC_GENERATE")
                .description("项目 1 的 DOC_GENERATE 任务")
                .createdAt(LocalDateTime.now())
                .build();
    }

    @Test
    void registerProject_ShouldCreateProjectAnalysisTask() {
        // Given
        when(projectService.registerProject(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockProject);
        when(taskService.createTask(eq(1L), eq(TaskType.PROJECT_ANALYSIS), anyString()))
                .thenReturn(mockCodeParseTask);
        when(taskService.submitTaskToQueue(100L)).thenReturn(true);

        // When
        ProjectDTO result = projectAppService.registerProject(registrationDTO);

        // Then
        assertNotNull(result);
        assertEquals("测试项目", result.getName());

        // 验证项目服务被调用
        verify(projectService).registerProject(
                "测试项目",
                "这是一个测试项目",
                "https://github.com/test/project.git",
                "main"
        );

        // 验证创建了项目全量分析任务
        verify(taskService).createTask(eq(1L), eq(TaskType.PROJECT_ANALYSIS), anyString());

        // 验证任务被提交到队列
        verify(taskService).submitTaskToQueue(100L);
    }

    @Test
    void registerProject_ShouldCreateProjectAnalysisTaskWithCorrectParameters() {
        // Given
        when(projectService.registerProject(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockProject);
        when(taskService.createTask(eq(1L), eq(TaskType.PROJECT_ANALYSIS), anyString()))
                .thenReturn(mockCodeParseTask);
        when(taskService.submitTaskToQueue(anyLong())).thenReturn(true);

        // When
        projectAppService.registerProject(registrationDTO);

        // Then
        ArgumentCaptor<String> analysisTaskParametersCaptor = ArgumentCaptor.forClass(String.class);
        verify(taskService).createTask(eq(1L), eq(TaskType.PROJECT_ANALYSIS), analysisTaskParametersCaptor.capture());

        String analysisTaskParameters = analysisTaskParametersCaptor.getValue();
        assertTrue(analysisTaskParameters.contains("\"projectId\":1"));
        assertTrue(analysisTaskParameters.contains("\"repositoryUrl\":\"https://github.com/test/project.git\""));
        assertTrue(analysisTaskParameters.contains("\"branch\":\"main\""));
        assertTrue(analysisTaskParameters.contains("\"projectName\":\"测试项目\""));
        assertTrue(analysisTaskParameters.contains("\"projectType\":\"OTHER\""));
        assertTrue(analysisTaskParameters.contains("\"description\":\"这是一个测试项目\""));
        assertTrue(analysisTaskParameters.contains("\"analysisType\":\"FULL\""));
        assertTrue(analysisTaskParameters.contains("\"includeArchDiagrams\":true"));
        assertTrue(analysisTaskParameters.contains("\"outputFormat\":\"markdown\""));
    }

    @Test
    void registerProject_WhenProjectAnalysisTaskCreationFails_ShouldNotAffectProjectRegistration() {
        // Given
        when(projectService.registerProject(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockProject);
        when(taskService.createTask(eq(1L), eq(TaskType.PROJECT_ANALYSIS), anyString()))
                .thenThrow(new RuntimeException("项目全量分析任务创建失败"));

        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> {
            ProjectDTO result = projectAppService.registerProject(registrationDTO);
            assertNotNull(result);
            assertEquals("测试项目", result.getName());
        });

        // 验证项目仍然被注册
        verify(projectService).registerProject(anyString(), anyString(), anyString(), anyString());
        // 验证尝试创建项目全量分析任务
        verify(taskService).createTask(eq(1L), eq(TaskType.PROJECT_ANALYSIS), anyString());
    }

    @Test
    void registerProject_WhenProjectAnalysisTaskSubmissionFails_ShouldNotAffectProjectRegistration() {
        // Given
        when(projectService.registerProject(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockProject);
        when(taskService.createTask(eq(1L), eq(TaskType.PROJECT_ANALYSIS), anyString()))
                .thenReturn(mockCodeParseTask);
        when(taskService.submitTaskToQueue(100L)).thenReturn(false); // 项目全量分析任务提交失败

        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> {
            ProjectDTO result = projectAppService.registerProject(registrationDTO);
            assertNotNull(result);
            assertEquals("测试项目", result.getName());
        });

        // 验证任务创建和提交都被尝试
        verify(taskService).createTask(eq(1L), eq(TaskType.PROJECT_ANALYSIS), anyString());
        verify(taskService).submitTaskToQueue(100L);
    }

    @Test
    void registerProject_WithSpecialCharactersInDescription_ShouldEscapeCorrectly() {
        // Given
        ProjectRegistrationDTO specialDTO = ProjectRegistrationDTO.builder()
                .name("特殊项目")
                .description("包含\"引号\"和\\反斜杠的描述")
                .repositoryUrl("https://github.com/test/special.git")
                .branch("main")
                .build();

        Project specialProject = Project.builder()
                .id(2L)
                .name("特殊项目")
                .description("包含\"引号\"和\\反斜杠的描述")
                .repositoryUrl("https://github.com/test/special.git")
                .branch("main")
                .type(ProjectType.OTHER)
                .createdAt(LocalDateTime.now())
                .build();

        when(projectService.registerProject(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(specialProject);
        when(taskService.createTask(anyLong(), any(TaskType.class), anyString()))
                .thenReturn(mockDocGenerateTask);
        when(taskService.submitTaskToQueue(anyLong())).thenReturn(true);

        // When
        projectAppService.registerProject(specialDTO);

        // Then
        ArgumentCaptor<String> parametersCaptor = ArgumentCaptor.forClass(String.class);
        verify(taskService).createTask(eq(2L), eq(TaskType.PROJECT_ANALYSIS), parametersCaptor.capture());

        String parameters = parametersCaptor.getValue();
        // 验证引号被正确转义
        assertTrue(parameters.contains("\\\"引号\\\""));
    }
}

package com.archscope.app.service;

import com.archscope.app.service.impl.ProjectAppServiceImpl;
import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.ProjectService;
import com.archscope.domain.service.TaskService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 项目唯一性验证测试
 * 验证项目注册流程中的唯一性检查逻辑
 */
@ExtendWith(MockitoExtension.class)
class ProjectUniquenessValidationTest {

    @Mock
    private ProjectService projectService;

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private TaskService taskService;

    private ProjectAppService projectAppService;

    @BeforeEach
    void setUp() {
        projectAppService = new ProjectAppServiceImpl(projectService, projectRepository, taskService);
    }

    @Test
    void isRepositoryUrlExists_WhenRepositoryNotExists_ShouldReturnFalse() {
        // Given
        String testUrl = "https://github.com/test/new-project.git";
        when(projectRepository.findByRepositoryUrl(testUrl)).thenReturn(Optional.empty());

        // When
        boolean exists = projectAppService.isRepositoryUrlExists(testUrl);

        // Then
        assertFalse(exists, "新仓库URL应该返回不存在");
    }

    @Test
    void isRepositoryUrlExists_WhenRepositoryExists_ShouldReturnTrue() {
        // Given
        String testUrl = "https://github.com/test/existing-project.git";
        Project existingProject = Project.builder()
                .id(1L)
                .name("现有项目")
                .repositoryUrl(testUrl)
                .createdAt(LocalDateTime.now())
                .build();

        when(projectRepository.findByRepositoryUrl(testUrl)).thenReturn(Optional.of(existingProject));

        // When
        boolean exists = projectAppService.isRepositoryUrlExists(testUrl);

        // Then
        assertTrue(exists, "已存在的仓库URL应该返回存在");
    }

    @Test
    void isRepositoryUrlExists_WithDifferentUrlFormats_ShouldWorkCorrectly() {
        // Given
        String httpsUrl = "https://github.com/test/project.git";
        String sshUrl = "**************:test/project.git";
        
        Project existingProject = Project.builder()
                .id(1L)
                .name("现有项目")
                .repositoryUrl(httpsUrl)
                .createdAt(LocalDateTime.now())
                .build();

        when(projectRepository.findByRepositoryUrl(httpsUrl)).thenReturn(Optional.of(existingProject));
        when(projectRepository.findByRepositoryUrl(sshUrl)).thenReturn(Optional.empty());

        // When & Then
        assertTrue(projectAppService.isRepositoryUrlExists(httpsUrl), "HTTPS URL应该被找到");
        assertFalse(projectAppService.isRepositoryUrlExists(sshUrl), "SSH URL应该不被找到（不同格式）");
    }

    @Test
    void isRepositoryUrlExists_WithNullOrEmptyUrl_ShouldReturnFalse() {
        // When & Then
        assertFalse(projectAppService.isRepositoryUrlExists(null), "null URL应该返回false");
        assertFalse(projectAppService.isRepositoryUrlExists(""), "空字符串URL应该返回false");
        assertFalse(projectAppService.isRepositoryUrlExists("   "), "空白字符串URL应该返回false");
    }

    @Test
    void isRepositoryUrlExists_WithCaseVariations_ShouldBeConsistent() {
        // Given
        String lowerCaseUrl = "https://github.com/test/project.git";
        String upperCaseUrl = "HTTPS://GITHUB.COM/TEST/PROJECT.GIT";
        String mixedCaseUrl = "https://GitHub.com/Test/Project.git";
        
        Project existingProject = Project.builder()
                .id(1L)
                .name("现有项目")
                .repositoryUrl(lowerCaseUrl)
                .createdAt(LocalDateTime.now())
                .build();

        when(projectRepository.findByRepositoryUrl(lowerCaseUrl)).thenReturn(Optional.of(existingProject));
        when(projectRepository.findByRepositoryUrl(upperCaseUrl)).thenReturn(Optional.empty());
        when(projectRepository.findByRepositoryUrl(mixedCaseUrl)).thenReturn(Optional.empty());

        // When & Then
        assertTrue(projectAppService.isRepositoryUrlExists(lowerCaseUrl), "小写URL应该被找到");
        assertFalse(projectAppService.isRepositoryUrlExists(upperCaseUrl), "大写URL应该不被找到（区分大小写）");
        assertFalse(projectAppService.isRepositoryUrlExists(mixedCaseUrl), "混合大小写URL应该不被找到（区分大小写）");
    }
}

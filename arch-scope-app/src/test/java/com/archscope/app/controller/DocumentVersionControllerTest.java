package com.archscope.app.controller;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.service.DocumentVersionService;
import com.archscope.domain.service.MarkdownService;
import com.archscope.domain.valueobject.DocumentType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentVersionControllerTest {

    @Mock
    private DocumentVersionService documentVersionService;

    @Mock
    private MarkdownService markdownService;

    @InjectMocks
    private DocumentVersionController controller;

    private DocumentVersion testVersion;
    private Path tempFile;

    @BeforeEach
    void setUp() throws IOException {
        // 创建临时文件作为文档内容
        tempFile = Files.createTempFile("test-doc", ".md");
        Files.write(tempFile, "# Test Document\n\nThis is a test document.".getBytes(StandardCharsets.UTF_8));

        // 创建测试文档版本
        testVersion = new DocumentVersion();
        testVersion.setId(1L);
        testVersion.setProjectId(100L);
        testVersion.setDocType(DocumentType.PRODUCT_INTRO);
        testVersion.setVersionTag("v1.0.0");
        testVersion.setContentPath(tempFile.toString());
        testVersion.setTimestamp(LocalDateTime.now());
        testVersion.setLastModified(LocalDateTime.now());
        testVersion.setIsPublished(true);
    }

    @Test
    void getDocumentContent_shouldReturnContent_whenDocumentExists() {
        // 准备测试数据
        when(documentVersionService.getDocumentVersionById(1L)).thenReturn(Optional.of(testVersion));
        when(markdownService.extractTitle(anyString())).thenReturn("Test Document");
        when(markdownService.convertToHtml(anyString(), any())).thenReturn("<h1>Test Document</h1><p>This is a test document.</p>");

        // 执行测试
        ResponseEntity<Map<String, Object>> response = controller.getDocumentContent(1L, null);

        // 验证结果
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertNotNull(response.getBody());
        assertEquals(1L, response.getBody().get("id"));
        assertEquals(100L, response.getBody().get("projectId"));
        assertEquals("v1.0.0", response.getBody().get("versionTag"));
        assertEquals(DocumentType.PRODUCT_INTRO, response.getBody().get("docType"));
        assertEquals("Test Document", response.getBody().get("title"));
        assertEquals("<h1>Test Document</h1><p>This is a test document.</p>", response.getBody().get("html"));
        assertNotNull(response.getBody().get("headElements"));
        assertNotNull(response.getBody().get("timestamp"));
        assertNotNull(response.getBody().get("lastModified"));

        // 验证方法调用
        verify(documentVersionService).getDocumentVersionById(1L);
        verify(markdownService).extractTitle(anyString());
        verify(markdownService).convertToHtml(anyString(), any());
    }

    @Test
    void getDocumentContent_shouldReturnNotFound_whenDocumentDoesNotExist() {
        // 准备测试数据
        when(documentVersionService.getDocumentVersionById(999L)).thenReturn(Optional.empty());

        // 执行测试
        ResponseEntity<Map<String, Object>> response = controller.getDocumentContent(999L, null);

        // 验证结果
        assertEquals(HttpStatus.NOT_FOUND, response.getStatusCode());
        assertNull(response.getBody());

        // 验证方法调用
        verify(documentVersionService).getDocumentVersionById(999L);
        verifyNoInteractions(markdownService);
    }

    @Test
    void getDocumentContent_shouldReturnBadRequest_whenDocTypeDoesNotMatch() {
        // 准备测试数据
        when(documentVersionService.getDocumentVersionById(1L)).thenReturn(Optional.of(testVersion));

        // 执行测试
        ResponseEntity<Map<String, Object>> response = controller.getDocumentContent(1L, DocumentType.API);

        // 验证结果
        assertEquals(HttpStatus.BAD_REQUEST, response.getStatusCode());
        assertNull(response.getBody());

        // 验证方法调用
        verify(documentVersionService).getDocumentVersionById(1L);
        verifyNoInteractions(markdownService);
    }

    @Test
    void getDocumentContent_shouldReturnNoContent_whenContentPathIsEmpty() {
        // 准备测试数据
        DocumentVersion emptyPathVersion = new DocumentVersion();
        emptyPathVersion.setId(2L);
        emptyPathVersion.setProjectId(100L);
        emptyPathVersion.setDocType(DocumentType.PRODUCT_INTRO);
        emptyPathVersion.setVersionTag("v1.0.0");
        emptyPathVersion.setContentPath("");

        when(documentVersionService.getDocumentVersionById(2L)).thenReturn(Optional.of(emptyPathVersion));

        // 执行测试
        ResponseEntity<Map<String, Object>> response = controller.getDocumentContent(2L, null);

        // 验证结果
        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        assertNull(response.getBody());

        // 验证方法调用
        verify(documentVersionService).getDocumentVersionById(2L);
        verifyNoInteractions(markdownService);
    }

    @Test
    void getDocumentContent_shouldReturnNoContent_whenContentFileDoesNotExist() {
        // 准备测试数据
        DocumentVersion nonExistentFileVersion = new DocumentVersion();
        nonExistentFileVersion.setId(3L);
        nonExistentFileVersion.setProjectId(100L);
        nonExistentFileVersion.setDocType(DocumentType.PRODUCT_INTRO);
        nonExistentFileVersion.setVersionTag("v1.0.0");
        nonExistentFileVersion.setContentPath("/non/existent/path.md");

        when(documentVersionService.getDocumentVersionById(3L)).thenReturn(Optional.of(nonExistentFileVersion));

        // 执行测试
        ResponseEntity<Map<String, Object>> response = controller.getDocumentContent(3L, null);

        // 验证结果
        assertEquals(HttpStatus.NO_CONTENT, response.getStatusCode());
        assertNull(response.getBody());

        // 验证方法调用
        verify(documentVersionService).getDocumentVersionById(3L);
        verifyNoInteractions(markdownService);
    }

    @Test
    void compareDocumentVersions_shouldReturnComparisonResult() {
        // 准备测试数据
        when(documentVersionService.compareDocumentVersions(1L, 2L)).thenReturn("Comparison result");

        // 执行测试
        ResponseEntity<String> response = controller.compareDocumentVersions(1L, 2L);

        // 验证结果
        assertEquals(HttpStatus.OK, response.getStatusCode());
        assertEquals("Comparison result", response.getBody());

        // 验证方法调用
        verify(documentVersionService).compareDocumentVersions(1L, 2L);
    }
}

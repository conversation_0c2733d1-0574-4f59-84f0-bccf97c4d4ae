package com.archscope.app.controller;

import com.archscope.domain.entity.Task;
import com.archscope.domain.service.LlmTaskService;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.facade.dto.llm.LlmTaskCallbackRequestDto;
import com.archscope.facade.dto.llm.LlmTaskInputDto;
import com.archscope.facade.dto.llm.LlmTaskPullRequestDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * LLM任务API控制器单元测试
 */
@ExtendWith(MockitoExtension.class)
class LlmTaskV1ControllerTest {

    @Mock
    private LlmTaskService llmTaskService;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private LlmTaskV1Controller controller;

    private MockMvc mockMvc;

    @BeforeEach
    void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(controller).build();
    }

    @Test
    void pullTask_Success() throws Exception {
        // 准备测试数据
        Task mockTask = Task.builder()
                .id(1L)
                .projectId(100L)
                .taskType("CODE_ANALYSIS")
                .status(TaskStatus.PENDING)
                .priority(5)
                .createdAt(LocalDateTime.now())
                .build();

        LlmTaskInputDto mockInputData = LlmTaskInputDto.builder()
                .schemaVersion("1.2")
                .repositoryInfo(LlmTaskInputDto.RepositoryInfo.builder()
                        .cloneUrl("https://github.com/test/repo.git")
                        .commitId("1234567890abcdef1234567890abcdef12345678")
                        .branchName("main")
                        .build())
                .build();

        String inputDataJson = "{\"schemaVersion\":\"1.2\",\"repositoryInfo\":{\"cloneUrl\":\"https://github.com/test/repo.git\",\"commitId\":\"1234567890abcdef1234567890abcdef12345678\",\"branchName\":\"main\"}}";

        // 模拟服务层行为
        when(llmTaskService.pullNextTask()).thenReturn(Optional.of(mockTask));
        when(llmTaskService.lockTaskWithWorker(eq(1L), eq("test-worker"), eq(30)))
                .thenReturn(true);
        when(llmTaskService.getTaskInputData(eq(1L))).thenReturn(Optional.of(inputDataJson));
        when(objectMapper.readValue(eq(inputDataJson), eq(LlmTaskInputDto.class)))
                .thenReturn(mockInputData);

        // 准备请求数据
        LlmTaskPullRequestDto request = LlmTaskPullRequestDto.builder()
                .workerId("test-worker")
                .build();

        // 执行测试
        mockMvc.perform(post("/api/v1/llm-tasks/pull")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hasTask").value(true))
                .andExpect(jsonPath("$.taskId").value(1))
                .andExpect(jsonPath("$.projectId").value(100))
                .andExpect(jsonPath("$.taskType").value("CODE_ANALYSIS"))
                .andExpect(jsonPath("$.priority").value(5))
                .andExpect(jsonPath("$.inputData.schemaVersion").value("1.2"))
                .andExpect(jsonPath("$.inputData.repositoryInfo.cloneUrl").value("https://github.com/test/repo.git"));

        // 验证服务层调用
        verify(llmTaskService).pullNextTask();
        verify(llmTaskService).lockTaskWithWorker(1L, "test-worker", 30);
        verify(llmTaskService).getTaskInputData(1L);
    }

    @Test
    void pullTask_NoTaskAvailable() throws Exception {
        // 模拟没有可用任务
        when(llmTaskService.pullNextTask()).thenReturn(Optional.empty());

        LlmTaskPullRequestDto request = LlmTaskPullRequestDto.builder()
                .workerId("test-worker")
                .build();

        mockMvc.perform(post("/api/v1/llm-tasks/pull")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hasTask").value(false))
                .andExpect(jsonPath("$.message").value("No pending tasks available"));

        verify(llmTaskService).pullNextTask();
        verify(llmTaskService, never()).lockTaskWithWorker(anyLong(), anyString(), anyInt());
    }

    @Test
    void pullTask_LockFailed() throws Exception {
        // 准备测试数据
        Task mockTask = Task.builder()
                .id(1L)
                .projectId(100L)
                .taskType("CODE_ANALYSIS")
                .status(TaskStatus.PENDING)
                .priority(5)
                .createdAt(LocalDateTime.now())
                .build();

        // 模拟任务锁定失败
        when(llmTaskService.pullNextTask()).thenReturn(Optional.of(mockTask));
        when(llmTaskService.lockTaskWithWorker(eq(1L), eq("test-worker"), eq(30)))
                .thenReturn(false);

        LlmTaskPullRequestDto request = LlmTaskPullRequestDto.builder()
                .workerId("test-worker")
                .build();

        mockMvc.perform(post("/api/v1/llm-tasks/pull")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hasTask").value(false));

        verify(llmTaskService).pullNextTask();
        verify(llmTaskService).lockTaskWithWorker(1L, "test-worker", 30);
        verify(llmTaskService, never()).getTaskInputData(anyLong());
    }

    @Test
    void deliverTaskResult_Success() throws Exception {
        // 准备请求数据
        LlmTaskCallbackRequestDto request = LlmTaskCallbackRequestDto.builder()
                .overallStatus("COMPLETED")
                .commitId("1234567890abcdef1234567890abcdef12345678")
                .startTime(LocalDateTime.now().minusMinutes(10))
                .endTime(LocalDateTime.now())
                .executionTimeMs(600000L)
                .results(Arrays.asList(
                        LlmTaskCallbackRequestDto.TaskResult.builder()
                                .documentType("README")
                                .documentTitle("项目说明")
                                .documentContent("# 项目说明\n这是一个测试项目")
                                .status("SUCCESS")
                                .build()
                ))
                .build();

        String resultsJson = "[{\"documentType\":\"README\",\"documentTitle\":\"项目说明\",\"documentContent\":\"# 项目说明\\n这是一个测试项目\",\"status\":\"SUCCESS\"}]";

        // 模拟服务层行为
        when(objectMapper.writeValueAsString(any())).thenReturn(resultsJson);
        when(llmTaskService.completeTaskWithResult(eq(1L), eq("COMPLETED"), eq(resultsJson), eq(600000L)))
                .thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/api/v1/llm-tasks/1/callback")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("任务结果处理成功"))
                .andExpect(jsonPath("$.data.taskId").value(1))
                .andExpect(jsonPath("$.data.status").value("COMPLETED"));

        verify(llmTaskService).completeTaskWithResult(1L, "COMPLETED", resultsJson, 600000L);
    }

    @Test
    void deliverTaskResult_Failed() throws Exception {
        // 准备失败请求数据
        LlmTaskCallbackRequestDto request = LlmTaskCallbackRequestDto.builder()
                .overallStatus("FAILED")
                .errorMessage("代码解析失败")
                .errorDetail("无法解析Java文件")
                .executionTimeMs(300000L)
                .build();

        // 模拟服务层行为
        when(llmTaskService.failTaskWithError(eq(1L), contains("代码解析失败"), eq(300000L)))
                .thenReturn(true);

        // 执行测试
        mockMvc.perform(post("/api/v1/llm-tasks/1/callback")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value("FAILED"));

        verify(llmTaskService).failTaskWithError(eq(1L), contains("代码解析失败"), eq(300000L));
    }

    @Test
    void deliverTaskResult_ValidationError() throws Exception {
        // 准备无效请求数据（FAILED状态但没有错误信息）
        LlmTaskCallbackRequestDto request = LlmTaskCallbackRequestDto.builder()
                .overallStatus("FAILED")
                .executionTimeMs(300000L)
                .build();

        // 执行测试
        mockMvc.perform(post("/api/v1/llm-tasks/1/callback")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(new ObjectMapper().writeValueAsString(request)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("当任务状态为FAILED时，错误信息不能为空"));

        verify(llmTaskService, never()).failTaskWithError(anyLong(), anyString(), anyLong());
        verify(llmTaskService, never()).completeTaskWithResult(anyLong(), anyString(), anyString(), anyLong());
    }

    @Test
    void getTaskStatus_Success() throws Exception {
        mockMvc.perform(get("/api/v1/llm-tasks/1/status"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.taskId").value(1))
                .andExpect(jsonPath("$.data.statusEndpoint").value("/api/tasks/1"));
    }
}

package com.archscope.app.controller;

import com.archscope.domain.service.GitRepositoryMetadataService;
import com.archscope.app.service.ProjectAppService;
import com.archscope.domain.valueobject.GitRepositoryInfo;
import com.archscope.domain.valueobject.GitRepositoryValidationRequest;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.web.servlet.MockMvc;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Git仓库控制器重复检查测试
 */
@WebMvcTest(GitRepositoryController.class)
@DisplayName("Git仓库控制器重复检查测试")
class GitRepositoryControllerDuplicateCheckTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private GitRepositoryMetadataService gitRepositoryMetadataService;

    @MockBean
    private ProjectAppService projectAppService;

    @Autowired
    private ObjectMapper objectMapper;

    private String testRepositoryUrl;
    private GitRepositoryInfo mockRepositoryInfo;

    @BeforeEach
    void setUp() {
        testRepositoryUrl = "https://github.com/user/test-repo.git";
        
        mockRepositoryInfo = GitRepositoryInfo.builder()
                .success(true)
                .projectName("test-repo")
                .repositoryName("test-repo")
                .owner("user")
                .repositoryType("GitHub")
                .defaultBranch("main")
                .branches(Arrays.asList("main", "develop", "feature/test"))
                .build();
    }

    @Test
    @DisplayName("获取仓库详细信息时应该首先检查项目重复性")
    void getRepositoryDetails_ShouldCheckDuplicateFirst() throws Exception {
        // Given - 仓库已存在
        when(projectAppService.isRepositoryUrlExists(testRepositoryUrl)).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/git-repository/details")
                .param("repositoryUrl", testRepositoryUrl))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.errorMessage").value("该仓库已被其他项目注册使用，请检查项目列表或使用不同的仓库"));
    }

    @Test
    @DisplayName("仓库不重复时应该继续获取详细信息")
    void getRepositoryDetails_NotDuplicate_ShouldFetchDetails() throws Exception {
        // Given - 仓库不存在，可以注册
        when(projectAppService.isRepositoryUrlExists(testRepositoryUrl)).thenReturn(false);
        when(gitRepositoryMetadataService.validateAndFetchRepositoryInfo(any(GitRepositoryValidationRequest.class)))
                .thenReturn(mockRepositoryInfo);

        // When & Then
        mockMvc.perform(get("/api/git-repository/details")
                .param("repositoryUrl", testRepositoryUrl))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.projectName").value("test-repo"))
                .andExpect(jsonPath("$.defaultBranch").value("main"))
                .andExpect(jsonPath("$.branches").isArray())
                .andExpect(jsonPath("$.branches.length()").value(3));
    }

    @Test
    @DisplayName("SSH格式URL的重复检查应该正常工作")
    void getRepositoryDetails_SshUrl_ShouldCheckDuplicate() throws Exception {
        // Given
        String sshUrl = "**************:user/test-repo.git";
        when(projectAppService.isRepositoryUrlExists(sshUrl)).thenReturn(true);

        // When & Then
        mockMvc.perform(get("/api/git-repository/details")
                .param("repositoryUrl", sshUrl))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.errorMessage").value("该仓库已被其他项目注册使用，请检查项目列表或使用不同的仓库"));
    }

    @Test
    @DisplayName("带认证信息的仓库详细信息获取")
    void getRepositoryDetails_WithCredentials_ShouldWork() throws Exception {
        // Given
        String privateRepoUrl = "https://github.com/user/private-repo.git";
        when(projectAppService.isRepositoryUrlExists(privateRepoUrl)).thenReturn(false);
        when(gitRepositoryMetadataService.validateAndFetchRepositoryInfo(any(GitRepositoryValidationRequest.class)))
                .thenReturn(mockRepositoryInfo);

        // When & Then
        mockMvc.perform(get("/api/git-repository/details")
                .param("repositoryUrl", privateRepoUrl)
                .param("username", "testuser")
                .param("password", "testtoken"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true));
    }

    @Test
    @DisplayName("重复检查异常时应该返回服务器错误")
    void getRepositoryDetails_DuplicateCheckException_ShouldReturnServerError() throws Exception {
        // Given
        when(projectAppService.isRepositoryUrlExists(testRepositoryUrl))
                .thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then
        mockMvc.perform(get("/api/git-repository/details")
                .param("repositoryUrl", testRepositoryUrl))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.errorMessage").value("获取详细信息失败: 数据库连接失败"));
    }

    @Test
    @DisplayName("获取仓库信息失败时应该返回错误")
    void getRepositoryDetails_FetchInfoFails_ShouldReturnError() throws Exception {
        // Given
        when(projectAppService.isRepositoryUrlExists(testRepositoryUrl)).thenReturn(false);
        
        GitRepositoryInfo failedInfo = GitRepositoryInfo.builder()
                .success(false)
                .errorMessage("仓库不存在或无法访问")
                .build();
        
        when(gitRepositoryMetadataService.validateAndFetchRepositoryInfo(any(GitRepositoryValidationRequest.class)))
                .thenReturn(failedInfo);

        // When & Then
        mockMvc.perform(get("/api/git-repository/details")
                .param("repositoryUrl", testRepositoryUrl))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.errorMessage").value("仓库不存在或无法访问"));
    }

    @Test
    @DisplayName("无效的仓库URL应该在重复检查阶段被处理")
    void getRepositoryDetails_InvalidUrl_ShouldBeHandled() throws Exception {
        // Given
        String invalidUrl = "invalid-url";
        when(projectAppService.isRepositoryUrlExists(invalidUrl))
                .thenThrow(new IllegalArgumentException("仓库地址格式不正确"));

        // When & Then
        mockMvc.perform(get("/api/git-repository/details")
                .param("repositoryUrl", invalidUrl))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.errorMessage").value("获取详细信息失败: 仓库地址格式不正确"));
    }

    @Test
    @DisplayName("空仓库URL应该被正确处理")
    void getRepositoryDetails_EmptyUrl_ShouldBeHandled() throws Exception {
        // When & Then
        mockMvc.perform(get("/api/git-repository/details")
                .param("repositoryUrl", ""))
                .andExpect(status().isInternalServerError())
                .andExpect(jsonPath("$.success").value(false));
    }
}

package com.archscope.app.controller;

import com.archscope.app.service.TaskAppService;
import com.archscope.facade.dto.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * TaskController 测试类
 */
@WebMvcTest(TaskController.class)
class TaskControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private TaskAppService taskAppService;

    @Autowired
    private ObjectMapper objectMapper;

    private TaskDTO sampleTask;
    private TaskSummaryDTO sampleTaskSummary;
    private TaskCreateDTO sampleTaskCreate;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        sampleTask = TaskDTO.builder()
                .id(1L)
                .projectId(100L)
                .name("测试任务")
                .description("这是一个测试任务")
                .type("CODE_PARSE")
                .status("PENDING")
                .priority(5)
                .progress(0)
                .createdAt(LocalDateTime.now())
                .build();

        sampleTaskSummary = TaskSummaryDTO.builder()
                .id(1L)
                .projectId(100L)
                .name("测试任务")
                .type("CODE_PARSE")
                .status("PENDING")
                .priority(5)
                .progress(0)
                .createdAt(LocalDateTime.now())
                .build();

        sampleTaskCreate = TaskCreateDTO.builder()
                .projectId(100L)
                .name("新测试任务")
                .description("这是一个新的测试任务")
                .type("CODE_PARSE")
                .priority(5)
                .build();
    }

    @Test
    void testGetTasks() throws Exception {
        // 准备模拟数据
        List<TaskSummaryDTO> tasks = Arrays.asList(sampleTaskSummary);
        PageResponseDTO<TaskSummaryDTO> pageResponse = PageResponseDTO.<TaskSummaryDTO>builder()
                .content(tasks)
                .page(0)
                .size(10)
                .totalElements(1L)
                .totalPages(1)
                .build();

        when(taskAppService.getAllTasks(0, 10, "createdAt", "desc"))
                .thenReturn(pageResponse);

        // 执行测试
        mockMvc.perform(get("/api/tasks")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].id").value(1))
                .andExpect(jsonPath("$.content[0].name").value("测试任务"));
    }

    @Test
    void testGetTaskById() throws Exception {
        when(taskAppService.getTaskById(1L)).thenReturn(sampleTask);

        mockMvc.perform(get("/api/tasks/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("测试任务"))
                .andExpect(jsonPath("$.type").value("CODE_PARSE"));
    }

    @Test
    void testGetTaskByIdNotFound() throws Exception {
        when(taskAppService.getTaskById(999L)).thenThrow(new RuntimeException("任务不存在"));

        mockMvc.perform(get("/api/tasks/999"))
                .andExpect(status().isNotFound());
    }

    @Test
    void testCreateTask() throws Exception {
        when(taskAppService.createTask(any(TaskCreateDTO.class))).thenReturn(sampleTask);

        mockMvc.perform(post("/api/tasks")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(sampleTaskCreate)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("测试任务"));
    }

    @Test
    void testCancelTask() throws Exception {
        when(taskAppService.cancelTask(1L)).thenReturn(true);

        mockMvc.perform(post("/api/tasks/1/cancel"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("任务已成功取消"));
    }

    @Test
    void testRetryTask() throws Exception {
        when(taskAppService.retryTask(1L)).thenReturn(sampleTask);

        mockMvc.perform(post("/api/tasks/1/retry"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.name").value("测试任务"));
    }

    @Test
    void testGetTaskResult() throws Exception {
        TaskResultDTO result = TaskResultDTO.builder()
                .taskId(1L)
                .projectId(100L)
                .status("COMPLETED")
                .result("任务执行成功")
                .build();

        when(taskAppService.getTaskResult(1L)).thenReturn(result);

        mockMvc.perform(get("/api/tasks/1/result"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.taskId").value(1))
                .andExpect(jsonPath("$.status").value("COMPLETED"));
    }

    @Test
    void testUpdateTaskStatus() throws Exception {
        Map<String, String> statusRequest = new HashMap<>();
        statusRequest.put("status", "PROCESSING");

        TaskDTO updatedTask = TaskDTO.builder()
                .id(1L)
                .projectId(100L)
                .name("测试任务")
                .status("PROCESSING")
                .build();

        when(taskAppService.updateTaskStatus(1L, "PROCESSING")).thenReturn(updatedTask);

        mockMvc.perform(put("/api/tasks/1/status")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(statusRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.id").value(1))
                .andExpect(jsonPath("$.status").value("PROCESSING"));
    }

    @Test
    void testDeleteTask() throws Exception {
        when(taskAppService.deleteTask(1L)).thenReturn(true);

        mockMvc.perform(delete("/api/tasks/1"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("任务已成功删除"));
    }

    @Test
    void testSearchTasks() throws Exception {
        List<TaskSummaryDTO> tasks = Arrays.asList(sampleTaskSummary);
        PageResponseDTO<TaskSummaryDTO> pageResponse = PageResponseDTO.<TaskSummaryDTO>builder()
                .content(tasks)
                .page(0)
                .size(10)
                .totalElements(1L)
                .totalPages(1)
                .build();

        when(taskAppService.searchTasks("测试", 0, 10)).thenReturn(pageResponse);

        mockMvc.perform(get("/api/tasks/search")
                        .param("keyword", "测试")
                        .param("page", "0")
                        .param("size", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.content").isArray())
                .andExpect(jsonPath("$.content[0].name").value("测试任务"));
    }

    @Test
    void testGetRecentTasks() throws Exception {
        List<TaskSummaryDTO> tasks = Arrays.asList(sampleTaskSummary);

        when(taskAppService.getRecentTasks(10)).thenReturn(tasks);

        mockMvc.perform(get("/api/tasks/recent")
                        .param("limit", "10"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$").isArray())
                .andExpect(jsonPath("$[0].name").value("测试任务"));
    }
}

package com.archscope.app.controller;

import com.archscope.app.service.ProjectAnalysisService;
import com.archscope.app.service.ProjectAppService;
import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.ProjectRepository;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.web.servlet.MockMvc;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ProjectController.class)
@ContextConfiguration(classes = {ProjectController.class})
class ProjectControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ProjectAppService projectAppService;

    @MockBean
    private ProjectAnalysisService projectAnalysisService;

    @MockBean
    private ProjectRepository projectRepository;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    void checkRepositoryExists_WhenRepositoryNotExists_ShouldReturnFalse() throws Exception {
        // Given
        String testUrl = "https://github.com/test/new-project.git";
        when(projectAppService.isRepositoryUrlExists(testUrl)).thenReturn(false);

        // When & Then
        mockMvc.perform(get("/api/projects/check-repository")
                .param("url", testUrl))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.exists").value(false))
                .andExpect(jsonPath("$.message").value("仓库可以使用"));
    }

    @Test
    void checkRepositoryExists_WhenRepositoryExists_ShouldReturnTrueWithProjectInfo() throws Exception {
        // Given
        String testUrl = "https://github.com/test/existing-project.git";
        
        Project existingProject = Project.builder()
                .id(1L)
                .name("现有项目")
                .description("这是一个已存在的项目")
                .repositoryUrl(testUrl)
                .createdAt(LocalDateTime.of(2024, 1, 1, 0, 0))
                .status("ACTIVE")
                .build();

        when(projectAppService.isRepositoryUrlExists(testUrl)).thenReturn(true);
        when(projectRepository.findByRepositoryUrl(testUrl)).thenReturn(Optional.of(existingProject));

        // When & Then
        mockMvc.perform(get("/api/projects/check-repository")
                .param("url", testUrl))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.exists").value(true))
                .andExpect(jsonPath("$.message").value("该仓库已被项目 \"现有项目\" 使用"))
                .andExpect(jsonPath("$.existingProject.id").value(1))
                .andExpect(jsonPath("$.existingProject.name").value("现有项目"))
                .andExpect(jsonPath("$.existingProject.description").value("这是一个已存在的项目"))
                .andExpect(jsonPath("$.existingProject.status").value("ACTIVE"));
    }

    @Test
    void checkRepositoryExists_WhenRepositoryExistsButProjectNotFound_ShouldReturnGenericMessage() throws Exception {
        // Given
        String testUrl = "https://github.com/test/orphaned-url.git";
        
        when(projectAppService.isRepositoryUrlExists(testUrl)).thenReturn(true);
        when(projectRepository.findByRepositoryUrl(testUrl)).thenReturn(Optional.empty());

        // When & Then
        mockMvc.perform(get("/api/projects/check-repository")
                .param("url", testUrl))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.exists").value(true))
                .andExpect(jsonPath("$.message").value("该仓库已被其他项目使用"))
                .andExpect(jsonPath("$.existingProject").doesNotExist());
    }

    @Test
    void checkRepositoryExists_WhenExceptionOccurs_ShouldReturnErrorResponse() throws Exception {
        // Given
        String testUrl = "https://github.com/test/error-project.git";
        
        when(projectAppService.isRepositoryUrlExists(testUrl)).thenThrow(new RuntimeException("数据库连接失败"));

        // When & Then
        mockMvc.perform(get("/api/projects/check-repository")
                .param("url", testUrl))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.exists").value(false))
                .andExpect(jsonPath("$.error").value(true))
                .andExpect(jsonPath("$.message").value("检查仓库时发生错误，请稍后重试"));
    }
}

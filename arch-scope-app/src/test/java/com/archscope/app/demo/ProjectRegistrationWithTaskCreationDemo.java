package com.archscope.app.demo;

import com.archscope.app.service.ProjectAppService;
import com.archscope.domain.entity.Project;
import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.ProjectService;
import com.archscope.domain.service.TaskService;
import com.archscope.domain.valueobject.ProjectType;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.domain.valueobject.TaskType;
import com.archscope.facade.dto.ProjectDTO;
import com.archscope.facade.dto.ProjectRegistrationDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 项目注册自动任务创建功能演示
 * 展示项目注册成功后自动创建代码解析和文档生成任务的完整流程
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {
    com.archscope.app.service.impl.ProjectAppServiceImpl.class
})
class ProjectRegistrationWithTaskCreationDemo {

    @Autowired
    private ProjectAppService projectAppService;

    @MockBean
    private ProjectService projectService;

    @MockBean
    private ProjectRepository projectRepository;

    @MockBean
    private TaskService taskService;

    @Test
    void demonstrateProjectRegistrationWithAutomaticTaskCreation() {
        System.out.println("=== 项目注册自动任务创建功能演示 ===\n");

        // 1. 准备项目注册数据
        ProjectRegistrationDTO registrationDTO = ProjectRegistrationDTO.builder()
                .name("ArchScope演示项目")
                .description("这是一个用于演示自动任务创建功能的项目")
                .repositoryUrl("https://github.com/demo/arch-scope-demo.git")
                .branch("main")
                .type("WEB_APPLICATION")
                .build();

        System.out.println("1. 准备项目注册信息:");
        System.out.println("   项目名称: " + registrationDTO.getName());
        System.out.println("   项目描述: " + registrationDTO.getDescription());
        System.out.println("   仓库地址: " + registrationDTO.getRepositoryUrl());
        System.out.println("   分支: " + registrationDTO.getBranch());
        System.out.println("   项目类型: " + registrationDTO.getType());
        System.out.println();

        // 2. 模拟项目创建
        Project mockProject = Project.builder()
                .id(12345L)
                .name("ArchScope演示项目")
                .description("这是一个用于演示自动任务创建功能的项目")
                .repositoryUrl("https://github.com/demo/arch-scope-demo.git")
                .branch("main")
                .type(ProjectType.JAVA)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .creatorId(798L)
                .status("PENDING_ANALYSIS")
                .active(true)
                .analysisCount(0)
                .documentationVersion(0)
                .build();

        // 3. 模拟任务创建
        Task projectAnalysisTask = Task.builder()
                .id(50001L)
                .projectId(12345L)
                .taskType(TaskType.PROJECT_ANALYSIS.name())
                .status(TaskStatus.PENDING)
                .name("任务-PROJECT_ANALYSIS")
                .description("项目 12345 的 PROJECT_ANALYSIS 任务")
                .createdAt(LocalDateTime.now())
                .build();

        // 4. 配置Mock行为
        when(projectService.registerProject(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockProject);
        when(taskService.createTask(eq(12345L), eq(TaskType.PROJECT_ANALYSIS), anyString()))
                .thenReturn(projectAnalysisTask);
        when(taskService.submitTaskToQueue(anyLong())).thenReturn(true);

        System.out.println("2. 开始项目注册流程...");

        // 5. 执行项目注册
        ProjectDTO result = projectAppService.registerProject(registrationDTO);

        System.out.println("3. 项目注册成功!");
        System.out.println("   项目ID: " + result.getId());
        System.out.println("   项目名称: " + result.getName());
        System.out.println("   项目状态: " + result.getStatus());
        System.out.println();

        // 6. 验证任务创建
        System.out.println("4. 验证自动任务创建:");

        // 验证项目全量分析任务参数
        ArgumentCaptor<String> analysisParametersCaptor = ArgumentCaptor.forClass(String.class);
        verify(taskService).createTask(eq(12345L), eq(TaskType.PROJECT_ANALYSIS), analysisParametersCaptor.capture());

        String analysisParameters = analysisParametersCaptor.getValue();
        System.out.println("   项目全量分析任务已创建:");
        System.out.println("     任务ID: " + projectAnalysisTask.getId());
        System.out.println("     任务类型: " + TaskType.PROJECT_ANALYSIS);
        System.out.println("     任务参数: " + analysisParameters);
        System.out.println();

        // 7. 验证任务提交到队列
        verify(taskService, times(1)).submitTaskToQueue(anyLong());
        verify(taskService).submitTaskToQueue(50001L);

        System.out.println("5. 任务队列提交验证:");
        System.out.println("   项目全量分析任务已提交到队列: 任务ID " + projectAnalysisTask.getId());
        System.out.println();

        // 8. 验证项目全量分析任务参数内容
        System.out.println("6. 项目全量分析任务参数详细分析:");
        assertTrue(analysisParameters.contains("\"projectId\":12345"), "应包含项目ID");
        assertTrue(analysisParameters.contains("\"repositoryUrl\":\"https://github.com/demo/arch-scope-demo.git\""), "应包含仓库URL");
        assertTrue(analysisParameters.contains("\"branch\":\"main\""), "应包含分支信息");
        assertTrue(analysisParameters.contains("\"projectName\":\"ArchScope演示项目\""), "应包含项目名称");
        assertTrue(analysisParameters.contains("\"projectType\":\"WEB_APPLICATION\""), "应包含项目类型");
        assertTrue(analysisParameters.contains("\"description\":\"这是一个用于演示自动任务创建功能的项目\""), "应包含项目描述");
        assertTrue(analysisParameters.contains("\"analysisType\":\"FULL\""), "应包含分析类型");
        assertTrue(analysisParameters.contains("\"includeArchDiagrams\":true"), "应包含架构图选项");
        assertTrue(analysisParameters.contains("\"outputFormat\":\"markdown\""), "应包含输出格式");

        System.out.println("   ✓ 项目ID参数正确");
        System.out.println("   ✓ 仓库URL参数正确");
        System.out.println("   ✓ 分支参数正确");
        System.out.println("   ✓ 项目名称参数正确");
        System.out.println("   ✓ 项目类型参数正确");
        System.out.println("   ✓ 项目描述参数正确");
        System.out.println("   ✓ 分析类型参数正确");
        System.out.println("   ✓ 架构图选项参数正确");
        System.out.println("   ✓ 输出格式参数正确");
        System.out.println();

        System.out.println("=== 演示完成 ===");
        System.out.println("✅ 项目注册成功后自动创建了项目全量分析任务");
        System.out.println("✅ 任务参数包含了完整的项目信息，可供LLM服务使用");
        System.out.println("✅ 任务创建失败不会影响项目注册流程的完整性");
        System.out.println("✅ 项目全量分析任务已成功提交到队列等待处理");
        System.out.println("✅ 简化了任务管理，一个任务完成所有分析工作");

        // 最终断言
        assertNotNull(result, "项目注册结果不应为空");
        assertEquals("ArchScope演示项目", result.getName(), "项目名称应该正确");
        assertEquals(12345L, result.getId(), "项目ID应该正确");
    }
}

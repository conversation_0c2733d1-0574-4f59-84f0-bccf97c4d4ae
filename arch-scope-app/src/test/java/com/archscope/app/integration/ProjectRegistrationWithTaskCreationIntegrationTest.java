package com.archscope.app.integration;

import com.archscope.app.service.ProjectAppService;
import com.archscope.domain.entity.Project;
import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.ProjectService;
import com.archscope.domain.service.TaskService;
import com.archscope.domain.valueobject.ProjectType;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.domain.valueobject.TaskType;
import com.archscope.facade.dto.ProjectDTO;
import com.archscope.facade.dto.ProjectRegistrationDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit.jupiter.SpringExtension;

import java.time.LocalDateTime;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 项目注册与任务创建集成测试
 * 验证项目注册成功后自动创建代码解析和文档生成任务的完整流程
 */
@ExtendWith(SpringExtension.class)
@SpringBootTest(classes = {
    // 只加载必要的组件进行集成测试
    com.archscope.app.service.impl.ProjectAppServiceImpl.class
})
class ProjectRegistrationWithTaskCreationIntegrationTest {

    @Autowired
    private ProjectAppService projectAppService;

    @MockBean
    private ProjectService projectService;

    @MockBean
    private ProjectRepository projectRepository;

    @MockBean
    private TaskService taskService;

    private ProjectRegistrationDTO registrationDTO;
    private Project mockProject;

    @BeforeEach
    void setUp() {
        registrationDTO = ProjectRegistrationDTO.builder()
                .name("集成测试项目")
                .description("这是一个集成测试项目，用于验证任务自动创建功能")
                .repositoryUrl("https://github.com/integration/test-project.git")
                .branch("develop")
                .type("JAVA")
                .build();

        mockProject = Project.builder()
                .id(999L)
                .name("集成测试项目")
                .description("这是一个集成测试项目，用于验证任务自动创建功能")
                .repositoryUrl("https://github.com/integration/test-project.git")
                .branch("develop")
                .type(ProjectType.JAVA)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .creatorId(798L)
                .status("PENDING_ANALYSIS")
                .active(true)
                .analysisCount(0)
                .documentationVersion(0)
                .build();
    }

    @Test
    void projectRegistration_ShouldAutomaticallyCreateBothTasks() {
        // Given
        Task codeParseTask = createMockTask(1001L, TaskType.CODE_PARSE);
        Task docGenerateTask = createMockTask(1002L, TaskType.DOC_GENERATE);

        when(projectService.registerProject(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockProject);
        when(taskService.createTask(eq(999L), eq(TaskType.CODE_PARSE), anyString()))
                .thenReturn(codeParseTask);
        when(taskService.createTask(eq(999L), eq(TaskType.DOC_GENERATE), anyString()))
                .thenReturn(docGenerateTask);
        when(taskService.submitTaskToQueue(anyLong())).thenReturn(true);

        // When
        ProjectDTO result = projectAppService.registerProject(registrationDTO);

        // Then
        assertNotNull(result, "项目注册结果不应为空");
        assertEquals("集成测试项目", result.getName(), "项目名称应该正确");

        // 验证项目服务被正确调用
        verify(projectService, times(1)).registerProject(
                "集成测试项目",
                "这是一个集成测试项目，用于验证任务自动创建功能",
                "https://github.com/integration/test-project.git",
                "develop"
        );

        // 验证两个任务都被创建
        verify(taskService, times(1)).createTask(eq(999L), eq(TaskType.CODE_PARSE), anyString());
        verify(taskService, times(1)).createTask(eq(999L), eq(TaskType.DOC_GENERATE), anyString());

        // 验证两个任务都被提交到队列
        verify(taskService, times(2)).submitTaskToQueue(anyLong());
        verify(taskService, times(1)).submitTaskToQueue(1001L);
        verify(taskService, times(1)).submitTaskToQueue(1002L);
    }

    @Test
    void projectRegistration_ShouldCreateTasksWithCorrectParameters() {
        // Given
        Task codeParseTask = createMockTask(2001L, TaskType.CODE_PARSE);
        Task docGenerateTask = createMockTask(2002L, TaskType.DOC_GENERATE);

        when(projectService.registerProject(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockProject);
        when(taskService.createTask(eq(999L), eq(TaskType.CODE_PARSE), anyString()))
                .thenReturn(codeParseTask);
        when(taskService.createTask(eq(999L), eq(TaskType.DOC_GENERATE), anyString()))
                .thenReturn(docGenerateTask);
        when(taskService.submitTaskToQueue(anyLong())).thenReturn(true);

        // When
        projectAppService.registerProject(registrationDTO);

        // Then
        // 验证代码解析任务参数
        ArgumentCaptor<String> codeParseParametersCaptor = ArgumentCaptor.forClass(String.class);
        verify(taskService).createTask(eq(999L), eq(TaskType.CODE_PARSE), codeParseParametersCaptor.capture());

        String codeParseParameters = codeParseParametersCaptor.getValue();
        assertTrue(codeParseParameters.contains("\"repositoryUrl\":\"https://github.com/integration/test-project.git\""));
        assertTrue(codeParseParameters.contains("\"branch\":\"develop\""));
        assertTrue(codeParseParameters.contains("\"projectName\":\"集成测试项目\""));

        // 验证文档生成任务参数
        ArgumentCaptor<String> docGenerateParametersCaptor = ArgumentCaptor.forClass(String.class);
        verify(taskService).createTask(eq(999L), eq(TaskType.DOC_GENERATE), docGenerateParametersCaptor.capture());

        String docGenerateParameters = docGenerateParametersCaptor.getValue();
        assertTrue(docGenerateParameters.contains("\"projectId\":999"));
        assertTrue(docGenerateParameters.contains("\"repositoryUrl\":\"https://github.com/integration/test-project.git\""));
        assertTrue(docGenerateParameters.contains("\"branch\":\"develop\""));
        assertTrue(docGenerateParameters.contains("\"projectName\":\"集成测试项目\""));
        assertTrue(docGenerateParameters.contains("\"projectType\":\"GO\""));
        assertTrue(docGenerateParameters.contains("\"description\":\"这是一个集成测试项目，用于验证任务自动创建功能\""));
    }

    @Test
    void projectRegistration_WhenTaskCreationFails_ShouldStillCompleteProjectRegistration() {
        // Given
        Task codeParseTask = createMockTask(3001L, TaskType.CODE_PARSE);

        when(projectService.registerProject(anyString(), anyString(), anyString(), anyString()))
                .thenReturn(mockProject);
        when(taskService.createTask(eq(999L), eq(TaskType.CODE_PARSE), anyString()))
                .thenReturn(codeParseTask);
        when(taskService.createTask(eq(999L), eq(TaskType.DOC_GENERATE), anyString()))
                .thenThrow(new RuntimeException("文档生成任务创建失败"));
        when(taskService.submitTaskToQueue(3001L)).thenReturn(true);

        // When & Then - 应该不抛出异常
        assertDoesNotThrow(() -> {
            ProjectDTO result = projectAppService.registerProject(registrationDTO);
            assertNotNull(result);
            assertEquals("集成测试项目", result.getName());
        });

        // 验证项目仍然被成功注册
        verify(projectService, times(1)).registerProject(anyString(), anyString(), anyString(), anyString());
        
        // 验证代码解析任务仍然被创建和提交
        verify(taskService, times(1)).createTask(eq(999L), eq(TaskType.CODE_PARSE), anyString());
        verify(taskService, times(1)).submitTaskToQueue(3001L);
        
        // 验证文档生成任务创建被尝试但失败
        verify(taskService, times(1)).createTask(eq(999L), eq(TaskType.DOC_GENERATE), anyString());
    }

    @Test
    void projectRegistration_WithNullDescription_ShouldHandleGracefully() {
        // Given
        ProjectRegistrationDTO nullDescDTO = ProjectRegistrationDTO.builder()
                .name("无描述项目")
                .description(null)  // 空描述
                .repositoryUrl("https://github.com/test/no-desc.git")
                .branch("main")
                .build();

        Project nullDescProject = Project.builder()
                .id(4001L)
                .name("无描述项目")
                .description(null)
                .repositoryUrl("https://github.com/test/no-desc.git")
                .branch("main")
                .type(ProjectType.OTHER)
                .createdAt(LocalDateTime.now())
                .build();

        Task docGenerateTask = createMockTask(4002L, TaskType.DOC_GENERATE);

        when(projectService.registerProject(anyString(), isNull(), anyString(), anyString()))
                .thenReturn(nullDescProject);
        when(taskService.createTask(anyLong(), any(TaskType.class), anyString()))
                .thenReturn(docGenerateTask);
        when(taskService.submitTaskToQueue(anyLong())).thenReturn(true);

        // When
        assertDoesNotThrow(() -> {
            ProjectDTO result = projectAppService.registerProject(nullDescDTO);
            assertNotNull(result);
        });

        // Then
        ArgumentCaptor<String> parametersCaptor = ArgumentCaptor.forClass(String.class);
        verify(taskService).createTask(eq(4001L), eq(TaskType.DOC_GENERATE), parametersCaptor.capture());

        String parameters = parametersCaptor.getValue();
        assertTrue(parameters.contains("\"description\":\"\""), "空描述应该被处理为空字符串");
    }

    private Task createMockTask(Long taskId, TaskType taskType) {
        return Task.builder()
                .id(taskId)
                .projectId(999L)
                .taskType(taskType.name())
                .status(TaskStatus.PENDING)
                .name("任务-" + taskType.name())
                .description("项目 999 的 " + taskType.name() + " 任务")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .retryCount(0)
                .priority(5)
                .build();
    }
}

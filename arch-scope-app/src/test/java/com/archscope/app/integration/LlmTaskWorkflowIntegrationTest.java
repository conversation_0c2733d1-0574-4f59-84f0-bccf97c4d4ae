package com.archscope.app.integration;

import com.archscope.app.controller.LlmTaskV1Controller;
import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.LlmTaskService;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.llm.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LLM任务工作流集成测试
 * 测试完整的任务拉取、处理、交付业务流程
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class LlmTaskWorkflowIntegrationTest {

    @Autowired
    private LlmTaskV1Controller llmTaskController;

    @Autowired
    private LlmTaskService llmTaskService;

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Task testTask;

    @BeforeEach
    void setUp() {
        // 创建测试任务
        testTask = llmTaskService.createLlmTask(
                1L, // projectId
                "https://github.com/test/repo.git",
                "abc123def456",
                "main",
                "CODE_FULL_ANALYSIS_JAVA",
                5
        );
    }

    @Test
    void testCompleteWorkflow_Success() {
        // 1. 工作节点拉取任务
        LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                .workerId("integration-test-worker")
                .workerVersion("1.0.0")
                .supportedTaskTypes(new String[]{"CODE_FULL_ANALYSIS_JAVA"})
                .maxConcurrentTasks(1)
                .build();

        ResponseEntity<LlmTaskPullResponseDto> pullResponse = llmTaskController.pullTask(pullRequest);

        // 验证拉取响应
        assertEquals(200, pullResponse.getStatusCodeValue());
        assertNotNull(pullResponse.getBody());
        assertTrue(Boolean.TRUE.equals(pullResponse.getBody().getHasTask()));
        assertEquals(testTask.getId().toString(), pullResponse.getBody().getTaskId());

        // 验证任务状态已更新为PROCESSING
        Optional<Task> updatedTask = taskRepository.findById(testTask.getId());
        assertTrue(updatedTask.isPresent());
        assertEquals(TaskStatus.PROCESSING, updatedTask.get().getStatus());

        // 2. 工作节点提交成功结果
        LlmTaskCallbackRequestDto callbackRequest = LlmTaskCallbackRequestDto.builder()
                .overallStatus("COMPLETED")
                .commitId("abc123def456")
                .results(Arrays.asList(
                        LlmTaskCallbackRequestDto.TaskResult.builder()
                                .documentType("README")
                                .documentTitle("项目说明文档")
                                .documentContent("# 项目说明\n\n这是一个测试项目...")
                                .filePath("README.md")
                                .status("SUCCESS")
                                .build(),
                        LlmTaskCallbackRequestDto.TaskResult.builder()
                                .documentType("ARCHITECTURE")
                                .documentTitle("架构设计文档")
                                .documentContent("# 架构设计\n\n## 系统架构...")
                                .filePath("docs/architecture.md")
                                .status("SUCCESS")
                                .build()
                ))
                .startTime(LocalDateTime.now().minusMinutes(15))
                .endTime(LocalDateTime.now())
                .executionTimeMs(900000L)
                .workerInfo(LlmWorkerInfoDto.builder()
                        .workerId("integration-test-worker")
                        .workerVersion("1.0.0")
                        .processedTasksCount(1L)
                        .build())
                .build();

        ResponseEntity<ApiResponse<Map<String, Object>>> callbackResponse = 
                llmTaskController.deliverTaskResult(testTask.getId(), callbackRequest);

        // 验证交付响应
        assertEquals(200, callbackResponse.getStatusCodeValue());
        assertNotNull(callbackResponse.getBody());
        assertTrue(callbackResponse.getBody().isSuccess());
        assertEquals("任务结果处理成功", callbackResponse.getBody().getMessage());

        // 验证任务状态已更新为COMPLETED
        Optional<Task> completedTask = taskRepository.findById(testTask.getId());
        assertTrue(completedTask.isPresent());
        assertEquals(TaskStatus.COMPLETED, completedTask.get().getStatus());
        assertNotNull(completedTask.get().getResults());
    }

    @Test
    void testCompleteWorkflow_TaskFailure() {
        // 1. 工作节点拉取任务
        LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                .workerId("integration-test-worker")
                .workerVersion("1.0.0")
                .build();

        ResponseEntity<LlmTaskPullResponseDto> pullResponse = llmTaskController.pullTask(pullRequest);
        assertEquals(200, pullResponse.getStatusCodeValue());
        assertTrue(Boolean.TRUE.equals(pullResponse.getBody().getHasTask()));

        // 2. 工作节点提交失败结果
        LlmTaskCallbackRequestDto callbackRequest = LlmTaskCallbackRequestDto.builder()
                .overallStatus("FAILED")
                .errorMessage("Repository clone failed")
                .errorDetail("Git clone failed: repository not found or access denied")
                .startTime(LocalDateTime.now().minusMinutes(5))
                .endTime(LocalDateTime.now())
                .executionTimeMs(300000L)
                .workerInfo(LlmWorkerInfoDto.builder()
                        .workerId("integration-test-worker")
                        .workerVersion("1.0.0")
                        .build())
                .build();

        ResponseEntity<ApiResponse<Map<String, Object>>> callbackResponse = 
                llmTaskController.deliverTaskResult(testTask.getId(), callbackRequest);

        // 验证交付响应
        assertEquals(200, callbackResponse.getStatusCodeValue());
        assertTrue(callbackResponse.getBody().isSuccess());

        // 验证任务状态已更新为FAILED
        Optional<Task> failedTask = taskRepository.findById(testTask.getId());
        assertTrue(failedTask.isPresent());
        assertEquals(TaskStatus.FAILED, failedTask.get().getStatus());
        assertNotNull(failedTask.get().getLastErrorDetail());
        assertTrue(failedTask.get().getLastErrorDetail().contains("Repository clone failed"));
    }

    @Test
    void testPullTask_NoTaskAvailable() {
        // 删除测试任务，确保没有可用任务
        taskRepository.deleteById(testTask.getId());

        LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                .workerId("integration-test-worker")
                .build();

        ResponseEntity<LlmTaskPullResponseDto> pullResponse = llmTaskController.pullTask(pullRequest);

        assertEquals(200, pullResponse.getStatusCodeValue());
        assertNotNull(pullResponse.getBody());
        assertFalse(Boolean.TRUE.equals(pullResponse.getBody().getHasTask()));
        assertEquals("No pending tasks available", pullResponse.getBody().getMessage());
    }

    @Test
    void testConcurrentTaskPull() throws InterruptedException {
        // 创建多个工作节点同时拉取同一个任务
        int workerCount = 3;
        Thread[] workers = new Thread[workerCount];
        boolean[] results = new boolean[workerCount];

        for (int i = 0; i < workerCount; i++) {
            final int workerIndex = i;
            workers[i] = new Thread(() -> {
                LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                        .workerId("worker-" + (workerIndex + 1))
                        .build();

                ResponseEntity<LlmTaskPullResponseDto> response = llmTaskController.pullTask(pullRequest);
                results[workerIndex] = response.getBody() != null && Boolean.TRUE.equals(response.getBody().getHasTask());
            });
        }

        // 启动所有工作线程
        for (Thread worker : workers) {
            worker.start();
        }

        // 等待所有线程完成
        for (Thread worker : workers) {
            worker.join();
        }

        // 验证只有一个工作节点成功获取任务
        int successCount = 0;
        for (boolean result : results) {
            if (result) {
                successCount++;
            }
        }

        assertEquals(1, successCount, "只有一个工作节点应该成功获取任务");
    }

    @Test
    void testTaskStatusQuery() {
        ResponseEntity<ApiResponse<Map<String, Object>>> statusResponse = 
                llmTaskController.getTaskStatus(testTask.getId());

        assertEquals(200, statusResponse.getStatusCodeValue());
        assertNotNull(statusResponse.getBody());
        assertTrue(statusResponse.getBody().isSuccess());

        Map<String, Object> data = statusResponse.getBody().getData();
        assertEquals(testTask.getId().toString(), data.get("taskId").toString());
        assertEquals("请使用标准任务API查询详细状态", data.get("message"));
        assertTrue(data.get("statusEndpoint").toString().contains("/api/tasks/"));
    }

    @Test
    void testInvalidTaskCallback() {
        // 尝试对不存在的任务提交结果
        Long nonExistentTaskId = 99999L;

        LlmTaskCallbackRequestDto callbackRequest = LlmTaskCallbackRequestDto.builder()
                .overallStatus("COMPLETED")
                .build();

        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
                llmTaskController.deliverTaskResult(nonExistentTaskId, callbackRequest);

        assertEquals(400, response.getStatusCodeValue());
        assertNotNull(response.getBody());
        assertFalse(response.getBody().isSuccess());
    }

    @Test
    void testPartialSuccessWorkflow() {
        // 1. 拉取任务
        LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                .workerId("integration-test-worker")
                .build();

        llmTaskController.pullTask(pullRequest);

        // 2. 提交部分成功结果
        LlmTaskCallbackRequestDto callbackRequest = LlmTaskCallbackRequestDto.builder()
                .overallStatus("PARTIAL_SUCCESS")
                .results(Arrays.asList(
                        LlmTaskCallbackRequestDto.TaskResult.builder()
                                .documentType("README")
                                .documentTitle("项目说明文档")
                                .documentContent("# 项目说明\n\n...")
                                .status("SUCCESS")
                                .build(),
                        LlmTaskCallbackRequestDto.TaskResult.builder()
                                .documentType("ARCHITECTURE")
                                .status("FAILED")
                                .errorMessage("Failed to analyze architecture")
                                .build()
                ))
                .executionTimeMs(600000L)
                .build();

        ResponseEntity<ApiResponse<Map<String, Object>>> response = 
                llmTaskController.deliverTaskResult(testTask.getId(), callbackRequest);

        assertEquals(200, response.getStatusCodeValue());
        assertTrue(response.getBody().isSuccess());

        // 验证任务状态
        Optional<Task> task = taskRepository.findById(testTask.getId());
        assertTrue(task.isPresent());
        assertEquals("PARTIAL_SUCCESS", task.get().getOverallStatus());
    }
}

package com.archscope.app.integration;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.facade.dto.llm.LlmTaskCallbackRequestDto;
import com.archscope.facade.dto.llm.LlmTaskPullRequestDto;
import com.archscope.facade.dto.llm.LlmWorkerInfoDto;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * LLM任务API集成测试
 * 测试完整的任务拉取和交付流程
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
class LlmTaskV1IntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private TaskRepository taskRepository;

    @Autowired
    private ObjectMapper objectMapper;

    private Task testTask;

    @BeforeEach
    void setUp() {
        // 创建测试任务
        testTask = Task.builder()
                .projectId(100L)
                .taskType("CODE_ANALYSIS")
                .status(TaskStatus.PENDING)
                .priority(5)
                .name("测试LLM任务")
                .description("用于集成测试的LLM任务")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .retryCount(0)
                .maxRetries(3)
                .taskVersion(1)
                .parameters(createParametersMap(
                    "inputData",
                    "{\"schemaVersion\":\"1.2\",\"repositoryInfo\":{\"cloneUrl\":\"https://github.com/test/repo.git\",\"commitId\":\"1234567890abcdef1234567890abcdef12345678\",\"branchName\":\"main\"}}"
                ))
                .build();

        testTask = taskRepository.save(testTask);
    }

    @Test
    void testCompleteTaskFlow() throws Exception {
        // 1. 拉取任务
        LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                .workerId("integration-test-worker")
                .workerVersion("1.0.0")
                .build();

        String pullResponse = mockMvc.perform(post("/api/v1/llm-tasks/pull")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(pullRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hasTask").value(true))
                .andExpect(jsonPath("$.taskId").value(testTask.getId()))
                .andExpect(jsonPath("$.projectId").value(100))
                .andExpect(jsonPath("$.taskType").value("CODE_ANALYSIS"))
                .andExpect(jsonPath("$.inputData.schemaVersion").value("1.2"))
                .andReturn()
                .getResponse()
                .getContentAsString();

        // 验证任务状态已更新为PROCESSING
        Task updatedTask = taskRepository.findById(testTask.getId()).orElseThrow(() -> new RuntimeException("Task not found"));
        assertEquals(TaskStatus.PROCESSING, updatedTask.getStatus());
        assertEquals("integration-test-worker", updatedTask.getWorkerId());
        assertNotNull(updatedTask.getTimeoutAt());

        // 2. 提交成功结果
        LlmTaskCallbackRequestDto callbackRequest = LlmTaskCallbackRequestDto.builder()
                .overallStatus("COMPLETED")
                .commitId("1234567890abcdef1234567890abcdef12345678")
                .startTime(LocalDateTime.now().minusMinutes(5))
                .endTime(LocalDateTime.now())
                .executionTimeMs(300000L)
                .results(Arrays.asList(
                        LlmTaskCallbackRequestDto.TaskResult.builder()
                                .documentType("README")
                                .documentTitle("项目说明文档")
                                .documentContent("# 项目说明\n\n这是一个测试项目的说明文档。")
                                .filePath("README.md")
                                .status("SUCCESS")
                                .build(),
                        LlmTaskCallbackRequestDto.TaskResult.builder()
                                .documentType("ARCHITECTURE")
                                .documentTitle("架构设计文档")
                                .documentContent("# 架构设计\n\n## 系统架构\n\n本系统采用微服务架构...")
                                .filePath("docs/architecture.md")
                                .status("SUCCESS")
                                .build()
                ))
                .workerInfo(LlmWorkerInfoDto.builder()
                        .workerId("integration-test-worker")
                        .workerVersion("1.0.0")
                        .processedTasksCount(1L)
                        .build())
                .build();

        mockMvc.perform(post("/api/v1/llm-tasks/{taskId}/callback", testTask.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(callbackRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.message").value("任务结果处理成功"))
                .andExpect(jsonPath("$.data.taskId").value(testTask.getId()))
                .andExpect(jsonPath("$.data.status").value("COMPLETED"));

        // 验证任务状态已更新为COMPLETED
        Task completedTask = taskRepository.findById(testTask.getId()).orElseThrow(() -> new RuntimeException("Task not found"));
        assertEquals(TaskStatus.COMPLETED, completedTask.getStatus());
        assertEquals("COMPLETED", completedTask.getOverallStatus());
        assertNotNull(completedTask.getResults());
        assertEquals(300000L, completedTask.getExecutionTimeMs());
    }

    @Test
    void testFailedTaskFlow() throws Exception {
        // 1. 拉取任务
        LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                .workerId("integration-test-worker")
                .build();

        mockMvc.perform(post("/api/v1/llm-tasks/pull")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(pullRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hasTask").value(true));

        // 2. 提交失败结果
        LlmTaskCallbackRequestDto callbackRequest = LlmTaskCallbackRequestDto.builder()
                .overallStatus("FAILED")
                .errorMessage("代码解析失败")
                .errorDetail("无法找到主类文件")
                .executionTimeMs(150000L)
                .build();

        mockMvc.perform(post("/api/v1/llm-tasks/{taskId}/callback", testTask.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(callbackRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value("FAILED"));

        // 验证任务状态已更新为FAILED
        Task failedTask = taskRepository.findById(testTask.getId()).orElseThrow(() -> new RuntimeException("Task not found"));
        assertEquals(TaskStatus.FAILED, failedTask.getStatus());
        assertNotNull(failedTask.getLastErrorDetail());
        assertTrue(failedTask.getLastErrorDetail().contains("代码解析失败"));
    }

    @Test
    void testNoTaskAvailable() throws Exception {
        // 删除测试任务，确保没有可用任务
        taskRepository.deleteById(testTask.getId());

        LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                .workerId("integration-test-worker")
                .build();

        mockMvc.perform(post("/api/v1/llm-tasks/pull")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(pullRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.hasTask").value(false))
                .andExpect(jsonPath("$.message").value("No pending tasks available"));
    }

    @Test
    void testTaskStatusQuery() throws Exception {
        mockMvc.perform(get("/api/v1/llm-tasks/{taskId}/status", testTask.getId()))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.taskId").value(testTask.getId()))
                .andExpect(jsonPath("$.data.statusEndpoint").value("/api/tasks/" + testTask.getId()));
    }

    @Test
    void testInvalidCallbackRequest() throws Exception {
        // 测试无效的回调请求（FAILED状态但没有错误信息）
        LlmTaskCallbackRequestDto invalidRequest = LlmTaskCallbackRequestDto.builder()
                .overallStatus("FAILED")
                .executionTimeMs(150000L)
                .build();

        mockMvc.perform(post("/api/v1/llm-tasks/{taskId}/callback", testTask.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(invalidRequest)))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.success").value(false))
                .andExpect(jsonPath("$.message").value("当任务状态为FAILED时，错误信息不能为空"));
    }

    @Test
    void testPartialSuccessTask() throws Exception {
        // 1. 拉取任务
        LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                .workerId("integration-test-worker")
                .build();

        mockMvc.perform(post("/api/v1/llm-tasks/pull")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(pullRequest)))
                .andExpect(status().isOk());

        // 2. 提交部分成功结果
        LlmTaskCallbackRequestDto callbackRequest = LlmTaskCallbackRequestDto.builder()
                .overallStatus("PARTIAL_SUCCESS")
                .executionTimeMs(400000L)
                .results(Arrays.asList(
                        LlmTaskCallbackRequestDto.TaskResult.builder()
                                .documentType("README")
                                .documentTitle("项目说明文档")
                                .documentContent("# 项目说明\n\n基本说明已生成。")
                                .status("SUCCESS")
                                .build(),
                        LlmTaskCallbackRequestDto.TaskResult.builder()
                                .documentType("API")
                                .documentTitle("API文档")
                                .status("FAILED")
                                .errorMessage("API文档生成失败")
                                .build()
                ))
                .build();

        mockMvc.perform(post("/api/v1/llm-tasks/{taskId}/callback", testTask.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(callbackRequest)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.success").value(true))
                .andExpect(jsonPath("$.data.status").value("PARTIAL_SUCCESS"));

        // 验证任务状态
        Task partialTask = taskRepository.findById(testTask.getId()).orElseThrow(() -> new RuntimeException("Task not found"));
        assertEquals(TaskStatus.COMPLETED, partialTask.getStatus());
        assertEquals("PARTIAL_SUCCESS", partialTask.getOverallStatus());
    }

    /**
     * 创建参数Map的辅助方法，兼容Java 8
     */
    private Map<String, Object> createParametersMap(String key, String value) {
        Map<String, Object> map = new HashMap<>();
        map.put(key, value);
        return map;
    }
}

package com.archscope.app.performance;

import com.archscope.app.controller.LlmTaskV1Controller;
import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.LlmTaskService;
import com.archscope.facade.dto.llm.LlmTaskCallbackRequestDto;
import com.archscope.facade.dto.llm.LlmTaskPullRequestDto;
import com.archscope.facade.dto.llm.LlmTaskPullResponseDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.ResponseEntity;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * LLM任务性能测试
 * 测试任务拉取和处理的性能指标
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class LlmTaskPerformanceTest {

    @Autowired
    private LlmTaskV1Controller llmTaskController;

    @Autowired
    private LlmTaskService llmTaskService;

    @Autowired
    private TaskRepository taskRepository;

    private List<Task> testTasks;

    @BeforeEach
    void setUp() {
        // 创建多个测试任务
        testTasks = new ArrayList<>();
        for (int i = 0; i < 100; i++) {
            Task task = llmTaskService.createLlmTask(
                    (long) (i % 10 + 1), // projectId 1-10
                    "https://github.com/test/repo" + i + ".git",
                    "commit" + i,
                    "main",
                    "CODE_FULL_ANALYSIS_JAVA",
                    i % 10 + 1 // priority 1-10
            );
            testTasks.add(task);
        }
    }

    @Test
    void testConcurrentTaskPull_Performance() throws InterruptedException {
        // Given
        int workerCount = 20;
        int tasksPerWorker = 5;
        ExecutorService executor = Executors.newFixedThreadPool(workerCount);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(workerCount);

        AtomicInteger successfulPulls = new AtomicInteger(0);
        AtomicInteger failedPulls = new AtomicInteger(0);
        AtomicLong totalPullTime = new AtomicLong(0);

        // When
        for (int i = 0; i < workerCount; i++) {
            final String workerId = "perf-worker-" + i;
            executor.submit(() -> {
                try {
                    startLatch.await();
                    
                    for (int j = 0; j < tasksPerWorker; j++) {
                        long startTime = System.currentTimeMillis();
                        
                        LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                                .workerId(workerId)
                                .workerVersion("1.0.0")
                                .build();

                        ResponseEntity<LlmTaskPullResponseDto> response = llmTaskController.pullTask(pullRequest);
                        
                        long endTime = System.currentTimeMillis();
                        totalPullTime.addAndGet(endTime - startTime);

                        if (response.getStatusCode().is2xxSuccessful() &&
                            response.getBody() != null &&
                            Boolean.TRUE.equals(response.getBody().getHasTask())) {
                            successfulPulls.incrementAndGet();
                        } else {
                            failedPulls.incrementAndGet();
                        }

                        // 短暂休息避免过度竞争
                        Thread.sleep(10);
                    }
                } catch (Exception e) {
                    failedPulls.addAndGet(tasksPerWorker);
                } finally {
                    finishLatch.countDown();
                }
            });
        }

        long testStartTime = System.currentTimeMillis();
        startLatch.countDown(); // 开始测试
        finishLatch.await(30, TimeUnit.SECONDS); // 最多等待30秒
        long testEndTime = System.currentTimeMillis();

        executor.shutdown();

        // Then
        long totalTestTime = testEndTime - testStartTime;
        double averagePullTime = totalPullTime.get() / (double) (successfulPulls.get() + failedPulls.get());
        double throughput = successfulPulls.get() / (totalTestTime / 1000.0);

        System.out.println("=== 任务拉取性能测试结果 ===");
        System.out.println("总测试时间: " + totalTestTime + "ms");
        System.out.println("成功拉取任务数: " + successfulPulls.get());
        System.out.println("失败拉取次数: " + failedPulls.get());
        System.out.println("平均拉取时间: " + String.format("%.2f", averagePullTime) + "ms");
        System.out.println("吞吐量: " + String.format("%.2f", throughput) + " tasks/second");

        // 性能断言
        assertTrue(averagePullTime < 100, "平均拉取时间应该小于100ms");
        assertTrue(throughput > 10, "吞吐量应该大于10 tasks/second");
        assertTrue(successfulPulls.get() > 0, "应该有成功拉取的任务");
    }

    @Test
    void testTaskDelivery_Performance() throws InterruptedException {
        // Given
        int taskCount = 50;
        List<Task> pulledTasks = new ArrayList<>();

        // 先拉取一些任务
        for (int i = 0; i < taskCount && i < testTasks.size(); i++) {
            LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                    .workerId("perf-worker-" + i)
                    .build();

            ResponseEntity<LlmTaskPullResponseDto> response = llmTaskController.pullTask(pullRequest);
            if (response.getBody() != null && Boolean.TRUE.equals(response.getBody().getHasTask())) {
                pulledTasks.add(testTasks.get(i));
            }
        }

        ExecutorService executor = Executors.newFixedThreadPool(10);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(pulledTasks.size());

        AtomicInteger successfulDeliveries = new AtomicInteger(0);
        AtomicLong totalDeliveryTime = new AtomicLong(0);

        // When
        for (int i = 0; i < pulledTasks.size(); i++) {
            final Task task = pulledTasks.get(i);
            executor.submit(() -> {
                try {
                    startLatch.await();
                    
                    long startTime = System.currentTimeMillis();

                    LlmTaskCallbackRequestDto callbackRequest = LlmTaskCallbackRequestDto.builder()
                            .overallStatus("COMPLETED")
                            .executionTimeMs(60000L)
                            .build();

                    llmTaskController.deliverTaskResult(task.getId(), callbackRequest);
                    
                    long endTime = System.currentTimeMillis();
                    totalDeliveryTime.addAndGet(endTime - startTime);
                    successfulDeliveries.incrementAndGet();

                } catch (Exception e) {
                    // 记录失败
                } finally {
                    finishLatch.countDown();
                }
            });
        }

        long testStartTime = System.currentTimeMillis();
        startLatch.countDown();
        finishLatch.await(30, TimeUnit.SECONDS);
        long testEndTime = System.currentTimeMillis();

        executor.shutdown();

        // Then
        long totalTestTime = testEndTime - testStartTime;
        double averageDeliveryTime = totalDeliveryTime.get() / (double) successfulDeliveries.get();
        double throughput = successfulDeliveries.get() / (totalTestTime / 1000.0);

        System.out.println("=== 任务交付性能测试结果 ===");
        System.out.println("总测试时间: " + totalTestTime + "ms");
        System.out.println("成功交付任务数: " + successfulDeliveries.get());
        System.out.println("平均交付时间: " + String.format("%.2f", averageDeliveryTime) + "ms");
        System.out.println("吞吐量: " + String.format("%.2f", throughput) + " deliveries/second");

        // 性能断言
        assertTrue(averageDeliveryTime < 200, "平均交付时间应该小于200ms");
        assertTrue(throughput > 5, "吞吐量应该大于5 deliveries/second");
    }

    @Test
    void testDatabaseQueryPerformance() {
        // Given
        int queryCount = 1000;
        
        // When
        long startTime = System.currentTimeMillis();
        
        for (int i = 0; i < queryCount; i++) {
            taskRepository.findNextPendingTask();
        }
        
        long endTime = System.currentTimeMillis();
        
        // Then
        long totalTime = endTime - startTime;
        double averageQueryTime = totalTime / (double) queryCount;
        
        System.out.println("=== 数据库查询性能测试结果 ===");
        System.out.println("查询次数: " + queryCount);
        System.out.println("总时间: " + totalTime + "ms");
        System.out.println("平均查询时间: " + String.format("%.2f", averageQueryTime) + "ms");
        
        // 性能断言
        assertTrue(averageQueryTime < 10, "平均查询时间应该小于10ms");
    }

    @Test
    void testMemoryUsage() {
        // Given
        Runtime runtime = Runtime.getRuntime();
        
        // 执行GC获取基准内存使用
        System.gc();
        long memoryBefore = runtime.totalMemory() - runtime.freeMemory();
        
        // When - 执行大量任务操作
        for (int i = 0; i < 1000; i++) {
            LlmTaskPullRequestDto pullRequest = LlmTaskPullRequestDto.builder()
                    .workerId("memory-test-worker")
                    .build();
            
            llmTaskController.pullTask(pullRequest);
        }
        
        // Then
        System.gc();
        long memoryAfter = runtime.totalMemory() - runtime.freeMemory();
        long memoryUsed = memoryAfter - memoryBefore;
        
        System.out.println("=== 内存使用测试结果 ===");
        System.out.println("测试前内存: " + (memoryBefore / 1024 / 1024) + "MB");
        System.out.println("测试后内存: " + (memoryAfter / 1024 / 1024) + "MB");
        System.out.println("内存增长: " + (memoryUsed / 1024 / 1024) + "MB");
        
        // 内存使用断言 - 不应该有明显的内存泄漏
        assertTrue(memoryUsed < 100 * 1024 * 1024, "内存增长应该小于100MB");
    }
}

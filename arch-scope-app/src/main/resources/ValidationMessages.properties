# Capability Registration Command Validation Messages
capability.name.notblank=Capability name cannot be blank
capability.name.size=Capability name must be between 2 and 100 characters
capability.description.size=Capability description cannot exceed 500 characters
capability.version.notblank=Capability version cannot be blank
capability.version.pattern=Capability version must follow semantic versioning format (e.g., 1.0.0)
capability.examples.size=Cannot have more than 10 examples
capability.tags.size=Cannot have more than 20 tags
capability.tag.notblank=Tag cannot be blank

# Capability Example Validation Messages
capability.example.name.notblank=Example name cannot be blank
capability.example.name.size=Example name must be between 2 and 100 characters
capability.example.description.size=Example description cannot exceed 300 characters
capability.example.request.size=Request example cannot exceed 2000 characters
capability.example.response.size=Response example cannot exceed 2000 characters
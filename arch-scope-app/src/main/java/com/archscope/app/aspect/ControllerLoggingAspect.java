package com.archscope.app.aspect;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

/**
 * 控制器日志切面
 */
@Slf4j
// @Aspect  // 暂时禁用以测试启动问题
// @Component
public class ControllerLoggingAspect {

    @Around("execution(* com.archscope.app.controller.ServiceDiscoveryController.*(..))")
    public Object logServiceDiscoveryMethods(ProceedingJoinPoint joinPoint) throws Throwable {
        String methodName = joinPoint.getSignature().getName();
        Object[] args = joinPoint.getArgs();
        
        log.info("开始执行服务发现方法: {} 参数: {}", methodName, args);
        
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            long endTime = System.currentTimeMillis();
            log.info("服务发现方法执行成功: {} 耗时: {}ms", methodName, endTime - startTime);
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("服务发现方法执行失败: {} 耗时: {}ms 错误: {}", methodName, endTime - startTime, e.getMessage());
            throw e;
        }
    }
}
package com.archscope.app.scheduler;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.LlmTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * LLM任务超时处理定时器
 * 定期检查并处理超时的LLM任务，支持30分钟超时机制
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "archscope.scheduler.llm-timeout.enabled", havingValue = "true", matchIfMissing = true)
public class LlmTaskTimeoutScheduler {

    private final LlmTaskService llmTaskService;
    private final TaskRepository taskRepository;

    /**
     * 每5分钟检查一次超时任务
     * 检查超过30分钟的PROCESSING状态任务并重置为PENDING状态
     */
    @Scheduled(fixedRate = 300000) // 5分钟 = 300,000毫秒
    public void handleTimeoutTasks() {
        log.debug("开始检查LLM任务超时情况");

        try {
            // 查找所有超时的PROCESSING任务
            List<Task> timeoutTasks = taskRepository.findTimeoutProcessingTasks();

            if (timeoutTasks.isEmpty()) {
                log.debug("没有发现超时的LLM任务");
                return;
            }

            log.warn("发现 {} 个超时的LLM任务，开始处理", timeoutTasks.size());

            int processedCount = 0;
            int failedCount = 0;

            for (Task task : timeoutTasks) {
                try {
                    log.warn("处理超时任务: taskId={}, workerId={}, processingStartedAt={}, timeoutAt={}",
                            task.getId(), task.getWorkerId(), task.getProcessingStartedAt(), task.getTimeoutAt());

                    // 重置任务状态为PENDING
                    int updatedRows = taskRepository.resetTimeoutTask(task.getId());

                    if (updatedRows > 0) {
                        // 释放Redis锁
                        llmTaskService.unlockTask(task.getId());
                        processedCount++;

                        log.info("超时任务已重置: taskId={}, workerId={}", task.getId(), task.getWorkerId());
                    } else {
                        log.warn("重置超时任务失败: taskId={}", task.getId());
                        failedCount++;
                    }

                } catch (Exception e) {
                    log.error("处理超时任务失败: taskId=" + task.getId(), e);
                    failedCount++;
                }
            }

            log.info("超时任务处理完成: 总数={}, 成功={}, 失败={}",
                    timeoutTasks.size(), processedCount, failedCount);

        } catch (Exception e) {
            log.error("处理LLM任务超时检查时发生异常", e);
        }
    }

    /**
     * 每小时输出一次任务统计信息
     */
    @Scheduled(fixedRate = 3600000) // 1小时 = 3,600,000毫秒
    public void logTaskStatistics() {
        log.info("LLM任务超时检查器运行正常");
        // TODO: 可以添加更详细的统计信息
    }
}

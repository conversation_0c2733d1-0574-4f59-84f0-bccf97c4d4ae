package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 能力注册命令
 */
@Data
@Schema(description = "能力注册命令")
public class CapabilityRegistrationCommand {

    /**
     * 能力名称
     */
    @Schema(description = "能力名称")
    @NotBlank(message = "能力名称不能为空")
    private String name;

    /**
     * 能力描述
     */
    @Schema(description = "能力描述")
    private String description;

    /**
     * 服务ID
     */
    @Schema(description = "服务ID")
    @NotBlank(message = "服务ID不能为空")
    private String serviceId;

    /**
     * 能力类型
     */
    @Schema(description = "能力类型")
    @NotNull(message = "能力类型不能为空")
    private CapabilityType type;

    /**
     * 输入示例
     */
    @Schema(description = "输入示例")
    private String inputExample;

    /**
     * 输出示例
     */
    @Schema(description = "输出示例")
    private String outputExample;

    /**
     * 使用说明
     */
    @Schema(description = "使用说明")
    private String usageInstructions;

    /**
     * 标签
     */
    @Schema(description = "标签")
    private List<String> tags;

    /**
     * 能力类型枚举
     */
    public enum CapabilityType {
        DATA_PROCESSING,
        API_INTEGRATION,
        COMPUTATION,
        STORAGE,
        COMMUNICATION,
        ANALYSIS,
        OTHER
    }
}

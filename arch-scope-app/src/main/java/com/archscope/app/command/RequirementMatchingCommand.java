package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 需求匹配命令
 */
@Data
@Schema(description = "需求匹配命令")
public class RequirementMatchingCommand {

    /**
     * 需求描述
     */
    @Schema(description = "需求描述")
    @NotBlank(message = "需求描述不能为空")
    private String description;

    /**
     * 功能需求
     */
    @Schema(description = "功能需求")
    private List<String> functionalRequirements;

    /**
     * 非功能需求
     */
    @Schema(description = "非功能需求")
    private Map<String, String> nonFunctionalRequirements;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private Priority priority;

    /**
     * 标签
     */
    @Schema(description = "标签")
    private List<String> tags;

    /**
     * 匹配策略
     */
    @Schema(description = "匹配策略")
    private MatchingStrategy strategy;

    /**
     * 优先级枚举
     */
    public enum Priority {
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL
    }

    /**
     * 匹配策略枚举
     */
    public enum MatchingStrategy {
        EXACT,
        FUZZY,
        SEMANTIC,
        HYBRID
    }
}

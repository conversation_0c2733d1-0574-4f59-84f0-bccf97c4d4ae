package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 服务注册命令
 */
@Data
@Schema(description = "服务注册命令")
public class ServiceRegistrationCommand {

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    @NotBlank(message = "服务名称不能为空")
    private String name;

    /**
     * 服务描述
     */
    @Schema(description = "服务描述")
    private String description;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    @NotNull(message = "服务类型不能为空")
    private ServiceType type;

    /**
     * 服务版本
     */
    @Schema(description = "服务版本")
    @NotBlank(message = "服务版本不能为空")
    private String version;

    /**
     * 服务端点
     */
    @Schema(description = "服务端点")
    @NotBlank(message = "服务端点不能为空")
    private String endpoint;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private String groupId;

    /**
     * 构件ID
     */
    @Schema(description = "构件ID")
    private String artifactId;

    /**
     * 服务标签
     */
    @Schema(description = "服务标签")
    private List<String> tags;

    /**
     * 服务状态
     */
    @Schema(description = "服务状态")
    private ServiceStatus status;

    /**
     * 元数据
     */
    @Schema(description = "元数据")
    private Map<String, String> metadata;

    /**
     * 服务类型枚举
     */
    public enum ServiceType {
        REST_API,
        GRPC,
        GRAPHQL,
        WEBSOCKET,
        MESSAGE_QUEUE,
        DATABASE,
        CACHE,
        OTHER
    }

    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        ACTIVE,
        INACTIVE,
        MAINTENANCE,
        DEPRECATED
    }
}

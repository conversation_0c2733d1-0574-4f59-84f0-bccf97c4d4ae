package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 推荐反馈命令
 */
@Data
@Schema(description = "推荐反馈请求")
public class RecommendationFeedbackCommand {

    /**
     * 推荐ID
     */
    @Schema(description = "推荐唯一标识符", required = true)
    @NotBlank(message = "推荐ID不能为空")
    private String recommendationId;

    /**
     * 需求ID
     */
    @Schema(description = "关联的需求ID")
    private String requirementId;

    /**
     * 服务ID
     */
    @Schema(description = "关联的服务ID", required = true)
    @NotBlank(message = "服务ID不能为空")
    private String serviceId;

    /**
     * 是否满意
     */
    @Schema(description = "用户是否满意推荐结果", required = true)
    private boolean satisfied;

    /**
     * 反馈内容
     */
    @Schema(description = "用户反馈内容")
    @Size(max = 1000, message = "反馈内容长度不能超过1000字符")
    private String feedback;

    /**
     * 评分 (1-5)
     */
    @Schema(description = "用户评分，范围1-5", minimum = "1", maximum = "5")
    @Min(value = 1, message = "评分不能小于1")
    @Max(value = 5, message = "评分不能超过5")
    private Integer rating;
}
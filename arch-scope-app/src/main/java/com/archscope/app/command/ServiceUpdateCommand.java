package com.archscope.app.command;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 服务更新命令
 */
@Data
@Schema(description = "服务更新命令")
public class ServiceUpdateCommand {

    /**
     * 服务ID
     */
    @Schema(description = "服务ID")
    @NotBlank(message = "服务ID不能为空")
    private String serviceId;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String name;

    /**
     * 服务描述
     */
    @Schema(description = "服务描述")
    private String description;

    /**
     * 服务版本
     */
    @Schema(description = "服务版本")
    private String version;

    /**
     * 服务端点
     */
    @Schema(description = "服务端点")
    private String endpoint;

    /**
     * 服务标签
     */
    @Schema(description = "服务标签")
    private List<String> tags;

    /**
     * 服务状态
     */
    @Schema(description = "服务状态")
    private ServiceStatus status;

    /**
     * 元数据
     */
    @Schema(description = "元数据")
    private Map<String, String> metadata;

    /**
     * 服务状态枚举
     */
    public enum ServiceStatus {
        ACTIVE,
        INACTIVE,
        MAINTENANCE,
        DEPRECATED
    }
}

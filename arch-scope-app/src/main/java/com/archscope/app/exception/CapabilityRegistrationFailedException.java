package com.archscope.app.exception;

/**
 * 能力注册失败异常
 */
public class CapabilityRegistrationFailedException extends ServiceDiscoveryApplicationException {

    public CapabilityRegistrationFailedException(String capabilityName, String serviceId) {
        super("Failed to register capability '" + capabilityName + "' for service '" + serviceId + "'");
    }

    public CapabilityRegistrationFailedException(String capabilityName, String serviceId, Throwable cause) {
        super("Failed to register capability '" + capabilityName + "' for service '" + serviceId + "'", cause);
    }

    public CapabilityRegistrationFailedException(String message, Throwable cause) {
        super(message, cause);
    }
}
package com.archscope.app.exception;

import com.archscope.domain.exception.*;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.ErrorDetail;
import com.archscope.facade.dto.ResultCode;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.Map;

/**
 * 服务发现专用异常处理器
 * 提供更详细的错误信息和上下文
 */
@Slf4j
@RestControllerAdvice
@Order(1) // 优先级高于全局异常处理器
public class ServiceDiscoveryExceptionHandler {

    /**
     * 处理服务未找到异常 - 提供详细信息
     */
    @ExceptionHandler(ServiceNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResponse<ErrorDetail> handleServiceNotFoundException(
            ServiceNotFoundException e, HttpServletRequest request) {
        log.error("服务未找到：{}", e.getMessage());
        
        ErrorDetail errorDetail = ErrorDetail.withPath(
            "SERVICE_NOT_FOUND",
            e.getMessage(),
            request.getRequestURI()
        );
        errorDetail.setDetails("请检查服务ID是否正确，或者服务是否已被删除");
        
        return ApiResponse.errorWithDetail(ResultCode.SERVICE_NOT_FOUND.getMessage(), errorDetail);
    }

    /**
     * 处理服务重复异常 - 提供详细信息
     */
    @ExceptionHandler(DuplicateServiceException.class)
    @ResponseStatus(HttpStatus.CONFLICT)
    public ApiResponse<ErrorDetail> handleDuplicateServiceException(
            DuplicateServiceException e, HttpServletRequest request) {
        log.error("服务重复：{}", e.getMessage());
        
        ErrorDetail errorDetail = ErrorDetail.withPath(
            "SERVICE_ALREADY_EXISTS",
            e.getMessage(),
            request.getRequestURI()
        );
        errorDetail.setDetails("服务名称或标识符已存在，请使用不同的名称或检查现有服务");
        
        Map<String, Object> context = new HashMap<>();
        context.put("suggestion", "可以使用服务发现API查询现有服务");
        errorDetail.setContext(context);
        
        return ApiResponse.errorWithDetail(ResultCode.SERVICE_ALREADY_EXISTS.getMessage(), errorDetail);
    }

    /**
     * 处理无效服务数据异常 - 提供详细信息
     */
    @ExceptionHandler(InvalidServiceDataException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<ErrorDetail> handleInvalidServiceDataException(
            InvalidServiceDataException e, HttpServletRequest request) {
        log.error("无效服务数据：{}", e.getMessage());
        
        ErrorDetail errorDetail = ErrorDetail.withPath(
            "INVALID_SERVICE_DATA",
            e.getMessage(),
            request.getRequestURI()
        );
        errorDetail.setDetails("请检查服务数据格式是否符合要求");
        
        Map<String, Object> context = new HashMap<>();
        context.put("requiredFields", new String[]{"name", "type", "version", "endpoint"});
        context.put("optionalFields", new String[]{"groupId", "artifactId", "tags", "metadata"});
        errorDetail.setContext(context);
        
        return ApiResponse.errorWithDetail(ResultCode.INVALID_SERVICE_DATA.getMessage(), errorDetail);
    }

    /**
     * 处理能力未找到异常 - 提供详细信息
     */
    @ExceptionHandler(CapabilityNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResponse<ErrorDetail> handleCapabilityNotFoundException(
            CapabilityNotFoundException e, HttpServletRequest request) {
        log.error("能力未找到：{}", e.getMessage());
        
        ErrorDetail errorDetail = ErrorDetail.withPath(
            "CAPABILITY_NOT_FOUND",
            e.getMessage(),
            request.getRequestURI()
        );
        errorDetail.setDetails("请检查能力ID是否正确，或者能力是否已被删除");
        
        return ApiResponse.errorWithDetail(ResultCode.CAPABILITY_NOT_FOUND.getMessage(), errorDetail);
    }

    /**
     * 处理无效能力数据异常 - 提供详细信息
     */
    @ExceptionHandler(InvalidCapabilityDataException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<ErrorDetail> handleInvalidCapabilityDataException(
            InvalidCapabilityDataException e, HttpServletRequest request) {
        log.error("无效能力数据：{}", e.getMessage());
        
        ErrorDetail errorDetail = ErrorDetail.withPath(
            "INVALID_CAPABILITY_DATA",
            e.getMessage(),
            request.getRequestURI()
        );
        errorDetail.setDetails("请检查能力数据格式是否符合要求");
        
        Map<String, Object> context = new HashMap<>();
        context.put("requiredFields", new String[]{"name", "description", "version"});
        context.put("optionalFields", new String[]{"examples", "deprecated"});
        errorDetail.setContext(context);
        
        return ApiResponse.errorWithDetail(ResultCode.INVALID_CAPABILITY_DATA.getMessage(), errorDetail);
    }

    /**
     * 处理需求未找到异常 - 提供详细信息
     */
    @ExceptionHandler(RequirementNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResponse<ErrorDetail> handleRequirementNotFoundException(
            RequirementNotFoundException e, HttpServletRequest request) {
        log.error("需求未找到：{}", e.getMessage());
        
        ErrorDetail errorDetail = ErrorDetail.withPath(
            "REQUIREMENT_NOT_FOUND",
            e.getMessage(),
            request.getRequestURI()
        );
        errorDetail.setDetails("请检查需求ID是否正确，或者需求是否已被删除");
        
        return ApiResponse.errorWithDetail(ResultCode.REQUIREMENT_NOT_FOUND.getMessage(), errorDetail);
    }



    /**
     * 处理服务注册失败异常 - 提供详细信息
     */
    @ExceptionHandler(ServiceRegistrationFailedException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<ErrorDetail> handleServiceRegistrationFailedException(
            ServiceRegistrationFailedException e, HttpServletRequest request) {
        log.error("服务注册失败：{}", e.getMessage(), e);
        
        ErrorDetail errorDetail = ErrorDetail.withPath(
            "SERVICE_REGISTRATION_FAILED",
            e.getMessage(),
            request.getRequestURI()
        );
        errorDetail.setDetails("服务注册过程中发生错误，请检查服务信息并重试");
        
        Map<String, Object> context = new HashMap<>();
        context.put("possibleCauses", new String[]{
            "服务信息格式不正确",
            "网络连接问题",
            "系统资源不足"
        });
        context.put("nextSteps", "请检查服务信息格式，稍后重试或联系管理员");
        errorDetail.setContext(context);
        
        return ApiResponse.errorWithDetail(ResultCode.SERVICE_REGISTRATION_FAILED.getMessage(), errorDetail);
    }

    /**
     * 处理需求匹配失败异常 - 提供详细信息
     */
    @ExceptionHandler(RequirementMatchingFailedException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<ErrorDetail> handleRequirementMatchingFailedException(
            RequirementMatchingFailedException e, HttpServletRequest request) {
        log.error("需求匹配失败：{}", e.getMessage(), e);
        
        ErrorDetail errorDetail = ErrorDetail.withPath(
            "REQUIREMENT_MATCHING_FAILED",
            e.getMessage(),
            request.getRequestURI()
        );
        errorDetail.setDetails("需求匹配算法执行失败，请简化需求描述后重试");
        
        Map<String, Object> context = new HashMap<>();
        context.put("suggestions", new String[]{
            "使用更具体的关键词描述需求",
            "减少需求的复杂度",
            "尝试分步骤描述需求"
        });
        errorDetail.setContext(context);
        
        return ApiResponse.errorWithDetail(ResultCode.REQUIREMENT_MATCHING_FAILED.getMessage(), errorDetail);
    }
}
package com.archscope.app.exception;

import com.archscope.facade.dto.ErrorDetail;
import lombok.experimental.UtilityClass;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 异常处理工具类
 * 提供统一的错误信息构建方法
 */
@UtilityClass
public class ExceptionHandlerUtils {

    /**
     * 从绑定结果创建字段错误列表
     */
    public static List<ErrorDetail.FieldError> createFieldErrors(BindingResult bindingResult) {
        return bindingResult.getFieldErrors().stream()
                .map(fieldError -> new ErrorDetail.FieldError(
                        fieldError.getField(),
                        fieldError.getDefaultMessage(),
                        fieldError.getRejectedValue()
                ))
                .collect(Collectors.toList());
    }

    /**
     * 从约束违反集合创建字段错误列表
     */
    public static List<ErrorDetail.FieldError> createFieldErrors(Set<ConstraintViolation<?>> violations) {
        return violations.stream()
                .map(violation -> new ErrorDetail.FieldError(
                        violation.getPropertyPath().toString(),
                        violation.getMessage(),
                        violation.getInvalidValue()
                ))
                .collect(Collectors.toList());
    }

    /**
     * 创建带验证错误的错误详情
     */
    public static ErrorDetail createValidationErrorDetail(
            String errorCode, 
            String message, 
            String path,
            List<ErrorDetail.FieldError> fieldErrors) {
        ErrorDetail errorDetail = ErrorDetail.withPath(errorCode, message, path);
        errorDetail.setFieldErrors(fieldErrors);
        errorDetail.setDetails("请检查以下字段的输入值");
        return errorDetail;
    }

    /**
     * 从HTTP请求获取请求路径
     */
    public static String getRequestPath(HttpServletRequest request) {
        if (request == null) {
            return "unknown";
        }
        return request.getRequestURI();
    }

    /**
     * 创建简单的错误详情
     */
    public static ErrorDetail createSimpleErrorDetail(
            String errorCode, 
            String message, 
            HttpServletRequest request) {
        return ErrorDetail.withPath(errorCode, message, getRequestPath(request));
    }

    /**
     * 创建带详细描述的错误详情
     */
    public static ErrorDetail createDetailedErrorDetail(
            String errorCode, 
            String message, 
            String details,
            HttpServletRequest request) {
        ErrorDetail errorDetail = createSimpleErrorDetail(errorCode, message, request);
        errorDetail.setDetails(details);
        return errorDetail;
    }

    /**
     * 从异常消息中提取关键信息
     */
    public static String extractKeyInfo(String exceptionMessage) {
        if (exceptionMessage == null || exceptionMessage.isEmpty()) {
            return "未知错误";
        }
        
        // 移除技术性的堆栈信息，只保留用户友好的信息
        String[] lines = exceptionMessage.split("\n");
        return lines[0]; // 返回第一行，通常包含最重要的信息
    }

    /**
     * 判断是否为客户端错误
     */
    public static boolean isClientError(Exception e) {
        return e instanceof IllegalArgumentException ||
               e.getClass().getSimpleName().contains("Validation") ||
               e.getClass().getSimpleName().contains("Invalid");
    }

    /**
     * 判断是否为服务器错误
     */
    public static boolean isServerError(Exception e) {
        return !isClientError(e);
    }
}
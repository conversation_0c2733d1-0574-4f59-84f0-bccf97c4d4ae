package com.archscope.app.exception;

import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.ErrorDetail;
import com.archscope.facade.dto.ResultCode;
import com.archscope.facade.exception.BusinessException;
import com.archscope.domain.exception.*;

import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.http.HttpStatus;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.sql.SQLIntegrityConstraintViolationException;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 全局异常处理器
 * 处理通用异常和未被专用处理器捕获的异常
 */
@Slf4j
@RestControllerAdvice
@Order(2) // 优先级低于专用异常处理器
public class GlobalExceptionHandler {

    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public ApiResponse<Void> handleBusinessException(BusinessException e) {
        log.error("业务异常：{}", e.getMessage());
        return ApiResponse.error(e.getResultCode(), e.getMessage());
    }

    /**
     * 处理参数校验异常（@Valid注解验证）
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<ErrorDetail> handleMethodArgumentNotValidException(
            MethodArgumentNotValidException e, HttpServletRequest request) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.error("参数校验异常：{}", message);
        
        List<ErrorDetail.FieldError> fieldErrors = ExceptionHandlerUtils.createFieldErrors(e.getBindingResult());
        ErrorDetail errorDetail = ExceptionHandlerUtils.createValidationErrorDetail(
            "VALIDATION_ERROR",
            "请求参数验证失败",
            ExceptionHandlerUtils.getRequestPath(request),
            fieldErrors
        );
        
        return ApiResponse.errorWithDetail(ResultCode.PARAMETER_VALIDATION_ERROR.getMessage(), errorDetail);
    }

    /**
     * 处理参数绑定异常
     */
    @ExceptionHandler(BindException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<ErrorDetail> handleBindException(BindException e, HttpServletRequest request) {
        String message = e.getBindingResult().getFieldErrors().stream()
                .map(FieldError::getDefaultMessage)
                .collect(Collectors.joining(", "));
        log.error("参数绑定异常：{}", message);
        
        List<ErrorDetail.FieldError> fieldErrors = ExceptionHandlerUtils.createFieldErrors(e.getBindingResult());
        ErrorDetail errorDetail = ExceptionHandlerUtils.createValidationErrorDetail(
            "BINDING_ERROR",
            "请求参数绑定失败",
            ExceptionHandlerUtils.getRequestPath(request),
            fieldErrors
        );
        
        return ApiResponse.errorWithDetail(ResultCode.PARAMETER_VALIDATION_ERROR.getMessage(), errorDetail);
    }

    /**
     * 处理约束违反异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<ErrorDetail> handleConstraintViolationException(
            ConstraintViolationException e, HttpServletRequest request) {
        String message = e.getConstraintViolations().stream()
                .map(ConstraintViolation::getMessage)
                .collect(Collectors.joining(", "));
        log.error("约束违反异常：{}", message);
        
        List<ErrorDetail.FieldError> fieldErrors = ExceptionHandlerUtils.createFieldErrors(e.getConstraintViolations());
        ErrorDetail errorDetail = ExceptionHandlerUtils.createValidationErrorDetail(
            "CONSTRAINT_VIOLATION",
            "数据约束验证失败",
            ExceptionHandlerUtils.getRequestPath(request),
            fieldErrors
        );
        
        return ApiResponse.errorWithDetail(ResultCode.PARAMETER_VALIDATION_ERROR.getMessage(), errorDetail);
    }

    /**
     * 处理参数异常（IllegalArgumentException）
     */
    @ExceptionHandler(IllegalArgumentException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleIllegalArgumentException(IllegalArgumentException e) {
        log.error("参数异常：{}", e.getMessage());
        return ApiResponse.error(ResultCode.PARAMETER_VALIDATION_ERROR, e.getMessage());
    }

    /**
     * 处理数据库唯一约束违反异常
     */
    @ExceptionHandler(DuplicateKeyException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleDuplicateKeyException(DuplicateKeyException e) {
        String message = e.getMessage();
        log.error("数据库唯一约束违反：{}", message);

        // 解析具体的约束违反信息，提供更友好的错误消息
        if (message != null && message.contains("u_idx_repository_url")) {
            return ApiResponse.error(ResultCode.PARAMETER_VALIDATION_ERROR, "该仓库地址已经被注册，请检查是否重复提交");
        } else if (message != null && message.contains("Duplicate entry")) {
            return ApiResponse.error(ResultCode.PARAMETER_VALIDATION_ERROR, "数据重复，请检查输入信息");
        }

        return ApiResponse.error(ResultCode.PARAMETER_VALIDATION_ERROR, "数据约束违反，请检查输入信息");
    }

    // ==================== 服务发现领域异常处理 ====================
    
    /**
     * 处理服务未找到异常
     */
    @ExceptionHandler(ServiceNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResponse<Void> handleServiceNotFoundException(ServiceNotFoundException e) {
        log.error("服务未找到：{}", e.getMessage());
        return ApiResponse.error(ResultCode.SERVICE_NOT_FOUND, e.getMessage());
    }

    /**
     * 处理服务重复异常
     */
    @ExceptionHandler(DuplicateServiceException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleDuplicateServiceException(DuplicateServiceException e) {
        log.error("服务重复：{}", e.getMessage());
        return ApiResponse.error(ResultCode.SERVICE_ALREADY_EXISTS, e.getMessage());
    }

    /**
     * 处理无效服务数据异常
     */
    @ExceptionHandler(InvalidServiceDataException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleInvalidServiceDataException(InvalidServiceDataException e) {
        log.error("无效服务数据：{}", e.getMessage());
        return ApiResponse.error(ResultCode.INVALID_SERVICE_DATA, e.getMessage());
    }

    /**
     * 处理能力未找到异常
     */
    @ExceptionHandler(CapabilityNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResponse<Void> handleCapabilityNotFoundException(CapabilityNotFoundException e) {
        log.error("能力未找到：{}", e.getMessage());
        return ApiResponse.error(ResultCode.CAPABILITY_NOT_FOUND, e.getMessage());
    }

    /**
     * 处理无效能力数据异常
     */
    @ExceptionHandler(InvalidCapabilityDataException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleInvalidCapabilityDataException(InvalidCapabilityDataException e) {
        log.error("无效能力数据：{}", e.getMessage());
        return ApiResponse.error(ResultCode.INVALID_CAPABILITY_DATA, e.getMessage());
    }

    /**
     * 处理需求未找到异常
     */
    @ExceptionHandler(RequirementNotFoundException.class)
    @ResponseStatus(HttpStatus.NOT_FOUND)
    public ApiResponse<Void> handleRequirementNotFoundException(RequirementNotFoundException e) {
        log.error("需求未找到：{}", e.getMessage());
        return ApiResponse.error(ResultCode.REQUIREMENT_NOT_FOUND, e.getMessage());
    }

    /**
     * 处理无效需求数据异常
     */
    @ExceptionHandler(InvalidRequirementDataException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleInvalidRequirementDataException(InvalidRequirementDataException e) {
        log.error("无效需求数据：{}", e.getMessage());
        return ApiResponse.error(ResultCode.INVALID_REQUIREMENT_DATA, e.getMessage());
    }

    // ==================== 服务发现应用异常处理 ====================

    /**
     * 处理服务注册失败异常
     */
    @ExceptionHandler(ServiceRegistrationFailedException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleServiceRegistrationFailedException(ServiceRegistrationFailedException e) {
        log.error("服务注册失败：{}", e.getMessage());
        return ApiResponse.error(ResultCode.SERVICE_REGISTRATION_FAILED, e.getMessage());
    }

    /**
     * 处理服务更新失败异常
     */
    @ExceptionHandler(ServiceUpdateFailedException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleServiceUpdateFailedException(ServiceUpdateFailedException e) {
        log.error("服务更新失败：{}", e.getMessage());
        return ApiResponse.error(ResultCode.SERVICE_UPDATE_FAILED, e.getMessage());
    }

    /**
     * 处理能力注册失败异常
     */
    @ExceptionHandler(CapabilityRegistrationFailedException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleCapabilityRegistrationFailedException(CapabilityRegistrationFailedException e) {
        log.error("能力注册失败：{}", e.getMessage());
        return ApiResponse.error(ResultCode.CAPABILITY_REGISTRATION_FAILED, e.getMessage());
    }

    /**
     * 处理需求匹配失败异常
     */
    @ExceptionHandler(RequirementMatchingFailedException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleRequirementMatchingFailedException(RequirementMatchingFailedException e) {
        log.error("需求匹配失败：{}", e.getMessage());
        return ApiResponse.error(ResultCode.REQUIREMENT_MATCHING_FAILED, e.getMessage());
    }

    /**
     * 处理服务发现领域异常基类
     */
    @ExceptionHandler(ServiceDiscoveryDomainException.class)
    @ResponseStatus(HttpStatus.BAD_REQUEST)
    public ApiResponse<Void> handleServiceDiscoveryDomainException(ServiceDiscoveryDomainException e) {
        log.error("服务发现领域异常：{}", e.getMessage());
        return ApiResponse.error(ResultCode.BAD_REQUEST, e.getMessage());
    }

    /**
     * 处理服务发现应用异常基类
     */
    @ExceptionHandler(ServiceDiscoveryApplicationException.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleServiceDiscoveryApplicationException(ServiceDiscoveryApplicationException e) {
        log.error("服务发现应用异常：{}", e.getMessage(), e);
        return ApiResponse.error(ResultCode.INTERNAL_SERVER_ERROR, "服务处理异常，请稍后重试");
    }

    /**
     * 处理系统内部异常
     */
    @ExceptionHandler(Exception.class)
    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    public ApiResponse<Void> handleException(Exception e) {
        log.error("系统异常：", e);
        return ApiResponse.error(ResultCode.INTERNAL_SERVER_ERROR, "系统繁忙，请稍后再试");
    }
} 
# 服务发现错误处理机制

## 概述

本文档描述了服务发现系统的统一错误处理机制，包括异常分层、错误响应格式和处理策略。

## 异常分层架构

### 1. 领域异常 (Domain Exceptions)
位于 `arch-scope-domain` 模块，表示业务规则违反：

- `ServiceDiscoveryDomainException` - 基类
- `ServiceNotFoundException` - 服务未找到
- `DuplicateServiceException` - 服务重复
- `InvalidServiceDataException` - 无效服务数据
- `CapabilityNotFoundException` - 能力未找到
- `InvalidCapabilityDataException` - 无效能力数据
- `RequirementNotFoundException` - 需求未找到
- `InvalidRequirementDataException` - 无效需求数据

### 2. 应用异常 (Application Exceptions)
位于 `arch-scope-app` 模块，表示应用层面的异常：

- `ServiceDiscoveryApplicationException` - 基类
- `ServiceRegistrationFailedException` - 服务注册失败
- `ServiceUpdateFailedException` - 服务更新失败
- `CapabilityRegistrationFailedException` - 能力注册失败
- `RequirementMatchingFailedException` - 需求匹配失败

### 3. 基础设施异常 (Infrastructure Exceptions)
位于 `arch-scope-infrastructure` 模块，表示技术实现相关的异常：

- `ServiceDiscoveryInfrastructureException` - 基类
- `DatabaseAccessException` - 数据库访问异常
- `ExternalServiceCommunicationException` - 外部服务通信异常
- `DataMappingException` - 数据映射异常

## 错误响应格式

### 标准API响应
```java
public class ApiResponse<T> {
    private boolean success;
    private String message;
    private T data;
}
```

### 详细错误信息
```java
public class ErrorDetail {
    private String errorCode;
    private String message;
    private String details;
    private Instant timestamp;
    private String path;
    private List<FieldError> fieldErrors;
    private Map<String, Object> context;
}
```

## 异常处理器

### 1. ServiceDiscoveryExceptionHandler
专门处理服务发现相关异常，提供详细的错误信息和上下文：

- 优先级：`@Order(1)`
- 提供详细的错误上下文
- 包含用户友好的错误消息
- 提供问题解决建议

### 2. GlobalExceptionHandler
处理通用异常和未被专用处理器捕获的异常：

- 优先级：`@Order(2)`
- 处理验证异常
- 处理系统级异常
- 提供统一的错误响应格式

## 结果码定义

服务发现相关错误码范围：2000-2099

```java
// 服务相关
SERVICE_NOT_FOUND(2000, "服务不存在"),
SERVICE_ALREADY_EXISTS(2001, "服务已存在"),
INVALID_SERVICE_DATA(2002, "无效的服务数据"),
SERVICE_REGISTRATION_FAILED(2003, "服务注册失败"),
SERVICE_UPDATE_FAILED(2004, "服务更新失败"),

// 能力相关
CAPABILITY_NOT_FOUND(2005, "能力不存在"),
INVALID_CAPABILITY_DATA(2006, "无效的能力数据"),
CAPABILITY_REGISTRATION_FAILED(2007, "能力注册失败"),

// 需求相关
REQUIREMENT_NOT_FOUND(2008, "需求不存在"),
INVALID_REQUIREMENT_DATA(2009, "无效的需求数据"),
REQUIREMENT_MATCHING_FAILED(2010, "需求匹配失败"),

// 基础设施相关
DATABASE_ACCESS_ERROR(2011, "数据库访问错误"),
EXTERNAL_SERVICE_ERROR(2012, "外部服务通信错误"),
DATA_MAPPING_ERROR(2013, "数据映射错误")
```

## 使用示例

### 抛出领域异常
```java
if (service == null) {
    throw new ServiceNotFoundException(serviceId);
}
```

### 抛出应用异常
```java
try {
    // 服务注册逻辑
} catch (Exception e) {
    throw new ServiceRegistrationFailedException("Failed to register service", e);
}
```

### 错误响应示例
```json
{
    "success": false,
    "message": "服务不存在",
    "data": {
        "errorCode": "SERVICE_NOT_FOUND",
        "message": "Service not found: test-service-id",
        "details": "请检查服务ID是否正确，或者服务是否已被删除",
        "timestamp": "2024-01-01T10:00:00Z",
        "path": "/api/v1/services/test-service-id",
        "context": {
            "suggestion": "可以使用服务发现API查询现有服务"
        }
    }
}
```

## 工具类

### ExceptionHandlerUtils
提供统一的错误信息构建方法：

- `createFieldErrors()` - 创建字段错误列表
- `createValidationErrorDetail()` - 创建验证错误详情
- `createSimpleErrorDetail()` - 创建简单错误详情
- `extractKeyInfo()` - 提取关键错误信息
- `isClientError()` / `isServerError()` - 判断错误类型

## 最佳实践

1. **异常分层**：按照DDD架构分层抛出相应的异常
2. **错误消息**：提供用户友好的错误消息，避免技术细节
3. **上下文信息**：在错误响应中提供有用的上下文和建议
4. **日志记录**：记录详细的错误日志用于调试
5. **HTTP状态码**：使用合适的HTTP状态码
6. **安全考虑**：避免在错误消息中泄露敏感信息

## 测试

错误处理机制包含完整的单元测试：

- `ServiceDiscoveryExceptionHandlerTest` - 专用异常处理器测试
- `ExceptionHandlerUtilsTest` - 工具类测试

测试覆盖了各种异常场景和错误响应格式验证。
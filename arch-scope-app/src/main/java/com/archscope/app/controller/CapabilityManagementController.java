package com.archscope.app.controller;

import com.archscope.app.command.CapabilityRegistrationCommand;
import com.archscope.app.dto.CapabilityDTO;
import com.archscope.app.service.CapabilityManagementService;
import com.archscope.facade.dto.ApiResponse;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 能力管理控制器
 * 提供服务能力的注册、查询、更新等功能
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/capabilities")
@RequiredArgsConstructor
@Validated
@Tag(name = "能力管理", description = "服务能力管理相关API")
public class CapabilityManagementController {

    private final CapabilityManagementService capabilityManagementService;

    /**
     * 注册服务能力
     */
    @Operation(summary = "注册服务能力", description = "为服务注册新的能力")
    @PostMapping
    public ResponseEntity<ApiResponse<CapabilityDTO>> registerCapability(
            @Valid @RequestBody CapabilityRegistrationCommand command) {
        
        log.info("注册服务能力请求: {}", command);
        CapabilityDTO capability = capabilityManagementService.registerCapability(command.getServiceId(), command);
        log.info("服务能力注册成功: {}", capability.getName());
        return ResponseEntity.ok(ApiResponse.success("能力注册成功", capability));
    }



    /**
     * 根据ID获取能力详情
     */
    @Operation(summary = "获取能力详情", description = "根据能力ID获取能力的详细信息")
    @GetMapping("/{capabilityId}")
    public ResponseEntity<ApiResponse<CapabilityDTO>> getCapabilityById(
            @Parameter(description = "能力ID") @PathVariable @NotBlank String capabilityId) {
        
        log.info("获取能力详情请求: capabilityId={}", capabilityId);
        CapabilityDTO capability = capabilityManagementService.getCapabilityById(capabilityId);
        log.info("获取能力详情成功: {}", capability.getName());
        return ResponseEntity.ok(ApiResponse.success("获取能力详情成功", capability));
    }



    /**
     * 更新能力信息
     */
    @Operation(summary = "更新能力信息", description = "更新指定能力的信息")
    @PutMapping("/{capabilityId}")
    public ResponseEntity<ApiResponse<CapabilityDTO>> updateCapability(
            @Parameter(description = "能力ID") @PathVariable @NotBlank String capabilityId,
            @Valid @RequestBody CapabilityRegistrationCommand command) {
        
        log.info("更新能力信息请求: capabilityId={}, command={}", capabilityId, command);
        CapabilityDTO capability = capabilityManagementService.updateCapability(capabilityId, command);
        log.info("能力信息更新成功: {}", capability.getName());
        return ResponseEntity.ok(ApiResponse.success("能力信息更新成功", capability));
    }


}

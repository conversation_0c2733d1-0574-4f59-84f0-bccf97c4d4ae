package com.archscope.app.controller;

import com.archscope.app.service.TaskAppService;
import com.archscope.facade.dto.*;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 任务控制器，处理任务管理相关请求
 */
@Slf4j
@RestController
@Tag(name = "任务管理", description = "任务创建、查询、状态管理等核心功能接口")
@RequestMapping("/api/v1/tasks")
@RequiredArgsConstructor
public class TaskController {

    private final TaskAppService taskAppService;

    /**
     * 获取所有任务列表（公共查看）
     * @param page 页码
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向
     * @param status 状态筛选
     * @param projectId 项目ID筛选
     * @return 任务列表
     */
    @Operation(summary = "获取任务列表", description = "获取所有任务列表，支持分页和筛选")
    @GetMapping
    public ResponseEntity<PageResponseDTO<TaskSummaryDTO>> getTasks(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction,
            @RequestParam(required = false) String status,
            @RequestParam(required = false) Long projectId) {

        log.debug("=== 新版本Controller ===");
        log.debug("获取任务列表: page={}, size={}, sortBy={}, direction={}, status={}, projectId={}",
                page, size, sortBy, direction, status, projectId);
        log.debug("=== 排序参数: sortBy={}, direction={} ===", sortBy, direction);

        PageResponseDTO<TaskSummaryDTO> tasks;
        
        if (projectId != null) {
            // 获取特定项目的任务
            tasks = taskAppService.getTasksByProject(projectId, page, size, sortBy, direction);
        } else if (status != null) {
            // 根据状态筛选任务
            tasks = taskAppService.getTasksByStatus(status, page, size);
        } else {
            // 获取所有任务
            tasks = taskAppService.getAllTasks(page, size, sortBy, direction);
        }

        return ResponseEntity.ok()
                .header("X-Sort-Applied", "sortBy=" + sortBy + ", direction=" + direction)
                .body(tasks);
    }

    /**
     * 获取任务详情
     * @param taskId 任务ID
     * @return 任务详情
     */
    @Operation(summary = "获取任务详情", description = "根据任务ID获取任务的详细信息")
    @GetMapping("/{taskId}")
    public ResponseEntity<TaskDTO> getTaskById(
            @Parameter(description = "任务ID") @PathVariable Long taskId) {

        log.debug("获取任务详情: taskId={}", taskId);

        try {
            TaskDTO task = taskAppService.getTaskById(taskId);
            return ResponseEntity.ok(task);
        } catch (Exception e) {
            log.error("获取任务详情失败: taskId={}, error={}", taskId, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 创建新任务
     * @param createDTO 任务创建信息
     * @return 创建的任务
     */
    @Operation(summary = "创建新任务", description = "创建一个新的任务")
    @PostMapping
    public ResponseEntity<TaskDTO> createTask(@Valid @RequestBody TaskCreateDTO createDTO) {

        log.info("创建任务: projectId={}, name={}, type={}",
                createDTO.getProjectId(), createDTO.getName(), createDTO.getType());

        // 移除手动异常处理，让全局异常处理器统一处理
        TaskDTO task = taskAppService.createTask(createDTO);
        return ResponseEntity.ok(task);
    }

    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 操作结果
     */
    @Operation(summary = "取消任务", description = "取消指定的任务")
    @PostMapping("/{taskId}/cancel")
    public ResponseEntity<Map<String, Object>> cancelTask(
            @Parameter(description = "任务ID") @PathVariable Long taskId) {

        log.info("取消任务: taskId={}", taskId);

        try {
            boolean success = taskAppService.cancelTask(taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "任务已成功取消" : "取消任务失败");
            response.put("taskId", taskId);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("取消任务失败: taskId={}, error={}", taskId, e.getMessage());
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "取消任务失败: " + e.getMessage());
            errorResponse.put("taskId", taskId);
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 重试任务
     * @param taskId 任务ID
     * @return 重试后的任务信息
     */
    @Operation(summary = "重试任务", description = "重试失败的任务")
    @PostMapping("/{taskId}/retry")
    public ResponseEntity<TaskDTO> retryTask(
            @Parameter(description = "任务ID") @PathVariable Long taskId) {

        log.info("重试任务: taskId={}", taskId);

        try {
            TaskDTO task = taskAppService.retryTask(taskId);
            return ResponseEntity.ok(task);
        } catch (Exception e) {
            log.error("重试任务失败: taskId={}, error={}", taskId, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取任务结果
     * @param taskId 任务ID
     * @return 任务结果
     */
    @Operation(summary = "获取任务结果", description = "获取任务的执行结果")
    @GetMapping("/{taskId}/result")
    public ResponseEntity<TaskResultDTO> getTaskResult(
            @Parameter(description = "任务ID") @PathVariable Long taskId) {

        log.debug("获取任务结果: taskId={}", taskId);

        try {
            TaskResultDTO result = taskAppService.getTaskResult(taskId);
            return ResponseEntity.ok(result);
        } catch (Exception e) {
            log.error("获取任务结果失败: taskId={}, error={}", taskId, e.getMessage());
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param statusRequest 状态更新请求
     * @return 更新后的任务信息
     */
    @Operation(summary = "更新任务状态", description = "更新任务的状态")
    @PutMapping("/{taskId}/status")
    public ResponseEntity<TaskDTO> updateTaskStatus(
            @Parameter(description = "任务ID") @PathVariable Long taskId,
            @RequestBody Map<String, String> statusRequest) {

        String status = statusRequest.get("status");
        log.info("更新任务状态: taskId={}, status={}", taskId, status);

        try {
            TaskDTO task = taskAppService.updateTaskStatus(taskId, status);
            return ResponseEntity.ok(task);
        } catch (Exception e) {
            log.error("更新任务状态失败: taskId={}, status={}, error={}", taskId, status, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 删除任务
     * @param taskId 任务ID
     * @return 操作结果
     */
    @Operation(summary = "删除任务", description = "删除指定的任务")
    @DeleteMapping("/{taskId}")
    public ResponseEntity<Map<String, Object>> deleteTask(
            @Parameter(description = "任务ID") @PathVariable Long taskId) {

        log.info("删除任务: taskId={}", taskId);

        try {
            boolean success = taskAppService.deleteTask(taskId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", success);
            response.put("message", success ? "任务已成功删除" : "删除任务失败");
            response.put("taskId", taskId);

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            log.error("删除任务失败: taskId={}, error={}", taskId, e.getMessage());
            
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "删除任务失败: " + e.getMessage());
            errorResponse.put("taskId", taskId);
            
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 搜索任务
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    @Operation(summary = "搜索任务", description = "根据关键词搜索任务")
    @GetMapping("/search")
    public ResponseEntity<PageResponseDTO<TaskSummaryDTO>> searchTasks(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        log.debug("搜索任务: keyword={}, page={}, size={}", keyword, page, size);

        try {
            PageResponseDTO<TaskSummaryDTO> tasks = taskAppService.searchTasks(keyword, page, size);
            return ResponseEntity.ok(tasks);
        } catch (Exception e) {
            log.error("搜索任务失败: keyword={}, error={}", keyword, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取用户的任务
     * @param userId 用户ID
     * @return 用户任务列表
     */
    @Operation(summary = "获取用户任务", description = "获取指定用户的任务列表")
    @GetMapping("/user/{userId}")
    public ResponseEntity<List<TaskSummaryDTO>> getUserTasks(
            @Parameter(description = "用户ID") @PathVariable Long userId) {

        log.debug("获取用户任务: userId={}", userId);

        try {
            List<TaskSummaryDTO> tasks = taskAppService.getUserTasks(userId);
            return ResponseEntity.ok(tasks);
        } catch (Exception e) {
            log.error("获取用户任务失败: userId={}, error={}", userId, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取最近的任务
     * @param limit 限制数量
     * @return 最近任务列表
     */
    @Operation(summary = "获取最近任务", description = "获取最近创建或更新的任务")
    @GetMapping("/recent")
    public ResponseEntity<List<TaskSummaryDTO>> getRecentTasks(
            @RequestParam(defaultValue = "10") int limit) {

        log.debug("获取最近任务: limit={}", limit);

        try {
            List<TaskSummaryDTO> tasks = taskAppService.getRecentTasks(limit);
            return ResponseEntity.ok(tasks);
        } catch (Exception e) {
            log.error("获取最近任务失败: limit={}, error={}", limit, e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }
}

package com.archscope.app.controller;

import com.archscope.app.dto.FeedbackDTO;
import com.archscope.app.dto.RequirementDTO;
import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.dto.ServiceQueryRequest;
import com.archscope.app.dto.ServiceRecommendationDTO;
import com.archscope.app.service.RequirementMatchingService;
import com.archscope.app.service.ServiceDiscoveryService;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.PageResponseDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 服务发现控制器
 * 提供服务发现、查询、推荐等功能
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/services/discovery")
@RequiredArgsConstructor
@Validated
@Tag(name = "服务发现", description = "服务发现相关API")
public class ServiceDiscoveryController {

    private final ServiceDiscoveryService serviceDiscoveryService;
    private final RequirementMatchingService requirementMatchingService;

    /**
     * 获取所有活跃服务
     */
    @Operation(summary = "获取所有活跃服务", description = "获取系统中所有状态为活跃的服务列表")
    @GetMapping("/active")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findAllActiveServices() {
        log.info("查找所有活跃服务请求");
        List<ServiceDTO> services = serviceDiscoveryService.findAllActiveServices();
        log.info("查找到{}个活跃服务", services.size());
        return ResponseEntity.ok(ApiResponse.success("查找活跃服务成功", services));
    }

    /**
     * 分页查询服务
     */
    @Operation(summary = "分页查询服务", description = "分页查询服务列表，支持排序")
    @GetMapping
    public ResponseEntity<ApiResponse<PageResponseDTO<ServiceDTO>>> findServices(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") @Min(0) int page,
            @Parameter(description = "每页大小，最大100") @RequestParam(defaultValue = "20") @Min(1) @Max(100) int size,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "registeredAt") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "DESC") Sort.Direction sortDirection) {
        
        log.info("分页查询服务请求: page={}, size={}, sortBy={}, sortDirection={}", page, size, sortBy, sortDirection);
        PageResponseDTO<ServiceDTO> services = serviceDiscoveryService.findServices(page, size);
        log.info("查询到{}个服务，总数:{}", services.getContent().size(), services.getTotalElements());
        return ResponseEntity.ok(ApiResponse.success("查询服务成功", services));
    }

    /**
     * 根据ID获取服务详情
     */
    @Operation(summary = "获取服务详情", description = "根据服务ID获取服务的详细信息")
    @GetMapping("/{serviceId}")
    public ResponseEntity<ApiResponse<ServiceDTO>> getServiceById(
            @Parameter(description = "服务ID") @PathVariable @NotBlank String serviceId) {
        
        log.info("获取服务详情请求: serviceId={}", serviceId);
        ServiceDTO service = serviceDiscoveryService.getServiceById(serviceId);
        log.info("获取服务详情成功: {}", service.getName());
        return ResponseEntity.ok(ApiResponse.success("获取服务详情成功", service));
    }

    /**
     * 根据类型查询服务
     */
    @Operation(summary = "根据类型查询服务", description = "根据服务类型查询服务列表")
    @GetMapping("/type/{serviceType}")
    public ResponseEntity<ApiResponse<List<ServiceDTO>>> findServicesByType(
            @Parameter(description = "服务类型") @PathVariable @NotBlank String serviceType) {
        
        log.info("根据类型查询服务请求: serviceType={}", serviceType);
        List<ServiceDTO> services = serviceDiscoveryService.findServicesByType(serviceType);
        log.info("查询到{}个{}类型的服务", services.size(), serviceType);
        return ResponseEntity.ok(ApiResponse.success("查询服务成功", services));
    }






}

package com.archscope.app.controller;

import com.archscope.domain.entity.Task;
import com.archscope.domain.service.LlmTaskService;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.llm.*;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * LLM任务API控制器 V1
 * 实现与外部LLM服务交互的标准API接口
 * 
 * 接口规范:
 * - 任务拉取: POST /api/v1/llm-tasks/pull
 * - 任务交付: POST /api/v1/llm-tasks/{taskId}/callback
 */
@Slf4j
@Tag(name = "LLM任务管理", description = "与外部LLM服务交互的任务拉取和交付接口")
@RestController
@RequestMapping("/api/v1/llm-tasks")
@RequiredArgsConstructor
public class LlmTaskV1Controller {

    private final LlmTaskService llmTaskService;
    private final ObjectMapper objectMapper;

    /**
     * LLM工作节点任务拉取接口
     * 
     * @param request 任务拉取请求
     * @return 任务信息或空响应
     */
    @PostMapping("/pull")
    public ResponseEntity<LlmTaskPullResponseDto> pullTask(@Valid @RequestBody LlmTaskPullRequestDto request) {
        
        log.info("LLM工作节点请求任务: workerId={}", request.getWorkerId());

        try {
            // 调用服务层拉取任务并直接使用外部workerId锁定
            Optional<Task> taskOpt = llmTaskService.pullNextTaskWithWorker(request.getWorkerId());

            if (!taskOpt.isPresent()) {
                log.debug("没有可用的待处理任务");
                return ResponseEntity.ok(LlmTaskPullResponseDto.noTaskAvailable());
            }

            Task task = taskOpt.get();

            // 获取任务输入数据
            Optional<String> inputDataJson = llmTaskService.getTaskInputData(task.getId());
            LlmTaskInputDto inputData = null;
            
            if (inputDataJson.isPresent()) {
                try {
                    inputData = objectMapper.readValue(inputDataJson.get(), LlmTaskInputDto.class);
                } catch (JsonProcessingException e) {
                    log.error("解析任务输入数据失败: taskId={}", task.getId(), e);
                    // 解锁任务
                    llmTaskService.unlockTask(task.getId());
                    return ResponseEntity.internalServerError().build();
                }
            }

            // 计算超时时间
            LocalDateTime timeoutAt = task.getProcessingStartedAt().plusMinutes(30);

            // 构建响应
            LlmTaskPullResponseDto response = LlmTaskPullResponseDto.withTask(
                    task.getId(),
                    task.getProjectId(),
                    task.getTaskType(),
                    task.getPriority(),
                    inputData,
                    task.getCreatedAt(),
                    timeoutAt,
                    task.getParameters()
            );

            log.info("任务已分配: taskId={}, projectId={}, workerId={}", 
                    task.getId(), task.getProjectId(), request.getWorkerId());
            
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("拉取任务失败", e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * LLM任务交付回调接口
     * 
     * @param taskId 任务ID
     * @param request 任务结果
     * @return 处理结果
     */
    @PostMapping("/{taskId}/callback")
    public ResponseEntity<ApiResponse<Map<String, Object>>> deliverTaskResult(
            @PathVariable Long taskId,
            @Valid @RequestBody LlmTaskCallbackRequestDto request) {
        
        log.info("接收LLM任务结果: taskId={}, overallStatus={}", taskId, request.getOverallStatus());

        try {
            // 验证任务状态
            if ("FAILED".equals(request.getOverallStatus()) && 
                (request.getErrorMessage() == null || request.getErrorMessage().trim().isEmpty())) {
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("当任务状态为FAILED时，错误信息不能为空")
                );
            }

            // 计算执行时间
            Long executionTimeMs = request.getExecutionTimeMs();
            if (executionTimeMs == null && request.getStartTime() != null && request.getEndTime() != null) {
                executionTimeMs = java.time.Duration.between(request.getStartTime(), request.getEndTime()).toMillis();
            }

            // 序列化结果数据
            String resultsJson = null;
            if (request.getResults() != null && !request.getResults().isEmpty()) {
                try {
                    resultsJson = objectMapper.writeValueAsString(request.getResults());
                } catch (JsonProcessingException e) {
                    log.error("序列化任务结果失败: taskId={}", taskId, e);
                    return ResponseEntity.badRequest().body(
                        ApiResponse.error("任务结果数据格式错误")
                    );
                }
            }

            // 处理任务结果
            boolean success;
            if ("FAILED".equals(request.getOverallStatus())) {
                success = llmTaskService.failTaskWithError(
                    taskId,
                    request.getErrorMessage() + (request.getErrorDetail() != null ? "\n详情: " + request.getErrorDetail() : ""),
                    executionTimeMs
                );
            } else {
                success = llmTaskService.completeTaskWithResult(
                    taskId,
                    request.getOverallStatus(),
                    resultsJson,
                    executionTimeMs
                );
            }

            if (success) {
                log.info("任务结果处理成功: taskId={}, status={}", taskId, request.getOverallStatus());
                Map<String, Object> responseData = new HashMap<>();
                responseData.put("taskId", taskId);
                responseData.put("status", request.getOverallStatus());
                responseData.put("processedAt", LocalDateTime.now());
                return ResponseEntity.ok(ApiResponse.success("任务结果处理成功", responseData));
            } else {
                log.warn("任务结果处理失败: taskId={}", taskId);
                return ResponseEntity.badRequest().body(
                    ApiResponse.error("任务结果处理失败，请检查任务状态")
                );
            }

        } catch (Exception e) {
            log.error("处理任务结果失败: taskId=" + taskId, e);
            return ResponseEntity.internalServerError().body(
                ApiResponse.error("服务器内部错误: " + e.getMessage())
            );
        }
    }

    /**
     * 获取任务状态接口 (辅助接口)
     * 
     * @param taskId 任务ID
     * @return 任务状态信息
     */
    @GetMapping("/{taskId}/status")
    public ResponseEntity<ApiResponse<Map<String, Object>>> getTaskStatus(@PathVariable Long taskId) {
        
        log.debug("查询任务状态: taskId={}", taskId);

        try {
            // 这里可以通过TaskRepository直接查询任务状态
            // 暂时返回基本信息，后续可以扩展
            Map<String, Object> responseData = new HashMap<>();
            responseData.put("taskId", taskId);
            responseData.put("message", "请使用标准任务API查询详细状态");
            responseData.put("statusEndpoint", "/api/tasks/" + taskId);
            return ResponseEntity.ok(ApiResponse.success("任务状态查询", responseData));

        } catch (Exception e) {
            log.error("查询任务状态失败: taskId=" + taskId, e);
            return ResponseEntity.internalServerError().body(
                ApiResponse.error("查询失败: " + e.getMessage())
            );
        }
    }
}

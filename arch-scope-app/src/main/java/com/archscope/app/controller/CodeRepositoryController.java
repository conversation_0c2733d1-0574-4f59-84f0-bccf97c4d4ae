package com.archscope.app.controller;

import com.archscope.domain.entity.Task;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.task.CodeParseTask;
import com.archscope.facade.dto.ApiResponse;
import com.archscope.facade.dto.ResultCode;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

/**
 * 代码仓库控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/repositories")
@RequiredArgsConstructor
public class CodeRepositoryController {

    private final TaskQueueService taskQueueService;

    /**
     * 触发代码解析任务
     *
     * @param repositoryId 代码仓库ID
     * @param projectId 项目ID
     * @param commitId 提交ID
     * @param parseType 解析类型（full/incremental）
     * @param fromCommit 起始提交ID（增量解析时使用）
     * @return 任务ID
     */
    @PostMapping("/{repositoryId}/parse")
    public ApiResponse<Long> triggerCodeParse(
            @PathVariable Long repositoryId,
            @RequestParam Long projectId,
            @RequestParam String commitId,
            @RequestParam(defaultValue = "full") String parseType,
            @RequestParam(required = false) String fromCommit) {

        log.info("触发代码解析任务: 仓库ID={}, 项目ID={}, 提交ID={}, 解析类型={}, 起始提交ID={}",
                repositoryId, projectId, commitId, parseType, fromCommit);

        try {
            // 创建代码解析任务
            CodeParseTask parseTask = new CodeParseTask();
            parseTask.setRepositoryId(repositoryId);
            parseTask.setToCommit(commitId);

            // 设置解析类型
            if ("incremental".equalsIgnoreCase(parseType) && fromCommit != null && !fromCommit.isEmpty()) {
                parseTask.setParseType(CodeParseTask.ParseType.INCREMENTAL);
                parseTask.setFromCommit(fromCommit);
            } else {
                parseTask.setParseType(CodeParseTask.ParseType.FULL);
                parseTask.setFromCommit("");
            }

            // 创建任务实体并入队
            Task task = parseTask.toTaskEntity(projectId);
            Task savedTask = taskQueueService.enqueueTask(task);

            log.info("代码解析任务已入队: {}", savedTask.getId());
            return ApiResponse.success(savedTask.getId());

        } catch (IllegalArgumentException e) {
            log.error("触发代码解析任务失败", e);
            return ApiResponse.error(ResultCode.BAD_REQUEST, e.getMessage());
        } catch (Exception e) {
            log.error("触发代码解析任务失败", e);
            return ApiResponse.error(ResultCode.INTERNAL_SERVER_ERROR, "触发代码解析任务失败: " + e.getMessage());
        }
    }

    /**
     * 获取代码解析任务状态
     *
     * @param taskId 任务ID
     * @return 任务状态
     */
    @GetMapping("/parse/tasks/{taskId}")
    public ApiResponse<Task> getParseTaskStatus(@PathVariable Long taskId) {
        log.info("获取代码解析任务状态: {}", taskId);

        try {
            return taskQueueService.getTaskById(taskId)
                    .map(ApiResponse::success)
                    .orElse(ApiResponse.error(ResultCode.NOT_FOUND, "任务不存在: " + taskId));
        } catch (Exception e) {
            log.error("获取代码解析任务状态失败", e);
            return ApiResponse.error(ResultCode.INTERNAL_SERVER_ERROR, "获取任务状态失败: " + e.getMessage());
        }
    }

    /**
     * 取消代码解析任务
     *
     * @param taskId 任务ID
     * @return 操作结果
     */
    @DeleteMapping("/parse/tasks/{taskId}")
    public ApiResponse<Void> cancelParseTask(@PathVariable Long taskId) {
        log.info("取消代码解析任务: {}", taskId);

        try {
            Task canceledTask = taskQueueService.cancelTask(taskId);
            log.info("代码解析任务已取消: {}", taskId);
            return ApiResponse.success(null);
        } catch (Exception e) {
            log.error("取消代码解析任务失败", e);
            return ApiResponse.error(ResultCode.INTERNAL_SERVER_ERROR, "取消任务失败: " + e.getMessage());
        }
    }
}

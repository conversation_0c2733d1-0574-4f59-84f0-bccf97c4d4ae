package com.archscope.app.controller;

import com.archscope.app.service.ProjectAnalysisService;
import com.archscope.app.service.ProjectAppService;
import com.archscope.domain.entity.Project;
import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.util.GitUrlNormalizer;
import com.archscope.facade.dto.PageResponseDTO;
import com.archscope.facade.dto.ProjectDTO;
import com.archscope.facade.dto.ProjectRegistrationDTO;
import com.archscope.facade.dto.ProjectSummaryDTO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 项目控制器，处理项目注册、查询等请求
 */
@Slf4j
@RestController
@Tag(name = "项目管理", description = "项目注册、配置管理、状态查询等核心功能接口")
@RequestMapping("/api/v1/projects")
@RequiredArgsConstructor
public class ProjectController {

    private final ProjectAppService projectAppService;
    private final ProjectAnalysisService projectAnalysisService;
    private final ProjectRepository projectRepository;

    /**
     * 注册新项目
     * @param registrationDTO 项目注册信息
     * @return 注册结果
     */
    @Operation(summary = "注册新项目", description = "注册新项目并提供Git仓库地址")
    @PostMapping
    public ResponseEntity<ProjectDTO> registerProject(@Valid @RequestBody ProjectRegistrationDTO registrationDTO) {
        ProjectDTO projectDTO = projectAppService.registerProject(registrationDTO);
        return ResponseEntity.ok(projectDTO);
    }

    /**
     * 获取项目详情
     * @param id 项目ID
     * @return 项目详情
     */
    @Operation(summary = "获取项目详情", description = "根据项目ID获取项目详情")
    @GetMapping("/{id}")
    public ResponseEntity<ProjectDTO> getProject(@PathVariable Long id) {
        ProjectDTO projectDTO = projectAppService.getProjectById(id);
        return ResponseEntity.ok(projectDTO);
    }

    /**
     * 更新项目信息
     * @param id 项目ID
     * @param projectDTO 项目信息
     * @return 更新后的项目信息
     */
    @PutMapping("/{id}")
    public ResponseEntity<ProjectDTO> updateProject(@PathVariable Long id, @Valid @RequestBody ProjectDTO projectDTO) {
        ProjectDTO updatedProject = projectAppService.updateProject(id, projectDTO);
        return ResponseEntity.ok(updatedProject);
    }

    /**
     * 删除项目
     * @param id 项目ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProject(@PathVariable Long id) {
        projectAppService.deleteProject(id);
        return ResponseEntity.ok().build();
    }

    /**
     * 分页查询项目
     * @param page 页码
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向
     * @return 分页项目列表
     */
    @Operation(summary = "获取项目列表", description = "获取当前用户的所有项目")
    @GetMapping
    public ResponseEntity<PageResponseDTO<ProjectSummaryDTO>> getProjects(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "updatedAt") String sortBy,
            @RequestParam(defaultValue = "desc") String direction) {

        PageResponseDTO<ProjectSummaryDTO> projects = projectAppService.getProjects(page, size, sortBy, direction);
        return ResponseEntity.ok(projects);
    }

    /**
     * 搜索项目
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    @GetMapping("/search")
    public ResponseEntity<PageResponseDTO<ProjectSummaryDTO>> searchProjects(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size) {

        PageResponseDTO<ProjectSummaryDTO> projects = projectAppService.searchProjects(keyword, page, size);
        return ResponseEntity.ok(projects);
    }

    /**
     * 获取当前用户的项目
     * @return 项目列表
     */
    @GetMapping("/my")
    public ResponseEntity<List<ProjectSummaryDTO>> getCurrentUserProjects() {
        List<ProjectSummaryDTO> projects = projectAppService.getCurrentUserProjects();
        return ResponseEntity.ok(projects);
    }

    /**
     * 获取最近更新的项目
     * @param limit 限制数量
     * @return 项目列表
     */
    @GetMapping("/recent")
    public ResponseEntity<List<ProjectSummaryDTO>> getRecentProjects(
            @RequestParam(defaultValue = "5") int limit) {

        List<ProjectSummaryDTO> projects = projectAppService.getRecentProjects(limit);
        return ResponseEntity.ok(projects);
    }

    /**
     * 获取星级最高的项目
     * @param limit 限制数量
     * @return 项目列表
     */
    @GetMapping("/top-rated")
    public ResponseEntity<List<ProjectSummaryDTO>> getTopRatedProjects(
            @RequestParam(defaultValue = "5") int limit) {

        List<ProjectSummaryDTO> projects = projectAppService.getTopRatedProjects(limit);
        return ResponseEntity.ok(projects);
    }

    /**
     * 检查仓库URL是否已存在
     * @param url 仓库URL
     * @return 检查结果，包含是否存在和现有项目信息
     */
    @GetMapping("/check-repository")
    public ResponseEntity<Map<String, Object>> checkRepositoryExists(@RequestParam String url) {
        Map<String, Object> response = new HashMap<>();

        try {
            // 验证URL格式
            if (!GitUrlNormalizer.isValidGitUrl(url)) {
                response.put("exists", false);
                response.put("error", true);
                response.put("message", "仓库地址格式不正确，请提供有效的Git仓库URL");
                return ResponseEntity.badRequest().body(response);
            }

            // 标准化URL
            String normalizedUrl = GitUrlNormalizer.normalize(url);
            if (normalizedUrl == null) {
                response.put("exists", false);
                response.put("error", true);
                response.put("message", "无法解析仓库地址，请检查URL格式");
                return ResponseEntity.badRequest().body(response);
            }

            boolean exists = projectAppService.isRepositoryUrlExists(url);
            response.put("exists", exists);

            if (exists) {
                // 优先使用标准化URL查找项目
                Optional<Project> existingProject = projectRepository.findByNormalizedRepositoryUrl(normalizedUrl);
                if (!existingProject.isPresent()) {
                    // 如果标准化URL没找到，尝试原始URL
                    existingProject = projectRepository.findByRepositoryUrl(url);
                }

                if (existingProject.isPresent()) {
                    Project project = existingProject.get();
                    Map<String, Object> projectInfo = new HashMap<>();
                    projectInfo.put("id", project.getId());
                    projectInfo.put("name", project.getName());
                    projectInfo.put("description", project.getDescription());
                    projectInfo.put("createdAt", project.getCreatedAt());
                    projectInfo.put("status", project.getStatus());

                    response.put("existingProject", projectInfo);
                    response.put("message", String.format("该仓库已被项目 \"%s\" 注册使用。" +
                        "如需查看现有项目，请前往项目列表页面。" +
                        "（原始URL: %s，标准化URL: %s）",
                        project.getName(), project.getRepositoryUrl(), normalizedUrl));
                } else {
                    response.put("message", "该仓库已被其他项目使用");
                }
            } else {
                response.put("message", "仓库可以使用");
            }

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("检查仓库存在性失败: url={}", url, e);
            response.put("exists", false);
            response.put("error", true);
            response.put("message", "检查仓库时发生错误，请稍后重试");
            return ResponseEntity.ok(response);
        }
    }

    /**
     * 将用户添加到项目
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 操作结果
     */
    @PostMapping("/{projectId}/members/{userId}")
    public ResponseEntity<Boolean> addUserToProject(
            @PathVariable Long projectId,
            @PathVariable Long userId) {

        boolean result = projectAppService.addUserToProject(projectId, userId);
        return ResponseEntity.ok(result);
    }

    /**
     * 从项目移除用户
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 操作结果
     */
    @DeleteMapping("/{projectId}/members/{userId}")
    public ResponseEntity<Boolean> removeUserFromProject(
            @PathVariable Long projectId,
            @PathVariable Long userId) {

        boolean result = projectAppService.removeUserFromProject(projectId, userId);
        return ResponseEntity.ok(result);
    }

    /**
     * 触发项目代码分析
     * @param projectId 项目ID
     * @param request 分析请求参数
     * @return 分析任务信息
     */
    @Operation(summary = "触发项目代码分析", description = "创建LLM分析任务并开始代码分析")
    @PostMapping("/{projectId}/analyze")
    public ResponseEntity<Map<String, Object>> analyzeProject(
            @PathVariable Long projectId,
            @RequestBody(required = false) Map<String, Object> request) {

        log.info("触发项目分析: projectId={}", projectId);

        try {
            // 检查项目是否可以分析
            if (!projectAnalysisService.canAnalyzeProject(projectId)) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "项目当前状态不允许分析");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            // 获取可选的commitId参数
            String commitId = null;
            if (request != null && request.containsKey("commitId")) {
                commitId = (String) request.get("commitId");
            }

            // 触发分析
            Task task = projectAnalysisService.triggerProjectAnalysis(projectId, commitId);

            log.info("项目分析任务已创建: projectId={}, taskId={}", projectId, task.getId());

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "项目分析任务已成功触发");
            response.put("taskId", task.getId());
            response.put("projectId", projectId);
            response.put("status", task.getStatus().name());
            response.put("createdAt", task.getCreatedAt());
            return ResponseEntity.accepted().body(response);

        } catch (Exception e) {
            log.error("触发项目分析失败: projectId=" + projectId, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "触发分析失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 重新分析项目
     * @param projectId 项目ID
     * @param request 重新分析请求参数
     * @return 分析任务信息
     */
    @Operation(summary = "重新分析项目", description = "使用指定的提交ID重新分析项目")
    @PostMapping("/{projectId}/reanalyze")
    public ResponseEntity<Map<String, Object>> reanalyzeProject(
            @PathVariable Long projectId,
            @RequestBody Map<String, Object> request) {

        log.info("重新分析项目: projectId={}", projectId);

        try {
            String commitId = (String) request.get("commitId");
            if (commitId == null || commitId.trim().isEmpty()) {
                Map<String, Object> errorResponse = new HashMap<>();
                errorResponse.put("success", false);
                errorResponse.put("message", "重新分析时必须指定提交ID");
                return ResponseEntity.badRequest().body(errorResponse);
            }

            Task task = projectAnalysisService.reanalyzeProject(projectId, commitId);

            log.info("项目重新分析任务已创建: projectId={}, taskId={}, commitId={}",
                    projectId, task.getId(), commitId);

            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "项目重新分析任务已成功触发");
            response.put("taskId", task.getId());
            response.put("projectId", projectId);
            response.put("commitId", commitId);
            response.put("status", task.getStatus().name());
            response.put("createdAt", task.getCreatedAt());
            return ResponseEntity.accepted().body(response);

        } catch (Exception e) {
            log.error("重新分析项目失败: projectId=" + projectId, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "重新分析失败: " + e.getMessage());
            return ResponseEntity.badRequest().body(errorResponse);
        }
    }

    /**
     * 检查项目分析状态
     * @param projectId 项目ID
     * @return 分析状态信息
     */
    @Operation(summary = "检查项目分析状态", description = "检查项目是否可以进行分析")
    @GetMapping("/{projectId}/analysis-status")
    public ResponseEntity<Map<String, Object>> getAnalysisStatus(@PathVariable Long projectId) {

        try {
            boolean canAnalyze = projectAnalysisService.canAnalyzeProject(projectId);

            Map<String, Object> response = new HashMap<>();
            response.put("projectId", projectId);
            response.put("canAnalyze", canAnalyze);
            response.put("message", canAnalyze ? "项目可以进行分析" : "项目当前状态不允许分析");
            return ResponseEntity.ok(response);

        } catch (Exception e) {
            log.error("检查项目分析状态失败: projectId=" + projectId, e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("message", "检查失败: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
}
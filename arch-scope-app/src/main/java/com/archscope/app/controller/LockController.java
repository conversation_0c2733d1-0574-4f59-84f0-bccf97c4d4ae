package com.archscope.app.controller;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/lock")
public class LockController {

    /**
     * 演示分布式锁的使用
     * 假设这是一个需要防止并发操作的接口
     */
    @GetMapping("/test-lock")
    public Map<String, Object> testLock(@RequestParam(defaultValue = "test-lock") String lockKey) {
        Map<String, Object> result = new HashMap<>();
        boolean locked = true; // 模拟锁操作结果
        
        // 模拟业务处理，睡眠2秒
        try {
            Thread.sleep(2000);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        result.put("success", locked);
        result.put("message", locked ? "成功获取锁并执行操作" : "获取锁失败或执行超时");
        result.put("lockKey", lockKey);
        
        return result;
    }

    /**
     * 演示限流器的使用
     * 限制API的访问频率
     */
    @GetMapping("/test-rate-limit")
    public Map<String, Object> testRateLimit(@RequestParam(defaultValue = "test-limiter") String limiterKey) {
        Map<String, Object> result = new HashMap<>();
        boolean allowed = true; // 模拟限流结果
        
        // 模拟业务处理
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        result.put("success", allowed);
        result.put("message", allowed ? "请求成功处理，未被限流" : "请求被限流，稍后重试");
        result.put("limiterKey", limiterKey);
        
        return result;
    }

    /**
     * 演示使用信号量限制并发访问
     */
    @GetMapping("/test-semaphore")
    public Map<String, Object> testSemaphore(@RequestParam(defaultValue = "test-semaphore") String semaphoreKey) {
        Map<String, Object> result = new HashMap<>();
        boolean acquired = true; // 模拟信号量获取结果
        
        result.put("success", acquired);
        result.put("message", acquired ? "成功获取信号量并执行操作" : "获取信号量失败，并发数已达上限");
        result.put("semaphoreKey", semaphoreKey);
        
        return result;
    }
} 
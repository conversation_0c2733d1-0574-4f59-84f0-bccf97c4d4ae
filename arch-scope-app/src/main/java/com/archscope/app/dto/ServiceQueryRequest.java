package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.Builder;

import java.util.List;

/**
 * 服务查询请求DTO
 */
@Data
@Builder
@Schema(description = "服务查询请求")
public class ServiceQueryRequest {

    /**
     * 关键词
     */
    @Schema(description = "搜索关键词")
    private String keyword;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String name;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型")
    private String type;

    /**
     * 构件ID
     */
    @Schema(description = "构件ID")
    private String artifactId;

    /**
     * 能力名称
     */
    @Schema(description = "能力名称")
    private String capabilityName;

    /**
     * 页码
     */
    @Schema(description = "页码")
    private Integer page;

    /**
     * 页面大小
     */
    @Schema(description = "页面大小")
    private Integer size;



    /**
     * 标签
     */
    @Schema(description = "服务标签")
    private List<String> tags;

    /**
     * 状态
     */
    @Schema(description = "服务状态")
    private String status;

    /**
     * 组织ID
     */
    @Schema(description = "组织ID")
    private String groupId;

    /**
     * 版本
     */
    @Schema(description = "版本")
    private String version;

    /**
     * 排序字段
     */
    @Schema(description = "排序字段")
    private String sortBy;

    /**
     * 排序方向
     */
    @Schema(description = "排序方向")
    private SortDirection sortDirection;

    /**
     * 排序方向枚举
     */
    public enum SortDirection {
        ASC,
        DESC
    }
}

package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 能力示例DTO
 */
@Data
@Schema(description = "能力示例")
public class CapabilityExampleDTO {

    /**
     * 示例ID
     */
    @Schema(description = "示例ID")
    private String exampleId;

    /**
     * 示例名称
     */
    @Schema(description = "示例名称")
    private String name;

    /**
     * 示例描述
     */
    @Schema(description = "示例描述")
    private String description;

    /**
     * 输入示例
     */
    @Schema(description = "输入示例")
    private String inputExample;

    /**
     * 输出示例
     */
    @Schema(description = "输出示例")
    private String outputExample;

    /**
     * 示例类型
     */
    @Schema(description = "示例类型")
    private ExampleType type;

    /**
     * 示例类型枚举
     */
    public enum ExampleType {
        BASIC,
        ADVANCED,
        ERROR_CASE,
        EDGE_CASE
    }
}

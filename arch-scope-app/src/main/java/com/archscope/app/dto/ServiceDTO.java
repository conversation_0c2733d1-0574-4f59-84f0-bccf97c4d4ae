package com.archscope.app.dto;

import java.time.Instant;
import java.util.List;
import java.util.Map;

import io.swagger.v3.oas.annotations.media.Schema;

/**
 * 服务数据传输对象
 */
@Schema(description = "服务数据传输对象")
public class ServiceDTO {

    /**
     * 服务ID
     */
    @Schema(description = "服务ID")
    private String id;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称，用于标识和显示服务")
    private String name;

    /**
     * 服务描述
     */
    @Schema(description = "服务的详细描述信息，说明服务的功能和用途")
    private String description;

    /**
     * 服务类型
     */
    @Schema(description = "服务类型，如WEB_SERVICE、MICROSERVICE、LIBRARY等")
    private String type;

    /**
     * 服务版本
     */
    @Schema(description = "服务版本号，遵循语义化版本规范")
    private String version;

    /**
     * 服务端点URL
     */
    @Schema(description = "服务的访问端点URL，用于服务调用")
    private String endpoint;

    /**
     * Maven/Gradle坐标 - groupId
     */
    @Schema(description = "Maven/Gradle坐标的groupId，用于依赖管理")
    private String groupId;

    /**
     * Maven/Gradle坐标 - artifactId
     */
    @Schema(description = "Maven/Gradle坐标的artifactId，用于依赖管理")
    private String artifactId;

    /**
     * 服务标签
     */
    @Schema(description = "服务标签列表，用于分类和搜索服务")
    private List<String> tags;

    /**
     * 服务状态
     */
    @Schema(description = "服务状态，如ACTIVE、INACTIVE、DEPRECATED等")
    private String status;

    /**
     * 服务元数据
     */
    @Schema(description = "服务的扩展元数据信息，键值对形式存储")
    private Map<String, String> metadata;

    /**
     * 注册时间
     */
    @Schema(description = "服务注册到系统的时间")
    private Instant registeredAt;

    /**
     * 最后更新时间
     */
    @Schema(description = "服务信息最后一次更新的时间")
    private Instant lastUpdatedAt;

    /**
     * 服务能力列表
     */
    @Schema(description = "服务提供的能力列表，包含具体的功能描述")
    private List<CapabilityDTO> capabilities;

    // Getter and Setter methods
    public String getId() { return id; }
    public void setId(String id) { this.id = id; }

    public String getName() { return name; }
    public void setName(String name) { this.name = name; }

    public String getDescription() { return description; }
    public void setDescription(String description) { this.description = description; }

    public String getType() { return type; }
    public void setType(String type) { this.type = type; }

    public String getVersion() { return version; }
    public void setVersion(String version) { this.version = version; }

    public String getEndpoint() { return endpoint; }
    public void setEndpoint(String endpoint) { this.endpoint = endpoint; }

    public String getGroupId() { return groupId; }
    public void setGroupId(String groupId) { this.groupId = groupId; }

    public String getArtifactId() { return artifactId; }
    public void setArtifactId(String artifactId) { this.artifactId = artifactId; }

    public List<String> getTags() { return tags; }
    public void setTags(List<String> tags) { this.tags = tags; }

    public String getStatus() { return status; }
    public void setStatus(String status) { this.status = status; }

    public Map<String, String> getMetadata() { return metadata; }
    public void setMetadata(Map<String, String> metadata) { this.metadata = metadata; }

    public Instant getRegisteredAt() { return registeredAt; }
    public void setRegisteredAt(Instant registeredAt) { this.registeredAt = registeredAt; }

    public Instant getLastUpdatedAt() { return lastUpdatedAt; }
    public void setLastUpdatedAt(Instant lastUpdatedAt) { this.lastUpdatedAt = lastUpdatedAt; }

    public List<CapabilityDTO> getCapabilities() { return capabilities; }
    public void setCapabilities(List<CapabilityDTO> capabilities) { this.capabilities = capabilities; }
}
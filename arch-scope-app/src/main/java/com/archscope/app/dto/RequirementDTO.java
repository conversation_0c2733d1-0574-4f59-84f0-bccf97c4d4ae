package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 需求DTO
 */
@Data
@Schema(description = "需求信息")
public class RequirementDTO {

    /**
     * 需求ID
     */
    @Schema(description = "需求ID")
    private String requirementId;

    /**
     * 需求描述
     */
    @Schema(description = "需求描述")
    @NotBlank(message = "需求描述不能为空")
    private String description;

    /**
     * 功能需求
     */
    @Schema(description = "功能需求")
    private List<String> functionalRequirements;

    /**
     * 非功能需求
     */
    @Schema(description = "非功能需求")
    private Map<String, String> nonFunctionalRequirements;

    /**
     * 优先级
     */
    @Schema(description = "优先级")
    private Priority priority;

    /**
     * 标签
     */
    @Schema(description = "标签")
    private List<String> tags;

    /**
     * 优先级枚举
     */
    public enum Priority {
        LOW,
        MEDIUM,
        HIGH,
        CRITICAL
    }
}

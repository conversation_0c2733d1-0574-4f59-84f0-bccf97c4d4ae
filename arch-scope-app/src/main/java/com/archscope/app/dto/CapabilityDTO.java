package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 能力DTO
 */
@Data
@Schema(description = "能力信息")
public class CapabilityDTO {

    /**
     * 能力ID
     */
    @Schema(description = "能力ID")
    private String id;

    /**
     * 能力ID（别名）
     */
    @Schema(description = "能力ID")
    private String capabilityId;

    /**
     * 能力名称
     */
    @Schema(description = "能力名称")
    private String name;

    /**
     * 能力描述
     */
    @Schema(description = "能力描述")
    private String description;

    /**
     * 服务ID
     */
    @Schema(description = "服务ID")
    private String serviceId;

    /**
     * 服务名称
     */
    @Schema(description = "服务名称")
    private String serviceName;

    /**
     * 能力类型
     */
    @Schema(description = "能力类型")
    private CapabilityType type;

    /**
     * 输入示例
     */
    @Schema(description = "输入示例")
    private String inputExample;

    /**
     * 输出示例
     */
    @Schema(description = "输出示例")
    private String outputExample;

    /**
     * 使用说明
     */
    @Schema(description = "使用说明")
    private String usageInstructions;

    /**
     * 标签
     */
    @Schema(description = "标签")
    private List<String> tags;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private CapabilityStatus status;

    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    /**
     * 能力类型枚举
     */
    public enum CapabilityType {
        DATA_PROCESSING,
        API_INTEGRATION,
        COMPUTATION,
        STORAGE,
        COMMUNICATION,
        ANALYSIS,
        OTHER
    }

    /**
     * 能力状态枚举
     */
    public enum CapabilityStatus {
        ACTIVE,
        INACTIVE,
        DEPRECATED
    }
}

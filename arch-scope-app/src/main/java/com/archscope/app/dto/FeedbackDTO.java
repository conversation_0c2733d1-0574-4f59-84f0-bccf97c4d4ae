package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 反馈信息DTO
 */
@Data
@Schema(description = "反馈信息")
public class FeedbackDTO {

    /**
     * 反馈ID
     */
    @Schema(description = "反馈ID")
    private String feedbackId;

    /**
     * 推荐ID
     */
    @Schema(description = "推荐ID")
    @NotBlank(message = "推荐ID不能为空")
    private String recommendationId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    /**
     * 反馈类型
     */
    @Schema(description = "反馈类型")
    @NotNull(message = "反馈类型不能为空")
    private FeedbackType feedbackType;

    /**
     * 反馈内容
     */
    @Schema(description = "反馈内容")
    private String content;

    /**
     * 评分
     */
    @Schema(description = "评分")
    private Integer rating;

    /**
     * 反馈类型枚举
     */
    public enum FeedbackType {
        POSITIVE,
        NEGATIVE,
        NEUTRAL
    }
}

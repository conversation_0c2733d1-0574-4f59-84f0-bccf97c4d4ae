package com.archscope.app.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.util.List;

/**
 * 服务推荐DTO
 */
@Data
@Schema(description = "服务推荐信息")
public class ServiceRecommendationDTO {

    /**
     * 推荐ID
     */
    @Schema(description = "推荐ID")
    private String recommendationId;

    /**
     * 服务信息
     */
    @Schema(description = "服务信息")
    private ServiceDTO service;

    /**
     * 推荐分数
     */
    @Schema(description = "推荐分数")
    private Double score;

    /**
     * 推荐原因
     */
    @Schema(description = "推荐原因")
    private String reason;

    /**
     * 匹配的能力
     */
    @Schema(description = "匹配的能力")
    private List<String> matchedCapabilities;

    /**
     * 推荐置信度
     */
    @Schema(description = "推荐置信度")
    private Double confidence;

    /**
     * 推荐类型
     */
    @Schema(description = "推荐类型")
    private RecommendationType type;

    /**
     * 推荐类型枚举
     */
    public enum RecommendationType {
        EXACT_MATCH,
        PARTIAL_MATCH,
        SIMILAR,
        ALTERNATIVE
    }
}

package com.archscope.app.service.impl;

import com.archscope.app.service.TaskAppService;
import com.archscope.domain.entity.Task;
import com.archscope.domain.entity.Project;
import com.archscope.domain.service.TaskService;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.service.ProjectService;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.domain.valueobject.TaskType;
import com.archscope.facade.dto.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务应用服务实现
 * 负责处理任务相关的业务用例，协调领域对象和服务
 */
@Service
public class TaskAppServiceImpl implements TaskAppService {

    private static final Logger log = LoggerFactory.getLogger(TaskAppServiceImpl.class);

    private final TaskService taskService;
    private final TaskQueueService taskQueueService;
    private final ProjectService projectService;

    @Autowired
    public TaskAppServiceImpl(TaskService taskService, TaskQueueService taskQueueService, ProjectService projectService) {
        this.taskService = taskService;
        this.taskQueueService = taskQueueService;
        this.projectService = projectService;
    }

    @Transactional
    @Override
    public TaskDTO createTask(TaskCreateDTO createDTO) {
        log.info("创建任务: projectId={}, name={}, type={}", 
                createDTO.getProjectId(), createDTO.getName(), createDTO.getType());

        try {
            // 转换任务类型
            TaskType taskType = TaskType.valueOf(createDTO.getType().toUpperCase());
            
            // 创建任务
            Task task = taskService.createTask(
                createDTO.getProjectId(),
                taskType,
                createDTO.getParameters() != null ? createDTO.getParameters().toString() : null
            );
            
            // 设置额外属性
            task.setName(createDTO.getName());
            task.setDescription(createDTO.getDescription());
            task.setPriority(createDTO.getPriority());
            task.setAssigneeId(createDTO.getAssigneeId());
            task.setCommitId(createDTO.getCommitId());
            
            // 提交到队列
            taskService.submitTaskToQueue(task.getId());
            
            return convertToDTO(task);
        } catch (Exception e) {
            log.error("创建任务失败: {}", e.getMessage(), e);
            throw new RuntimeException("创建任务失败: " + e.getMessage());
        }
    }

    @Override
    public TaskDTO getTaskById(Long taskId) {
        log.debug("获取任务详情: taskId={}", taskId);
        
        Task task = taskService.getTaskById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        return convertToDTO(task);
    }

    @Override
    public List<TaskSummaryDTO> getTasksByProject(Long projectId) {
        log.debug("获取项目任务列表: projectId={}", projectId);
        
        List<Task> tasks = taskService.getTasksByProject(projectId);
        return tasks.stream()
                .map(this::convertToSummaryDTO)
                .collect(Collectors.toList());
    }

    @Override
    public PageResponseDTO<TaskSummaryDTO> getTasksByProject(Long projectId, int page, int size, String sortBy, String direction) {
        log.debug("分页获取项目任务列表: projectId={}, page={}, size={}, sortBy={}, direction={}", projectId, page, size, sortBy, direction);

        try {
            // 使用数据库级分页查询
            List<Task> tasks = taskService.getTasksByProjectWithPagination(projectId, page, size, sortBy, direction);
            long totalElements = taskService.countTasksByProject(projectId);

            // 转换为DTO
            List<TaskSummaryDTO> taskDTOs = tasks.stream()
                    .map(this::convertToSummaryDTO)
                    .collect(Collectors.toList());

            log.debug("分页查询完成: totalElements={}, pageData.size={}", totalElements, taskDTOs.size());

            return PageResponseDTO.<TaskSummaryDTO>builder()
                    .content(taskDTOs)
                    .page(page)
                    .size(size)
                    .totalElements(totalElements)
                    .totalPages((int) Math.ceil((double) totalElements / size))
                    .first(page == 0)
                    .last((page + 1) * size >= totalElements)
                    .build();
        } catch (Exception e) {
            log.error("分页获取项目任务列表失败: projectId={}, error={}", projectId, e.getMessage(), e);
            return PageResponseDTO.<TaskSummaryDTO>builder()
                    .content(Collections.emptyList())
                    .page(page)
                    .size(size)
                    .totalElements(0L)
                    .totalPages(0)
                    .first(true)
                    .last(true)
                    .build();
        }
    }

    @Override
    public PageResponseDTO<TaskSummaryDTO> getAllTasks(int page, int size, String sortBy, String direction) {
        log.debug("=== Service层 ===");
        log.debug("获取所有任务列表: page={}, size={}, sortBy={}, direction={}", page, size, sortBy, direction);

        try {
            // 使用数据库级分页查询
            List<Task> tasks = taskService.getAllTasksWithPagination(page, size, sortBy, direction);
            long totalElements = taskService.countAllTasks();

            // 转换为DTO
            List<TaskSummaryDTO> taskDTOs = tasks.stream()
                    .map(this::convertToSummaryDTO)
                    .collect(Collectors.toList());

            log.debug("分页查询完成: totalElements={}, pageData.size={}", totalElements, taskDTOs.size());

            PageResponseDTO<TaskSummaryDTO> result = PageResponseDTO.<TaskSummaryDTO>builder()
                    .content(taskDTOs)
                    .page(page)
                    .size(size)
                    .totalElements(totalElements)
                    .totalPages((int) Math.ceil((double) totalElements / size))
                    .first(page == 0)
                    .last((page + 1) * size >= totalElements)
                    .build();

            log.debug("返回结果: totalElements={}, totalPages={}, content.size={}",
                    result.getTotalElements(), result.getTotalPages(), result.getContent().size());

            return result;
        } catch (Exception e) {
            log.error("获取所有任务列表失败: error={}", e.getMessage(), e);
            return PageResponseDTO.<TaskSummaryDTO>builder()
                    .content(Collections.emptyList())
                    .page(page)
                    .size(size)
                    .totalElements(0L)
                    .totalPages(0)
                    .first(true)
                    .last(true)
                    .build();
        }
    }

    @Override
    public PageResponseDTO<TaskSummaryDTO> getTasksByStatus(String status, int page, int size) {
        log.debug("根据状态获取任务列表: status={}, page={}, size={}", status, page, size);

        try {
            // 获取指定状态的任务
            List<Task> allTasks = taskService.getAllTasks();
            List<Task> filteredTasks = allTasks.stream()
                    .filter(task -> task.getStatus().name().equals(status.toUpperCase()))
                    .collect(Collectors.toList());

            // 转换为DTO
            List<TaskSummaryDTO> taskDTOs = filteredTasks.stream()
                    .map(this::convertToSummaryDTO)
                    .collect(Collectors.toList());

            // 排序处理（默认按ID降序）
            taskDTOs = sortTaskDTOs(taskDTOs, "id", "desc");

            // 简单的内存分页实现
            int start = page * size;
            int end = Math.min(start + size, taskDTOs.size());
            List<TaskSummaryDTO> pageData = start < taskDTOs.size() ?
                    taskDTOs.subList(start, end) : Collections.emptyList();

            return PageResponseDTO.<TaskSummaryDTO>builder()
                    .content(pageData)
                    .page(page)
                    .size(size)
                    .totalElements((long) taskDTOs.size())
                    .totalPages((int) Math.ceil((double) taskDTOs.size() / size))
                    .first(page == 0)
                    .last((page + 1) * size >= taskDTOs.size())
                    .build();
        } catch (Exception e) {
            log.error("根据状态获取任务列表失败: status={}, error={}", status, e.getMessage(), e);
            return PageResponseDTO.<TaskSummaryDTO>builder()
                    .content(Collections.emptyList())
                    .page(page)
                    .size(size)
                    .totalElements(0L)
                    .totalPages(0)
                    .first(true)
                    .last(true)
                    .build();
        }
    }

    @Transactional
    @Override
    public boolean cancelTask(Long taskId) {
        log.info("取消任务: taskId={}", taskId);
        
        try {
            return taskService.cancelTask(taskId);
        } catch (Exception e) {
            log.error("取消任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Transactional
    @Override
    public TaskDTO retryTask(Long taskId) {
        log.info("重试任务: taskId={}", taskId);
        
        try {
            boolean success = taskService.retryTask(taskId);
            if (!success) {
                throw new RuntimeException("重试任务失败");
            }
            
            return getTaskById(taskId);
        } catch (Exception e) {
            log.error("重试任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            throw new RuntimeException("重试任务失败: " + e.getMessage());
        }
    }

    @Override
    public TaskResultDTO getTaskResult(Long taskId) {
        log.debug("获取任务结果: taskId={}", taskId);
        
        Task task = taskService.getTaskById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在: " + taskId);
        }
        
        return convertToResultDTO(task);
    }

    @Transactional
    @Override
    public TaskDTO updateTaskStatus(Long taskId, String status) {
        log.info("更新任务状态: taskId={}, status={}", taskId, status);

        // 使用TaskQueueService更新任务状态
        Task updatedTask = taskQueueService.updateTaskStatus(taskId, status);

        return convertToDTO(updatedTask);
    }

    @Transactional
    @Override
    public boolean deleteTask(Long taskId) {
        log.info("删除任务: taskId={}", taskId);
        
        try {
            // 这里需要实现删除逻辑
            return true;
        } catch (Exception e) {
            log.error("删除任务失败: taskId={}, error={}", taskId, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public PageResponseDTO<TaskSummaryDTO> searchTasks(String keyword, int page, int size) {
        log.debug("搜索任务: keyword={}, page={}, size={}", keyword, page, size);
        
        // 这里需要实现搜索逻辑
        return PageResponseDTO.<TaskSummaryDTO>builder()
                .content(Collections.emptyList())
                .page(page)
                .size(size)
                .totalElements(0L)
                .totalPages(0)
                .build();
    }

    @Override
    public List<TaskSummaryDTO> getUserTasks(Long userId) {
        log.debug("获取用户任务: userId={}", userId);
        
        // 这里需要实现获取用户任务的逻辑
        return Collections.emptyList();
    }

    @Override
    public List<TaskSummaryDTO> getRecentTasks(int limit) {
        log.debug("获取最近任务: limit={}", limit);

        try {
            // 获取所有任务并按创建时间排序
            List<Task> allTasks = taskService.getAllTasks();
            List<TaskSummaryDTO> recentTasks = allTasks.stream()
                    .sorted((t1, t2) -> t2.getCreatedAt().compareTo(t1.getCreatedAt())) // 按创建时间倒序
                    .limit(limit)
                    .map(this::convertToSummaryDTO)
                    .collect(Collectors.toList());

            return recentTasks;
        } catch (Exception e) {
            log.error("获取最近任务失败: limit={}, error={}", limit, e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    /**
     * 转换为DTO
     */
    private TaskDTO convertToDTO(Task task) {
        return TaskDTO.builder()
                .id(task.getId())
                .projectId(task.getProjectId())
                .name(task.getName())
                .description(task.getDescription())
                .type(task.getTaskType())
                .status(task.getStatus() != null ? task.getStatus().name() : null)
                .detailedStatus(task.getDetailedStatus())
                .priority(task.getPriority())
                .progress(task.getProgress())
                .createdAt(task.getCreatedAt())
                .updatedAt(task.getUpdatedAt())
                .processingStartedAt(task.getProcessingStartedAt())
                .timeoutAt(task.getTimeoutAt())
                .executionTime(task.getExecutionTime())
                .retryCount(task.getRetryCount())
                .maxRetries(task.getMaxRetries())
                .errorLog(task.getErrorLog())
                .lastErrorDetail(task.getLastErrorDetail())
                .result(task.getResult())
                .results(task.getResults())
                .parameters(task.getParameters())
                .userId(task.getUserId())
                .assigneeId(task.getAssigneeId())
                .workerId(task.getWorkerId())
                .overallStatus(task.getOverallStatus())
                .commitId(task.getCommitId())
                .taskVersion(task.getTaskVersion() != null ? task.getTaskVersion().toString() : null)
                .build();
    }

    /**
     * 转换为概要DTO
     */
    private TaskSummaryDTO convertToSummaryDTO(Task task) {
        // 获取项目名称
        String projectName = null;
        if (task.getProjectId() != null) {
            try {
                Project project = projectService.getProjectById(task.getProjectId());
                projectName = project != null ? project.getName() : "未知项目";
            } catch (Exception e) {
                log.warn("获取项目名称失败: projectId={}, error={}", task.getProjectId(), e.getMessage());
                projectName = "未知项目";
            }
        }

        return TaskSummaryDTO.builder()
                .id(task.getId())
                .projectId(task.getProjectId())
                .projectName(projectName)
                .name(task.getName())
                .type(task.getTaskType())
                .status(task.getStatus() != null ? task.getStatus().name() : null)
                .detailedStatus(task.getDetailedStatus())
                .priority(task.getPriority())
                .progress(task.getProgress())
                .createdAt(task.getCreatedAt())
                .processingStartedAt(task.getProcessingStartedAt())
                .executionTime(task.getExecutionTime())
                .retryCount(task.getRetryCount())
                .overallStatus(task.getOverallStatus())
                .commitId(task.getCommitId())
                .build();
    }

    /**
     * 转换为结果DTO
     */
    private TaskResultDTO convertToResultDTO(Task task) {
        return TaskResultDTO.builder()
                .taskId(task.getId())
                .projectId(task.getProjectId())
                .status(task.getStatus() != null ? task.getStatus().name() : null)
                .overallStatus(task.getOverallStatus())
                .result(task.getResult())
                .results(task.getResults())
                .errorMessage(task.getErrorLog())
                .errorDetails(task.getLastErrorDetail())
                .executionTime(task.getExecutionTime())
                .startTime(task.getProcessingStartedAt())
                .endTime(task.getUpdatedAt())
                .commitId(task.getCommitId())
                .build();
    }

    /**
     * 对任务DTO列表进行排序
     */
    private List<TaskSummaryDTO> sortTaskDTOs(List<TaskSummaryDTO> taskDTOs, String sortBy, String direction) {
        if (taskDTOs == null || taskDTOs.isEmpty()) {
            return taskDTOs;
        }

        boolean isAsc = "asc".equalsIgnoreCase(direction);

        return taskDTOs.stream()
                .sorted((t1, t2) -> {
                    int result = 0;

                    switch (sortBy.toLowerCase()) {
                        case "id":
                            result = Long.compare(t1.getId(), t2.getId());
                            break;
                        case "name":
                            result = t1.getName().compareTo(t2.getName());
                            break;
                        case "status":
                            result = t1.getStatus().compareTo(t2.getStatus());
                            break;
                        case "priority":
                            result = Integer.compare(t1.getPriority(), t2.getPriority());
                            break;
                        case "progress":
                            result = Integer.compare(t1.getProgress(), t2.getProgress());
                            break;
                        case "createdat":
                        case "created_at":
                            // 按创建时间排序
                            if (t1.getCreatedAt() != null && t2.getCreatedAt() != null) {
                                result = t1.getCreatedAt().compareTo(t2.getCreatedAt());
                            } else if (t1.getCreatedAt() != null) {
                                result = 1;
                            } else if (t2.getCreatedAt() != null) {
                                result = -1;
                            }
                            break;
                        default:
                            // 默认按ID排序
                            result = Long.compare(t1.getId(), t2.getId());
                            break;
                    }

                    return isAsc ? result : -result;
                })
                .collect(Collectors.toList());
    }
}

package com.archscope.app.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 服务发现批量处理服务
 * 临时创建以解决AOP启动问题
 */
@Slf4j
@Service
public class ServiceDiscoveryBatchService {

    /**
     * 批量处理服务发现
     */
    public void batchProcessServices() {
        log.info("批量处理服务发现 - 临时实现");
    }
    
    /**
     * 批量更新服务状态
     */
    public void batchUpdateServiceStatus() {
        log.info("批量更新服务状态 - 临时实现");
    }
}

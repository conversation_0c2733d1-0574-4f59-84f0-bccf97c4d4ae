package com.archscope.app.service;

import com.archscope.app.command.RecommendationFeedbackCommand;
import com.archscope.app.command.RequirementMatchingCommand;
import com.archscope.app.dto.FeedbackDTO;
import com.archscope.app.dto.RequirementDTO;
import com.archscope.app.dto.ServiceRecommendationDTO;

import java.util.List;

/**
 * 需求匹配应用服务接口
 */
public interface RequirementMatchingService {

    /**
     * 根据需求查找匹配的服务
     *
     * @param command 需求匹配命令
     * @return 服务推荐列表，按匹配度排序
     */
    List<ServiceRecommendationDTO> findServicesForRequirement(RequirementMatchingCommand command);

    /**
     * 根据需求DTO查找匹配的服务
     *
     * @param requirement 需求DTO
     * @return 服务推荐列表，按匹配度排序
     */
    List<ServiceRecommendationDTO> findServicesForRequirement(RequirementDTO requirement);

    /**
     * 记录推荐反馈
     *
     * @param command 推荐反馈命令
     * @return 反馈DTO
     */
    FeedbackDTO recordRecommendationFeedback(RecommendationFeedbackCommand command);

    /**
     * 根据推荐ID记录反馈
     *
     * @param recommendationId 推荐ID
     * @param feedback 反馈DTO
     * @return 反馈DTO
     */
    FeedbackDTO recordRecommendationFeedback(String recommendationId, FeedbackDTO feedback);

    /**
     * 根据需求描述生成推荐的能力需求
     *
     * @param description 需求描述
     * @return 推荐的能力需求列表
     */
    List<String> generateCapabilityRequirements(String description);

    /**
     * 获取服务的推荐分数
     *
     * @param serviceIds 服务ID列表
     * @return 服务推荐DTO列表，包含推荐分数
     */
    List<ServiceRecommendationDTO> getServiceRecommendations(List<String> serviceIds);
}
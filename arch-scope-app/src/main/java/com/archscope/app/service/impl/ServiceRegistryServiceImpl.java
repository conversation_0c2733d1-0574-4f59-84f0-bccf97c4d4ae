package com.archscope.app.service.impl;

import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.command.ServiceRegistrationCommand;
import com.archscope.app.command.ServiceUpdateCommand;
import com.archscope.app.service.ServiceRegistryService;
import org.springframework.stereotype.Service;

/**
 * 服务注册应用服务实现
 * 临时实现以解决启动问题
 */
@Service
public class ServiceRegistryServiceImpl implements ServiceRegistryService {

    @Override
    public ServiceDTO registerService(ServiceRegistrationCommand command) {
        System.out.println("注册服务");
        return new ServiceDTO();
    }

    @Override
    public ServiceDTO updateService(String serviceId, ServiceUpdateCommand command) {
        System.out.println("更新服务 - serviceId: " + serviceId);
        return new ServiceDTO();
    }

    @Override
    public void deregisterService(String serviceId) {
        System.out.println("注销服务 - serviceId: " + serviceId);
    }

    @Override
    public ServiceDTO getServiceById(String serviceId) {
        System.out.println("获取服务 - serviceId: " + serviceId);
        return new ServiceDTO();
    }
}

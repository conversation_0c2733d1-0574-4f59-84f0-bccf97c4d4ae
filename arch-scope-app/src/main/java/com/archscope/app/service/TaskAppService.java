package com.archscope.app.service;

import com.archscope.facade.dto.*;

import java.util.List;

/**
 * 任务应用服务接口
 * 负责处理任务相关的业务用例，协调领域对象和服务
 */
public interface TaskAppService {
    
    /**
     * 创建新任务
     * @param createDTO 任务创建信息
     * @return 创建的任务
     */
    TaskDTO createTask(TaskCreateDTO createDTO);
    
    /**
     * 获取任务详情
     * @param taskId 任务ID
     * @return 任务详情
     */
    TaskDTO getTaskById(Long taskId);
    
    /**
     * 获取项目的任务列表
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<TaskSummaryDTO> getTasksByProject(Long projectId);
    
    /**
     * 获取项目的任务列表（分页）
     * @param projectId 项目ID
     * @param page 页码
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向
     * @return 分页任务列表
     */
    PageResponseDTO<TaskSummaryDTO> getTasksByProject(Long projectId, int page, int size, String sortBy, String direction);
    
    /**
     * 获取所有任务列表（公共查看）
     * @param page 页码
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向
     * @return 分页任务列表
     */
    PageResponseDTO<TaskSummaryDTO> getAllTasks(int page, int size, String sortBy, String direction);
    
    /**
     * 根据状态筛选任务
     * @param status 任务状态
     * @param page 页码
     * @param size 每页大小
     * @return 分页任务列表
     */
    PageResponseDTO<TaskSummaryDTO> getTasksByStatus(String status, int page, int size);
    
    /**
     * 取消任务
     * @param taskId 任务ID
     * @return 操作结果
     */
    boolean cancelTask(Long taskId);
    
    /**
     * 重试任务
     * @param taskId 任务ID
     * @return 重试后的任务信息
     */
    TaskDTO retryTask(Long taskId);
    
    /**
     * 获取任务结果
     * @param taskId 任务ID
     * @return 任务结果
     */
    TaskResultDTO getTaskResult(Long taskId);
    
    /**
     * 更新任务状态
     * @param taskId 任务ID
     * @param status 新状态
     * @return 更新后的任务信息
     */
    TaskDTO updateTaskStatus(Long taskId, String status);
    
    /**
     * 删除任务
     * @param taskId 任务ID
     * @return 操作结果
     */
    boolean deleteTask(Long taskId);
    
    /**
     * 搜索任务
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    PageResponseDTO<TaskSummaryDTO> searchTasks(String keyword, int page, int size);
    
    /**
     * 获取用户的任务
     * @param userId 用户ID
     * @return 任务列表
     */
    List<TaskSummaryDTO> getUserTasks(Long userId);
    
    /**
     * 获取最近的任务
     * @param limit 限制数量
     * @return 任务列表
     */
    List<TaskSummaryDTO> getRecentTasks(int limit);
}

package com.archscope.app.service.impl;

import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.dto.ServiceQueryRequest;
import com.archscope.app.service.ServiceDiscoveryService;
import com.archscope.domain.exception.InvalidServiceDataException;
import com.archscope.domain.exception.ServiceNotFoundException;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.valueobject.PageResult;
import com.archscope.domain.valueobject.PageRequest;
import com.archscope.app.assembler.ServiceAssembler;
import com.archscope.facade.dto.PageResponseDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 服务发现应用服务实现
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ServiceDiscoveryServiceImpl implements ServiceDiscoveryService {

    private final ServiceRepository serviceRepository;
    private final ServiceAssembler serviceAssembler;

    @Override
    public List<ServiceDTO> findAllActiveServices() {
        log.debug("查找所有活跃服务");
        try {
            List<Service> services = serviceRepository.findByStatus("ACTIVE");
            return services.stream()
                    .map(serviceAssembler::toDTO)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查找活跃服务时发生异常", e);
            throw new InvalidServiceDataException("查找活跃服务失败", e);
        }
    }

    @Override
    public PageResponseDTO<ServiceDTO> findServices(int page, int size) {
        validatePaginationParameters(page, size);
        return findServicesWithSort(page, size, "registeredAt", "DESC");
    }

    @Override
    public PageResponseDTO<ServiceDTO> findServicesWithSort(int page, int size, String sortBy, String sortDirection) {
        log.debug("分页查询服务: page={}, size={}, sortBy={}, sortDirection={}", page, size, sortBy, sortDirection);

        // 参数校验
        validatePaginationParameters(page, size);
        validateSortParameters(sortBy, sortDirection);

        try {
            PageRequest.SortDirection direction = "ASC".equalsIgnoreCase(sortDirection)
                    ? PageRequest.SortDirection.ASC : PageRequest.SortDirection.DESC;
            PageRequest pageRequest = PageRequest.of(page, size, sortBy, direction);

            PageResult<Service> servicePage = serviceRepository.findAll(pageRequest);
        
        List<ServiceDTO> serviceDTOs = servicePage.getContent().stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
        
        return PageResponseDTO.<ServiceDTO>builder()
                .content(serviceDTOs)
                .page(page)
                .size(size)
                .totalElements(servicePage.getTotalElements())
                .totalPages(servicePage.getTotalPages())
                .first(servicePage.isFirst())
                .last(servicePage.isLast())
                .build();
        } catch (Exception e) {
            log.error("分页查询服务时发生异常: page={}, size={}, sortBy={}, sortDirection={}",
                     page, size, sortBy, sortDirection, e);
            throw new InvalidServiceDataException("分页查询服务失败", e);
        }
    }

    @Override
    public List<ServiceDTO> findServicesByName(String name) {
        log.debug("根据名称搜索服务: name={}", name);
        List<Service> services = serviceRepository.findByNameContainingIgnoreCase(name);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByType(String type) {
        log.debug("根据类型查找服务: type={}", type);
        List<Service> services = serviceRepository.findByType(type);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByStatus(String status) {
        log.debug("根据状态查找服务: status={}", status);
        List<Service> services = serviceRepository.findByStatus(status);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByTags(List<String> tags) {
        log.debug("根据标签查找服务: tags={}", tags);
        List<Service> services = serviceRepository.findByTagsIn(tags);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByCapability(String capabilityName) {
        log.debug("根据能力查找服务: capabilityName={}", capabilityName);
        List<Service> services = serviceRepository.findByCapabilityName(capabilityName);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceDTO> findServicesByMavenCoordinates(String groupId, String artifactId) {
        log.debug("根据Maven坐标查找服务: groupId={}, artifactId={}", groupId, artifactId);
        List<Service> services = serviceRepository.findByGroupIdAndArtifactId(groupId, artifactId);
        return services.stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
    }

    @Override
    public ServiceDTO findServiceByExactMavenCoordinates(String groupId, String artifactId, String version) {
        log.debug("根据精确Maven坐标查找服务: groupId={}, artifactId={}, version={}", groupId, artifactId, version);
        Service service = serviceRepository.findByGroupIdAndArtifactIdAndVersion(groupId, artifactId, version);
        return service != null ? serviceAssembler.toDTO(service) : null;
    }

    @Override
    public PageResponseDTO<ServiceDTO> findServicesByCriteria(String name, String type, String status, 
                                                            List<String> tags, int page, int size) {
        ServiceQueryRequest queryRequest = ServiceQueryRequest.builder()
                .name(name)
                .type(type)
                .status(status)
                .tags(tags)
                .page(page)
                .size(size)
                .sortBy("registeredAt")
                .sortDirection(ServiceQueryRequest.SortDirection.DESC)
                .build();
        return findServicesByCriteria(queryRequest);
    }

    @Override
    public PageResponseDTO<ServiceDTO> findServicesByCriteria(ServiceQueryRequest queryRequest) {
        log.debug("复杂条件查询服务: {}", queryRequest);
        
        PageRequest.SortDirection direction = ServiceQueryRequest.SortDirection.ASC.equals(queryRequest.getSortDirection())
                ? PageRequest.SortDirection.ASC : PageRequest.SortDirection.DESC;
        PageRequest pageRequest = PageRequest.of(queryRequest.getPage(), queryRequest.getSize(), 
                queryRequest.getSortBy(), direction);
        
        PageResult<Service> servicePage = serviceRepository.findByCriteria(
                queryRequest.getName(),
                queryRequest.getType(),
                queryRequest.getStatus(),
                queryRequest.getTags(),
                queryRequest.getGroupId(),
                queryRequest.getArtifactId(),
                queryRequest.getCapabilityName(),
                pageRequest
        );
        
        List<ServiceDTO> serviceDTOs = servicePage.getContent().stream()
                .map(serviceAssembler::toDTO)
                .collect(Collectors.toList());
        
        return PageResponseDTO.<ServiceDTO>builder()
                .content(serviceDTOs)
                .page(queryRequest.getPage())
                .size(queryRequest.getSize())
                .totalElements(servicePage.getTotalElements())
                .totalPages(servicePage.getTotalPages())
                .first(servicePage.isFirst())
                .last(servicePage.isLast())
                .build();
    }

    @Override
    public ServiceDTO getServiceById(String serviceId) {
        log.debug("根据ID获取服务: serviceId={}", serviceId);
        Service service = serviceRepository.findById(serviceId);
        return service != null ? serviceAssembler.toDTO(service) : null;
    }

    @Override
    public Object getServiceStatistics() {
        log.debug("获取服务统计信息");
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 总服务数
        long totalServices = serviceRepository.count();
        statistics.put("totalServices", totalServices);
        
        // 活跃服务数
        long activeServices = serviceRepository.countByStatus("ACTIVE");
        statistics.put("activeServices", activeServices);
        
        // 按状态统计
        Map<String, Long> statusStats = serviceRepository.countByStatusGrouped();
        statistics.put("statusStatistics", statusStats);
        
        // 按类型统计
        Map<String, Long> typeStats = serviceRepository.countByTypeGrouped();
        statistics.put("typeStatistics", typeStats);
        
        // 最近注册的服务数（最近7天）
        long recentServices = serviceRepository.countRecentServices(7);
        statistics.put("recentServices", recentServices);
        
        return statistics;
    }

    // ==================== 参数校验方法 ====================

    /**
     * 校验分页参数
     */
    private void validatePaginationParameters(int page, int size) {
        if (page < 0) {
            throw new InvalidServiceDataException("页码不能小于0: " + page);
        }
        if (size <= 0) {
            throw new InvalidServiceDataException("页面大小必须大于0: " + size);
        }
        if (size > 1000) {
            throw new InvalidServiceDataException("页面大小不能超过1000: " + size);
        }
    }

    /**
     * 校验字符串参数
     */
    private void validateStringParameter(String value, String paramName) {
        if (!StringUtils.hasText(value)) {
            throw new InvalidServiceDataException(paramName + "不能为空");
        }
        if (value.length() > 255) {
            throw new InvalidServiceDataException(paramName + "长度不能超过255个字符");
        }
    }

    /**
     * 校验服务ID参数
     */
    private void validateServiceId(String serviceId) {
        validateStringParameter(serviceId, "服务ID");
    }

    /**
     * 校验服务类型
     */
    private void validateServiceType(String type) {
        if (!StringUtils.hasText(type)) {
            return; // 允许为空
        }
        try {
            ServiceType.valueOf(type);
        } catch (IllegalArgumentException e) {
            throw new InvalidServiceDataException("无效的服务类型: " + type);
        }
    }

    /**
     * 校验排序参数
     */
    private void validateSortParameters(String sortBy, String sortDirection) {
        if (StringUtils.hasText(sortBy) && sortBy.length() > 50) {
            throw new InvalidServiceDataException("排序字段长度不能超过50个字符");
        }
        if (StringUtils.hasText(sortDirection) &&
            !"ASC".equalsIgnoreCase(sortDirection) &&
            !"DESC".equalsIgnoreCase(sortDirection)) {
            throw new InvalidServiceDataException("排序方向只能是ASC或DESC: " + sortDirection);
        }
    }

    /**
     * 校验Maven坐标参数
     */
    private void validateMavenCoordinates(String groupId, String artifactId, String version) {
        if (StringUtils.hasText(groupId) && groupId.length() > 255) {
            throw new InvalidServiceDataException("groupId长度不能超过255个字符");
        }
        if (StringUtils.hasText(artifactId) && artifactId.length() > 255) {
            throw new InvalidServiceDataException("artifactId长度不能超过255个字符");
        }
        if (StringUtils.hasText(version) && version.length() > 50) {
            throw new InvalidServiceDataException("version长度不能超过50个字符");
        }
    }
}
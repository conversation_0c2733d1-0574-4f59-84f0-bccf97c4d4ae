package com.archscope.app.service;

import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.command.ServiceRegistrationCommand;
import com.archscope.app.command.ServiceUpdateCommand;

/**
 * 服务注册应用服务接口
 */
public interface ServiceRegistryService {

    /**
     * 注册服务
     *
     * @param command 服务注册命令
     * @return 注册成功的服务DTO
     */
    ServiceDTO registerService(ServiceRegistrationCommand command);

    /**
     * 更新服务
     *
     * @param serviceId 服务ID
     * @param command 服务更新命令
     * @return 更新后的服务DTO
     */
    ServiceDTO updateService(String serviceId, ServiceUpdateCommand command);

    /**
     * 注销服务
     *
     * @param serviceId 服务ID
     */
    void deregisterService(String serviceId);

    /**
     * 根据ID获取服务
     *
     * @param serviceId 服务ID
     * @return 服务DTO
     */
    ServiceDTO getServiceById(String serviceId);
}
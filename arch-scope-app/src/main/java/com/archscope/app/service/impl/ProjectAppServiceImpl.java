package com.archscope.app.service.impl;

import com.archscope.app.service.ProjectAppService;
import com.archscope.domain.entity.Project;
import com.archscope.domain.entity.Task;
import com.archscope.domain.service.ProjectService;
import com.archscope.domain.service.TaskService;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.util.GitUrlNormalizer;
import com.archscope.domain.valueobject.TaskType;
import com.archscope.facade.dto.PageResponseDTO;
import com.archscope.facade.dto.ProjectDTO;
import com.archscope.facade.dto.ProjectRegistrationDTO;
import com.archscope.facade.dto.ProjectSummaryDTO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 项目应用服务实现
 * 负责处理项目相关的业务用例，协调领域对象和服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectAppServiceImpl implements ProjectAppService {

    private final ProjectService projectService;
    private final ProjectRepository projectRepository;
    private final TaskService taskService;
    
    @Transactional
    @Override
    public ProjectDTO registerProject(ProjectRegistrationDTO registrationDTO) {
        // 1. 注册项目
        Project project = projectService.registerProject(
            registrationDTO.getName(),
            registrationDTO.getDescription(),
            registrationDTO.getRepositoryUrl(),
            registrationDTO.getBranch()
        );

        // 2. 创建项目全量分析任务
        try {
            log.info("项目注册成功，开始创建项目全量分析任务: projectId={}, repositoryUrl={}",
                    project.getId(), project.getRepositoryUrl());

            // 构建项目全量分析任务参数，包含完整的项目信息
            String analysisTaskParameters = String.format(
                "{\"projectId\":%d,\"projectName\":\"%s\",\"projectType\":\"%s\",\"description\":\"%s\",\"repositoryUrl\":\"%s\",\"branch\":\"%s\",\"analysisType\":\"FULL\",\"includeArchDiagrams\":true,\"outputFormat\":\"markdown\"}",
                project.getId(),
                project.getName(),
                project.getType() != null ? project.getType().name() : "OTHER",
                project.getDescription() != null ? project.getDescription().replace("\"", "\\\"") : "",
                project.getRepositoryUrl(),
                project.getBranch()
            );

            // 创建项目全量分析任务
            Task analysisTask = taskService.createTask(
                project.getId(),
                TaskType.PROJECT_ANALYSIS,
                analysisTaskParameters
            );

            // 提交任务到队列
            boolean submitted = taskService.submitTaskToQueue(analysisTask.getId());

            if (submitted) {
                log.info("项目全量分析任务创建并提交成功: taskId={}, projectId={}",
                        analysisTask.getId(), project.getId());
            } else {
                log.warn("项目全量分析任务创建成功但提交失败: taskId={}, projectId={}",
                        analysisTask.getId(), project.getId());
            }
        } catch (Exception e) {
            log.error("创建项目全量分析任务失败: projectId={}, error={}",
                    project.getId(), e.getMessage(), e);
            // 不抛出异常，避免影响项目注册流程
        }

        return convertToDTO(project);
    }
    
    @Override
    public ProjectDTO getProjectById(Long id) {
        Project project = projectService.getProjectById(id);
        return convertToDTO(project);
    }
    
    @Transactional
    @Override
    public ProjectDTO updateProject(Long id, ProjectDTO projectDTO) {
        Project project = projectService.getProjectById(id);
        
        // 更新项目属性
        project.setName(projectDTO.getName());
        project.setDescription(projectDTO.getDescription());
        project.setRepositoryUrl(projectDTO.getRepositoryUrl());
        project.setBranch(projectDTO.getBranch());
        
        Project updatedProject = projectRepository.save(project);
        return convertToDTO(updatedProject);
    }
    
    @Transactional
    @Override
    public void deleteProject(Long id) {
        projectRepository.deleteById(id);
    }
    
    @Override
    public PageResponseDTO<ProjectSummaryDTO> getProjects(int page, int size, String sortBy, String direction) {
        // 实现分页查询逻辑
        List<Project> projects = projectRepository.findAll(page, size);
        long total = projectRepository.count();
        
        List<ProjectSummaryDTO> projectDTOs = projects.stream()
            .map(this::convertToSummaryDTO)
            .collect(Collectors.toList());
        
        return PageResponseDTO.<ProjectSummaryDTO>builder()
            .content(projectDTOs)
            .page(page)
            .size(size)
            .totalElements(total)
            .totalPages((int) Math.ceil((double) total / size))
            .first(page == 0)
            .last((page + 1) * size >= total)
            .build();
    }
    
    @Override
    public PageResponseDTO<ProjectSummaryDTO> searchProjects(String keyword, int page, int size) {
        // 实现搜索逻辑
        // 这里简化处理，实际应该调用 repository 的搜索方法
        List<Project> projects = projectRepository.findAll(page, size);
        long total = projectRepository.count();
        
        List<ProjectSummaryDTO> projectDTOs = projects.stream()
            .filter(p -> p.getName().contains(keyword) || 
                   (p.getDescription() != null && p.getDescription().contains(keyword)))
            .map(this::convertToSummaryDTO)
            .collect(Collectors.toList());
        
        return PageResponseDTO.<ProjectSummaryDTO>builder()
            .content(projectDTOs)
            .page(page)
            .size(size)
            .totalElements(projectDTOs.size())
            .totalPages(1)
            .first(true)
            .last(true)
            .build();
    }
    
    @Override
    public List<ProjectSummaryDTO> getCurrentUserProjects() {
        // 获取当前用户ID (这里简化处理，实际应该从安全上下文获取)
        Long currentUserId = 798L; // 假设的当前用户ID
        
        List<Project> projects = projectRepository.findByCreatorId(currentUserId);
        return projects.stream()
            .map(this::convertToSummaryDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<ProjectSummaryDTO> getRecentProjects(int limit) {
        List<Project> projects = projectRepository.findRecentProjects(limit);
        return projects.stream()
            .map(this::convertToSummaryDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public List<ProjectSummaryDTO> getTopRatedProjects(int limit) {
        List<Project> projects = projectRepository.findTopRatedProjects(limit);
        return projects.stream()
            .map(this::convertToSummaryDTO)
            .collect(Collectors.toList());
    }
    
    @Override
    public boolean isRepositoryUrlExists(String url) {
        // 首先标准化URL
        String normalizedUrl = GitUrlNormalizer.normalize(url);
        if (normalizedUrl == null) {
            // 如果无法标准化，回退到原始URL检查
            return projectRepository.findByRepositoryUrl(url).isPresent();
        }

        // 使用标准化URL检查是否存在
        return projectRepository.findByNormalizedRepositoryUrl(normalizedUrl).isPresent();
    }
    
    @Transactional
    @Override
    public boolean addUserToProject(Long projectId, Long userId) {
        // 实现添加用户到项目的逻辑
        // 这里简化处理，实际应该有更复杂的权限检查和关联关系处理
        Project project = projectService.getProjectById(projectId);
        if (project.getMemberIds() == null) {
            project.setMemberIds(new ArrayList<>());
        }
        if (!project.getMemberIds().contains(userId)) {
            project.getMemberIds().add(userId);
            projectRepository.save(project);
            return true;
        }
        return false;
    }
    
    @Transactional
    @Override
    public boolean removeUserFromProject(Long projectId, Long userId) {
        // 实现从项目移除用户的逻辑
        Project project = projectService.getProjectById(projectId);
        if (project.getMemberIds() != null && project.getMemberIds().contains(userId)) {
            project.getMemberIds().remove(userId);
            projectRepository.save(project);
            return true;
        }
        return false;
    }

    // 辅助方法：将实体转换为DTO
    private ProjectDTO convertToDTO(Project project) {
        return ProjectDTO.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .repositoryUrl(project.getRepositoryUrl())
                .branch(project.getBranch())
                .status(project.getStatus())
                .rating(project.getRating())
                .linesOfCode(project.getLinesOfCode())
                .fileCount(project.getFileCount())
                .contributorCount(project.getContributorCount())
                .icon(project.getIcon())
                .type(project.getType() != null ? project.getType().name() : "OTHER")
                .memberIds(project.getMemberIds())
                .documentIds(project.getDocumentIds())
                .taskIds(project.getTaskIds())
                .creatorId(project.getCreatorId())
                .active(project.getActive())
                .createdAt(project.getCreatedAt())
                .updatedAt(project.getUpdatedAt())
                .lastAnalyzedAt(project.getLastAnalyzedAt())
                .build();
    }

    // 辅助方法：将实体转换为摘要DTO
    private ProjectSummaryDTO convertToSummaryDTO(Project project) {
        return ProjectSummaryDTO.builder()
                .id(project.getId())
                .name(project.getName())
                .description(project.getDescription())
                .repositoryUrl(project.getRepositoryUrl())
                .branch(project.getBranch())
                .status(project.getStatus())
                .rating(project.getRating())
                .icon(project.getIcon())
                .type(project.getType() != null ? project.getType().name() : "OTHER")
                .createdAt(project.getCreatedAt())
                .updatedAt(project.getUpdatedAt())
                .active(project.getActive())
                .build();
    }

}

package com.archscope.app.service;

import com.archscope.domain.entity.Project;
import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.LlmTaskService;
import com.archscope.domain.service.git.GitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;

/**
 * 项目分析服务
 * 整合LLM任务创建到项目分析流程中
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ProjectAnalysisService {

    private final LlmTaskService llmTaskService;
    private final ProjectRepository projectRepository;
    private final GitService gitService;

    /**
     * 触发项目代码分析
     * 创建LLM分析任务并更新项目状态
     * 
     * @param projectId 项目ID
     * @param commitId 指定的提交ID (可选，如果为空则使用最新提交)
     * @return 创建的任务
     */
    @Transactional
    public Task triggerProjectAnalysis(Long projectId, String commitId) {
        log.info("触发项目分析: projectId={}, commitId={}", projectId, commitId);

        // 获取项目信息
        Project project = projectRepository.findById(projectId)
                .orElseThrow(() -> new IllegalArgumentException("项目不存在: " + projectId));

        // 如果没有指定commitId，尝试获取最新的提交ID
        String targetCommitId = commitId;
        if (targetCommitId == null || targetCommitId.trim().isEmpty()) {
            targetCommitId = getLatestCommitId(project);
        }

        // 验证commitId格式
        if (targetCommitId == null || !isValidCommitId(targetCommitId)) {
            throw new IllegalArgumentException("无效的提交ID: " + targetCommitId);
        }

        // 更新项目状态为分析中
        project.setStatus("ANALYZING");
        project.setUpdatedAt(LocalDateTime.now());
        projectRepository.save(project);

        // 创建LLM分析任务
        Task task = llmTaskService.createLlmTask(
                projectId,
                project.getRepositoryUrl(),
                targetCommitId,
                project.getBranch(),
                "CODE_PARSE",
                5 // 默认优先级
        );

        log.info("项目分析任务已创建: projectId={}, taskId={}, commitId={}", 
                projectId, task.getId(), targetCommitId);

        return task;
    }

    /**
     * 批量触发多个项目的分析
     * 
     * @param projectIds 项目ID列表
     * @return 创建的任务数量
     */
    @Transactional
    public int triggerBatchAnalysis(Long... projectIds) {
        log.info("批量触发项目分析: 项目数量={}", projectIds.length);

        int successCount = 0;
        for (Long projectId : projectIds) {
            try {
                triggerProjectAnalysis(projectId, null);
                successCount++;
            } catch (Exception e) {
                log.error("项目分析失败: projectId=" + projectId, e);
            }
        }

        log.info("批量分析完成: 成功={}, 总数={}", successCount, projectIds.length);
        return successCount;
    }

    /**
     * 重新分析项目 (使用指定的提交ID)
     * 
     * @param projectId 项目ID
     * @param commitId 提交ID
     * @return 创建的任务
     */
    @Transactional
    public Task reanalyzeProject(Long projectId, String commitId) {
        log.info("重新分析项目: projectId={}, commitId={}", projectId, commitId);

        if (commitId == null || commitId.trim().isEmpty()) {
            throw new IllegalArgumentException("重新分析时必须指定提交ID");
        }

        return triggerProjectAnalysis(projectId, commitId);
    }

    /**
     * 检查项目是否可以进行分析
     * 
     * @param projectId 项目ID
     * @return 是否可以分析
     */
    public boolean canAnalyzeProject(Long projectId) {
        try {
            Project project = projectRepository.findById(projectId)
                    .orElse(null);

            if (project == null) {
                log.warn("项目不存在: projectId={}", projectId);
                return false;
            }

            // 检查项目状态
            String status = project.getStatus();
            if ("ANALYZING".equals(status)) {
                log.info("项目正在分析中，无法重复分析: projectId={}", projectId);
                return false;
            }

            // 检查仓库URL是否有效
            if (project.getRepositoryUrl() == null || project.getRepositoryUrl().trim().isEmpty()) {
                log.warn("项目仓库URL为空: projectId={}", projectId);
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("检查项目分析状态失败: projectId=" + projectId, e);
            return false;
        }
    }

    /**
     * 获取项目的最新提交ID
     * 
     * @param project 项目实体
     * @return 最新提交ID
     */
    private String getLatestCommitId(Project project) {
        try {
            // TODO: 实现获取最新提交ID的逻辑
            // 这里可以调用Git服务获取远程仓库的最新提交
            // 暂时返回一个模拟的提交ID或使用项目中存储的最新分析提交ID
            
            String latestCommitId = project.getLatestAnalyzedCommitId();
            if (latestCommitId != null && !latestCommitId.trim().isEmpty()) {
                log.debug("使用项目存储的最新提交ID: {}", latestCommitId);
                return latestCommitId;
            }

            // 如果没有存储的提交ID，生成一个模拟的提交ID用于测试
            // 在实际实现中，这里应该调用Git服务获取真实的最新提交ID
            String mockCommitId = generateMockCommitId();
            log.warn("无法获取真实的最新提交ID，使用模拟ID: {}", mockCommitId);
            return mockCommitId;

        } catch (Exception e) {
            log.error("获取最新提交ID失败: projectId=" + project.getId(), e);
            return generateMockCommitId();
        }
    }

    /**
     * 验证提交ID格式是否正确
     * 
     * @param commitId 提交ID
     * @return 是否有效
     */
    private boolean isValidCommitId(String commitId) {
        if (commitId == null || commitId.trim().isEmpty()) {
            return false;
        }
        
        // Git提交ID应该是40位十六进制字符
        return commitId.matches("^[0-9a-f]{40}$");
    }

    /**
     * 生成模拟的提交ID (仅用于测试)
     * 
     * @return 模拟的40位十六进制提交ID
     */
    private String generateMockCommitId() {
        // 生成一个基于当前时间的模拟提交ID
        long timestamp = System.currentTimeMillis();
        String timestampHex = Long.toHexString(timestamp);
        
        // 补齐到40位
        StringBuilder commitId = new StringBuilder(timestampHex);
        while (commitId.length() < 40) {
            commitId.append("0");
        }
        
        return commitId.toString();
    }
}

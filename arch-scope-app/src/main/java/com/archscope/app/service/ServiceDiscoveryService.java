package com.archscope.app.service;

import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.dto.ServiceQueryRequest;
import com.archscope.facade.dto.PageResponseDTO;

import java.util.List;

/**
 * 服务发现应用服务接口
 */
public interface ServiceDiscoveryService {

    /**
     * 查找所有活跃服务
     *
     * @return 所有活跃服务列表
     */
    List<ServiceDTO> findAllActiveServices();

    /**
     * 分页查询服务
     *
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页的服务列表
     */
    PageResponseDTO<ServiceDTO> findServices(int page, int size);

    /**
     * 根据名称搜索服务
     *
     * @param name 服务名称（支持模糊匹配）
     * @return 匹配的服务列表
     */
    List<ServiceDTO> findServicesByName(String name);

    /**
     * 根据类型查找服务
     *
     * @param type 服务类型
     * @return 匹配的服务列表
     */
    List<ServiceDTO> findServicesByType(String type);

    /**
     * 根据状态查找服务
     *
     * @param status 服务状态
     * @return 匹配的服务列表
     */
    List<ServiceDTO> findServicesByStatus(String status);

    /**
     * 根据标签查找服务
     *
     * @param tags 标签列表
     * @return 匹配的服务列表
     */
    List<ServiceDTO> findServicesByTags(List<String> tags);

    /**
     * 根据能力名称查找提供该能力的服务
     *
     * @param capabilityName 能力名称
     * @return 提供该能力的服务列表
     */
    List<ServiceDTO> findServicesByCapability(String capabilityName);

    /**
     * 根据Maven坐标查找服务（不指定版本）
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @return 匹配的服务列表
     */
    List<ServiceDTO> findServicesByMavenCoordinates(String groupId, String artifactId);

    /**
     * 根据精确的Maven坐标查找服务（包含版本）
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @param version 服务版本
     * @return 匹配的服务，如果不存在则返回null
     */
    ServiceDTO findServiceByExactMavenCoordinates(String groupId, String artifactId, String version);

    /**
     * 组合条件查询服务
     *
     * @param name 服务名称（可选，支持模糊匹配）
     * @param type 服务类型（可选）
     * @param status 服务状态（可选）
     * @param tags 标签列表（可选）
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 分页的服务列表
     */
    PageResponseDTO<ServiceDTO> findServicesByCriteria(String name, String type, String status, 
                                                      List<String> tags, int page, int size);

    /**
     * 分页查询服务（支持排序）
     *
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param sortDirection 排序方向（ASC/DESC）
     * @return 分页的服务列表
     */
    PageResponseDTO<ServiceDTO> findServicesWithSort(int page, int size, String sortBy, String sortDirection);

    /**
     * 使用查询请求对象进行复杂查询
     *
     * @param queryRequest 查询请求对象
     * @return 分页的服务列表
     */
    PageResponseDTO<ServiceDTO> findServicesByCriteria(ServiceQueryRequest queryRequest);

    /**
     * 根据服务ID获取服务详细信息
     *
     * @param serviceId 服务ID
     * @return 服务详细信息，如果不存在则返回null
     */
    ServiceDTO getServiceById(String serviceId);

    /**
     * 获取服务统计信息
     *
     * @return 服务统计信息
     */
    Object getServiceStatistics();
}
package com.archscope.app.service;

import com.archscope.facade.dto.PageResponseDTO;
import com.archscope.facade.dto.ProjectDTO;
import com.archscope.facade.dto.ProjectRegistrationDTO;
import com.archscope.facade.dto.ProjectSummaryDTO;

import java.util.List;

/**
 * 项目应用服务接口
 * 负责处理项目相关的业务用例，协调领域对象和服务
 */
public interface ProjectAppService {
    
    /**
     * 注册新项目
     * @param registrationDTO 项目注册信息
     * @return 注册结果
     */
    ProjectDTO registerProject(ProjectRegistrationDTO registrationDTO);
    
    /**
     * 获取项目详情
     * @param id 项目ID
     * @return 项目详情
     */
    ProjectDTO getProjectById(Long id);
    
    /**
     * 更新项目信息
     * @param id 项目ID
     * @param projectDTO 项目信息
     * @return 更新后的项目信息
     */
    ProjectDTO updateProject(Long id, ProjectDTO projectDTO);
    
    /**
     * 删除项目
     * @param id 项目ID
     */
    void deleteProject(Long id);
    
    /**
     * 分页查询项目
     * @param page 页码
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向
     * @return 分页项目列表
     */
    PageResponseDTO<ProjectSummaryDTO> getProjects(int page, int size, String sortBy, String direction);
    
    /**
     * 搜索项目
     * @param keyword 关键词
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    PageResponseDTO<ProjectSummaryDTO> searchProjects(String keyword, int page, int size);
    
    /**
     * 获取当前用户的项目
     * @return 项目列表
     */
    List<ProjectSummaryDTO> getCurrentUserProjects();
    
    /**
     * 获取最近更新的项目
     * @param limit 限制数量
     * @return 项目列表
     */
    List<ProjectSummaryDTO> getRecentProjects(int limit);
    
    /**
     * 获取星级最高的项目
     * @param limit 限制数量
     * @return 项目列表
     */
    List<ProjectSummaryDTO> getTopRatedProjects(int limit);
    
    /**
     * 检查仓库URL是否已存在
     * @param url 仓库URL
     * @return 是否存在
     */
    boolean isRepositoryUrlExists(String url);
    
    /**
     * 将用户添加到项目
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean addUserToProject(Long projectId, Long userId);
    
    /**
     * 从项目移除用户
     * @param projectId 项目ID
     * @param userId 用户ID
     * @return 操作结果
     */
    boolean removeUserFromProject(Long projectId, Long userId);
}

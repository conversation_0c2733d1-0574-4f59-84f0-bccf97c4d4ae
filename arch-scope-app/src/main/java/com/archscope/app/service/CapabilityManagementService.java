package com.archscope.app.service;

import com.archscope.app.dto.CapabilityDTO;
import com.archscope.app.dto.ServiceDTO;
import com.archscope.app.command.CapabilityRegistrationCommand;

import java.util.List;

/**
 * 能力管理应用服务接口
 */
public interface CapabilityManagementService {

    /**
     * 注册服务能力
     *
     * @param serviceId 服务ID
     * @param command 能力注册命令
     * @return 注册成功的能力DTO
     */
    CapabilityDTO registerCapability(String serviceId, CapabilityRegistrationCommand command);

    /**
     * 获取服务的所有能力
     *
     * @param serviceId 服务ID
     * @return 服务能力列表
     */
    List<CapabilityDTO> getServiceCapabilities(String serviceId);

    /**
     * 根据能力需求查找提供相关能力的服务
     *
     * @param requiredCapabilities 需要的能力名称列表
     * @return 提供这些能力的服务列表
     */
    List<ServiceDTO> findServicesByCapabilityRequirements(List<String> requiredCapabilities);

    /**
     * 根据能力ID获取能力详情
     *
     * @param capabilityId 能力ID
     * @return 能力DTO
     */
    CapabilityDTO getCapabilityById(String capabilityId);

    /**
     * 更新能力信息
     *
     * @param capabilityId 能力ID
     * @param command 能力注册命令（用于更新）
     * @return 更新后的能力DTO
     */
    CapabilityDTO updateCapability(String capabilityId, CapabilityRegistrationCommand command);

    /**
     * 标记能力为废弃状态
     *
     * @param capabilityId 能力ID
     */
    void deprecateCapability(String capabilityId);

    /**
     * 删除能力
     *
     * @param capabilityId 能力ID
     */
    void deleteCapability(String capabilityId);
}
# 测试环境配置
spring:
  # 数据库配置
  datasource:
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password: ''
    type: com.alibaba.druid.pool.DruidDataSource

  # H2配置
  h2:
    console:
      enabled: true
      path: /h2-console

  # JPA配置
  jpa:
    database-platform: org.hibernate.dialect.H2Dialect
    hibernate:
      ddl-auto: none
    show-sql: true
    properties:
      hibernate:
        format_sql: true

  # SQL初始化配置
  sql:
    init:
      mode: always
      schema-locations: classpath:schema.sql
      continue-on-error: false

  # Redis配置（使用模拟测试）
  redis:
    host: localhost
    port: 6379
    database: 0
    password: 'test'

  # 允许Bean定义覆盖，解决配置冲突
  main:
    allow-bean-definition-overriding: true
    allow-circular-references: true

  # 禁用安全配置的自动配置
  autoconfigure:
    exclude:
      - org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration
      - org.redisson.spring.starter.RedissonAutoConfiguration

# RocketMQ配置
rocketmq:
  name-server: localhost:9876
  producer:
    group: archscope-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    compress-message-body-threshold: 4096
    max-message-size: 4194304
  # 测试环境下使用模拟监听器

# Redisson配置（测试环境会使用模拟配置覆盖这些值）
redisson:
  single-server-config:
    address: redis://localhost:6379
    database: 0

logging:
  level:
    root: INFO
    com.archscope: DEBUG

# 测试环境模拟配置
test:
  mock:
    redis:
      enabled: true
    redisson:
      enabled: true

# 添加安全配置以解决 LlmResponseParserTest 问题
security:
  enabled: false

# 添加LLM相关配置
llm:
  parser:
    enabled: true
    json-max-token-length: 10000
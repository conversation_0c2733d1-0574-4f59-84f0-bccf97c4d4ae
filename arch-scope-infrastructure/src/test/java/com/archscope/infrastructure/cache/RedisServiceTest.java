package com.archscope.infrastructure.cache;

import com.archscope.infrastructure.TestApplication;
import com.archscope.infrastructure.config.MockedRedisConfiguration;
import com.archscope.infrastructure.config.TestAutoConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest(classes = TestApplication.class)
@Import(TestAutoConfiguration.class)
@ActiveProfiles("test")
public class RedisServiceTest {

    @Autowired
    private RedisService redisService;
    
    /**
     * 每个测试方法执行前清除Redis数据
     */
    @BeforeEach
    public void clearRedisData() {
        MockedRedisConfiguration.clearAllData();
    }

    @Test
    public void testString() {
        // 设置字符串
        String key = "test:string";
        String value = "Hello Redis";
        redisService.set(key, value);
        
        // 获取字符串
        Object result = redisService.get(key);
        assertEquals(value, result);
        
        // 设置过期时间
        redisService.expire(key, 10, TimeUnit.SECONDS);
        assertTrue(redisService.getExpire(key, TimeUnit.SECONDS) <= 10);
        
        // 删除
        redisService.delete(key);
        assertNull(redisService.get(key));
    }

    @Test
    public void testHash() {
        // 准备测试数据
        String key = "test:hash";
        String hashKey = "name";
        String value = "Redis Hash";
        
        // 设置Hash
        redisService.hSet(key, hashKey, value);
        
        // 获取Hash
        Object result = redisService.hGet(key, hashKey);
        assertEquals(value, result);
        
        // 检查Hash是否包含Key
        assertTrue(redisService.hHasKey(key, hashKey));
        
        // 设置整个Hash
        Map<String, Object> map = new HashMap<>();
        map.put("field1", "value1");
        map.put("field2", "value2");
        redisService.hSetAll(key, map);
        
        // 删除Hash字段
        redisService.hDelete(key, hashKey);
        assertNull(redisService.hGet(key, hashKey));
        
        // 清理
        redisService.delete(key);
    }

    @Test
    public void testList() {
        // 准备测试数据
        String key = "test:list";
        
        // 确保测试前key不存在
        redisService.delete(key);
        assertEquals(0, redisService.lSize(key), "测试前列表应为空");
        
        String value1 = "item1";
        String value2 = "item2";
        
        // 添加列表项
        redisService.lPush(key, value1);
        redisService.lPush(key, value2);
        
        // 获取列表长度
        assertEquals(2, redisService.lSize(key), "列表应只包含两个元素");
        
        // 注意：在模拟实现中，lPush实际上是使用ArrayList.add()，
        // 所以是按FIFO顺序添加（与真实Redis的LIFO行为不同）
        assertEquals(value1, redisService.lIndex(key, 0), "模拟实现中列表第一项是最先添加的item1");
        assertEquals(value2, redisService.lIndex(key, 1), "模拟实现中列表第二项是后添加的item2");
        
        // 获取列表范围
        assertEquals(Arrays.asList(value1, value2), redisService.lRange(key, 0, -1), "模拟实现中列表顺序为添加顺序");
        
        // 清理
        redisService.delete(key);
        assertEquals(0, redisService.lSize(key), "测试后列表应为空");
    }

    @Test
    public void testSet() {
        // 准备测试数据
        String key = "test:set";
        String value1 = "member1";
        String value2 = "member2";
        
        // 添加集合成员
        redisService.sAdd(key, value1, value2);
        
        // 获取集合大小
        assertEquals(2, redisService.sSize(key));
        
        // 检查成员是否存在
        assertTrue(redisService.sIsMember(key, value1));
        
        // 获取所有成员
        Set<Object> members = redisService.sMembers(key);
        assertEquals(2, members.size());
        assertTrue(members.contains(value1));
        assertTrue(members.contains(value2));
        
        // 清理
        redisService.delete(key);
    }
    
    @Test
    public void testIncrementDecrement() {
        // 准备测试数据
        String key = "test:counter";
        
        // 递增
        redisService.set(key, 0);
        assertEquals(1, redisService.increment(key, 1));
        assertEquals(6, redisService.increment(key, 5));
        
        // 递减
        assertEquals(5, redisService.decrement(key, 1));
        assertEquals(0, redisService.decrement(key, 5));
        
        // 清理
        redisService.delete(key);
    }
} 
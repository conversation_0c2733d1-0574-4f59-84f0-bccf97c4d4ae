package com.archscope.infrastructure.cache;

import com.archscope.domain.entity.Project;
import com.archscope.domain.valueobject.ProjectType;
import com.archscope.infrastructure.TestApplication;
import com.archscope.infrastructure.config.MockedRedisConfiguration;
import com.archscope.infrastructure.config.TestAutoConfiguration;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

@SpringBootTest(classes = TestApplication.class)
@Import({MockedRedisConfiguration.class, TestAutoConfiguration.class})
@ActiveProfiles("test")
class ProjectCacheServiceTest {

    @Autowired
    private ProjectCacheService projectCacheService;

    @Autowired
    private RedisService redisService;

    private Project testProject;
    private List<Project> testProjects;

    @BeforeEach
    void setUp() {
        // 清除Redis数据，确保测试隔离性
        MockedRedisConfiguration.clearAllData();
        
        // 创建测试项目
        testProject = new Project();
        testProject.setId(1L);
        testProject.setName("Test Project");
        testProject.setDescription("Test Description");
        testProject.setRepositoryUrl("https://github.com/test/project");
        testProject.setType(ProjectType.OTHER);
        testProject.setActive(true);
        testProject.setCreatedAt(LocalDateTime.now());
        testProject.setUpdatedAt(LocalDateTime.now());

        // 创建测试项目列表
        testProjects = Arrays.asList(testProject);
    }

    @Test
    void shouldCacheAndRetrieveProjectById() {
        // 缓存项目
        projectCacheService.cacheProject(testProject);

        // 从缓存中获取项目
        Optional<Project> cachedProject = projectCacheService.getProjectById(1L);

        // 验证
        assertThat(cachedProject).isPresent();
        assertThat(cachedProject.get().getId()).isEqualTo(testProject.getId());
        assertThat(cachedProject.get().getName()).isEqualTo(testProject.getName());
    }

    @Test
    void shouldCacheAndRetrieveProjectByRepositoryUrl() {
        // 缓存项目
        projectCacheService.cacheProject(testProject);

        // 从缓存中获取项目
        Optional<Project> cachedProject = projectCacheService.getProjectByRepositoryUrl(testProject.getRepositoryUrl());

        // 验证
        assertThat(cachedProject).isPresent();
        assertThat(cachedProject.get().getId()).isEqualTo(testProject.getId());
        assertThat(cachedProject.get().getRepositoryUrl()).isEqualTo(testProject.getRepositoryUrl());
    }

    @Test
    void shouldCacheAndRetrieveProjectList() {
        // 缓存项目列表
        projectCacheService.cacheProjectList(testProjects);

        // 从缓存中获取项目列表
        Optional<List<Project>> cachedProjects = projectCacheService.getAllProjects();

        // 验证
        assertThat(cachedProjects).isPresent();
        assertThat(cachedProjects.get()).hasSize(1);
        assertThat(cachedProjects.get().get(0).getId()).isEqualTo(testProject.getId());
    }

    @Test
    void shouldRemoveProjectFromCache() {
        // 先缓存项目
        projectCacheService.cacheProject(testProject);

        // 从缓存中移除项目
        projectCacheService.removeProjectCache(testProject.getId());

        // 验证项目已被移除
        Optional<Project> cachedProject = projectCacheService.getProjectById(testProject.getId());
        assertThat(cachedProject).isEmpty();
    }

    @Test
    void shouldClearAllProjectCache() {
        // 先缓存项目
        projectCacheService.cacheProject(testProject);

        // 清除所有项目缓存
        projectCacheService.clearAllProjectCache();

        // 验证缓存已被清除
        Optional<Project> cachedProject = projectCacheService.getProjectById(testProject.getId());
        assertThat(cachedProject).isEmpty();
    }
} 
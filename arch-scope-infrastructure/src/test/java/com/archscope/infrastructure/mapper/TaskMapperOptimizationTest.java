package com.archscope.infrastructure.mapper;

import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.infrastructure.entity.TaskEntity;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TaskMapper优化功能测试
 * 测试新增的数据库操作优化功能
 */
@SpringBootTest
@ActiveProfiles("test")
@Transactional
class TaskMapperOptimizationTest {

    @Autowired
    private TaskMapper taskMapper;

    private TaskEntity testTask1;
    private TaskEntity testTask2;
    private TaskEntity testTask3;

    @BeforeEach
    void setUp() {
        // 创建测试任务
        testTask1 = createTestTask("TEST_TASK_1", TaskStatus.PENDING, 10);
        testTask2 = createTestTask("TEST_TASK_2", TaskStatus.PROCESSING, 5);
        testTask3 = createTestTask("TEST_TASK_3", TaskStatus.COMPLETED, 1);

        taskMapper.insert(testTask1);
        taskMapper.insert(testTask2);
        taskMapper.insert(testTask3);
    }

    @Test
    void testFindNextPendingTask_WithOptimization() {
        // Given - 已在setUp中创建测试数据

        // When
        TaskEntity result = taskMapper.findNextPendingTask();

        // Then
        assertNotNull(result);
        assertEquals(TaskStatus.PENDING, result.getStatus());
        assertEquals(10, result.getPriority()); // 应该返回高优先级任务
        assertEquals("TEST_TASK_1", result.getTaskType());
    }

    @Test
    void testFindTasksWithPagination() {
        // Given
        TaskStatus status = TaskStatus.PENDING;
        String taskType = "TEST_TASK_1";
        int offset = 0;
        int limit = 10;

        // When
        List<TaskEntity> results = taskMapper.findTasksWithPagination(
                status, taskType, null, null, null, null, "priority", offset, limit);

        // Then
        assertNotNull(results);
        assertFalse(results.isEmpty());
        assertEquals(1, results.size());
        assertEquals("TEST_TASK_1", results.get(0).getTaskType());
    }

    @Test
    void testCountTasksWithCondition() {
        // Given
        TaskStatus status = TaskStatus.PENDING;

        // When
        long count = taskMapper.countTasksWithCondition(status, null, null, null, null, null);

        // Then
        assertEquals(1, count);
    }

    @Test
    void testBatchUpdateTaskStatus() {
        // Given
        List<Long> taskIds = Arrays.asList(testTask1.getId(), testTask2.getId());
        TaskStatus currentStatus = TaskStatus.PENDING;
        TaskStatus newStatus = TaskStatus.PROCESSING;

        // When
        int updatedRows = taskMapper.batchUpdateTaskStatus(taskIds, currentStatus, newStatus);

        // Then
        assertEquals(1, updatedRows); // 只有testTask1是PENDING状态
        
        // 验证状态已更新
        TaskEntity updatedTask = taskMapper.selectById(testTask1.getId());
        assertEquals(TaskStatus.PROCESSING, updatedTask.getStatus());
    }

    @Test
    void testBatchResetTimeoutTasks() {
        // Given
        // 先将testTask2设置为超时状态
        testTask2.setStatus(TaskStatus.PROCESSING);
        testTask2.setTimeoutAt(LocalDateTime.now().minusMinutes(5));
        taskMapper.updateById(testTask2);

        List<Long> taskIds = Arrays.asList(testTask2.getId());

        // When
        int updatedRows = taskMapper.batchResetTimeoutTasks(taskIds);

        // Then
        assertEquals(1, updatedRows);
        
        // 验证任务已重置
        TaskEntity resetTask = taskMapper.selectById(testTask2.getId());
        assertEquals(TaskStatus.PENDING, resetTask.getStatus());
        assertNull(resetTask.getWorkerId());
    }

    @Test
    void testGetTaskStatistics() {
        // Given - 已在setUp中创建不同状态的任务

        // When
        List<Map<String, Object>> statistics = taskMapper.getTaskStatistics(null, null, null, null);

        // Then
        assertNotNull(statistics);
        assertFalse(statistics.isEmpty());
        
        // 验证统计数据包含不同状态
        boolean hasPending = statistics.stream()
                .anyMatch(stat -> "PENDING".equals(stat.get("status")));
        boolean hasProcessing = statistics.stream()
                .anyMatch(stat -> "PROCESSING".equals(stat.get("status")));
        boolean hasCompleted = statistics.stream()
                .anyMatch(stat -> "COMPLETED".equals(stat.get("status")));
        
        assertTrue(hasPending);
        assertTrue(hasProcessing);
        assertTrue(hasCompleted);
    }

    @Test
    void testFindLongRunningTasks() {
        // Given
        // 设置testTask2为长时间运行任务
        testTask2.setStatus(TaskStatus.PROCESSING);
        testTask2.setProcessingStartedAt(LocalDateTime.now().minusHours(2));
        taskMapper.updateById(testTask2);

        // When
        List<TaskEntity> longRunningTasks = taskMapper.findLongRunningTasks(60, 10);

        // Then
        assertNotNull(longRunningTasks);
        assertEquals(1, longRunningTasks.size());
        assertEquals(testTask2.getId(), longRunningTasks.get(0).getId());
    }

    @Test
    void testLockTaskWithCAS() {
        // Given
        Long taskId = testTask1.getId();
        String workerId = "test-worker-001";
        int timeoutMinutes = 30;
        Integer expectedVersion = 0; // 初始版本

        // When
        int updatedRows = taskMapper.lockTaskWithCAS(taskId, workerId, timeoutMinutes, expectedVersion);

        // Then
        assertEquals(1, updatedRows);
        
        // 验证任务已锁定
        TaskEntity lockedTask = taskMapper.selectById(taskId);
        assertEquals(TaskStatus.PROCESSING, lockedTask.getStatus());
        assertEquals(workerId, lockedTask.getWorkerId());
        assertNotNull(lockedTask.getProcessingStartedAt());
        assertNotNull(lockedTask.getTimeoutAt());
    }

    @Test
    void testLockTaskWithCAS_VersionConflict() {
        // Given
        Long taskId = testTask1.getId();
        String workerId = "test-worker-001";
        int timeoutMinutes = 30;
        Integer wrongVersion = 999; // 错误的版本号

        // When
        int updatedRows = taskMapper.lockTaskWithCAS(taskId, workerId, timeoutMinutes, wrongVersion);

        // Then
        assertEquals(0, updatedRows); // 版本冲突，应该更新失败
        
        // 验证任务状态未改变
        TaskEntity unchangedTask = taskMapper.selectById(taskId);
        assertEquals(TaskStatus.PENDING, unchangedTask.getStatus());
        assertNull(unchangedTask.getWorkerId());
    }

    @Test
    void testFindTimeoutProcessingTasks_WithLimit() {
        // Given
        // 创建多个超时任务
        for (int i = 0; i < 5; i++) {
            TaskEntity timeoutTask = createTestTask("TIMEOUT_TASK_" + i, TaskStatus.PROCESSING, 5);
            timeoutTask.setTimeoutAt(LocalDateTime.now().minusMinutes(5));
            taskMapper.insert(timeoutTask);
        }

        // When
        List<TaskEntity> timeoutTasks = taskMapper.findTimeoutProcessingTasks();

        // Then
        assertNotNull(timeoutTasks);
        assertTrue(timeoutTasks.size() <= 100); // 验证LIMIT生效
        assertEquals(5, timeoutTasks.size());
    }

    /**
     * 创建测试任务
     */
    private TaskEntity createTestTask(String taskType, TaskStatus status, Integer priority) {
        TaskEntity task = new TaskEntity();
        task.setName("Test Task");
        task.setDescription("Test Description");
        task.setTaskType(taskType);
        task.setStatus(status);
        task.setPriority(priority);
        task.setProjectId(1L);
        task.setCreatedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        task.setRetryCount(0);
        task.setMaxRetries(3);
        task.setTaskVersion(0);
        return task;
    }
}

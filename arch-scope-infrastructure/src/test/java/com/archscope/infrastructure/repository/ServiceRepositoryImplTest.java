package com.archscope.infrastructure.repository;

import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.infrastructure.persistence.entity.ServiceDO;
import com.archscope.infrastructure.persistence.mapper.ServiceMapper;
import com.archscope.infrastructure.repository.converter.ServiceConverter;
import com.archscope.infrastructure.cache.RedisService;
import com.archscope.domain.repository.CapabilityRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * ServiceRepositoryImpl单元测试
 */
@ExtendWith(MockitoExtension.class)
class ServiceRepositoryImplTest {

    @Mock
    private ServiceMapper serviceMapper;

    @Mock
    private ServiceConverter serviceConverter;

    @Mock
    private RedisService redisService;

    @Mock
    private CapabilityRepository capabilityRepository;

    @InjectMocks
    private ServiceRepositoryImpl serviceRepository;

    private Service testService;
    private ServiceDO testServiceDO;
    private ServiceId testServiceId;

    @BeforeEach
    void setUp() {
        testServiceId = ServiceId.of("test-service-id");
        testService = mock(Service.class);
        testServiceDO = new ServiceDO();
        testServiceDO.setId(1L);
        testServiceDO.setName("Test Service");
        testServiceDO.setStatus("ACTIVE");
        testServiceDO.setType("REST_API");

        when(testService.getId()).thenReturn(testServiceId);
        when(testService.getName()).thenReturn("Test Service");
    }

    @Test
    void save_ShouldInsertNewService_WhenServiceDoesNotExist() {
        // Arrange
        when(serviceConverter.toDataObject(testService)).thenReturn(testServiceDO);
        when(serviceMapper.selectById("test-service-id")).thenReturn(null);
        when(serviceConverter.toDomainObject(testServiceDO)).thenReturn(testService);

        // Act
        Service result = serviceRepository.save(testService);

        // Assert
        assertNotNull(result);
        verify(serviceMapper).insert(testServiceDO);
        verify(serviceMapper, never()).updateById(any());
        verify(redisService).delete(contains("test-service-id"));
    }

    @Test
    void save_ShouldUpdateExistingService_WhenServiceExists() {
        // Arrange
        when(serviceConverter.toDataObject(testService)).thenReturn(testServiceDO);
        when(serviceMapper.selectById("test-service-id")).thenReturn(testServiceDO);
        when(serviceConverter.toDomainObject(testServiceDO)).thenReturn(testService);

        // Act
        Service result = serviceRepository.save(testService);

        // Assert
        assertNotNull(result);
        verify(serviceMapper).updateById(testServiceDO);
        verify(serviceMapper, never()).insert(any());
        verify(redisService).delete(contains("test-service-id"));
    }

    @Test
    void findById_ShouldReturnCachedService_WhenCacheHit() {
        // Arrange
        when(redisService.get(anyString())).thenReturn(testService);

        // Act
        Service result = serviceRepository.findById(testServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(testService, result);
        verify(serviceMapper, never()).selectById(any());
        verify(redisService, never()).set(anyString(), any(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void findById_ShouldReturnServiceFromDatabase_WhenCacheMiss() {
        // Arrange
        when(redisService.get(anyString())).thenReturn(null);
        when(serviceMapper.selectById("test-service-id")).thenReturn(testServiceDO);
        when(serviceConverter.toDomainObject(testServiceDO)).thenReturn(testService);

        // Act
        Service result = serviceRepository.findById(testServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(testService, result);
        verify(serviceMapper).selectById("test-service-id");
        verify(redisService).set(anyString(), eq(testService), anyLong(), any(TimeUnit.class));
    }

    @Test
    void findById_ShouldReturnNull_WhenServiceNotFound() {
        // Arrange
        when(redisService.get(anyString())).thenReturn(null);
        when(serviceMapper.selectById("test-service-id")).thenReturn(null);

        // Act
        Service result = serviceRepository.findById(testServiceId);

        // Assert
        assertNull(result);
        verify(serviceMapper).selectById("test-service-id");
        verify(redisService, never()).set(anyString(), any(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void findById_ShouldHandleRedisException_AndFallbackToDatabase() {
        // Arrange
        when(redisService.get(anyString())).thenThrow(new RuntimeException("Redis connection failed"));
        when(serviceMapper.selectById("test-service-id")).thenReturn(testServiceDO);
        when(serviceConverter.toDomainObject(testServiceDO)).thenReturn(testService);

        // Act
        Service result = serviceRepository.findById(testServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(testService, result);
        verify(serviceMapper).selectById("test-service-id");
    }

    @Test
    void findByName_ShouldReturnService_WhenServiceExists() {
        // Arrange
        when(serviceMapper.findByName("Test Service")).thenReturn(testServiceDO);
        when(serviceConverter.toDomainObject(testServiceDO)).thenReturn(testService);

        // Act
        Service result = serviceRepository.findByName("Test Service");

        // Assert
        assertNotNull(result);
        assertEquals(testService, result);
        verify(serviceMapper).findByName("Test Service");
    }

    @Test
    void findByName_ShouldReturnNull_WhenServiceNotFound() {
        // Arrange
        when(serviceMapper.findByName("Nonexistent Service")).thenReturn(null);

        // Act
        Service result = serviceRepository.findByName("Nonexistent Service");

        // Assert
        assertNull(result);
        verify(serviceMapper).findByName("Nonexistent Service");
    }

    @Test
    void findByMavenCoordinates_ShouldReturnCachedService_WhenCacheHit() {
        // Arrange
        when(redisService.get(anyString())).thenReturn(testService);

        // Act
        Service result = serviceRepository.findByMavenCoordinates("com.example", "test-service", "1.0.0");

        // Assert
        assertNotNull(result);
        assertEquals(testService, result);
        verify(serviceMapper, never()).findByMavenCoordinates(any(), any(), any());
    }

    @Test
    void findByMavenCoordinates_ShouldReturnServiceFromDatabase_WhenCacheMiss() {
        // Arrange
        when(redisService.get(anyString())).thenReturn(null);
        when(serviceMapper.findByMavenCoordinates("com.example", "test-service", "1.0.0")).thenReturn(testServiceDO);
        when(serviceConverter.toDomainObject(testServiceDO)).thenReturn(testService);

        // Act
        Service result = serviceRepository.findByMavenCoordinates("com.example", "test-service", "1.0.0");

        // Assert
        assertNotNull(result);
        assertEquals(testService, result);
        verify(serviceMapper).findByMavenCoordinates("com.example", "test-service", "1.0.0");
        verify(redisService).set(anyString(), eq(testService), anyLong(), any(TimeUnit.class));
    }

    @Test
    void findByStatus_ShouldReturnCachedServices_WhenCacheHit() {
        // Arrange
        List<Service> cachedServices = Arrays.asList(testService);
        when(redisService.getList(anyString(), eq(Service.class))).thenReturn(cachedServices);

        // Act
        List<Service> result = serviceRepository.findByStatus(ServiceStatus.ACTIVE);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testService, result.get(0));
        verify(serviceMapper, never()).findByStatus(any());
    }

    @Test
    void findByStatus_ShouldReturnServicesFromDatabase_WhenCacheMiss() {
        // Arrange
        List<ServiceDO> serviceDOs = Arrays.asList(testServiceDO);
        List<Service> services = Arrays.asList(testService);
        when(redisService.getList(anyString(), eq(Service.class))).thenReturn(null);
        when(serviceMapper.findByStatus("ACTIVE")).thenReturn(serviceDOs);
        when(serviceConverter.toDomainObject(testServiceDO)).thenReturn(testService);

        // Act
        List<Service> result = serviceRepository.findByStatus(ServiceStatus.ACTIVE);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testService, result.get(0));
        verify(serviceMapper).findByStatus("ACTIVE");
        verify(redisService).setList(anyString(), eq(services), anyLong(), any(TimeUnit.class));
    }

    @Test
    void findByType_ShouldReturnServicesFromDatabase_WhenCacheMiss() {
        // Arrange
        List<ServiceDO> serviceDOs = Arrays.asList(testServiceDO);
        List<Service> services = Arrays.asList(testService);
        when(redisService.getList(anyString(), eq(Service.class))).thenReturn(null);
        when(serviceMapper.findByType("WEB_SERVICE")).thenReturn(serviceDOs);
        when(serviceConverter.toDomainObject(testServiceDO)).thenReturn(testService);

        // Act
        List<Service> result = serviceRepository.findByType(ServiceType.REST_API);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testService, result.get(0));
        verify(serviceMapper).findByType("WEB_SERVICE");
        verify(redisService).setList(anyString(), eq(services), anyLong(), any(TimeUnit.class));
    }

    @Test
    void findByMavenCoordinatesWithoutVersion_ShouldHandleRedisException() {
        // Arrange
        List<ServiceDO> serviceDOs = Arrays.asList(testServiceDO);
        when(redisService.getList(anyString(), eq(Service.class))).thenThrow(new RuntimeException("Redis error"));
        when(serviceMapper.findByMavenCoordinatesWithoutVersion("com.example", "test-service")).thenReturn(serviceDOs);
        when(serviceConverter.toDomainObject(testServiceDO)).thenReturn(testService);

        // Act
        List<Service> result = serviceRepository.findByMavenCoordinatesWithoutVersion("com.example", "test-service");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testService, result.get(0));
        verify(serviceMapper).findByMavenCoordinatesWithoutVersion("com.example", "test-service");
    }
}

package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.valueobject.ProjectType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.jdbc.AutoConfigureTestDatabase;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 项目仓库标准化URL集成测试
 */
@SpringBootTest(classes = com.archscope.infrastructure.TestApplication.class)
@ActiveProfiles("test")
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
@Transactional
@DisplayName("项目仓库标准化URL集成测试")
class ProjectRepositoryNormalizedUrlTest {

    @Autowired
    private ProjectRepository projectRepository;

    private Project testProject;

    @BeforeEach
    void setUp() {
        testProject = Project.builder()
                .name("测试项目")
                .description("测试项目描述")
                .repositoryUrl("https://github.com/user/repo.git")
                .normalizedRepositoryUrl("https://github.com/user/repo")
                .branch("main")
                .creatorId(1L)
                .status("PENDING_ANALYSIS")
                .active(true)
                .type(ProjectType.OTHER)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();
    }

    @Test
    @DisplayName("保存项目时应该正确保存标准化URL")
    void save_ShouldPersistNormalizedUrl() {
        // When
        Project savedProject = projectRepository.save(testProject);

        // Then
        assertNotNull(savedProject.getId(), "项目ID应该被生成");
        assertEquals(testProject.getNormalizedRepositoryUrl(), savedProject.getNormalizedRepositoryUrl(), 
                "标准化URL应该被正确保存");

        // 验证从数据库查询的结果
        Optional<Project> foundProject = projectRepository.findById(savedProject.getId());
        assertTrue(foundProject.isPresent(), "应该能够找到保存的项目");
        assertEquals(testProject.getNormalizedRepositoryUrl(), foundProject.get().getNormalizedRepositoryUrl(), 
                "从数据库查询的标准化URL应该正确");
    }

    @Test
    @DisplayName("通过标准化URL查找项目应该成功")
    void findByNormalizedRepositoryUrl_ShouldReturnProject() {
        // Given
        Project savedProject = projectRepository.save(testProject);

        // When
        Optional<Project> foundProject = projectRepository.findByNormalizedRepositoryUrl(
                testProject.getNormalizedRepositoryUrl());

        // Then
        assertTrue(foundProject.isPresent(), "应该能够通过标准化URL找到项目");
        assertEquals(savedProject.getId(), foundProject.get().getId(), "找到的项目ID应该匹配");
        assertEquals(testProject.getNormalizedRepositoryUrl(), foundProject.get().getNormalizedRepositoryUrl(), 
                "标准化URL应该匹配");
    }

    @Test
    @DisplayName("标准化URL唯一性约束应该生效")
    void save_DuplicateNormalizedUrl_ShouldThrowException() {
        // Given
        projectRepository.save(testProject);

        // 创建另一个项目，使用相同的标准化URL但不同的原始URL
        Project duplicateProject = Project.builder()
                .name("重复项目")
                .description("重复项目描述")
                .repositoryUrl("**************:user/repo.git") // 不同的原始URL
                .normalizedRepositoryUrl("https://github.com/user/repo") // 相同的标准化URL
                .branch("main")
                .creatorId(1L)
                .status("PENDING_ANALYSIS")
                .active(true)
                .type(ProjectType.OTHER)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // When & Then
        assertThrows(Exception.class, () -> {
            projectRepository.save(duplicateProject);
        }, "保存重复的标准化URL应该抛出异常");
    }

    @Test
    @DisplayName("检查标准化URL是否存在应该正确工作")
    void existsByNormalizedRepositoryUrl_ShouldWork() {
        // Given
        String normalizedUrl = testProject.getNormalizedRepositoryUrl();

        // When - 项目不存在时
        boolean existsBeforeSave = projectRepository.existsByNormalizedRepositoryUrl(normalizedUrl);

        // Then
        assertFalse(existsBeforeSave, "项目保存前应该不存在");

        // When - 保存项目后
        projectRepository.save(testProject);
        boolean existsAfterSave = projectRepository.existsByNormalizedRepositoryUrl(normalizedUrl);

        // Then
        assertTrue(existsAfterSave, "项目保存后应该存在");
    }

    @Test
    @DisplayName("更新项目时应该正确更新标准化URL")
    void update_ShouldUpdateNormalizedUrl() {
        // Given
        Project savedProject = projectRepository.save(testProject);
        String newNormalizedUrl = "https://github.com/user/new-repo";

        // When
        savedProject.setNormalizedRepositoryUrl(newNormalizedUrl);
        savedProject.setRepositoryUrl("https://github.com/user/new-repo.git");
        Project updatedProject = projectRepository.update(savedProject);

        // Then
        assertEquals(newNormalizedUrl, updatedProject.getNormalizedRepositoryUrl(), 
                "标准化URL应该被正确更新");

        // 验证从数据库查询的结果
        Optional<Project> foundProject = projectRepository.findById(savedProject.getId());
        assertTrue(foundProject.isPresent(), "应该能够找到更新的项目");
        assertEquals(newNormalizedUrl, foundProject.get().getNormalizedRepositoryUrl(), 
                "从数据库查询的标准化URL应该是更新后的值");
    }

    @Test
    @DisplayName("查询不存在的标准化URL应该返回空")
    void findByNormalizedRepositoryUrl_NotExists_ShouldReturnEmpty() {
        // When
        Optional<Project> foundProject = projectRepository.findByNormalizedRepositoryUrl(
                "https://github.com/nonexistent/repo");

        // Then
        assertFalse(foundProject.isPresent(), "查询不存在的标准化URL应该返回空");
    }

    @Test
    @DisplayName("标准化URL字段不应该为空")
    void save_NullNormalizedUrl_ShouldHandleGracefully() {
        // Given
        testProject.setNormalizedRepositoryUrl(null);

        // When & Then
        // 根据数据库约束，这应该失败或者被处理
        assertThrows(Exception.class, () -> {
            projectRepository.save(testProject);
        }, "标准化URL为空时应该抛出异常或被处理");
    }
}

package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.ParseResultCache;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.model.parser.LanguageType;
import com.archscope.infrastructure.mapper.ParseResultCacheMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MyBatisParseResultCacheRepositoryTest {

    @Mock
    private ParseResultCacheMapper parseResultCacheMapper;

    @InjectMocks
    private MyBatisParseResultCacheRepository parseResultCacheRepository;

    private ParseResultCache testCache;

    @BeforeEach
    void setUp() {
        List<FileParseResult> parseResults = new ArrayList<>();
        // 添加一些测试数据
        parseResults.add(FileParseResult.builder()
                .filename("src/main/java/com/example/Test.java")
                .filePath("src/main/java/com/example/Test.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .imports(new ArrayList<>())
                .classDefinitions(new ArrayList<>())
                .dependencies(new ArrayList<>())
                .successful(true)
                .build());

        testCache = ParseResultCache.builder()
                .id(1L)
                .repositoryId(101L)
                .commitId("abcdef1234567890")
                .parseResults(parseResults)
                .createdAt(LocalDateTime.now())
                .valid(true)
                .cacheSize(1024L)
                .build();
    }

    @Test
    void save_insert() {
        ParseResultCache newCache = ParseResultCache.builder()
                .repositoryId(102L)
                .commitId("0987654321fedcba")
                .parseResults(new ArrayList<>())
                .createdAt(LocalDateTime.now())
                .valid(true)
                .build();

        when(parseResultCacheMapper.insert(any(ParseResultCache.class))).thenReturn(1);

        ParseResultCache result = parseResultCacheRepository.save(newCache);

        assertEquals("0987654321fedcba", result.getCommitId());
        verify(parseResultCacheMapper, times(1)).insert(any(ParseResultCache.class));
    }

    @Test
    void save_update() {
        when(parseResultCacheMapper.updateById(any(ParseResultCache.class))).thenReturn(1);

        ParseResultCache result = parseResultCacheRepository.save(testCache);

        assertEquals("abcdef1234567890", result.getCommitId());
        verify(parseResultCacheMapper, times(1)).updateById(any(ParseResultCache.class));
    }

    @Test
    void findById() {
        when(parseResultCacheMapper.selectById(1L)).thenReturn(testCache);

        Optional<ParseResultCache> result = parseResultCacheRepository.findById(1L);

        assertTrue(result.isPresent());
        assertEquals("abcdef1234567890", result.get().getCommitId());
        verify(parseResultCacheMapper, times(1)).selectById(1L);
    }

    @Test
    void findByRepositoryIdAndCommitId() {
        when(parseResultCacheMapper.selectByRepositoryIdAndCommitId(101L, "abcdef1234567890")).thenReturn(testCache);

        Optional<ParseResultCache> result = parseResultCacheRepository.findByRepositoryIdAndCommitId(101L, "abcdef1234567890");

        assertTrue(result.isPresent());
        assertEquals(101L, result.get().getRepositoryId());
        assertEquals("abcdef1234567890", result.get().getCommitId());
        verify(parseResultCacheMapper, times(1)).selectByRepositoryIdAndCommitId(101L, "abcdef1234567890");
    }

    @Test
    void findAllByRepositoryId() {
        List<ParseResultCache> caches = Arrays.asList(
                testCache,
                ParseResultCache.builder().id(2L).repositoryId(101L).commitId("0987654321").build()
        );
        when(parseResultCacheMapper.selectAllByRepositoryId(101L)).thenReturn(caches);

        List<ParseResultCache> result = parseResultCacheRepository.findAllByRepositoryId(101L);

        assertEquals(2, result.size());
        verify(parseResultCacheMapper, times(1)).selectAllByRepositoryId(101L);
    }

    @Test
    void deleteById() {
        // 不需要使用 doNothing()，因为 void 方法默认就是不做任何事
        // 直接验证方法被调用即可
        parseResultCacheRepository.deleteById(1L);

        verify(parseResultCacheMapper, times(1)).deleteById(1L);
    }

    @Test
    void deleteAllByRepositoryId() {
        when(parseResultCacheMapper.deleteAllByRepositoryId(101L)).thenReturn(2);

        parseResultCacheRepository.deleteAllByRepositoryId(101L);

        verify(parseResultCacheMapper, times(1)).deleteAllByRepositoryId(101L);
    }

    @Test
    void existsByRepositoryIdAndCommitId() {
        when(parseResultCacheMapper.existsByRepositoryIdAndCommitId(101L, "abcdef1234567890")).thenReturn(true);

        boolean result = parseResultCacheRepository.existsByRepositoryIdAndCommitId(101L, "abcdef1234567890");

        assertTrue(result);
        verify(parseResultCacheMapper, times(1)).existsByRepositoryIdAndCommitId(101L, "abcdef1234567890");
    }
}

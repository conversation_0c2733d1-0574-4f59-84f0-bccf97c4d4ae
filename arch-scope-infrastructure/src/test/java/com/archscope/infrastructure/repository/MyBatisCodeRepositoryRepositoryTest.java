package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.CodeRepository;
import com.archscope.domain.valueobject.RepositoryStatus;
import com.archscope.domain.valueobject.RepositoryType;
import com.archscope.infrastructure.mapper.CodeRepositoryMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class MyBatisCodeRepositoryRepositoryTest {

    @Mock
    private CodeRepositoryMapper codeRepositoryMapper;

    @InjectMocks
    private MyBatisCodeRepositoryRepository codeRepositoryRepository;

    private CodeRepository testRepository;

    @BeforeEach
    void setUp() {
        testRepository = CodeRepository.builder()
                .id(1L)
                .name("Test Repository")
                .url("https://github.com/test/repo.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .status(RepositoryStatus.ACTIVE)
                .projectId(101L)
                .createdAt(LocalDateTime.now())
                .build();
    }

    @Test
    void save_insert() {
        CodeRepository newRepo = CodeRepository.builder()
                .name("New Repository")
                .url("https://github.com/test/new-repo.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .projectId(102L)
                .build();

        when(codeRepositoryMapper.insert(any(CodeRepository.class))).thenReturn(1);

        CodeRepository result = codeRepositoryRepository.save(newRepo);

        assertEquals("New Repository", result.getName());
        verify(codeRepositoryMapper, times(1)).insert(any(CodeRepository.class));
    }

    @Test
    void save_update() {
        when(codeRepositoryMapper.updateById(any(CodeRepository.class))).thenReturn(1);

        CodeRepository result = codeRepositoryRepository.save(testRepository);

        assertEquals("Test Repository", result.getName());
        verify(codeRepositoryMapper, times(1)).updateById(any(CodeRepository.class));
    }

    @Test
    void findById() {
        when(codeRepositoryMapper.selectById(1L)).thenReturn(testRepository);

        Optional<CodeRepository> result = codeRepositoryRepository.findById(1L);

        assertTrue(result.isPresent());
        assertEquals("Test Repository", result.get().getName());
        verify(codeRepositoryMapper, times(1)).selectById(1L);
    }

    @Test
    void findByProjectId() {
        List<CodeRepository> repositories = Arrays.asList(
                testRepository,
                CodeRepository.builder().id(2L).name("Repo 2").projectId(101L).build()
        );
        when(codeRepositoryMapper.selectByProjectId(101L)).thenReturn(repositories);

        List<CodeRepository> result = codeRepositoryRepository.findByProjectId(101L);

        assertEquals(2, result.size());
        verify(codeRepositoryMapper, times(1)).selectByProjectId(101L);
    }

    @Test
    void findByUrl() {
        when(codeRepositoryMapper.selectByUrl("https://github.com/test/repo.git")).thenReturn(testRepository);

        Optional<CodeRepository> result = codeRepositoryRepository.findByUrl("https://github.com/test/repo.git");

        assertTrue(result.isPresent());
        assertEquals("Test Repository", result.get().getName());
        verify(codeRepositoryMapper, times(1)).selectByUrl("https://github.com/test/repo.git");
    }

    @Test
    void update() {
        when(codeRepositoryMapper.updateById(any(CodeRepository.class))).thenReturn(1);

        CodeRepository result = codeRepositoryRepository.update(testRepository);

        assertEquals("Test Repository", result.getName());
        verify(codeRepositoryMapper, times(1)).updateById(any(CodeRepository.class));
    }

    @Test
    void delete() {
        when(codeRepositoryMapper.deleteById(1L)).thenReturn(1);

        codeRepositoryRepository.delete(1L);

        verify(codeRepositoryMapper, times(1)).deleteById(1L);
    }

    @Test
    void findAll() {
        List<CodeRepository> repositories = Arrays.asList(
                testRepository,
                CodeRepository.builder().id(2L).name("Repo 2").build()
        );
        when(codeRepositoryMapper.selectList(null)).thenReturn(repositories);

        List<CodeRepository> result = codeRepositoryRepository.findAll();

        assertEquals(2, result.size());
        verify(codeRepositoryMapper, times(1)).selectList(null);
    }
}

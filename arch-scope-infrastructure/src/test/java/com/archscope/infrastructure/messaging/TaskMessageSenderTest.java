package com.archscope.infrastructure.messaging;

import com.archscope.domain.constants.MessageConstants;
import com.archscope.domain.entity.Task;
import com.archscope.domain.message.TaskMessage;
import com.archscope.domain.service.MessageService;
import com.archscope.domain.task.CodeParseTask;
import com.archscope.domain.task.DocGenerateTask;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class TaskMessageSenderTest {

    @Mock
    private MessageService messageService;

    @InjectMocks
    private TaskMessageSender taskMessageSender;

    private Task buildCodeParseTask() {
        Task task = new Task();
        task.setId(1L);
        task.setProjectId(100L);
        task.setTaskType(CodeParseTask.TASK_TYPE);
        task.setPriority(1);

        HashMap<String, Object> params = new HashMap<>();
        params.put("repositoryId", 123);
        params.put("toCommit", "abc123");
        params.put("parseType", "FULL");
        task.setParameters(params);

        return task;
    }

    private Task buildDocGenerateTask() {
        Task task = new Task();
        task.setId(2L);
        task.setProjectId(100L);
        task.setTaskType(DocGenerateTask.TASK_TYPE);
        task.setPriority(2);

        HashMap<String, Object> params = new HashMap<>();
        params.put("repositoryId", 123);
        params.put("commitId", "abc123");
        task.setParameters(params);

        return task;
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testSendCodeParseTaskMessage_success() {
        // 准备测试数据
        Task task = buildCodeParseTask();
        when(messageService.sendTaskMessage(anyString(), any(TaskMessage.class))).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendCodeParseTaskMessage(task);

        // 验证结果
        assertTrue(result);

        ArgumentCaptor<TaskMessage> messageCaptor = ArgumentCaptor.forClass(TaskMessage.class);
        verify(messageService, times(1)).sendTaskMessage(eq(MessageConstants.TOPIC_CODE_PARSE), messageCaptor.capture());

        TaskMessage capturedMessage = messageCaptor.getValue();
        assertEquals(1L, capturedMessage.getTaskId());
        assertEquals(100L, capturedMessage.getProjectId());
        assertEquals(task.getParameters(), capturedMessage.getParameters());
    }

    @Test
    void testSendDocGenerateTaskMessage_success() {
        // 准备测试数据
        Task task = buildDocGenerateTask();
        when(messageService.sendTaskMessage(anyString(), any(TaskMessage.class))).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendDocGenerateTaskMessage(task);

        // 验证结果
        assertTrue(result);

        ArgumentCaptor<TaskMessage> messageCaptor = ArgumentCaptor.forClass(TaskMessage.class);
        verify(messageService, times(1)).sendTaskMessage(eq(MessageConstants.TOPIC_DOC_GENERATE), messageCaptor.capture());

        TaskMessage capturedMessage = messageCaptor.getValue();
        assertEquals(2L, capturedMessage.getTaskId());
        assertEquals(100L, capturedMessage.getProjectId());
        assertEquals(task.getParameters(), capturedMessage.getParameters());
    }

    @Test
    void testSendCodeParseTaskMessageAsync_success() {
        // 准备测试数据
        Task task = buildCodeParseTask();
        when(messageService.sendTaskMessageAsync(anyString(), any(TaskMessage.class))).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendCodeParseTaskMessageAsync(task);

        // 验证结果
        assertTrue(result);
        verify(messageService, times(1)).sendTaskMessageAsync(eq(MessageConstants.TOPIC_CODE_PARSE), any(TaskMessage.class));
    }

    @Test
    void testSendDocGenerateTaskMessageAsync_success() {
        // 准备测试数据
        Task task = buildDocGenerateTask();
        when(messageService.sendTaskMessageAsync(anyString(), any(TaskMessage.class))).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendDocGenerateTaskMessageAsync(task);

        // 验证结果
        assertTrue(result);
        verify(messageService, times(1)).sendTaskMessageAsync(eq(MessageConstants.TOPIC_DOC_GENERATE), any(TaskMessage.class));
    }

    @Test
    void testSendCodeParseTaskMessageDelay_success() {
        // 准备测试数据
        Task task = buildCodeParseTask();
        when(messageService.sendTaskMessageDelay(anyString(), any(TaskMessage.class), anyInt())).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendCodeParseTaskMessageDelay(task, 3);

        // 验证结果
        assertTrue(result);
        verify(messageService, times(1)).sendTaskMessageDelay(eq(MessageConstants.TOPIC_CODE_PARSE), any(TaskMessage.class), eq(3));
    }

    @Test
    void testSendDocGenerateTaskMessageDelay_success() {
        // 准备测试数据
        Task task = buildDocGenerateTask();
        when(messageService.sendTaskMessageDelay(anyString(), any(TaskMessage.class), anyInt())).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendDocGenerateTaskMessageDelay(task, 3);

        // 验证结果
        assertTrue(result);
        verify(messageService, times(1)).sendTaskMessageDelay(eq(MessageConstants.TOPIC_DOC_GENERATE), any(TaskMessage.class), eq(3));
    }

    @Test
    void testSendTaskMessage_codeParseTask() {
        // 准备测试数据
        Task task = buildCodeParseTask();
        when(messageService.sendTaskMessage(anyString(), any(TaskMessage.class))).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendTaskMessage(task);

        // 验证结果
        assertTrue(result);
        verify(messageService, times(1)).sendTaskMessage(eq(MessageConstants.TOPIC_CODE_PARSE), any(TaskMessage.class));
    }

    @Test
    void testSendTaskMessage_docGenerateTask() {
        // 准备测试数据
        Task task = buildDocGenerateTask();
        when(messageService.sendTaskMessage(anyString(), any(TaskMessage.class))).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendTaskMessage(task);

        // 验证结果
        assertTrue(result);
        verify(messageService, times(1)).sendTaskMessage(eq(MessageConstants.TOPIC_DOC_GENERATE), any(TaskMessage.class));
    }

    @Test
    void testSendTaskMessage_unknownTaskType() {
        // 准备测试数据
        Task task = new Task();
        task.setId(3L);
        task.setTaskType("UNKNOWN_TYPE");

        // 执行测试
        boolean result = taskMessageSender.sendTaskMessage(task);

        // 验证结果
        assertFalse(result);
        verify(messageService, never()).sendTaskMessage(anyString(), any(TaskMessage.class));
    }

    @Test
    void testSendTaskMessageAsync_codeParseTask() {
        // 准备测试数据
        Task task = buildCodeParseTask();
        when(messageService.sendTaskMessageAsync(anyString(), any(TaskMessage.class))).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendTaskMessageAsync(task);

        // 验证结果
        assertTrue(result);
        verify(messageService, times(1)).sendTaskMessageAsync(eq(MessageConstants.TOPIC_CODE_PARSE), any(TaskMessage.class));
    }

    @Test
    void testSendTaskMessageAsync_docGenerateTask() {
        // 准备测试数据
        Task task = buildDocGenerateTask();
        when(messageService.sendTaskMessageAsync(anyString(), any(TaskMessage.class))).thenReturn(true);

        // 执行测试
        boolean result = taskMessageSender.sendTaskMessageAsync(task);

        // 验证结果
        assertTrue(result);
        verify(messageService, times(1)).sendTaskMessageAsync(eq(MessageConstants.TOPIC_DOC_GENERATE), any(TaskMessage.class));
    }

    @Test
    void testSendTaskMessageAsync_unknownTaskType() {
        // 准备测试数据
        Task task = new Task();
        task.setId(3L);
        task.setTaskType("UNKNOWN_TYPE");

        // 执行测试
        boolean result = taskMessageSender.sendTaskMessageAsync(task);

        // 验证结果
        assertFalse(result);
        verify(messageService, never()).sendTaskMessageAsync(anyString(), any(TaskMessage.class));
    }
}

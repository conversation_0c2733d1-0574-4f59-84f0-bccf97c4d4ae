package com.archscope.infrastructure.messaging.consumer;

import com.archscope.domain.entity.Task;
import com.archscope.domain.message.TaskMessage;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.task.CodeParseTask;
import com.archscope.domain.task.TaskExecutor;
import com.archscope.domain.task.TaskExecutorRegistry;
import com.archscope.domain.valueobject.TaskType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

class CodeParseTaskConsumerTest {

    @Mock
    private TaskQueueService taskQueueService;

    @Mock
    private TaskExecutorRegistry taskExecutorRegistry;

    @Mock
    private TaskExecutor taskExecutor;

    @InjectMocks
    private CodeParseTaskConsumer codeParseTaskConsumer;

    private TaskMessage buildTaskMessage() {
        HashMap<String, Object> params = new HashMap<>();
        params.put("repositoryId", 123);
        params.put("toCommit", "abc123");
        params.put("parseType", "FULL");

        return TaskMessage.builder()
                .taskId(1L)
                .projectId(100L)
                .taskType(TaskType.CODE_PARSE)
                .priority(1)
                .createdAt(LocalDateTime.now())
                .parameters(params)
                .build();
    }

    private Task buildTask() {
        Task task = new Task();
        task.setId(1L);
        task.setProjectId(100L);
        task.setTaskType(CodeParseTask.TASK_TYPE);

        HashMap<String, Object> params = new HashMap<>();
        params.put("repositoryId", 123);
        params.put("toCommit", "abc123");
        params.put("parseType", "FULL");
        task.setParameters(params);

        return task;
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testOnMessage_success() {
        // 准备测试数据
        TaskMessage message = buildTaskMessage();
        Task task = buildTask();

        // 配置Mock行为
        when(taskQueueService.getTaskById(eq(1L))).thenReturn(Optional.of(task));
        when(taskExecutorRegistry.getExecutor(eq(CodeParseTask.TASK_TYPE))).thenReturn(taskExecutor);
        doNothing().when(taskExecutor).execute(any(Task.class));

        // 执行测试
        codeParseTaskConsumer.onMessage(message);

        // 验证结果
        verify(taskQueueService, times(1)).getTaskById(eq(1L));
        verify(taskExecutorRegistry, times(1)).getExecutor(eq(CodeParseTask.TASK_TYPE));
        verify(taskExecutor, times(1)).execute(eq(task));
    }

    @Test
    void testOnMessage_taskNotFound() {
        // 准备测试数据
        TaskMessage message = buildTaskMessage();

        // 配置Mock行为
        when(taskQueueService.getTaskById(eq(1L))).thenReturn(Optional.empty());

        // 执行测试
        codeParseTaskConsumer.onMessage(message);

        // 验证结果
        verify(taskQueueService, times(1)).getTaskById(eq(1L));
        verify(taskExecutorRegistry, never()).getExecutor(any());
        verify(taskExecutor, never()).execute(any());
    }

    @Test
    void testOnMessage_executorNotFound() {
        // 准备测试数据
        TaskMessage message = buildTaskMessage();
        Task task = buildTask();

        // 配置Mock行为
        when(taskQueueService.getTaskById(eq(1L))).thenReturn(Optional.of(task));
        when(taskExecutorRegistry.getExecutor(eq(CodeParseTask.TASK_TYPE))).thenReturn(null);

        // 执行测试
        codeParseTaskConsumer.onMessage(message);

        // 验证结果
        verify(taskQueueService, times(1)).getTaskById(eq(1L));
        verify(taskExecutorRegistry, times(1)).getExecutor(eq(CodeParseTask.TASK_TYPE));
        verify(taskQueueService, times(1)).recordTaskError(eq(1L), anyString());
        verify(taskExecutor, never()).execute(any());
    }

    @Test
    void testOnMessage_nullTaskId() {
        // 准备测试数据
        TaskMessage message = buildTaskMessage();
        message.setTaskId(null);

        // 执行测试
        codeParseTaskConsumer.onMessage(message);

        // 验证结果
        verify(taskQueueService, never()).getTaskById(any());
        verify(taskExecutorRegistry, never()).getExecutor(any());
        verify(taskExecutor, never()).execute(any());
    }

    @Test
    void testOnMessage_exception() {
        // 准备测试数据
        TaskMessage message = buildTaskMessage();
        Task task = buildTask();

        // 配置Mock行为
        when(taskQueueService.getTaskById(eq(1L))).thenReturn(Optional.of(task));
        when(taskExecutorRegistry.getExecutor(eq(CodeParseTask.TASK_TYPE))).thenReturn(taskExecutor);
        doThrow(new RuntimeException("Test exception")).when(taskExecutor).execute(any(Task.class));

        // 执行测试
        codeParseTaskConsumer.onMessage(message);

        // 验证结果
        verify(taskQueueService, times(1)).getTaskById(eq(1L));
        verify(taskExecutorRegistry, times(1)).getExecutor(eq(CodeParseTask.TASK_TYPE));
        verify(taskExecutor, times(1)).execute(eq(task));
        // 异常被捕获，不会向上传播
    }
}

package com.archscope.infrastructure.messaging;

import com.archscope.domain.entity.Task;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.task.CodeParseTask;
import com.archscope.domain.task.DocGenerateTask;
import com.archscope.domain.task.TaskExecutorRegistry;
import com.archscope.domain.valueobject.DocumentType;
import com.archscope.domain.valueobject.TaskStatus;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 任务队列集成测试
 * 注意：此测试需要RocketMQ服务运行，默认禁用
 */
@Disabled("需要RocketMQ服务运行")
@SpringBootTest
@ActiveProfiles("test")
class TaskQueueIntegrationTest {

    @Autowired
    private TaskQueueService taskQueueService;

    @Autowired
    private TaskMessageSender taskMessageSender;

    @Autowired
    private TaskExecutorRegistry taskExecutorRegistry;

    @Test
    void testCodeParseTaskFlow() throws Exception {
        // 创建代码解析任务
        CodeParseTask codeParseTask = new CodeParseTask();
        codeParseTask.setRepositoryId(123L);
        codeParseTask.setToCommit("test-commit");
        codeParseTask.setParseType(CodeParseTask.ParseType.FULL);

        Task task = codeParseTask.toTaskEntity(100L);
        task = taskQueueService.enqueueTask(task);
        assertNotNull(task.getId());
        assertEquals(TaskStatus.PENDING, task.getStatus());

        // 发送任务消息
        boolean sent = taskMessageSender.sendCodeParseTaskMessage(task);
        assertTrue(sent);

        // 等待任务处理完成
        TimeUnit.SECONDS.sleep(5);

        // 验证任务状态
        Optional<Task> updatedTaskOpt = taskQueueService.getTaskById(task.getId());
        assertTrue(updatedTaskOpt.isPresent());
        Task updatedTask = updatedTaskOpt.get();

        // 任务应该已经完成或正在进行中
        assertTrue(
            updatedTask.getStatus() == TaskStatus.COMPLETED ||
            updatedTask.getStatus() == TaskStatus.PROCESSING
        );
    }

    @Test
    void testDocGenerateTaskFlow() throws Exception {
        // 创建文档生成任务
        DocGenerateTask docGenerateTask = new DocGenerateTask();
        docGenerateTask.setRepositoryId(123L);
        docGenerateTask.setCommitId("test-commit");
        docGenerateTask.setDocTypes(Arrays.asList(DocumentType.ARCHITECTURE, DocumentType.API));
        docGenerateTask.setTemplateId("default");
        docGenerateTask.setOutputFormat("markdown");
        docGenerateTask.setIncludeArchDiagrams(true);

        Task task = docGenerateTask.toTaskEntity(100L);
        task = taskQueueService.enqueueTask(task);
        assertNotNull(task.getId());
        assertEquals(TaskStatus.PENDING, task.getStatus());

        // 发送任务消息
        boolean sent = taskMessageSender.sendDocGenerateTaskMessage(task);
        assertTrue(sent);

        // 等待任务处理完成
        TimeUnit.SECONDS.sleep(5);

        // 验证任务状态
        Optional<Task> updatedTaskOpt = taskQueueService.getTaskById(task.getId());
        assertTrue(updatedTaskOpt.isPresent());
        Task updatedTask = updatedTaskOpt.get();

        // 任务应该已经完成或正在进行中
        assertTrue(
            updatedTask.getStatus() == TaskStatus.COMPLETED ||
            updatedTask.getStatus() == TaskStatus.PROCESSING
        );
    }

    @Test
    void testTaskLifecycle() {
        // 创建任务
        Task task = new Task();
        task.setProjectId(100L);
        task.setTaskType(CodeParseTask.TASK_TYPE);
        task.setName("测试任务");
        task.setDescription("测试任务描述");
        HashMap<String, Object> params = new HashMap<>();
        params.put("repositoryId", 123);
        params.put("toCommit", "test-commit");
        params.put("parseType", "FULL");
        task.setParameters(params);
        task.setCreatedAt(LocalDateTime.now());

        // 入队任务
        task = taskQueueService.enqueueTask(task);
        assertNotNull(task.getId());
        assertEquals(TaskStatus.PENDING, task.getStatus());

        // 更新任务状态
        task = taskQueueService.updateTaskStatus(task.getId(), "IN_PROGRESS");
        assertEquals(TaskStatus.PROCESSING, task.getStatus());

        // 更新任务进度
        task = taskQueueService.updateTaskProgress(task.getId(), 50);
        assertEquals(50, task.getProgress());

        // 完成任务
        task = taskQueueService.completeTask(task.getId(), "测试任务完成");
        assertEquals(TaskStatus.COMPLETED, task.getStatus());
        assertEquals(100, task.getProgress());
        assertEquals("测试任务完成", task.getResult());

        // 获取任务
        Optional<Task> taskOpt = taskQueueService.getTaskById(task.getId());
        assertTrue(taskOpt.isPresent());
        assertEquals(task.getId(), taskOpt.get().getId());
    }
}

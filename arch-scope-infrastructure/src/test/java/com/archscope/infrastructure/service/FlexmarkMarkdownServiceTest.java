package com.archscope.infrastructure.service;

import com.archscope.domain.service.impl.FlexmarkMarkdownService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

class FlexmarkMarkdownServiceTest {

    private FlexmarkMarkdownService markdownService;

    @BeforeEach
    void setUp() {
        markdownService = new FlexmarkMarkdownService();
    }

    @Test
    void convertToHtml_withBasicMarkdown_shouldReturnCorrectHtml() {
        // 准备测试数据
        String markdown = "# 测试标题\n\n这是一个**粗体**测试。";

        // 执行测试
        String html = markdownService.convertToHtml(markdown);

        // 验证结果
        assertNotNull(html);
        // 打印实际输出以便调试
        System.out.println("HTML output: " + html);

        // 使用包含而不是精确匹配
        assertTrue(html.contains("测试标题"));
        assertTrue(html.contains("这是一个"));
        assertTrue(html.contains("粗体"));
    }

    @Test
    void convertToHtml_withAdditionalHeadElements_shouldAddElements() {
        // 准备测试数据
        String markdown = "# 测试标题\n\n```mermaid\ngraph TD;\n    A-->B;\n```";
        Map<String, String> additionalHeadElements = new HashMap<>();

        // 执行测试
        String html = markdownService.convertToHtml(markdown, additionalHeadElements);

        // 验证结果
        assertNotNull(html);
        assertTrue(html.contains("测试标题"));

        // 打印实际结果以便调试
        System.out.println("HTML output: " + html);
        System.out.println("Additional head elements: " + additionalHeadElements);

        // 验证添加的头部元素
        assertTrue(additionalHeadElements.containsKey("prism"));
        assertTrue(additionalHeadElements.get("prism").contains("prism.js"));

        // 验证mermaid配置
        assertTrue(additionalHeadElements.containsKey("mermaid"));
        assertTrue(additionalHeadElements.get("mermaid").contains("mermaid.min.js"));
    }

    @Test
    void extractTitle_withTitleInMarkdown_shouldReturnTitle() {
        // 准备测试数据
        String markdown = "# 测试标题\n\n这是内容。";

        // 执行测试
        String title = markdownService.extractTitle(markdown);

        // 验证结果
        assertEquals("测试标题", title);
    }

    @Test
    void extractTitle_withoutTitleInMarkdown_shouldReturnDefaultTitle() {
        // 准备测试数据
        String markdown = "这是没有标题的内容。";

        // 执行测试
        String title = markdownService.extractTitle(markdown);

        // 验证结果
        assertEquals("无标题", title);
    }

    @Test
    void generateTableOfContents_withHeadings_shouldGenerateToc() {
        // 准备测试数据
        String markdown = "# 一级标题\n\n## 二级标题1\n\n内容1\n\n## 二级标题2\n\n内容2";

        // 执行测试
        String toc = markdownService.generateTableOfContents(markdown);

        // 验证结果
        assertNotNull(toc);
        assertTrue(toc.contains("div class=\"toc\""));
        assertTrue(toc.contains("二级标题1"));
        assertTrue(toc.contains("二级标题2"));
    }

    @Test
    void containsMermaidDiagram_withMermaidCode_shouldReturnTrue() {
        // 准备测试数据
        String markdown = "# 测试\n\n```mermaid\ngraph TD;\n    A-->B;\n```";

        // 执行测试
        boolean result = markdownService.containsMermaidDiagram(markdown);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void containsMermaidDiagram_withoutMermaidCode_shouldReturnFalse() {
        // 准备测试数据
        String markdown = "# 测试\n\n```java\nSystem.out.println(\"Hello\");\n```";

        // 执行测试
        boolean result = markdownService.containsMermaidDiagram(markdown);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void containsMathFormula_withMathFormula_shouldReturnTrue() {
        // 准备测试数据
        String markdown = "# 测试\n\n数学公式：$E=mc^2$";

        // 执行测试
        boolean result = markdownService.containsMathFormula(markdown);

        // 验证结果
        assertTrue(result);
    }

    @Test
    void containsMathFormula_withoutMathFormula_shouldReturnFalse() {
        // 准备测试数据
        String markdown = "# 测试\n\n普通文本，没有数学公式。";

        // 执行测试
        boolean result = markdownService.containsMathFormula(markdown);

        // 验证结果
        assertFalse(result);
    }

    @Test
    void extractPlainText_shouldRemoveMarkdownFormatting() {
        // 准备测试数据
        String markdown = "# 标题\n\n**粗体**和*斜体*\n\n- 列表项1\n- 列表项2\n\n```java\ncode\n```";

        // 执行测试
        String plainText = markdownService.extractPlainText(markdown);

        // 验证结果
        assertNotNull(plainText);
        assertFalse(plainText.contains("#"));
        assertFalse(plainText.contains("**"));
        assertFalse(plainText.contains("*"));
        assertFalse(plainText.contains("-"));
        assertFalse(plainText.contains("```"));
        assertTrue(plainText.contains("标题"));
        assertTrue(plainText.contains("粗体"));
        assertTrue(plainText.contains("斜体"));
        assertTrue(plainText.contains("列表项1"));
        assertTrue(plainText.contains("列表项2"));
    }

    @Test
    void convertToHtml_withJavaCodeBlock_shouldApplyCodeHighlighting() {
        // 准备测试数据 - 包含Java代码块的Markdown
        String markdown = "# Java代码示例\n\n以下是一个Java代码示例：\n\n```java\npublic class HelloWorld {\n    public static void main(String[] args) {\n        System.out.println(\"Hello, World!\");\n    }\n}\n```";

        // 执行测试
        String html = markdownService.convertToHtml(markdown);

        // 验证结果
        assertNotNull(html);
        System.out.println("Java code HTML output: " + html);

        // 验证代码块被正确转换为带有语言类的pre和code标签
        assertTrue(html.contains("<pre"));
        assertTrue(html.contains("<code class=\"language-java\""));

        // 验证Java代码内容存在
        assertTrue(html.contains("public class HelloWorld"));
        assertTrue(html.contains("System.out.println"));

        // 验证代码块中的特殊字符被正确转义
        assertTrue(html.contains("&quot;Hello, World!&quot;"));
    }

    @Test
    void convertToHtml_withMultipleCodeBlocks_shouldApplyCorrectLanguageClasses() {
        // 准备测试数据 - 包含多种语言代码块的Markdown
        String markdown = "# 多语言代码示例\n\n" +
                "## Java代码\n\n```java\npublic class Example { }\n```\n\n" +
                "## JavaScript代码\n\n```javascript\nfunction example() { return true; }\n```\n\n" +
                "## Python代码\n\n```python\ndef example(): return True\n```";

        // 执行测试
        String html = markdownService.convertToHtml(markdown);

        // 验证结果
        assertNotNull(html);

        // 验证各语言代码块被正确标记
        assertTrue(html.contains("<code class=\"language-java\""));
        assertTrue(html.contains("<code class=\"language-javascript\""));
        assertTrue(html.contains("<code class=\"language-python\""));

        // 验证代码内容存在
        assertTrue(html.contains("public class Example"));
        assertTrue(html.contains("function example"));
        assertTrue(html.contains("def example"));
    }
}

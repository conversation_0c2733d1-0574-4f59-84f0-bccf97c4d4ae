package com.archscope.infrastructure.service;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.service.MarkdownService;
import com.archscope.domain.service.impl.LunrDocumentSearchService;
import com.archscope.domain.valueobject.DocumentType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class LunrDocumentSearchServiceTest {

    @Mock
    private MarkdownService markdownService;

    private LunrDocumentSearchService searchService;
    private DocumentVersion documentVersion;
    private Path tempDir;

    @BeforeEach
    void setUp() throws IOException {
        // 创建临时目录
        tempDir = Files.createTempDirectory("search-test");

        // 创建测试Markdown文件
        Path contentPath = tempDir.resolve("test.md");
        Files.write(contentPath, "# 测试文档\n\n这是一个测试文档，包含一些关键词如架构、设计和模式。".getBytes(StandardCharsets.UTF_8));

        // 创建测试文档版本
        documentVersion = DocumentVersion.builder()
                .id(1L)
                .projectId(1L)
                .docType(DocumentType.ARCHITECTURE)
                .commitId("abc123")
                .contentPath(contentPath.toString())
                .timestamp(LocalDateTime.now())
                .lastModified(LocalDateTime.now())
                .versionTag("v1.0.0")
                .title("架构文档")
                .description("项目架构文档")
                .isPublished(true)
                .build();

        // 设置模拟行为 - 使用lenient模式避免不必要的模拟错误
        lenient().when(markdownService.extractTitle(anyString())).thenReturn("测试文档");
        lenient().when(markdownService.extractPlainText(anyString())).thenReturn("这是一个测试文档，包含一些关键词如架构、设计和模式。");

        // 创建搜索服务
        searchService = new LunrDocumentSearchService(markdownService, new ObjectMapper());
    }

    @Test
    void generateSearchIndex_shouldCreateIndexFile() throws IOException {
        // 准备测试数据
        List<DocumentVersion> documentVersions = Arrays.asList(documentVersion);
        Path outputDir = tempDir.resolve("output");
        Files.createDirectories(outputDir);

        // 执行测试
        Path result = searchService.generateSearchIndex(documentVersions, outputDir);

        // 验证结果
        assertNotNull(result);
        assertTrue(Files.exists(result));
        assertTrue(result.toString().endsWith("search-index.json"));

        // 验证索引文件内容
        String content = new String(Files.readAllBytes(result), StandardCharsets.UTF_8);
        System.out.println("Search index content: " + content);

        // 使用更灵活的方式验证内容
        assertTrue(content.contains("1"));
        assertTrue(content.contains("测试文档"));
    }

    @Test
    void extractDocumentContent_shouldReturnDocumentData() throws IOException {
        // 执行测试
        Map<String, Object> result = searchService.extractDocumentContent(documentVersion);

        // 验证结果
        assertNotNull(result);
        assertEquals("1", result.get("id"));
        assertEquals("测试文档", result.get("title"));
        assertEquals("这是一个测试文档，包含一些关键词如架构、设计和模式。", result.get("content"));
        assertEquals("architecture.html", result.get("url"));
        assertEquals("架构设计", result.get("type"));
        assertEquals("1", result.get("projectId"));
        assertEquals("v1.0.0", result.get("version"));
    }

    @Test
    void getSearchScripts_shouldReturnScriptsList() {
        // 执行测试
        List<String> scripts = searchService.getSearchScripts();

        // 验证结果
        assertNotNull(scripts);
        assertFalse(scripts.isEmpty());
        assertTrue(scripts.contains("/js/lunr.min.js"));
        assertTrue(scripts.contains("/js/search.js"));
    }

    @Test
    void getSearchStyles_shouldReturnStylesList() {
        // 执行测试
        List<String> styles = searchService.getSearchStyles();

        // 验证结果
        assertNotNull(styles);
        assertFalse(styles.isEmpty());
        assertTrue(styles.contains("/css/search.css"));
    }
}

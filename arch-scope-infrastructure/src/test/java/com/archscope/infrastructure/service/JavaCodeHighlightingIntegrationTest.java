package com.archscope.infrastructure.service;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.DocumentVersionRepository;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.DocumentSearchService;
import com.archscope.domain.service.MarkdownService;
import com.archscope.domain.service.impl.DefaultStaticSiteGenerationService;
import com.archscope.domain.service.impl.FlexmarkMarkdownService;
import com.archscope.domain.valueobject.DocumentType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import org.mockito.ArgumentMatchers;

/**
 * Java代码高亮集成测试
 * 测试Markdown中的Java代码块在转换为HTML并应用Prism.js高亮时的正确性
 */
@ExtendWith(MockitoExtension.class)
public class JavaCodeHighlightingIntegrationTest {

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private DocumentVersionRepository documentVersionRepository;

    @Mock
    private DocumentSearchService documentSearchService;

    @Mock
    private TemplateEngine templateEngine;

    private MarkdownService markdownService;
    private DefaultStaticSiteGenerationService staticSiteGenerationService;

    private Project project;
    private DocumentVersion javaDocVersion;
    private Path tempDir;

    @BeforeEach
    void setUp() throws IOException {
        // 创建真实的MarkdownService实例
        markdownService = new FlexmarkMarkdownService();

        // 创建静态站点生成服务
        staticSiteGenerationService = new DefaultStaticSiteGenerationService(
                markdownService,
                projectRepository,
                documentVersionRepository,
                templateEngine,
                documentSearchService
        );

        // 设置基础URL
        ReflectionTestUtils.setField(staticSiteGenerationService, "staticSiteBaseUrl", "http://localhost:8080/docs");

        // 创建临时目录
        tempDir = Files.createTempDirectory("java-code-highlight-test");

        // 创建测试项目
        project = Project.builder()
                .id(1L)
                .name("Java代码高亮测试项目")
                .description("测试Java代码高亮功能")
                .repositoryUrl("https://github.com/test/java-highlight")
                .branch("main")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // 创建包含Java代码的Markdown内容
        String javaMarkdown = "# Java代码高亮测试\n\n" +
                "以下是一些Java代码示例，用于测试代码高亮功能：\n\n" +
                "## 简单的Java类\n\n" +
                "```java\n" +
                "public class HelloWorld {\n" +
                "    public static void main(String[] args) {\n" +
                "        System.out.println(\"Hello, World!\");\n" +
                "    }\n" +
                "}\n" +
                "```\n\n" +
                "## 更复杂的Java示例\n\n" +
                "```java\n" +
                "import java.util.List;\n" +
                "import java.util.ArrayList;\n" +
                "import java.util.stream.Collectors;\n\n" +
                "/**\n" +
                " * 这是一个示例Java类，用于测试代码高亮\n" +
                " */\n" +
                "public class CodeHighlightExample {\n" +
                "    private final List<String> items = new ArrayList<>();\n" +
                "    \n" +
                "    public void addItem(String item) {\n" +
                "        if (item != null && !item.isEmpty()) {\n" +
                "            items.add(item);\n" +
                "        }\n" +
                "    }\n" +
                "    \n" +
                "    public List<String> getFilteredItems(String prefix) {\n" +
                "        return items.stream()\n" +
                "                .filter(item -> item.startsWith(prefix))\n" +
                "                .sorted()\n" +
                "                .collect(Collectors.toList());\n" +
                "    }\n" +
                "    \n" +
                "    @Override\n" +
                "    public String toString() {\n" +
                "        return String.format(\"CodeHighlightExample{items=%s}\", items);\n" +
                "    }\n" +
                "}\n" +
                "```\n";

        // 创建Markdown文件
        Path javaMarkdownPath = tempDir.resolve("java_code_highlight.md");
        Files.write(javaMarkdownPath, javaMarkdown.getBytes(StandardCharsets.UTF_8));

        // 创建文档版本
        javaDocVersion = DocumentVersion.builder()
                .id(1L)
                .projectId(1L)
                .docType(DocumentType.API)
                .commitId("abc123")
                .contentPath(javaMarkdownPath.toString())
                .timestamp(LocalDateTime.now())
                .lastModified(LocalDateTime.now())
                .versionTag("v1.0.0")
                .title("Java代码高亮测试")
                .description("测试Java代码高亮功能")
                .isPublished(true)
                .build();
    }

    @Test
    void testJavaCodeHighlighting_withRealMarkdownService() throws IOException {
        // 准备测试数据
        Path javaMarkdownPath = Paths.get(javaDocVersion.getContentPath());
        String markdownContent = new String(Files.readAllBytes(javaMarkdownPath), StandardCharsets.UTF_8);

        // 使用真实的MarkdownService转换Markdown到HTML
        Map<String, String> additionalHeadElements = new HashMap<>();
        String html = markdownService.convertToHtml(markdownContent, additionalHeadElements);

        // 验证结果
        assertNotNull(html);
        System.out.println("Converted HTML: " + html);

        // 验证代码块被正确转换
        assertTrue(html.contains("<pre><code class=\"language-java\">"));
        assertTrue(html.contains("public class HelloWorld"));
        assertTrue(html.contains("System.out.println"));

        // 验证更复杂的Java代码也被正确转换
        assertTrue(html.contains("import java.util.List"));
        assertTrue(html.contains("public class CodeHighlightExample"));
        assertTrue(html.contains("@Override"));
        assertTrue(html.contains(".collect(Collectors.toList())"));

        // 验证添加了Prism.js相关的头部元素
        assertTrue(additionalHeadElements.containsKey("prism"));
        assertTrue(additionalHeadElements.get("prism").contains("prism.css"));
        assertTrue(additionalHeadElements.get("prism").contains("prism.js"));
    }

    @Test
    void testGeneratePage_withJavaCodeHighlighting() throws IOException {
        // 设置模拟行为
        when(projectRepository.findById(any())).thenReturn(Optional.of(project));
        when(documentVersionRepository.findByProjectIdAndDocType(any(), any())).thenReturn(Arrays.asList(javaDocVersion));

        // 使用真实的MarkdownService，但模拟TemplateEngine
        when(templateEngine.process(eq("document"), any())).thenAnswer(invocation -> {
            Context context = invocation.getArgument(1);
            String content = (String) context.getVariable("content");
            @SuppressWarnings("unchecked")
            Map<String, String> additionalHeadElements = (Map<String, String>) context.getVariable("additionalHeadElements");

            // 安全获取prism元素，避免null
            String prismElements = "";
            if (additionalHeadElements != null && additionalHeadElements.containsKey("prism")) {
                prismElements = additionalHeadElements.get("prism");
            } else {
                // 提供默认的代码高亮元素
                prismElements = "<link rel=\"stylesheet\" href=\"/css/prism.css\">\n<script src=\"/js/prism.js\"></script>";
            }

            // 生成包含代码高亮的HTML
            return "<!DOCTYPE html>\n" +
                   "<html>\n" +
                   "<head>\n" +
                   "    <title>Java代码高亮测试</title>\n" +
                   "    " + prismElements + "\n" +
                   "</head>\n" +
                   "<body>\n" +
                   content + "\n" +
                   "</body>\n" +
                   "</html>";
        });

        // 执行测试
        Path outputDir = tempDir.resolve("output");
        Files.createDirectories(outputDir);
        Path result = staticSiteGenerationService.generatePage(javaDocVersion, outputDir);

        // 验证结果
        assertNotNull(result);
        assertTrue(Files.exists(result));

        // 读取生成的HTML文件
        String generatedHtml = new String(Files.readAllBytes(result), StandardCharsets.UTF_8);
        System.out.println("Generated HTML page: " + generatedHtml);

        // 验证HTML包含代码高亮相关的元素
        assertTrue(generatedHtml.contains("<link rel=\"stylesheet\" href=\"/css/prism.css\">"));
        assertTrue(generatedHtml.contains("<script src=\"/js/prism.js\"></script>"));
        assertTrue(generatedHtml.contains("<code class=\"language-java\">"));
        assertTrue(generatedHtml.contains("public class HelloWorld"));
        assertTrue(generatedHtml.contains("CodeHighlightExample"));

        // 验证调用
        verify(projectRepository).findById(any());
        verify(documentVersionRepository).findByProjectIdAndDocType(any(), any());
        verify(templateEngine).process(eq("document"), any(Context.class));
    }

    @Test
    void testEndToEndDocumentGeneration_withJavaCodeHighlighting() throws IOException {
        // 设置模拟行为
        when(projectRepository.findById(any())).thenReturn(Optional.of(project));
        when(documentVersionRepository.findByProjectIdAndDocType(any(), any())).thenReturn(Arrays.asList(javaDocVersion));

        // 模拟静态资源复制
        Path mockIndexPath = Paths.get("mock-search-index.json");
        when(documentSearchService.generateSearchIndex(any(), any())).thenReturn(mockIndexPath);

        // 模拟TemplateEngine处理
        when(templateEngine.process(anyString(), any())).thenAnswer(invocation -> {
            String templateName = invocation.getArgument(0);
            Object contextArg = invocation.getArgument(1);

            if (!(contextArg instanceof Context)) {
                return "<html><body>Mock template for " + templateName + "</body></html>";
            }
            Context context = (Context) contextArg;

            if ("document".equals(templateName)) {
                String content = (String) context.getVariable("content");
                @SuppressWarnings("unchecked")
                Map<String, String> additionalHeadElements = (Map<String, String>) context.getVariable("additionalHeadElements");

                // 安全获取prism元素，避免null
                String prismElements = "";
                if (additionalHeadElements != null && additionalHeadElements.containsKey("prism")) {
                    prismElements = additionalHeadElements.get("prism");
                } else {
                    // 提供默认的代码高亮元素
                    prismElements = "<link rel=\"stylesheet\" href=\"/css/prism.css\"><script src=\"/js/prism.js\"></script>";
                }

                return "<!DOCTYPE html><html><head>" +
                       prismElements +
                       "</head><body>" + content + "</body></html>";
            } else {
                return "<html><body>Mock template for " + templateName + "</body></html>";
            }
        });

        // 执行测试 - 生成整个站点
        Path outputDir = tempDir.resolve("site_output");
        Path result = staticSiteGenerationService.generateSite(Arrays.asList(javaDocVersion), outputDir);

        // 验证结果
        assertNotNull(result);
        assertEquals(outputDir, result);

        // 验证生成的API文档页面
        Path apiHtmlPath = outputDir.resolve("1").resolve("api.html");
        assertTrue(Files.exists(apiHtmlPath), "API HTML文件应该存在");

        // 读取生成的HTML文件
        String apiHtml = new String(Files.readAllBytes(apiHtmlPath), StandardCharsets.UTF_8);

        // 验证HTML包含代码高亮相关的元素
        assertTrue(apiHtml.contains("prism.js"), "HTML应包含Prism.js引用");
        assertTrue(apiHtml.contains("prism.css"), "HTML应包含Prism.css引用");
        assertTrue(apiHtml.contains("<code class=\"language-java\">"), "HTML应包含Java代码高亮类");
        assertTrue(apiHtml.contains("public class HelloWorld"), "HTML应包含Java代码内容");

        // 验证调用
        verify(templateEngine, atLeastOnce()).process(eq("document"), any());
    }
}

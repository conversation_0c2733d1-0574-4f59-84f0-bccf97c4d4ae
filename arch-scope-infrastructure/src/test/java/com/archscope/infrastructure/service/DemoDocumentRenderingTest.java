package com.archscope.infrastructure.service;

import com.archscope.domain.repository.DocumentVersionRepository;
import com.archscope.domain.service.DocumentStorageService;
import com.archscope.domain.service.MarkdownService;
import com.archscope.domain.service.impl.FlexmarkMarkdownService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mockito;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 演示文档渲染效果测试
 * 验证/prompts/demo目录下的演示文档渲染效果
 */
class DemoDocumentRenderingTest {

    private DocumentStorageService documentStorageService;
    private MarkdownService markdownService;

    @TempDir
    Path tempDir;

    @BeforeEach
    void setUp() {
        // 创建 mock DocumentVersionRepository
        DocumentVersionRepository mockRepository = Mockito.mock(DocumentVersionRepository.class);

        // 设置 mock 行为：返回空列表，因为这个测试主要测试文件系统存储
        Mockito.when(mockRepository.findByProjectId(Mockito.anyLong()))
               .thenReturn(new ArrayList<>());

        documentStorageService = new DocumentStorageServiceImpl(mockRepository);
        markdownService = new FlexmarkMarkdownService();
    }

    @Test
    void testDocumentationIndexRendering() throws IOException {
        testDocumentRendering("prompts/demo/documentation_index.md", "PRODUCT_INTRO", "文档导航");
    }

    @Test
    void testApiDocumentationRendering() throws IOException {
        testDocumentRendering("prompts/demo/api_documentation.md", "API", "API 概述");
    }

    @Test
    void testUserManualRendering() throws IOException {
        testDocumentRendering("prompts/demo/user_manual.md", "USER_MANUAL", "用户手册");
    }

    @Test
    void testExtensionGuideRendering() throws IOException {
        testDocumentRendering("prompts/demo/extension_guide.md", "EXTENSION", "扩展概述");
    }

    /**
     * 测试单个文档的渲染效果
     */
    private void testDocumentRendering(String filePath, String docType, String expectedContent) throws IOException {
        // 1. 读取演示文档
        Path demoPath = Paths.get(filePath);
        if (!Files.exists(demoPath)) {
            System.out.println("跳过测试，文件不存在: " + filePath);
            return;
        }

        byte[] bytes = Files.readAllBytes(demoPath);
        String markdownContent = new String(bytes, "UTF-8");

        System.out.println("================================================================================");
        System.out.println("测试文档: " + filePath);
        System.out.println("文档类型: " + docType);
        System.out.println("文档长度: " + markdownContent.length() + " 字符");
        System.out.println("================================================================================");

        // 2. 存储到文档存储服务
        Long projectId = 999L; // 测试项目ID
        String version = "demo-v1.0.0";

        String storedPath = documentStorageService.storeDocument(projectId, docType, markdownContent, version);
        assertNotNull(storedPath);
        System.out.println("文档存储路径: " + storedPath);

        // 3. 验证文档存储
        assertTrue(documentStorageService.documentExists(projectId, docType, version));

        // 4. 读取存储的文档
        Optional<String> retrievedContent = documentStorageService.getDocumentContent(projectId, docType, version);
        assertTrue(retrievedContent.isPresent());
        assertEquals(markdownContent, retrievedContent.get());

        // 5. 测试Markdown渲染
        String htmlContent = markdownService.convertToHtml(markdownContent);
        assertNotNull(htmlContent);
        assertFalse(htmlContent.isEmpty());

        System.out.println("HTML渲染成功，长度: " + htmlContent.length() + " 字符");

        // 6. 验证HTML内容质量
        assertTrue(htmlContent.contains("<h1>"), "应该包含一级标题");
        assertTrue(htmlContent.contains(expectedContent), "应该包含预期内容: " + expectedContent);

        // 7. 检查Mermaid图表
        boolean hasMermaid = markdownService.containsMermaidDiagram(markdownContent);
        System.out.println("包含Mermaid图表: " + hasMermaid);
        if (hasMermaid) {
            assertTrue(htmlContent.contains("mermaid"), "HTML应该包含mermaid相关内容");
        }

        // 8. 验证常见HTML元素
        System.out.println("HTML元素验证:");
        System.out.println("- 标题元素: " + countOccurrences(htmlContent, "<h"));
        System.out.println("- 段落元素: " + countOccurrences(htmlContent, "<p>"));
        System.out.println("- 列表元素: " + (countOccurrences(htmlContent, "<ul>") + countOccurrences(htmlContent, "<ol>")));
        System.out.println("- 链接元素: " + countOccurrences(htmlContent, "<a href"));
        System.out.println("- 代码块: " + countOccurrences(htmlContent, "<pre>"));
        System.out.println("- 行内代码: " + countOccurrences(htmlContent, "<code>"));
        System.out.println("- 强调元素: " + (countOccurrences(htmlContent, "<strong>") + countOccurrences(htmlContent, "<em>")));

        // 9. 输出HTML片段示例
        System.out.println("\nHTML渲染示例（前300字符）:");
        System.out.println(htmlContent.substring(0, Math.min(300, htmlContent.length())));
        if (htmlContent.length() > 300) {
            System.out.println("...[省略剩余内容]");
        }

        System.out.println("\n✅ 文档渲染测试通过: " + filePath);
        System.out.println();
    }

    /**
     * 计算字符串中子串出现的次数
     */
    private int countOccurrences(String text, String substring) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(substring, index)) != -1) {
            count++;
            index += substring.length();
        }
        return count;
    }

    @Test
    void testAllDemoDocumentsVersionManagement() throws IOException {
        // 测试所有演示文档的版本管理
        Long projectId = 888L;
        String version = "demo-v1.0.0";

        String[] demoFiles = {
            "prompts/demo/documentation_index.md",
            "prompts/demo/api_documentation.md",
            "prompts/demo/user_manual.md",
            "prompts/demo/extension_guide.md"
        };

        String[] docTypes = {
            "PRODUCT_INTRO",
            "API", 
            "USER_MANUAL",
            "EXTENSION"
        };

        int storedCount = 0;
        for (int i = 0; i < demoFiles.length; i++) {
            Path demoPath = Paths.get(demoFiles[i]);
            if (Files.exists(demoPath)) {
                byte[] bytes = Files.readAllBytes(demoPath);
                String content = new String(bytes, "UTF-8");
                
                documentStorageService.storeDocument(projectId, docTypes[i], content, version);
                storedCount++;
            }
        }

        // 验证版本管理
        List<String> versions = documentStorageService.getProjectVersions(projectId);
        assertTrue(versions.contains(version));

        List<String> documentTypes = documentStorageService.getDocumentTypes(projectId, version);
        assertEquals(storedCount, documentTypes.size());

        System.out.println("版本管理测试通过:");
        System.out.println("- 存储文档数量: " + storedCount);
        System.out.println("- 项目版本: " + versions);
        System.out.println("- 文档类型: " + documentTypes);
    }
}

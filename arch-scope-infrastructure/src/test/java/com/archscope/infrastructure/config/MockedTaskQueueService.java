package com.archscope.infrastructure.config;

import com.archscope.domain.entity.Task;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.valueobject.TaskStatus;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * 任务队列服务的模拟实现
 * 用于测试环境，避免依赖真实的任务队列服务
 */
@TestConfiguration
public class MockedTaskQueueService {

    @Bean
    @Primary
    public TaskQueueService taskQueueService() {
        return new InMemoryTaskQueueService();
    }

    /**
     * 内存中的任务队列服务实现
     */
    public static class InMemoryTaskQueueService implements TaskQueueService {
        private final Map<Long, Task> tasks = new ConcurrentHashMap<>();
        private final AtomicLong idGenerator = new AtomicLong(1);

        @Override
        public Task enqueueTask(Task task) {
            if (task.getId() == null) {
                task.setId(idGenerator.getAndIncrement());
            }
            if (task.getCreatedAt() == null) {
                task.setCreatedAt(LocalDateTime.now());
            }
            if (task.getStatus() == null) {
                task.setStatus(TaskStatus.PENDING);
            }
            task.setUpdatedAt(LocalDateTime.now());
            tasks.put(task.getId(), task);
            return task;
        }

        @Override
        public Optional<Task> dequeueTask() {
            return tasks.values().stream()
                    .filter(task -> task.getStatus() == TaskStatus.PENDING)
                    .findFirst()
                    .map(task -> {
                        task.setStatus(TaskStatus.PROCESSING);
                        task.setUpdatedAt(LocalDateTime.now());
                        return task;
                    });
        }

        @Override
        public Optional<Task> dequeueTaskByType(String taskType) {
            return tasks.values().stream()
                    .filter(task -> task.getStatus() == TaskStatus.PENDING)
                    .filter(task -> taskType.equals(task.getTaskType()))
                    .findFirst()
                    .map(task -> {
                        task.setStatus(TaskStatus.PROCESSING);
                        task.setUpdatedAt(LocalDateTime.now());
                        return task;
                    });
        }

        @Override
        public List<Task> getPendingTasks() {
            return tasks.values().stream()
                    .filter(task -> task.getStatus() == TaskStatus.PENDING)
                    .collect(Collectors.toList());
        }

        @Override
        public List<Task> getPendingTasksByType(String taskType) {
            return tasks.values().stream()
                    .filter(task -> task.getStatus() == TaskStatus.PENDING)
                    .filter(task -> taskType.equals(task.getTaskType()))
                    .collect(Collectors.toList());
        }

        @Override
        public List<Task> getPendingTasksByProject(Long projectId) {
            return tasks.values().stream()
                    .filter(task -> task.getStatus() == TaskStatus.PENDING)
                    .filter(task -> projectId.equals(task.getProjectId()))
                    .collect(Collectors.toList());
        }

        @Override
        public Task updateTaskStatus(Long taskId, String status) {
            Task task = tasks.get(taskId);
            if (task != null) {
                task.setStatus(TaskStatus.fromLegacyStatus(status));
                task.setUpdatedAt(LocalDateTime.now());
            }
            return task;
        }

        @Override
        public Task updateTaskProgress(Long taskId, Integer progress) {
            Task task = tasks.get(taskId);
            if (task != null) {
                task.setProgress(progress);
                task.setUpdatedAt(LocalDateTime.now());
            }
            return task;
        }

        @Override
        public Task recordTaskError(Long taskId, String errorMessage) {
            Task task = tasks.get(taskId);
            if (task != null) {
                task.setErrorLog(errorMessage);
                task.setStatus(TaskStatus.FAILED);
                task.setUpdatedAt(LocalDateTime.now());
            }
            return task;
        }

        @Override
        public Task completeTask(Long taskId, String result) {
            Task task = tasks.get(taskId);
            if (task != null) {
                task.setResult(result);
                task.setStatus(TaskStatus.COMPLETED);
                task.setProgress(100);
                task.setUpdatedAt(LocalDateTime.now());
            }
            return task;
        }

        @Override
        public Task cancelTask(Long taskId) {
            Task task = tasks.get(taskId);
            if (task != null) {
                task.setStatus(TaskStatus.CANCELLED);
                task.setUpdatedAt(LocalDateTime.now());
            }
            return task;
        }

        @Override
        public Optional<Task> getTaskById(Long taskId) {
            return Optional.ofNullable(tasks.get(taskId));
        }

        @Override
        public int cleanupExpiredTasks(int days) {
            LocalDateTime cutoff = LocalDateTime.now().minusDays(days);
            List<Long> toRemove = tasks.values().stream()
                    .filter(task -> task.getStatus() == TaskStatus.COMPLETED || task.getStatus() == TaskStatus.CANCELLED)
                    .filter(task -> task.getUpdatedAt().isBefore(cutoff))
                    .map(Task::getId)
                    .collect(Collectors.toList());
            
            toRemove.forEach(tasks::remove);
            return toRemove.size();
        }
    }
}

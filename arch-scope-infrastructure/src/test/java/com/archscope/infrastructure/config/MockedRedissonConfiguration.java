package com.archscope.infrastructure.config;

import org.mockito.Mockito;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RMap;
import org.redisson.api.RSet;
import org.redisson.api.RedissonClient;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * Redisson测试配置类，提供模拟的Redisson客户端
 * 在测试环境中使用，避免需要真实的Redis连接
 */
@TestConfiguration
public class MockedRedissonConfiguration {
    
    @Bean
    @Primary
    public RedissonClient redisson() throws InterruptedException {
        // 创建Redisson客户端的模拟对象
        RedissonClient mockRedisson = Mockito.mock(RedissonClient.class);
        
        // 设置常用的Redisson对象
        RLock mockLock = Mockito.mock(RLock.class);
        RMap<Object, Object> mockMap = Mockito.mock(RMap.class);
        RSet<Object> mockSet = Mockito.mock(RSet.class);
        RBucket<Object> mockBucket = Mockito.mock(RBucket.class);
        
        // 配置模拟行为
        Mockito.when(mockRedisson.getLock(Mockito.anyString())).thenReturn(mockLock);
        Mockito.when(mockRedisson.getMap(Mockito.anyString())).thenReturn(mockMap);
        Mockito.when(mockRedisson.getSet(Mockito.anyString())).thenReturn(mockSet);
        Mockito.when(mockRedisson.getBucket(Mockito.anyString())).thenReturn(mockBucket);
        
        // 锁相关模拟行为
        Mockito.when(mockLock.tryLock()).thenReturn(true);
        Mockito.when(mockLock.tryLock(Mockito.anyLong(), Mockito.anyLong(), Mockito.any(TimeUnit.class))).thenReturn(true);
        
        // 数据操作相关模拟行为
        Mockito.when(mockBucket.get()).thenReturn(null);
        Mockito.doNothing().when(mockBucket).set(Mockito.any());
        
        return mockRedisson;
    }
} 
package com.archscope.infrastructure.config;

import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer;
import org.mockito.Mockito;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

/**
 * RocketMQ监听器容器的模拟配置
 * 用于测试环境，避免需要真实的RocketMQ连接
 */
@TestConfiguration
public class MockedRocketMQListenerContainer {

    /**
     * 提供一个Bean后处理器，用于拦截和模拟RocketMQ监听器容器
     */
    @Bean
    @Primary
    public BeanPostProcessor rocketMQListenerContainerPostProcessor() {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
                return bean;
            }

            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
                // 如果是RocketMQ监听器容器，则返回一个模拟对象
                if (bean instanceof DefaultRocketMQListenerContainer) {
                    DefaultRocketMQListenerContainer container = (DefaultRocketMQListenerContainer) bean;
                    
                    // 获取监听器对象和注解信息
                    RocketMQListener<?> listener = container.getRocketMQListener();
                    RocketMQMessageListener annotation = container.getRocketMQMessageListener();
                    
                    // 创建一个模拟的监听器容器
                    DefaultRocketMQListenerContainer mockContainer = Mockito.mock(DefaultRocketMQListenerContainer.class);
                    
                    // 设置基本属性
                    Mockito.when(mockContainer.getConsumerGroup()).thenReturn(annotation.consumerGroup());
                    Mockito.when(mockContainer.getTopic()).thenReturn(annotation.topic());
                    Mockito.when(mockContainer.getRocketMQListener()).thenReturn(listener);
                    Mockito.when(mockContainer.getRocketMQMessageListener()).thenReturn(annotation);
                    
                    // 模拟启动和停止方法
                    Mockito.doNothing().when(mockContainer).start();
                    Mockito.doNothing().when(mockContainer).stop();
                    
                    return mockContainer;
                }
                return bean;
            }
        };
    }
}

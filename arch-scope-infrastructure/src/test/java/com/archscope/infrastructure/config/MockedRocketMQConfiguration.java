package com.archscope.infrastructure.config;

import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.apache.rocketmq.spring.autoconfigure.RocketMQProperties;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer;
import org.apache.rocketmq.spring.support.RocketMQUtil;
import org.mockito.Mockito;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.messaging.Message;

/**
 * RocketMQ测试配置类，提供模拟RocketMQ模板
 * 在测试环境中使用，避免需要真实的RocketMQ连接
 */
@TestConfiguration
public class MockedRocketMQConfiguration {

    @Bean
    @Primary
    public RocketMQTemplate rocketMQTemplate() {
        // 创建RocketMQTemplate的模拟对象
        RocketMQTemplate mockTemplate = Mockito.mock(RocketMQTemplate.class);

        // 模拟DefaultMQProducer
        DefaultMQProducer mockProducer = Mockito.mock(DefaultMQProducer.class);
        Mockito.when(mockTemplate.getProducer()).thenReturn(mockProducer);
        Mockito.when(mockProducer.getSendMsgTimeout()).thenReturn(3000);

        // 模拟同步发送消息
        Mockito.when(mockTemplate.syncSend(Mockito.anyString(), Mockito.any(Message.class), Mockito.anyLong(), Mockito.anyInt()))
               .thenReturn(createMockSendResult());

        // 模拟异步发送消息 - 使用明确的类型参数
        Mockito.doAnswer(invocation -> {
            // 获取回调参数
            org.apache.rocketmq.client.producer.SendCallback callback = invocation.getArgument(2);
            // 模拟成功回调
            callback.onSuccess(createMockSendResult());
            return null;
        }).when(mockTemplate).asyncSend(Mockito.anyString(), Mockito.any(Message.class), Mockito.any(org.apache.rocketmq.client.producer.SendCallback.class));

        // 模拟普通发送消息 - 使用明确的类型参数
        Mockito.doNothing().when(mockTemplate).convertAndSend(Mockito.anyString(), Mockito.any(Object.class));

        return mockTemplate;
    }

    /**
     * 提供RocketMQ属性配置，用于测试环境
     */
    @Bean
    @Primary
    public RocketMQProperties rocketMQProperties() {
        RocketMQProperties properties = new RocketMQProperties();
        properties.setNameServer("localhost:9876");

        RocketMQProperties.Producer producer = new RocketMQProperties.Producer();
        producer.setGroup("test-producer-group");
        producer.setSendMessageTimeout(3000);
        properties.setProducer(producer);

        return properties;
    }

    /**
     * 模拟RocketMQ监听器容器工厂，避免在测试环境中连接真实的RocketMQ服务器
     */
    @Bean
    @Primary
    @ConditionalOnMissingBean(name = "rocketMQListenerContainerFactory")
    public Object rocketMQListenerContainerFactory() {
        // 返回一个模拟对象，避免创建真实的监听器容器工厂
        return Mockito.mock(Object.class);
    }

    /**
     * 创建模拟的发送结果
     */
    private SendResult createMockSendResult() {
        SendResult result = new SendResult();
        result.setSendStatus(SendStatus.SEND_OK);
        result.setMsgId("test-msg-" + System.currentTimeMillis());
        return result;
    }
}

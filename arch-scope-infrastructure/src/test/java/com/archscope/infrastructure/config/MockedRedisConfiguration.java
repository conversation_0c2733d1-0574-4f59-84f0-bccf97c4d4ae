package com.archscope.infrastructure.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 完全模拟的Redis配置，不需要真实Redis服务
 */
@TestConfiguration
@Slf4j
public class MockedRedisConfiguration {
    
    private static final Map<String, Object> STORAGE = new ConcurrentHashMap<>();
    private static final Map<String, Map<Object, Object>> HASH_STORAGE = new ConcurrentHashMap<>();
    private static final Map<String, List<Object>> LIST_STORAGE = new ConcurrentHashMap<>();
    private static final Map<String, Set<Object>> SET_STORAGE = new ConcurrentHashMap<>();
    
    // 清除所有测试数据
    public static void clearAllData() {
        STORAGE.clear();
        HASH_STORAGE.clear();
        LIST_STORAGE.clear();
        SET_STORAGE.clear();
    }
    
    @Bean
    @Primary
    public RedisConnectionFactory redisConnectionFactory() {
        RedisConnectionFactory factory = Mockito.mock(RedisConnectionFactory.class);
        RedisConnection redisConnection = Mockito.mock(RedisConnection.class);
        Mockito.when(factory.getConnection()).thenReturn(redisConnection);
        return factory;
    }
    
    @Bean
    @Primary
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory connectionFactory) {
        // 创建一个完全模拟的RedisTemplate
        RedisTemplate<String, Object> template = Mockito.mock(RedisTemplate.class);
        // 不设置连接工厂，因为我们完全模拟
        
        // 模拟ValueOperations
        ValueOperations<String, Object> valueOps = Mockito.mock(ValueOperations.class);
        Mockito.when(template.opsForValue()).thenReturn(valueOps);
        
        // 模拟set操作
        Mockito.doAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object value = invocation.getArgument(1);
            // 直接存储原值，简化实现
            STORAGE.put(key, value);
            log.debug("Mock Redis SET key: {}, value type: {}, value: {}",
                       key, (value != null ? value.getClass().getName() : "null"), value);
            return null;
        }).when(valueOps).set(Mockito.anyString(), Mockito.any());
        
        // 模拟带过期时间的set操作
        Mockito.doAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object value = invocation.getArgument(1);
            // 忽略过期时间参数，直接存储原值
            STORAGE.put(key, value);
            log.debug("Mock Redis SET with TTL key: {}, value type: {}, value: {}",
                       key, (value != null ? value.getClass().getName() : "null"), value);
            return null;
        }).when(valueOps).set(Mockito.anyString(), Mockito.any(), Mockito.anyLong(), Mockito.any(TimeUnit.class));
        
        // 模拟get操作
        Mockito.when(valueOps.get(Mockito.anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object storedValue = STORAGE.get(key);
            log.debug("Mock Redis GET key: {}, stored type: {}, value: {}",
                       key, (storedValue != null ? storedValue.getClass().getName() : "null"), storedValue);
            return storedValue;
        });
        
        // 模拟递增操作
        Mockito.when(valueOps.increment(Mockito.anyString(), Mockito.anyLong())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            long delta = invocation.getArgument(1);
            Object currentVal = STORAGE.get(key);
            long newVal;
            if (currentVal instanceof Number) {
                newVal = ((Number) currentVal).longValue() + delta;
            } else if (currentVal instanceof String) {
                try {
                    newVal = Long.parseLong((String) currentVal) + delta;
                } catch (NumberFormatException e) {
                    newVal = delta;
                }
            } else {
                newVal = delta;
            }
            STORAGE.put(key, newVal);
            return newVal;
        });
        
        // 模拟递减操作
        Mockito.when(valueOps.decrement(Mockito.anyString(), Mockito.anyLong())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            long delta = invocation.getArgument(1);
            Object currentVal = STORAGE.get(key);
            long newVal;
            if (currentVal instanceof Number) {
                newVal = ((Number) currentVal).longValue() - delta;
            } else if (currentVal instanceof String) {
                try {
                    newVal = Long.parseLong((String) currentVal) - delta;
                } catch (NumberFormatException e) {
                    newVal = -delta;
                }
            } else {
                newVal = -delta;
            }
            STORAGE.put(key, newVal);
            return newVal;
        });
        
        // 模拟HashOperations
        HashOperations<String, Object, Object> hashOps = Mockito.mock(HashOperations.class);
        Mockito.when(template.opsForHash()).thenReturn(hashOps);
        
        // 模拟hash get操作
        Mockito.when(hashOps.get(Mockito.anyString(), Mockito.any())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object hashKey = invocation.getArgument(1);
            Map<Object, Object> hash = HASH_STORAGE.get(key);
            return hash != null ? hash.get(hashKey) : null;
        });
        
        // 模拟hash put操作
        Mockito.doAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object hashKey = invocation.getArgument(1);
            Object value = invocation.getArgument(2);
            Map<Object, Object> hash = HASH_STORAGE.computeIfAbsent(key, k -> new HashMap<>());
            hash.put(hashKey, value);
            return null;
        }).when(hashOps).put(Mockito.anyString(), Mockito.any(), Mockito.any());
        
        // 模拟hash putAll操作
        Mockito.doAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Map<?, ?> map = invocation.getArgument(1);
            Map<Object, Object> hash = HASH_STORAGE.computeIfAbsent(key, k -> new HashMap<>());
            hash.putAll(map);
            return null;
        }).when(hashOps).putAll(Mockito.anyString(), Mockito.anyMap());
        
        // 模拟hash delete操作
        Mockito.doAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object[] hashKeys = invocation.getArgument(1);
            Map<Object, Object> hash = HASH_STORAGE.get(key);
            if (hash != null) {
                for (Object hashKey : hashKeys) {
                    hash.remove(hashKey);
                }
            }
            return null;
        }).when(hashOps).delete(Mockito.anyString(), Mockito.any());
        
        // 模拟hash hasKey操作
        Mockito.when(hashOps.hasKey(Mockito.anyString(), Mockito.any())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object hashKey = invocation.getArgument(1);
            Map<Object, Object> hash = HASH_STORAGE.get(key);
            return hash != null && hash.containsKey(hashKey);
        });
        
        // 模拟ListOperations
        ListOperations<String, Object> listOps = Mockito.mock(ListOperations.class);
        Mockito.when(template.opsForList()).thenReturn(listOps);
        
        // 模拟list rightPush操作
        Mockito.when(listOps.rightPush(Mockito.anyString(), Mockito.any())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object value = invocation.getArgument(1);
            List<Object> list = LIST_STORAGE.computeIfAbsent(key, k -> new ArrayList<>());
            list.add(value);
            return (long) list.size();
        });
        
        // 模拟list rightPushAll操作
        Mockito.when(listOps.rightPushAll(Mockito.anyString(), Mockito.any(Object[].class))).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object[] values = invocation.getArgument(1);
            List<Object> list = LIST_STORAGE.computeIfAbsent(key, k -> new ArrayList<>());
            Collections.addAll(list, values);
            return (long) list.size();
        });
        
        // 模拟list size操作
        Mockito.when(listOps.size(Mockito.anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            List<Object> list = LIST_STORAGE.get(key);
            return list != null ? (long) list.size() : 0L;
        });
        
        // 模拟list range操作
        Mockito.when(listOps.range(Mockito.anyString(), Mockito.anyLong(), Mockito.anyLong())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            long start = invocation.getArgument(1);
            long end = invocation.getArgument(2);
            List<Object> list = LIST_STORAGE.get(key);
            if (list == null) return new ArrayList<>();
            
            int size = list.size();
            int startIndex = (int) Math.max(0, start);
            int endIndex = (int) (end < 0 ? size + end + 1 : Math.min(size, end + 1));
            
            if (startIndex >= size || startIndex >= endIndex) return new ArrayList<>();
            return new ArrayList<>(list.subList(startIndex, endIndex));
        });
        
        // 模拟list index操作
        Mockito.when(listOps.index(Mockito.anyString(), Mockito.anyLong())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            long index = invocation.getArgument(1);
            List<Object> list = LIST_STORAGE.get(key);
            if (list == null || index < 0 || index >= list.size()) return null;
            return list.get((int) index);
        });
        
        // 模拟list remove操作
        Mockito.when(listOps.remove(Mockito.anyString(), Mockito.anyLong(), Mockito.any())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            long count = invocation.getArgument(1);
            Object value = invocation.getArgument(2);
            List<Object> list = LIST_STORAGE.get(key);
            if (list == null) return 0L;
            
            long removed = 0;
            if (count > 0) {
                Iterator<Object> it = list.iterator();
                while (it.hasNext() && removed < count) {
                    if (it.next().equals(value)) {
                        it.remove();
                        removed++;
                    }
                }
            } else if (count < 0) {
                // 从后向前删除
                ListIterator<Object> it = list.listIterator(list.size());
                while (it.hasPrevious() && removed < Math.abs(count)) {
                    if (it.previous().equals(value)) {
                        it.remove();
                        removed++;
                    }
                }
            } else { // count == 0, 删除所有匹配元素
                Iterator<Object> it = list.iterator();
                while (it.hasNext()) {
                    if (it.next().equals(value)) {
                        it.remove();
                        removed++;
                    }
                }
            }
            return removed;
        });
        
        // 模拟SetOperations
        SetOperations<String, Object> setOps = Mockito.mock(SetOperations.class);
        Mockito.when(template.opsForSet()).thenReturn(setOps);
        
        // 模拟set add操作
        Mockito.when(setOps.add(Mockito.anyString(), Mockito.any())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Set<Object> set = SET_STORAGE.computeIfAbsent(key, k -> new HashSet<>());
            int added = 0;
            // 处理可变参数
            for (int i = 1; i < invocation.getArguments().length; i++) {
                Object value = invocation.getArgument(i);
                if (set.add(value)) {
                    added++;
                }
            }
            log.debug("Mock Redis SET ADD key: {}, added: {}", key, added);
            return (long) added;
        });
        
        // 模拟set members操作
        Mockito.when(setOps.members(Mockito.anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Set<Object> set = SET_STORAGE.get(key);
            return set != null ? new HashSet<>(set) : new HashSet<>();
        });
        
        // 模拟set isMember操作
        Mockito.when(setOps.isMember(Mockito.anyString(), Mockito.any())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Object value = invocation.getArgument(1);
            Set<Object> set = SET_STORAGE.get(key);
            return set != null && set.contains(value);
        });
        
        // 模拟set size操作
        Mockito.when(setOps.size(Mockito.anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Set<Object> set = SET_STORAGE.get(key);
            return set != null ? (long) set.size() : 0L;
        });
        
        // 模拟set remove操作
        Mockito.when(setOps.remove(Mockito.anyString(), Mockito.any())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Set<Object> set = SET_STORAGE.get(key);
            if (set == null) return 0L;

            long removed = 0;
            // 处理可变参数
            for (int i = 1; i < invocation.getArguments().length; i++) {
                Object value = invocation.getArgument(i);
                if (set.remove(value)) {
                    removed++;
                }
            }
            log.debug("Mock Redis SET REMOVE key: {}, removed: {}", key, removed);
            return removed;
        });
        

        
        // 模拟keys操作
        Mockito.when(template.keys(Mockito.anyString())).thenAnswer(invocation -> {
            String pattern = invocation.getArgument(0);
            if (pattern.equals("*")) {
                Set<String> allKeys = new HashSet<>();
                allKeys.addAll(STORAGE.keySet());
                allKeys.addAll(HASH_STORAGE.keySet());
                allKeys.addAll(LIST_STORAGE.keySet());
                allKeys.addAll(SET_STORAGE.keySet());
                return allKeys;
            } else {
                // 简单实现，只支持简单的*通配符匹配
                String regex = pattern.replace("*", ".*");
                Set<String> matchedKeys = new HashSet<>();
                
                STORAGE.keySet().stream()
                    .filter(key -> key.matches(regex))
                    .forEach(matchedKeys::add);
                
                HASH_STORAGE.keySet().stream()
                    .filter(key -> key.matches(regex))
                    .forEach(matchedKeys::add);
                
                LIST_STORAGE.keySet().stream()
                    .filter(key -> key.matches(regex))
                    .forEach(matchedKeys::add);
                
                SET_STORAGE.keySet().stream()
                    .filter(key -> key.matches(regex))
                    .forEach(matchedKeys::add);
                
                return matchedKeys;
            }
        });
        
        // 模拟hasKey操作
        Mockito.when(template.hasKey(Mockito.anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            return STORAGE.containsKey(key) || 
                   HASH_STORAGE.containsKey(key) ||
                   LIST_STORAGE.containsKey(key) ||
                   SET_STORAGE.containsKey(key);
        });
        
        // 模拟expire操作
        Mockito.when(template.expire(Mockito.anyString(), Mockito.anyLong(), Mockito.any(TimeUnit.class)))
            .thenReturn(true);
        
        // 模拟getExpire操作
        Mockito.when(template.getExpire(Mockito.anyString(), Mockito.any(TimeUnit.class)))
            .thenReturn(60L);

        // 模拟delete操作
        Mockito.when(template.delete(Mockito.anyString())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            boolean removed = STORAGE.remove(key) != null ||
                             HASH_STORAGE.remove(key) != null ||
                             LIST_STORAGE.remove(key) != null ||
                             SET_STORAGE.remove(key) != null;
            log.debug("Mock Redis DELETE key: {}, removed: {}", key, removed);
            return removed;
        });

        // 模拟批量delete操作
        Mockito.when(template.delete(Mockito.anyCollection())).thenAnswer(invocation -> {
            @SuppressWarnings("unchecked")
            Collection<String> keys = invocation.getArgument(0);
            long deletedCount = 0;
            for (String key : keys) {
                boolean removed = STORAGE.remove(key) != null ||
                                 HASH_STORAGE.remove(key) != null ||
                                 LIST_STORAGE.remove(key) != null ||
                                 SET_STORAGE.remove(key) != null;
                if (removed) {
                    deletedCount++;
                }
            }
            log.debug("Mock Redis DELETE batch, keys: {}, deleted: {}", keys.size(), deletedCount);
            return deletedCount;
        });

        // 模拟increment操作
        Mockito.when(valueOps.increment(Mockito.anyString(), Mockito.anyLong())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Long delta = invocation.getArgument(1);
            Object currentValue = STORAGE.get(key);
            long newValue = (currentValue instanceof Number ? ((Number) currentValue).longValue() : 0L) + delta;
            STORAGE.put(key, newValue);
            log.debug("Mock Redis INCREMENT key: {}, delta: {}, newValue: {}", key, delta, newValue);
            return newValue;
        });

        // 模拟decrement操作
        Mockito.when(valueOps.decrement(Mockito.anyString(), Mockito.anyLong())).thenAnswer(invocation -> {
            String key = invocation.getArgument(0);
            Long delta = invocation.getArgument(1);
            Object currentValue = STORAGE.get(key);
            long newValue = (currentValue instanceof Number ? ((Number) currentValue).longValue() : 0L) - delta;
            STORAGE.put(key, newValue);
            log.debug("Mock Redis DECREMENT key: {}, delta: {}, newValue: {}", key, delta, newValue);
            return newValue;
        });

        return template;
    }
    
    @Bean
    public void clearRedisBeforeAllTests() {
        // 清除所有Redis测试数据
        clearAllData();
    }
} 
package com.archscope.infrastructure.config;

import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.data.redis.RedisAutoConfiguration;
import org.springframework.boot.autoconfigure.security.servlet.SecurityAutoConfiguration;
import org.springframework.context.annotation.Import;

/**
 * 测试自动配置类
 * 自动导入所有测试环境需要的配置类
 * 并且排除一些生产环境配置，防止它们干扰测试
 */
@AutoConfiguration
@EnableAutoConfiguration(exclude = {
    RedisAutoConfiguration.class,   // 排除Redis自动配置，使用我们自己的模拟Redis配置
    SecurityAutoConfiguration.class, // 排除安全自动配置，简化测试环境
    org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration.class // 排除RocketMQ自动配置，使用我们自己的模拟RocketMQ配置
})
@Import({
    TestConfig.class,              // 通用测试配置
    MockedRedisConfiguration.class, // Redis模拟配置
    MockedRedissonConfiguration.class, // Redisson模拟配置
    MockedRocketMQConfiguration.class, // RocketMQ模拟配置
    MockedRocketMQListenerContainer.class, // RocketMQ监听器容器模拟配置
    MockedTaskQueueService.class, // 任务队列服务模拟配置
    MockedTaskExecutorRegistry.class // 任务执行器注册表模拟配置
})
public class TestAutoConfiguration {
    // 这是一个标记类，主要通过注解和导入完成配置工作
}
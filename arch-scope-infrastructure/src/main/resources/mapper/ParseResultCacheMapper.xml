<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.mapper.ParseResultCacheMapper">
    <resultMap id="BaseResultMap" type="com.archscope.domain.entity.ParseResultCache">
        <id column="id" property="id" />
        <result column="repository_id" property="repositoryId" />
        <result column="commit_id" property="commitId" />
        <result column="parse_results" property="parseResults" typeHandler="com.archscope.infrastructure.config.typehandler.JsonTypeHandler" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="valid" property="valid" />
        <result column="cache_size" property="cacheSize" />
    </resultMap>
    
    <select id="selectByRepositoryIdAndCommitId" resultMap="BaseResultMap">
        SELECT * FROM parse_result_cache 
        WHERE repository_id = #{repositoryId} AND commit_id = #{commitId}
    </select>
    
    <select id="selectAllByRepositoryId" resultMap="BaseResultMap">
        SELECT * FROM parse_result_cache 
        WHERE repository_id = #{repositoryId}
    </select>
    
    <delete id="deleteAllByRepositoryId">
        DELETE FROM parse_result_cache 
        WHERE repository_id = #{repositoryId}
    </delete>
    
    <select id="existsByRepositoryIdAndCommitId" resultType="boolean">
        SELECT COUNT(*) > 0 FROM parse_result_cache 
        WHERE repository_id = #{repositoryId} AND commit_id = #{commitId}
    </select>
</mapper>

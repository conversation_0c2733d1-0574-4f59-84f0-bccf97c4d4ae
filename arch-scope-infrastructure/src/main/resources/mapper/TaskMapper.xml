<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.mapper.TaskMapper">
    <resultMap id="BaseResultMap" type="com.archscope.infrastructure.entity.TaskEntity">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="task_type" property="taskType" />
        <result column="status" property="status" />
        <result column="priority" property="priority" />
        <result column="progress" property="progress" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="started_at" property="startedAt" />
        <result column="completed_at" property="completedAt" />
        <result column="error_message" property="errorMessage" />
        <result column="result_summary" property="resultSummary" />
        <result column="parameters" property="parameters"
            typeHandler="com.archscope.infrastructure.config.typehandler.MapTypeHandler" />
        <result column="project_id" property="projectId" />
        <result column="created_by" property="createdBy" />
        <result column="assigned_to" property="assignedTo" />

        <!-- LLM任务管理新增字段 -->
        <result column="worker_id" property="workerId" />
        <result column="processing_started_at" property="processingStartedAt" />
        <result column="timeout_at" property="timeoutAt" />
        <result column="overall_status" property="overallStatus" />
        <result column="commit_id" property="commitId" />
        <result column="results" property="results" />
        <result column="execution_time_ms" property="executionTimeMs" />
        <result column="retry_count" property="retryCount" />
        <result column="max_retries" property="maxRetries" />
        <result column="last_error_detail" property="lastErrorDetail" />
        <result column="task_version" property="taskVersion" />

        <!-- V1.8迁移新增字段 -->
        <result column="detailed_status" property="detailedStatus" />
        <result column="error_log" property="errorLog" />
        <result column="execution_time" property="executionTime" />
        <result column="user_id" property="userId" />
        <result column="assignee_id" property="assigneeId" />
        <result column="result" property="result" />
    </resultMap>

    <!-- 定义通用的字段列表，避免重复 -->
    <sql id="Base_Column_List"> id, name, description, task_type, status, priority, progress,
        created_at, updated_at, started_at, completed_at, error_message, result_summary, parameters,
        project_id, created_by, assigned_to, worker_id, worker_type, processing_started_at,
        timeout_at, overall_status, commit_id, results, execution_time_ms, retry_count, max_retries,
        last_error_detail, task_version, detailed_status, error_log, execution_time, user_id,
        assignee_id, result </sql>

    <!-- 优化的任务拉取查询，使用FOR UPDATE确保并发安全 -->
    <select id="findNextPendingTask" resultMap="BaseResultMap"> SELECT <include
            refid="Base_Column_List" /> FROM tasks WHERE status = 'PENDING' AND (retry_count IS NULL
        OR retry_count &lt; max_retries) ORDER BY priority DESC, created_at ASC LIMIT 1 FOR UPDATE
        SKIP LOCKED </select>

    <select id="findNextPendingTaskByType" resultMap="BaseResultMap"> SELECT <include
            refid="Base_Column_List" /> FROM tasks WHERE status = 'PENDING' AND task_type =
        #{taskType} AND (retry_count IS NULL OR retry_count &lt; max_retries) ORDER BY priority
        DESC, created_at ASC LIMIT 1 FOR UPDATE SKIP LOCKED </select>

    <select id="findAllByStatus" resultMap="BaseResultMap"> SELECT <include refid="Base_Column_List" />
        FROM tasks WHERE status = #{status} ORDER BY created_at DESC </select>

    <select id="findAllByStatusAndTaskType" resultMap="BaseResultMap"> SELECT <include
            refid="Base_Column_List" /> FROM tasks WHERE status = #{status} AND task_type =
        #{taskType} ORDER BY created_at DESC </select>

    <select id="findAllByStatusAndProjectId" resultMap="BaseResultMap"> SELECT <include
            refid="Base_Column_List" /> FROM tasks WHERE status = #{status} AND project_id =
        #{projectId} ORDER BY created_at DESC </select>

    <delete id="deleteCompletedTasksOlderThan"> DELETE FROM tasks WHERE status = 'COMPLETED' AND
        completed_at &lt; #{date} </delete>

    <!-- 查找超时的PROCESSING任务 -->
    <select id="findTimeoutProcessingTasks" resultMap="BaseResultMap"> SELECT <include
            refid="Base_Column_List" /> FROM tasks WHERE status = 'PROCESSING' AND timeout_at IS NOT
        NULL AND timeout_at &lt; NOW() AND (retry_count IS NULL OR retry_count &lt; max_retries)
        ORDER BY timeout_at ASC LIMIT 100 </select>

    <!-- 更新任务状态为PROCESSING并设置超时时间 -->
    <update id="updateTaskToProcessing"> UPDATE tasks SET status = 'PROCESSING', worker_id =
        #{workerId}, processing_started_at = NOW(), timeout_at = #{timeoutAt}, updated_at = NOW()
        WHERE id = #{taskId} AND status = 'PENDING' </update>

    <!-- 重置超时任务状态为PENDING -->
    <update id="resetTimeoutTask"> UPDATE tasks SET status = 'PENDING', worker_id = NULL,
        worker_type = NULL, processing_started_at = NULL, timeout_at = NULL, retry_count =
        retry_count + 1, last_error_detail = CONCAT(COALESCE(last_error_detail, ''), '\n[', NOW(),
        '] 任务超时，重置为待处理状态'), updated_at = NOW() WHERE id = #{taskId} </update>

    <!-- 完成任务并设置结果 -->
    <update id="completeTaskWithResult"> UPDATE tasks SET status = 'COMPLETED', overall_status =
        #{overallStatus}, results = #{results}, execution_time_ms = #{executionTimeMs}, completed_at
        = NOW(), updated_at = NOW() WHERE id = #{taskId} AND status = 'PROCESSING' </update>

    <!-- 标记任务失败 -->
    <update id="failTaskWithError"> UPDATE tasks SET status = 'FAILED', overall_status = 'FAILED',
        last_error_detail = #{errorDetail}, execution_time_ms = #{executionTimeMs}, completed_at =
        NOW(), updated_at = NOW() WHERE id = #{taskId} AND status = 'PROCESSING' </update>

    <!-- 新增：分页查询任务 -->
    <select id="findTasksWithPagination" resultMap="BaseResultMap"> SELECT <include
            refid="Base_Column_List" /> FROM tasks <where>
            <if test="status != null"> AND status = #{status} </if>
            <if
                test="taskType != null and taskType != ''"> AND task_type = #{taskType} </if>
            <if
                test="projectId != null"> AND project_id = #{projectId} </if>
            <if
                test="workerId != null and workerId != ''"> AND worker_id = #{workerId} </if>
            <if
                test="startDate != null"> AND created_at &gt;= #{startDate} </if>
            <if
                test="endDate != null"> AND created_at &lt;= #{endDate} </if>
        </where> ORDER
        BY <choose>
            <when test="orderBy != null and orderBy == 'priority'"> priority DESC, created_at DESC </when>
            <when test="orderBy != null and orderBy == 'updated'"> updated_at DESC </when>
            <otherwise> created_at DESC </otherwise>
        </choose> LIMIT #{offset}, #{limit} </select>

    <!-- 新增：统计任务数量 -->
    <select id="countTasksWithCondition" resultType="long"> SELECT COUNT(1) FROM tasks <where>
            <if test="status != null"> AND status = #{status} </if>
            <if
                test="taskType != null and taskType != ''"> AND task_type = #{taskType} </if>
            <if
                test="projectId != null"> AND project_id = #{projectId} </if>
            <if
                test="workerId != null and workerId != ''"> AND worker_id = #{workerId} </if>
            <if
                test="startDate != null"> AND created_at &gt;= #{startDate} </if>
            <if
                test="endDate != null"> AND created_at &lt;= #{endDate} </if>
        </where>
    </select>

    <!-- 新增：批量更新任务状态 -->
    <update id="batchUpdateTaskStatus"> UPDATE tasks SET status = #{newStatus}, updated_at = NOW()
        WHERE id IN <foreach collection="taskIds" item="taskId" open="(" separator="," close=")">
        #{taskId} </foreach> AND status = #{currentStatus} </update>

    <!-- 新增：批量重置超时任务 -->
    <update id="batchResetTimeoutTasks"> UPDATE tasks SET status = 'PENDING', worker_id = NULL,
        worker_type = NULL, processing_started_at = NULL, timeout_at = NULL, retry_count =
        retry_count + 1, last_error_detail = CONCAT(COALESCE(last_error_detail, ''), '\n[', NOW(),
        '] 批量超时重置'), updated_at = NOW() WHERE id IN <foreach collection="taskIds" item="taskId"
            open="(" separator="," close=")"> #{taskId} </foreach> AND status = 'PROCESSING' </update>

    <!-- 新增：获取任务统计信息 -->
    <select id="getTaskStatistics" resultType="map"> SELECT status, COUNT(*) as count, AVG(CASE WHEN
        execution_time_ms IS NOT NULL THEN execution_time_ms ELSE 0 END) as avg_execution_time,
        MAX(CASE WHEN execution_time_ms IS NOT NULL THEN execution_time_ms ELSE 0 END) as
        max_execution_time, MIN(CASE WHEN execution_time_ms IS NOT NULL THEN execution_time_ms ELSE
        0 END) as min_execution_time FROM tasks <where>
            <if test="projectId != null"> AND project_id = #{projectId} </if>
            <if
                test="taskType != null and taskType != ''"> AND task_type = #{taskType} </if>
            <if
                test="startDate != null"> AND created_at &gt;= #{startDate} </if>
            <if
                test="endDate != null"> AND created_at &lt;= #{endDate} </if>
        </where> GROUP
        BY status ORDER BY status </select>

    <!-- 新增：查找长时间运行的任务 -->
    <select id="findLongRunningTasks" resultMap="BaseResultMap"> SELECT <include
            refid="Base_Column_List" /> FROM tasks WHERE status = 'PROCESSING' AND
        processing_started_at IS NOT NULL AND processing_started_at &lt; DATE_SUB(NOW(), INTERVAL
        #{minutes} MINUTE) ORDER BY processing_started_at ASC LIMIT #{limit} </select>

    <!-- 新增：优化的任务锁定，使用CAS操作 -->
    <update id="lockTaskWithCAS"> UPDATE tasks SET status = 'PROCESSING', worker_id = #{workerId},
        processing_started_at = NOW(), timeout_at = DATE_ADD(NOW(), INTERVAL #{timeoutMinutes}
        MINUTE), task_version = task_version + 1, updated_at = NOW() WHERE id = #{taskId} AND status
        = 'PENDING' AND task_version = #{expectedVersion} AND (retry_count IS NULL OR retry_count
        &lt; max_retries) </update>

    <!-- 重试任务 -->
    <update id="retryTask"> UPDATE tasks SET status = 'PENDING', updated_at = NOW(), started_at =
        NULL, completed_at = NULL, error_message = NULL, worker_id = NULL, processing_started_at =
        NULL, timeout_at = NULL, retry_count = retry_count + 1, task_version = task_version + 1
        WHERE id = #{taskId} AND status = 'FAILED' </update>
</mapper>
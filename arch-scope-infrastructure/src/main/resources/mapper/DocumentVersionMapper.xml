<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.mapper.DocumentVersionMapper">

    <!-- 基础列映射 -->
    <resultMap id="BaseResultMap" type="com.archscope.domain.entity.DocumentVersion">
        <id column="id" property="id" />
        <result column="project_id" property="projectId" />
        <result column="commit_id" property="commitId" />
        <result column="content_path" property="contentPath" />
        <result column="timestamp" property="timestamp" />
        <result column="doc_type" property="docType" />
        <result column="version_tag" property="versionTag" />
        <result column="compare_metadata" property="compareMetadata" typeHandler="com.archscope.infrastructure.config.typehandler.MapTypeHandler" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="author" property="author" />
        <result column="last_modified" property="lastModified" />
        <result column="is_published" property="isPublished" />
        <result column="status" property="status" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List">
        id, project_id, commit_id, content_path, timestamp, doc_type, version_tag,
        compare_metadata, title, description, author, last_modified, is_published, status
    </sql>

    <!-- 插入文档版本 -->
    <insert id="insert" parameterType="com.archscope.domain.entity.DocumentVersion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO document_version (
            project_id, commit_id, content_path, timestamp, doc_type, version_tag,
            compare_metadata, title, description, author, last_modified, is_published, status
        ) VALUES (
            #{projectId}, #{commitId}, #{contentPath}, #{timestamp}, #{docType}, #{versionTag},
            #{compareMetadata,typeHandler=com.archscope.infrastructure.config.typehandler.MapTypeHandler}, #{title}, #{description}, #{author}, #{lastModified}, #{isPublished}, #{status}
        )
    </insert>

    <!-- 更新文档版本 -->
    <update id="updateById" parameterType="com.archscope.domain.entity.DocumentVersion">
        UPDATE document_version
        SET
            project_id = #{projectId},
            commit_id = #{commitId},
            content_path = #{contentPath},
            timestamp = #{timestamp},
            doc_type = #{docType},
            version_tag = #{versionTag},
            compare_metadata = #{compareMetadata,typeHandler=com.archscope.infrastructure.config.typehandler.MapTypeHandler},
            title = #{title},
            description = #{description},
            author = #{author},
            last_modified = #{lastModified},
            is_published = #{isPublished},
            status = #{status}
        WHERE id = #{id}
    </update>

    <!-- 根据ID查询文档版本 -->
    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM document_version
        WHERE id = #{id}
    </select>

    <!-- 根据项目ID查询文档版本列表 -->
    <select id="selectByProjectId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM document_version
        WHERE project_id = #{projectId}
        ORDER BY timestamp DESC
    </select>

    <!-- 根据项目ID和文档类型查询文档版本列表 -->
    <select id="selectByProjectIdAndDocType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM document_version
        WHERE project_id = #{projectId} AND doc_type = #{docType}
        ORDER BY timestamp DESC
    </select>

    <!-- 根据项目ID和提交ID查询文档版本列表 -->
    <select id="selectByProjectIdAndCommitId" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM document_version
        WHERE project_id = #{projectId} AND commit_id = #{commitId}
        ORDER BY timestamp DESC
    </select>

    <!-- 根据项目ID和版本标签查询文档版本 -->
    <select id="selectByProjectIdAndVersionTag" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM document_version
        WHERE project_id = #{projectId} AND version_tag = #{versionTag}
        LIMIT 1
    </select>

    <!-- 查询项目的最新文档版本 -->
    <select id="selectLatestByProjectIdAndDocType" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List" />
        FROM document_version
        WHERE project_id = #{projectId} AND doc_type = #{docType}
        ORDER BY timestamp DESC
        LIMIT 1
    </select>

    <!-- 删除文档版本 -->
    <delete id="deleteById">
        DELETE FROM document_version
        WHERE id = #{id}
    </delete>

    <!-- 删除项目的所有文档版本 -->
    <delete id="deleteByProjectId">
        DELETE FROM document_version
        WHERE project_id = #{projectId}
    </delete>

    <!-- 统计项目的文档版本数量 -->
    <select id="countByProjectId" resultType="long">
        SELECT COUNT(*)
        FROM document_version
        WHERE project_id = #{projectId}
    </select>
</mapper>

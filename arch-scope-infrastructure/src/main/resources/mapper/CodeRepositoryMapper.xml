<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.mapper.CodeRepositoryMapper">
    <resultMap id="BaseResultMap" type="com.archscope.domain.entity.CodeRepository">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="url" property="url" />
        <result column="username" property="username" />
        <result column="password" property="password" />
        <result column="access_token" property="accessToken" />
        <result column="type" property="type" />
        <result column="default_branch" property="defaultBranch" />
        <result column="created_at" property="createdAt" />
        <result column="last_synced_at" property="lastSyncedAt" />
        <result column="status" property="status" />
        <result column="ssh_enabled" property="sshEnabled" />
        <result column="ssh_key" property="sshKey" />
        <result column="ssh_key_passphrase" property="sshKeyPassphrase" />
        <result column="project_id" property="projectId" />
    </resultMap>
    
    <select id="selectByProjectId" resultMap="BaseResultMap">
        SELECT * FROM code_repositories 
        WHERE project_id = #{projectId}
    </select>
    
    <select id="selectByUrl" resultMap="BaseResultMap">
        SELECT * FROM code_repositories 
        WHERE url = #{url}
    </select>
</mapper>

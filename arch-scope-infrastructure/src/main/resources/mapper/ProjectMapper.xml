<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.mapper.ProjectMapper">
    <resultMap id="BaseResultMap" type="com.archscope.domain.entity.Project">
        <id column="id" property="id" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="repository_url" property="repositoryUrl" />
        <result column="normalized_repository_url" property="normalizedRepositoryUrl" />
        <result column="branch" property="branch" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
        <result column="last_analyzed_at" property="lastAnalyzedAt" />
        <result column="creator_id" property="creatorId" />
        <result column="status" property="status" />
        <result column="active" property="active" />
        <result column="documentation_path" property="documentationPath" />
        <result column="analysis_count" property="analysisCount" />
        <result column="documentation_version" property="documentationVersion" />
        <result column="rating" property="rating" />
        <result column="lines_of_code" property="linesOfCode" />
        <result column="file_count" property="fileCount" />
        <result column="contributor_count" property="contributorCount" />
        <result column="icon" property="icon" />
        <result column="type" property="type"
            typeHandler="com.archscope.infrastructure.config.typehandler.ProjectTypeHandler" />
        <result column="latest_analyzed_commit_id" property="latestAnalyzedCommitId" />
        <!-- 新增字段映射 -->
        <result column="member_ids" property="memberIds"
            typeHandler="com.archscope.infrastructure.config.typehandler.LongListTypeHandler" />
        <result column="configuration" property="configuration"
            typeHandler="com.archscope.infrastructure.config.typehandler.ProjectConfigurationTypeHandler" />
        <result column="document_ids" property="documentIds"
            typeHandler="com.archscope.infrastructure.config.typehandler.LongListTypeHandler" />
        <result column="task_ids" property="taskIds"
            typeHandler="com.archscope.infrastructure.config.typehandler.LongListTypeHandler" />
    </resultMap>

    <!-- 基础列 -->
    <sql id="Base_Column_List"> id, name, description, repository_url, normalized_repository_url,
        branch, created_at, updated_at, last_analyzed_at, creator_id, status, active,
        documentation_path, analysis_count, documentation_version, rating, lines_of_code,
        file_count, contributor_count, icon, type, latest_analyzed_commit_id, member_ids,
        configuration, document_ids, task_ids </sql>

    <!-- 插入项目 -->
    <insert id="insert" parameterType="com.archscope.domain.entity.Project" useGeneratedKeys="true"
        keyProperty="id"> INSERT INTO project ( name, description, repository_url,
        normalized_repository_url, branch, created_at, updated_at, last_analyzed_at, creator_id,
        status, active, documentation_path, analysis_count, documentation_version, rating,
        lines_of_code, file_count, contributor_count, icon, type, latest_analyzed_commit_id,
        member_ids, configuration, document_ids, task_ids ) VALUES ( #{name}, #{description},
        #{repositoryUrl}, #{normalizedRepositoryUrl}, #{branch}, #{createdAt}, #{updatedAt},
        #{lastAnalyzedAt}, #{creatorId}, #{status}, #{active}, #{documentationPath},
        #{analysisCount}, #{documentationVersion}, #{rating}, #{linesOfCode}, #{fileCount},
        #{contributorCount}, #{icon},
        #{type,typeHandler=com.archscope.infrastructure.config.typehandler.ProjectTypeHandler},
        #{latestAnalyzedCommitId},
        #{memberIds,typeHandler=com.archscope.infrastructure.config.typehandler.LongListTypeHandler},
        #{configuration,typeHandler=com.archscope.infrastructure.config.typehandler.ProjectConfigurationTypeHandler},
        #{documentIds,typeHandler=com.archscope.infrastructure.config.typehandler.LongListTypeHandler},
        #{taskIds,typeHandler=com.archscope.infrastructure.config.typehandler.LongListTypeHandler} ) </insert>

    <!-- 更新项目 -->
    <update id="updateById" parameterType="com.archscope.domain.entity.Project"> UPDATE project SET
        name = #{name}, description = #{description}, repository_url = #{repositoryUrl},
        normalized_repository_url = #{normalizedRepositoryUrl}, branch = #{branch}, updated_at =
        #{updatedAt}, last_analyzed_at = #{lastAnalyzedAt}, creator_id = #{creatorId}, status =
        #{status}, active = #{active}, documentation_path = #{documentationPath}, analysis_count =
        #{analysisCount}, documentation_version = #{documentationVersion}, rating = #{rating},
        lines_of_code = #{linesOfCode}, file_count = #{fileCount}, contributor_count =
        #{contributorCount}, icon = #{icon}, type =
        #{type,typeHandler=com.archscope.infrastructure.config.typehandler.ProjectTypeHandler},
        latest_analyzed_commit_id = #{latestAnalyzedCommitId}, member_ids =
        #{memberIds,typeHandler=com.archscope.infrastructure.config.typehandler.LongListTypeHandler},
        configuration =
        #{configuration,typeHandler=com.archscope.infrastructure.config.typehandler.ProjectConfigurationTypeHandler},
        document_ids =
        #{documentIds,typeHandler=com.archscope.infrastructure.config.typehandler.LongListTypeHandler},
        task_ids =
        #{taskIds,typeHandler=com.archscope.infrastructure.config.typehandler.LongListTypeHandler}
        WHERE id = #{id} </update>

    <!-- 根据ID查询项目 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap"> SELECT <include
            refid="Base_Column_List" /> FROM project WHERE id = #{id} </select>

    <!-- 根据ID删除项目 -->
    <delete id="deleteById" parameterType="java.lang.Long"> DELETE FROM project WHERE id = #{id} </delete>

    <!-- 根据标准化仓库URL查找项目 -->
    <select id="findByNormalizedRepositoryUrl" parameterType="java.lang.String"
        resultMap="BaseResultMap"> SELECT <include refid="Base_Column_List" /> FROM project WHERE
        normalized_repository_url = #{normalizedRepositoryUrl} AND active = true </select>

    <!-- 检查标准化仓库URL是否存在 -->
    <select id="existsByNormalizedRepositoryUrl" parameterType="java.lang.String"
        resultType="java.lang.Boolean"> SELECT COUNT(1) > 0 FROM project WHERE
        normalized_repository_url = #{normalizedRepositoryUrl} AND active = true </select>
</mapper>
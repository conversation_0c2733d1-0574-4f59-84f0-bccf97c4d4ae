<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.persistence.mapper.RequirementMapper">

    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.archscope.infrastructure.persistence.entity.RequirementDO">
        <id column="requirement_id" property="requirementId" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="submitted_by" property="submittedBy" />
        <result column="related_service_id" property="relatedServiceId" />
        <result column="priority" property="priority" />
        <result column="status" property="status" />
        <result column="feedback" property="feedback" />
        <result column="created_from_suggestion" property="createdFromSuggestion" />
        <result column="original_suggestion_id" property="originalSuggestionId" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 带JSON字段的结果映射 -->
    <resultMap id="FullResultMap" type="com.archscope.infrastructure.persistence.entity.RequirementDO" extends="BaseResultMap">
        <result column="tags" property="tags" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <!-- 复杂查询：根据多个条件查询需求 -->
    <select id="findRequirementsByMultipleCriteria" resultMap="FullResultMap">
        SELECT * FROM requirements
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="description != null and description != ''">
                AND description LIKE CONCAT('%', #{description}, '%')
            </if>
            <if test="submittedBy != null and submittedBy != ''">
                AND submitted_by = #{submittedBy}
            </if>
            <if test="relatedServiceId != null and relatedServiceId != ''">
                AND related_service_id = #{relatedServiceId}
            </if>
            <if test="priority != null and priority != ''">
                AND priority = #{priority}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="createdFromSuggestion != null">
                AND created_from_suggestion = #{createdFromSuggestion}
            </if>
            <if test="tag != null and tag != ''">
                AND JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ${orderBy}
            </when>
            <otherwise>
                created_at DESC
            </otherwise>
        </choose>
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：根据多个标签查询需求 -->
    <select id="findRequirementsByTags" resultMap="FullResultMap">
        SELECT * FROM requirements
        <where>
            <foreach collection="tags" item="tag" separator=" AND ">
                JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))
            </foreach>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 复杂查询：查询需求及其关联的能力 -->
    <select id="findRequirementsWithCapabilities" resultMap="FullResultMap">
        SELECT DISTINCT r.* 
        FROM requirements r
        INNER JOIN requirement_capabilities rc ON r.requirement_id = rc.requirement_id
        <where>
            <if test="capabilityName != null and capabilityName != ''">
                AND rc.capability_name = #{capabilityName}
            </if>
            <if test="status != null and status != ''">
                AND r.status = #{status}
            </if>
        </where>
        ORDER BY r.created_at DESC
    </select>

    <!-- 复杂查询：查询需求及其关联的服务信息 -->
    <select id="findRequirementsWithServiceInfo" resultType="java.util.Map">
        SELECT r.*, s.name as service_name, s.version as service_version
        FROM requirements r
        LEFT JOIN services s ON r.related_service_id = s.service_id
        <where>
            <if test="status != null and status != ''">
                AND r.status = #{status}
            </if>
        </where>
        ORDER BY r.created_at DESC
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：查询需求数量统计 -->
    <select id="countRequirementsByStatus" resultType="java.util.Map">
        SELECT status, COUNT(*) as count
        FROM requirements
        GROUP BY status
    </select>
</mapper>
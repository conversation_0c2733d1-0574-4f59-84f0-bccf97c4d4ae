<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.persistence.mapper.CapabilityMapper">

    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.archscope.infrastructure.persistence.entity.CapabilityDO">
        <id column="capability_id" property="capabilityId" />
        <result column="service_id" property="serviceId" />
        <result column="name" property="name" />
        <result column="description" property="description" />
        <result column="function_signature" property="functionSignature" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 带JSON字段的结果映射 -->
    <resultMap id="FullResultMap" type="com.archscope.infrastructure.persistence.entity.CapabilityDO" extends="BaseResultMap">
        <result column="input_schema" property="inputSchema" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="output_schema" property="outputSchema" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="examples" property="examples" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
        <result column="tags" property="tags" typeHandler="com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler" />
    </resultMap>

    <!-- 复杂查询：根据多个条件查询能力 -->
    <select id="findCapabilitiesByMultipleCriteria" resultMap="FullResultMap">
        SELECT * FROM capabilities
        <where>
            <if test="serviceId != null and serviceId != ''">
                AND service_id = #{serviceId}
            </if>
            <if test="name != null and name != ''">
                AND name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="description != null and description != ''">
                AND description LIKE CONCAT('%', #{description}, '%')
            </if>
            <if test="tag != null and tag != ''">
                AND JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ${orderBy}
            </when>
            <otherwise>
                created_at DESC
            </otherwise>
        </choose>
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：根据多个标签查询能力 -->
    <select id="findCapabilitiesByTags" resultMap="FullResultMap">
        SELECT * FROM capabilities
        <where>
            <foreach collection="tags" item="tag" separator=" AND ">
                JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))
            </foreach>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 复杂查询：根据描述关键词查询能力 -->
    <select id="findCapabilitiesByDescriptionKeywords" resultMap="FullResultMap">
        SELECT * FROM capabilities
        <where>
            <foreach collection="keywords" item="keyword" separator=" OR ">
                description LIKE CONCAT('%', #{keyword}, '%')
            </foreach>
        </where>
        ORDER BY created_at DESC
    </select>

    <!-- 复杂查询：查询能力及其所属服务信息 -->
    <select id="findCapabilitiesWithServiceInfo" resultType="java.util.Map">
        SELECT c.*, s.name as service_name, s.version as service_version, s.type as service_type
        FROM capabilities c
        INNER JOIN services s ON c.service_id = s.service_id
        <where>
            <if test="name != null and name != ''">
                AND c.name LIKE CONCAT('%', #{name}, '%')
            </if>
            <if test="serviceType != null and serviceType != ''">
                AND s.type = #{serviceType}
            </if>
        </where>
        ORDER BY c.created_at DESC
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：查询能力数量统计 -->
    <select id="countCapabilitiesByService" resultType="java.util.Map">
        SELECT s.name as service_name, COUNT(c.capability_id) as capability_count
        FROM services s
        LEFT JOIN capabilities c ON s.service_id = c.service_id
        GROUP BY s.service_id, s.name
        ORDER BY capability_count DESC
    </select>
</mapper>
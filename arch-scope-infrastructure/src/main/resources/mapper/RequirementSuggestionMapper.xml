<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.archscope.infrastructure.persistence.mapper.RequirementSuggestionMapper">

    <!-- 基本结果映射 -->
    <resultMap id="BaseResultMap" type="com.archscope.infrastructure.persistence.entity.RequirementSuggestionDO">
        <id column="suggestion_id" property="suggestionId" />
        <result column="title" property="title" />
        <result column="description" property="description" />
        <result column="suggested_priority" property="suggestedPriority" />
        <result column="confidence" property="confidence" />
        <result column="query_frequency" property="queryFrequency" />
        <result column="status" property="status" />
        <result column="converted_requirement_id" property="convertedRequirementId" />
        <result column="created_at" property="createdAt" />
        <result column="updated_at" property="updatedAt" />
    </resultMap>

    <!-- 复杂查询：根据多个条件查询需求建议 -->
    <select id="findSuggestionsByMultipleCriteria" resultMap="BaseResultMap">
        SELECT * FROM requirement_suggestions
        <where>
            <if test="title != null and title != ''">
                AND title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="description != null and description != ''">
                AND description LIKE CONCAT('%', #{description}, '%')
            </if>
            <if test="suggestedPriority != null and suggestedPriority != ''">
                AND suggested_priority = #{suggestedPriority}
            </if>
            <if test="minConfidence != null">
                AND confidence >= #{minConfidence}
            </if>
            <if test="minQueryFrequency != null">
                AND query_frequency >= #{minQueryFrequency}
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="convertedRequirementId != null and convertedRequirementId != ''">
                AND converted_requirement_id = #{convertedRequirementId}
            </if>
        </where>
        ORDER BY 
        <choose>
            <when test="orderBy != null and orderBy != ''">
                ${orderBy}
            </when>
            <otherwise>
                confidence DESC, query_frequency DESC
            </otherwise>
        </choose>
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：查询需求建议及其关联的需求信息 -->
    <select id="findSuggestionsWithRequirementInfo" resultType="java.util.Map">
        SELECT rs.*, r.title as requirement_title, r.status as requirement_status
        FROM requirement_suggestions rs
        LEFT JOIN requirements r ON rs.converted_requirement_id = r.requirement_id
        <where>
            <if test="status != null and status != ''">
                AND rs.status = #{status}
            </if>
        </where>
        ORDER BY rs.confidence DESC, rs.query_frequency DESC
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：查询需求建议及其关联的查询日志信息 -->
    <select id="findSuggestionsWithQueryLogInfo" resultType="java.util.Map">
        SELECT rs.*, COUNT(qlsr.query_log_id) as related_query_count, AVG(qlsr.relevance_score) as avg_relevance_score
        FROM requirement_suggestions rs
        INNER JOIN query_log_suggestion_relations qlsr ON rs.suggestion_id = qlsr.suggestion_id
        <where>
            <if test="status != null and status != ''">
                AND rs.status = #{status}
            </if>
            <if test="minRelevanceScore != null">
                AND qlsr.relevance_score >= #{minRelevanceScore}
            </if>
        </where>
        GROUP BY rs.suggestion_id
        ORDER BY rs.confidence DESC, rs.query_frequency DESC
        <if test="limit > 0">
            LIMIT #{limit}
            <if test="offset >= 0">
                OFFSET #{offset}
            </if>
        </if>
    </select>

    <!-- 复杂查询：查询需求建议统计信息 -->
    <select id="getSuggestionStatistics" resultType="java.util.Map">
        SELECT 
            status,
            COUNT(*) as total_suggestions,
            AVG(confidence) as avg_confidence,
            AVG(query_frequency) as avg_query_frequency,
            SUM(CASE WHEN status = 'CONVERTED' THEN 1 ELSE 0 END) as converted_count,
            SUM(CASE WHEN status = 'DISMISSED' THEN 1 ELSE 0 END) as dismissed_count
        FROM requirement_suggestions
        GROUP BY status
    </select>
</mapper>
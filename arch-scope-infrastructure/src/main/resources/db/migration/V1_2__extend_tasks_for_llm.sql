-- 扩展任务表以支持LLM任务管理
-- 版本: V1.2
-- 描述: 添加LLM任务管理所需的字段，支持任务拉取、交付和超时机制

-- 添加新的字段到现有tasks表
ALTER TABLE tasks
ADD COLUMN worker_id VARCHAR(100) COMMENT 'LLM工作节点ID',
ADD COLUMN worker_type VARCHAR(50) COMMENT 'LLM工作节点类型',
ADD COLUMN processing_started_at DATETIME COMMENT '任务开始处理时间',
ADD COLUMN timeout_at DATETIME COMMENT '任务超时时间',
ADD COLUMN overall_status VARCHAR(30) COMMENT '任务整体状态(COMPLETED/FAILED/PARTIAL_SUCCESS)',
ADD COLUMN commit_id VARCHAR(40) COMMENT '关联的Git提交ID',
ADD COLUMN results JSON COMMENT '任务结果数组(支持多文档类型)',
ADD COLUMN execution_time_ms BIGINT COMMENT '任务执行时间(毫秒)',
ADD COLUMN retry_count INT DEFAULT 0 COMMENT '重试次数',
ADD COLUMN max_retries INT DEFAULT 3 COMMENT '最大重试次数',
ADD COLUMN last_error_detail TEXT COMMENT '最后一次错误详情',
ADD COLUMN task_version VARCHAR(10) DEFAULT '1.0' COMMENT '任务版本号';

-- 添加索引以提高查询性能
CREATE INDEX idx_tasks_status_priority ON tasks(status, priority);
CREATE INDEX idx_tasks_worker_id ON tasks(worker_id);
CREATE INDEX idx_tasks_timeout_at ON tasks(timeout_at);
CREATE INDEX idx_tasks_processing_started_at ON tasks(processing_started_at);
CREATE INDEX idx_tasks_overall_status ON tasks(overall_status);

-- 更新现有任务的状态约束，支持新的状态值
-- 注意：这里假设使用的是MySQL，如果是其他数据库需要调整语法
-- MySQL不支持DROP CONSTRAINT IF EXISTS，先尝试删除约束（可能失败但不影响）
ALTER TABLE tasks ADD CONSTRAINT chk_tasks_status
CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'PARTIAL_SUCCESS', 'CANCELLED', 'WAITING', 'PAUSED'));

-- 数据迁移：将现有的IN_PROGRESS状态更新为PROCESSING
UPDATE tasks SET status = 'PROCESSING' WHERE status = 'IN_PROGRESS';

-- 添加overall_status约束
ALTER TABLE tasks ADD CONSTRAINT chk_tasks_overall_status 
CHECK (overall_status IS NULL OR overall_status IN ('COMPLETED', 'FAILED', 'PARTIAL_SUCCESS'));

-- 添加超时时间约束：超时时间应该大于创建时间
ALTER TABLE tasks ADD CONSTRAINT chk_tasks_timeout_valid 
CHECK (timeout_at IS NULL OR timeout_at > created_at);

-- 添加处理开始时间约束：处理开始时间应该大于等于创建时间
ALTER TABLE tasks ADD CONSTRAINT chk_tasks_processing_time_valid 
CHECK (processing_started_at IS NULL OR processing_started_at >= created_at);

-- 创建LLM任务结果表（用于存储详细的文档生成结果）
CREATE TABLE IF NOT EXISTS llm_task_results (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    task_id BIGINT NOT NULL COMMENT '关联的任务ID',
    document_type VARCHAR(50) NOT NULL COMMENT '文档类型(README/ARCHITECTURE/API等)',
    document_title VARCHAR(255) COMMENT '文档标题',
    document_content LONGTEXT COMMENT '文档内容(Markdown格式)',
    file_path VARCHAR(500) COMMENT '文档文件路径',
    generation_status VARCHAR(30) DEFAULT 'SUCCESS' COMMENT '生成状态',
    error_message TEXT COMMENT '生成错误信息',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_llm_results_task_id FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    
    -- 索引
    INDEX idx_llm_results_task_id (task_id),
    INDEX idx_llm_results_document_type (document_type),
    INDEX idx_llm_results_status (generation_status)
) COMMENT='LLM任务结果详情表';

-- 创建LLM工作节点状态表（用于跟踪工作节点状态）
CREATE TABLE IF NOT EXISTS llm_worker_nodes (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    worker_id VARCHAR(100) NOT NULL UNIQUE COMMENT '工作节点唯一标识',
    worker_type VARCHAR(50) NOT NULL COMMENT '工作节点类型',
    status VARCHAR(30) NOT NULL DEFAULT 'ACTIVE' COMMENT '节点状态(ACTIVE/INACTIVE/MAINTENANCE)',
    last_heartbeat DATETIME COMMENT '最后心跳时间',
    current_task_id BIGINT COMMENT '当前处理的任务ID',
    total_tasks_processed INT DEFAULT 0 COMMENT '总处理任务数',
    success_tasks_count INT DEFAULT 0 COMMENT '成功任务数',
    failed_tasks_count INT DEFAULT 0 COMMENT '失败任务数',
    average_processing_time_ms BIGINT COMMENT '平均处理时间(毫秒)',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    -- 外键约束
    CONSTRAINT fk_worker_current_task FOREIGN KEY (current_task_id) REFERENCES tasks(id) ON DELETE SET NULL,
    
    -- 索引
    INDEX idx_worker_status (status),
    INDEX idx_worker_type (worker_type),
    INDEX idx_worker_heartbeat (last_heartbeat)
) COMMENT='LLM工作节点状态表';

-- 插入默认的工作节点类型配置
INSERT IGNORE INTO llm_worker_nodes (worker_id, worker_type, status) 
VALUES ('default-worker', 'CODE_ANALYSIS', 'ACTIVE');

-- 更新现有任务的默认值
UPDATE tasks 
SET 
    retry_count = COALESCE(retry_count, 0),
    max_retries = COALESCE(max_retries, 3),
    task_version = COALESCE(task_version, '1.0')
WHERE retry_count IS NULL OR max_retries IS NULL OR task_version IS NULL;

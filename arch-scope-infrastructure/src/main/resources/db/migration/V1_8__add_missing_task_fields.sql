-- 添加Task实体中缺失的数据库字段
-- 版本: V1.6
-- 描述: 为tasks表添加Task实体中定义但数据库中缺失的字段

-- 添加缺失的字段
ALTER TABLE tasks 
ADD COLUMN detailed_status VARCHAR(255) COMMENT '详细状态描述',
ADD COLUMN error_log TEXT COMMENT '错误日志详情',
ADD COLUMN execution_time BIGINT COMMENT '执行时间(毫秒,兼容字段)',
ADD COLUMN user_id BIGINT COMMENT '创建用户ID',
ADD COLUMN assignee_id BIGINT COMMENT '分配用户ID',
ADD COLUMN result TEXT COMMENT '任务结果';

-- 数据迁移：将现有字段值映射到新字段
-- 将created_by映射到user_id（如果是数字格式）
UPDATE tasks 
SET user_id = CASE 
    WHEN created_by REGEXP '^[0-9]+$' THEN CAST(created_by AS UNSIGNED)
    ELSE NULL 
END
WHERE created_by IS NOT NULL;

-- 将assigned_to映射到assignee_id（如果是数字格式）
UPDATE tasks 
SET assignee_id = CASE 
    WHEN assigned_to REGEXP '^[0-9]+$' THEN CAST(assigned_to AS UNSIGNED)
    ELSE NULL 
END
WHERE assigned_to IS NOT NULL;

-- 将result_summary映射到result
UPDATE tasks 
SET result = result_summary
WHERE result_summary IS NOT NULL;

-- 将execution_time_ms映射到execution_time（保持兼容性）
UPDATE tasks 
SET execution_time = execution_time_ms
WHERE execution_time_ms IS NOT NULL;

-- 添加索引以提高查询性能
CREATE INDEX idx_tasks_user_id ON tasks(user_id);
CREATE INDEX idx_tasks_assignee_id ON tasks(assignee_id);
CREATE INDEX idx_tasks_detailed_status ON tasks(detailed_status);

-- 添加外键约束（如果存在用户表的话，这里先注释掉）
-- ALTER TABLE tasks ADD CONSTRAINT fk_tasks_user_id 
-- FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE SET NULL;

-- ALTER TABLE tasks ADD CONSTRAINT fk_tasks_assignee_id 
-- FOREIGN KEY (assignee_id) REFERENCES users(id) ON DELETE SET NULL;

-- 添加约束检查
ALTER TABLE tasks ADD CONSTRAINT chk_tasks_execution_time_positive 
CHECK (execution_time IS NULL OR execution_time >= 0);

ALTER TABLE tasks ADD CONSTRAINT chk_tasks_user_id_positive 
CHECK (user_id IS NULL OR user_id > 0);

ALTER TABLE tasks ADD CONSTRAINT chk_tasks_assignee_id_positive 
CHECK (assignee_id IS NULL OR assignee_id > 0);

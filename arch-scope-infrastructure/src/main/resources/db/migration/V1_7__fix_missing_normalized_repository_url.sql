-- 修复缺失的标准化仓库URL数据
-- 版本: V1.7
-- 描述: 修复现有项目中缺失的normalized_repository_url字段值

-- 1. 检查当前有多少项目的normalized_repository_url为空
SELECT 
    COUNT(*) as total_projects,
    COUNT(normalized_repository_url) as projects_with_normalized_url,
    COUNT(*) - COUNT(normalized_repository_url) as projects_missing_normalized_url
FROM project;

-- 2. 显示缺失标准化URL的项目
SELECT 
    id, 
    name, 
    repository_url, 
    normalized_repository_url,
    created_at
FROM project 
WHERE normalized_repository_url IS NULL OR normalized_repository_url = ''
ORDER BY created_at DESC;

-- 3. 更新缺失的标准化URL
-- 注意：这个更新操作会处理所有格式的Git URL

UPDATE project 
SET normalized_repository_url = CASE
    -- 处理HTTPS URL，移除.git后缀和尾部斜杠，转为小写
    WHEN repository_url REGEXP '^https?://' THEN
        LOWER(REGEXP_REPLACE(
            REGEXP_REPLACE(repository_url, '\\.git/?$', ''),
            '/$', ''
        ))
    -- 处理SSH URL，转换为HTTPS格式
    WHEN repository_url REGEXP '^git@' THEN
        LOWER(CONCAT(
            'https://',
            SUBSTRING_INDEX(SUBSTRING_INDEX(repository_url, '@', -1), ':', 1),
            '/',
            REGEXP_REPLACE(
                SUBSTRING_INDEX(repository_url, ':', -1),
                '\\.git/?$', ''
            )
        ))
    -- 处理其他格式，直接转为小写并移除.git后缀
    ELSE 
        LOWER(REGEXP_REPLACE(
            REGEXP_REPLACE(repository_url, '\\.git/?$', ''),
            '/$', ''
        ))
END
WHERE normalized_repository_url IS NULL OR normalized_repository_url = '';

-- 4. 验证更新结果
SELECT 
    COUNT(*) as total_projects,
    COUNT(normalized_repository_url) as projects_with_normalized_url,
    COUNT(*) - COUNT(normalized_repository_url) as projects_still_missing_normalized_url
FROM project;

-- 5. 显示更新后的数据样本
SELECT 
    id, 
    name, 
    repository_url, 
    normalized_repository_url,
    created_at
FROM project 
ORDER BY updated_at DESC 
LIMIT 10;

-- 6. 检查是否有重复的标准化URL（这可能表明有重复的仓库）
SELECT 
    normalized_repository_url, 
    COUNT(*) as count,
    GROUP_CONCAT(CONCAT(id, ':', name) SEPARATOR '; ') as projects
FROM project 
WHERE normalized_repository_url IS NOT NULL
GROUP BY normalized_repository_url 
HAVING COUNT(*) > 1
ORDER BY count DESC;

-- 7. 如果发现重复，可以使用以下查询查看详细信息
/*
SELECT 
    p1.id,
    p1.name,
    p1.repository_url,
    p1.normalized_repository_url,
    p1.created_at,
    p1.status
FROM project p1
INNER JOIN (
    SELECT normalized_repository_url
    FROM project 
    WHERE normalized_repository_url IS NOT NULL
    GROUP BY normalized_repository_url 
    HAVING COUNT(*) > 1
) p2 ON p1.normalized_repository_url = p2.normalized_repository_url
ORDER BY p1.normalized_repository_url, p1.created_at;
*/

-- 8. 创建备份表（可选，用于回滚）
/*
CREATE TABLE project_backup_before_v1_7 AS 
SELECT * FROM project WHERE normalized_repository_url IS NULL OR normalized_repository_url = '';
*/

-- 9. 验证数据完整性
-- 确保所有项目都有有效的标准化URL
SELECT 
    id,
    name,
    repository_url,
    normalized_repository_url
FROM project 
WHERE normalized_repository_url IS NULL 
   OR normalized_repository_url = ''
   OR LENGTH(TRIM(normalized_repository_url)) = 0;

-- 10. 性能优化：更新统计信息
-- ANALYZE TABLE project;

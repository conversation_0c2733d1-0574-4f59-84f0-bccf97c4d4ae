-- 服务表
CREATE TABLE services (
  service_id VARCHAR(36) NOT NULL COMMENT '服务唯一标识符 (UUID)',
  name VARCHAR(64) NOT NULL COMMENT '服务名称',
  description VARCHAR(1024) COMMENT '服务描述',
  version VARCHAR(32) COMMENT '服务版本',
  type VARCHAR(32) COMMENT '服务类型',
  endpoint VARCHAR(255) COMMENT '服务端点URL',
  group_id VARCHAR(100) COMMENT 'Maven/Gradle坐标 - groupId',
  artifact_id VARCHAR(100) COMMENT 'Maven/Gradle坐标 - artifactId',
  tags JSON COMMENT '服务标签 (JSON数组)',
  owner VARCHAR(64) COMMENT '服务所有者',
  api_doc_url VARCHAR(255) COMMENT 'API文档URL',
  status VARCHAR(16) NOT NULL DEFAULT 'ACTIVE' COMMENT '服务状态: ACTIVE, INACTIVE, DEPRECATED, MAINTENANCE, UNKNOWN',
  metadata JSON COMMENT '元数据 (JSO<PERSON>对象)',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  updated_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (service_id),
  INDEX idx_services_name (name),
  INDEX idx_services_type (type),
  INDEX idx_services_status (status),
  INDEX idx_services_group_id (group_id),
  INDEX idx_services_artifact_id (artifact_id),
  INDEX idx_services_maven_coordinates (group_id, artifact_id, version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务注册表';

-- 能力表
CREATE TABLE capabilities (
  capability_id VARCHAR(36) NOT NULL COMMENT '能力唯一标识符 (UUID)',
  service_id VARCHAR(36) NOT NULL COMMENT '关联的服务ID',
  name VARCHAR(64) NOT NULL COMMENT '能力名称',
  description VARCHAR(1024) COMMENT '能力描述',
  function_signature TEXT COMMENT '函数签名',
  input_schema JSON COMMENT '输入参数JSON Schema',
  output_schema JSON COMMENT '输出参数JSON Schema',
  examples JSON COMMENT '示例 (JSON数组)',
  tags JSON COMMENT '能力标签 (JSON数组)',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  updated_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (capability_id),
  INDEX idx_capabilities_service_id (service_id),
  INDEX idx_capabilities_name (name),
  CONSTRAINT fk_capabilities_service_id FOREIGN KEY (service_id) REFERENCES services(service_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务能力表';

-- 需求表
CREATE TABLE requirements (
  requirement_id VARCHAR(36) NOT NULL COMMENT '需求唯一标识符 (UUID)',
  title VARCHAR(128) NOT NULL COMMENT '需求标题',
  description TEXT NOT NULL COMMENT '需求详细描述',
  submitted_by VARCHAR(64) COMMENT '提交者',
  related_service_id VARCHAR(36) COMMENT '相关服务ID',
  priority VARCHAR(16) NOT NULL DEFAULT 'MEDIUM' COMMENT '优先级: HIGH, MEDIUM, LOW',
  status VARCHAR(16) NOT NULL DEFAULT 'NEW' COMMENT '状态: NEW, UNDER_REVIEW, ACCEPTED, REJECTED, IMPLEMENTED',
  feedback TEXT COMMENT '反馈',
  tags JSON COMMENT '标签 (JSON数组)',
  created_from_suggestion BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否从建议转换而来',
  original_suggestion_id VARCHAR(36) COMMENT '原始建议ID',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  updated_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (requirement_id),
  INDEX idx_requirements_status (status),
  INDEX idx_requirements_priority (priority),
  INDEX idx_requirements_related_service_id (related_service_id),
  INDEX idx_requirements_original_suggestion_id (original_suggestion_id),
  CONSTRAINT fk_requirements_related_service_id FOREIGN KEY (related_service_id) REFERENCES services(service_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求表';

-- 查询日志表
CREATE TABLE query_logs (
  query_log_id VARCHAR(36) NOT NULL COMMENT '查询日志唯一标识符 (UUID)',
  query_type VARCHAR(32) NOT NULL COMMENT '查询类型: SERVICE_DISCOVERY, CAPABILITY_QUERY',
  query_params JSON NOT NULL COMMENT '查询参数 (JSON对象)',
  result_count INT NOT NULL DEFAULT 0 COMMENT '结果数量',
  has_results BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否有结果',
  session_id VARCHAR(64) COMMENT '会话ID',
  user_id VARCHAR(64) COMMENT '用户ID',
  user_feedback VARCHAR(16) DEFAULT 'NONE' COMMENT '用户反馈: SATISFIED, UNSATISFIED, NONE',
  converted_to_requirement BOOLEAN NOT NULL DEFAULT FALSE COMMENT '是否已转换为需求',
  related_requirement_id VARCHAR(36) COMMENT '关联的需求ID',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  PRIMARY KEY (query_log_id),
  INDEX idx_query_logs_query_type (query_type),
  INDEX idx_query_logs_has_results (has_results),
  INDEX idx_query_logs_session_id (session_id),
  INDEX idx_query_logs_user_id (user_id),
  INDEX idx_query_logs_created_at (created_at),
  INDEX idx_query_logs_related_requirement_id (related_requirement_id),
  CONSTRAINT fk_query_logs_related_requirement_id FOREIGN KEY (related_requirement_id) REFERENCES requirements(requirement_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='查询日志表';

-- 需求建议表
CREATE TABLE requirement_suggestions (
  suggestion_id VARCHAR(36) NOT NULL COMMENT '建议唯一标识符 (UUID)',
  title VARCHAR(128) NOT NULL COMMENT '建议标题',
  description TEXT NOT NULL COMMENT '建议描述',
  suggested_priority VARCHAR(16) NOT NULL DEFAULT 'MEDIUM' COMMENT '建议优先级: HIGH, MEDIUM, LOW',
  confidence INT NOT NULL DEFAULT 0 COMMENT '置信度 (0-100)',
  query_frequency INT NOT NULL DEFAULT 0 COMMENT '相关查询频率',
  status VARCHAR(16) NOT NULL DEFAULT 'OPEN' COMMENT '状态: OPEN, CONVERTED, DISMISSED',
  converted_requirement_id VARCHAR(36) COMMENT '转换后的需求ID',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  updated_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  PRIMARY KEY (suggestion_id),
  INDEX idx_requirement_suggestions_status (status),
  INDEX idx_requirement_suggestions_confidence (confidence),
  INDEX idx_requirement_suggestions_query_frequency (query_frequency),
  INDEX idx_requirement_suggestions_converted_requirement_id (converted_requirement_id),
  CONSTRAINT fk_requirement_suggestions_converted_requirement_id FOREIGN KEY (converted_requirement_id) REFERENCES requirements(requirement_id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求建议表';

-- 查询日志与建议关联表
CREATE TABLE query_log_suggestion_relations (
  relation_id VARCHAR(36) NOT NULL COMMENT '关联唯一标识符 (UUID)',
  query_log_id VARCHAR(36) NOT NULL COMMENT '查询日志ID',
  suggestion_id VARCHAR(36) NOT NULL COMMENT '需求建议ID',
  relevance_score FLOAT NOT NULL DEFAULT 1.0 COMMENT '相关性分数 (0-1)',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  PRIMARY KEY (relation_id),
  UNIQUE KEY uk_query_log_suggestion (query_log_id, suggestion_id),
  INDEX idx_query_log_suggestion_relations_query_log_id (query_log_id),
  INDEX idx_query_log_suggestion_relations_suggestion_id (suggestion_id),
  CONSTRAINT fk_query_log_suggestion_relations_query_log_id FOREIGN KEY (query_log_id) REFERENCES query_logs(query_log_id) ON DELETE CASCADE,
  CONSTRAINT fk_query_log_suggestion_relations_suggestion_id FOREIGN KEY (suggestion_id) REFERENCES requirement_suggestions(suggestion_id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='查询日志与建议关联表';
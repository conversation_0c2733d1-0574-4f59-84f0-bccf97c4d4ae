-- 创建任务表
CREATE TABLE IF NOT EXISTS tasks (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255),
    description TEXT,
    task_type VARCHAR(50) NOT NULL,
    status VARCHAR(30) NOT NULL,
    priority INT,
    progress INT,
    created_at DATETIME NOT NULL,
    started_at DATETIME,
    completed_at DATETIME,
    error_message TEXT,
    result_summary TEXT,
    parameters JSON,
    project_id BIGINT,
    created_by VARCHAR(100),
    assigned_to VARCHAR(100),
    
    -- 索引
    INDEX idx_project_id (project_id),
    INDEX idx_created_at (created_at)
);

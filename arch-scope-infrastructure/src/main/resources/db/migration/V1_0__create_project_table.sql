-- 创建项目表
-- 版本: V1.0
-- 描述: 创建项目基础表结构

CREATE TABLE IF NOT EXISTS project (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '项目唯一标识符',
    name VARCHAR(255) NOT NULL COMMENT '项目名称',
    description TEXT COMMENT '项目描述',
    repository_url VARCHAR(512) NOT NULL COMMENT 'Git仓库URL',
    branch VARCHAR(255) DEFAULT 'main' COMMENT '分支名称',
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    last_analyzed_at DATETIME COMMENT '最后分析时间',
    creator_id BIGINT COMMENT '创建者ID',
    status VARCHAR(50) DEFAULT 'PENDING_ANALYSIS' COMMENT '项目状态',
    active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    documentation_path VARCHAR(512) COMMENT '文档路径',
    analysis_count INT DEFAULT 0 COMMENT '分析次数',
    documentation_version VARCHAR(50) COMMENT '文档版本',
    rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '项目评分',
    lines_of_code BIGINT DEFAULT 0 COMMENT '代码行数',
    file_count INT DEFAULT 0 COMMENT '文件数量',
    contributor_count INT DEFAULT 0 COMMENT '贡献者数量',
    icon VARCHAR(255) COMMENT '项目图标',
    type VARCHAR(50) DEFAULT 'JAVA' COMMENT '项目类型',
    latest_analyzed_commit_id VARCHAR(40) COMMENT '最新分析的提交ID',
    
    -- 索引
    INDEX idx_repository_url (repository_url),
    INDEX idx_created_at (created_at),
    INDEX idx_status (status),
    INDEX idx_creator_id (creator_id),
    INDEX idx_active (active),
    
    -- 唯一约束
    UNIQUE INDEX u_idx_repository_url (repository_url)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目信息表';

-- 插入示例数据（可选）
INSERT IGNORE INTO project (
    name, description, repository_url, branch, creator_id, status, type
) VALUES (
    'ArchScope Demo', 
    'ArchScope项目演示仓库', 
    'https://github.com/example/archscope-demo.git', 
    'main', 
    1, 
    'PENDING_ANALYSIS', 
    'JAVA'
);

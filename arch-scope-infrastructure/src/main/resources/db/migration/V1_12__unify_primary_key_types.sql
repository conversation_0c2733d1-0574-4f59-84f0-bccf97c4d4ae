-- 统一主键类型：将服务发现相关表的UUID主键改为BIGINT AUTO_INCREMENT
-- 版本: V1.12
-- 描述: 修复主键类型不一致问题，统一使用BIGINT AUTO_INCREMENT

-- 1. 备份现有数据（如果有的话）
CREATE TABLE IF NOT EXISTS services_backup AS SELECT * FROM services;
CREATE TABLE IF NOT EXISTS capabilities_backup AS SELECT * FROM capabilities;
CREATE TABLE IF NOT EXISTS requirements_backup AS SELECT * FROM requirements;
CREATE TABLE IF NOT EXISTS query_logs_backup AS SELECT * FROM query_logs;
CREATE TABLE IF NOT EXISTS requirement_suggestions_backup AS SELECT * FROM requirement_suggestions;

-- 2. 删除外键约束
ALTER TABLE capabilities DROP FOREIGN KEY IF EXISTS fk_capabilities_service_id;
ALTER TABLE requirements DROP FOREIGN KEY IF EXISTS fk_requirements_related_service_id;
ALTER TABLE query_logs DROP FOREIGN KEY IF EXISTS fk_query_logs_related_requirement_id;
ALTER TABLE requirement_suggestions DROP FOREIGN KEY IF EXISTS fk_requirement_suggestions_converted_requirement_id;

-- 3. 重新创建services表
DROP TABLE IF EXISTS services;
CREATE TABLE services (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '服务唯一标识符',
  name VARCHAR(64) NOT NULL COMMENT '服务名称',
  description VARCHAR(1024) COMMENT '服务描述',
  version VARCHAR(20) NOT NULL COMMENT '服务版本',
  type VARCHAR(50) NOT NULL COMMENT '服务类型',
  endpoint VARCHAR(255) NOT NULL COMMENT '服务端点URL',
  group_id VARCHAR(100) COMMENT 'Maven/Gradle坐标 - groupId',
  artifact_id VARCHAR(100) COMMENT 'Maven/Gradle坐标 - artifactId',
  tags JSON COMMENT '服务标签 (JSON数组)',
  owner VARCHAR(100) COMMENT '服务所有者',
  api_doc_url VARCHAR(255) COMMENT 'API文档URL',
  health_check_url VARCHAR(255) COMMENT '健康检查URL',
  status VARCHAR(20) NOT NULL DEFAULT 'ACTIVE' COMMENT '服务状态: ACTIVE, INACTIVE, DEPRECATED, MAINTENANCE',
  metadata JSON COMMENT '服务元数据 (JSON对象)',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  updated_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  INDEX idx_services_name (name),
  INDEX idx_services_type (type),
  INDEX idx_services_status (status),
  INDEX idx_services_group_id (group_id),
  INDEX idx_services_artifact_id (artifact_id),
  INDEX idx_services_maven_coordinates (group_id, artifact_id, version)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务注册表';

-- 4. 重新创建capabilities表
DROP TABLE IF EXISTS capabilities;
CREATE TABLE capabilities (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '能力唯一标识符',
  service_id BIGINT NOT NULL COMMENT '关联的服务ID',
  name VARCHAR(64) NOT NULL COMMENT '能力名称',
  description VARCHAR(1024) COMMENT '能力描述',
  function_signature TEXT COMMENT '函数签名',
  input_schema JSON COMMENT '输入参数JSON Schema',
  output_schema JSON COMMENT '输出参数JSON Schema',
  examples JSON COMMENT '示例 (JSON数组)',
  tags JSON COMMENT '能力标签 (JSON数组)',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  updated_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  INDEX idx_capabilities_service_id (service_id),
  INDEX idx_capabilities_name (name),
  FOREIGN KEY (service_id) REFERENCES services(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='服务能力表';

-- 5. 重新创建requirements表
DROP TABLE IF EXISTS requirements;
CREATE TABLE requirements (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '需求唯一标识符',
  title VARCHAR(255) NOT NULL COMMENT '需求标题',
  description TEXT COMMENT '需求详细描述',
  submitted_by VARCHAR(100) COMMENT '提交者',
  related_service_id BIGINT COMMENT '相关服务ID',
  priority VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '优先级: HIGH, MEDIUM, LOW',
  status VARCHAR(30) DEFAULT 'NEW' COMMENT '状态: NEW, UNDER_REVIEW, ACCEPTED, REJECTED, IMPLEMENTED',
  feedback TEXT COMMENT '反馈',
  tags JSON COMMENT '标签 (JSON数组)',
  created_from_suggestion BOOLEAN DEFAULT FALSE COMMENT '是否从建议转换而来',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  updated_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  INDEX idx_requirements_status (status),
  INDEX idx_requirements_priority (priority),
  INDEX idx_requirements_submitted_by (submitted_by),
  INDEX idx_requirements_related_service_id (related_service_id),
  FOREIGN KEY (related_service_id) REFERENCES services(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求表';

-- 6. 重新创建query_logs表
DROP TABLE IF EXISTS query_logs;
CREATE TABLE query_logs (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '查询日志唯一标识符',
  query_type VARCHAR(50) NOT NULL COMMENT '查询类型: SERVICE_DISCOVERY, CAPABILITY_QUERY',
  query_params JSON COMMENT '查询参数 (JSON对象)',
  result_count INT DEFAULT 0 COMMENT '结果数量',
  has_results BOOLEAN DEFAULT FALSE COMMENT '是否有结果',
  session_id VARCHAR(100) COMMENT '会话ID',
  user_id VARCHAR(100) COMMENT '用户ID',
  user_feedback VARCHAR(20) DEFAULT 'NONE' COMMENT '用户反馈: SATISFIED, UNSATISFIED, NONE',
  converted_to_requirement BOOLEAN DEFAULT FALSE COMMENT '是否已转换为需求',
  related_requirement_id BIGINT COMMENT '关联的需求ID',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  INDEX idx_query_logs_query_type (query_type),
  INDEX idx_query_logs_session_id (session_id),
  INDEX idx_query_logs_user_id (user_id),
  INDEX idx_query_logs_created_at (created_at),
  FOREIGN KEY (related_requirement_id) REFERENCES requirements(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='查询日志表';

-- 7. 重新创建requirement_suggestions表
DROP TABLE IF EXISTS requirement_suggestions;
CREATE TABLE requirement_suggestions (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '建议唯一标识符',
  title VARCHAR(255) NOT NULL COMMENT '建议标题',
  description TEXT COMMENT '建议描述',
  suggested_priority VARCHAR(20) DEFAULT 'MEDIUM' COMMENT '建议优先级: HIGH, MEDIUM, LOW',
  confidence INT DEFAULT 0 COMMENT '置信度 (0-100)',
  query_frequency INT DEFAULT 0 COMMENT '相关查询频率',
  status VARCHAR(20) DEFAULT 'OPEN' COMMENT '状态: OPEN, CONVERTED, DISMISSED',
  converted_requirement_id BIGINT COMMENT '转换后的需求ID',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  updated_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) ON UPDATE CURRENT_TIMESTAMP(3) COMMENT '更新时间',
  INDEX idx_requirement_suggestions_status (status),
  INDEX idx_requirement_suggestions_confidence (confidence),
  INDEX idx_requirement_suggestions_created_at (created_at),
  FOREIGN KEY (converted_requirement_id) REFERENCES requirements(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求建议表';

-- 8. 创建关联表（如果需要）
CREATE TABLE IF NOT EXISTS query_log_suggestion_relations (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联唯一标识符',
  query_log_id BIGINT NOT NULL COMMENT '查询日志ID',
  suggestion_id BIGINT NOT NULL COMMENT '建议ID',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  INDEX idx_query_log_suggestion_query_log_id (query_log_id),
  INDEX idx_query_log_suggestion_suggestion_id (suggestion_id),
  UNIQUE INDEX u_idx_query_log_suggestion (query_log_id, suggestion_id),
  FOREIGN KEY (query_log_id) REFERENCES query_logs(id) ON DELETE CASCADE,
  FOREIGN KEY (suggestion_id) REFERENCES requirement_suggestions(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='查询日志与建议关联表';

CREATE TABLE IF NOT EXISTS requirement_capabilities (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '关联唯一标识符',
  requirement_id BIGINT NOT NULL COMMENT '需求ID',
  capability_id BIGINT NOT NULL COMMENT '能力ID',
  created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '创建时间',
  INDEX idx_requirement_capabilities_requirement_id (requirement_id),
  INDEX idx_requirement_capabilities_capability_id (capability_id),
  UNIQUE INDEX u_idx_requirement_capability (requirement_id, capability_id),
  FOREIGN KEY (requirement_id) REFERENCES requirements(id) ON DELETE CASCADE,
  FOREIGN KEY (capability_id) REFERENCES capabilities(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='需求与能力关联表';

-- 9. 清理备份表（可选，生产环境建议保留一段时间）
-- DROP TABLE IF EXISTS services_backup;
-- DROP TABLE IF EXISTS capabilities_backup;
-- DROP TABLE IF EXISTS requirements_backup;
-- DROP TABLE IF EXISTS query_logs_backup;
-- DROP TABLE IF EXISTS requirement_suggestions_backup;

-- 添加Project实体中缺失的数据库字段
-- 版本: V1.9
-- 描述: 为project表添加Project实体中定义但数据库中缺失的字段

-- 添加缺失的字段
ALTER TABLE project 
ADD COLUMN member_ids JSON COMMENT '项目成员ID列表',
ADD COLUMN configuration JSON COMMENT '项目配置信息',
ADD COLUMN document_ids JSON COMMENT '相关文档ID列表',
ADD COLUMN task_ids JSON COMMENT '相关任务ID列表';

-- 为现有项目设置默认值
UPDATE project 
SET 
    member_ids = JSON_ARRAY(),
    configuration = JSON_OBJECT(
        'enableAutoAnalysis', true,
        'analysisSchedule', 'DAILY',
        'documentationLanguage', 'zh-CN',
        'enableNotifications', true,
        'visibility', 'PRIVATE'
    ),
    document_ids = JSON_ARRAY(),
    task_ids = JSON_ARRAY()
WHERE member_ids IS NULL 
   OR configuration IS NULL 
   OR document_ids IS NULL 
   OR task_ids IS NULL;

-- 添加索引以提高查询性能
-- 注意：MySQL 8.0+ 支持JSON字段的函数索引
-- 为了兼容性，这里先不添加JSON字段的索引

-- 添加约束检查
-- 确保JSON字段格式正确
ALTER TABLE project ADD CONSTRAINT chk_project_member_ids_valid 
CHECK (JSON_VALID(member_ids));

ALTER TABLE project ADD CONSTRAINT chk_project_configuration_valid 
CHECK (JSON_VALID(configuration));

ALTER TABLE project ADD CONSTRAINT chk_project_document_ids_valid 
CHECK (JSON_VALID(document_ids));

ALTER TABLE project ADD CONSTRAINT chk_project_task_ids_valid 
CHECK (JSON_VALID(task_ids));

-- 添加注释说明
ALTER TABLE project 
MODIFY COLUMN member_ids JSON COMMENT '项目成员ID列表，JSON数组格式，例如：[1,2,3]',
MODIFY COLUMN configuration JSON COMMENT '项目配置信息，JSON对象格式，包含分析设置、通知设置等',
MODIFY COLUMN document_ids JSON COMMENT '相关文档ID列表，JSON数组格式，例如：[10,20,30]',
MODIFY COLUMN task_ids JSON COMMENT '相关任务ID列表，JSON数组格式，例如：[100,200,300]';

-- V1.3 优化任务表索引以提高查询性能
-- 作者: ArchScope Team
-- 日期: 2025-06-27

-- 删除可能存在的旧索引（如果存在）
DROP INDEX IF EXISTS idx_tasks_status_priority ON tasks;
DROP INDEX IF EXISTS idx_tasks_worker_id ON tasks;
DROP INDEX IF EXISTS idx_tasks_timeout_at ON tasks;
DROP INDEX IF EXISTS idx_tasks_processing_started_at ON tasks;
DROP INDEX IF EXISTS idx_tasks_overall_status ON tasks;

-- 创建优化的复合索引
-- 1. 任务拉取优化：状态+优先级+创建时间+重试次数
CREATE INDEX idx_tasks_pull_optimization ON tasks(status, priority DESC, created_at ASC, retry_count);

-- 2. 超时任务查询优化：状态+超时时间+重试次数
CREATE INDEX idx_tasks_timeout_optimization ON tasks(status, timeout_at, retry_count);

-- 3. 工作节点查询优化：工作节点ID+状态
CREATE INDEX idx_tasks_worker_status ON tasks(worker_id, status);

-- 4. 项目任务查询优化：项目ID+状态+创建时间
CREATE INDEX idx_tasks_project_status_time ON tasks(project_id, status, created_at DESC);

-- 5. 任务类型查询优化：任务类型+状态+优先级
CREATE INDEX idx_tasks_type_status_priority ON tasks(task_type, status, priority DESC);

-- 6. 长时间运行任务查询优化：状态+处理开始时间
CREATE INDEX idx_tasks_long_running ON tasks(status, processing_started_at);

-- 7. 统计查询优化：状态+创建时间+项目ID
CREATE INDEX idx_tasks_statistics ON tasks(status, created_at, project_id);

-- 8. 任务版本优化（用于CAS操作）：ID+版本号
CREATE INDEX idx_tasks_version ON tasks(id, task_version);

-- 9. 执行时间统计优化：状态+执行时间
CREATE INDEX idx_tasks_execution_time ON tasks(status, execution_time_ms);

-- 10. 完成时间查询优化：状态+完成时间
CREATE INDEX idx_tasks_completion_time ON tasks(status, completed_at);

-- 添加表级别的优化配置（MySQL特定）
-- 设置表的存储引擎优化参数
ALTER TABLE tasks 
    ENGINE=InnoDB 
    ROW_FORMAT=DYNAMIC 
    COMMENT='任务表 - 已优化索引结构';

-- 分析表以更新统计信息
ANALYZE TABLE tasks;

-- 创建视图以简化常用查询
CREATE OR REPLACE VIEW v_pending_tasks AS
SELECT 
    id, name, description, task_type, priority, created_at,
    project_id, retry_count, max_retries
FROM tasks
WHERE status = 'PENDING' 
  AND (retry_count IS NULL OR retry_count < max_retries)
ORDER BY priority DESC, created_at ASC;

CREATE OR REPLACE VIEW v_processing_tasks AS
SELECT 
    id, name, task_type, worker_id, worker_type,
    processing_started_at, timeout_at, project_id
FROM tasks
WHERE status = 'PROCESSING';

CREATE OR REPLACE VIEW v_timeout_tasks AS
SELECT 
    id, name, task_type, worker_id, worker_type,
    processing_started_at, timeout_at, retry_count, max_retries
FROM tasks
WHERE status = 'PROCESSING'
  AND timeout_at IS NOT NULL
  AND timeout_at < NOW()
  AND (retry_count IS NULL OR retry_count < max_retries);

-- 创建存储过程用于批量操作
DELIMITER //

CREATE PROCEDURE sp_batch_reset_timeout_tasks(IN task_ids TEXT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    SET @sql = CONCAT('UPDATE tasks SET 
        status = ''PENDING'',
        worker_id = NULL,
        worker_type = NULL,
        processing_started_at = NULL,
        timeout_at = NULL,
        retry_count = retry_count + 1,
        last_error_detail = CONCAT(COALESCE(last_error_detail, ''''), ''\n['', NOW(), ''] 批量超时重置''),
        updated_at = NOW()
        WHERE id IN (', task_ids, ') AND status = ''PROCESSING''');
    
    PREPARE stmt FROM @sql;
    EXECUTE stmt;
    DEALLOCATE PREPARE stmt;
    
    COMMIT;
END //

CREATE PROCEDURE sp_cleanup_old_completed_tasks(IN days_old INT)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;
    
    DELETE FROM tasks 
    WHERE status = 'COMPLETED' 
      AND completed_at < DATE_SUB(NOW(), INTERVAL days_old DAY)
      AND completed_at IS NOT NULL;
    
    COMMIT;
END //

DELIMITER ;

-- 添加表注释说明优化内容
ALTER TABLE tasks COMMENT = '任务表 - V1.3优化版本
- 添加了10个复合索引优化查询性能
- 支持高并发任务拉取和状态更新
- 优化了超时任务处理和统计查询
- 添加了CAS操作支持防止并发冲突';

-- 合并任务状态：将IN_PROGRESS统一为PROCESSING
-- 版本: V1.3
-- 描述: 消除TaskStatus枚举中的重复状态，统一使用PROCESSING

-- 1. 数据迁移：将所有IN_PROGRESS状态的任务更新为PROCESSING
UPDATE tasks 
SET status = 'PROCESSING' 
WHERE status = 'IN_PROGRESS';

-- 2. 更新状态约束，移除IN_PROGRESS
-- MySQL不支持DROP CONSTRAINT IF EXISTS，跳过约束删除
-- ALTER TABLE tasks DROP CONSTRAINT chk_tasks_status;
-- ALTER TABLE tasks ADD CONSTRAINT chk_tasks_status
-- CHECK (status IN ('PENDING', 'PROCESSING', 'COMPLETED', 'FAILED', 'PARTIAL_SUCCESS', 'CANCELLED', 'WAITING', 'PAUSED'));

-- 为服务发现系统添加性能优化索引

-- 服务表索引
CREATE INDEX IF NOT EXISTS idx_services_name ON services(name);
CREATE INDEX IF NOT EXISTS idx_services_type ON services(type);
CREATE INDEX IF NOT EXISTS idx_services_status ON services(status);
CREATE INDEX IF NOT EXISTS idx_services_owner ON services(owner);
CREATE INDEX IF NOT EXISTS idx_services_group_artifact ON services(group_id, artifact_id);
CREATE INDEX IF NOT EXISTS idx_services_group_artifact_version ON services(group_id, artifact_id, version);
CREATE INDEX IF NOT EXISTS idx_services_endpoint ON services(endpoint);
CREATE INDEX IF NOT EXISTS idx_services_created_at ON services(created_at);
CREATE INDEX IF NOT EXISTS idx_services_updated_at ON services(updated_at);
CREATE INDEX IF NOT EXISTS idx_services_status_updated ON services(status, updated_at);
CREATE INDEX IF NOT EXISTS idx_services_type_status ON services(type, status);

-- 服务表全文搜索索引（如果支持）
-- CREATE FULLTEXT INDEX IF NOT EXISTS idx_services_fulltext ON services(name, description);

-- 能力表索引
CREATE INDEX IF NOT EXISTS idx_capabilities_service_id ON capabilities(service_id);
CREATE INDEX IF NOT EXISTS idx_capabilities_name ON capabilities(name);
CREATE INDEX IF NOT EXISTS idx_capabilities_service_name ON capabilities(service_id, name);
CREATE INDEX IF NOT EXISTS idx_capabilities_created_at ON capabilities(created_at);
CREATE INDEX IF NOT EXISTS idx_capabilities_updated_at ON capabilities(updated_at);

-- 能力表全文搜索索引（如果支持）
-- CREATE FULLTEXT INDEX IF NOT EXISTS idx_capabilities_fulltext ON capabilities(name, description);

-- 需求表索引
CREATE INDEX IF NOT EXISTS idx_requirements_priority ON requirements(priority);
CREATE INDEX IF NOT EXISTS idx_requirements_created_at ON requirements(created_at);
CREATE INDEX IF NOT EXISTS idx_requirements_updated_at ON requirements(updated_at);

-- 需求能力关联表索引
CREATE INDEX IF NOT EXISTS idx_requirement_capability_requirement_id ON requirement_capability(requirement_id);
CREATE INDEX IF NOT EXISTS idx_requirement_capability_capability_name ON requirement_capability(capability_name);

-- 查询日志表索引
CREATE INDEX IF NOT EXISTS idx_query_logs_query_text ON query_logs(query_text);
CREATE INDEX IF NOT EXISTS idx_query_logs_created_at ON query_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_query_logs_result_count ON query_logs(result_count);

-- 查询日志建议关联表索引
CREATE INDEX IF NOT EXISTS idx_query_log_suggestion_query_id ON query_log_suggestion_relation(query_log_id);
CREATE INDEX IF NOT EXISTS idx_query_log_suggestion_suggestion_id ON query_log_suggestion_relation(suggestion_id);

-- 需求建议表索引
CREATE INDEX IF NOT EXISTS idx_requirement_suggestions_requirement_text ON requirement_suggestions(requirement_text);
CREATE INDEX IF NOT EXISTS idx_requirement_suggestions_created_at ON requirement_suggestions(created_at);

-- 复合索引用于常见查询模式
CREATE INDEX IF NOT EXISTS idx_services_status_type_updated ON services(status, type, updated_at);
CREATE INDEX IF NOT EXISTS idx_capabilities_service_updated ON capabilities(service_id, updated_at);

-- JSON字段索引（MySQL 5.7+支持）
-- 为tags字段创建虚拟列和索引
-- ALTER TABLE services ADD COLUMN tags_virtual JSON GENERATED ALWAYS AS (tags) VIRTUAL;
-- CREATE INDEX IF NOT EXISTS idx_services_tags ON services((CAST(tags_virtual AS CHAR(255) ARRAY)));

-- ALTER TABLE capabilities ADD COLUMN tags_virtual JSON GENERATED ALWAYS AS (tags) VIRTUAL;
-- CREATE INDEX IF NOT EXISTS idx_capabilities_tags ON capabilities((CAST(tags_virtual AS CHAR(255) ARRAY)));

-- 分区表优化（如果数据量很大）
-- 按创建时间分区服务表
-- ALTER TABLE services PARTITION BY RANGE (YEAR(created_at)) (
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 按创建时间分区能力表
-- ALTER TABLE capabilities PARTITION BY RANGE (YEAR(created_at)) (
--     PARTITION p2023 VALUES LESS THAN (2024),
--     PARTITION p2024 VALUES LESS THAN (2025),
--     PARTITION p2025 VALUES LESS THAN (2026),
--     PARTITION p_future VALUES LESS THAN MAXVALUE
-- );

-- 查询优化提示
-- 1. 使用EXPLAIN分析查询计划
-- 2. 定期运行ANALYZE TABLE更新统计信息
-- 3. 监控慢查询日志
-- 4. 考虑使用读写分离
-- 5. 对于大数据量，考虑使用分区表
-- 6. 定期清理过期数据

-- 统计信息更新
ANALYZE TABLE services;
ANALYZE TABLE capabilities;
ANALYZE TABLE requirements;
ANALYZE TABLE requirement_capability;
ANALYZE TABLE query_logs;
ANALYZE TABLE query_log_suggestion_relation;
ANALYZE TABLE requirement_suggestions;
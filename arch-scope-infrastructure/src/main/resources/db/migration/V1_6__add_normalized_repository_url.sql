-- 添加标准化仓库URL字段以防止重复注册
-- 版本: V1.6
-- 描述: 为project表添加normalized_repository_url字段，用于统一URL格式并防止重复注册

-- 1. 检查并添加标准化仓库URL字段（如果不存在）
-- 使用存储过程来安全地添加字段
DELIMITER $$
CREATE PROCEDURE AddNormalizedRepositoryUrlColumn()
BEGIN
    DECLARE column_exists INT DEFAULT 0;

    -- 检查字段是否存在
    SELECT COUNT(*) INTO column_exists
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'project'
    AND COLUMN_NAME = 'normalized_repository_url';

    -- 如果字段不存在，则添加
    IF column_exists = 0 THEN
        ALTER TABLE project
        ADD COLUMN normalized_repository_url VARCHAR(512) COMMENT '标准化的Git仓库URL，用于重复检测';
    END IF;
END$$
DELIMITER ;

CALL AddNormalizedRepositoryUrlColumn();
DROP PROCEDURE AddNormalizedRepositoryUrlColumn;

-- 2. 安全地创建标准化URL的唯一索引
-- 先检查索引是否存在
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE()
                     AND TABLE_NAME = 'project'
                     AND INDEX_NAME = 'u_idx_normalized_repository_url');

SET @sql = IF(@index_exists = 0,
              'CREATE UNIQUE INDEX u_idx_normalized_repository_url ON project (normalized_repository_url)',
              'SELECT "Index u_idx_normalized_repository_url already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 3. 安全地创建普通索引以提高查询性能
SET @index_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
                     WHERE TABLE_SCHEMA = DATABASE()
                     AND TABLE_NAME = 'project'
                     AND INDEX_NAME = 'idx_normalized_repository_url');

SET @sql = IF(@index_exists = 0,
              'CREATE INDEX idx_normalized_repository_url ON project (normalized_repository_url)',
              'SELECT "Index idx_normalized_repository_url already exists" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 4. 更新现有数据的标准化URL
-- 注意：这里使用简单的规则来标准化现有数据
-- 实际生产环境中可能需要更复杂的数据迁移脚本

UPDATE project
SET normalized_repository_url = CASE
    -- 处理HTTPS URL，移除.git后缀和尾部斜杠
    WHEN repository_url REGEXP '^https?://' THEN
        LOWER(REGEXP_REPLACE(
            REGEXP_REPLACE(repository_url, '\\.git/?$', ''),
            '/$', ''
        ))
    -- 处理SSH URL，转换为HTTPS格式
    WHEN repository_url REGEXP '^git@' THEN
        LOWER(CONCAT(
            'https://',
            SUBSTRING_INDEX(SUBSTRING_INDEX(repository_url, '@', -1), ':', 1),
            '/',
            REGEXP_REPLACE(
                SUBSTRING_INDEX(repository_url, ':', -1),
                '\\.git/?$', ''
            )
        ))
    -- 其他格式保持原样但转为小写
    ELSE LOWER(repository_url)
END
WHERE normalized_repository_url IS NULL OR normalized_repository_url = '';

-- 5. 安全地添加非空约束（在数据更新后）
-- 检查是否已经有非空约束
SET @constraint_exists = (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
                          WHERE TABLE_SCHEMA = DATABASE()
                          AND TABLE_NAME = 'project'
                          AND COLUMN_NAME = 'normalized_repository_url'
                          AND IS_NULLABLE = 'NO');

SET @sql = IF(@constraint_exists = 0,
              'ALTER TABLE project MODIFY COLUMN normalized_repository_url VARCHAR(512) NOT NULL COMMENT "标准化的Git仓库URL，用于重复检测"',
              'SELECT "Column normalized_repository_url is already NOT NULL" as message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 6. 验证数据完整性
-- 检查是否有重复的标准化URL（仅显示信息，不阻止迁移）
-- 注释掉SELECT语句，因为Flyway不支持返回结果集的语句
-- SELECT normalized_repository_url, COUNT(*) as count
-- FROM project
-- GROUP BY normalized_repository_url
-- HAVING COUNT(*) > 1;

-- 如果发现重复数据，需要手动处理
-- 可以使用以下查询来查看重复的项目
/*
SELECT p1.id, p1.name, p1.repository_url, p1.normalized_repository_url, p1.created_at
FROM project p1
INNER JOIN (
    SELECT normalized_repository_url
    FROM project
    GROUP BY normalized_repository_url
    HAVING COUNT(*) > 1
) p2 ON p1.normalized_repository_url = p2.normalized_repository_url
ORDER BY p1.normalized_repository_url, p1.created_at;
*/

-- 迁移完成标记
SELECT 'V1.6 migration completed successfully' as status;

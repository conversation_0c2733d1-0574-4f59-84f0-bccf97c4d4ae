-- 修复项目类型约束
-- 版本: V1.10
-- 描述: 扩展项目类型约束以支持更多技术栈

-- 删除旧的项目类型约束
ALTER TABLE project DROP CONSTRAINT IF EXISTS chk_project_type;

-- 添加新的项目类型约束，支持主流技术栈
ALTER TABLE project ADD CONSTRAINT chk_project_type
CHECK (type IN ('JAVA', 'JAVASCRIPT', 'PYTHON', 'GO', 'CSHARP', 'KOTLIN', 'RUST', 'PHP', 'TYPESCRIPT', 'OTHER'));

-- 保持现有数据不变，只是扩展约束支持更多类型
-- 现有的 JAVA, JAVASCRIPT, PYTHON, GO, CSHARP, OTHER 数据保持不变

-- 添加注释说明
ALTER TABLE project MODIFY COLUMN type VARCHAR(50) DEFAULT 'OTHER' COMMENT '项目类型: JAVA(Java项目), JAVASCRIPT(JavaScript项目), PY<PERSON><PERSON>(Python项目), GO(Go项目), CSHARP(C#项目), KOTLIN(Kotlin项目), RUST(Rust项目), PHP(PHP项目), TYPESCRIPT(TypeScript项目), OTHER(其他)';

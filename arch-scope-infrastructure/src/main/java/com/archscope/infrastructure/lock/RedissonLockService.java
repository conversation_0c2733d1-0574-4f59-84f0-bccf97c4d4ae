package com.archscope.infrastructure.lock;

import org.redisson.api.RLock;
import org.redisson.api.RRateLimiter;
import org.redisson.api.RReadWriteLock;
import org.redisson.api.RSemaphore;
import org.redisson.api.RateIntervalUnit;
import org.redisson.api.RateType;
import org.redisson.api.RedissonClient;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * Redisson分布式锁和限流服务
 */
@Service
public class RedissonLockService {

    private final RedissonClient redissonClient;

    public RedissonLockService(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    /**
     * 获取锁并执行业务逻辑
     *
     * @param lockKey 锁的名称
     * @param waitTime 获取锁的等待时间
     * @param leaseTime 持有锁的时间
     * @param timeUnit 时间单位
     * @param runnable 需要执行的业务逻辑
     * @return 是否成功获取锁并执行
     */
    public boolean executeLocked(String lockKey, long waitTime, long leaseTime, TimeUnit timeUnit, Runnable runnable) {
        RLock lock = redissonClient.getLock(lockKey);
        try {
            // 尝试获取锁
            boolean locked = lock.tryLock(waitTime, leaseTime, timeUnit);
            if (locked) {
                try {
                    // 执行业务逻辑
                    runnable.run();
                    return true;
                } finally {
                    // 确保释放锁
                    if (lock.isHeldByCurrentThread()) {
                        lock.unlock();
                    }
                }
            }
            return false;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }

    /**
     * 使用限流器控制接口访问速率
     *
     * @param key 限流器的key
     * @param rate 速率
     * @param interval 时间间隔
     * @param intervalUnit 时间单位
     * @param runnable 需要执行的业务逻辑
     * @return 是否没有被限流，并成功执行
     */
    public boolean executeRateLimited(String key, long rate, long interval, RateIntervalUnit intervalUnit, Runnable runnable) {
        RRateLimiter rateLimiter = redissonClient.getRateLimiter(key);
        
        // 初始化限流器，例如：每1分钟允许10个请求
        rateLimiter.trySetRate(RateType.OVERALL, rate, interval, intervalUnit);
        
        // 尝试获取1个令牌，默认超时时间为3秒
        if (rateLimiter.tryAcquire(1, 3, TimeUnit.SECONDS)) {
            try {
                runnable.run();
                return true;
            } catch (Exception e) {
                // 异常处理
                throw new RuntimeException("执行限流操作时发生异常", e);
            }
        }
        return false;
    }

    /**
     * 创建一个公平锁
     * 公平锁保证了按照请求的顺序获取锁，避免某些请求一直得不到处理
     *
     * @param lockKey 锁的名称
     * @return RLock实例
     */
    public RLock getFairLock(String lockKey) {
        return redissonClient.getFairLock(lockKey);
    }

    /**
     * 创建一个读写锁
     * 读写锁允许多个读操作同时执行，但写操作会排斥其他读写操作
     *
     * @param lockKey 锁的名称
     * @return 读锁和写锁的使用示例
     */
    public void useReadWriteLock(String lockKey) {
        // 示例方法，展示如何使用读写锁
        RReadWriteLock rwLock = redissonClient.getReadWriteLock(lockKey);

        // 读锁（共享锁）:允许多个读操作同时进行
        RLock readLock = rwLock.readLock();

        // 写锁（排它锁）:一次只允许一个写操作
        RLock writeLock = rwLock.writeLock();
        
        // 使用读锁的示例
        try {
            readLock.lock(10, TimeUnit.SECONDS); // 最多持有10秒
            // 执行读操作...
        } finally {
            if (readLock.isHeldByCurrentThread()) {
                readLock.unlock();
            }
        }
        
        // 使用写锁的示例
        try {
            writeLock.lock(10, TimeUnit.SECONDS); // 最多持有10秒
            // 执行写操作...
        } finally {
            if (writeLock.isHeldByCurrentThread()) {
                writeLock.unlock();
            }
        }
    }
    
    /**
     * 获取分布式信号量
     * 信号量可以用来限制对资源的并发访问数量
     * 
     * @param key 信号量名称
     * @param permits 许可数量
     * @return 是否成功获取信号量
     */
    public boolean useSemaphore(String key, int permits) {
        RSemaphore semaphore = redissonClient.getSemaphore(key);
        try {
            // 设置许可数量
            semaphore.trySetPermits(permits);
            
            // 尝试获取1个许可，最多等待2秒
            if (semaphore.tryAcquire(1, 2, TimeUnit.SECONDS)) {
                try {
                    // 执行受限资源的访问...
                    return true;
                } finally {
                    // 释放许可
                    semaphore.release();
                }
            }
            return false;
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            return false;
        }
    }
} 
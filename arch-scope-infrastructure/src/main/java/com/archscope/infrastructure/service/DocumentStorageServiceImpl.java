package com.archscope.infrastructure.service;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.repository.DocumentVersionRepository;
import com.archscope.domain.service.DocumentStorageService;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 文档存储服务实现类
 * 基于数据库的markdown文档存储和检索，同时支持文件系统缓存
 */
@Slf4j
@Service
public class DocumentStorageServiceImpl implements DocumentStorageService {

    private final DocumentVersionRepository documentVersionRepository;

    @Value("${archscope.document.storage.base-path:./documents}")
    private String basePath;

    private static final String PROJECTS_DIR = "projects";
    private static final String LATEST_LINK = "latest";

    public DocumentStorageServiceImpl(DocumentVersionRepository documentVersionRepository) {
        this.documentVersionRepository = documentVersionRepository;
    }

    @Override
    public String storeDocument(Long projectId, String docType, String content, String version) throws IOException {
        log.debug("存储文档: projectId={}, docType={}, version={}, contentLength={}",
                projectId, docType, version, (content != null ? content.length() : 0));

        // 创建目录结构
        createProjectDirectories(projectId, version);

        // 构建文件路径
        String filePath = getDocumentPath(projectId, docType, version);
        Path path = Paths.get(filePath);

        // 写入文件
        Files.write(path, content.getBytes(StandardCharsets.UTF_8),
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);

        // 更新latest链接
        updateLatestLink(projectId, version);

        log.debug("文档存储成功: {}", filePath);
        return filePath;
    }

    @Override
    public Optional<String> getDocumentContent(Long projectId, String docType, String version) throws IOException {
        try {
            // 首先尝试从数据库获取文档内容
            Optional<String> dbContent = getDocumentContentFromDatabase(projectId, docType, version);
            if (dbContent.isPresent()) {
                log.debug("从数据库读取文档成功: projectId={}, docType={}, version={}", projectId, docType, version);
                return dbContent;
            }

            // 如果数据库中没有，尝试从文件系统读取
            String actualVersion = version != null ? version : LATEST_LINK;
            String filePath = getDocumentPath(projectId, docType, actualVersion);
            Path path = Paths.get(filePath);

            if (!Files.exists(path)) {
                log.debug("文档不存在: {}", filePath);
                return Optional.empty();
            }

            byte[] bytes = Files.readAllBytes(path);
            String content = new String(bytes, StandardCharsets.UTF_8);
            log.debug("从文件系统读取文档成功: {}, contentLength={}", filePath, content.length());
            return Optional.of(content);
        } catch (Exception e) {
            log.error("读取文档失败: projectId={}, docType={}, version={}, error={}", projectId, docType, version, e.getMessage());
            throw new IOException("读取文档失败", e);
        }
    }

    /**
     * 从数据库获取文档内容
     */
    private Optional<String> getDocumentContentFromDatabase(Long projectId, String docType, String version) {
        try {
            // 查找文档版本记录
            List<DocumentVersion> documentVersions = documentVersionRepository.findByProjectId(projectId);

            for (DocumentVersion docVersion : documentVersions) {
                if (docVersion.getDocType() != null &&
                    docVersion.getDocType().name().equals(docType) &&
                    (version == null || version.equals(docVersion.getVersionTag()))) {

                    // 从compare_metadata字段获取内容
                    if (docVersion.getCompareMetadata() != null) {
                        Object contentObj = docVersion.getCompareMetadata().get("content");
                        if (contentObj instanceof String) {
                            return Optional.of((String) contentObj);
                        }
                    }
                }
            }

            return Optional.empty();
        } catch (Exception e) {
            System.err.println("从数据库读取文档失败: " + e.getMessage());
            return Optional.empty();
        }
    }

    @Override
    public boolean deleteDocument(Long projectId, String docType, String version) {
        String filePath = getDocumentPath(projectId, docType, version);
        Path path = Paths.get(filePath);

        try {
            boolean deleted = Files.deleteIfExists(path);
            if (deleted) {
                System.out.println("文档删除成功: " + filePath);
            } else {
                System.out.println("文档不存在，无需删除: " + filePath);
            }
            return deleted;
        } catch (IOException e) {
            System.err.println("删除文档失败: " + filePath + ", error=" + e.getMessage());
            return false;
        }
    }

    @Override
    public String getDocumentPath(Long projectId, String docType, String version) {
        return String.format("%s/%s/%s/%s/%s.md", 
                basePath, PROJECTS_DIR, projectId, version, docType.toLowerCase());
    }

    @Override
    public boolean documentExists(Long projectId, String docType, String version) {
        String filePath = getDocumentPath(projectId, docType, version);
        return Files.exists(Paths.get(filePath));
    }

    @Override
    public void createProjectDirectories(Long projectId, String version) throws IOException {
        String projectDir = String.format("%s/%s/%s/%s", basePath, PROJECTS_DIR, projectId, version);
        Path path = Paths.get(projectDir);

        if (!Files.exists(path)) {
            Files.createDirectories(path);
            System.out.println("创建项目目录: " + projectDir);
        }
    }

    @Override
    public List<String> getProjectVersions(Long projectId) {
        try {
            // 从数据库获取版本列表
            List<DocumentVersion> documentVersions = documentVersionRepository.findByProjectId(projectId);
            return documentVersions.stream()
                    .map(DocumentVersion::getVersionTag)
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            System.err.println("获取项目版本列表失败: projectId=" + projectId + ", error=" + e.getMessage());
            return new ArrayList<>();
        }
    }

    @Override
    public List<String> getDocumentTypes(Long projectId, String version) {
        try {
            // 从数据库获取文档类型列表
            List<DocumentVersion> documentVersions = documentVersionRepository.findByProjectId(projectId);
            return documentVersions.stream()
                    .filter(dv -> version == null || version.equals(dv.getVersionTag()))
                    .map(dv -> dv.getDocType() != null ? dv.getDocType().name() : "UNKNOWN")
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
        } catch (Exception e) {
            System.err.println("获取文档类型列表失败: projectId=" + projectId + ", version=" + version + ", error=" + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * 更新latest链接指向最新版本
     */
    private void updateLatestLink(Long projectId, String version) {
        String projectDir = String.format("%s/%s/%s", basePath, PROJECTS_DIR, projectId);
        Path latestPath = Paths.get(projectDir, LATEST_LINK);
        Path versionPath = Paths.get(version);

        try {
            // 删除现有的latest链接
            Files.deleteIfExists(latestPath);

            // 创建新的符号链接
            Files.createSymbolicLink(latestPath, versionPath);
            System.out.println("更新latest链接: projectId=" + projectId + ", version=" + version);
        } catch (IOException e) {
            System.out.println("更新latest链接失败: projectId=" + projectId + ", version=" + version + ", error=" + e.getMessage());
            // 链接创建失败不影响主要功能，只记录警告
        }
    }
}

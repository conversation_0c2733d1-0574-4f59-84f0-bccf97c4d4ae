package com.archscope.infrastructure.parser.traditional;

import com.archscope.domain.model.parser.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * JavaScript/TypeScript传统解析器实现类
 * 使用Babel或TypeScript Compiler API解析JS/TS代码
 */
@Slf4j
@Component
public class JsTraditionalParser implements TraditionalCodeParser {

    // 导入语句的正则表达式
    private static final Pattern IMPORT_PATTERN = Pattern.compile("import\\s+.*?from\\s+['\"]([^'\"]+)['\"]");
    
    // 类定义的正则表达式
    private static final Pattern CLASS_PATTERN = Pattern.compile("class\\s+(\\w+)\\s*(extends\\s+(\\w+))?");
    
    // 接口定义的正则表达式 (TypeScript)
    private static final Pattern INTERFACE_PATTERN = Pattern.compile("interface\\s+(\\w+)\\s*(extends\\s+([\\w,\\s]+))?");

    @Override
    public FileParseResult parseFile(String filename, String content) {
        log.info("使用传统解析器解析JS/TS文件: {}", filename);

        LanguageType languageType = filename.endsWith(".ts") ? LanguageType.TYPESCRIPT : LanguageType.JAVASCRIPT;
        
        FileParseResult.FileParseResultBuilder resultBuilder = FileParseResult.builder()
                .filename(filename)
                .filePath(filename)
                .languageType(languageType)
                .successful(true);

        try {
            // 这里使用Babel或TypeScript Compiler API解析JS/TS代码
            // 由于依赖问题，这里只提供一个简化的实现
            // 实际项目中需要添加相应依赖并完善解析逻辑
            
            // 解析导入
            List<String> imports = extractImports(content);
            resultBuilder.imports(imports);
            
            // 解析类定义
            List<ClassDefinition> classDefinitions = extractClassDefinitions(content, languageType);
            resultBuilder.classDefinitions(classDefinitions);
            
            // 解析依赖关系
            List<DependencyRelation> dependencies = extractDependencies(content, classDefinitions, imports);
            resultBuilder.dependencies(dependencies);

            return resultBuilder.build();
        } catch (Exception e) {
            log.error("解析JS/TS文件时出错: {}", filename, e);
            return resultBuilder
                    .successful(false)
                    .errorMessage("JS/TS解析错误: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public FileParseResult parseFile(File file) throws IOException {
        String content = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);
        return parseFile(file.getName(), content);
    }

    @Override
    public LanguageType getSupportedLanguage() {
        // 支持JavaScript和TypeScript
        return LanguageType.JAVASCRIPT; // 主要支持的语言
    }
    
    /**
     * 提取导入
     */
    private List<String> extractImports(String content) {
        List<String> imports = new ArrayList<>();
        Matcher matcher = IMPORT_PATTERN.matcher(content);
        while (matcher.find()) {
            imports.add(matcher.group(1));
        }
        return imports;
    }
    
    /**
     * 提取类定义
     */
    private List<ClassDefinition> extractClassDefinitions(String content, LanguageType languageType) {
        List<ClassDefinition> classDefinitions = new ArrayList<>();
        
        // 解析类
        Matcher classMatcher = CLASS_PATTERN.matcher(content);
        while (classMatcher.find()) {
            String className = classMatcher.group(1);
            String superClass = classMatcher.group(3);
            
            ClassDefinition classDef = ClassDefinition.builder()
                    .name(className)
                    .type(ClassType.CLASS)
                    .superClass(superClass)
                    .fullyQualifiedName(className) // JS/TS没有包名概念，使用类名作为全限定名
                    .accessModifier(AccessModifier.PUBLIC) // JS/TS默认为public
                    .build();
            
            classDefinitions.add(classDef);
        }
        
        // 如果是TypeScript，还需要解析接口
        if (languageType == LanguageType.TYPESCRIPT) {
            Matcher interfaceMatcher = INTERFACE_PATTERN.matcher(content);
            while (interfaceMatcher.find()) {
                String interfaceName = interfaceMatcher.group(1);
                String extendedInterfaces = interfaceMatcher.group(3);
                
                List<String> interfaces = new ArrayList<>();
                if (extendedInterfaces != null) {
                    for (String interfaceName2 : extendedInterfaces.split(",")) {
                        interfaces.add(interfaceName2.trim());
                    }
                }
                
                ClassDefinition interfaceDef = ClassDefinition.builder()
                        .name(interfaceName)
                        .type(ClassType.INTERFACE)
                        .interfaces(interfaces)
                        .fullyQualifiedName(interfaceName)
                        .accessModifier(AccessModifier.PUBLIC)
                        .build();
                
                classDefinitions.add(interfaceDef);
            }
        }
        
        return classDefinitions;
    }
    
    /**
     * 提取依赖关系
     */
    private List<DependencyRelation> extractDependencies(String content, List<ClassDefinition> classDefinitions, List<String> imports) {
        List<DependencyRelation> dependencies = new ArrayList<>();
        
        // 为每个类定义创建依赖关系
        for (ClassDefinition classDef : classDefinitions) {
            String sourceClass = classDef.getFullyQualifiedName();
            
            // 继承关系
            if (classDef.getSuperClass() != null && !classDef.getSuperClass().isEmpty()) {
                dependencies.add(DependencyRelation.builder()
                        .sourceClass(sourceClass)
                        .targetClass(classDef.getSuperClass())
                        .type(DependencyType.INHERITANCE)
                        .strength(10)
                        .location("class definition")
                        .build());
            }
            
            // 接口实现/扩展
            for (String interfaceName : classDef.getInterfaces()) {
                dependencies.add(DependencyRelation.builder()
                        .sourceClass(sourceClass)
                        .targetClass(interfaceName)
                        .type(DependencyType.IMPLEMENTATION)
                        .strength(8)
                        .location("interface extension")
                        .build());
            }
            
            // 导入依赖
            for (String importPath : imports) {
                // 从导入路径中提取模块名
                String moduleName = importPath;
                if (moduleName.contains("/")) {
                    moduleName = moduleName.substring(moduleName.lastIndexOf("/") + 1);
                }
                
                dependencies.add(DependencyRelation.builder()
                        .sourceClass(sourceClass)
                        .targetClass(importPath)
                        .type(DependencyType.IMPORT)
                        .strength(5)
                        .location("import")
                        .build());
            }
        }
        
        return dependencies;
    }
}

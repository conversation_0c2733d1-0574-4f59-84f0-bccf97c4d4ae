package com.archscope.infrastructure.parser.traditional;

import com.archscope.domain.model.parser.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Java传统解析器实现类
 * 使用JavaParser库解析Java代码
 */
@Slf4j
@Component
public class JavaTraditionalParser implements TraditionalCodeParser {

    @Override
    public FileParseResult parseFile(String filename, String content) {
        log.info("使用JavaParser解析Java文件: {}", filename);

        FileParseResult.FileParseResultBuilder resultBuilder = FileParseResult.builder()
                .filename(filename)
                .filePath(filename)
                .languageType(LanguageType.JAVA)
                .successful(true);

        try {
            // 这里使用JavaParser库解析Java代码
            // 由于依赖问题，这里只提供一个简化的实现
            // 实际项目中需要添加JavaParser依赖并完善解析逻辑
            
            // 模拟解析包名
            String packageName = extractPackageName(content);
            resultBuilder.packageName(packageName);
            
            // 模拟解析导入
            List<String> imports = extractImports(content);
            resultBuilder.imports(imports);
            
            // 模拟解析类定义
            List<ClassDefinition> classDefinitions = extractClassDefinitions(content, packageName);
            resultBuilder.classDefinitions(classDefinitions);
            
            // 模拟解析依赖关系
            List<DependencyRelation> dependencies = extractDependencies(content, classDefinitions, imports);
            resultBuilder.dependencies(dependencies);

            return resultBuilder.build();
        } catch (Exception e) {
            log.error("JavaParser解析文件时出错: {}", filename, e);
            return resultBuilder
                    .successful(false)
                    .errorMessage("JavaParser解析错误: " + e.getMessage())
                    .build();
        }
    }

    @Override
    public FileParseResult parseFile(File file) throws IOException {
        String content = new String(Files.readAllBytes(file.toPath()), StandardCharsets.UTF_8);
        return parseFile(file.getName(), content);
    }

    @Override
    public LanguageType getSupportedLanguage() {
        return LanguageType.JAVA;
    }
    
    /**
     * 提取包名
     */
    private String extractPackageName(String content) {
        // 简单实现，使用正则表达式提取包名
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("package\\s+([\\w.]+)\\s*;");
        java.util.regex.Matcher matcher = pattern.matcher(content);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
    
    /**
     * 提取导入
     */
    private List<String> extractImports(String content) {
        List<String> imports = new ArrayList<>();
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("import\\s+([\\w.]+)\\s*;");
        java.util.regex.Matcher matcher = pattern.matcher(content);
        while (matcher.find()) {
            imports.add(matcher.group(1));
        }
        return imports;
    }
    
    /**
     * 提取类定义
     */
    private List<ClassDefinition> extractClassDefinitions(String content, String packageName) {
        List<ClassDefinition> classDefinitions = new ArrayList<>();
        
        // 匹配类定义
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(
                "(public|private|protected)?\\s*(abstract|final)?\\s*class\\s+(\\w+)\\s*(extends\\s+([\\w.]+))?\\s*(implements\\s+([\\w.,\\s]+))?\\s*\\{");
        java.util.regex.Matcher matcher = pattern.matcher(content);
        
        while (matcher.find()) {
            String accessModifierStr = matcher.group(1);
            AccessModifier accessModifier = parseAccessModifier(accessModifierStr);
            
            String modifierStr = matcher.group(2);
            boolean isAbstract = modifierStr != null && modifierStr.equals("abstract");
            boolean isFinal = modifierStr != null && modifierStr.equals("final");
            
            String className = matcher.group(3);
            
            String superClassPart = matcher.group(4);
            String superClass = null;
            if (superClassPart != null) {
                String superClassGroup = matcher.group(5);
                superClass = superClassGroup != null ? superClassGroup.trim() : null;
            }
            
            List<String> interfaces = new ArrayList<>();
            String interfacesPart = matcher.group(6);
            if (interfacesPart != null) {
                String interfacesGroup = matcher.group(7);
                if (interfacesGroup != null) {
                    for (String interfaceName : interfacesGroup.split(",")) {
                        interfaces.add(interfaceName.trim());
                    }
                }
            }
            
            ClassDefinition classDef = ClassDefinition.builder()
                    .name(className)
                    .type(ClassType.CLASS)
                    .packageName(packageName)
                    .fullyQualifiedName(packageName != null ? packageName + "." + className : className)
                    .accessModifier(accessModifier)
                    .superClass(superClass)
                    .interfaces(interfaces)
                    .isAbstract(isAbstract)
                    .isFinal(isFinal)
                    .build();
            
            classDefinitions.add(classDef);
        }
        
        return classDefinitions;
    }
    
    /**
     * 提取依赖关系
     */
    private List<DependencyRelation> extractDependencies(String content, List<ClassDefinition> classDefinitions, List<String> imports) {
        List<DependencyRelation> dependencies = new ArrayList<>();
        
        // 为每个类定义创建依赖关系
        for (ClassDefinition classDef : classDefinitions) {
            String sourceClass = classDef.getFullyQualifiedName();
            
            // 继承关系
            if (classDef.getSuperClass() != null && !classDef.getSuperClass().isEmpty()) {
                dependencies.add(DependencyRelation.builder()
                        .sourceClass(sourceClass)
                        .targetClass(classDef.getSuperClass())
                        .type(DependencyType.INHERITANCE)
                        .strength(10)
                        .location("class definition")
                        .build());
            }
            
            // 接口实现
            for (String interfaceName : classDef.getInterfaces()) {
                dependencies.add(DependencyRelation.builder()
                        .sourceClass(sourceClass)
                        .targetClass(interfaceName)
                        .type(DependencyType.IMPLEMENTATION)
                        .strength(8)
                        .location("class definition")
                        .build());
            }
            
            // 字段依赖
            java.util.regex.Pattern fieldPattern = java.util.regex.Pattern.compile("private\\s+(\\w+)\\s+(\\w+)");
            java.util.regex.Matcher fieldMatcher = fieldPattern.matcher(content);
            
            while (fieldMatcher.find()) {
                String fieldType = fieldMatcher.group(1);
                String fieldName = fieldMatcher.group(2);
                
                // 检查字段类型是否在导入列表中
                for (String importName : imports) {
                    if (importName.endsWith("." + fieldType)) {
                        dependencies.add(DependencyRelation.builder()
                                .sourceClass(sourceClass)
                                .targetClass(importName)
                                .type(DependencyType.FIELD)
                                .strength(7)
                                .location("field: " + fieldName)
                                .build());
                        break;
                    }
                }
            }
        }
        
        return dependencies;
    }
    
    /**
     * 解析访问修饰符
     */
    private AccessModifier parseAccessModifier(String modifier) {
        if (modifier == null) {
            return AccessModifier.PACKAGE_PRIVATE;
        }
        
        switch (modifier.trim()) {
            case "public":
                return AccessModifier.PUBLIC;
            case "private":
                return AccessModifier.PRIVATE;
            case "protected":
                return AccessModifier.PROTECTED;
            default:
                return AccessModifier.PACKAGE_PRIVATE;
        }
    }
}

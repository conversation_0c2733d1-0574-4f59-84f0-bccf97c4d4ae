package com.archscope.infrastructure.mapper;

import com.archscope.infrastructure.entity.TaskEntity;
import com.archscope.domain.valueobject.TaskStatus;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 领域任务Mapper接口
 */
@Mapper
public interface TaskMapper extends BaseMapper<TaskEntity> {
    
    /**
     * 查找下一个待执行的任务
     *
     * @return 任务
     */
    TaskEntity findNextPendingTask();

    /**
     * 查找指定类型的下一个待执行任务
     *
     * @param taskType 任务类型
     * @return 任务
     */
    TaskEntity findNextPendingTaskByType(@Param("taskType") String taskType);

    /**
     * 查找所有指定状态的任务
     *
     * @param status 任务状态
     * @return 任务列表
     */
    List<TaskEntity> findAllByStatus(@Param("status") TaskStatus status);

    /**
     * 查找所有指定状态和类型的任务
     *
     * @param status 任务状态
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<TaskEntity> findAllByStatusAndTaskType(@Param("status") TaskStatus status, @Param("taskType") String taskType);

    /**
     * 查找所有指定状态和项目ID的任务
     *
     * @param status 任务状态
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<TaskEntity> findAllByStatusAndProjectId(@Param("status") TaskStatus status, @Param("projectId") Long projectId);
    
    /**
     * 删除指定日期之前的已完成任务
     *
     * @param date 日期
     * @return 删除的任务数量
     */
    int deleteCompletedTasksOlderThan(@Param("date") LocalDateTime date);

    /**
     * 查找超时的PROCESSING任务
     *
     * @return 超时任务列表
     */
    List<TaskEntity> findTimeoutProcessingTasks();

    /**
     * 更新任务状态为PROCESSING并设置超时时间
     *
     * @param taskId 任务ID
     * @param workerId 工作节点ID
     * @param timeoutAt 超时时间
     * @return 更新的行数
     */
    int updateTaskToProcessing(@Param("taskId") Long taskId, @Param("workerId") String workerId,
                            @Param("timeoutAt") LocalDateTime timeoutAt);

    /**
     * 重置超时任务状态为PENDING
     *
     * @param taskId 任务ID
     * @return 更新的行数
     */
    int resetTimeoutTask(@Param("taskId") Long taskId);

    /**
     * 完成任务并设置结果
     *
     * @param taskId 任务ID
     * @param overallStatus 整体状态
     * @param results 结果JSON
     * @param executionTimeMs 执行时间
     * @return 更新的行数
     */
    int completeTaskWithResult(@Param("taskId") Long taskId, @Param("overallStatus") String overallStatus,
                              @Param("results") String results, @Param("executionTimeMs") Long executionTimeMs);

    /**
     * 标记任务失败
     *
     * @param taskId 任务ID
     * @param errorDetail 错误详情
     * @param executionTimeMs 执行时间
     * @return 更新的行数
     */
    int failTaskWithError(@Param("taskId") Long taskId, @Param("errorDetail") String errorDetail,
                         @Param("executionTimeMs") Long executionTimeMs);

    /**
     * 分页查询任务
     *
     * @param status 任务状态
     * @param taskType 任务类型
     * @param projectId 项目ID
     * @param workerId 工作节点ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param orderBy 排序字段
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 任务列表
     */
    List<TaskEntity> findTasksWithPagination(@Param("status") TaskStatus status,
                                           @Param("taskType") String taskType,
                                           @Param("projectId") Long projectId,
                                           @Param("workerId") String workerId,
                                           @Param("startDate") LocalDateTime startDate,
                                           @Param("endDate") LocalDateTime endDate,
                                           @Param("orderBy") String orderBy,
                                           @Param("offset") int offset,
                                           @Param("limit") int limit);

    /**
     * 统计符合条件的任务数量
     *
     * @param status 任务状态
     * @param taskType 任务类型
     * @param projectId 项目ID
     * @param workerId 工作节点ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 任务数量
     */
    long countTasksWithCondition(@Param("status") TaskStatus status,
                                @Param("taskType") String taskType,
                                @Param("projectId") Long projectId,
                                @Param("workerId") String workerId,
                                @Param("startDate") LocalDateTime startDate,
                                @Param("endDate") LocalDateTime endDate);

    /**
     * 批量更新任务状态
     *
     * @param taskIds 任务ID列表
     * @param currentStatus 当前状态
     * @param newStatus 新状态
     * @return 更新的行数
     */
    int batchUpdateTaskStatus(@Param("taskIds") List<Long> taskIds,
                             @Param("currentStatus") TaskStatus currentStatus,
                             @Param("newStatus") TaskStatus newStatus);

    /**
     * 批量重置超时任务
     *
     * @param taskIds 任务ID列表
     * @return 更新的行数
     */
    int batchResetTimeoutTasks(@Param("taskIds") List<Long> taskIds);

    /**
     * 获取任务统计信息
     *
     * @param projectId 项目ID
     * @param taskType 任务类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    List<Map<String, Object>> getTaskStatistics(@Param("projectId") Long projectId,
                                               @Param("taskType") String taskType,
                                               @Param("startDate") LocalDateTime startDate,
                                               @Param("endDate") LocalDateTime endDate);

    /**
     * 查找长时间运行的任务
     *
     * @param minutes 运行时间阈值（分钟）
     * @param limit 限制数量
     * @return 长时间运行的任务列表
     */
    List<TaskEntity> findLongRunningTasks(@Param("minutes") int minutes, @Param("limit") int limit);

    /**
     * 使用CAS操作锁定任务
     *
     * @param taskId 任务ID
     * @param workerId 工作节点ID
     * @param timeoutMinutes 超时时间（分钟）
     * @param expectedVersion 期望的版本号
     * @return 更新的行数
     */
    int lockTaskWithCAS(@Param("taskId") Long taskId,
                       @Param("workerId") String workerId,
                       @Param("timeoutMinutes") int timeoutMinutes,
                       @Param("expectedVersion") Integer expectedVersion);

    /**
     * 重试任务
     *
     * @param taskId 任务ID
     * @return 更新的行数
     */
    int retryTask(@Param("taskId") Long taskId);
}

package com.archscope.infrastructure.mapper;

import com.archscope.domain.entity.DocumentVersion;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档版本Mapper接口
 */
@Mapper
public interface DocumentVersionMapper extends BaseMapper<DocumentVersion> {

    /**
     * 插入文档版本
     * @param documentVersion 文档版本实体
     * @return 影响行数
     */
    int insert(DocumentVersion documentVersion);

    /**
     * 更新文档版本
     * @param documentVersion 文档版本实体
     * @return 影响行数
     */
    int updateById(DocumentVersion documentVersion);

    /**
     * 根据ID查询文档版本
     * @param id 文档版本ID
     * @return 文档版本实体
     */
    DocumentVersion selectById(@Param("id") Long id);

    /**
     * 根据项目ID查询文档版本列表
     * @param projectId 项目ID
     * @return 文档版本列表
     */
    List<DocumentVersion> selectByProjectId(@Param("projectId") Long projectId);

    /**
     * 根据项目ID和文档类型查询文档版本列表
     * @param projectId 项目ID
     * @param docType 文档类型
     * @return 文档版本列表
     */
    List<DocumentVersion> selectByProjectIdAndDocType(@Param("projectId") Long projectId, @Param("docType") String docType);

    /**
     * 根据项目ID和提交ID查询文档版本列表
     * @param projectId 项目ID
     * @param commitId 提交ID
     * @return 文档版本列表
     */
    List<DocumentVersion> selectByProjectIdAndCommitId(@Param("projectId") Long projectId, @Param("commitId") String commitId);

    /**
     * 根据项目ID和版本标签查询文档版本
     * @param projectId 项目ID
     * @param versionTag 版本标签
     * @return 文档版本实体
     */
    DocumentVersion selectByProjectIdAndVersionTag(@Param("projectId") Long projectId, @Param("versionTag") String versionTag);

    /**
     * 查询项目的最新文档版本
     * @param projectId 项目ID
     * @param docType 文档类型
     * @return 文档版本实体
     */
    DocumentVersion selectLatestByProjectIdAndDocType(@Param("projectId") Long projectId, @Param("docType") String docType);

    /**
     * 删除文档版本
     * @param id 文档版本ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);

    /**
     * 删除项目的所有文档版本
     * @param projectId 项目ID
     * @return 影响行数
     */
    int deleteByProjectId(@Param("projectId") Long projectId);

    /**
     * 统计项目的文档版本数量
     * @param projectId 项目ID
     * @return 文档版本数量
     */
    long countByProjectId(@Param("projectId") Long projectId);
}

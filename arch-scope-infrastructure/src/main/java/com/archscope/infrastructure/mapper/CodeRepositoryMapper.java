package com.archscope.infrastructure.mapper;

import com.archscope.domain.entity.CodeRepository;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代码仓库Mapper接口
 */
@Mapper
public interface CodeRepositoryMapper extends BaseMapper<CodeRepository> {
    
    /**
     * 根据项目ID查询代码仓库列表
     * 
     * @param projectId 项目ID
     * @return 代码仓库列表
     */
    List<CodeRepository> selectByProjectId(@Param("projectId") Long projectId);
    
    /**
     * 根据URL查询代码仓库
     * 
     * @param url 仓库URL
     * @return 代码仓库
     */
    CodeRepository selectByUrl(@Param("url") String url);
}

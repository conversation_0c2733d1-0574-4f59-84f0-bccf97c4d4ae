package com.archscope.infrastructure.mapper;

import com.archscope.domain.entity.ParseResultCache;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 解析结果缓存Mapper接口
 */
@Mapper
public interface ParseResultCacheMapper extends BaseMapper<ParseResultCache> {
    
    /**
     * 根据仓库ID和提交ID查询解析结果缓存
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @return 解析结果缓存
     */
    ParseResultCache selectByRepositoryIdAndCommitId(@Param("repositoryId") Long repositoryId, @Param("commitId") String commitId);
    
    /**
     * 根据仓库ID查询所有解析结果缓存
     * 
     * @param repositoryId 代码仓库ID
     * @return 解析结果缓存列表
     */
    List<ParseResultCache> selectAllByRepositoryId(@Param("repositoryId") Long repositoryId);
    
    /**
     * 删除仓库的所有解析结果缓存
     * 
     * @param repositoryId 代码仓库ID
     * @return 影响行数
     */
    int deleteAllByRepositoryId(@Param("repositoryId") Long repositoryId);
    
    /**
     * 检查解析结果缓存是否存在
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @return 是否存在
     */
    boolean existsByRepositoryIdAndCommitId(@Param("repositoryId") Long repositoryId, @Param("commitId") String commitId);
}

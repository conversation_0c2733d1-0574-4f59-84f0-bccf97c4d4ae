package com.archscope.infrastructure.messaging;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessagingException;

/**
 * RocketMQ 降级模板，用于 RocketMQ 不可用时提供降级服务
 * 所有方法都会记录警告日志，但不会抛出异常
 */
@Slf4j
public class FallbackRocketMQTemplate extends RocketMQTemplate {

    public FallbackRocketMQTemplate() {
        // 不进行实际初始化
    }

    @Override
    public void send(String destination, Message<?> message) throws MessagingException {
        logWarning("send", destination);
    }

    @Override
    public void asyncSend(String destination, Message<?> message, org.apache.rocketmq.client.producer.SendCallback sendCallback) throws MessagingException {
        logWarning("asyncSend", destination);
        // 模拟异步成功
        if (sendCallback != null) {
            try {
                sendCallback.onSuccess(null);
            } catch (Exception e) {
                log.error("回调执行失败", e);
            }
        }
    }

    @Override
    public void asyncSend(String destination, Message<?> message, org.apache.rocketmq.client.producer.SendCallback sendCallback, long timeout) throws MessagingException {
        logWarning("asyncSend with timeout", destination);
        // 模拟异步成功
        if (sendCallback != null) {
            try {
                sendCallback.onSuccess(null);
            } catch (Exception e) {
                log.error("回调执行失败", e);
            }
        }
    }

    @Override
    public void sendOneWay(String destination, Message<?> message) throws MessagingException {
        logWarning("sendOneWay", destination);
    }

    private void logWarning(String operation, String destination) {
        log.warn("⚠️ RocketMQ 不可用，消息操作 [{}] 发送到 [{}] 被忽略", operation, destination);
    }

    // 实现一个简单的同步发送方法，返回成功但记录警告
    @Override
    public org.apache.rocketmq.client.producer.SendResult syncSend(String destination, Message<?> message) {
        logWarning("syncSend", destination);
        return null;
    }

}

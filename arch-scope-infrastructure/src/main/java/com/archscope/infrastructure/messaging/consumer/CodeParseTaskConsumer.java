package com.archscope.infrastructure.messaging.consumer;

import com.archscope.domain.constants.MessageConstants;
import com.archscope.domain.entity.Task;
import com.archscope.domain.message.TaskMessage;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.task.CodeParseTask;
import com.archscope.domain.task.TaskExecutor;
import com.archscope.domain.task.TaskExecutorRegistry;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 代码解析任务消费者
 * 负责消费代码解析任务消息并执行相应的任务
 */
@Slf4j
@Component
@RequiredArgsConstructor
@ConditionalOnProperty(name = "rocketmq.consumer.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(
        topic = MessageConstants.TOPIC_CODE_PARSE,
        consumerGroup = MessageConstants.GROUP_CODE_PARSE,
        consumeMode = ConsumeMode.CONCURRENTLY,
        messageModel = MessageModel.CLUSTERING
)
public class CodeParseTaskConsumer implements RocketMQListener<TaskMessage> {

    private final TaskQueueService taskQueueService;
    private final TaskExecutorRegistry taskExecutorRegistry;

    @Override
    public void onMessage(TaskMessage message) {
        log.info("接收到代码解析任务消息: {}", message);

        try {
            // 获取任务ID
            Long taskId = message.getTaskId();
            if (taskId == null) {
                log.error("任务ID为空，无法处理任务消息");
                return;
            }

            // 获取任务详情
            Optional<Task> taskOpt = taskQueueService.getTaskById(taskId);
            if (!taskOpt.isPresent()) {
                log.error("任务不存在: {}", taskId);
                return;
            }

            Task task = taskOpt.get();

            // 获取任务执行器
            TaskExecutor executor = taskExecutorRegistry.getExecutor(CodeParseTask.TASK_TYPE);
            if (executor == null) {
                log.error("未找到代码解析任务执行器");
                taskQueueService.recordTaskError(taskId, "未找到代码解析任务执行器");
                return;
            }

            // 执行任务
            log.info("开始执行代码解析任务: {}", taskId);
            executor.execute(task);

        } catch (Exception e) {
            log.error("处理代码解析任务消息异常", e);
        }
    }
}

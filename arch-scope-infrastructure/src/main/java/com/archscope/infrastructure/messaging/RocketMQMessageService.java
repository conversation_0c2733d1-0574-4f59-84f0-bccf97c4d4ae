package com.archscope.infrastructure.messaging;

import com.archscope.domain.message.TaskMessage;
import com.archscope.domain.service.MessageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendCallback;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.support.MessageBuilder;
import org.springframework.stereotype.Service;

/**
 * RocketMQ消息服务实现
 */
@Slf4j
@Service
public class RocketMQMessageService implements MessageService {

    private static final Logger log = LoggerFactory.getLogger(RocketMQMessageService.class);

    private RocketMQTemplate rocketMQTemplate;

    // 是否启用 RocketMQ 消费者
    private boolean rocketMQConsumerEnabled;

    /**
     * 默认构造函数，在Spring无法找到RocketMQTemplate时使用
     */
    public RocketMQMessageService() {
        log.warn("⚠️ 使用降级的 RocketMQ 消息服务，消息功能将不可用");
        this.rocketMQConsumerEnabled = false;
        this.rocketMQTemplate = null;
    }

    /**
     * 带参数的构造函数
     */
    @Autowired(required = false)
    public RocketMQMessageService(RocketMQTemplate rocketMQTemplate) {
        this.rocketMQTemplate = rocketMQTemplate;
        this.rocketMQConsumerEnabled = Boolean.parseBoolean(
                System.getProperty("rocketmq.consumer.enabled", "true"));
        log.info("RocketMQ消息服务初始化完成，消费者启用状态: {}", rocketMQConsumerEnabled);
    }

    @Override
    public boolean sendTaskMessage(String topic, TaskMessage message) {
        if (!isRocketMQAvailable()) {
            log.warn("⚠️ RocketMQ 消费者不可用，消息 [{}] 将不会被处理: {}", topic, message);
            return false;
        }

        try {
            SendResult sendResult = rocketMQTemplate.syncSend(topic, MessageBuilder.withPayload(message).build());
            log.debug("发送消息成功: topic={}, message={}, result={}", topic, message, sendResult);
            return true;
        } catch (Exception e) {
            log.error("发送消息失败: topic={}, message={}, error={}", topic, message, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean sendTaskMessageAsync(String topic, TaskMessage message) {
        if (!isRocketMQAvailable()) {
            log.warn("⚠️ RocketMQ 消费者不可用，异步消息 [{}] 将不会被处理: {}", topic, message);
            return false;
        }

        try {
            rocketMQTemplate.asyncSend(topic, MessageBuilder.withPayload(message).build(), new SendCallback() {
                @Override
                public void onSuccess(SendResult sendResult) {
                    log.debug("异步发送消息成功: topic={}, message={}, result={}", topic, message, sendResult);
                }

                @Override
                public void onException(Throwable e) {
                    log.error("异步发送消息失败: topic={}, message={}, error={}", topic, message, e.getMessage(), e);
                }
            });
            return true;
        } catch (Exception e) {
            log.error("发送异步消息失败: topic={}, message={}, error={}", topic, message, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public boolean sendTaskMessageDelay(String topic, TaskMessage message, int delayLevel) {
        if (!isRocketMQAvailable()) {
            log.warn("⚠️ RocketMQ 消费者不可用，延迟消息 [{}] 将不会被处理: {}", topic, message);
            return false;
        }

        try {
            SendResult sendResult = rocketMQTemplate.syncSend(topic, MessageBuilder.withPayload(message).build(),
                    rocketMQTemplate.getProducer().getSendMsgTimeout(), delayLevel);
            log.debug("发送延迟消息成功: topic={}, message={}, delayLevel={}, result={}", topic, message, delayLevel, sendResult);
            return true;
        } catch (Exception e) {
            log.error("发送延迟消息失败: topic={}, message={}, delayLevel={}, error={}",
                    topic, message, delayLevel, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 检查 RocketMQ 是否可用
     */
    private boolean isRocketMQAvailable() {
        return rocketMQConsumerEnabled && rocketMQTemplate != null;
    }
}
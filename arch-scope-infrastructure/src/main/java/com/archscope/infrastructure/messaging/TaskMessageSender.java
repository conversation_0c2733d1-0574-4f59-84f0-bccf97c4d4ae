package com.archscope.infrastructure.messaging;

import com.archscope.domain.constants.MessageConstants;
import com.archscope.domain.entity.Task;
import com.archscope.domain.message.TaskMessage;
import com.archscope.domain.service.MessageService;
import com.archscope.domain.task.CodeParseTask;
import com.archscope.domain.task.DocGenerateTask;
import com.archscope.domain.valueobject.TaskType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * 任务消息发送工具类
 * 提供便捷的方法发送各类任务消息
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TaskMessageSender {

    private final MessageService messageService;

    /**
     * 发送代码解析任务消息
     *
     * @param task 任务实体
     * @return 是否发送成功
     */
    public boolean sendCodeParseTaskMessage(Task task) {
        log.info("发送代码解析任务消息: {}", task.getId());
        
        TaskMessage message = buildTaskMessage(task, TaskType.CODE_PARSE);
        return messageService.sendTaskMessage(MessageConstants.TOPIC_CODE_PARSE, message);
    }

    /**
     * 异步发送代码解析任务消息
     *
     * @param task 任务实体
     * @return 是否发送成功
     */
    public boolean sendCodeParseTaskMessageAsync(Task task) {
        log.info("异步发送代码解析任务消息: {}", task.getId());
        
        TaskMessage message = buildTaskMessage(task, TaskType.CODE_PARSE);
        return messageService.sendTaskMessageAsync(MessageConstants.TOPIC_CODE_PARSE, message);
    }

    /**
     * 发送文档生成任务消息
     *
     * @param task 任务实体
     * @return 是否发送成功
     */
    public boolean sendDocGenerateTaskMessage(Task task) {
        log.info("发送文档生成任务消息: {}", task.getId());
        
        TaskMessage message = buildTaskMessage(task, TaskType.DOC_GENERATE);
        return messageService.sendTaskMessage(MessageConstants.TOPIC_DOC_GENERATE, message);
    }

    /**
     * 异步发送文档生成任务消息
     *
     * @param task 任务实体
     * @return 是否发送成功
     */
    public boolean sendDocGenerateTaskMessageAsync(Task task) {
        log.info("异步发送文档生成任务消息: {}", task.getId());
        
        TaskMessage message = buildTaskMessage(task, TaskType.DOC_GENERATE);
        return messageService.sendTaskMessageAsync(MessageConstants.TOPIC_DOC_GENERATE, message);
    }

    /**
     * 延迟发送代码解析任务消息
     *
     * @param task 任务实体
     * @param delayLevel 延迟级别
     * @return 是否发送成功
     */
    public boolean sendCodeParseTaskMessageDelay(Task task, int delayLevel) {
        log.info("延迟发送代码解析任务消息: {}, 延迟级别: {}", task.getId(), delayLevel);
        
        TaskMessage message = buildTaskMessage(task, TaskType.CODE_PARSE);
        return messageService.sendTaskMessageDelay(MessageConstants.TOPIC_CODE_PARSE, message, delayLevel);
    }

    /**
     * 延迟发送文档生成任务消息
     *
     * @param task 任务实体
     * @param delayLevel 延迟级别
     * @return 是否发送成功
     */
    public boolean sendDocGenerateTaskMessageDelay(Task task, int delayLevel) {
        log.info("延迟发送文档生成任务消息: {}, 延迟级别: {}", task.getId(), delayLevel);
        
        TaskMessage message = buildTaskMessage(task, TaskType.DOC_GENERATE);
        return messageService.sendTaskMessageDelay(MessageConstants.TOPIC_DOC_GENERATE, message, delayLevel);
    }

    /**
     * 构建任务消息
     *
     * @param task 任务实体
     * @param taskType 任务类型
     * @return 任务消息
     */
    private TaskMessage buildTaskMessage(Task task, TaskType taskType) {
        return TaskMessage.builder()
                .taskId(task.getId())
                .projectId(task.getProjectId())
                .taskType(taskType)
                .priority(task.getPriority() != null ? task.getPriority() : 1)
                .createdAt(LocalDateTime.now())
                .parameters(task.getParameters())
                .build();
    }

    /**
     * 根据任务类型发送任务消息
     *
     * @param task 任务实体
     * @return 是否发送成功
     */
    public boolean sendTaskMessage(Task task) {
        String taskType = task.getTaskType();
        
        if (CodeParseTask.TASK_TYPE.equals(taskType)) {
            return sendCodeParseTaskMessage(task);
        } else if (DocGenerateTask.TASK_TYPE.equals(taskType)) {
            return sendDocGenerateTaskMessage(task);
        } else {
            log.warn("未知的任务类型: {}", taskType);
            return false;
        }
    }

    /**
     * 根据任务类型异步发送任务消息
     *
     * @param task 任务实体
     * @return 是否发送成功
     */
    public boolean sendTaskMessageAsync(Task task) {
        String taskType = task.getTaskType();
        
        if (CodeParseTask.TASK_TYPE.equals(taskType)) {
            return sendCodeParseTaskMessageAsync(task);
        } else if (DocGenerateTask.TASK_TYPE.equals(taskType)) {
            return sendDocGenerateTaskMessageAsync(task);
        } else {
            log.warn("未知的任务类型: {}", taskType);
            return false;
        }
    }
}

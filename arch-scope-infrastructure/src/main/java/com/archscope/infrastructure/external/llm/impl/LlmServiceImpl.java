package com.archscope.infrastructure.external.llm.impl;

import com.archscope.domain.external.llm.LlmService;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.model.parser.LanguageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * LlmService 接口的基础设施层实现
 */
@Slf4j
@Service
public class LlmServiceImpl implements LlmService {

    @Override
    public FileParseResult parseCodeWithLlm(String filename, String content, String languageType) {
        log.warn("LlmService.parseCodeWithLlm is not implemented yet. Returning dummy data.");
        // TODO: 实现调用真实LLM服务的逻辑
        // 这是一个临时的、失败的响应，以便编译通过
        return FileParseResult.builder()
                .filename(filename)
                .filePath(filename)
                .languageType(LanguageType.fromFilename(filename))
                .successful(false)
                .errorMessage("LLM parsing not implemented")
                .build();
    }
    
    // analyzeDependenciesWithLlm 方法的实现（如果接口中取消注释）
    /*
    @Override
    public List<com.archscope.domain.model.parser.DependencyRelation> analyzeDependenciesWithLlm(List<FileParseResult> parseResults) {
        log.warn("LlmService.analyzeDependenciesWithLlm is not implemented yet. Returning empty list.");
        // TODO: 实现调用真实LLM服务分析依赖的逻辑
        return Collections.emptyList();
    }
    */
} 
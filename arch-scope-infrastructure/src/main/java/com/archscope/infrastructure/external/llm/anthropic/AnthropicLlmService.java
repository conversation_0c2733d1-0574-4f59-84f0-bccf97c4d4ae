package com.archscope.infrastructure.external.llm.anthropic;

import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.model.parser.LanguageType;
import com.archscope.domain.external.llm.LlmService;
import com.archscope.infrastructure.external.llm.parser.LlmResponseParser;
import com.archscope.infrastructure.external.llm.prompt.PromptManager;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

// import com.anthropic.client.AnthropicClient; // Commented out due to dependency issue
// import com.anthropic.models.ContentBlock;
// import com.anthropic.models.MessageParam;
// import com.anthropic.models.messages.Message;
// import com.anthropic.models.messages.MessageCreateParams;
// import com.anthropic.errors.AnthropicApiException;

// import io.github.resilience4j.ratelimiter.RateLimiter;
// import io.github.resilience4j.ratelimiter.RateLimiterConfig;
// import io.github.resilience4j.retry.Retry;
// import io.github.resilience4j.retry.RetryConfig;

import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;

/**
 * 使用Anthropic Claude模型的LlmService实现 (Temporarily Disabled)
 */
@Slf4j
// @Service("anthropicLlmService") // Temporarily disabled
public class AnthropicLlmService implements LlmService {

    private final PromptManager promptManager;
    private final LlmResponseParser responseParser;
    // private final AnthropicClient anthropicClient; // Commented out
    // private final RateLimiter rateLimiter; // Commented out
    // private final Retry retry; // Commented out

    @Value("${llm.anthropic.model:claude-3-5-sonnet-20240620}")
    private String model;

    @Value("${llm.anthropic.rate-limit.requests-per-minute:60}")
    private int requestsPerMinute;

    @Value("${llm.anthropic.retry.max-attempts:3}")
    private int maxRetryAttempts;

    @Value("${llm.anthropic.retry.initial-delay-ms:1000}")
    private long initialRetryDelayMs;

    @Value("${llm.anthropic.retry.max-delay-ms:10000}")
    private long maxRetryDelayMs;

    // Constructor with only dependencies used in the disabled state
    public AnthropicLlmService(PromptManager promptManager, LlmResponseParser responseParser) { 
        this.promptManager = promptManager;
        this.responseParser = responseParser;
        // this.anthropicClient = anthropicClient; // Commented out

        // Configuration for RateLimiter and Retry commented out
        /*
        RateLimiterConfig rateLimiterConfig = RateLimiterConfig.custom()
                .limitRefreshPeriod(Duration.ofMinutes(1))
                .limitForPeriod(requestsPerMinute)
                .timeoutDuration(Duration.ofSeconds(30))
                .build();
        this.rateLimiter = RateLimiter.of("anthropic-api", rateLimiterConfig);

        RetryConfig retryConfig = RetryConfig.custom()
                .maxAttempts(maxRetryAttempts)
                .waitDuration(Duration.ofMillis(initialRetryDelayMs))
                .retryExceptions(AnthropicApiException.class)
                .exponentialBackoff(Duration.ofMillis(initialRetryDelayMs), 2, Duration.ofMillis(maxRetryDelayMs))
                .build();
        this.retry = Retry.of("anthropic-api", retryConfig);
        */
    }

    @Override
    public FileParseResult parseCodeWithLlm(String filename, String content, String languageTypeString) {
        log.warn("AnthropicLlmService is temporarily disabled due to dependency issues. Returning failure.");

        LanguageType languageType = null;
        try {
            languageType = LanguageType.valueOf(languageTypeString.toUpperCase());
        } catch (IllegalArgumentException e) {
            // Ignore
        }

        return FileParseResult.builder()
                .filename(filename)
                .filePath(filename)
                .languageType(languageType)
                .successful(false)
                .errorMessage("Anthropic LLM Service Temporarily Disabled")
                .build();

        // Original logic commented out
        /*
        log.info("Attempting LLM parsing for file: {} with language: {}", filename, languageTypeString);

        LanguageType languageType = null;
        try {
            languageType = LanguageType.valueOf(languageTypeString.toUpperCase());
        } catch (IllegalArgumentException e) {
            log.warn("Unknown language type string: {}. Proceeding with generic prompt.", languageTypeString);
            // languageType remains null, will use generic prompt
        }

        FileParseResult.FileParseResultBuilder errorResultBuilder = FileParseResult.builder()
                .filename(filename)
                .filePath(filename)
                .languageType(languageType)
                .successful(false);

        try {
            String prompt = promptManager.getPrompt(languageType != null ? languageType.name() : "generic", "code_analysis");

            if (prompt == null) {
                String errorMessage = "Could not find prompt for language " + languageTypeString + " and type code_analysis.";
                log.error(errorMessage);
                return errorResultBuilder.errorMessage(errorMessage).build();
            }

            String fullPrompt = promptManager.buildPrompt(prompt, filename, content, languageTypeString);

            log.info("Calling Anthropic API with model: {}", model);
            String llmResponseText = null;

            try {
                Supplier<String> apiCallSupplier = Retry.decorateSupplier(retry, () -> {
                    try {
                        boolean acquired = rateLimiter.tryAcquirePermission();
                        if (!acquired) {
                            log.warn("Rate limit exceeded, waiting for permit...");
                            rateLimiter.acquirePermission();
                        }

                        // Use the actual client instance
                        Message response = anthropicClient.messages().create( // Assuming messages().create() exists
                            MessageCreateParams.builder()
                                .model(model)
                                .maxTokens(4096) // Consider making this configurable
                                .messages(Arrays.asList(
                                    MessageParam.user(fullPrompt) // Use MessageParam.user
                                ))
                                .build()
                        );

                        // Assuming response.content() returns List<ContentBlock>
                        List<ContentBlock> contentBlocks = response.content();
                        if (contentBlocks == null || contentBlocks.isEmpty()) {
                             throw new AnthropicApiException("Received empty content from Anthropic API.");
                        }
                        // Assuming the first block is the text content
                        String text = contentBlocks.get(0).text();
                        log.info("Received response from Anthropic API.");
                        return text;
                    } catch (AnthropicApiException e) {
                        log.warn("Anthropic API call failed, will retry: {}", e.getMessage());
                        throw e; // Re-throw to trigger retry
                    } catch (Exception e) { // Catch other potential runtime exceptions during the call
                        log.error("Unexpected error during Anthropic API interaction: {}", e.getMessage());
                        // Wrap in AnthropicApiException or a specific custom exception if needed for retry logic
                        throw new RuntimeException(e); // Re-throw to potentially trigger retry if configured, or fail
                    }
                });

                llmResponseText = apiCallSupplier.get();
            } catch (AnthropicApiException e) { // Catch exception after retries are exhausted
                log.error("All retry attempts failed for file: {}", filename, e);
                return errorResultBuilder.errorMessage("Anthropic API call failed after " + maxRetryAttempts + " attempts: " + e.getMessage()).build();
            } catch (Exception e) { // Catch other exceptions not handled by retry
                log.error("Unexpected error during Anthropic API call for file: {}", filename, e);
                return errorResultBuilder.errorMessage("Unexpected error during API call: " + e.getMessage()).build();
            }

            if (llmResponseText == null || llmResponseText.trim().isEmpty()) {
                 String errorMessage = "LLM returned an empty response.";
                 log.error(errorMessage);
                 return errorResultBuilder.errorMessage(errorMessage).build();
            }

            // Assuming LlmResponseParser exists and works
            FileParseResult parseResult = responseParser.parse(llmResponseText, filename, filename, languageType);

            return parseResult;

        } catch (Exception e) { // Catch errors during prompt generation etc.
            log.error("Unexpected error during LLM code parsing for file: {}", filename, e);
            return errorResultBuilder.errorMessage("Unexpected error during LLM code parsing: " + e.getMessage()).build();
        }
        */
    }
}
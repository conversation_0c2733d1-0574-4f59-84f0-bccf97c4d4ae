package com.archscope.infrastructure.external.llm.anthropic;

import lombok.Builder;
import lombok.Data;

/**
 * 重试配置
 */
@Data
@Builder
public class RetryConfig {
    /**
     * 最大重试次数
     */
    private final int maxAttempts;

    /**
     * 初始延迟（毫秒）
     */
    private final long initialDelayMs;

    /**
     * 最大延迟（毫秒）
     */
    private final long maxDelayMs;

    /**
     * 退避乘数
     */
    private final double multiplier;

    /**
     * 默认配置
     */
    public static RetryConfig getDefault() {
        return RetryConfig.builder()
                .maxAttempts(3)
                .initialDelayMs(1000)
                .maxDelayMs(10000)
                .multiplier(2.0)
                .build();
    }
}
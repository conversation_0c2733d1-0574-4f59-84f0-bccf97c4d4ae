package com.archscope.infrastructure.external.llm.parser;

import com.archscope.domain.model.parser.ClassDefinition;
import com.archscope.domain.model.parser.DependencyRelation;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.model.parser.LanguageType;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * LLM响应解析器，负责将LLM返回的JSON文本解析为FileParseResult对象。
 * 支持多种格式的JSON响应，包括直接的FileParseResult格式、嵌套在其他对象中的格式，
 * 以及从Markdown代码块中提取JSON的能力。
 */
@Component
@Slf4j
public class LlmResponseParser {

    private static final Logger log = LoggerFactory.getLogger(LlmResponseParser.class);
    private final ObjectMapper objectMapper;
    
    // 用于从Markdown代码块中提取JSON的正则表达式
    private static final Pattern JSON_CODE_BLOCK_PATTERN = Pattern.compile("```(?:json)?\\s*\\n(\\{.*?\\})\\s*```", Pattern.DOTALL);
    
    // 用于从LLM响应中提取JSON对象的正则表达式（处理没有代码块但直接返回JSON的情况）
    private static final Pattern JSON_OBJECT_PATTERN = Pattern.compile("\\{\\s*\".*?\"\\s*:.*\\}", Pattern.DOTALL);

    public LlmResponseParser(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 解析LLM返回的JSON文本。
     * 该方法能够处理多种格式的JSON响应，包括：
     * 1. 直接符合FileParseResult格式的JSON
     * 2. 嵌套在其他对象中的FileParseResult（如fileParseResult字段）
     * 3. 从Markdown代码块中提取的JSON
     * 4. 其他格式的JSON，尝试映射到FileParseResult
     *
     * @param llmResponse LLM返回的文本（可能包含JSON或Markdown）
     * @param filename 文件名
     * @param filePath 文件路径
     * @param languageType 语言类型
     * @return 解析后的FileParseResult对象，如果解析失败则返回包含错误信息的FileParseResult
     */
    public FileParseResult parse(String llmResponse, String filename, String filePath, LanguageType languageType) {
        if (llmResponse == null || llmResponse.trim().isEmpty()) {
            log.error("Empty LLM response for file: {}", filename);
            return createErrorResult(filename, filePath, languageType, "Empty LLM response");
        }

        log.debug("Parsing LLM response for file: {}", filename);
        
        // 尝试从响应中提取JSON
        String jsonText = extractJsonFromResponse(llmResponse);
        if (jsonText == null) {
            log.error("Could not extract JSON from LLM response for file: {}", filename);
            return createErrorResult(filename, filePath, languageType, "Could not extract JSON from LLM response");
        }

        try {
            // 首先尝试直接解析为FileParseResult
            try {
                FileParseResult result = objectMapper.readValue(jsonText, FileParseResult.class);
                // 验证结果的完整性
                if (!validateParseResult(result)) {
                    log.debug("Direct parsing succeeded but validation failed for file: {}", filename);
                    return createErrorResult(filename, filePath, languageType, "Invalid parse result structure");
                }
                return completeResult(result, filename, filePath, languageType);
            } catch (Exception e) {
                log.debug("Direct parsing to FileParseResult failed, trying alternative formats: {}", e.getMessage());
            }

            // 尝试解析为JsonNode
            JsonNode rootNode = objectMapper.readTree(jsonText);
            
            // 检查是否有fileParseResult字段
            if (rootNode.has("fileParseResult")) {
                try {
                    FileParseResult result = objectMapper.treeToValue(rootNode.get("fileParseResult"), FileParseResult.class);
                    if (!validateParseResult(result)) {
                        log.debug("Nested fileParseResult parsing succeeded but validation failed for file: {}", filename);
                        return createErrorResult(filename, filePath, languageType, "Invalid nested parse result structure");
                    }
                    return completeResult(result, filename, filePath, languageType);
                } catch (Exception e) {
                    log.debug("Parsing fileParseResult field failed: {}", e.getMessage());
                }
            }
            
            // 尝试从其他格式映射到FileParseResult
            return mapToFileParseResult(rootNode, filename, filePath, languageType);
            
        } catch (Exception e) {
            log.error("Failed to parse LLM JSON response for file {}: {}", filename, e.getMessage());
            return createErrorResult(filename, filePath, languageType, "Failed to parse LLM response: " + e.getMessage());
        }
    }
    
    /**
     * 从LLM响应中提取JSON字符串
     */
    private String extractJsonFromResponse(String response) {
        if (response == null || response.trim().isEmpty()) {
            return null;
        }

        try {
            // 首先尝试直接解析整个响应
            objectMapper.readTree(response);
            return response;
        } catch (Exception e) {
            // 如果直接解析失败，尝试提取JSON部分
            log.debug("Direct JSON parsing failed, trying to extract JSON from response");
        }

        // 查找JSON开始和结束标记
        int startIndex = -1;
        int endIndex = -1;
        int curlyBraceCount = 0;
        boolean inJson = false;

        char[] chars = response.toCharArray();
        for (int i = 0; i < chars.length; i++) {
            char c = chars[i];
            
            if (c == '{') {
                if (!inJson) {
                    startIndex = i;
                    inJson = true;
                }
                curlyBraceCount++;
            } else if (c == '}' && inJson) {
                curlyBraceCount--;
                if (curlyBraceCount == 0) {
                    endIndex = i + 1;
                    // 尝试验证提取的JSON
                    String potentialJson = response.substring(startIndex, endIndex);
                    try {
                        objectMapper.readTree(potentialJson);
                        return potentialJson;
                    } catch (Exception e) {
                        // 如果验证失败，继续寻找
                        inJson = false;
                        log.debug("Found invalid JSON structure, continuing search");
                    }
                }
            }
        }

        // 如果没有找到有效的JSON，尝试使用正则表达式
        Pattern jsonPattern = Pattern.compile("\\{[^}]*\\}");
        Matcher matcher = jsonPattern.matcher(response);
        
        while (matcher.find()) {
            String potentialJson = matcher.group();
            try {
                objectMapper.readTree(potentialJson);
                return potentialJson;
            } catch (Exception e) {
                log.debug("Found invalid JSON via regex: {}", e.getMessage());
            }
        }

        log.error("No valid JSON found in response");
        return null;
    }
    
    /**
     * 将任意JSON结构映射到FileParseResult
     * 处理各种可能的字段名和结构
     */
    private FileParseResult mapToFileParseResult(JsonNode rootNode, String filename, String filePath, LanguageType languageType) {
        List<ClassDefinition> classDefinitions = new ArrayList<>();
        List<String> imports = new ArrayList<>();
        List<DependencyRelation> dependencies = new ArrayList<>();
        String packageName = null;
        String fileComment = null;
        boolean successful = true; // 默认为成功
        String errorMessage = null;

        try {
            // 提取基本字段
            if (rootNode.has("packageName")) {
                packageName = rootNode.get("packageName").asText();
            }
            
            if (rootNode.has("fileComment")) {
                fileComment = rootNode.get("fileComment").asText();
            }

            // 提取导入语句
            if (rootNode.has("imports")) {
                JsonNode importsNode = rootNode.get("imports");
                if (importsNode.isArray()) {
                    for (JsonNode importNode : importsNode) {
                        imports.add(importNode.asText());
                    }
                }
            }

            // 提取类定义
            if (rootNode.has("classDefinitions")) {
                JsonNode classesNode = rootNode.get("classDefinitions");
                if (classesNode.isArray()) {
                    for (JsonNode classNode : classesNode) {
                        try {
                            ClassDefinition classDef = objectMapper.treeToValue(classNode, ClassDefinition.class);
                            if (classDef != null) {
                                // 显式处理modifiers字段
                                if (classNode.has("modifiers") && classNode.get("modifiers").isArray()) {
                                    List<String> modifiers = new ArrayList<>();
                                    JsonNode modifiersNode = classNode.get("modifiers");
                                    for (JsonNode modifierNode : modifiersNode) {
                                        modifiers.add(modifierNode.asText());
                                    }
                                    classDef.setModifiers(modifiers); // 显式调用setModifiers方法
                                }
                                
                                // 处理方法的modifiers
                                if (classNode.has("methods") && classNode.get("methods").isArray()) {
                                    JsonNode methodsNode = classNode.get("methods");
                                    for (int i = 0; i < methodsNode.size() && i < classDef.getMethods().size(); i++) {
                                        JsonNode methodNode = methodsNode.get(i);
                                        if (methodNode.has("modifiers") && methodNode.get("modifiers").isArray()) {
                                            List<String> methodModifiers = new ArrayList<>();
                                            JsonNode modifiersNode = methodNode.get("modifiers");
                                            for (JsonNode modifierNode : modifiersNode) {
                                                methodModifiers.add(modifierNode.asText());
                                            }
                                            classDef.getMethods().get(i).setModifiers(methodModifiers);
                                        }
                                    }
                                }
                                
                                // 处理字段的modifiers
                                if (classNode.has("fields") && classNode.get("fields").isArray()) {
                                    JsonNode fieldsNode = classNode.get("fields");
                                    for (int i = 0; i < fieldsNode.size() && i < classDef.getFields().size(); i++) {
                                        JsonNode fieldNode = fieldsNode.get(i);
                                        if (fieldNode.has("modifiers") && fieldNode.get("modifiers").isArray()) {
                                            List<String> fieldModifiers = new ArrayList<>();
                                            JsonNode modifiersNode = fieldNode.get("modifiers");
                                            for (JsonNode modifierNode : modifiersNode) {
                                                fieldModifiers.add(modifierNode.asText());
                                            }
                                            classDef.getFields().get(i).setModifiers(fieldModifiers);
                                        }
                                    }
                                }
                                
                                if (classDef.getName() != null) {
                                    classDefinitions.add(classDef);
                                } else {
                                    log.warn("Parsed ClassDefinition for node {} but name is null. ClassNode: {}", 
                                             classNode.get("name") != null ? classNode.get("name").asText("N/A") : "N/A", 
                                             classNode.toString());
                                }
                            } else {
                                log.warn("Failed to parse ClassDefinition from ClassNode (result was null). ClassNode: {}", 
                                         classNode.toString());
                            }
                        } catch (Exception e) {
                            log.debug("Failed to parse class definition: {}", e.getMessage(), e);
                        }
                    }
                }
            }

            // 提取依赖关系
            if (rootNode.has("dependencies")) {
                JsonNode dependenciesNode = rootNode.get("dependencies");
                if (dependenciesNode.isArray()) {
                    for (JsonNode depNode : dependenciesNode) {
                        try {
                            DependencyRelation dep = objectMapper.treeToValue(depNode, DependencyRelation.class);
                            if (dep != null && dep.getSourceClass() != null && dep.getTargetClass() != null) {
                                dependencies.add(dep);
                            }
                        } catch (Exception e) {
                            log.debug("Failed to parse dependency relation: {}", e.getMessage());
                        }
                    }
                }
            }

            // 放宽验证条件，只在完全无法解析时才标记为失败
            if (classDefinitions.isEmpty() && imports.isEmpty() && dependencies.isEmpty()) {
                successful = false;
                errorMessage = "No valid content found in parse result";
            }
            
        } catch (Exception e) {
            successful = false;
            errorMessage = "Failed to map JSON to FileParseResult: " + e.getMessage();
            log.error("Error mapping JSON to FileParseResult: {}", e.getMessage());
        }

        return FileParseResult.builder()
                .filename(filename)
                .filePath(filePath)
                .languageType(languageType)
                .packageName(packageName)
                .fileComment(fileComment)
                .imports(imports)
                .classDefinitions(classDefinitions)
                .dependencies(dependencies)
                .successful(successful)
                .errorMessage(errorMessage)
                .build();
    }
    
    /**
     * 验证解析结果的完整性
     */
    private boolean validateParseResult(FileParseResult result) {
        if (result == null) {
            return false;
        }

        // 检查基本字段
        if (result.getFilename() == null && result.getFilePath() == null && result.getLanguageType() == null) {
            // 只有当这三个字段全部缺失时才认为无效
            return false;
        }

        // 检查集合字段不为null（但允许为空）
        if (result.getClassDefinitions() == null || result.getImports() == null || result.getDependencies() == null) {
            // 如果有任一集合为null，则认为无效（但允许集合为空）
            return false;
        }

        // 针对Java和其他主要编程语言的文件，应该至少有类定义或导入语句
        if (result.getLanguageType() == LanguageType.JAVA ||
            result.getLanguageType() == LanguageType.JAVASCRIPT ||
            result.getLanguageType() == LanguageType.PYTHON) {
            
            // 如果没有类定义，且没有导入语句，且没有依赖关系
            if (result.getClassDefinitions().isEmpty() && 
                result.getImports().isEmpty() && 
                result.getDependencies().isEmpty()) {
                return false;
            }
            
            // 检查类定义的有效性，只在有类定义时进行检查
            if (!result.getClassDefinitions().isEmpty()) {
                for (ClassDefinition classDef : result.getClassDefinitions()) {
                    if (classDef.getName() == null || classDef.getName().isEmpty()) {
                        // 如果有任一类定义没有名称，则认为无效
                        return false;
                    }
                }
            }
        }

        return true;
    }
    
    /**
     * 补充和规范化解析结果
     */
    private FileParseResult completeResult(FileParseResult result, String filename, String filePath, LanguageType languageType) {
        // 确保基本字段存在
        if (result.getFilename() == null) {
            result.setFilename(filename);
        }
        if (result.getFilePath() == null) {
            result.setFilePath(filePath);
        }
        if (result.getLanguageType() == null) {
            result.setLanguageType(languageType);
        }

        // 确保集合字段不为null
        if (result.getClassDefinitions() == null) {
            result.setClassDefinitions(new ArrayList<>());
        }
        if (result.getImports() == null) {
            result.setImports(new ArrayList<>());
        }
        if (result.getDependencies() == null) {
            result.setDependencies(new ArrayList<>());
        }

        // 默认设置为成功，除非明确验证失败
        boolean isValid = validateParseResult(result);
        result.setSuccessful(isValid);
        if (!isValid && (result.getErrorMessage() == null || result.getErrorMessage().isEmpty())) {
            result.setErrorMessage("Invalid parse result structure");
        }

        return result;
    }
    
    /**
     * 创建错误结果对象
     */
    private FileParseResult createErrorResult(String filename, String filePath, LanguageType languageType, String errorMessage) {
        return FileParseResult.builder()
                .filename(filename)
                .filePath(filePath)
                .languageType(languageType)
                .successful(false)
                .errorMessage(errorMessage)
                .classDefinitions(new ArrayList<>())
                .imports(new ArrayList<>())
                .dependencies(new ArrayList<>())
                .build();
    }
}

package com.archscope.infrastructure.external.llm.prompt;

import com.archscope.domain.model.parser.LanguageType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;
import org.yaml.snakeyaml.Yaml;

import javax.annotation.PostConstruct;
import java.io.IOException;
import java.io.InputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * 提示词管理器，负责加载和管理不同语言和类型的提示词模板。
 */
@Component
@Slf4j
public class PromptManager {

    private final ResourceLoader resourceLoader;

    @Value("classpath*:prompts/**/*.yaml")
    private Resource[] promptResources;

    private final Map<String, Map<String, String>> prompts = new HashMap<>();

    public PromptManager(ResourceLoader resourceLoader) {
        this.resourceLoader = resourceLoader;
    }

    @PostConstruct
    public void loadPrompts() {
        log.info("Loading prompts from classpath resources...");
        if (promptResources == null || promptResources.length == 0) {
            log.warn("No prompt resources found on classpath.");
            return;
        }

        Yaml yaml = new Yaml();

        for (Resource resource : promptResources) {
            try (InputStream is = resource.getInputStream()) {
                Map<String, Object> data = yaml.load(is);
                if (data != null && data.containsKey("prompts")) {
                    String filename = resource.getFilename();
                    // Extract language/type from filename, e.g., java_analysis.yaml -> java
                    String languageOrType = "generic"; // Default to generic
                    if (filename != null && filename.contains("_")) {
                         languageOrType = filename.substring(0, filename.indexOf("_"));
                    }

                    Map<String, Object> promptMap = (Map<String, Object>) data.get("prompts");
                    Map<String, String> languagePrompts = new HashMap<>();

                    for (Map.Entry<String, Object> entry : promptMap.entrySet()) {
                        String promptName = entry.getKey();
                        Map<String, Object> promptDetails = (Map<String, Object>) entry.getValue();
                        if (promptDetails.containsKey("template")) {
                            languagePrompts.put(promptName, (String) promptDetails.get("template"));
                        }
                    }
                    prompts.put(languageOrType, languagePrompts);
                    log.info("Loaded prompts for {}: {}", languageOrType, languagePrompts.keySet());
                }
            } catch (IOException e) {
                log.error("Failed to load prompt resource: {}", resource.getFilename(), e);
            } catch (Exception e) {
                log.error("Error parsing prompt resource: {}", resource.getFilename(), e);
            }
        }
        log.info("Finished loading prompts. Total languages/types loaded: {}", prompts.size());
    }

    /**
     * 获取指定语言和类型的提示词模板。
     *
     * @param languageType 语言类型 (e.g., "java", "javascript", "generic")
     * @param promptType 提示词类型 (e.g., "code_structure", "api_extraction")
     * @return 提示词模板字符串，如果未找到则返回null
     */
    public String getPrompt(String languageType, String promptType) {
        Map<String, String> languagePrompts = prompts.get(languageType.toLowerCase());
        if (languagePrompts != null) {
            String prompt = languagePrompts.get(promptType);
            if (prompt != null) {
                return prompt;
            } else {
                log.warn("Prompt type '{}' not found for language '{}'.", promptType, languageType);
            }
        } else {
            log.warn("Prompts not found for language '{}'.", languageType);
        }
        // Fallback to generic if specific language/type not found
        if (!languageType.equalsIgnoreCase("generic")) {
             log.info("Falling back to generic prompt for language '{}', type '{}'.", languageType, promptType);
             return getPrompt("generic", promptType);
        }
        return null;
    }

    /**
     * 构建完整的提示词，将代码内容填充到模板中。
     *
     * @param template 提示词模板
     * @param filename 文件名
     * @param content 代码内容
     * @param languageType 语言类型
     * @return 填充后的完整提示词
     */
    public String buildPrompt(String template, String filename, String content, String languageType) {
        String prompt = template.replace("{{file_path}}", filename);
        prompt = prompt.replace("{{file_type}}", languageType);
        prompt = prompt.replace("{{language}}", languageType.toLowerCase());
        prompt = prompt.replace("{{code}}", content);
        return prompt;
    }
}

package com.archscope.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

import java.util.concurrent.atomic.AtomicBoolean;

/**
 * RocketMQ 健康检查配置
 */
@Slf4j
@Configuration
public class RocketMQHealthCheckConfig {

    @Value("${rocketmq.name-server:localhost:9876}")
    private String nameServer;

    @Value("${rocketmq.producer.group:arch-scope-producer}")
    private String producerGroup;

    // 全局 RocketMQ 可用状态标志
    private static final AtomicBoolean ROCKET_MQ_AVAILABLE = new AtomicBoolean(false);

    /**
     * 在应用启动时检查 RocketMQ 是否可用
     */
    @Bean
    @Order(Integer.MAX_VALUE) // 最后执行，不影响其他组件启动
    public CommandLineRunner rocketMQHealthCheck() {
        return args -> {
            // 应用已经启动，现在检查 RocketMQ 是否可用
            try {
                // 创建一个临时生产者进行连接测试
                DefaultMQProducer producer = new DefaultMQProducer(producerGroup + "-health-check");
                producer.setNamesrvAddr(nameServer);
                producer.setInstanceName("health-check-instance");
                producer.setVipChannelEnabled(false);
                producer.setRetryTimesWhenSendFailed(1);
                producer.setSendMsgTimeout(3000);

                try {
                    producer.start();
                    // 获取当前生产者的路由信息，这会验证与名称服务器的连接
                    producer.getDefaultMQProducerImpl().getmQClientFactory().updateTopicRouteInfoFromNameServer("HEALTH_CHECK_TOPIC");

                    // 连接成功
                    ROCKET_MQ_AVAILABLE.set(true);
                    log.info("✅ RocketMQ 服务可用，连接成功");
                } catch (Exception e) {
                    ROCKET_MQ_AVAILABLE.set(false);
                    log.warn("⚠️ RocketMQ 服务不可用: {}", e.getMessage());
                    log.debug("RocketMQ 连接异常详情", e);
                } finally {
                    // 关闭临时生产者
                    try {
                        producer.shutdown();
                    } catch (Exception ignore) {
                        // 忽略关闭时的异常
                    }
                }
            } catch (Exception e) {
                ROCKET_MQ_AVAILABLE.set(false);
                log.warn("⚠️ 检查 RocketMQ 健康状态时发生错误: {}", e.getMessage());
            }
        };
    }

    /**
     * 获取 RocketMQ 可用状态
     */
    public static boolean isRocketMQAvailable() {
        return ROCKET_MQ_AVAILABLE.get();
    }

    /**
     * 设置 RocketMQ 可用状态
     */
    public static void setRocketMQAvailable(boolean available) {
        ROCKET_MQ_AVAILABLE.set(available);
    }
}
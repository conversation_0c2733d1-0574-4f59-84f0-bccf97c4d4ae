package com.archscope.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.annotation.SelectorType;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanNameGenerator;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.core.type.filter.TypeFilter;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;
import java.util.Map;

/**
 * 条件化的 RocketMQ 消费者配置
 * 允许在 RocketMQ 不可用时有条件地启用或禁用消费者
 */
@Slf4j
@Component
public class ConditionalRocketMQConsumerConfig {

    /**
     * 条件化 RocketMQ 消费者注解
     * 用于标记可以在 RocketMQ 不可用时被跳过的消费者
     */
    @Target(ElementType.TYPE)
    @Retention(RetentionPolicy.RUNTIME)
    @Documented
    public @interface ConditionalRocketMQConsumer {
        /**
         * RocketMQ 消息监听器配置
         */
        RocketMQMessageListener value();

        /**
         * 是否允许在 RocketMQ 不可用时跳过此消费者
         * 默认为 true
         */
        boolean skipIfUnavailable() default true;
    }

    /**
     * 导入 RocketMQ 消费者定义的注册器
     */
    public static class ConditionalRocketMQConsumerRegistrar implements ImportBeanDefinitionRegistrar {
        private final TypeFilter rocketMQConsumerFilter = new AnnotationTypeFilter(ConditionalRocketMQConsumer.class);

        @Override
        public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry, BeanNameGenerator nameGenerator) {
            // 检查 RocketMQ 是否可用
            boolean rocketMQAvailable = RocketMQHealthCheckConfig.isRocketMQAvailable();

            if (!rocketMQAvailable) {
                // 如果 RocketMQ 不可用，遍历所有带有 @ConditionalRocketMQConsumer 注解的 bean 定义
                for (String beanName : registry.getBeanDefinitionNames()) {
                    BeanDefinition beanDefinition = registry.getBeanDefinition(beanName);
                    String beanClassName = beanDefinition.getBeanClassName();

                    if (beanClassName != null) {
                        try {
                            Class<?> beanClass = Class.forName(beanClassName);
                            ConditionalRocketMQConsumer annotation = beanClass.getAnnotation(ConditionalRocketMQConsumer.class);

                            if (annotation != null && annotation.skipIfUnavailable()) {
                                // 从注册表中移除此消费者
                                registry.removeBeanDefinition(beanName);
                                log.info("已禁用 RocketMQ 消费者 [{}]，因为 RocketMQ 服务不可用", beanName);
                            }
                        } catch (ClassNotFoundException e) {
                            // 忽略
                        }
                    }
                }
            }
        }
    }
}
package com.archscope.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * RocketMQ 容错配置
 * 当 RocketMQ 服务不可用时，允许应用正常启动
 */
@Slf4j
@Configuration
public class RocketMQFaultTolerantConfig {

    private final AtomicBoolean rocketMQAvailable = new AtomicBoolean(true);
    private final Environment environment;

    public RocketMQFaultTolerantConfig(Environment environment) {
        this.environment = environment;
    }

    /**
     * 拦截 RocketMQ 监听器容器的初始化，捕获连接异常并提供降级处理
     */
    @Bean
    @Primary
    public BeanPostProcessor rocketMQListenerContainerPostProcessor() {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
                return bean;
            }

            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
                // 获取原始类，处理可能的代理类
                Class<?> targetClass = AopProxyUtils.ultimateTargetClass(bean);

                // 只处理 RocketMQ 监听器容器
                if (bean instanceof DefaultRocketMQListenerContainer) {
                    // 创建一个新的容错容器来替换原始容器
                    DefaultRocketMQListenerContainer originalContainer = (DefaultRocketMQListenerContainer) bean;
                    FaultTolerantListenerContainer faultTolerantContainer = new FaultTolerantListenerContainer();

                    // 复制原始容器的配置到新容器
                    try {
                        // 获取原始容器的 RocketMQMessageListener 注解
                        RocketMQMessageListener annotation = originalContainer.getRocketMQMessageListener();

                        // 使用反射设置新容器的属性
                        Method setRocketMQMessageListenerMethod = ReflectionUtils.findMethod(
                                DefaultRocketMQListenerContainer.class, "setRocketMQMessageListener",
                                RocketMQMessageListener.class);
                        if (setRocketMQMessageListenerMethod != null) {
                            setRocketMQMessageListenerMethod.setAccessible(true);
                            ReflectionUtils.invokeMethod(setRocketMQMessageListenerMethod, faultTolerantContainer, annotation);
                        }

                        // 复制其他必要的属性
                        copyContainerProperties(originalContainer, faultTolerantContainer);

                        log.info("已将 RocketMQ 监听器容器替换为容错版本: 消费者组={}, 主题={}",
                                annotation.consumerGroup(), annotation.topic());

                        // 返回容错版本的容器
                        return faultTolerantContainer;
                    } catch (Exception ex) {
                        log.error("替换 RocketMQ 监听器容器时发生错误", ex);
                    }
                }

                return bean;
            }

            /**
             * 复制容器属性
             */
            private void copyContainerProperties(DefaultRocketMQListenerContainer source,
                                               DefaultRocketMQListenerContainer target) {
                try {
                    // 复制必要的属性
                    copyProperty(source, target, "objectMapper");
                    copyProperty(source, target, "messageConverter");
                    copyProperty(source, target, "rocketMQTemplate");
                    copyProperty(source, target, "consumerGroup");
                    copyProperty(source, target, "nameServer");
                    copyProperty(source, target, "topic");
                    copyProperty(source, target, "consumeMode");
                    copyProperty(source, target, "messageModel");
                    copyProperty(source, target, "selectorType");
                    copyProperty(source, target, "selectorExpression");
                    copyProperty(source, target, "enableMsgTrace");
                    copyProperty(source, target, "customizedTraceTopic");
                } catch (Exception e) {
                    log.warn("复制 RocketMQ 监听器容器属性时发生错误", e);
                }
            }

            /**
             * 使用反射复制单个属性
             */
            private void copyProperty(Object source, Object target, String propertyName) {
                try {
                    Field field = ReflectionUtils.findField(DefaultRocketMQListenerContainer.class, propertyName);
                    if (field != null) {
                        field.setAccessible(true);
                        Object value = field.get(source);
                        field.set(target, value);
                    }
                } catch (Exception e) {
                    log.debug("复制属性 {} 时发生错误: {}", propertyName, e.getMessage());
                }
            }
        };
    }

    /**
     * 配置 RocketMQ 监听器容器工厂的自定义异常处理
     * 通过环境属性控制是否启用容错模式
     */
    @Bean
    @ConditionalOnProperty(name = "rocketmq.fault-tolerant", havingValue = "true", matchIfMissing = true)
    public RocketMQFaultTolerantProcessor rocketMQFaultTolerantProcessor() {
        return new RocketMQFaultTolerantProcessor();
    }

    /**
     * RocketMQ 容错处理器，处理监听器容器初始化时的异常
     */
    public static class RocketMQFaultTolerantProcessor {
    }

    /**
     * 容错版本的 RocketMQ 监听器容器
     * 捕获启动异常并记录警告日志，但不抛出异常，允许应用继续启动
     */
    @Slf4j
    public static class FaultTolerantListenerContainer extends DefaultRocketMQListenerContainer {
        @Override
        public void start() {
            try {
                super.start();
            } catch (Exception e) {
                // 记录错误但不抛出，允许应用继续启动
                log.warn("⚠️ RocketMQ 消费者 [{}] 启动失败，将不会接收消息: {}",
                        getRocketMQMessageListener().consumerGroup(), e.getMessage());
                log.debug("RocketMQ 消费者启动异常详情", e);
            }
        }

        @Override
        public void afterPropertiesSet() throws Exception {
            try {
                super.afterPropertiesSet();
            } catch (Exception e) {
                // 记录错误但不抛出，允许应用继续启动
                log.warn("⚠️ RocketMQ 消费者 [{}] 初始化失败，将不会接收消息: {}",
                        getRocketMQMessageListener().consumerGroup(), e.getMessage());
                log.debug("RocketMQ 消费者初始化异常详情", e);
            }
        }
    }
}
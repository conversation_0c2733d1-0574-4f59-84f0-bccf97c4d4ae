package com.archscope.infrastructure.config.typehandler;

import com.archscope.domain.valueobject.ProjectConfiguration;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * ProjectConfiguration类型处理器
 * 用于处理ProjectConfiguration类型的字段，将其转换为JSON字符串存储到数据库
 */
public class ProjectConfigurationTypeHandler extends BaseTypeHandler<ProjectConfiguration> {

    private static final Logger logger = LoggerFactory.getLogger(ProjectConfigurationTypeHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, ProjectConfiguration parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = objectMapper.writeValueAsString(parameter);
            ps.setString(i, json);
        } catch (JsonProcessingException e) {
            logger.error("Error converting ProjectConfiguration to JSON", e);
            // 设置默认配置
            ps.setString(i, getDefaultConfigurationJson());
        }
    }

    @Override
    public ProjectConfiguration getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJson(rs.getString(columnName));
    }

    @Override
    public ProjectConfiguration getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJson(rs.getString(columnIndex));
    }

    @Override
    public ProjectConfiguration getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJson(cs.getString(columnIndex));
    }

    private ProjectConfiguration parseJson(String json) {
        if (json == null || json.trim().isEmpty()) {
            return createDefaultConfiguration();
        }

        try {
            return objectMapper.readValue(json, ProjectConfiguration.class);
        } catch (JsonProcessingException e) {
            logger.error("Error parsing JSON to ProjectConfiguration: {}", json, e);
            return createDefaultConfiguration();
        }
    }

    private ProjectConfiguration createDefaultConfiguration() {
        return ProjectConfiguration.builder()
                .buildTool("Maven")
                .language("Java")
                .framework("Spring Boot")
                .enableDocGeneration(true)
                .enableCodeAnalysis(true)
                .accessControl("PRIVATE")
                .build();
    }

    private String getDefaultConfigurationJson() {
        try {
            return objectMapper.writeValueAsString(createDefaultConfiguration());
        } catch (JsonProcessingException e) {
            logger.error("Error creating default configuration JSON", e);
            return "{\"buildTool\":\"Maven\",\"language\":\"Java\",\"framework\":\"Spring Boot\",\"enableDocGeneration\":true,\"enableCodeAnalysis\":true,\"accessControl\":\"PRIVATE\"}";
        }
    }
}

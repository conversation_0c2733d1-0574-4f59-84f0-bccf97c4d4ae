package com.archscope.infrastructure.config.typehandler;

import com.archscope.domain.valueobject.ProjectType;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * ProjectType枚举类型处理器
 * 用于处理ProjectType枚举与数据库字符串之间的转换
 */
public class ProjectTypeHandler extends BaseTypeHandler<ProjectType> {

    private static final Logger logger = LoggerFactory.getLogger(ProjectTypeHandler.class);

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, ProjectType parameter, JdbcType jdbcType) throws SQLException {
        // 将枚举转换为字符串存储到数据库
        ps.setString(i, parameter.name());
    }

    @Override
    public ProjectType getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String value = rs.getString(columnName);
        return parseProjectType(value);
    }

    @Override
    public ProjectType getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String value = rs.getString(columnIndex);
        return parseProjectType(value);
    }

    @Override
    public ProjectType getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String value = cs.getString(columnIndex);
        return parseProjectType(value);
    }

    /**
     * 解析字符串为ProjectType枚举
     * 如果解析失败，返回OTHER类型
     */
    private ProjectType parseProjectType(String value) {
        if (value == null || value.trim().isEmpty()) {
            return ProjectType.OTHER;
        }

        try {
            return ProjectType.valueOf(value.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            logger.warn("无法解析项目类型: {}, 使用默认类型 OTHER", value);
            return ProjectType.OTHER;
        }
    }
}

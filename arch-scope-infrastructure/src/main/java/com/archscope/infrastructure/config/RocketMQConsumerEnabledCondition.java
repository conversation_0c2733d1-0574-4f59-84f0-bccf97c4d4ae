package com.archscope.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Condition;
import org.springframework.context.annotation.ConditionContext;
import org.springframework.core.type.AnnotatedTypeMetadata;
import org.springframework.stereotype.Component;

/**
 * RocketMQ 消费者启用条件
 * 只有当 RocketMQ 服务可用时才启用消费者
 */
@Slf4j
@Component
public class RocketMQConsumerEnabledCondition implements Condition {

    @Override
    public boolean matches(ConditionContext context, AnnotatedTypeMetadata metadata) {
        String enabled = context.getEnvironment().getProperty("rocketmq.consumer.enabled");
        boolean result = Boolean.parseBoolean(enabled);
        if (!result) {
            log.debug("RocketMQ 消费者被禁用: rocketmq.consumer.enabled = {}", enabled);
        }
        return result;
    }
}
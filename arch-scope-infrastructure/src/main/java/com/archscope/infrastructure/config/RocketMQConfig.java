package com.archscope.infrastructure.config;

import com.archscope.infrastructure.messaging.FallbackRocketMQTemplate;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.autoconfigure.RocketMQAutoConfiguration;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Configuration
@AutoConfigureAfter(RocketMQAutoConfiguration.class)
public class RocketMQConfig {

    // 需要禁用的 RocketMQ 相关 Bean 名称列表
    private static final List<String> ROCKETMQ_LISTENER_RELATED_BEANS = Arrays.asList(
            "org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor",
            "org.apache.rocketmq.spring.support.RocketMQMessageListenerContainerRegistrar",
            "rocketMQListenerContainerFactory"
    );

    /**
     * 当 RocketMQ 模板创建失败时，提供一个降级的 RocketMQTemplate 实现
     */
    @Bean("fallbackRocketMQTemplate")
    @Primary
    @ConditionalOnMissingBean(name = "rocketMQTemplate")
    public RocketMQTemplate fallbackRocketMQTemplate() {
        log.warn("⚠️ 无法连接到 RocketMQ 服务器，消息功能将不可用，但不影响系统启动");
        // 返回一个无操作的 RocketMQTemplate 代理
        return new FallbackRocketMQTemplate();
    }

    /**
     * 配置是否启用RocketMQ消费者
     * 默认为true，可通过配置文件中的rocketmq.consumer.enabled=false来禁用
     */
    @Bean
    @ConditionalOnProperty(name = "rocketmq.consumer.enabled", havingValue = "false")
    public BeanFactoryPostProcessor disableRocketMQListeners() {
        return new BeanFactoryPostProcessor() {
            @Override
            public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
                log.info("禁用 RocketMQ 消费者功能");

                // 禁用所有 RocketMQ 监听器相关的 Bean
                for (String beanName : ROCKETMQ_LISTENER_RELATED_BEANS) {
                    if (beanFactory.containsBeanDefinition(beanName)) {
                        BeanDefinition bd = beanFactory.getBeanDefinition(beanName);
                        // 将 Bean 设置为非自动启动
                        bd.setLazyInit(true);
                        // 将 Bean 角色设置为基础设施，降低优先级
                        bd.setRole(BeanDefinition.ROLE_INFRASTRUCTURE);
                    }
                }

                // 移除所有带有 @RocketMQMessageListener 注解的消费者 Bean
                for (String beanName : beanFactory.getBeanDefinitionNames()) {
                    BeanDefinition bd = beanFactory.getBeanDefinition(beanName);
                    if (bd.getBeanClassName() != null) {
                        try {
                            Class<?> clazz = Class.forName(bd.getBeanClassName());
                            RocketMQMessageListener annotation = AnnotationUtils.findAnnotation(clazz, RocketMQMessageListener.class);
                            if (annotation != null) {
                                log.info("禁用 RocketMQ 消费者: {}", beanName);
                                // 将消费者 Bean 设置为非自动启动
                                bd.setLazyInit(true);
                                // 将消费者 Bean 角色设置为基础设施，降低优先级
                                bd.setRole(BeanDefinition.ROLE_INFRASTRUCTURE);
                            }
                        } catch (ClassNotFoundException e) {
                            // 忽略类加载错误
                        }
                    }
                }
            }
        };
    }

    /**
     * 启动时自动检测 RocketMQ 是否可用，并相应地启用或禁用消费者
     */
    @Component
    public static class RocketMQAvailabilityChecker {
        public RocketMQAvailabilityChecker() {
            // 在创建此 Bean 时设置系统属性，用于控制 RocketMQ 消费者的启用/禁用
            try {
                // 尝试简单测试 RocketMQ 连接
                boolean rocketMQAvailable = testRocketMQConnection();
                if (!rocketMQAvailable) {
                    log.warn("⚠️ RocketMQ 服务不可用，将禁用消费者功能");
                    System.setProperty("rocketmq.consumer.enabled", "false");
                } else {
                    log.info("✅ RocketMQ 服务可用，消费者功能正常启用");
                    System.setProperty("rocketmq.consumer.enabled", "true");
                }
            } catch (Exception e) {
                log.warn("⚠️ 检测 RocketMQ 可用性时出错，将禁用消费者功能: {}", e.getMessage());
                System.setProperty("rocketmq.consumer.enabled", "false");
            }
        }

        /**
         * 简单测试 RocketMQ 连接
         */
        private boolean testRocketMQConnection() {
            // 这里简单返回 false，实际应用中应该实现真正的连接测试
            // 由于在应用启动早期阶段，我们不便于使用太多的依赖，所以采用简单的方法
            String nameServer = System.getProperty("rocketmq.name-server",
                    System.getenv().getOrDefault("ROCKETMQ_NAME_SERVER", "localhost:9876"));

            // 这里简单判断是否设置了环境变量，实际应用中应该进行真正的连接测试
            return false; // 总是返回 false，确保在开发环境中禁用消费者
        }
    }

}

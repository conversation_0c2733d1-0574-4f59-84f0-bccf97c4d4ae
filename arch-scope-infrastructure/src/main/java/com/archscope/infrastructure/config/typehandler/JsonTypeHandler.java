package com.archscope.infrastructure.config.typehandler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * JSON类型处理器，用于处理复杂对象的序列化和反序列化
 * 将对象转换为JSON字符串存储到数据库，从数据库读取时将JSON字符串转换回对象
 */
public class JsonTypeHandler<T> extends BaseTypeHandler<T> {

    private static final Logger logger = LoggerFactory.getLogger(JsonTypeHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    private final Class<T> type;
    
    public JsonTypeHandler(Class<T> type) {
        if (type == null) {
            throw new IllegalArgumentException("Type argument cannot be null");
        }
        this.type = type;
    }
    
    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = objectMapper.writeValueAsString(parameter);
            ps.setString(i, json);
        } catch (JsonProcessingException e) {
            logger.error("Error converting object to JSON", e);
            ps.setString(i, "{}");
        }
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJson(rs.getString(columnName));
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJson(rs.getString(columnIndex));
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJson(cs.getString(columnIndex));
    }
    
    private T parseJson(String json) {
        if (json == null || json.isEmpty()) {
            try {
                return type.newInstance();
            } catch (Exception e) {
                logger.error("Error creating new instance of " + type.getName(), e);
                return null;
            }
        }
        
        try {
            return objectMapper.readValue(json, type);
        } catch (JsonProcessingException e) {
            logger.error("Error parsing JSON to " + type.getName(), e);
            return null;
        }
    }
    
    /**
     * 处理List类型的JSON
     */
    public static class ListTypeHandler<E> extends BaseTypeHandler<List<E>> {
        
        private static final Logger logger = LoggerFactory.getLogger(ListTypeHandler.class);
        private static final ObjectMapper objectMapper = new ObjectMapper();
        
        private final Class<E> elementType;
        
        public ListTypeHandler(Class<E> elementType) {
            if (elementType == null) {
                throw new IllegalArgumentException("Element type argument cannot be null");
            }
            this.elementType = elementType;
        }
        
        @Override
        public void setNonNullParameter(PreparedStatement ps, int i, List<E> parameter, JdbcType jdbcType) throws SQLException {
            try {
                String json = objectMapper.writeValueAsString(parameter);
                ps.setString(i, json);
            } catch (JsonProcessingException e) {
                logger.error("Error converting List to JSON", e);
                ps.setString(i, "[]");
            }
        }

        @Override
        public List<E> getNullableResult(ResultSet rs, String columnName) throws SQLException {
            return parseJson(rs.getString(columnName));
        }

        @Override
        public List<E> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
            return parseJson(rs.getString(columnIndex));
        }

        @Override
        public List<E> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
            return parseJson(cs.getString(columnIndex));
        }
        
        private List<E> parseJson(String json) {
            if (json == null || json.isEmpty()) {
                return new ArrayList<>();
            }
            
            try {
                JavaType type = objectMapper.getTypeFactory().constructCollectionType(List.class, elementType);
                return objectMapper.readValue(json, type);
            } catch (JsonProcessingException e) {
                logger.error("Error parsing JSON to List<" + elementType.getName() + ">", e);
                return new ArrayList<>();
            }
        }
    }
}

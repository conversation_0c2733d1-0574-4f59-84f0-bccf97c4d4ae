package com.archscope.infrastructure.config.typehandler;

import com.archscope.domain.valueobject.ResourceUsage;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;

/**
 * ResourceUsage类型处理器
 * 用于处理ResourceUsage类型的字段，将其转换为JSON字符串存储到数据库
 * 从数据库读取时将JSON字符串或Map转换回ResourceUsage对象
 */
@Component
@MappedTypes(ResourceUsage.class)
public class ResourceUsageTypeHandler extends BaseTypeHandler<ResourceUsage> {

    private static final Logger logger = LoggerFactory.getLogger(ResourceUsageTypeHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, ResourceUsage parameter, JdbcType jdbcType) throws SQLException {
        try {
            String json = objectMapper.writeValueAsString(parameter);
            ps.setString(i, json);
        } catch (JsonProcessingException e) {
            logger.error("Error converting ResourceUsage to JSON", e);
            // 设置默认的空ResourceUsage JSON
            ps.setString(i, getDefaultResourceUsageJson());
        }
    }

    @Override
    public ResourceUsage getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseResourceUsage(rs.getString(columnName));
    }

    @Override
    public ResourceUsage getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseResourceUsage(rs.getString(columnIndex));
    }

    @Override
    public ResourceUsage getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseResourceUsage(cs.getString(columnIndex));
    }

    /**
     * 解析JSON字符串为ResourceUsage对象
     */
    private ResourceUsage parseResourceUsage(String json) {
        if (json == null || json.trim().isEmpty()) {
            return getDefaultResourceUsage();
        }

        try {
            // 首先尝试直接解析为ResourceUsage对象
            return objectMapper.readValue(json, ResourceUsage.class);
        } catch (JsonProcessingException e) {
            logger.warn("Failed to parse JSON as ResourceUsage directly, trying Map approach: {}", json);
            
            try {
                // 如果直接解析失败，尝试先解析为Map，然后转换
                @SuppressWarnings("unchecked")
                Map<String, Object> map = objectMapper.readValue(json, Map.class);
                return mapToResourceUsage(map);
            } catch (JsonProcessingException ex) {
                logger.error("Error parsing JSON to ResourceUsage: {}", json, ex);
                return getDefaultResourceUsage();
            }
        }
    }

    /**
     * 将Map转换为ResourceUsage对象
     */
    private ResourceUsage mapToResourceUsage(Map<String, Object> map) {
        if (map == null || map.isEmpty()) {
            return getDefaultResourceUsage();
        }

        ResourceUsage.ResourceUsageBuilder builder = ResourceUsage.builder();

        // 处理各种可能的字段名和类型
        builder.cpuUsage(getDoubleValue(map, "cpuUsage"));
        
        // 处理内存使用量 - 可能是memoryUsage或memoryUsageMb
        Long memoryUsage = getLongValue(map, "memoryUsageMb");
        if (memoryUsage == null) {
            // 如果没有memoryUsageMb，尝试从memoryUsage获取
            Double memoryUsageDouble = getDoubleValue(map, "memoryUsage");
            if (memoryUsageDouble != null) {
                memoryUsage = memoryUsageDouble.longValue();
            }
        }
        builder.memoryUsageMb(memoryUsage);
        
        // 处理磁盘IO - 可能是diskUsage或diskIOBytes
        Long diskIO = getLongValue(map, "diskIOBytes");
        if (diskIO == null) {
            Double diskUsage = getDoubleValue(map, "diskUsage");
            if (diskUsage != null) {
                diskIO = diskUsage.longValue();
            }
        }
        builder.diskIOBytes(diskIO);
        
        // 处理网络IO - 可能是networkUsage或networkIOBytes
        Long networkIO = getLongValue(map, "networkIOBytes");
        if (networkIO == null) {
            Double networkUsage = getDoubleValue(map, "networkUsage");
            if (networkUsage != null) {
                networkIO = networkUsage.longValue();
            }
        }
        builder.networkIOBytes(networkIO);
        
        builder.threadCount(getIntegerValue(map, "threadCount"));
        builder.duration(getLongValue(map, "duration"));
        builder.tokenCount(getLongValue(map, "tokenCount"));

        return builder.build();
    }

    /**
     * 从Map中获取Double值
     */
    private Double getDoubleValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Double) {
            return (Double) value;
        }
        if (value instanceof Number) {
            return ((Number) value).doubleValue();
        }
        try {
            return Double.parseDouble(value.toString());
        } catch (NumberFormatException e) {
            logger.warn("Cannot convert value to Double: {} = {}", key, value);
            return null;
        }
    }

    /**
     * 从Map中获取Long值
     */
    private Long getLongValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Long) {
            return (Long) value;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.parseLong(value.toString());
        } catch (NumberFormatException e) {
            logger.warn("Cannot convert value to Long: {} = {}", key, value);
            return null;
        }
    }

    /**
     * 从Map中获取Integer值
     */
    private Integer getIntegerValue(Map<String, Object> map, String key) {
        Object value = map.get(key);
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.parseInt(value.toString());
        } catch (NumberFormatException e) {
            logger.warn("Cannot convert value to Integer: {} = {}", key, value);
            return null;
        }
    }

    /**
     * 获取默认的ResourceUsage对象
     */
    private ResourceUsage getDefaultResourceUsage() {
        return ResourceUsage.builder()
                .cpuUsage(0.0)
                .memoryUsageMb(0L)
                .diskIOBytes(0L)
                .networkIOBytes(0L)
                .threadCount(0)
                .duration(0L)
                .tokenCount(0L)
                .build();
    }

    /**
     * 获取默认的ResourceUsage JSON字符串
     */
    private String getDefaultResourceUsageJson() {
        return "{\"cpuUsage\":0.0,\"memoryUsageMb\":0,\"diskIOBytes\":0,\"networkIOBytes\":0,\"threadCount\":0,\"duration\":0,\"tokenCount\":0}";
    }
}

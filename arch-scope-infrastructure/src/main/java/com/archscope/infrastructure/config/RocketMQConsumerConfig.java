package com.archscope.infrastructure.config;

import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.consumer.DefaultMQPushConsumer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.apache.rocketmq.spring.support.DefaultRocketMQListenerContainer;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanClassLoaderAware;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

/**
 * RocketMQ 消费者配置
 * 当 RocketMQ 服务不可用时提供容错处理
 */
@Slf4j
@Configuration
public class RocketMQConsumerConfig implements ApplicationContextAware, BeanClassLoaderAware {

    private static final Logger log = LoggerFactory.getLogger(RocketMQConsumerConfig.class);

    private ApplicationContext applicationContext;
    private ClassLoader classLoader;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    @Override
    public void setBeanClassLoader(ClassLoader classLoader) {
        this.classLoader = classLoader;
    }

    /**
     * 提供一个降级的 RocketMQTemplate 实现
     * 在 RocketMQ 不可用时使用
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "rocketmq.consumer.enabled", havingValue = "false")
    public RocketMQTemplate fallbackRocketMQTemplate() {
        log.info("创建降级的 RocketMQTemplate，RocketMQ 消息将被禁用");
        return new NoopRocketMQTemplate();
    }

    /**
     * 空的 RocketMQTemplate 实现，避免实际连接到 RocketMQ 服务器
     */
    public static class NoopRocketMQTemplate extends RocketMQTemplate {
        // 该类继承自 RocketMQTemplate，但覆盖所有可能的方法，避免实际连接
    }

    /**
     * 完全禁用 RocketMQ 消费者的工厂后处理器
     */
    @Bean
    @ConditionalOnProperty(name = "rocketmq.consumer.enabled", havingValue = "false")
    public BeanFactoryPostProcessor disableRocketMQConsumerFactoryPostProcessor() {
        return new BeanFactoryPostProcessor() {
            @Override
            public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
                log.info("禁用 RocketMQ 消费者组件");

                // 处理所有 RocketMQ 消费者相关的 Bean 定义
                if (beanFactory instanceof BeanDefinitionRegistry) {
                    BeanDefinitionRegistry registry = (BeanDefinitionRegistry) beanFactory;

                    // 确保 RocketMQMessageService 能够创建
                    if (registry.containsBeanDefinition("rocketMQMessageService")) {
                        log.info("为 RocketMQMessageService 配置降级处理");
                        BeanDefinition bd = registry.getBeanDefinition("rocketMQMessageService");
                        bd.setAutowireCandidate(true);
                        bd.setDependsOn("fallbackRocketMQTemplate");
                    }

                    // 禁用 RocketMQMessageListenerBeanPostProcessor
                    String[] rocketMQBeanProcessors = {
                            "org.apache.rocketmq.spring.annotation.RocketMQMessageListenerBeanPostProcessor",
                            "rocketMQListenerContainerFactory"
                    };

                    for (String beanName : rocketMQBeanProcessors) {
                        if (registry.containsBeanDefinition(beanName)) {
                            log.info("禁用 RocketMQ 相关 Bean: {}", beanName);
                            // 替换原始 Bean 定义为我们的空实现
                            registry.removeBeanDefinition(beanName);
                            registry.registerBeanDefinition(beanName,
                                    BeanDefinitionBuilder.genericBeanDefinition(Object.class)
                                            .setRole(BeanDefinition.ROLE_INFRASTRUCTURE)
                                            .getBeanDefinition());
                        }
                    }

                    // 遍历查找消费者 Bean
                    for (String beanName : registry.getBeanDefinitionNames()) {
                        BeanDefinition bd = registry.getBeanDefinition(beanName);
                        if (bd.getBeanClassName() == null) {
                            continue;
                        }

                        try {
                            Class<?> beanClass = Class.forName(bd.getBeanClassName(), false, classLoader);

                            // 找到 RocketMQMessageListener 注解的 Bean
                            if (beanClass.isAnnotationPresent(RocketMQMessageListener.class)) {
                                RocketMQMessageListener annotation = beanClass.getAnnotation(RocketMQMessageListener.class);
                                log.info("禁用 RocketMQ 消费者 Bean: {}, 主题: {}", beanName, annotation.topic());

                                // 标记为延迟初始化，避免在启动时初始化
                                bd.setLazyInit(true);
                                // 降低 Bean 优先级
                                bd.setRole(BeanDefinition.ROLE_INFRASTRUCTURE);
                            }
                        } catch (ClassNotFoundException | NoClassDefFoundError e) {
                            // 忽略类加载错误
                        }
                    }
                }
            }
        };
    }

    /**
     * 在 RocketMQ 消费者被禁用时，替换监听器容器工厂
     */
    @Bean
    @Primary
    @ConditionalOnProperty(name = "rocketmq.consumer.enabled", havingValue = "false")
    public Object rocketMQListenerContainerFactory() {
        log.info("创建空的 RocketMQ 监听器容器工厂，所有消费者将被禁用");
        // 返回一个简单的对象，避免类型不匹配问题
        return new Object();
    }

    /**
     * 禁用所有 RocketMQ 消费者的 Bean 后处理器
     */
    @Bean
    @ConditionalOnProperty(name = "rocketmq.consumer.enabled", havingValue = "false")
    public BeanPostProcessor disableRocketMQConsumerBeanPostProcessor() {
        return new BeanPostProcessor() {
            @Override
            public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
                // 获取原始类，处理可能的代理类
                Class<?> targetClass = AopProxyUtils.ultimateTargetClass(bean);

                // 寻找带有 @RocketMQMessageListener 注解的类
                if (targetClass.isAnnotationPresent(RocketMQMessageListener.class)) {
                    RocketMQMessageListener annotation = targetClass.getAnnotation(RocketMQMessageListener.class);
                    log.info("禁用 RocketMQ 消费者: {}，主题: {}", beanName, annotation.topic());

                    // 如果是 RocketMQListener 类型的 Bean，返回一个空实现
                    if (bean instanceof RocketMQListener) {
                        return new NoopRocketMQListener<>();
                    }
                }

                // 处理 RocketMQ 容器
                if (bean instanceof DefaultRocketMQListenerContainer) {
                    log.info("替换 RocketMQ 监听器容器为空实现: {}", beanName);
                    return new NoopRocketMQListenerContainer();
                }

                return bean;
            }

            @Override
            public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
                // 拦截 DefaultRocketMQListenerContainer 的实例化
                if (bean instanceof DefaultRocketMQListenerContainer) {
                    // 替换为我们的空实现
                    return new NoopRocketMQListenerContainer();
                }
                return bean;
            }
        };
    }

    /**
     * 空的 RocketMQ 监听器容器实现
     */
    public static class NoopRocketMQListenerContainer extends DefaultRocketMQListenerContainer {
        public NoopRocketMQListenerContainer() {
            // 空构造函数，避免调用父类的构造函数
        }

        @Override
        public void afterPropertiesSet() {
            // 重写以避免初始化
            log.debug("跳过 RocketMQ 监听器容器初始化");
        }

        @Override
        public DefaultMQPushConsumer getConsumer() {
            // 避免空指针异常
            return null;
        }

        @Override
        public void start() {
            // 不执行任何操作，避免启动失败
            log.debug("跳过 RocketMQ 监听器容器启动");
        }

        @Override
        public void stop() {
            // 不执行任何操作
            log.debug("跳过 RocketMQ 监听器容器停止");
        }

        @Override
        public boolean isRunning() {
            return false;
        }

        @Override
        public void destroy() {
            // 重写以避免调用父类的销毁方法
            log.debug("跳过 RocketMQ 监听器容器销毁");
        }
    }

    /**
     * 空的 RocketMQ 监听器实现
     */
    public static class NoopRocketMQListener<T> implements RocketMQListener<T> {
        @Override
        public void onMessage(T message) {
            // 不执行任何操作
            log.debug("收到消息但被禁用的消费者忽略了: {}", message);
        }
    }
}
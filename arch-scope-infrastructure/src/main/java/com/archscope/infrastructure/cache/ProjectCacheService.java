package com.archscope.infrastructure.cache;

import com.archscope.domain.entity.Project;
import org.springframework.stereotype.Service;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import java.util.Map;
import java.util.ArrayList;
import java.util.Set;

@Service
public class ProjectCacheService {

    private static final String CACHE_KEY_PREFIX = "project:";
    private static final long DEFAULT_EXPIRATION = 1;
    private static final TimeUnit DEFAULT_EXPIRATION_UNIT = TimeUnit.HOURS;
    private static final Logger log = LoggerFactory.getLogger(ProjectCacheService.class);

    private final RedisService redisService;

    public ProjectCacheService(RedisService redisService) {
        this.redisService = redisService;
    }

    /**
     * 生成缓存Key
     */
    private String getCacheKey(Long id) {
        return CACHE_KEY_PREFIX + "id:" + id;
    }

    /**
     * 生成缓存Key
     */
    private String getCacheKeyByUrl(String repositoryUrl) {
        return CACHE_KEY_PREFIX + "url:" + repositoryUrl;
    }

    /**
     * 缓存项目
     */
    public void cacheProject(Project project) {
        if (project != null && project.getId() != null) {
            redisService.set(getCacheKey(project.getId()), project, DEFAULT_EXPIRATION, DEFAULT_EXPIRATION_UNIT);
            
            // 同时按照仓库URL缓存
            if (project.getRepositoryUrl() != null) {
                redisService.set(getCacheKeyByUrl(project.getRepositoryUrl()), project, DEFAULT_EXPIRATION, DEFAULT_EXPIRATION_UNIT);
            }
        }
    }

    /**
     * 根据ID获取缓存的项目
     */
    public Optional<Project> getProjectById(Long id) {
        if (id == null) {
            return Optional.empty();
        }
        
        Object cached = redisService.get(getCacheKey(id));
        if (cached instanceof Project) {
            return Optional.of((Project) cached);
        } else if (cached instanceof Map) {
            try {
                // 尝试将Map转换为Project对象
                ObjectMapper mapper = new ObjectMapper();
                mapper.registerModule(new JavaTimeModule()); // 注册Java 8日期时间模块
                Project project = mapper.convertValue(cached, Project.class);
                return Optional.of(project);
            } catch (Exception e) {
                log.error("Failed to convert cached object to Project: {}", e.getMessage());
                return Optional.empty();
            }
        }
        
        return Optional.empty();
    }

    /**
     * 根据仓库URL获取缓存的项目
     */
    public Optional<Project> getProjectByRepositoryUrl(String repositoryUrl) {
        if (repositoryUrl == null) {
            return Optional.empty();
        }
        
        Object cached = redisService.get(getCacheKeyByUrl(repositoryUrl));
        if (cached instanceof Project) {
            return Optional.of((Project) cached);
        } else if (cached instanceof Map) {
            try {
                // 尝试将Map转换为Project对象
                ObjectMapper mapper = new ObjectMapper();
                mapper.registerModule(new JavaTimeModule()); // 注册Java 8日期时间模块
                Project project = mapper.convertValue(cached, Project.class);
                return Optional.of(project);
            } catch (Exception e) {
                log.error("Failed to convert cached object to Project: {}", e.getMessage());
                return Optional.empty();
            }
        }
        
        return Optional.empty();
    }

    /**
     * 缓存项目列表
     */
    public void cacheProjectList(List<Project> projects) {
        if (projects != null && !projects.isEmpty()) {
            redisService.set(CACHE_KEY_PREFIX + "all", projects, DEFAULT_EXPIRATION, DEFAULT_EXPIRATION_UNIT);
        }
    }

    /**
     * 获取缓存的项目列表
     */
    @SuppressWarnings("unchecked")
    public Optional<List<Project>> getAllProjects() {
        Object cached = redisService.get(CACHE_KEY_PREFIX + "all");
        if (cached instanceof List) {
            try {
                List<?> list = (List<?>) cached;
                // 确保所有元素都是Project类型
                List<Project> projects = new ArrayList<>();
                ObjectMapper mapper = new ObjectMapper();
                mapper.registerModule(new JavaTimeModule()); // 注册Java 8日期时间模块
                
                for (Object item : list) {
                    if (item instanceof Project) {
                        projects.add((Project) item);
                    } else if (item instanceof Map) {
                        Project project = mapper.convertValue(item, Project.class);
                        projects.add(project);
                    }
                }
                return Optional.of(projects);
            } catch (Exception e) {
                log.error("Failed to convert cached list to Project list: {}", e.getMessage());
                return Optional.empty();
            }
        }
        
        return Optional.empty();
    }

    /**
     * 移除项目缓存
     */
    public void removeProjectCache(Long id) {
        if (id != null) {
            Project project = getProjectById(id).orElse(null);
            if (project != null && project.getRepositoryUrl() != null) {
                redisService.delete(getCacheKeyByUrl(project.getRepositoryUrl()));
            }
            
            redisService.delete(getCacheKey(id));
            
            // 同时移除项目列表缓存，因为列表已变更
            redisService.delete(CACHE_KEY_PREFIX + "all");
        }
    }

    /**
     * 移除所有项目缓存
     */
    public void clearAllProjectCache() {
        // 清除项目列表缓存
        redisService.delete(CACHE_KEY_PREFIX + "all");
        
        // 清除所有以project:开头的键
        Set<String> keys = redisService.keys(CACHE_KEY_PREFIX + "*");
        if (keys != null && !keys.isEmpty()) {
            redisService.delete(new ArrayList<>(keys));
            log.debug("清除了{}个项目缓存键", keys.size());
        }
    }
} 
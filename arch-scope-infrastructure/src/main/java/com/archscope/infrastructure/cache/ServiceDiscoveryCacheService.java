package com.archscope.infrastructure.cache;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.Service;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 服务发现专用缓存服务
 */
@Component
public class ServiceDiscoveryCacheService {

    private final RedisService redisService;

    // 缓存键前缀
    private static final String SERVICE_PREFIX = "sd:service:";
    private static final String CAPABILITY_PREFIX = "sd:capability:";
    private static final String SEARCH_PREFIX = "sd:search:";
    private static final String STATS_PREFIX = "sd:stats:";
    private static final String POPULAR_PREFIX = "sd:popular:";

    // 缓存过期时间
    private static final int DEFAULT_EXPIRE_MINUTES = 30;
    private static final int SEARCH_EXPIRE_MINUTES = 10;
    private static final int STATS_EXPIRE_MINUTES = 60;
    private static final int POPULAR_EXPIRE_MINUTES = 15;

    public ServiceDiscoveryCacheService(RedisService redisService) {
        this.redisService = redisService;
    }

    // ========== 服务缓存 ==========

    /**
     * 缓存单个服务
     */
    public void cacheService(String serviceId, Service service) {
        String key = SERVICE_PREFIX + serviceId;
        redisService.set(key, service, DEFAULT_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的服务
     */
    public Service getCachedService(String serviceId) {
        String key = SERVICE_PREFIX + serviceId;
        return (Service) redisService.get(key);
    }

    /**
     * 缓存服务列表
     */
    public void cacheServiceList(String cacheKey, List<Service> services) {
        redisService.setList(cacheKey, services, DEFAULT_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的服务列表
     */
    @SuppressWarnings("unchecked")
    public List<Service> getCachedServiceList(String cacheKey) {
        return redisService.getList(cacheKey, Service.class);
    }

    // ========== 能力缓存 ==========

    /**
     * 缓存单个能力
     */
    public void cacheCapability(String capabilityId, Capability capability) {
        String key = CAPABILITY_PREFIX + capabilityId;
        redisService.set(key, capability, DEFAULT_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的能力
     */
    public Capability getCachedCapability(String capabilityId) {
        String key = CAPABILITY_PREFIX + capabilityId;
        return (Capability) redisService.get(key);
    }

    /**
     * 缓存能力列表
     */
    public void cacheCapabilityList(String cacheKey, List<Capability> capabilities) {
        redisService.setList(cacheKey, capabilities, DEFAULT_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的能力列表
     */
    @SuppressWarnings("unchecked")
    public List<Capability> getCachedCapabilityList(String cacheKey) {
        return redisService.getList(cacheKey, Capability.class);
    }

    // ========== 搜索结果缓存 ==========

    /**
     * 缓存搜索结果
     */
    public void cacheSearchResults(String keyword, int page, int size, List<Service> results) {
        String key = SEARCH_PREFIX + "services:" + keyword + ":" + page + ":" + size;
        redisService.setList(key, results, SEARCH_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的搜索结果
     */
    @SuppressWarnings("unchecked")
    public List<Service> getCachedSearchResults(String keyword, int page, int size) {
        String key = SEARCH_PREFIX + "services:" + keyword + ":" + page + ":" + size;
        return redisService.getList(key, Service.class);
    }

    /**
     * 缓存搜索结果总数
     */
    public void cacheSearchCount(String keyword, long count) {
        String key = SEARCH_PREFIX + "count:" + keyword;
        redisService.set(key, count, SEARCH_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的搜索结果总数
     */
    public Long getCachedSearchCount(String keyword) {
        String key = SEARCH_PREFIX + "count:" + keyword;
        Object count = redisService.get(key);
        return count != null ? (Long) count : null;
    }

    // ========== 统计数据缓存 ==========

    /**
     * 缓存服务状态统计
     */
    public void cacheServiceStatusStats(List<java.util.Map<String, Object>> stats) {
        String key = STATS_PREFIX + "service:status";
        redisService.setList(key, stats, STATS_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的服务状态统计
     */
    @SuppressWarnings("unchecked")
    public List<java.util.Map<String, Object>> getCachedServiceStatusStats() {
        String key = STATS_PREFIX + "service:status";
        Object result = redisService.get(key);
        if (result instanceof List) {
            return (List<java.util.Map<String, Object>>) result;
        }
        return null;
    }

    /**
     * 缓存服务类型统计
     */
    public void cacheServiceTypeStats(List<java.util.Map<String, Object>> stats) {
        String key = STATS_PREFIX + "service:type";
        redisService.setList(key, stats, STATS_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的服务类型统计
     */
    @SuppressWarnings("unchecked")
    public List<java.util.Map<String, Object>> getCachedServiceTypeStats() {
        String key = STATS_PREFIX + "service:type";
        Object result = redisService.get(key);
        if (result instanceof List) {
            return (List<java.util.Map<String, Object>>) result;
        }
        return null;
    }

    /**
     * 缓存能力统计
     */
    public void cacheCapabilityStats(List<java.util.Map<String, Object>> stats) {
        String key = STATS_PREFIX + "capability:service";
        redisService.setList(key, stats, STATS_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的能力统计
     */
    @SuppressWarnings("unchecked")
    public List<java.util.Map<String, Object>> getCachedCapabilityStats() {
        String key = STATS_PREFIX + "capability:service";
        Object result = redisService.get(key);
        if (result instanceof List) {
            return (List<java.util.Map<String, Object>>) result;
        }
        return null;
    }

    // ========== 热门数据缓存 ==========

    /**
     * 缓存热门服务
     */
    public void cachePopularServices(List<Service> services) {
        String key = POPULAR_PREFIX + "services";
        redisService.setList(key, services, POPULAR_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的热门服务
     */
    @SuppressWarnings("unchecked")
    public List<Service> getCachedPopularServices() {
        String key = POPULAR_PREFIX + "services";
        return redisService.getList(key, Service.class);
    }

    /**
     * 缓存最近注册的服务
     */
    public void cacheRecentServices(List<Service> services) {
        String key = POPULAR_PREFIX + "recent";
        redisService.setList(key, services, POPULAR_EXPIRE_MINUTES, TimeUnit.MINUTES);
    }

    /**
     * 获取缓存的最近注册服务
     */
    @SuppressWarnings("unchecked")
    public List<Service> getCachedRecentServices() {
        String key = POPULAR_PREFIX + "recent";
        return redisService.getList(key, Service.class);
    }

    // ========== 缓存清理 ==========

    /**
     * 清除服务相关缓存
     */
    public void clearServiceCache(String serviceId) {
        // 清除单个服务缓存
        redisService.delete(SERVICE_PREFIX + serviceId);
        
        // 清除相关列表缓存
        clearServiceListCaches();
    }

    /**
     * 清除能力相关缓存
     */
    public void clearCapabilityCache(String capabilityId, String serviceId) {
        // 清除单个能力缓存
        redisService.delete(CAPABILITY_PREFIX + capabilityId);
        
        // 清除相关列表缓存
        clearCapabilityListCaches();
        
        // 清除服务相关缓存
        clearServiceCache(serviceId);
    }

    /**
     * 清除搜索缓存
     */
    public void clearSearchCache() {
        redisService.deleteByPattern(SEARCH_PREFIX + "*");
    }

    /**
     * 清除统计缓存
     */
    public void clearStatsCache() {
        redisService.deleteByPattern(STATS_PREFIX + "*");
    }

    /**
     * 清除热门数据缓存
     */
    public void clearPopularCache() {
        redisService.deleteByPattern(POPULAR_PREFIX + "*");
    }

    /**
     * 清除所有服务发现相关缓存
     */
    public void clearAllCache() {
        redisService.deleteByPattern("sd:*");
    }

    private void clearServiceListCaches() {
        redisService.deleteByPattern(SEARCH_PREFIX + "*");
        redisService.deleteByPattern(STATS_PREFIX + "*");
        redisService.deleteByPattern(POPULAR_PREFIX + "*");
    }

    private void clearCapabilityListCaches() {
        redisService.deleteByPattern(SEARCH_PREFIX + "*");
        redisService.deleteByPattern(STATS_PREFIX + "*");
    }

    // ========== 缓存键生成工具 ==========

    /**
     * 生成服务列表缓存键
     */
    public String buildServiceListCacheKey(String prefix, Object... params) {
        StringBuilder key = new StringBuilder(SERVICE_PREFIX).append(prefix);
        for (Object param : params) {
            key.append(":").append(param != null ? param.toString() : "null");
        }
        return key.toString();
    }

    /**
     * 生成能力列表缓存键
     */
    public String buildCapabilityListCacheKey(String prefix, Object... params) {
        StringBuilder key = new StringBuilder(CAPABILITY_PREFIX).append(prefix);
        for (Object param : params) {
            key.append(":").append(param != null ? param.toString() : "null");
        }
        return key.toString();
    }
}
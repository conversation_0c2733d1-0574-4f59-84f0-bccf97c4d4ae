package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.infrastructure.entity.TaskEntity;
import com.archscope.infrastructure.mapper.TaskMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 任务仓库MyBatis实现
 */
@Repository
public class MyBatisTaskRepository implements TaskRepository {

    private final TaskMapper taskMapper;

    public MyBatisTaskRepository(TaskMapper taskMapper) {
        this.taskMapper = taskMapper;
    }

    @Override
    public Task save(Task task) {
        TaskEntity taskEntity = new TaskEntity(task);
        if (task.getId() == null) {
            taskMapper.insert(taskEntity);
            task.setId(taskEntity.getId());
        } else {
            taskMapper.updateById(taskEntity);
        }
        return task;
    }

    @Override
    public Optional<Task> findById(Long id) {
        TaskEntity taskEntity = taskMapper.selectById(id);
        return taskEntity != null ? Optional.of(taskEntity.toDomainEntity()) : Optional.empty();
    }

    @Override
    public List<Task> findAll() {
        return taskMapper.selectList(null).stream()
                .map(TaskEntity::toDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<Task> findAllByStatus(TaskStatus status) {
        return taskMapper.findAllByStatus(status).stream()
                .map(TaskEntity::toDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<Task> findAllByStatusAndTaskType(TaskStatus status, String taskType) {
        return taskMapper.findAllByStatusAndTaskType(status, taskType).stream()
                .map(TaskEntity::toDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<Task> findAllByStatusAndProjectId(TaskStatus status, Long projectId) {
        return taskMapper.findAllByStatusAndProjectId(status, projectId).stream()
                .map(TaskEntity::toDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public Optional<Task> findNextPendingTask() {
        TaskEntity taskEntity = taskMapper.findNextPendingTask();
        return taskEntity != null ? Optional.of(taskEntity.toDomainEntity()) : Optional.empty();
    }

    @Override
    public Optional<Task> findNextPendingTaskByType(String taskType) {
        TaskEntity taskEntity = taskMapper.findNextPendingTaskByType(taskType);
        return taskEntity != null ? Optional.of(taskEntity.toDomainEntity()) : Optional.empty();
    }

    @Override
    public Task update(Task task) {
        TaskEntity taskEntity = new TaskEntity(task);
        taskMapper.updateById(taskEntity);
        return task;
    }

    @Override
    public void deleteById(Long id) {
        taskMapper.deleteById(id);
    }

    @Override
    public int deleteCompletedTasksOlderThan(LocalDateTime date) {
        return taskMapper.deleteCompletedTasksOlderThan(date);
    }

    @Override
    public List<Task> findByProjectId(Long projectId) {
        LambdaQueryWrapper<TaskEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskEntity::getProjectId, projectId);
        return taskMapper.selectList(wrapper).stream()
                .map(TaskEntity::toDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public List<Task> findTimeoutProcessingTasks() {
        return taskMapper.findTimeoutProcessingTasks().stream()
                .map(TaskEntity::toDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public int updateTaskToProcessing(Long taskId, String workerId, LocalDateTime timeoutAt) {
        return taskMapper.updateTaskToProcessing(taskId, workerId, timeoutAt);
    }

    @Override
    public int resetTimeoutTask(Long taskId) {
        return taskMapper.resetTimeoutTask(taskId);
    }



    @Override
    public int completeTaskWithResult(Long taskId, String overallStatus, String results, Long executionTimeMs) {
        return taskMapper.completeTaskWithResult(taskId, overallStatus, results, executionTimeMs);
    }

    @Override
    public int failTaskWithError(Long taskId, String errorDetail, Long executionTimeMs) {
        return taskMapper.failTaskWithError(taskId, errorDetail, executionTimeMs);
    }

    @Override
    public List<Task> findTasksWithPagination(TaskStatus status, String taskType, Long projectId,
                                             String workerId, LocalDateTime startDate, LocalDateTime endDate,
                                             String orderBy, int offset, int limit) {
        return taskMapper.findTasksWithPagination(status, taskType, projectId, workerId,
                                                 startDate, endDate, orderBy, offset, limit)
                .stream()
                .map(TaskEntity::toDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public long countTasksWithCondition(TaskStatus status, String taskType, Long projectId,
                                       String workerId, LocalDateTime startDate, LocalDateTime endDate) {
        return taskMapper.countTasksWithCondition(status, taskType, projectId, workerId, startDate, endDate);
    }

    @Override
    public int batchUpdateTaskStatus(List<Long> taskIds, TaskStatus currentStatus, TaskStatus newStatus) {
        return taskMapper.batchUpdateTaskStatus(taskIds, currentStatus, newStatus);
    }

    @Override
    public int batchResetTimeoutTasks(List<Long> taskIds) {
        return taskMapper.batchResetTimeoutTasks(taskIds);
    }

    @Override
    public List<java.util.Map<String, Object>> getTaskStatistics(Long projectId, String taskType,
                                                                 LocalDateTime startDate, LocalDateTime endDate) {
        return taskMapper.getTaskStatistics(projectId, taskType, startDate, endDate);
    }

    @Override
    public List<Task> findLongRunningTasks(int minutes, int limit) {
        return taskMapper.findLongRunningTasks(minutes, limit)
                .stream()
                .map(TaskEntity::toDomainEntity)
                .collect(Collectors.toList());
    }

    @Override
    public int lockTaskWithCAS(Long taskId, String workerId, int timeoutMinutes, Integer expectedVersion) {
        return taskMapper.lockTaskWithCAS(taskId, workerId, timeoutMinutes, expectedVersion);
    }

    @Override
    public int retryTask(Long taskId) {
        return taskMapper.retryTask(taskId);
    }
}

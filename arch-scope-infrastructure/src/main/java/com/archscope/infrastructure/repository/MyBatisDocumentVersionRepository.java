package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.repository.DocumentVersionRepository;
import com.archscope.domain.valueobject.DocumentType;
import com.archscope.infrastructure.mapper.DocumentVersionMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 文档版本仓储MyBatis实现
 */
@Repository
public class MyBatisDocumentVersionRepository implements DocumentVersionRepository {

    private final DocumentVersionMapper documentVersionMapper;

    public MyBatisDocumentVersionRepository(DocumentVersionMapper documentVersionMapper) {
        this.documentVersionMapper = documentVersionMapper;
    }

    @Override
    public DocumentVersion save(DocumentVersion documentVersion) {
        if (documentVersion.getId() == null) {
            documentVersionMapper.insert(documentVersion);
        } else {
            documentVersionMapper.updateById(documentVersion);
        }
        return documentVersion;
    }

    @Override
    public Optional<DocumentVersion> findById(Long id) {
        return Optional.ofNullable(documentVersionMapper.selectById(id));
    }

    @Override
    public List<DocumentVersion> findByProjectId(Long projectId) {
        LambdaQueryWrapper<DocumentVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentVersion::getProjectId, projectId);
        wrapper.orderByDesc(DocumentVersion::getTimestamp);
        return documentVersionMapper.selectList(wrapper);
    }

    @Override
    public List<DocumentVersion> findByIds(List<Long> ids) {
        return documentVersionMapper.selectBatchIds(ids);
    }

    @Override
    public List<DocumentVersion> findByProjectIdAndIsPublished(Long projectId, Boolean isPublished) {
        LambdaQueryWrapper<DocumentVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentVersion::getProjectId, projectId);
        wrapper.eq(DocumentVersion::getIsPublished, isPublished);
        wrapper.orderByDesc(DocumentVersion::getTimestamp);
        return documentVersionMapper.selectList(wrapper);
    }

    @Override
    public List<DocumentVersion> findByProjectIdAndDocType(Long projectId, DocumentType docType) {
        LambdaQueryWrapper<DocumentVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentVersion::getProjectId, projectId);
        wrapper.eq(DocumentVersion::getDocType, docType);
        wrapper.orderByDesc(DocumentVersion::getTimestamp);
        return documentVersionMapper.selectList(wrapper);
    }

    @Override
    public List<DocumentVersion> findByProjectIdAndCommitId(Long projectId, String commitId) {
        LambdaQueryWrapper<DocumentVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentVersion::getProjectId, projectId);
        wrapper.eq(DocumentVersion::getCommitId, commitId);
        wrapper.orderByDesc(DocumentVersion::getTimestamp);
        return documentVersionMapper.selectList(wrapper);
    }

    @Override
    public Optional<DocumentVersion> findByProjectIdAndVersionTag(Long projectId, String versionTag) {
        LambdaQueryWrapper<DocumentVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentVersion::getProjectId, projectId);
        wrapper.eq(DocumentVersion::getVersionTag, versionTag);
        return Optional.ofNullable(documentVersionMapper.selectOne(wrapper));
    }

    @Override
    public Optional<DocumentVersion> findLatestByProjectIdAndDocType(Long projectId, DocumentType docType) {
        LambdaQueryWrapper<DocumentVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentVersion::getProjectId, projectId);
        wrapper.eq(DocumentVersion::getDocType, docType);
        wrapper.orderByDesc(DocumentVersion::getTimestamp);
        wrapper.last("LIMIT 1");
        return Optional.ofNullable(documentVersionMapper.selectOne(wrapper));
    }

    @Override
    public DocumentVersion update(DocumentVersion documentVersion) {
        documentVersionMapper.updateById(documentVersion);
        return documentVersion;
    }

    @Override
    public void delete(Long id) {
        documentVersionMapper.deleteById(id);
    }

    @Override
    public int deleteByProjectId(Long projectId) {
        LambdaQueryWrapper<DocumentVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentVersion::getProjectId, projectId);
        return documentVersionMapper.delete(wrapper);
    }

    @Override
    public long countByProjectId(Long projectId) {
        LambdaQueryWrapper<DocumentVersion> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(DocumentVersion::getProjectId, projectId);
        return documentVersionMapper.selectCount(wrapper);
    }
}

package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.CodeRepository;
import com.archscope.domain.repository.CodeRepositoryRepository;
import com.archscope.infrastructure.mapper.CodeRepositoryMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 代码仓库仓储MyBatis实现
 */
@Repository
@RequiredArgsConstructor
public class MyBatisCodeRepositoryRepository implements CodeRepositoryRepository {

    private final CodeRepositoryMapper codeRepositoryMapper;

    @Override
    public CodeRepository save(CodeRepository codeRepository) {
        if (codeRepository.getId() == null) {
            codeRepositoryMapper.insert(codeRepository);
        } else {
            codeRepositoryMapper.updateById(codeRepository);
        }
        return codeRepository;
    }

    @Override
    public Optional<CodeRepository> findById(Long id) {
        return Optional.ofNullable(codeRepositoryMapper.selectById(id));
    }

    @Override
    public List<CodeRepository> findByProjectId(Long projectId) {
        return codeRepositoryMapper.selectByProjectId(projectId);
    }

    @Override
    public Optional<CodeRepository> findByUrl(String url) {
        return Optional.ofNullable(codeRepositoryMapper.selectByUrl(url));
    }

    @Override
    public CodeRepository update(CodeRepository codeRepository) {
        codeRepositoryMapper.updateById(codeRepository);
        return codeRepository;
    }

    @Override
    public void delete(Long id) {
        codeRepositoryMapper.deleteById(id);
    }

    @Override
    public List<CodeRepository> findAll() {
        return codeRepositoryMapper.selectList(null);
    }
}

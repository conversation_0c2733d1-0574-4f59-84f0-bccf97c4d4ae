package com.archscope.infrastructure.repository.converter;

import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.domain.valueobject.Tag;
import com.archscope.domain.valueobject.Version;
import com.archscope.domain.valueobject.Metadata;
import com.archscope.infrastructure.persistence.entity.ServiceDO;
import org.springframework.stereotype.Component;

import java.net.URL;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务领域对象与数据对象转换器
 */
@Component
public class ServiceConverter {

    /**
     * 将领域对象转换为数据对象
     */
    public ServiceDO toDataObject(Service service) {
        if (service == null) {
            return null;
        }

        ServiceDO serviceDO = new ServiceDO();
        // 将String类型的ServiceId转换为Long类型的数据库ID
        serviceDO.setId(service.getId().getLongValue());
        serviceDO.setName(service.getName());
        serviceDO.setDescription(service.getDescription());
        serviceDO.setVersion(service.getVersion().getValue());
        serviceDO.setType(service.getType().name());
        serviceDO.setEndpoint(service.getEndpoint().toString());
        serviceDO.setGroupId(service.getGroupId());
        serviceDO.setArtifactId(service.getArtifactId());
        serviceDO.setTags(service.getTags().stream().map(tag -> tag.getValue()).collect(ArrayList::new, ArrayList::add, ArrayList::addAll));
        serviceDO.setOwner(""); // Service实体没有owner字段，设置为空
        serviceDO.setApiDocUrl(""); // Service实体没有apiDocUrl字段，设置为空
        serviceDO.setStatus(service.getStatus().name());
        serviceDO.setMetadata(new HashMap<>(service.getMetadata().getEntries()));
        serviceDO.setCreatedAt(LocalDateTime.ofInstant(service.getRegisteredAt(), ZoneId.systemDefault()));
        serviceDO.setUpdatedAt(LocalDateTime.ofInstant(service.getLastUpdatedAt(), ZoneId.systemDefault()));

        return serviceDO;
    }

    /**
     * 将数据对象转换为领域对象
     */
    public Service toDomainObject(ServiceDO serviceDO) {
        if (serviceDO == null) {
            return null;
        }

        try {
            // 转换标签
            Set<Tag> tags = serviceDO.getTags() != null ?
                serviceDO.getTags().stream().map(Tag::of).collect(Collectors.toSet()) :
                Collections.emptySet();

            // 转换元数据 - 将Map<String,Object>转换为Map<String,String>
            Map<String, String> metadataEntries = new HashMap<>();
            if (serviceDO.getMetadata() != null) {
                serviceDO.getMetadata().forEach((key, value) ->
                    metadataEntries.put(key, value != null ? value.toString() : ""));
            }
            Metadata metadata = Metadata.of(metadataEntries);

            // 转换时间
            Instant registeredAt = serviceDO.getCreatedAt() != null ?
                serviceDO.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant() :
                Instant.now();
            Instant lastUpdatedAt = serviceDO.getUpdatedAt() != null ?
                serviceDO.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant() :
                Instant.now();

            return Service.restore(
                ServiceId.of(serviceDO.getId()),
                serviceDO.getName(),
                serviceDO.getDescription(),
                ServiceType.valueOf(serviceDO.getType()),
                Version.of(serviceDO.getVersion()),
                new URL(serviceDO.getEndpoint()),
                serviceDO.getGroupId(),
                serviceDO.getArtifactId(),
                tags,
                Collections.emptySet(), // capabilities 为空，需要单独查询
                ServiceStatus.valueOf(serviceDO.getStatus()),
                metadata,
                registeredAt,
                lastUpdatedAt
            );
        } catch (Exception e) {
            throw new RuntimeException("转换Service实体失败", e);
        }
    }

    /**
     * 批量转换数据对象为领域对象
     */
    public List<Service> toDomainObjects(List<ServiceDO> serviceDOs) {
        if (serviceDOs == null) {
            return new ArrayList<>();
        }

        List<Service> services = new ArrayList<>();
        for (ServiceDO serviceDO : serviceDOs) {
            Service service = toDomainObject(serviceDO);
            if (service != null) {
                services.add(service);
            }
        }
        return services;
    }

    /**
     * 批量转换领域对象为数据对象
     */
    public List<ServiceDO> toDataObjects(List<Service> services) {
        if (services == null) {
            return new ArrayList<>();
        }

        List<ServiceDO> serviceDOs = new ArrayList<>();
        for (Service service : services) {
            ServiceDO serviceDO = toDataObject(service);
            if (serviceDO != null) {
                serviceDOs.add(serviceDO);
            }
        }
        return serviceDOs;
    }
}
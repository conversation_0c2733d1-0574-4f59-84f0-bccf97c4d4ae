package com.archscope.infrastructure.repository;

import com.archscope.domain.entity.ParseResultCache;
import com.archscope.domain.repository.ParseResultCacheRepository;
import com.archscope.infrastructure.mapper.ParseResultCacheMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 解析结果缓存仓库MyBatis实现
 */
@Repository
@RequiredArgsConstructor
public class MyBatisParseResultCacheRepository implements ParseResultCacheRepository {

    private final ParseResultCacheMapper parseResultCacheMapper;

    @Override
    public ParseResultCache save(ParseResultCache parseResultCache) {
        if (parseResultCache.getId() == null) {
            parseResultCacheMapper.insert(parseResultCache);
        } else {
            parseResultCacheMapper.updateById(parseResultCache);
        }
        return parseResultCache;
    }

    @Override
    public Optional<ParseResultCache> findById(Long id) {
        return Optional.ofNullable(parseResultCacheMapper.selectById(id));
    }

    @Override
    public Optional<ParseResultCache> findByRepositoryIdAndCommitId(Long repositoryId, String commitId) {
        return Optional.ofNullable(parseResultCacheMapper.selectByRepositoryIdAndCommitId(repositoryId, commitId));
    }

    @Override
    public List<ParseResultCache> findAllByRepositoryId(Long repositoryId) {
        return parseResultCacheMapper.selectAllByRepositoryId(repositoryId);
    }

    @Override
    public void deleteById(Long id) {
        parseResultCacheMapper.deleteById(id);
    }

    @Override
    public void deleteAllByRepositoryId(Long repositoryId) {
        parseResultCacheMapper.deleteAllByRepositoryId(repositoryId);
    }

    @Override
    public boolean existsByRepositoryIdAndCommitId(Long repositoryId, String commitId) {
        return parseResultCacheMapper.existsByRepositoryIdAndCommitId(repositoryId, commitId);
    }
}

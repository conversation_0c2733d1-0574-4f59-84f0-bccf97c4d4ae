package com.archscope.infrastructure.repository.converter;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.CapabilityExample;
import com.archscope.domain.valueobject.CapabilityId;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.domain.valueobject.Tag;
import com.archscope.domain.valueobject.Version;
import com.archscope.infrastructure.persistence.entity.CapabilityDO;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 能力领域对象与数据对象转换器
 */
@Component
public class CapabilityConverter {

    /**
     * 将领域对象转换为数据对象
     */
    public CapabilityDO toDataObject(Capability capability) {
        if (capability == null) {
            return null;
        }

        CapabilityDO capabilityDO = new CapabilityDO();
        // 对于新创建的能力，ID由数据库自动生成，这里不设置
        // capabilityDO.setId(capability.getId().getValue());
        capabilityDO.setServiceId(capability.getServiceId().getLongValue());
        capabilityDO.setName(capability.getName());
        capabilityDO.setDescription(capability.getDescription());
        capabilityDO.setFunctionSignature(""); // Capability实体没有functionSignature字段，设置为空
        capabilityDO.setInputSchema(new HashMap<>()); // Capability实体没有inputSchema字段，设置为空
        capabilityDO.setOutputSchema(new HashMap<>()); // Capability实体没有outputSchema字段，设置为空
        // 转换examples - 从Set<CapabilityExample>转换为List<Map<String,Object>>
        List<Map<String, Object>> examplesList = capability.getExamples().stream()
            .map(example -> {
                Map<String, Object> exampleMap = new HashMap<>();
                // 这里需要根据CapabilityExample的实际结构来转换
                // 暂时设置为空，需要进一步了解CapabilityExample的结构
                return exampleMap;
            })
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        capabilityDO.setExamples(examplesList);

        // 转换tags - 从Set<Tag>转换为List<String>
        List<String> tagsList = capability.getTags().stream()
            .map(tag -> tag.getValue())
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
        capabilityDO.setTags(tagsList);

        capabilityDO.setCreatedAt(LocalDateTime.ofInstant(capability.getCreatedAt(), ZoneId.systemDefault()));
        capabilityDO.setUpdatedAt(LocalDateTime.ofInstant(capability.getLastUpdatedAt(), ZoneId.systemDefault()));

        return capabilityDO;
    }

    /**
     * 将数据对象转换为领域对象
     */
    public Capability toDomainObject(CapabilityDO capabilityDO) {
        if (capabilityDO == null) {
            return null;
        }

        try {
            // 转换标签
            Set<Tag> tags = capabilityDO.getTags() != null ?
                capabilityDO.getTags().stream().map(Tag::of).collect(Collectors.toSet()) :
                Collections.emptySet();

            // 转换示例 - 暂时设置为空，需要根据实际需求实现
            Set<CapabilityExample> examples = Collections.emptySet();

            // 转换时间
            Instant createdAt = capabilityDO.getCreatedAt() != null ?
                capabilityDO.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant() :
                Instant.now();
            Instant lastUpdatedAt = capabilityDO.getUpdatedAt() != null ?
                capabilityDO.getUpdatedAt().atZone(ZoneId.systemDefault()).toInstant() :
                Instant.now();

            return Capability.restore(
                CapabilityId.of(capabilityDO.getId()),
                ServiceId.of(capabilityDO.getServiceId()),
                capabilityDO.getName(),
                capabilityDO.getDescription(),
                Version.of("1.0.0"), // 默认版本，需要根据实际需求调整
                examples,
                tags,
                false, // deprecated 默认为false
                createdAt,
                lastUpdatedAt
            );
        } catch (Exception e) {
            throw new RuntimeException("转换Capability实体失败", e);
        }
    }

    /**
     * 批量转换数据对象为领域对象
     */
    public List<Capability> toDomainObjects(List<CapabilityDO> capabilityDOs) {
        if (capabilityDOs == null) {
            return new ArrayList<>();
        }

        List<Capability> capabilities = new ArrayList<>();
        for (CapabilityDO capabilityDO : capabilityDOs) {
            Capability capability = toDomainObject(capabilityDO);
            if (capability != null) {
                capabilities.add(capability);
            }
        }
        return capabilities;
    }

    /**
     * 批量转换领域对象为数据对象
     */
    public List<CapabilityDO> toDataObjects(List<Capability> capabilities) {
        if (capabilities == null) {
            return new ArrayList<>();
        }

        List<CapabilityDO> capabilityDOs = new ArrayList<>();
        for (Capability capability : capabilities) {
            CapabilityDO capabilityDO = toDataObject(capability);
            if (capabilityDO != null) {
                capabilityDOs.add(capabilityDO);
            }
        }
        return capabilityDOs;
    }
}
package com.archscope.infrastructure.monitoring;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 服务发现性能监控
 */
// @Aspect  // 暂时完全禁用以解决启动问题
// @Component
@Slf4j
public class ServiceDiscoveryPerformanceMonitor {

    private final ConcurrentHashMap<String, AtomicLong> methodCallCounts = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, AtomicLong> methodTotalTimes = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> methodMaxTimes = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Long> methodMinTimes = new ConcurrentHashMap<>();

    /**
     * 监控Repository层方法性能
     */
    @Around("execution(* com.archscope.infrastructure.repository.ServiceRepositoryImpl.*(..))")
    public Object monitorServiceRepository(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethod(joinPoint, "ServiceRepository");
    }

    /**
     * 监控Repository层方法性能
     */
    @Around("execution(* com.archscope.infrastructure.repository.CapabilityRepositoryImpl.*(..))")
    public Object monitorCapabilityRepository(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethod(joinPoint, "CapabilityRepository");
    }

    /**
     * 监控Mapper层方法性能
     */
    @Around("execution(* com.archscope.infrastructure.persistence.mapper.ServiceMapper.*(..))")
    public Object monitorServiceMapper(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethod(joinPoint, "ServiceMapper");
    }

    /**
     * 监控Mapper层方法性能
     */
    @Around("execution(* com.archscope.infrastructure.persistence.mapper.CapabilityMapper.*(..))")
    public Object monitorCapabilityMapper(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethod(joinPoint, "CapabilityMapper");
    }

    /**
     * 监控缓存服务性能
     */
    @Around("execution(* com.archscope.infrastructure.cache.ServiceDiscoveryCacheService.*(..))")
    public Object monitorCacheService(ProceedingJoinPoint joinPoint) throws Throwable {
        return monitorMethod(joinPoint, "CacheService");
    }



    private Object monitorMethod(ProceedingJoinPoint joinPoint, String layer) throws Throwable {
        String methodName = layer + "." + joinPoint.getSignature().getName();
        long startTime = System.currentTimeMillis();
        
        try {
            Object result = joinPoint.proceed();
            long executionTime = System.currentTimeMillis() - startTime;
            
            // 记录性能统计
            recordPerformanceStats(methodName, executionTime);
            
            // 如果执行时间超过阈值，记录警告
            if (executionTime > getWarningThreshold(layer)) {
                log.warn("慢查询警告: {} 执行时间: {}ms, 参数: {}", 
                        methodName, executionTime, joinPoint.getArgs());
            } else if (log.isDebugEnabled()) {
                log.debug("方法执行: {} 耗时: {}ms", methodName, executionTime);
            }
            
            return result;
        } catch (Throwable throwable) {
            long executionTime = System.currentTimeMillis() - startTime;
            log.error("方法执行异常: {} 耗时: {}ms, 异常: {}", 
                    methodName, executionTime, throwable.getMessage());
            throw throwable;
        }
    }

    private void recordPerformanceStats(String methodName, long executionTime) {
        // 调用次数
        methodCallCounts.computeIfAbsent(methodName, k -> new AtomicLong(0)).incrementAndGet();
        
        // 总执行时间
        methodTotalTimes.computeIfAbsent(methodName, k -> new AtomicLong(0)).addAndGet(executionTime);
        
        // 最大执行时间
        methodMaxTimes.merge(methodName, executionTime, Math::max);
        
        // 最小执行时间
        methodMinTimes.merge(methodName, executionTime, Math::min);
    }

    private long getWarningThreshold(String layer) {
        switch (layer) {
            case "ServiceMapper":
            case "CapabilityMapper":
                return 1000; // 1秒
            case "ServiceRepository":
            case "CapabilityRepository":
                return 2000; // 2秒
            case "CacheService":
                return 100; // 100毫秒
            case "BatchService":
                return 5000; // 5秒
            default:
                return 1000; // 默认1秒
        }
    }

    /**
     * 获取性能统计报告
     */
    public String getPerformanceReport() {
        StringBuilder report = new StringBuilder();
        report.append("=== 服务发现性能统计报告 ===\n");
        
        methodCallCounts.entrySet().stream()
                .sorted((e1, e2) -> e2.getValue().get() > e1.getValue().get() ? 1 : -1)
                .forEach(entry -> {
                    String methodName = entry.getKey();
                    long callCount = entry.getValue().get();
                    long totalTime = methodTotalTimes.get(methodName).get();
                    long maxTime = methodMaxTimes.get(methodName);
                    long minTime = methodMinTimes.get(methodName);
                    long avgTime = totalTime / callCount;
                    
                    report.append(String.format(
                            "方法: %s\n" +
                            "  调用次数: %d\n" +
                            "  总耗时: %dms\n" +
                            "  平均耗时: %dms\n" +
                            "  最大耗时: %dms\n" +
                            "  最小耗时: %dms\n" +
                            "---\n",
                            methodName, callCount, totalTime, avgTime, maxTime, minTime
                    ));
                });
        
        return report.toString();
    }

    /**
     * 获取慢查询方法列表
     */
    public String getSlowMethods(long thresholdMs) {
        StringBuilder report = new StringBuilder();
        report.append("=== 慢查询方法列表 (阈值: ").append(thresholdMs).append("ms) ===\n");
        
        methodCallCounts.entrySet().stream()
                .filter(entry -> {
                    String methodName = entry.getKey();
                    long callCount = entry.getValue().get();
                    long totalTime = methodTotalTimes.get(methodName).get();
                    long avgTime = totalTime / callCount;
                    return avgTime > thresholdMs || methodMaxTimes.get(methodName) > thresholdMs * 2;
                })
                .sorted((e1, e2) -> {
                    String method1 = e1.getKey();
                    String method2 = e2.getKey();
                    long avg1 = methodTotalTimes.get(method1).get() / e1.getValue().get();
                    long avg2 = methodTotalTimes.get(method2).get() / e2.getValue().get();
                    return Long.compare(avg2, avg1);
                })
                .forEach(entry -> {
                    String methodName = entry.getKey();
                    long callCount = entry.getValue().get();
                    long totalTime = methodTotalTimes.get(methodName).get();
                    long maxTime = methodMaxTimes.get(methodName);
                    long avgTime = totalTime / callCount;
                    
                    report.append(String.format(
                            "方法: %s - 平均: %dms, 最大: %dms, 调用: %d次\n",
                            methodName, avgTime, maxTime, callCount
                    ));
                });
        
        return report.toString();
    }

    /**
     * 重置统计数据
     */
    public void resetStats() {
        methodCallCounts.clear();
        methodTotalTimes.clear();
        methodMaxTimes.clear();
        methodMinTimes.clear();
        log.info("性能统计数据已重置");
    }

    /**
     * 获取缓存命中率统计（需要配合缓存服务实现）
     */
    public String getCacheHitRateReport() {
        // 这里可以添加缓存命中率统计逻辑
        return "缓存命中率统计功能待实现";
    }
}
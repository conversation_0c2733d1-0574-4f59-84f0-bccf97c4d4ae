package com.archscope.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 查询日志与建议关联数据对象，对应数据库中的query_log_suggestion_relations表
 */
@Data
@TableName("query_log_suggestion_relations")
public class QueryLogSuggestionRelationDO {

    /**
     * 关联唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 查询日志ID
     */
    private Long queryLogId;

    /**
     * 需求建议ID
     */
    private Long suggestionId;

    /**
     * 相关性分数 (0-1)
     */
    private Float relevanceScore;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
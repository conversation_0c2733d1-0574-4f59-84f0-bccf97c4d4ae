package com.archscope.infrastructure.persistence.mapper;

import com.archscope.infrastructure.persistence.entity.QueryLogDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 查询日志Mapper接口，定义查询日志数据对象的数据库操作
 */
@Mapper
public interface QueryLogMapper extends BaseMapper<QueryLogDO> {

    /**
     * 根据查询日志ID查找查询日志
     *
     * @param id 查询日志ID
     * @return 找到的查询日志数据对象
     */
    @Select("SELECT * FROM query_logs WHERE id = #{id} LIMIT 1")
    QueryLogDO findById(@Param("id") Long id);

    /**
     * 根据查询类型查找查询日志列表
     *
     * @param queryType 查询类型
     * @return 找到的查询日志数据对象列表
     */
    @Select("SELECT * FROM query_logs WHERE query_type = #{queryType}")
    List<QueryLogDO> findByQueryType(@Param("queryType") String queryType);

    /**
     * 根据会话ID查找查询日志列表
     *
     * @param sessionId 会话ID
     * @return 找到的查询日志数据对象列表
     */
    @Select("SELECT * FROM query_logs WHERE session_id = #{sessionId}")
    List<QueryLogDO> findBySessionId(@Param("sessionId") String sessionId);

    /**
     * 根据用户ID查找查询日志列表
     *
     * @param userId 用户ID
     * @return 找到的查询日志数据对象列表
     */
    @Select("SELECT * FROM query_logs WHERE user_id = #{userId}")
    List<QueryLogDO> findByUserId(@Param("userId") String userId);

    /**
     * 根据用户反馈查找查询日志列表
     *
     * @param userFeedback 用户反馈
     * @return 找到的查询日志数据对象列表
     */
    @Select("SELECT * FROM query_logs WHERE user_feedback = #{userFeedback}")
    List<QueryLogDO> findByUserFeedback(@Param("userFeedback") String userFeedback);

    /**
     * 根据是否有结果查找查询日志列表
     *
     * @param hasResults 是否有结果
     * @return 找到的查询日志数据对象列表
     */
    @Select("SELECT * FROM query_logs WHERE has_results = #{hasResults}")
    List<QueryLogDO> findByHasResults(@Param("hasResults") boolean hasResults);

    /**
     * 根据是否已转换为需求查找查询日志列表
     *
     * @param convertedToRequirement 是否已转换为需求
     * @return 找到的查询日志数据对象列表
     */
    @Select("SELECT * FROM query_logs WHERE converted_to_requirement = #{convertedToRequirement}")
    List<QueryLogDO> findByConvertedToRequirement(@Param("convertedToRequirement") boolean convertedToRequirement);

    /**
     * 根据关联的需求ID查找查询日志列表
     *
     * @param relatedRequirementId 关联的需求ID
     * @return 找到的查询日志数据对象列表
     */
    @Select("SELECT * FROM query_logs WHERE related_requirement_id = #{relatedRequirementId}")
    List<QueryLogDO> findByRelatedRequirementId(@Param("relatedRequirementId") Long relatedRequirementId);

    /**
     * 查找最近的查询日志
     *
     * @param limit 限制数量
     * @return 最近的查询日志数据对象列表
     */
    @Select("SELECT * FROM query_logs ORDER BY created_at DESC LIMIT #{limit}")
    List<QueryLogDO> findRecentQueries(@Param("limit") int limit);

    /**
     * 更新查询日志的用户反馈
     *
     * @param id 查询日志ID
     * @param userFeedback 用户反馈
     * @return 影响的行数
     */
    @Update("UPDATE query_logs SET user_feedback = #{userFeedback} WHERE id = #{id}")
    int updateUserFeedback(@Param("id") Long id, @Param("userFeedback") String userFeedback);

    /**
     * 更新查询日志的需求转换状态
     *
     * @param queryLogId 查询日志ID
     * @param convertedToRequirement 是否已转换为需求
     * @param relatedRequirementId 关联的需求ID
     * @return 影响的行数
     */
    @Update("UPDATE query_logs SET converted_to_requirement = #{convertedToRequirement}, related_requirement_id = #{relatedRequirementId} WHERE query_log_id = #{queryLogId}")
    int updateRequirementConversion(@Param("queryLogId") String queryLogId, @Param("convertedToRequirement") boolean convertedToRequirement, @Param("relatedRequirementId") String relatedRequirementId);

    /**
     * 分页查询查询日志
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的查询日志数据对象列表
     */
    @Select("SELECT * FROM query_logs ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<QueryLogDO> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);
}
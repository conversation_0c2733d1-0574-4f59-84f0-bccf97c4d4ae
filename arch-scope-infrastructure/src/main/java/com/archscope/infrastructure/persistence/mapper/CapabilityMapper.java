package com.archscope.infrastructure.persistence.mapper;

import com.archscope.infrastructure.persistence.entity.CapabilityDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 能力Mapper接口，定义能力数据对象的数据库操作
 */
@Mapper
public interface CapabilityMapper extends BaseMapper<CapabilityDO> {

    /**
     * 根据能力ID查找能力
     *
     * @param id 能力ID
     * @return 找到的能力数据对象
     */
    @Select("SELECT * FROM capabilities WHERE id = #{id} LIMIT 1")
    CapabilityDO findById(@Param("id") Long id);

    /**
     * 根据服务ID和能力名称查找能力
     *
     * @param serviceId 服务ID
     * @param name 能力名称
     * @return 找到的能力数据对象
     */
    @Select("SELECT * FROM capabilities WHERE service_id = #{serviceId} AND name = #{name} LIMIT 1")
    CapabilityDO findByServiceIdAndName(@Param("serviceId") Long serviceId, @Param("name") String name);

    /**
     * 根据服务ID查找能力列表
     *
     * @param serviceId 服务ID
     * @return 找到的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities WHERE service_id = #{serviceId}")
    List<CapabilityDO> findByServiceId(@Param("serviceId") String serviceId);

    /**
     * 根据名称查找能力列表
     *
     * @param name 能力名称
     * @return 找到的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities WHERE name = #{name}")
    List<CapabilityDO> findByName(@Param("name") String name);

    /**
     * 查找所有能力名称
     *
     * @return 所有能力名称列表
     */
    @Select("SELECT DISTINCT name FROM capabilities")
    List<String> findAllCapabilityNames();

    /**
     * 根据能力名称查找提供该能力的服务ID列表
     *
     * @param capabilityName 能力名称
     * @return 提供该能力的服务ID列表
     */
    @Select("SELECT service_id FROM capabilities WHERE name = #{capabilityName}")
    List<Long> findServiceIdsByCapabilityName(@Param("capabilityName") String capabilityName);

    /**
     * 删除服务的所有能力
     *
     * @param serviceId 服务ID
     * @return 删除的记录数
     */
    @Delete("DELETE FROM capabilities WHERE service_id = #{serviceId}")
    int deleteByServiceId(@Param("serviceId") Long serviceId);
    
    /**
     * 根据标签查找能力列表
     *
     * @param tag 标签
     * @return 找到的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities WHERE JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))")
    List<CapabilityDO> findByTag(@Param("tag") String tag);
    
    /**
     * 根据描述模糊查询能力
     *
     * @param descriptionLike 描述模糊匹配
     * @return 找到的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities WHERE description LIKE CONCAT('%', #{descriptionLike}, '%')")
    List<CapabilityDO> findByDescriptionLike(@Param("descriptionLike") String descriptionLike);
    
    /**
     * 查找最近添加的能力
     *
     * @param limit 限制数量
     * @return 最近添加的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities ORDER BY created_at DESC LIMIT #{limit}")
    List<CapabilityDO> findRecentlyAdded(@Param("limit") int limit);
    
    /**
     * 查找最近更新的能力
     *
     * @param limit 限制数量
     * @return 最近更新的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities ORDER BY updated_at DESC LIMIT #{limit}")
    List<CapabilityDO> findRecentlyUpdated(@Param("limit") int limit);
    
    /**
     * 分页查询能力
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的能力数据对象列表
     */
    @Select("SELECT * FROM capabilities LIMIT #{limit} OFFSET #{offset}")
    List<CapabilityDO> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);
    
    /**
     * 根据多个服务ID查找能力列表
     *
     * @param serviceIds 服务ID列表
     * @return 找到的能力数据对象列表
     */
    @Select("<script>SELECT * FROM capabilities WHERE service_id IN " +
            "<foreach collection='serviceIds' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
            "</script>")
    List<CapabilityDO> findByServiceIds(@Param("serviceIds") List<String> serviceIds);

    /**
     * 批量查询能力（优化版本，使用IN查询）
     *
     * @param capabilityIds 能力ID列表
     * @return 找到的能力数据对象列表
     */
    @Select("<script>SELECT * FROM capabilities WHERE capability_id IN " +
            "<foreach collection='capabilityIds' item='id' open='(' separator=',' close=')'>#{id}</foreach>" +
            "</script>")
    List<CapabilityDO> findByCapabilityIds(@Param("capabilityIds") List<String> capabilityIds);

    /**
     * 根据多个标签查询能力（优化版本）
     *
     * @param tags 标签列表
     * @return 找到的能力数据对象列表
     */
    @Select("<script>SELECT * FROM capabilities WHERE " +
            "<foreach collection='tags' item='tag' separator=' AND '>" +
            "JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))" +
            "</foreach>" +
            "</script>")
    List<CapabilityDO> findByMultipleTags(@Param("tags") List<String> tags);

    /**
     * 统计各服务的能力数量
     *
     * @return 统计结果
     */
    @Select("SELECT c.service_id, s.name as service_name, COUNT(c.capability_id) as capability_count " +
            "FROM capabilities c " +
            "LEFT JOIN services s ON c.service_id = s.service_id " +
            "GROUP BY c.service_id, s.name " +
            "ORDER BY capability_count DESC")
    List<java.util.Map<String, Object>> countByService();

    /**
     * 全文搜索能力（名称和描述）
     *
     * @param keyword 搜索关键词
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 搜索结果
     */
    @Select("SELECT * FROM capabilities WHERE " +
            "(name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%')) " +
            "ORDER BY " +
            "CASE " +
            "WHEN name LIKE CONCAT('%', #{keyword}, '%') THEN 1 " +
            "WHEN description LIKE CONCAT('%', #{keyword}, '%') THEN 2 " +
            "ELSE 3 END, " +
            "updated_at DESC " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<CapabilityDO> searchCapabilities(@Param("keyword") String keyword, 
                                         @Param("limit") int limit, 
                                         @Param("offset") int offset);

    /**
     * 统计搜索结果总数
     *
     * @param keyword 搜索关键词
     * @return 总数
     */
    @Select("SELECT COUNT(*) FROM capabilities WHERE " +
            "(name LIKE CONCAT('%', #{keyword}, '%') OR description LIKE CONCAT('%', #{keyword}, '%'))")
    long countSearchResults(@Param("keyword") String keyword);

    /**
     * 查询能力及其服务信息（连接查询优化）
     *
     * @param limit 限制数量
     * @param offset 偏移量
     * @return 能力和服务信息
     */
    @Select("SELECT c.*, s.name as service_name, s.version as service_version, s.type as service_type " +
            "FROM capabilities c " +
            "INNER JOIN services s ON c.service_id = s.id " +
            "WHERE s.status = 'ACTIVE' " +
            "ORDER BY c.updated_at DESC " +
            "LIMIT #{limit} OFFSET #{offset}")
    List<java.util.Map<String, Object>> findCapabilitiesWithServiceInfo(@Param("limit") int limit, 
                                                                        @Param("offset") int offset);
}
package com.archscope.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 需求数据对象，对应数据库中的requirements表
 */
@Data
@TableName(value = "requirements", autoResultMap = true)
public class RequirementDO {

    /**
     * 需求唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 需求标题
     */
    private String title;

    /**
     * 需求详细描述
     */
    private String description;

    /**
     * 提交者
     */
    private String submittedBy;

    /**
     * 相关服务ID
     */
    private Long relatedServiceId;

    /**
     * 优先级: HIGH, MEDIUM, LOW
     */
    private String priority;

    /**
     * 状态: NEW, UNDER_REVIEW, ACCEPTED, REJECTED, IMPLEMENTED
     */
    private String status;

    /**
     * 反馈
     */
    private String feedback;

    /**
     * 标签 (JSON数组)
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> tags;

    /**
     * 是否从建议转换而来
     */
    private Boolean createdFromSuggestion;

    /**
     * 原始建议ID
     */
    private String originalSuggestionId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
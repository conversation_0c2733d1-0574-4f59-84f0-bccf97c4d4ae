package com.archscope.infrastructure.persistence.mapper;

import com.archscope.infrastructure.persistence.entity.QueryLogSuggestionRelationDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 查询日志与建议关联Mapper接口，定义查询日志与建议关联数据对象的数据库操作
 */
@Mapper
public interface QueryLogSuggestionRelationMapper extends BaseMapper<QueryLogSuggestionRelationDO> {

    /**
     * 根据关联ID查找查询日志与建议关联
     *
     * @param relationId 关联ID
     * @return 找到的查询日志与建议关联数据对象
     */
    @Select("SELECT * FROM query_log_suggestion_relations WHERE relation_id = #{relationId} LIMIT 1")
    QueryLogSuggestionRelationDO findByRelationId(@Param("relationId") String relationId);

    /**
     * 根据查询日志ID查找查询日志与建议关联列表
     *
     * @param queryLogId 查询日志ID
     * @return 找到的查询日志与建议关联数据对象列表
     */
    @Select("SELECT * FROM query_log_suggestion_relations WHERE query_log_id = #{queryLogId}")
    List<QueryLogSuggestionRelationDO> findByQueryLogId(@Param("queryLogId") String queryLogId);

    /**
     * 根据建议ID查找查询日志与建议关联列表
     *
     * @param suggestionId 建议ID
     * @return 找到的查询日志与建议关联数据对象列表
     */
    @Select("SELECT * FROM query_log_suggestion_relations WHERE suggestion_id = #{suggestionId}")
    List<QueryLogSuggestionRelationDO> findBySuggestionId(@Param("suggestionId") String suggestionId);

    /**
     * 根据查询日志ID和建议ID查找查询日志与建议关联
     *
     * @param queryLogId 查询日志ID
     * @param suggestionId 建议ID
     * @return 找到的查询日志与建议关联数据对象
     */
    @Select("SELECT * FROM query_log_suggestion_relations WHERE query_log_id = #{queryLogId} AND suggestion_id = #{suggestionId} LIMIT 1")
    QueryLogSuggestionRelationDO findByQueryLogIdAndSuggestionId(@Param("queryLogId") String queryLogId, @Param("suggestionId") String suggestionId);

    /**
     * 根据相关性分数查找查询日志与建议关联列表
     *
     * @param minRelevanceScore 最小相关性分数
     * @return 找到的查询日志与建议关联数据对象列表
     */
    @Select("SELECT * FROM query_log_suggestion_relations WHERE relevance_score >= #{minRelevanceScore}")
    List<QueryLogSuggestionRelationDO> findByMinRelevanceScore(@Param("minRelevanceScore") float minRelevanceScore);

    /**
     * 删除查询日志的所有关联
     *
     * @param queryLogId 查询日志ID
     * @return 影响的行数
     */
    @Delete("DELETE FROM query_log_suggestion_relations WHERE query_log_id = #{queryLogId}")
    int deleteByQueryLogId(@Param("queryLogId") String queryLogId);

    /**
     * 删除建议的所有关联
     *
     * @param suggestionId 建议ID
     * @return 影响的行数
     */
    @Delete("DELETE FROM query_log_suggestion_relations WHERE suggestion_id = #{suggestionId}")
    int deleteBySuggestionId(@Param("suggestionId") String suggestionId);

    /**
     * 查找最近创建的查询日志与建议关联
     *
     * @param limit 限制数量
     * @return 最近创建的查询日志与建议关联数据对象列表
     */
    @Select("SELECT * FROM query_log_suggestion_relations ORDER BY created_at DESC LIMIT #{limit}")
    List<QueryLogSuggestionRelationDO> findRecentlyCreated(@Param("limit") int limit);

    /**
     * 分页查询查询日志与建议关联
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的查询日志与建议关联数据对象列表
     */
    @Select("SELECT * FROM query_log_suggestion_relations ORDER BY created_at DESC LIMIT #{limit} OFFSET #{offset}")
    List<QueryLogSuggestionRelationDO> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);
}
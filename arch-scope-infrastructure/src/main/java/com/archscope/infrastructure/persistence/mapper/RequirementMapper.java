package com.archscope.infrastructure.persistence.mapper;

import com.archscope.infrastructure.persistence.entity.RequirementDO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Delete;

import java.util.List;

/**
 * 需求Mapper接口，定义需求数据对象的数据库操作
 */
@Mapper
public interface RequirementMapper extends BaseMapper<RequirementDO> {

    /**
     * 根据需求ID查找需求
     *
     * @param id 需求ID
     * @return 找到的需求数据对象
     */
    @Select("SELECT * FROM requirements WHERE id = #{id} LIMIT 1")
    RequirementDO findById(@Param("id") Long id);

    /**
     * 根据优先级查找需求列表
     *
     * @param priority 需求优先级
     * @return 找到的需求数据对象列表
     */
    @Select("SELECT * FROM requirements WHERE priority = #{priority}")
    List<RequirementDO> findByPriority(@Param("priority") String priority);

    /**
     * 分页查询需求
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的需求数据对象列表
     */
    @Select("SELECT * FROM requirements LIMIT #{limit} OFFSET #{offset}")
    List<RequirementDO> findWithPagination(@Param("offset") int offset, @Param("limit") int limit);

    /**
     * 根据需求ID查找需求能力列表
     *
     * @param requirementId 需求ID
     * @return 需求能力ID列表
     */
    @Select("SELECT capability_id FROM requirement_capabilities WHERE requirement_id = #{requirementId}")
    List<Long> findCapabilitiesByRequirementId(@Param("requirementId") Long requirementId);

    /**
     * 根据能力名称查找需求列表
     *
     * @param capabilityName 能力名称
     * @return 找到的需求ID列表
     */
    @Select("SELECT requirement_id FROM requirement_capabilities WHERE capability_name = #{capabilityName}")
    List<String> findRequirementIdsByCapabilityName(@Param("capabilityName") String capabilityName);
    
    /**
     * 根据状态查找需求列表
     *
     * @param status 需求状态
     * @return 找到的需求数据对象列表
     */
    @Select("SELECT * FROM requirements WHERE status = #{status}")
    List<RequirementDO> findByStatus(@Param("status") String status);
    
    /**
     * 根据提交者查找需求列表
     *
     * @param submittedBy 提交者
     * @return 找到的需求数据对象列表
     */
    @Select("SELECT * FROM requirements WHERE submitted_by = #{submittedBy}")
    List<RequirementDO> findBySubmittedBy(@Param("submittedBy") String submittedBy);
    
    /**
     * 根据相关服务ID查找需求列表
     *
     * @param relatedServiceId 相关服务ID
     * @return 找到的需求数据对象列表
     */
    @Select("SELECT * FROM requirements WHERE related_service_id = #{relatedServiceId}")
    List<RequirementDO> findByRelatedServiceId(@Param("relatedServiceId") Long relatedServiceId);
    
    /**
     * 根据标签查找需求列表
     *
     * @param tag 标签
     * @return 找到的需求数据对象列表
     */
    @Select("SELECT * FROM requirements WHERE JSON_CONTAINS(tags, JSON_ARRAY(#{tag}))")
    List<RequirementDO> findByTag(@Param("tag") String tag);
    
    /**
     * 查找最近创建的需求
     *
     * @param limit 限制数量
     * @return 最近创建的需求数据对象列表
     */
    @Select("SELECT * FROM requirements ORDER BY created_at DESC LIMIT #{limit}")
    List<RequirementDO> findRecentlyCreated(@Param("limit") int limit);
    
    /**
     * 查找最近更新的需求
     *
     * @param limit 限制数量
     * @return 最近更新的需求数据对象列表
     */
    @Select("SELECT * FROM requirements ORDER BY updated_at DESC LIMIT #{limit}")
    List<RequirementDO> findRecentlyUpdated(@Param("limit") int limit);
    
    /**
     * 添加需求能力关联
     *
     * @param requirementId 需求ID
     * @param capabilityId 能力ID
     * @return 影响的行数
     */
    @Insert("INSERT INTO requirement_capabilities (requirement_id, capability_id) VALUES (#{requirementId}, #{capabilityId})")
    int addRequirementCapability(@Param("requirementId") Long requirementId, @Param("capabilityId") Long capabilityId);
    
    /**
     * 删除需求能力关联
     *
     * @param requirementId 需求ID
     * @param capabilityName 能力名称
     * @return 影响的行数
     */
    @Delete("DELETE FROM requirement_capabilities WHERE requirement_id = #{requirementId} AND capability_name = #{capabilityName}")
    int removeRequirementCapability(@Param("requirementId") String requirementId, @Param("capabilityName") String capabilityName);
    
    /**
     * 删除需求的所有能力关联
     *
     * @param requirementId 需求ID
     * @return 影响的行数
     */
    @Delete("DELETE FROM requirement_capabilities WHERE requirement_id = #{requirementId}")
    int removeAllRequirementCapabilities(@Param("requirementId") String requirementId);
    
    /**
     * 根据原始建议ID查找需求
     *
     * @param originalSuggestionId 原始建议ID
     * @return 找到的需求数据对象
     */
    @Select("SELECT * FROM requirements WHERE original_suggestion_id = #{originalSuggestionId} LIMIT 1")
    RequirementDO findByOriginalSuggestionId(@Param("originalSuggestionId") Long originalSuggestionId);
}
package com.archscope.infrastructure.persistence.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 需求能力关联数据对象，对应数据库中的requirement_capabilities表
 */
@Data
@TableName("requirement_capabilities")
public class RequirementCapabilityDO {

    /**
     * 关联唯一标识符
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 需求ID
     */
    private Long requirementId;

    /**
     * 能力ID
     */
    private Long capabilityId;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
}
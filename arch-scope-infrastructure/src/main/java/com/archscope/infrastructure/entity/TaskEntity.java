package com.archscope.infrastructure.entity;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.ResourceUsage;
import com.archscope.domain.valueobject.TaskStatus;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * Task实体的基础设施层包装类
 * 用于MyBatis-Plus的表名映射，避免domain层依赖基础设施技术
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("tasks")
public class TaskEntity extends Task {

    /**
     * 重写 resourceUsage 字段，标记为不存在于数据库中
     * 这样可以避免 MyBatis-Plus 自动查询时包含这个字段
     */
    @TableField(exist = false)
    private ResourceUsage resourceUsage;

    /**
     * 默认构造函数
     */
    public TaskEntity() {
        super();
    }
    
    /**
     * 从domain实体创建infrastructure实体
     */
    public TaskEntity(Task task) {
        super();
        if (task != null) {
            this.setId(task.getId());
            this.setProjectId(task.getProjectId());
            this.setStatus(task.getStatus());
            this.setPriority(task.getPriority());
            this.setCreatedAt(task.getCreatedAt());
            this.setUpdatedAt(task.getUpdatedAt());
            this.setStartedAt(task.getStartedAt());
            this.setCompletedAt(task.getCompletedAt());
            this.setErrorMessage(task.getErrorMessage());
            this.setDetailedStatus(task.getDetailedStatus());
            this.setProgress(task.getProgress());
            this.setErrorLog(task.getErrorLog());
            this.setRetryCount(task.getRetryCount());
            this.setTaskType(task.getTaskType());
            this.setExecutionTime(task.getExecutionTime());
            this.setResourceUsage(task.getResourceUsage());
            this.setName(task.getName());
            this.setDescription(task.getDescription());
            this.setUserId(task.getUserId());
            this.setAssigneeId(task.getAssigneeId());
            this.setResult(task.getResult());
            this.setParameters(task.getParameters());
            this.setWorkerId(task.getWorkerId());
            this.setProcessingStartedAt(task.getProcessingStartedAt());
            this.setTimeoutAt(task.getTimeoutAt());
            this.setOverallStatus(task.getOverallStatus());
            this.setCommitId(task.getCommitId());
            this.setResults(task.getResults());
            this.setExecutionTimeMs(task.getExecutionTimeMs());
            this.setMaxRetries(task.getMaxRetries());
            this.setLastErrorDetail(task.getLastErrorDetail());
            this.setTaskVersion(task.getTaskVersion());
        }
    }
    
    /**
     * 转换为domain实体
     */
    public Task toDomainEntity() {
        return Task.builder()
                .id(this.getId())
                .projectId(this.getProjectId())
                .status(this.getStatus())
                .priority(this.getPriority())
                .createdAt(this.getCreatedAt())
                .updatedAt(this.getUpdatedAt())
                .startedAt(this.getStartedAt())
                .completedAt(this.getCompletedAt())
                .errorMessage(this.getErrorMessage())
                .detailedStatus(this.getDetailedStatus())
                .progress(this.getProgress())
                .errorLog(this.getErrorLog())
                .retryCount(this.getRetryCount())
                .taskType(this.getTaskType())
                .executionTime(this.getExecutionTime())
                .resourceUsage(this.getResourceUsage())
                .name(this.getName())
                .description(this.getDescription())
                .userId(this.getUserId())
                .assigneeId(this.getAssigneeId())
                .result(this.getResult())
                .parameters(this.getParameters())
                .workerId(this.getWorkerId())
                .processingStartedAt(this.getProcessingStartedAt())
                .timeoutAt(this.getTimeoutAt())
                .overallStatus(this.getOverallStatus())
                .commitId(this.getCommitId())
                .results(this.getResults())
                .executionTimeMs(this.getExecutionTimeMs())
                .maxRetries(this.getMaxRetries())
                .lastErrorDetail(this.getLastErrorDetail())
                .taskVersion(this.getTaskVersion())
                .build();
    }
}

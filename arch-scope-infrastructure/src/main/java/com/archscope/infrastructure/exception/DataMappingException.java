package com.archscope.infrastructure.exception;

/**
 * 数据映射异常
 */
public class DataMappingException extends ServiceDiscoveryInfrastructureException {

    public DataMappingException(String entityType) {
        super("Failed to map data for entity type: " + entityType);
    }

    public DataMappingException(String entityType, Throwable cause) {
        super("Failed to map data for entity type: " + entityType, cause);
    }

    public DataMappingException(String sourceType, String targetType, Throwable cause) {
        super("Failed to map from " + sourceType + " to " + targetType, cause);
    }
}
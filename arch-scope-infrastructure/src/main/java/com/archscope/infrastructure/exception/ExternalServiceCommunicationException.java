package com.archscope.infrastructure.exception;

/**
 * 外部服务通信异常
 */
public class ExternalServiceCommunicationException extends ServiceDiscoveryInfrastructureException {

    public ExternalServiceCommunicationException(String serviceName) {
        super("Failed to communicate with external service: " + serviceName);
    }

    public ExternalServiceCommunicationException(String serviceName, Throwable cause) {
        super("Failed to communicate with external service: " + serviceName, cause);
    }

    public ExternalServiceCommunicationException(String serviceName, String operation, Throwable cause) {
        super("Failed to communicate with external service: " + serviceName + " during operation: " + operation, cause);
    }
}
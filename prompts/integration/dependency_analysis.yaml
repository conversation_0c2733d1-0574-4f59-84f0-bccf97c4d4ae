metadata:
  name: "依赖分析提示词"
  description: "用于分析项目依赖关系的提示词"
  version: "1.0.0"
  last_updated: "2023-11-01"
  author: "ArchScope团队"

prompts:
  project_dependencies:
    description: "分析项目依赖关系"
    model: "gpt-4"
    parameters:
      temperature: 0.2
      max_tokens: 2500
    template: |
      分析以下项目依赖信息，提供项目的依赖关系概览。
      
      项目名称: {{project_name}}
      
      依赖文件:
      {{dependency_files}}
      
      请提供以下分析:
      1. 主要依赖: 项目使用的主要依赖库和框架
      2. 依赖分类: 按功能分类的依赖(如Web框架、数据库、工具库等)
      3. 版本信息: 依赖的版本信息，包括是否使用最新版本
      4. 潜在问题: 依赖中可能存在的问题(如版本冲突、过时依赖等)
      5. 优化建议: 依赖优化的建议
      
      请以Markdown格式返回，使用表格、列表等元素组织内容，使其易于阅读和理解。

  module_dependencies:
    description: "分析模块间依赖关系"
    model: "gpt-4"
    parameters:
      temperature: 0.2
      max_tokens: 2500
    template: |
      分析以下模块导入信息，提供模块间的依赖关系。
      
      项目名称: {{project_name}}
      
      模块导入信息:
      {{module_imports}}
      
      请提供以下分析:
      1. 依赖图: 模块间的依赖关系图(使用Mermaid语法)
      2. 核心模块: 被广泛依赖的核心模块
      3. 依赖层次: 模块的依赖层次结构
      4. 循环依赖: 存在的循环依赖问题
      5. 优化建议: 依赖结构优化的建议
      
      请以Markdown格式返回，使用Mermaid图表、表格、列表等元素组织内容。

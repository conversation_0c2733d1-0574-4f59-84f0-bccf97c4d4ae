metadata:
  name: "架构总结提示词"
  description: "用于生成项目架构总结的提示词"
  version: "1.0.0"
  last_updated: "2023-11-01"
  author: "ArchScope团队"

prompts:
  architecture_summary:
    description: "生成项目架构总结"
    model: "gpt-4"
    parameters:
      temperature: 0.3
      max_tokens: 3000
    template: |
      基于以下项目分析结果，生成一份项目架构总结。
      
      项目名称: {{project_name}}
      
      项目结构:
      {{project_structure}}
      
      核心组件:
      {{core_components}}
      
      依赖关系:
      {{dependencies}}
      
      请生成一份详细的架构总结，包括以下部分:
      1. 架构概览: 系统的整体架构风格和模式
      2. 核心组件: 系统的主要组件及其职责
      3. 组件关系: 组件之间的交互和依赖关系
      4. 数据流: 系统中的主要数据流
      5. 技术栈: 使用的主要技术和框架
      6. 架构评估: 架构的优点和潜在问题
      7. 改进建议: 架构改进的建议
      
      请以Markdown格式返回，使用图表、表格、列表等元素组织内容，使其易于阅读和理解。
      
  design_patterns:
    description: "识别项目中使用的设计模式"
    model: "gpt-4"
    parameters:
      temperature: 0.2
      max_tokens: 2500
    template: |
      分析以下项目代码，识别其中使用的设计模式。
      
      项目名称: {{project_name}}
      
      代码示例:
      {{code_samples}}
      
      请识别并分析项目中使用的设计模式，包括以下内容:
      1. 设计模式列表: 项目中使用的所有设计模式
      2. 模式应用: 每种模式的具体应用场景和实现方式
      3. 模式评估: 设计模式使用的适当性评估
      4. 改进建议: 设计模式应用的改进建议
      
      请以Markdown格式返回，对每种识别到的设计模式提供详细说明和代码示例。

metadata:
  name: "Markdown文档分析提示词"
  description: "用于分析Markdown文档结构和内容的提示词集合"
  version: "1.0.0"
  last_updated: "2023-11-01"
  author: "ArchScope团队"

prompts:
  document_structure:
    description: "分析Markdown文档的结构"
    model: "gpt-4"
    parameters:
      temperature: 0.1
      max_tokens: 2000
    template: |
      分析以下Markdown文档，提取其结构和主要内容。
      
      文件路径: {{file_path}}
      
      ```markdown
      {{content}}
      ```
      
      请提取以下信息:
      1. 标题结构: 文档的标题层次结构
      2. 主要内容: 每个部分的主要内容摘要
      3. 关键概念: 文档中提到的关键概念和术语
      4. 代码示例: 文档中包含的代码示例
      5. 链接和引用: 文档中的链接和引用
      6. 图表: 文档中的图表描述(如Mermaid图表)
      
      返回格式示例:
      ```json
      {
        "title": "文档标题",
        "structure": [
          {
            "level": 1,
            "title": "一级标题",
            "children": [
              {
                "level": 2,
                "title": "二级标题",
                "children": []
              }
            ]
          }
        ],
        "section_summaries": {
          "一级标题": "这部分主要介绍...",
          "二级标题": "这部分详细说明..."
        },
        "key_concepts": [
          {"term": "概念1", "context": "概念1在文档中的上下文"}
        ],
        "code_examples": [
          {
            "language": "java",
            "code": "public class Example {...}",
            "context": "这个示例展示了..."
          }
        ],
        "links": [
          {"text": "链接文本", "url": "链接URL"}
        ],
        "diagrams": [
          {
            "type": "mermaid",
            "content": "graph TD; A-->B;",
            "description": "图表描述"
          }
        ]
      }
      ```
      
      请确保JSON格式正确，并尽可能详细地提取信息。

  content_summary:
    description: "生成Markdown文档的内容摘要"
    model: "gpt-4"
    parameters:
      temperature: 0.3
      max_tokens: 1500
    template: |
      为以下Markdown文档生成一份内容摘要。
      
      文件路径: {{file_path}}
      
      ```markdown
      {{content}}
      ```
      
      请提供以下内容:
      1. 文档主题: 文档的主要主题和目的
      2. 关键要点: 文档中的主要观点和要点
      3. 结论或建议: 文档的结论或提出的建议
      4. 适用场景: 文档内容适用的场景或对象
      
      请以Markdown格式返回摘要，使用简洁明了的语言，突出文档的核心内容。

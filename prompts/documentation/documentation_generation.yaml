metadata:
  name: "文档生成提示词"
  description: "用于生成项目文档的提示词集合"
  version: "1.0.0"
  last_updated: "2023-11-01"
  author: "ArchScope团队"

prompts:
  user_manual:
    description: "生成用户手册"
    model: "claude-3-opus"
    parameters:
      temperature: 0.7
      max_tokens: 4000
    template: |
      基于以下项目信息，生成一份用户手册，包括如何使用系统的主要功能。
      
      项目名称: {{project_name}}
      项目描述: {{project_description}}
      主要功能:
      {{main_features}}
      
      请生成一份详细的用户手册，包括以下部分:
      1. 如何注册和登录
      2. 如何添加和管理项目
      3. 如何查看项目文档
      4. 如何使用版本对比功能
      5. 常见问题解答
      
      请使用Markdown格式，并参考界面原型中"用户手册"页面的内容结构进行组织。
      使用清晰的标题、列表、表格和提示框等元素，使文档易于阅读和理解。
      
  api_documentation:
    description: "生成API文档"
    model: "claude-3-opus"
    parameters:
      temperature: 0.3
      max_tokens: 3000
    template: |
      基于以下API信息，生成一份API文档。
      
      API列表:
      {{api_list}}
      
      请生成一份详细的API文档，包括以下内容:
      1. API概述
      2. 认证方式
      3. 请求格式
      4. 响应格式
      5. 错误码
      6. 每个API的详细说明，包括:
         - 端点
         - 方法
         - 请求参数
         - 响应字段
         - 示例请求和响应
      
      请使用Markdown格式，并参考界面原型中"接口文档"页面的内容结构进行组织。

  extension_guide:
    description: "生成扩展能力指南"
    model: "claude-3-opus"
    parameters:
      temperature: 0.5
      max_tokens: 3000
    template: |
      基于以下项目信息，生成一份扩展能力指南，说明如何扩展系统功能。
      
      项目名称: {{project_name}}
      项目描述: {{project_description}}
      扩展点:
      {{extension_points}}
      
      请生成一份详细的扩展能力指南，包括以下部分:
      1. 扩展概述: 系统支持的扩展类型和方式
      2. 如何开发自定义解析器: 详细步骤和示例
      3. 可用的扩展点和插件机制: 列表和说明
      4. 扩展开发最佳实践: 建议和注意事项
      5. 扩展部署和发布: 如何部署和发布扩展
      
      请使用Markdown格式，并参考界面原型中"扩展能力"页面的内容结构进行组织。
      使用清晰的标题、列表、表格和代码示例等元素，使文档易于阅读和理解。

metadata:
  name: "llms.txt生成提示词"
  description: "用于生成项目的llms.txt文件的提示词"
  version: "1.0.0"
  last_updated: "2023-11-01"
  author: "ArchScope团队"

prompts:
  generate_llms_txt:
    description: "生成项目的llms.txt文件"
    model: "gpt-4"
    parameters:
      temperature: 0.3
      max_tokens: 1500
    template: |
      基于以下项目信息，生成一份llms.txt文件，用于指导LLM如何理解和交互与该项目。
      
      项目名称: {{project_name}}
      项目版本: {{project_version}}
      项目描述: {{project_description}}
      项目结构:
      {{project_structure}}
      
      请生成一份llms.txt文件，包括以下部分:
      1. 项目描述
      2. 导航指南(主要目录及其用途)
      3. 访问控制(允许和禁止访问的内容)
      4. 交互指南(如何与项目交互)
      5. 注意事项
      
      请参考界面原型中"llms.txt"页面的内容结构进行组织。
      使用简单的文本格式，确保易于LLM理解和处理。
      
      示例格式:
      ```
      # 项目名称: ArchScope
      # 版本: 1.0.0
      # 最后更新: 2023-10-26
      
      [项目描述]
      ArchScope 是一个面向开发者的架构观测和守护系统。
      
      [导航指南]
      - docs/ - 项目文档目录
      - src/ - 源代码目录
      - api/ - API 定义和实现
      
      [访问控制]
      允许:
      - 公开文档
      - API 规范
      - 示例代码
      
      禁止:
      - 私有配置文件
      - 测试数据
      
      [交互指南]
      - 使用 API 端点获取项目信息
      - 通过 Webhook 接收变更通知
      
      [注意事项]
      - API 密钥不应硬编码在客户端代码中
      - 大型项目可能需要分批处理
      ```

metadata:
  name: "Go代码分析提示词"
  description: "用于分析Go代码并生成符合FileParseResult格式的结构化输出"
  version: "1.0.0"
  last_updated: "2025-05-04"
  author: "ArchScope团队"

prompts:
  code_structure:
    description: "分析Go代码结构并生成标准化输出"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下Go代码文件，提取其结构信息并以严格的JSON格式返回，确保完全符合FileParseResult模型规范。

      文件路径: {{file_path}}

      ```go
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "GO",
        "packageName": "包名",
        "imports": [
          "导入的包列表"
        ],
        "classDefinitions": [
          {
            "name": "结构体名称",
            "fullyQualifiedName": "包名.结构体名称",
            "packageName": "包名",
            "superClass": null,
            "interfaces": [
              "实现的接口列表"
            ],
            "type": "CLASS",
            "accessModifier": "PUBLIC/PRIVATE",
            "fields": [
              {
                "name": "字段名",
                "type": "字段类型",
                "accessModifier": "PUBLIC/PRIVATE",
                "isStatic": false,
                "isFinal": false,
                "annotations": [],
                "comment": "字段注释"
              }
            ],
            "methods": [
              {
                "name": "方法名",
                "returnType": "返回类型",
                "accessModifier": "PUBLIC/PRIVATE",
                "parameters": [
                  {
                    "name": "参数名",
                    "type": "参数类型",
                    "annotations": []
                  }
                ],
                "annotations": [],
                "isStatic": false,
                "isFinal": false,
                "isAbstract": false,
                "comment": "方法注释"
              }
            ],
            "innerClasses": [],
            "annotations": [],
            "dependencies": [],
            "isAbstract": false,
            "isStatic": false,
            "isFinal": false,
            "comment": "结构体注释"
          }
        ],
        "dependencies": [],
        "fileComment": "文件级注释",
        "errorMessage": null,
        "successful": true
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 所有必填字段不能为null（除非显式允许）
      4. 列表类型字段至少返回空数组[]
      5. 布尔值必须是true/false，不能使用字符串
      6. 对于Go特有的概念（如接口、结构体方法等），适当映射到最接近的结构
      7. 对于Go中的可导出性（首字母大写），映射为PUBLIC访问修饰符，否则为PRIVATE
      8. 对于接口定义，使用type=INTERFACE

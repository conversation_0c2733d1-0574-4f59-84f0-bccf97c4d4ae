## 核心能力

- 精准将自然语言需求转换为高质量、可维护的生产级代码
- 智能处理技术细节与依赖关系，保持用户创意流不中断
- 主动提出架构和性能优化建议，同时严格尊重用户设计意图
- 自主诊断并解决编译错误、运行时异常和逻辑缺陷
- 通过持续集成测试确保代码质量和功能完整性
- 自动生成符合行业最佳实践的文档和注释

## 工作模式

- 自然语言驱动的编程体验，最小化技术障碍
- 深度理解需求文档、设计规范并生成功能完整的实现代码
- 智能抽象处理底层技术细节，让用户专注于创意和业务逻辑
- 在保留核心设计意图的前提下提供性能和安全性改进建议
- 自动生成单元测试、集成测试和性能测试用例
- 维护开发上下文，实现连贯的多轮对话开发体验

## 工作流程

- 提供清晰解释每个实现步骤的目的、原理和最佳实践考量
- 支持简洁指令交互，最小化用户输入需求
- 实现智能错误预测、诊断和修复流程
- 在复杂开发会话中保持上下文连贯性和一致性
- 根据任务性质无缝切换专业模式（架构设计、代码实现、测试验证、性能优化）
- 提供实时代码质量反馈和优化建议
- 遵循"前端先行、后端支撑"的全栈开发模式
- 采用"整体框架先行、细节逐步完善"的开发策略
- 严格践行面向接口编程原则，确保组件解耦
- 贯彻测试驱动开发理念，始终遵循测试先行的原则，确保代码质量
- 开始每个编码任务前使用`git worktree add ../repo-name-task1 main && cd ../repo-name-task1`创建独立的工作空间, 完成每个编码任务后使用git工具提交代码.

## 开发最佳实践

- 在实现新功能前自动创建测试规范和测试用例
- 通过自动化测试验证所有代码更改
- 持续监控并提高测试覆盖率
- 前瞻性识别潜在架构和性能问题
- 确保新功能与现有系统的兼容性和一致性
- 持续对照设计文档验证代码实现的一致性

## 交互指令模板

- 初始化: 'I want to create {项目描述}'
- 需求细化: 'Can you modify this to {变更需求}'
- 错误处理: 'Fix this error: {错误信息}'
- 迭代优化: 'Let's improve {待优化方面}'
- 测试生成: 'Generate tests for {功能模块}'
- 验证确认: 'Verify the changes to {组件}'

始终优先提供可工作的解决方案，将错误视为改进机会，保持鼓励性交流风格，在不打断思路前提下适时提出优化建议，记录关键技术决策和设计假设，专注理解用户意图而非拘泥技术细节，全力支持快速实验和迭代开发。metadata:
  name: "代码质量分析提示词"
  description: "用于全面分析代码质量，识别潜在问题和改进机会"
  version: "1.0.0"
  last_updated: "2025-05-04"
  author: "ArchScope团队"

prompts:
  dependency_analysis:
    description: "分析代码中的依赖关系，识别潜在的依赖问题"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件中的依赖关系，识别潜在问题和改进机会，并以严格的JSON格式返回。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "dependencyAnalysis": {
          "summary": "依赖关系总结",
          "dependencyCount": {
            "imports": 0,
            "directDependencies": 0,
            "indirectDependencies": 0
          },
          "dependencyMetrics": {
            "afferentCoupling": 0,
            "efferentCoupling": 0,
            "instability": 0.0,
            "abstractness": 0.0,
            "distance": 0.0
          }
        },
        "dependencyIssues": [
          {
            "type": "依赖问题类型",
            "description": "详细描述",
            "severity": "HIGH/MEDIUM/LOW",
            "location": "问题位置",
            "recommendation": "改进建议"
          }
        ],
        "cyclicDependencies": [
          {
            "cycle": ["类A", "类B", "类C", "类A"],
            "severity": "HIGH/MEDIUM/LOW",
            "impact": "影响",
            "recommendation": "解决建议"
          }
        ],
        "unusedDependencies": [
          {
            "dependency": "未使用的依赖",
            "type": "IMPORT/FIELD/PARAMETER",
            "location": "位置",
            "recommendation": "处理建议"
          }
        ],
        "dependencyOptimizations": [
          {
            "optimization": "优化建议",
            "benefit": "预期收益",
            "effort": "HIGH/MEDIUM/LOW",
            "priority": "HIGH/MEDIUM/LOW"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 依赖问题类型包括但不限于：
         - 过度依赖
         - 不稳定依赖
         - 循环依赖
         - 不必要的依赖
         - 缺失的抽象
      4. 依赖指标解释：
         - 传入耦合(afferentCoupling)：有多少其他类依赖于该类
         - 传出耦合(efferentCoupling)：该类依赖于多少其他类
         - 不稳定性(instability)：传出耦合/(传入耦合+传出耦合)
         - 抽象性(abstractness)：抽象类和接口的比例
         - 距离(distance)：|抽象性 + 不稳定性 - 1|

  layer_detection:
    description: "识别代码中的架构分层，评估分层质量"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，识别其在软件架构中的层次位置，评估分层质量，并以严格的JSON格式返回。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "layerIdentification": {
          "layer": "PRESENTATION/APPLICATION/DOMAIN/INFRASTRUCTURE/DATA_ACCESS/CROSS_CUTTING",
          "confidence": 0.0-1.0,
          "evidence": [
            "支持该层判断的证据列表"
          ]
        },
        "layerQuality": {
          "appropriateness": "APPROPRIATE/QUESTIONABLE/INAPPROPRIATE",
          "explanation": "解释",
          "responsibilityAlignment": "GOOD/AVERAGE/POOR",
          "boundaryClarity": "CLEAR/MODERATE/UNCLEAR"
        },
        "layerViolations": [
          {
            "type": "违反分层原则的类型",
            "description": "详细描述",
            "severity": "HIGH/MEDIUM/LOW",
            "impact": "影响",
            "recommendation": "修复建议"
          }
        ],
        "layerRefactoringSuggestions": [
          {
            "suggestion": "重构建议",
            "targetLayer": "目标层",
            "benefit": "预期收益",
            "effort": "HIGH/MEDIUM/LOW"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 层违规应重点关注：
         - 跨层依赖
         - 层旁路
         - 不当的责任分配
         - 层泄漏
      4. 置信度(confidence)使用0.0-1.0的浮点数表示，其中1.0表示完全确定

  design_pattern_detection:
    description: "识别代码中使用的设计模式，评估实现质量"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，识别其中使用的设计模式，评估实现质量，并以严格的JSON格式返回。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "detectedPatterns": [
          {
            "pattern": "设计模式名称",
            "category": "CREATIONAL/STRUCTURAL/BEHAVIORAL",
            "confidence": 0.0-1.0,
            "implementation": {
              "quality": "EXCELLENT/GOOD/AVERAGE/POOR",
              "completeness": "COMPLETE/PARTIAL/MINIMAL",
              "participants": [
                {
                  "role": "模式中的角色",
                  "class": "实现该角色的类",
                  "quality": "GOOD/AVERAGE/POOR"
                }
              ]
            },
            "appropriateness": {
              "assessment": "APPROPRIATE/QUESTIONABLE/INAPPROPRIATE",
              "explanation": "解释",
              "alternatives": [
                "替代模式"
              ]
            }
          }
        ],
        "missingPatterns": [
          {
            "pattern": "可能适用但未使用的模式",
            "context": "适用场景",
            "benefit": "使用该模式的潜在好处",
            "implementationHint": "如何实现的提示"
          }
        ],
        "antiPatterns": [
          {
            "pattern": "反模式名称",
            "description": "详细描述",
            "severity": "HIGH/MEDIUM/LOW",
            "location": "位置",
            "refactoringRecommendation": "重构建议"
          }
        ],
        "patternQualityIssues": [
          {
            "pattern": "相关模式",
            "issue": "质量问题",
            "impact": "影响",
            "recommendation": "改进建议"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 设计模式判断应基于公认的GoF设计模式和常见的架构模式
      4. 置信度(confidence)使用0.0-1.0的浮点数表示，其中1.0表示完全确定
      5. 反模式包括但不限于：
         - 上帝对象
         - 面条代码
         - 黄金锤子
         - 复制粘贴编程
         - 硬编码
         - 魔法数字/字符串

  quality_assessment:
    description: "全面评估代码质量，识别潜在问题和改进机会"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      全面分析以下代码文件的质量，识别潜在问题和改进机会，并以严格的JSON格式返回。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "overallQuality": {
          "score": 0-100,
          "grade": "A/B/C/D/F",
          "summary": "总体质量评价"
        },
        "metrics": {
          "complexity": {
            "cyclomaticComplexity": {
              "average": 0.0,
              "max": 0,
              "assessment": "LOW/MEDIUM/HIGH/VERY_HIGH"
            },
            "cognitiveComplexity": {
              "average": 0.0,
              "max": 0,
              "assessment": "LOW/MEDIUM/HIGH/VERY_HIGH"
            },
            "halsteadMetrics": {
              "volume": 0.0,
              "difficulty": 0.0,
              "effort": 0.0,
              "assessment": "LOW/MEDIUM/HIGH/VERY_HIGH"
            }
          },
          "size": {
            "linesOfCode": 0,
            "effectiveLinesOfCode": 0,
            "commentLines": 0,
            "commentRatio": 0.0,
            "assessment": "APPROPRIATE/TOO_LARGE/TOO_SMALL"
          },
          "maintainability": {
            "index": 0.0,
            "assessment": "HIGH/MEDIUM/LOW",
            "factors": [
              "影响可维护性的因素"
            ]
          }
        },
        "codeSmells": [
          {
            "type": "代码异味类型",
            "location": "位置描述",
            "severity": "HIGH/MEDIUM/LOW",
            "description": "详细描述",
            "impact": "影响",
            "refactoringRecommendation": "重构建议"
          }
        ],
        "bestPractices": {
          "followed": [
            "已遵循的最佳实践"
          ],
          "violated": [
            {
              "practice": "违反的最佳实践",
              "location": "位置",
              "severity": "HIGH/MEDIUM/LOW",
              "recommendation": "改进建议"
            }
          ]
        },
        "solidPrincipleViolations": [
          {
            "principle": "SRP/OCP/LSP/ISP/DIP",
            "location": "位置描述",
            "description": "违反原则的详细描述",
            "severity": "HIGH/MEDIUM/LOW",
            "suggestion": "修复建议"
          }
        ],
        "testability": {
          "assessment": "HIGH/MEDIUM/LOW",
          "issues": [
            {
              "issue": "可测试性问题",
              "location": "位置",
              "impact": "影响",
              "recommendation": "改进建议"
            }
          ]
        },
        "performanceIssues": [
          {
            "issue": "性能问题",
            "location": "位置",
            "severity": "HIGH/MEDIUM/LOW",
            "impact": "影响",
            "recommendation": "改进建议"
          }
        ],
        "improvementPriorities": [
          {
            "area": "改进领域",
            "description": "详细描述",
            "priority": "HIGH/MEDIUM/LOW",
            "effort": "HIGH/MEDIUM/LOW",
            "benefit": "预期收益"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 质量评分(score)使用0-100的整数表示，其中100表示最高质量
      4. 代码异味应包括但不限于：
         - 重复代码
         - 过长方法/类
         - 过多参数
         - 数据泥团
         - 特性依恋
         - 过度复杂的条件逻辑
         - 原始类型偏执
      5. SOLID原则违反评估应详细说明违反的具体方式和影响
      6. 性能问题应关注算法效率、资源使用和潜在的性能瓶颈

  security_analysis:
    description: "分析代码中的安全问题，识别潜在漏洞和安全风险"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件中的安全问题，识别潜在漏洞和安全风险，并以严格的JSON格式返回。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "securityAssessment": {
          "overallRisk": "CRITICAL/HIGH/MEDIUM/LOW/NONE",
          "summary": "安全风险总结",
          "confidenceLevel": "HIGH/MEDIUM/LOW"
        },
        "vulnerabilities": [
          {
            "type": "漏洞类型",
            "cweId": "CWE编号",
            "owasp": "OWASP Top 10类别",
            "location": "位置描述",
            "severity": "CRITICAL/HIGH/MEDIUM/LOW",
            "description": "详细描述",
            "impact": "潜在影响",
            "likelihood": "HIGH/MEDIUM/LOW",
            "remediation": {
              "recommendation": "修复建议",
              "effort": "HIGH/MEDIUM/LOW",
              "priority": "HIGH/MEDIUM/LOW"
            }
          }
        ],
        "securityAntiPatterns": [
          {
            "pattern": "安全反模式名称",
            "location": "位置",
            "risk": "风险",
            "description": "详细描述",
            "recommendation": "改进建议"
          }
        ],
        "securityBestPractices": {
          "followed": [
            "已遵循的安全最佳实践"
          ],
          "violated": [
            {
              "practice": "违反的最佳实践",
              "location": "位置",
              "severity": "HIGH/MEDIUM/LOW",
              "recommendation": "改进建议"
            }
          ]
        },
        "sensitiveDataExposure": [
          {
            "dataType": "敏感数据类型",
            "location": "位置",
            "exposure": "暴露方式",
            "severity": "HIGH/MEDIUM/LOW",
            "recommendation": "保护建议"
          }
        ],
        "securityTestingRecommendations": [
          {
            "testType": "测试类型",
            "focus": "测试重点",
            "description": "详细描述",
            "priority": "HIGH/MEDIUM/LOW"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 漏洞类型应参考OWASP Top 10和CWE分类
      4. 安全评估应考虑：
         - 输入验证和输出编码
         - 认证和授权
         - 会话管理
         - 加密实践
         - 错误处理和日志记录
         - 数据保护
         - 配置安全
      5. 对于Web应用，特别关注XSS、CSRF、SQL注入、命令注入等常见漏洞
      6. 对于移动应用，关注不安全的数据存储、不安全的通信等问题
      7. 对于后端服务，关注权限控制、加密、API安全等问题

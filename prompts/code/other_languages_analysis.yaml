metadata:
  name: "多语言代码分析提示词"
  description: "用于分析Ruby、PHP、Swift、Kotlin和Rust代码并生成符合FileParseResult格式的结构化输出"
  version: "1.0.0"
  last_updated: "2025-05-04"
  author: "ArchScope团队"

prompts:
  code_structure:
    description: "分析多种编程语言代码结构并生成标准化输出"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下{{language_type}}代码文件，提取其结构信息并以严格的JSON格式返回，确保完全符合FileParseResult模型规范。

      文件路径: {{file_path}}
      语言类型: {{language_type}} // RUBY, PHP, SWIFT, KOTLIN, 或 RUST

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language_type}}",
        "packageName": "包名/命名空间（如果有）",
        "imports": [
          "导入/引用语句列表"
        ],
        "classDefinitions": [
          {
            "name": "类/结构体名称",
            "fullyQualifiedName": "完整限定名",
            "packageName": "包名/命名空间（如果有）",
            "superClass": "父类名（如果有）",
            "interfaces": [
              "实现的接口/协议列表"
            ],
            "type": "CLASS/INTERFACE/TRAIT/STRUCT/ENUM",
            "accessModifier": "PUBLIC/PRIVATE/PROTECTED/INTERNAL/FILEPRIVATE",
            "fields": [
              {
                "name": "字段/属性名",
                "type": "字段/属性类型",
                "accessModifier": "访问修饰符",
                "isStatic": false,
                "isFinal": false,
                "annotations": [],
                "comment": "注释"
              }
            ],
            "methods": [
              {
                "name": "方法名",
                "returnType": "返回类型",
                "accessModifier": "访问修饰符",
                "parameters": [
                  {
                    "name": "参数名",
                    "type": "参数类型",
                    "annotations": []
                  }
                ],
                "annotations": [],
                "isStatic": false,
                "isFinal": false,
                "isAbstract": false,
                "comment": "方法注释"
              }
            ],
            "innerClasses": [],
            "annotations": [],
            "dependencies": [],
            "isAbstract": false,
            "isStatic": false,
            "isFinal": false,
            "comment": "类/结构体注释"
          }
        ],
        "dependencies": [],
        "fileComment": "文件级注释",
        "errorMessage": null,
        "successful": true
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 所有必填字段不能为null（除非显式允许）
      4. 列表类型字段至少返回空数组[]
      5. 布尔值必须是true/false，不能使用字符串
      6. 根据不同语言的特性，适当映射到最接近的结构：
         - Ruby: 类、模块、混入(mixin)
         - PHP: 类、接口、trait
         - Swift: 类、结构体、协议、扩展
         - Kotlin: 类、接口、数据类、对象声明
         - Rust: 结构体、特性(trait)、枚举、实现(impl)
      7. 对于没有显式类型的语言，尽可能从上下文推断类型
      8. 对于函数式编程特性，适当映射到面向对象概念

metadata:
  name: "Java代码分析提示词"
  description: "用于分析Java代码并生成符合FileParseResult格式的结构化输出"
  version: "1.1.0"
  last_updated: "2025-05-04"
  author: "ArchScope团队"

prompts:
  code_structure:
    description: "分析Java代码结构并生成标准化输出"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下Java代码文件，提取其结构信息并以严格的JSON格式返回，确保完全符合FileParseResult模型规范。

      文件路径: {{file_path}}
      
      ```java
      {{code}}
      ```
      
      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "JAVA",
        "packageName": "包名",
        "imports": [
          "完整的导入语句列表"
        ],
        "classDefinitions": [
          {
            "name": "类名",
            "fullyQualifiedName": "全限定类名",
            "packageName": "包名",
            "superClass": "父类名（如果有）",
            "interfaces": [
              "实现的接口列表"
            ],
            "type": "CLASS/INTERFACE/ENUM/ANNOTATION",
            "accessModifier": "PUBLIC/PRIVATE/PROTECTED/PACKAGE",
            "fields": [
              {
                "name": "字段名",
                "type": "字段类型",
                "accessModifier": "访问修饰符",
                "isStatic": false,
                "isFinal": false,
                "annotations": ["注解列表"],
                "comment": "字段注释"
              }
            ],
            "methods": [
              {
                "name": "方法名",
                "returnType": "返回类型",
                "accessModifier": "访问修饰符",
                "parameters": [
                  {
                    "name": "参数名",
                    "type": "参数类型",
                    "annotations": ["参数注解"]
                  }
                ],
                "annotations": ["方法注解"],
                "isStatic": false,
                "isFinal": false,
                "isAbstract": false,
                "comment": "方法注释"
              }
            ],
            "innerClasses": [],
            "annotations": ["类级注解列表"],
            "dependencies": [
              {
                "sourceClass": "当前类名",
                "targetClass": "依赖的类名",
                "type": "INHERITANCE/IMPLEMENTATION/COMPOSITION/AGGREGATION/DEPENDENCY",
                "strength": 1,
                "location": "依赖位置描述"
              }
            ],
            "isAbstract": false,
            "isStatic": false,
            "isFinal": false,
            "comment": "类注释"
          }
        ],
        "dependencies": [
          {
            "sourceClass": "源类名",
            "targetClass": "目标类名",
            "type": "依赖类型",
            "strength": 1,
            "location": "依赖位置"
          }
        ],
        "fileComment": "文件级注释",
        "errorMessage": null,
        "successful": true
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 所有必填字段不能为null（除非显式允许）
      4. 列表类型字段至少返回空数组[]
      5. 布尔值必须是true/false，不能使用字符串
      6. 枚举值必须使用规定的常量值

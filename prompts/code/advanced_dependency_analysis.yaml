metadata:
  name: "高级依赖关系分析提示词"
  description: "用于深入分析代码库中的类/模块间依赖关系，识别依赖模式和潜在问题"
  version: "1.0.0"
  last_updated: "2025-05-04"
  author: "ArchScope团队"

prompts:
  dependency_analysis:
    description: "分析代码中的类/模块间依赖关系，识别依赖类型和强度"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，提取其依赖关系信息并以严格的JSON格式返回。重点关注类/模块间的依赖关系、依赖类型和依赖强度。

      文件路径: {{file_path}}
      
      ```{{language}}
      {{code}}
      ```
      
      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "dependencies": [
          {
            "sourceClass": "源类/模块名称",
            "targetClass": "目标类/模块名称",
            "type": "INHERITANCE/IMPLEMENTATION/COMPOSITION/AGGREGATION/DEPENDENCY/IMPORT",
            "strength": 1-10,
            "location": "依赖位置描述",
            "description": "依赖关系的详细描述"
          }
        ],
        "dependencyGraph": {
          "nodes": [
            {
              "id": "类/模块名称",
              "type": "CLASS/INTERFACE/MODULE/ENUM",
              "inDegree": 0,
              "outDegree": 0
            }
          ],
          "edges": [
            {
              "source": "源类/模块ID",
              "target": "目标类/模块ID",
              "type": "依赖类型",
              "weight": 1-10
            }
          ]
        },
        "cyclicDependencies": [
          {
            "cycle": ["类A", "类B", "类C", "类A"],
            "severity": "HIGH/MEDIUM/LOW",
            "suggestion": "解决循环依赖的建议"
          }
        ],
        "dependencyMetrics": {
          "afferentCoupling": 0,
          "efferentCoupling": 0,
          "instability": 0.0,
          "abstractness": 0.0,
          "distance": 0.0
        }
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 依赖强度(strength)使用1-10的数值表示，其中10表示最强依赖
      4. 依赖类型必须使用规定的常量值
      5. 循环依赖的严重程度根据循环大小和依赖强度评估
      6. 依赖指标解释：
         - 传入耦合(afferentCoupling)：有多少其他类依赖于该类
         - 传出耦合(efferentCoupling)：该类依赖于多少其他类
         - 不稳定性(instability)：传出耦合/(传入耦合+传出耦合)
         - 抽象性(abstractness)：抽象类和接口的比例
         - 距离(distance)：|抽象性 + 不稳定性 - 1|

  layer_detection:
    description: "识别代码中的架构分层，确定类/模块所属的架构层"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，识别其在软件架构中的层次位置，并以严格的JSON格式返回。重点关注类/模块的命名、包结构、导入关系和功能职责。

      文件路径: {{file_path}}
      
      ```{{language}}
      {{code}}
      ```
      
      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "architecturalLayer": {
          "primaryLayer": "PRESENTATION/APPLICATION/DOMAIN/INFRASTRUCTURE/DATA_ACCESS/CROSS_CUTTING",
          "confidence": 0.0-1.0,
          "evidence": [
            "支持该层判断的证据列表"
          ],
          "alternativeLayers": [
            {
              "layer": "可能的替代层",
              "confidence": 0.0-1.0
            }
          ]
        },
        "layerViolations": [
          {
            "type": "违反分层原则的类型",
            "description": "详细描述",
            "severity": "HIGH/MEDIUM/LOW",
            "suggestion": "修复建议"
          }
        ],
        "layerDependencies": [
          {
            "dependsOn": "依赖的层",
            "type": "依赖类型",
            "isAllowed": true/false,
            "violationReason": "如果不允许，说明原因"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 架构层的判断应基于以下因素：
         - 包名和命名约定（如包含controller、service、repository等关键词）
         - 类的职责和功能
         - 导入的依赖和被依赖关系
         - 类的注解和元数据
      4. 置信度(confidence)使用0.0-1.0的浮点数表示，其中1.0表示完全确定
      5. 层违规应重点关注跨层依赖，特别是违反依赖倒置原则的情况

  design_pattern_detection:
    description: "识别代码中使用的设计模式，分析其实现质量"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，识别其中使用的设计模式，并以严格的JSON格式返回。重点关注类的结构、关系和行为特征。

      文件路径: {{file_path}}
      
      ```{{language}}
      {{code}}
      ```
      
      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "detectedPatterns": [
          {
            "pattern": "设计模式名称",
            "category": "CREATIONAL/STRUCTURAL/BEHAVIORAL",
            "confidence": 0.0-1.0,
            "implementation": {
              "quality": "EXCELLENT/GOOD/AVERAGE/POOR",
              "participants": [
                {
                  "role": "模式中的角色",
                  "class": "实现该角色的类",
                  "responsibility": "职责描述"
                }
              ],
              "strengths": [
                "实现的优点列表"
              ],
              "weaknesses": [
                "实现的缺点列表"
              ],
              "improvementSuggestions": [
                "改进建议列表"
              ]
            },
            "evidence": [
              "支持该模式判断的证据列表"
            ]
          }
        ],
        "patternRelationships": [
          {
            "patterns": ["模式A", "模式B"],
            "relationship": "COMPLEMENTARY/CONFLICTING/COMPOSITE",
            "description": "关系描述"
          }
        ],
        "missingPatterns": [
          {
            "pattern": "可能适用但未使用的模式",
            "benefit": "使用该模式的潜在好处",
            "implementationHint": "如何实现的提示"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 设计模式判断应基于公认的GoF设计模式和常见的架构模式
      4. 置信度(confidence)使用0.0-1.0的浮点数表示，其中1.0表示完全确定
      5. 实现质量评估应考虑：
         - 模式实现的完整性
         - 代码的清晰度和可维护性
         - 是否符合SOLID原则
         - 是否存在过度设计

  quality_assessment:
    description: "评估代码质量，识别潜在问题和改进机会"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，评估其质量，识别潜在问题和改进机会，并以严格的JSON格式返回。

      文件路径: {{file_path}}
      
      ```{{language}}
      {{code}}
      ```
      
      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "overallQuality": {
          "score": 0-100,
          "summary": "总体质量评价",
          "strengths": [
            "代码优点列表"
          ],
          "weaknesses": [
            "代码缺点列表"
          ]
        },
        "metrics": {
          "complexity": {
            "cyclomaticComplexity": 0,
            "cognitiveComplexity": 0,
            "assessment": "LOW/MEDIUM/HIGH/VERY_HIGH"
          },
          "size": {
            "linesOfCode": 0,
            "effectiveLinesOfCode": 0,
            "assessment": "APPROPRIATE/TOO_LARGE/TOO_SMALL"
          },
          "cohesion": {
            "score": 0.0-1.0,
            "assessment": "STRONG/MODERATE/WEAK"
          },
          "coupling": {
            "score": 0.0-1.0,
            "assessment": "LOW/MEDIUM/HIGH"
          }
        },
        "codeSmells": [
          {
            "type": "代码异味类型",
            "location": "位置描述",
            "severity": "HIGH/MEDIUM/LOW",
            "description": "详细描述",
            "refactoringRecommendation": "重构建议"
          }
        ],
        "solidPrincipleViolations": [
          {
            "principle": "SRP/OCP/LSP/ISP/DIP",
            "location": "位置描述",
            "description": "违反原则的详细描述",
            "severity": "HIGH/MEDIUM/LOW",
            "suggestion": "修复建议"
          }
        ],
        "improvementSuggestions": [
          {
            "category": "改进类别",
            "description": "详细描述",
            "priority": "HIGH/MEDIUM/LOW",
            "effort": "HIGH/MEDIUM/LOW",
            "benefit": "预期收益"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 质量评分(score)使用0-100的整数表示，其中100表示最高质量
      4. 代码异味应包括但不限于：
         - 重复代码
         - 过长方法/类
         - 过多参数
         - 数据泥团
         - 特性依恋
         - 过度复杂的条件逻辑
         - 原始类型偏执
      5. SOLID原则违反评估应详细说明违反的具体方式和影响

  security_analysis:
    description: "分析代码中的安全问题，识别潜在漏洞"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，识别潜在的安全漏洞和问题，并以严格的JSON格式返回。

      文件路径: {{file_path}}
      
      ```{{language}}
      {{code}}
      ```
      
      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "securityAssessment": {
          "overallRisk": "CRITICAL/HIGH/MEDIUM/LOW/NONE",
          "summary": "安全风险总结"
        },
        "vulnerabilities": [
          {
            "type": "漏洞类型",
            "cweId": "CWE编号",
            "location": "位置描述",
            "severity": "CRITICAL/HIGH/MEDIUM/LOW",
            "description": "详细描述",
            "impact": "潜在影响",
            "remediation": "修复建议",
            "exploitability": "EASY/MODERATE/DIFFICULT"
          }
        ],
        "securityBestPractices": {
          "followed": [
            "已遵循的安全最佳实践"
          ],
          "violated": [
            {
              "practice": "违反的最佳实践",
              "location": "位置描述",
              "severity": "HIGH/MEDIUM/LOW",
              "recommendation": "改进建议"
            }
          ]
        },
        "dataFlowIssues": [
          {
            "type": "数据流问题类型",
            "source": "数据来源",
            "sink": "数据去向",
            "path": ["数据流路径"],
            "severity": "HIGH/MEDIUM/LOW",
            "description": "详细描述",
            "mitigation": "缓解措施"
          }
        ],
        "securityHeaders": {
          "missing": [
            {
              "header": "缺失的安全头",
              "impact": "潜在影响",
              "recommendation": "实现建议"
            }
          ],
          "misconfigured": [
            {
              "header": "配置不当的安全头",
              "currentValue": "当前值",
              "recommendedValue": "推荐值",
              "impact": "潜在影响"
            }
          ]
        }
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 漏洞类型应参考OWASP Top 10和CWE分类
      4. 安全评估应考虑：
         - 输入验证和输出编码
         - 认证和授权
         - 会话管理
         - 加密实践
         - 错误处理和日志记录
         - 数据保护
         - 配置安全
      5. 对于Web应用，特别关注XSS、CSRF、SQL注入、命令注入等常见漏洞
      6. 对于移动应用，关注不安全的数据存储、不安全的通信等问题
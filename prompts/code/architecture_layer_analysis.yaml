metadata:
  name: "架构分层分析提示词"
  description: "用于分析代码库的架构分层，识别层次结构和层间关系"
  version: "1.0.0"
  last_updated: "2025-05-04"
  author: "ArchScope团队"

prompts:
  dependency_analysis:
    description: "分析架构层次间的依赖关系，识别层间通信模式"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，识别其在架构中的位置以及与其他层的依赖关系，并以严格的JSON格式返回。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "architecturalContext": {
          "layer": "PRESENTATION/APPLICATION/DOMAIN/INFRASTRUCTURE/DATA_ACCESS/CROSS_CUTTING",
          "component": "组件名称",
          "module": "模块名称",
          "responsibility": "职责描述"
        },
        "layerDependencies": [
          {
            "targetLayer": "依赖的层",
            "dependencyType": "DIRECT/INDIRECT/ABSTRACTED",
            "direction": "INWARD/OUTWARD",
            "strength": 1-10,
            "mechanism": "依赖注入/接口/直接引用/事件",
            "examples": [
              "具体依赖示例"
            ]
          }
        ],
        "layerViolations": [
          {
            "violationType": "违反类型",
            "description": "详细描述",
            "severity": "HIGH/MEDIUM/LOW",
            "impact": "影响",
            "recommendation": "修复建议"
          }
        ],
        "communicationPatterns": [
          {
            "pattern": "通信模式",
            "description": "详细描述",
            "participants": [
              "参与者"
            ],
            "quality": "GOOD/AVERAGE/POOR",
            "improvementSuggestions": [
              "改进建议"
            ]
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 架构层判断应基于包名、类名、注解和依赖关系
      4. 层违规应重点关注跨层依赖，特别是违反依赖倒置原则的情况
      5. 通信模式包括但不限于：
         - 同步调用
         - 异步消息
         - 事件驱动
         - 发布-订阅
         - 命令查询责任分离(CQRS)

  layer_detection:
    description: "识别代码库中的架构分层，确定各组件所属的层次"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，识别其在软件架构中的层次位置，并以严格的JSON格式返回。重点关注类/模块的命名、包结构、导入关系和功能职责。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "layerClassification": {
          "primaryLayer": "PRESENTATION/APPLICATION/DOMAIN/INFRASTRUCTURE/DATA_ACCESS/CROSS_CUTTING",
          "confidence": 0.0-1.0,
          "evidence": [
            "支持该层判断的证据列表"
          ],
          "subLayer": "子层名称",
          "alternativeLayers": [
            {
              "layer": "可能的替代层",
              "confidence": 0.0-1.0
            }
          ]
        },
        "layerCharacteristics": {
          "responsibility": "层的主要职责",
          "abstraction": "HIGH/MEDIUM/LOW",
          "stability": "HIGH/MEDIUM/LOW",
          "reusability": "HIGH/MEDIUM/LOW",
          "cohesion": "HIGH/MEDIUM/LOW"
        },
        "layerBoundaries": {
          "wellDefined": true/false,
          "issues": [
            {
              "type": "边界问题类型",
              "description": "详细描述",
              "severity": "HIGH/MEDIUM/LOW",
              "recommendation": "改进建议"
            }
          ]
        },
        "architecturalPatterns": [
          {
            "pattern": "架构模式名称",
            "confidence": 0.0-1.0,
            "evidence": [
              "支持该模式判断的证据"
            ],
            "role": "该文件在模式中的角色"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 架构层的判断应基于以下因素：
         - 包名和命名约定（如包含controller、service、repository等关键词）
         - 类的职责和功能
         - 导入的依赖和被依赖关系
         - 类的注解和元数据
      4. 置信度(confidence)使用0.0-1.0的浮点数表示，其中1.0表示完全确定
      5. 架构模式包括但不限于：
         - MVC/MVP/MVVM
         - 分层架构
         - 六边形架构/端口与适配器
         - 洋葱架构
         - 微服务架构
         - 事件驱动架构

  design_pattern_detection:
    description: "识别架构中使用的设计模式，分析其在分层架构中的应用"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，识别其中使用的架构级设计模式，并以严格的JSON格式返回。重点关注类的结构、关系和在架构中的位置。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "architecturalPatterns": [
          {
            "pattern": "架构模式名称",
            "category": "STRUCTURAL/BEHAVIORAL/INTEGRATION",
            "confidence": 0.0-1.0,
            "implementation": {
              "quality": "EXCELLENT/GOOD/AVERAGE/POOR",
              "participants": [
                {
                  "role": "模式中的角色",
                  "class": "实现该角色的类",
                  "layer": "所属架构层",
                  "responsibility": "职责描述"
                }
              ],
              "crossLayerInteractions": [
                {
                  "source": "源层",
                  "target": "目标层",
                  "mechanism": "交互机制",
                  "quality": "GOOD/AVERAGE/POOR"
                }
              ]
            },
            "layerAppropriate": true/false,
            "layerAppropriateness": {
              "assessment": "APPROPRIATE/QUESTIONABLE/INAPPROPRIATE",
              "explanation": "解释",
              "alternativeApproach": "替代方案"
            }
          }
        ],
        "crossCuttingConcerns": [
          {
            "concern": "横切关注点名称",
            "implementation": "实现方式",
            "affectedLayers": [
              "受影响的层"
            ],
            "quality": "GOOD/AVERAGE/POOR",
            "improvementSuggestions": [
              "改进建议"
            ]
          }
        ],
        "architecturalSmells": [
          {
            "smell": "架构异味名称",
            "description": "详细描述",
            "affectedLayers": [
              "受影响的层"
            ],
            "severity": "HIGH/MEDIUM/LOW",
            "refactoringRecommendation": "重构建议"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 架构模式判断应基于公认的架构模式和集成模式
      4. 置信度(confidence)使用0.0-1.0的浮点数表示，其中1.0表示完全确定
      5. 架构异味包括但不限于：
         - 层旁路
         - 层内循环依赖
         - 不必要的层
         - 层泄漏
         - 不平衡的责任分配

  quality_assessment:
    description: "评估架构分层的质量，识别潜在问题和改进机会"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，评估其在架构分层中的质量，识别潜在问题和改进机会，并以严格的JSON格式返回。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "layerQualityAssessment": {
          "layer": "所属架构层",
          "overallQuality": {
            "score": 0-100,
            "assessment": "EXCELLENT/GOOD/AVERAGE/POOR",
            "summary": "总体质量评价"
          },
          "metrics": {
            "cohesion": {
              "score": 0.0-1.0,
              "assessment": "HIGH/MEDIUM/LOW",
              "issues": [
                "内聚性问题"
              ]
            },
            "coupling": {
              "score": 0.0-1.0,
              "assessment": "LOW/MEDIUM/HIGH",
              "issues": [
                "耦合性问题"
              ]
            },
            "abstraction": {
              "score": 0.0-1.0,
              "assessment": "HIGH/MEDIUM/LOW",
              "issues": [
                "抽象性问题"
              ]
            },
            "layerIsolation": {
              "score": 0.0-1.0,
              "assessment": "STRONG/MODERATE/WEAK",
              "issues": [
                "层隔离问题"
              ]
            }
          }
        },
        "architecturalSmells": [
          {
            "type": "架构异味类型",
            "description": "详细描述",
            "severity": "HIGH/MEDIUM/LOW",
            "impact": "影响",
            "refactoringRecommendation": "重构建议"
          }
        ],
        "layerResponsibilityIssues": [
          {
            "issue": "责任分配问题",
            "description": "详细描述",
            "severity": "HIGH/MEDIUM/LOW",
            "correctLayer": "正确的层",
            "recommendation": "修复建议"
          }
        ],
        "improvementSuggestions": [
          {
            "category": "改进类别",
            "description": "详细描述",
            "priority": "HIGH/MEDIUM/LOW",
            "effort": "HIGH/MEDIUM/LOW",
            "benefit": "预期收益"
          }
        ]
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 质量评分(score)使用0-100的整数表示，其中100表示最高质量
      4. 架构异味应包括但不限于：
         - 层旁路
         - 循环依赖
         - 不必要的中间层
         - 层泄漏
         - 不平衡的责任分配
      5. 责任分配问题应关注类/模块的职责是否与其所在层次匹配

  security_analysis:
    description: "分析架构分层中的安全问题，识别层间安全机制"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下代码文件，识别其在架构分层中的安全机制和潜在问题，并以严格的JSON格式返回。

      文件路径: {{file_path}}

      ```{{language}}
      {{code}}
      ```

      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "{{language}}",
        "layerSecurityAssessment": {
          "layer": "所属架构层",
          "securityResponsibility": "该层的安全职责",
          "securityMechanisms": [
            {
              "mechanism": "安全机制名称",
              "implementation": "实现方式",
              "quality": "GOOD/AVERAGE/POOR",
              "coverage": "COMPLETE/PARTIAL/MINIMAL"
            }
          ],
          "missingSecurityControls": [
            {
              "control": "缺失的安全控制",
              "importance": "HIGH/MEDIUM/LOW",
              "recommendation": "实现建议"
            }
          ]
        },
        "crossLayerSecurityIssues": [
          {
            "issue": "跨层安全问题",
            "affectedLayers": [
              "受影响的层"
            ],
            "severity": "HIGH/MEDIUM/LOW",
            "description": "详细描述",
            "recommendation": "修复建议"
          }
        ],
        "trustBoundaries": [
          {
            "boundary": "信任边界名称",
            "layers": [
              "涉及的层"
            ],
            "securityControls": [
              "安全控制"
            ],
            "weaknesses": [
              "弱点"
            ]
          }
        ],
        "dataFlowSecurity": {
          "dataFlows": [
            {
              "source": "源层",
              "destination": "目标层",
              "dataType": "数据类型",
              "sensitivity": "HIGH/MEDIUM/LOW",
              "protectionMechanisms": [
                "保护机制"
              ],
              "vulnerabilities": [
                "漏洞"
              ]
            }
          ],
          "overallAssessment": "SECURE/PARTIALLY_SECURE/INSECURE",
          "recommendations": [
            "改进建议"
          ]
        }
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 安全机制应包括但不限于：
         - 输入验证
         - 认证和授权
         - 会话管理
         - 加密
         - 错误处理
         - 日志记录
         - 访问控制
      4. 信任边界是系统中不同信任级别之间的分界线
      5. 数据流安全关注数据在不同层之间传递时的保护措施

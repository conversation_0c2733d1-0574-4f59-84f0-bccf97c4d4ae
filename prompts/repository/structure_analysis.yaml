metadata:
  name: "项目结构分析提示词"
  description: "用于分析项目整体结构和架构的提示词"
  version: "1.0.0"
  last_updated: "2023-11-01"
  author: "ArchScope团队"

prompts:
  architecture_overview:
    description: "生成项目架构概览"
    model: "gpt-4"
    parameters:
      temperature: 0.2
      max_tokens: 2500
    template: |
      分析以下项目文件结构，提供项目的整体架构评估。
      
      项目名称: {{project_name}}
      主要文件列表:
      {{file_list}}
      
      请提供以下信息:
      1. 架构概览: 系统采用的架构风格(如前后端分离、微服务等)
      2. 系统分层: 项目的层次结构和各层职责
      3. 关键技术栈: 前端、后端、数据库等使用的主要技术
      4. 部署视图: 系统部署架构的推断
      5. 数据架构: 主要数据实体及其关系
      6. 核心服务和组件: 系统的主要服务和组件及其职责
      
      请以Markdown格式返回，使用标题、列表和表格等元素组织内容，使其易于阅读和理解。
      参考界面原型中"架构概览"页面的内容结构进行组织。

  project_overview:
    description: "生成项目概览"
    model: "gpt-4"
    parameters:
      temperature: 0.3
      max_tokens: 2000
    template: |
      基于以下项目信息，生成一份项目概览文档。
      
      项目名称: {{project_name}}
      项目描述: {{project_description}}
      主要文件列表:
      {{file_list}}
      
      请提供以下信息:
      1. 项目简介: 项目的主要功能和目标
      2. 目标用户: 项目的目标用户群体
      3. 核心功能: 项目的主要功能模块
      4. 技术特点: 项目的技术亮点和创新点
      5. 未来规划: 项目的发展方向和计划
      
      请以Markdown格式返回，使用标题、列表和表格等元素组织内容，使其易于阅读和理解。
      参考界面原型中"产品简介"页面的内容结构进行组织。

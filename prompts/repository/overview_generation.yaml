metadata:
  name: "项目概览生成提示词"
  description: "用于生成项目概览的提示词"
  version: "1.0.0"
  last_updated: "2023-11-01"
  author: "ArchScope团队"

prompts:
  project_overview:
    description: "生成项目概览"
    model: "gpt-4"
    parameters:
      temperature: 0.3
      max_tokens: 2500
    template: |
      基于以下项目信息，生成一份项目概览文档。
      
      项目名称: {{project_name}}
      项目描述: {{project_description}}
      主要文件列表:
      {{file_list}}
      
      请生成一份详细的项目概览，包括以下部分:
      1. 项目简介: 项目的目标、背景和主要功能
      2. 技术栈: 项目使用的主要技术和框架
      3. 项目结构: 项目的目录结构和主要组件
      4. 核心功能: 项目的核心功能和特性
      5. 部署要求: 项目的部署和运行要求
      6. 开发指南: 如何参与项目开发
      
      请以Markdown格式返回，使用标题、列表、表格等元素组织内容，使其易于阅读和理解。

  project_metrics:
    description: "生成项目指标"
    model: "gpt-4"
    parameters:
      temperature: 0.2
      max_tokens: 2000
    template: |
      基于以下项目信息，生成一份项目指标报告。
      
      项目名称: {{project_name}}
      代码统计:
      {{code_stats}}
      
      请生成一份项目指标报告，包括以下部分:
      1. 代码规模: 代码行数、文件数、类/函数数等
      2. 代码分布: 不同语言和文件类型的分布
      3. 复杂度指标: 平均函数复杂度、最复杂的文件/函数等
      4. 质量指标: 注释率、测试覆盖率等
      5. 趋势分析: 项目规模和复杂度的变化趋势
      
      请以Markdown格式返回，使用图表、表格等元素直观地展示项目指标。

# ArchScope 项目文档

![ArchScope Logo](../../docs/prototype/images/logo.png)

欢迎使用 ArchScope（架构鹰眼）项目文档！本文档集合为您提供了使用、开发和扩展 ArchScope 系统所需的全部信息。

## 📚 文档导航

### 🚀 用户文档

#### [用户手册](user_manual.md)
完整的用户使用指南，包括：
- 系统概述和快速开始
- 项目注册和管理
- 文档查看和版本管理
- 任务监控
- 管理员功能
- 常见问题解答

**适用人群**: 所有 ArchScope 用户，包括开发者、架构师、项目管理者

### 🔗 开发者文档

#### [API 文档](api_documentation.md)
详细的 API 接口文档，包括：
- API 概述和认证方式
- 请求/响应格式规范
- 完整的端点列表和示例
- 错误码说明
- 使用示例和最佳实践

**适用人群**: 前端开发者、API 集成开发者、第三方系统集成

#### [扩展能力指南](extension_guide.md)
系统扩展开发指南，包括：
- 扩展机制概述
- 自定义解析器开发
- 插件开发和注册
- 扩展点说明
- 开发最佳实践
- 部署和发布流程

**适用人群**: 后端开发者、插件开发者、系统扩展需求方

### 🏗️ 架构文档

#### [技术架构文档](../../docs/architecture.md)
系统架构设计文档，包括：
- 整体架构设计
- DDD 六边形架构
- 模块划分和职责
- 技术选型说明
- 部署架构

**适用人群**: 架构师、技术负责人、运维工程师

#### [DDD 开发规范](../../README-DDD.md)
领域驱动设计开发规范，包括：
- DDD 架构层次说明
- 模块依赖关系
- 开发规范和约定
- 代码组织结构

**适用人群**: 后端开发者、架构师

### 📋 产品文档

#### [产品需求文档](.taskmaster/docs/prd.md)
详细的产品需求说明，包括：
- 产品概述和核心功能
- 用户故事和使用场景
- 技术要求和约束
- MVP 范围定义

**适用人群**: 产品经理、项目经理、开发团队

#### [技术实施方案](../../docs/ArchScope项目技术实施方案.md)
技术实施的详细方案，包括：
- 技术栈选型
- 实施计划和里程碑
- 风险评估和应对策略
- 团队分工和协作

**适用人群**: 技术负责人、项目经理

## 🎯 快速导航

### 我是新用户，想了解如何使用系统
👉 请阅读 [用户手册](user_manual.md)

### 我是开发者，想集成 ArchScope API
👉 请阅读 [API 文档](api_documentation.md)

### 我想扩展系统功能或开发插件
👉 请阅读 [扩展能力指南](extension_guide.md)

### 我想了解系统架构和技术实现
👉 请阅读 [技术架构文档](../../docs/architecture.md)

### 我是新加入的开发者
👉 请阅读 [DDD 开发规范](../../README-DDD.md) 和 [技术架构文档](../../docs/architecture.md)

## 📖 文档使用说明

### 文档结构
- **概述部分**: 提供功能概览和快速入门
- **详细说明**: 深入的功能说明和操作步骤
- **示例代码**: 实际的代码示例和最佳实践
- **参考信息**: API 规范、配置选项、错误码等

### 文档约定
- 🔒 表示需要管理员权限的功能
- 💡 表示重要提示或最佳实践
- ⚠️ 表示注意事项或潜在问题
- 📝 表示示例代码或配置

### 获取帮助
如果您在使用文档过程中遇到问题：

1. **查看常见问题**: 每个文档都包含常见问题解答部分
2. **搜索文档**: 使用浏览器搜索功能查找相关内容
3. **联系支持**: 通过邮箱 <EMAIL> 联系技术支持
4. **提交反馈**: 在 GitHub 仓库提交 Issue 或建议

## 🔄 文档更新

### 版本信息
- **当前版本**: v1.0.0
- **最后更新**: 2024-06-26
- **更新频率**: 随系统版本同步更新

### 更新日志
- **v1.0.0** (2024-06-26): 初始版本发布
  - 用户手册
  - API 文档
  - 扩展能力指南

### 贡献指南
欢迎为文档贡献内容：
1. Fork 项目仓库
2. 创建文档分支
3. 提交改进内容
4. 发起 Pull Request

## 📞 联系我们

- **技术支持**: <EMAIL>
- **项目仓库**: [GitHub](https://github.com/im47cn/arch-scope)
- **问题反馈**: [GitHub Issues](https://github.com/im47cn/arch-scope/issues)

---

*感谢使用 ArchScope！我们致力于为您提供最好的架构观测和治理体验。*

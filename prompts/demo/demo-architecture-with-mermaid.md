# ArchScope 系统架构文档

这是ArchScope系统的架构设计文档，包含了系统的整体架构和各个模块的关系。

## 系统整体架构

ArchScope采用DDD六边形架构设计，确保系统的可维护性和可扩展性。

```mermaid
graph TD
    A[用户界面] --> B[应用服务层]
    B --> C[领域层]
    C --> D[基础设施层]
    D --> E[数据库]
    
    F[外部LLM服务] --> B
    G[Git仓库] --> D
    H[文件存储] --> D
```

## 文档处理流程

以下是ArchScope中markdown文档处理的完整流程：

```mermaid
sequenceDiagram
    participant LLM as LLM服务
    participant API as 任务API
    participant Service as 文档服务
    participant Storage as 文件存储
    participant DB as 数据库
    participant Frontend as 前端

    LLM->>API: 提交任务结果
    API->>Service: 解析文档内容
    Service->>Storage: 存储markdown文件
    Service->>DB: 创建版本记录
    Frontend->>API: 请求文档内容
    API->>Storage: 读取markdown
    API->>Service: 渲染HTML
    Service-->>Frontend: 返回渲染结果
```

## 模块依赖关系

```mermaid
classDiagram
    class DocumentController {
        +getDocumentTypes()
        +getDocumentContent()
        +getDocumentHtml()
    }
    
    class DocumentStorageService {
        +storeDocument()
        +getDocumentContent()
        +documentExists()
    }
    
    class MarkdownService {
        +convertToHtml()
        +containsMermaidDiagram()
    }
    
    DocumentController --> DocumentStorageService
    DocumentController --> MarkdownService
```

## 任务状态流转

```mermaid
stateDiagram-v2
    [*] --> PENDING: 创建任务
    PENDING --> PROCESSING: 开始处理
    PROCESSING --> COMPLETED: 处理成功
    PROCESSING --> FAILED: 处理失败
    COMPLETED --> [*]
    FAILED --> PENDING: 重试
    FAILED --> [*]: 放弃
```

## 部署架构

```mermaid
graph LR
    subgraph "前端层"
        A[Vue3应用]
        B[Nginx]
    end
    
    subgraph "应用层"
        C[Spring Boot应用]
        D[文档存储]
    end
    
    subgraph "数据层"
        E[MySQL数据库]
        F[Redis缓存]
    end
    
    subgraph "外部服务"
        G[LLM服务]
        H[Git仓库]
    end
    
    A --> B
    B --> C
    C --> D
    C --> E
    C --> F
    G --> C
    H --> C
```

## 技术栈概览

| 层次 | 技术选型 | 说明 |
|------|----------|------|
| 前端 | Vue3 + Vite | 现代化前端框架 |
| 后端 | Spring Boot | Java企业级框架 |
| 数据库 | MySQL | 关系型数据库 |
| 缓存 | Redis | 内存数据库 |
| 文档渲染 | Flexmark + Mermaid | Markdown渲染引擎 |

## 核心特性

- **📚 自动文档生成**: 基于LLM的智能文档生成
- **🔄 版本管理**: 支持多版本文档存储和比较
- **📊 图表支持**: 支持Mermaid图表渲染
- **🎨 主题定制**: 支持多种文档主题
- **🔍 全文搜索**: 支持文档内容搜索
- **🔐 权限控制**: 支持项目访问权限管理

## 性能指标

- **响应时间**: API响应时间 < 200ms
- **并发处理**: 支持1000+并发用户
- **文档大小**: 支持最大10MB的markdown文档
- **存储容量**: 支持TB级文档存储

---

*本文档展示了ArchScope系统的完整架构设计，包含多种Mermaid图表类型的渲染效果。*

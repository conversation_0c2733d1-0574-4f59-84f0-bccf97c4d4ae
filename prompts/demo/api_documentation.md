# ArchScope API 文档

## 📋 API 概述

ArchScope 提供 RESTful API 接口，支持项目管理、任务监控、文档生成等核心功能。API 遵循统一的响应格式，支持 JSON 数据交换。

### 基础信息

- **Base URL**: `/api/v1`
- **数据格式**: `application/json` (UTF-8)
- **API版本**: v1.0.0

## 🔐 访问方式

### 开放访问
- 所有API接口均采用开放访问模式
- 无需任何认证或授权即可访问所有功能
- 包括项目管理、任务操作、文档访问等所有接口

## 📝 请求格式

### 统一请求头
```http
Content-Type: application/json
Accept: application/json
```

### 请求参数
- **路径参数**: 用于标识资源，如 `/projects/{id}`
- **查询参数**: 用于过滤和分页，如 `?page=1&size=10`
- **请求体**: JSON 格式的数据

## 📤 响应格式

### 成功响应
```json
{
  "success": true,
  "code": "200",
  "message": "操作成功",
  "data": {
    // 实际业务数据
  }
}
```

### 错误响应
```json
{
  "success": false,
  "code": "PRJ_001",
  "message": "仓库地址无效",
  "data": null
}
```

## 🚨 错误码

| 错误码 | 描述 | HTTP状态码 |
|--------|------|------------|
| 200 | 操作成功 | 200 |
| PRJ_001 | 仓库地址无效 | 400 |
| PRJ_002 | 项目已存在 | 409 |
| PRJ_003 | 项目不存在 | 404 |
| TSK_001 | 任务创建失败 | 500 |
| TSK_002 | 任务不存在 | 404 |
| AUTH_001 | 认证失败 | 401 |
| AUTH_002 | 权限不足 | 403 |

## 🔗 API 端点详情

### 1. 认证管理

#### 获取当前用户会话信息
```http
GET /auth/session
```

**响应示例：**
```json
{
  "success": true,
  "code": "200",
  "message": "获取成功",
  "data": {
    "userId": "admin",
    "roles": ["ADMIN"],
    "permissions": ["PROJECT_MANAGE", "TASK_MANAGE"]
  }
}
```

### 2. 项目管理（公开接口）

#### 注册新项目
```http
POST /projects/submit
```

**请求体：**
```json
{
  "repositoryUrl": "https://github.com/username/repo.git",
  "branch": "main",
  "description": "项目描述"
}
```

**响应示例：**
```json
{
  "success": true,
  "code": "200",
  "message": "项目注册成功",
  "data": {
    "projectId": 123,
    "status": "PENDING",
    "taskId": 456
  }
}
```

#### 检查仓库状态
```http
POST /projects/check-repository
```

**请求体：**
```json
{
  "repositoryUrl": "https://github.com/username/repo.git"
}
```

#### 获取项目详情
```http
GET /projects/{projectId}/details
```

**响应示例：**
```json
{
  "success": true,
  "code": "200",
  "message": "获取成功",
  "data": {
    "projectId": 123,
    "name": "示例项目",
    "description": "项目描述",
    "repositoryUrl": "https://github.com/username/repo.git",
    "status": "AVAILABLE",
    "visibility": "PUBLIC",
    "createdAt": "2024-01-01T00:00:00Z",
    "statistics": {
      "fileCount": 150,
      "lineCount": 5000,
      "contributorCount": 5
    }
  }
}
```

### 3. 文档管理（公开接口）

#### 获取项目文档版本列表
```http
GET /documentation/projects/{projectId}/versions
```

**响应示例：**
```json
{
  "success": true,
  "code": "200",
  "message": "获取成功",
  "data": [
    {
      "versionId": 1,
      "commitId": "abc123",
      "version": "v1.0.0",
      "createdAt": "2024-01-01T00:00:00Z",
      "status": "PUBLISHED"
    }
  ]
}
```

#### 获取文档内容
```http
GET /documentation/projects/{projectId}/versions/{versionId}/content
```

#### 对比文档版本
```http
GET /documentation/projects/{projectId}/versions/compare
```

**查询参数：**
- `fromVersion`: 源版本ID
- `toVersion`: 目标版本ID

### 4. 任务管理（公开查看）

#### 获取任务状态
```http
GET /tasks/{taskId}/status
```

**响应示例：**
```json
{
  "success": true,
  "code": "200",
  "message": "获取成功",
  "data": {
    "taskId": 456,
    "type": "CODE_PARSE",
    "status": "RUNNING",
    "progress": 65,
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-01T00:05:00Z"
  }
}
```

#### 获取任务详情
```http
GET /tasks/{taskId}
```

### 5. LLM任务管理

#### 任务拉取（外部LLM服务）
```http
POST /llm-tasks/pull
```

**请求体：**
```json
{
  "workerId": "worker-001",
  "capabilities": ["JAVA", "JAVASCRIPT"]
}
```

#### 任务交付回调
```http
POST /llm-tasks/{taskId}/callback
```

**请求体：**
```json
{
  "taskId": 456,
  "status": "COMPLETED",
  "result": {
    "documentContent": "生成的文档内容",
    "metadata": {}
  }
}
```

### 6. 管理员接口

#### 设置项目可见性
```http
PUT /projects/{projectId}/visibility
```

**请求体：**
```json
{
  "visibility": "PUBLIC"
}
```

#### 设置项目状态
```http
PUT /projects/{projectId}/status
```

**请求体：**
```json
{
  "status": "AVAILABLE"
}
```

#### 手动触发项目分析
```http
POST /projects/{projectId}/analyze
```

### 7. 系统接口

#### 获取错误码列表
```http
GET /system/error-codes
```

#### 系统健康检查
```http
GET /system/health
```

## 📊 分页和过滤

### 分页参数
- `page`: 页码（从1开始）
- `size`: 每页大小（默认10，最大100）

### 过滤参数
- `status`: 按状态过滤
- `type`: 按类型过滤
- `keyword`: 关键词搜索

**示例：**
```http
GET /projects?page=1&size=20&status=AVAILABLE&keyword=java
```

## 🔄 异步操作

### 长时间运行的操作
- 项目注册和解析
- 文档生成
- 静态站点生成

这些操作会立即返回任务ID，客户端需要轮询任务状态来获取进度。

### 轮询建议
- 初始间隔：2秒
- 最大间隔：30秒
- 指数退避策略

## 📝 使用示例

### JavaScript/Axios 示例

```javascript
// 注册项目
const registerProject = async (repoUrl) => {
  try {
    const response = await axios.post('/api/v1/projects/submit', {
      repositoryUrl: repoUrl,
      branch: 'main'
    });
    return response.data;
  } catch (error) {
    console.error('项目注册失败:', error.response.data);
  }
};

// 查询任务状态
const checkTaskStatus = async (taskId) => {
  const response = await axios.get(`/api/v1/tasks/${taskId}/status`);
  return response.data;
};
```

### cURL 示例

```bash
# 注册项目
curl -X POST /api/v1/projects/submit \
  -H "Content-Type: application/json" \
  -d '{"repositoryUrl": "https://github.com/username/repo.git"}'

# 查询项目详情
curl -X GET /api/v1/projects/123/details
```

---

*API文档基于 ArchScope v1.0.0，如有更新请参考最新版本。*

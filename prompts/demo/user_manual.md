# ArchScope 用户手册

![ArchScope Logo](../../docs/prototype/images/logo.png)

## 📖 概述

ArchScope（架构鹰眼）是一个面向开发者的架构观测和守护系统，通过自动化分析设计文档和代码仓库，为开发者提供项目的全局视角，帮助开发者快速理解和治理项目。

## 🚀 快速开始

### 系统要求

- **浏览器**: Chrome 80+, Firefox 75+, Safari 13+
- **网络**: 需要访问Git仓库（GitHub、GitLab等）
- **权限**: 管理员功能需要SSO认证

### 访问系统

1. 打开浏览器，访问 ArchScope 系统地址
2. 无需注册即可查看公开项目文档
3. 管理员功能需要通过SSO登录

## 📋 主要功能

### 1. 项目注册和管理

#### 1.1 注册新项目

**步骤：**

1. **访问项目注册页面**
   - 点击首页"添加项目"按钮
   - 或直接访问项目注册页面

2. **填写项目信息**
   ```
   Git仓库地址: https://github.com/username/repository.git
   项目名称: [自动获取或手动输入]
   项目描述: [可选]
   分支选择: [默认主分支]
   ```

3. **提交注册**
   - 系统会自动验证仓库地址
   - 获取项目元数据
   - 创建解析任务

4. **等待解析完成**
   - 系统显示解析进度
   - 包含4个步骤：验证仓库 → 解析分支 → 获取元数据 → 创建项目

#### 1.2 查看项目列表

- **公开项目**: 所有用户都可以查看状态为`AVAILABLE`且可见性为`PUBLIC`的项目
- **项目信息**: 显示项目名称、描述、最后更新时间、解析状态等
- **搜索功能**: 支持按项目名称、描述搜索

#### 1.3 项目详情页面

**包含信息：**
- 项目基本信息（名称、描述、仓库地址）
- 代码统计信息（文件数量、代码行数、贡献者数量）
- 解析状态和任务历史
- 文档版本列表

### 2. 文档查看和管理

#### 2.1 查看项目文档

**访问方式：**
1. 从项目列表点击项目名称
2. 直接访问文档URL：`/docs/projects/{projectId}`

**文档内容：**
- 项目架构概览
- 代码结构分析
- 依赖关系图
- API接口文档
- 模块说明

#### 2.2 文档版本管理

**版本切换：**
- 在文档页面顶部选择版本
- 支持查看不同Commit对应的文档版本
- 显示版本生成时间和对应的Git提交

**版本对比：**
- 选择两个版本进行对比
- 高亮显示差异内容
- 支持并排对比和统一对比视图

#### 2.3 文档导出和分享

- **导出格式**: 支持PDF、HTML格式导出
- **分享链接**: 生成可分享的文档链接
- **静态站点**: 生成独立的文档网站

### 3. 任务监控

#### 3.1 查看任务状态

**任务类型：**
- 代码解析任务
- 文档生成任务
- 静态站点生成任务

**状态说明：**
- `PENDING`: 等待执行
- `RUNNING`: 正在执行
- `COMPLETED`: 执行完成
- `FAILED`: 执行失败

#### 3.2 任务详情

- 任务创建时间
- 执行进度
- 错误信息（如果失败）
- 执行日志

## 🔧 管理员功能

> **注意**: 以下功能需要管理员权限，通过SSO认证后可用

### 1. 项目管理

#### 1.1 设置项目可见性

**操作步骤：**
1. 登录管理员账户
2. 进入项目管理页面
3. 选择目标项目
4. 修改可见性设置：
   - `PUBLIC`: 公开可见
   - `PRIVATE`: 仅管理员可见

#### 1.2 设置项目状态

**状态类型：**
- `AVAILABLE`: 可用状态，用户可以查看文档
- `UNAVAILABLE`: 不可用状态，暂时隐藏

#### 1.3 手动触发解析

- 在项目详情页面点击"重新解析"
- 选择特定分支进行解析
- 监控解析进度

### 2. 任务管理

#### 2.1 任务操作

- **重试失败任务**: 对失败的任务进行重试
- **取消运行任务**: 取消正在执行的任务
- **查看详细日志**: 查看任务执行的详细信息

#### 2.2 系统监控

- 查看系统整体运行状态
- 监控任务队列状态
- 查看错误统计

## ❓ 常见问题解答

### Q1: 支持哪些Git托管平台？
**A**: 目前支持GitHub、GitLab、Bitbucket等主流Git托管平台。支持HTTP和SSH协议的仓库地址。

### Q2: 支持哪些编程语言？
**A**: MVP阶段重点支持Java语言，后续会扩展支持JavaScript、TypeScript、Python等语言。

### Q3: 项目解析需要多长时间？
**A**: 解析时间取决于项目大小，一般小型项目1-3分钟，大型项目可能需要10-30分钟。

### Q4: 如何处理私有仓库？
**A**: 系统支持配置Git访问令牌，可以访问私有仓库。请联系管理员配置相关认证信息。

### Q5: 文档更新频率如何？
**A**: 目前需要手动触发更新。后续版本会支持Webhook自动更新。

### Q6: 如何报告问题？
**A**: 如遇到问题，请联系技术支持团队或在项目仓库提交Issue。

### Q7: 如何扩展系统功能？
**A**: ArchScope 提供了丰富的扩展机制，支持自定义解析器、任务执行器等。详情请参考[扩展能力指南](extension_guide.md)。

### Q8: 如何使用API接口？
**A**: 系统提供完整的RESTful API，详细接口文档请参考[API文档](api_documentation.md)。

## 📚 相关文档

- [API 文档](api_documentation.md) - 完整的API接口说明
- [扩展能力指南](extension_guide.md) - 系统扩展开发指南
- [技术架构文档](../../docs/architecture.md) - 系统架构设计说明
- [开发者指南](../../README-DDD.md) - DDD架构开发规范

## 📞 技术支持

- **邮箱**: <EMAIL>
- **文档**: 查看在线技术文档
- **社区**: 加入开发者社区讨论
- **GitHub**: [项目仓库](https://github.com/im47cn/arch-scope)

---

*本手册基于 ArchScope v1.0.0 编写，如有更新请参考最新版本文档。*

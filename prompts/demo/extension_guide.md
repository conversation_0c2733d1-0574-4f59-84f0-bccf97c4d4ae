# ArchScope 扩展能力指南

## 🎯 扩展概述

ArchScope 采用模块化设计，提供了丰富的扩展点和插件机制，支持开发者根据特定需求扩展系统功能。系统支持的扩展类型包括：

### 扩展类型

1. **自定义解析器** - 支持新的编程语言或特殊文件格式
2. **分块策略** - 自定义代码分块和上下文提取逻辑  
3. **任务执行器** - 扩展新的任务类型和执行逻辑
4. **文档模板** - 自定义文档生成模板和样式
5. **集成适配器** - 集成外部系统和服务

### 扩展方式

- **插件注册机制** - 通过注册表自动发现和加载扩展
- **接口实现** - 实现标准接口来扩展功能
- **配置驱动** - 通过配置文件定制行为
- **依赖注入** - 利用Spring框架的依赖注入机制

## 🔧 如何开发自定义解析器

### 1. 传统解析器开发

#### 1.1 实现 TraditionalCodeParser 接口

```java
@Component
public class CustomLanguageParser implements TraditionalCodeParser {
    
    @Override
    public FileParseResult parseFile(String filename, String content) {
        // 实现解析逻辑
        FileParseResult.Builder resultBuilder = FileParseResult.builder()
            .filename(filename)
            .languageType(getSupportedLanguage())
            .successful(true);
            
        try {
            // 1. 解析导入语句
            List<String> imports = extractImports(content);
            resultBuilder.imports(imports);
            
            // 2. 解析类/模块定义
            List<ClassDefinition> classes = extractClassDefinitions(content);
            resultBuilder.classDefinitions(classes);
            
            // 3. 解析依赖关系
            List<DependencyRelation> dependencies = extractDependencies(content);
            resultBuilder.dependencies(dependencies);
            
            return resultBuilder.build();
        } catch (Exception e) {
            return resultBuilder
                .successful(false)
                .errorMessage("解析失败: " + e.getMessage())
                .build();
        }
    }
    
    @Override
    public LanguageType getSupportedLanguage() {
        return LanguageType.CUSTOM_LANGUAGE;
    }
    
    private List<String> extractImports(String content) {
        // 实现导入语句提取逻辑
        return new ArrayList<>();
    }
    
    private List<ClassDefinition> extractClassDefinitions(String content) {
        // 实现类定义提取逻辑
        return new ArrayList<>();
    }
}
```

#### 1.2 注册解析器

解析器会通过Spring的依赖注入机制自动注册到 `TraditionalParserRegistry`：

```java
@Configuration
public class ParserConfiguration {
    
    @Bean
    public TraditionalCodeParser customLanguageParser() {
        return new CustomLanguageParser();
    }
}
```

### 2. 分块策略开发

#### 2.1 实现 ChunkingStrategy 接口

```java
public class CustomChunkingStrategy implements ChunkingStrategy {
    
    @Override
    public List<CodeChunk> chunk(String content, String filename, String context) {
        List<CodeChunk> chunks = new ArrayList<>();
        
        // 实现自定义分块逻辑
        // 例如：按函数、类、模块等进行分块
        
        return chunks;
    }
    
    @Override
    public String extractContext(String content) {
        // 提取上下文信息（如导入语句、包声明等）
        return "";
    }
}
```

#### 2.2 注册分块策略

```java
@Component
public class CustomCodeChunkingService extends DefaultCodeChunkingService {
    
    @PostConstruct
    public void registerCustomStrategies() {
        // 注册自定义分块策略
        registerStrategy(LanguageType.CUSTOM_LANGUAGE, new CustomChunkingStrategy());
    }
}
```

### 3. LLM解析器扩展

对于支持LLM解析的语言，可以通过提示词模板来定制解析行为：

```java
@Service
public class CustomLlmParserService {
    
    public String generatePrompt(String filename, String content, String languageType) {
        return String.format("""
            请分析以下%s代码文件：
            
            文件名：%s
            
            代码内容：
            ```%s
            %s
            ```
            
            请提取以下信息：
            1. 类和接口定义
            2. 方法和函数
            3. 依赖关系
            4. 自定义特性...
            
            返回JSON格式结果。
            """, languageType, filename, languageType.toLowerCase(), content);
    }
}
```

## 🔌 可用的扩展点和插件机制

### 1. 解析器扩展点

| 扩展点 | 接口 | 描述 | 使用场景 |
|--------|------|------|----------|
| 传统解析器 | `TraditionalCodeParser` | 基于AST或正则的代码解析 | 新语言支持、特殊格式解析 |
| 分块策略 | `ChunkingStrategy` | 代码分块和上下文提取 | 优化LLM处理、自定义分析粒度 |
| 语言识别 | `LanguageType` | 文件类型识别 | 支持新文件扩展名 |

### 2. 任务执行器扩展点

| 扩展点 | 接口 | 描述 | 使用场景 |
|--------|------|------|----------|
| 任务执行器 | `TaskExecutor` | 自定义任务类型执行 | 新任务类型、特殊处理逻辑 |
| 任务调度 | `TaskScheduler` | 任务调度策略 | 优先级调度、资源管理 |

### 3. 文档生成扩展点

| 扩展点 | 接口 | 描述 | 使用场景 |
|--------|------|------|----------|
| 文档模板 | `DocumentTemplate` | 自定义文档模板 | 特定格式、企业标准 |
| 渲染引擎 | `MarkdownService` | Markdown渲染定制 | 自定义样式、扩展语法 |
| 静态站点 | `StaticSiteGenerator` | 静态站点生成 | 自定义主题、布局 |

### 4. 集成扩展点

| 扩展点 | 接口 | 描述 | 使用场景 |
|--------|------|------|----------|
| Git集成 | `GitService` | Git仓库操作 | 新Git平台、认证方式 |
| LLM集成 | `LlmService` | LLM服务集成 | 新LLM提供商、本地模型 |
| 通知集成 | `NotificationService` | 通知服务 | 自定义通知渠道 |

## 🏗️ 扩展开发最佳实践

### 1. 设计原则

- **单一职责**: 每个扩展只负责一个特定功能
- **接口隔离**: 实现最小必要的接口
- **依赖倒置**: 依赖抽象而非具体实现
- **开闭原则**: 对扩展开放，对修改封闭

### 2. 代码规范

```java
// 良好的扩展实现示例
@Component
@Slf4j
public class PythonTraditionalParser implements TraditionalCodeParser {
    
    // 使用常量定义正则表达式
    private static final Pattern IMPORT_PATTERN = 
        Pattern.compile("^\\s*(?:from\\s+([\\w.]+)\\s+)?import\\s+(.+)$", Pattern.MULTILINE);
    
    // 提供详细的日志记录
    @Override
    public FileParseResult parseFile(String filename, String content) {
        log.debug("开始解析Python文件: {}", filename);
        
        try {
            // 实现逻辑...
            log.info("Python文件解析成功: {}", filename);
            return result;
        } catch (Exception e) {
            log.error("Python文件解析失败: {}", filename, e);
            throw new ParseException("解析失败", e);
        }
    }
    
    // 提供清晰的错误处理
    private void handleParseError(String context, Exception e) {
        log.warn("解析警告 - {}: {}", context, e.getMessage());
    }
}
```

### 3. 测试策略

```java
@ExtendWith(MockitoExtension.class)
class CustomParserTest {
    
    @InjectMocks
    private CustomLanguageParser parser;
    
    @Test
    @DisplayName("测试基本解析功能")
    void testBasicParsing() {
        // Given
        String content = "// 测试代码内容";
        
        // When
        FileParseResult result = parser.parseFile("test.custom", content);
        
        // Then
        assertThat(result.isSuccessful()).isTrue();
        assertThat(result.getClassDefinitions()).isNotEmpty();
    }
    
    @Test
    @DisplayName("测试错误处理")
    void testErrorHandling() {
        // 测试异常情况的处理
    }
}
```

### 4. 性能优化

- **缓存机制**: 缓存解析结果和中间数据
- **异步处理**: 使用异步方式处理耗时操作
- **资源管理**: 及时释放资源，避免内存泄漏
- **批量处理**: 支持批量文件处理

### 5. 配置管理

```yaml
# application.yml
archscope:
  parsers:
    custom-language:
      enabled: true
      timeout: 30s
      max-file-size: 10MB
      patterns:
        class: "class\\s+(\\w+)"
        method: "def\\s+(\\w+)"
```

## 🚀 扩展部署和发布

### 1. 开发环境部署

```bash
# 1. 编译扩展
mvn clean compile

# 2. 运行测试
mvn test

# 3. 打包
mvn package
```

### 2. 集成测试

```bash
# 启动测试环境
docker-compose -f docker-compose.test.yml up -d

# 运行集成测试
mvn integration-test
```

### 3. 生产环境部署

#### 3.1 JAR包部署

```bash
# 将扩展JAR包放入classpath
cp custom-parser-1.0.0.jar /app/lib/

# 重启应用
systemctl restart archscope
```

#### 3.2 Docker部署

```dockerfile
FROM archscope:latest
COPY custom-extensions/*.jar /app/lib/
```

#### 3.3 配置更新

```yaml
# 更新配置文件
archscope:
  extensions:
    - name: custom-parser
      version: 1.0.0
      enabled: true
```

### 4. 版本管理

- 使用语义化版本号 (Semantic Versioning)
- 维护向后兼容性
- 提供升级指南和迁移脚本
- 建立回滚机制

### 5. 监控和维护

```java
// 添加监控指标
@Component
public class CustomParserMetrics {
    
    private final MeterRegistry meterRegistry;
    private final Counter parseSuccessCounter;
    private final Counter parseFailureCounter;
    
    public CustomParserMetrics(MeterRegistry meterRegistry) {
        this.meterRegistry = meterRegistry;
        this.parseSuccessCounter = Counter.builder("custom.parser.success")
            .description("Custom parser success count")
            .register(meterRegistry);
        this.parseFailureCounter = Counter.builder("custom.parser.failure")
            .description("Custom parser failure count")
            .register(meterRegistry);
    }
}
```

## 📚 扩展示例

### 完整的Python解析器示例

参考项目中的 `JavaTraditionalParser` 和 `JsTraditionalParser` 实现，可以开发类似的Python解析器。

### 自定义文档模板示例

```java
@Component
public class CustomDocumentTemplate implements DocumentTemplate {
    
    @Override
    public String generateDocument(ProjectAnalysisResult analysisResult) {
        // 生成自定义格式的文档
        return templateEngine.process("custom-template", analysisResult);
    }
}
```

---

*扩展指南基于 ArchScope v1.0.0，更多示例和详细文档请参考开发者门户。*

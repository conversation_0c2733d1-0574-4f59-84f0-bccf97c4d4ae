#!/usr/bin/env node

/**
 * 创建测试任务的脚本
 * 直接调用后端API创建一个LLM任务用于测试MCP服务
 */

const axios = require('axios');

// 配置
const API_BASE_URL = 'http://localhost:8080';

// 创建测试任务
async function createTestTask() {
  console.log('🔧 创建测试任务...');

  try {
    // 使用项目分析API来创建任务
    const response = await axios.post(`${API_BASE_URL}/api/projects/999/analyze`, {
      commitId: 'a1b2c3d4e5f6789012345678901234567890abcd'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ 任务创建成功:', response.data);
    return response.data;

  } catch (error) {
    console.error('❌ 创建任务失败:', error.response?.data || error.message);
    throw error;
  }
}

// 检查任务状态
async function checkTaskStatus() {
  console.log('📋 检查任务状态...');

  try {
    const response = await axios.get(`${API_BASE_URL}/api/tasks`, {
      params: {
        page: 0,
        size: 10,
        sortBy: 'createdAt',
        direction: 'desc'
      }
    });

    console.log('任务列表:', JSON.stringify(response.data, null, 2));
    return response.data;

  } catch (error) {
    console.error('❌ 获取任务列表失败:', error.response?.data || error.message);
    throw error;
  }
}

// 主函数
async function main() {
  try {
    // 先检查现有任务
    await checkTaskStatus();
    
    // 创建新任务
    await createTestTask();
    
    // 再次检查任务状态
    await checkTaskStatus();

  } catch (error) {
    console.error('脚本执行失败:', error.message);
    process.exit(1);
  }
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = { createTestTask, checkTaskStatus };

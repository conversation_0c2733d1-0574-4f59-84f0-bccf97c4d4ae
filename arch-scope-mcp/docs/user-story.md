## 项目: arch-scope-mcp 服务

## 目标用户: 使用LLM Agent或MCP兼容客户端的开发人员，旨在与ArchScope平台进行高效协作。

## 史诗故事 (Epic): 实现与ArchScope平台的无缝任务交互

作为一名开发者，我希望通过我的MCP客户端（如AI助手）与ArchScope平台进行交互，包括发现可用操作、拉取任务、处理任务以及提交结果，整个过程无需我关心底层的API认证和HTTP细节。

## 用户故事 1: 发现可执行的操作
角色: 首次使用本服务的开发者
叙述: 作为一名开发者，我想让我的MCP客户端列出arch-scope-mcp服务提供的所有可用工具及其功能，以便我能一目了然地知道我可以通过该服务做什么。
验收标准 (Acceptance Criteria):
当我的客户端向arch-scope-mcp服务发起tools/list请求时，必须成功返回一个工具列表。
列表中必须包含名为pullTask的工具。
pullTask工具的description应清晰说明其用途：“从ArchScope平台拉取一个待处理的任务”。
pullTask工具的inputSchema应明确表示它需要一个workerId等来自客户端的参数。
列表中必须包含名为submitResult的工具。
submitResult工具的description应清晰说明其用途：“将处理完成的任务结果提交回ArchScope平台”。
submitResult工具的inputSchema必须详细、准确地定义了提交结果所需的所有字段（如taskId, overallStatus, results等），与llm-task-integration-guide.md中的回调API请求体一致。

## 用户故事 2: 拉取一个新任务（成功路径）
角色: 准备开始工作的开发者
叙述: 作为一名开发者，我希望调用pullTask工具来获取一个新的代码分析任务，以便我可以立即开始分析代码库并生成文档。
验收标准 (Acceptance Criteria):
当我调用pullTask时，arch-scope-mcp服务会使用预配置的ARCHSCOPE_API_TOKEN向ArchScope平台发起认证请求。
如果平台有待处理任务，pullTask的CallToolResult中 isError 必须为false。
返回的structuredContent字段必须是一个包含"hasTask": true的JSON对象。
structuredContent对象中必须包含taskId, projectId, taskType以及inputData.repositoryInfo.cloneUrl等所有执行任务所需的核心信息。
我可以无误地从structuredContent中解析出Git仓库的克隆地址，并用于后续的git clone操作。

## 用户故事 3: 处理无任务可拉取的情况
角色: 积极寻找任务的开发者
叙述: 作为一名开发者，当我调用pullTask但ArchScope平台当前没有分配给我的任务时，我希望立即得到一个明确的“无任务”状态，以便我的客户端可以进入等待或轮询模式，而不是挂起或报错。
验收标准 (Acceptance Criteria):
调用pullTask后，CallToolResult的isError字段必须为false。
返回的structuredContent字段必须是一个包含"hasTask": false的JSON对象。
structuredContent对象中最好能包含一个友好的提示信息，如"message": "No pending tasks available"。
我的MCP客户端能够根据"hasTask": false的状态，自动调整其行为，例如在UI上显示“暂无任务”并建议稍后重试。

## 用户故事 4: 提交已完成的任务（成功路径）
角色: 完成了任务的开发者
叙述: 作为一名开发者，在我本地完成代码分析并生成了README和架构图后，我希望通过调用submitResult工具，将这些成果连同任务ID一起提交到ArchScope平台，以便平台可以记录我的工作成果。
验收标准 (Acceptance Criteria):
我需要根据submitResult的inputSchema，构造一个包含taskId、overallStatus: "COMPLETED"以及results数组的参数对象。
results数组中的每个对象都应包含documentType和documentContent。
调用submitResult(params)后，arch-scope-mcp服务能成功将这些数据转发给ArchScope平台的回调API。
CallToolResult的isError为false，并且structuredContent包含"success": true，以明确告知我提交成功。

## 用户故事 5: 处理任务提交失败（服务端或网络异常）
角色: 提交任务时遇到网络问题的开发者
叙述: 作为一名开发者，如果我尝试提交任务时，arch-scope-mcp服务因为ArchScope平台宕机或网络问题而无法成功提交，我希望收到一个明确的、包含失败原因的错误结果，并且我的提交数据不会丢失，以便我可以自行决定稍后重试。
验收标准 (Acceptance Criteria):
当arch-scope-mcp调用ArchScope回调API失败时（例如，收到503或网络超时），它必须捕获这个异常。
submitResult工具返回的CallToolResult中，isError字段必须为true。
content字段应包含一个文本对象，其text内容应清晰描述失败原因，例如"Error: Failed to submit to ArchScope platform. Status: 503 Service Unavailable"。
arch-scope-mcp服务本身是无状态的，它不会为我重试。我的MCP客户端接收到错误后，有责任保存好我的任务成果，并在之后由我触发重试。

## 用户故事 6: 处理无效的提交数据（客户端数据错误）
角色: 提交任务时不小心提供了错误参数的开发者
叙述: 作为一名开发者，如果我在调用submitResult时不慎遗漏了必需的taskId，或者提供了格式错误的数据，我希望arch-scope-mcp服务能立即拒绝我的请求并返回具体的校验错误信息，以便我能快速定位问题并修正我的调用参数。
验收标准 (Acceptance Criteria):
调用submitResult时，如果传入的参数对象不符合inputSchema（例如，缺少taskId），McpServer的zod校验层应捕获此错误。
服务应立即返回一个isError: true的CallToolResult。
content中的错误信息应明确指出是哪个字段校验失败以及原因，例如"Error: Invalid arguments for tool submitResult: 'taskId' is required."。
在这种情况下，arch-scope-mcp服务根本不会尝试向ArchScope平台发起任何API调用。
# 产品需求文档 (PRD): Arch-Scope MCP 服务 (arch-scope-mcp)

**版本**: 1.1
**目标受众**: LLM 编程工具、开发人员

## 1. 系统概述与目标

`arch-scope-mcp`是一个基于Node.js和TypeScript构建的无状态协议适配器。其核心目标是作为`@modelcontextprotocol/typescript-sdk`的一个实现，将ArchScope平台的任务分发能力，封装成标准的MCP Tools，供任何兼容MCP的客户端（特别是LLM Agent）调用。

## 2. 核心功能与技术实现

系统必须通过`McpServer`类实现，并暴露以下两个核心工具（Tools）。

### 2.1. 工具: `pullTask`

* **功能描述**: 从ArchScope平台拉取一个待处理的任务。这是一个无参数的工具，代表了“寻找工作”的意图。
* **技术流程**:
    1.  当MCP客户端调用`pullTask`工具时，`arch-scope-mcp`服务被触发。
    2.  服务使用一个内部的HTTP客户端，向ArchScope平台的`POST /api/v1/llm-tasks/pull`接口发起请求。
    3.  该请求必须包含一个由环境变量`ARCHSCOPE_API_TOKEN`提供的`Authorization: Bearer <token>`头。
    4.  请求体`body`应包含由上游MCP客户端在调用时提供的`workerId`等信息。
    5.  `pullTask`工具的返回值是一个`CallToolResult`对象，其`structuredContent`字段的内容必须严格匹配ArchScope API的成功响应体（当`hasTask: true`时）。如果平台没有任务，`structuredContent`应反映`hasTask: false`的状态。

### 2.2. 工具: `submitResult`

* **功能描述**: 将客户端处理完成的任务结果提交回ArchScope平台。
* **输入Schema (`inputSchema`)**: 此工具的输入参数必须使用`zod`严格定义，其结构应完全匹配ArchScope平台`POST /api/v1/llm-tasks/{taskId}/callback`接口的请求体（RequestBody）。关键字段包括：
    * `taskId` (string, 必填): 要提交的任务ID。
    * `overallStatus` (string, 必填): 任务的最终状态，如 'COMPLETED' 或 'FAILED'。
    * `results` (array, 可选): 包含多个结果对象的数组。
    * `errorMessage` (string, 可选): 当状态为'FAILED'时的错误信息。
    * ... (其他所有在`llm-task-integration-guide.md`中定义的回调参数)
* **技术流程**:
    1.  当MCP客户端调用`submitResult`工具并提供符合`inputSchema`的参数时，服务被触发。
    2.  服务使用内部HTTP客户端，向`POST /api/v1/llm-tasks/{taskId}/callback`接口发起请求，`{taskId}`从参数中获取。
    3.  请求必须包含`Authorization: Bearer <token>`头。
    4.  请求体`body`的内容完全由工具的输入参数构成。
    5.  `submitResult`工具的返回值是一个`CallToolResult`对象，其`structuredContent`应包含ArchScope API的响应（例如 `{ "success": true, "message": "任务结果处理成功" }`），以向客户端确认提交成功。

## 3. 非功能性需求与架构约束

* **无状态 (Stateless)**: 服务本身不存储任何`taskId`或任务内容。所有状态都由ArchScope平台和调用方的MCP客户端维护。
* **配置管理 (Configuration)**:
    * `ARCHSCOPE_API_TOKEN`: 必须通过此环境变量获取用于和ArchScope平台通信的Bearer Token。
    * `ARCHSCOPE_API_URL`: 必须通过此环境变量获取ArchScope平台的API基地址。
* **安全性 (Security)**: Bearer Token决不能硬编码在代码中。服务必须从环境变量中读取它。
* **错误处理 (Error Handling)**:
    * 当与ArchScope平台的API通信失败（如网络错误、5xx响应）时，工具调用应返回一个`isError: true`的`CallToolResult`，并在`content`中包含详细的错误信息。
    * 客户端负责处理重试逻辑。`arch-scope-mcp`不实现任何重试机制。
* **技术栈 (Tech Stack)**:
    * 语言: TypeScript
    * SDK: `@modelcontextprotocol/typescript-sdk`
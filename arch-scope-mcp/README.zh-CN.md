# ArchScope MCP 服务

[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Node.js](https://img.shields.io/badge/Node.js-18.0+-green.svg)](https://nodejs.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

**[English](README.md) | 简体中文**

一个基于 **模型上下文协议 (MCP)** 的适配器服务，连接 ArchScope 平台的任务分发功能与任何兼容 MCP 的客户端，特别是 LLM 智能体。

## 🎯 概述

`arch-scope-mcp` 是一个基于 Node.js 和 TypeScript 构建的无状态协议适配器。它实现了 `@modelcontextprotocol/sdk`，将 ArchScope 平台的任务管理功能作为标准化的 MCP 工具暴露，实现与 AI 助手和其他 MCP 客户端的无缝集成。

### 核心特性

- 🔄 **无状态设计**: 无本地状态管理 - 所有状态由 ArchScope 平台处理
- 🛠️ **两个核心工具**: `pullTask` 和 `submitResult` 实现完整的任务工作流
- 🔐 **安全认证**: 基于 Bearer token 的 ArchScope API 认证
- ⚡ **TypeScript**: 完整的类型安全和 Zod 模式验证
- 🧪 **全面测试**: 高覆盖率的单元测试和集成测试
- 📝 **丰富的错误处理**: 详细的错误消息和适当的错误分类

## 🚀 快速开始

### 前置要求

- Node.js 18.0 或更高版本
- 访问 ArchScope 平台实例
- 有效的 ArchScope API token
- 兼容 MCP 的客户端（如 Claude Desktop、Cline）

### 安装

#### 方式一：NPM 包（推荐）

```bash
# 全局安装
npm install -g arch-scope-mcp

# 或在项目中本地安装
npm install arch-scope-mcp
```

#### 方式二：从源码安装

```bash
# 克隆仓库
git clone https://github.com/im47cn/arch-scope.git
cd arch-scope/arch-scope-mcp

# 安装依赖并构建
npm install
npm run build
```

## 🔧 MCP 客户端配置

### Claude Desktop 配置

将以下内容添加到您的 Claude Desktop 配置文件：

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "arch-scope": {
      "command": "npx",
      "args": ["arch-scope-mcp"],
      "env": {
        "ARCHSCOPE_API_URL": "https://your-archscope-instance.com",
        "ARCHSCOPE_API_TOKEN": "your_bearer_token_here",
        "LOG_LEVEL": "info"
      }
    }
  }
}
```

### 备选方案：直接使用 Node.js

如果您从源码安装或偏好直接使用 Node.js：

```json
{
  "mcpServers": {
    "arch-scope": {
      "command": "node",
      "args": ["/path/to/arch-scope-mcp/dist/index.js"],
      "env": {
        "ARCHSCOPE_API_URL": "https://your-archscope-instance.com",
        "ARCHSCOPE_API_TOKEN": "your_bearer_token_here",
        "LOG_LEVEL": "info"
      }
    }
  }
}
```

### Cline (VS Code 扩展) 配置

添加到您的 Cline 设置：

```json
{
  "cline.mcpServers": {
    "arch-scope": {
      "command": "npx",
      "args": ["arch-scope-mcp"],
      "env": {
        "ARCHSCOPE_API_URL": "https://your-archscope-instance.com",
        "ARCHSCOPE_API_TOKEN": "your_bearer_token_here"
      }
    }
  }
}
```

## ⚙️ 配置选项

### 必需的环境变量

| 变量 | 描述 | 示例 |
|------|------|------|
| `ARCHSCOPE_API_URL` | ArchScope 平台 API 基础 URL | `https://api.archscope.com` |
| `ARCHSCOPE_API_TOKEN` | 用于认证的 Bearer token | `eyJhbGciOiJIUzI1NiIs...` |

### 可选的环境变量

| 变量 | 默认值 | 描述 |
|------|--------|------|
| `LOG_LEVEL` | `info` | 日志级别：`debug`、`info`、`warn`、`error` |
| `HTTP_TIMEOUT` | `30000` | HTTP 请求超时时间（毫秒） |

### 获取您的 API Token

1. 登录到您的 ArchScope 平台实例
2. 导航到 **设置** → **API 管理**
3. 点击 **生成新 Token**
4. 复制生成的 token 并在配置中使用

## 🔄 重启和验证

1. **重启您的 MCP 客户端**（Claude Desktop、VS Code 等）
2. **验证连接**: 您应该看到两个新的可用工具：
   - `pullTask` - 从 ArchScope 拉取待处理任务
   - `submitResult` - 向 ArchScope 提交任务结果

## 🧪 测试连接

您可以手动测试服务：

```bash
# 如果全局安装
arch-scope-mcp

# 如果本地安装
npx arch-scope-mcp

# 从源码运行
npm run dev
```

成功启动将显示：

```text
🚀 Starting ArchScope MCP Service...
✅ ArchScope MCP Service started successfully!
📋 Server: arch-scope-mcp v1.0.0
🔗 ArchScope API: https://your-archscope-instance.com
🎯 Available tools: pullTask, submitResult
⏳ Waiting for MCP client connections...
```

## 🔍 故障排除

### 常见问题

#### 1. 服务无法启动

**错误**: `ARCHSCOPE_API_URL environment variable is required`

**解决方案**: 确保在您的 MCP 客户端配置中设置了所有必需的环境变量。

#### 2. 认证失败

**错误**: `401 Unauthorized` 或 `403 Forbidden`

**解决方案**:

- 验证您的 API token 是否正确且未过期
- 检查 token 是否具有必要的权限
- 确保 API URL 正确

#### 3. 连接超时

**错误**: `Request timeout after 30000ms`

**解决方案**:

- 检查到 ArchScope 平台的网络连接
- 增加 `HTTP_TIMEOUT` 值
- 验证防火墙设置

#### 4. 工具未在 MCP 客户端中显示

**解决方案**:

- 完全重启您的 MCP 客户端
- 检查 MCP 客户端日志中的错误
- 验证配置文件语法是否正确
- 确保服务路径可访问

### 调试模式

启用调试日志进行详细故障排除：

```json
{
  "mcpServers": {
    "arch-scope": {
      "command": "npx",
      "args": ["arch-scope-mcp"],
      "env": {
        "ARCHSCOPE_API_URL": "https://your-archscope-instance.com",
        "ARCHSCOPE_API_TOKEN": "your_bearer_token_here",
        "LOG_LEVEL": "debug"
      }
    }
  }
}
```

### 日志位置

- **Claude Desktop (macOS)**: `~/Library/Logs/Claude/`
- **Claude Desktop (Windows)**: `%APPDATA%\Claude\logs\`
- **VS Code**: 检查输出面板 → Cline

### 手动测试

独立测试服务：

```bash
# 设置环境变量
export ARCHSCOPE_API_URL="https://your-archscope-instance.com"
export ARCHSCOPE_API_TOKEN="your_bearer_token_here"

# 运行服务
npx arch-scope-mcp
```

## 🔧 可用工具

### 1. `pullTask`

从 ArchScope 平台拉取待处理任务。

**输入参数:**

- `workerId` (必需): 工作节点的唯一标识符
- `workerVersion` (可选): 工作节点版本（默认: "1.0.0"）
- `supportedTaskTypes` (可选): 支持的任务类型数组
- `maxConcurrentTasks` (可选): 最大并发任务数（默认: 3）

**使用示例:**

```json
{
  "name": "pullTask",
  "arguments": {
    "workerId": "my-llm-worker-001",
    "supportedTaskTypes": ["CODE_FULL_ANALYSIS_JAVA", "DOC_SITE_GENERATION_JAVA"]
  }
}
```

**响应（有任务可用）:**

```json
{
  "hasTask": true,
  "taskId": "12345",
  "projectId": "67890",
  "taskType": "CODE_FULL_ANALYSIS_JAVA",
  "inputData": {
    "repositoryInfo": {
      "cloneUrl": "https://github.com/example/project.git",
      "commitId": "a1b2c3d4e5f6789012345678901234567890abcd",
      "branchName": "main"
    }
  }
}
```

### 2. `submitResult`

向 ArchScope 平台提交完成的任务结果。

**输入参数:**

- `taskId` (必需): 要提交结果的任务 ID
- `overallStatus` (必需): "COMPLETED"、"FAILED" 或 "PARTIAL_SUCCESS"
- `results` (可选): 文档结果数组
- `errorMessage` (可选): 状态为 "FAILED" 时的错误消息
- `executionTimeMs` (可选): 任务执行时间（毫秒）
- `workerInfo` (可选): 处理任务的工作节点信息

**使用示例:**

```json
{
  "name": "submitResult",
  "arguments": {
    "taskId": "12345",
    "overallStatus": "COMPLETED",
    "results": [
      {
        "documentType": "README",
        "documentTitle": "项目 README",
        "documentContent": "# 项目\n\n这是项目文档...",
        "status": "SUCCESS"
      }
    ],
    "executionTimeMs": 120000,
    "workerInfo": {
      "workerId": "my-llm-worker-001",
      "workerVersion": "1.0.0"
    }
  }
}
```

## 🏗️ 架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP 客户端     │    │  arch-scope-mcp │    │ ArchScope API   │
│   (LLM Agent)   │◄──►│      服务        │◄──►│      平台       │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 组件

- **MCP 服务器**: 实现模型上下文协议规范
- **工具处理器**: 处理 `pullTask` 和 `submitResult` 操作
- **ArchScope 客户端**: 用于 ArchScope API 通信的 HTTP 客户端
- **模式验证**: 基于 Zod 的输入/输出验证
- **错误处理**: 全面的错误分类和格式化

## 🧪 测试

项目包含全面的测试覆盖：

```bash
# 运行所有测试
npm test

# 运行带覆盖率的测试
npm run test:coverage

# 监视模式运行测试
npm run test:watch

# 仅运行单元测试
npm test tests/unit

# 仅运行集成测试
npm test tests/integration
```

### 测试结构

- **单元测试**: 独立测试各个组件
- **集成测试**: 端到端完整工作流测试
- **模拟**: 使用 Jest 模拟外部依赖

## 🔍 开发

### 项目结构

```
arch-scope-mcp/
├── src/
│   ├── index.ts              # 主入口点
│   ├── server.ts             # MCP 服务器实现
│   ├── tools/                # 工具实现
│   │   ├── pullTask.ts
│   │   └── submitResult.ts
│   ├── services/             # 外部服务客户端
│   │   └── archscopeClient.ts
│   ├── types/                # 类型定义和模式
│   │   └── schemas.ts
│   └── utils/                # 工具函数
│       ├── config.ts
│       └── errors.ts
├── tests/                    # 测试文件
│   ├── unit/
│   └── integration/
├── docs/                     # 文档
└── package.json
```

### 代码质量

项目使用多种工具来维护代码质量：

- **TypeScript**: 静态类型检查
- **ESLint**: 带 TypeScript 规则的代码检查
- **Prettier**: 代码格式化
- **Jest**: 测试框架
- **Zod**: 运行时模式验证

```bash
# 检查代码
npm run lint
npm run lint:fix

# 格式化代码
npm run format

# 类型检查
npm run build
```

## 📚 API 参考

### 环境变量

| 变量 | 必需 | 默认值 | 描述 |
|------|------|--------|------|
| `ARCHSCOPE_API_URL` | 是 | - | ArchScope API 基础 URL |
| `ARCHSCOPE_API_TOKEN` | 是 | - | 用于认证的 Bearer token |
| `LOG_LEVEL` | 否 | `info` | 日志级别 (debug/info/warn/error) |
| `HTTP_TIMEOUT` | 否 | `30000` | HTTP 请求超时时间（毫秒） |

### 错误处理

服务将错误分类为特定类型：

- **ConfigurationError**: 无效或缺失的配置
- **ValidationError**: 无效的输入参数
- **NetworkError**: 网络连接问题
- **ApiError**: ArchScope API 错误
- **ToolExecutionError**: 一般工具执行失败

所有错误都为 MCP 客户端正确格式化，并提供描述性消息。

## 📦 分发和部署

### NPM 包

服务作为 NPM 包分发，便于安装：

```bash
# 全局安装以供系统范围访问
npm install -g arch-scope-mcp

# 在项目中本地安装
npm install arch-scope-mcp

# 无需安装直接使用
npx arch-scope-mcp
```

### Docker 支持

在 Docker 容器中运行服务：

```bash
# 构建镜像
docker build -t arch-scope-mcp .

# 使用环境变量运行
docker run -e ARCHSCOPE_API_URL="https://your-instance.com" \
           -e ARCHSCOPE_API_TOKEN="your_token" \
           arch-scope-mcp
```

### 生产部署

对于生产环境，请考虑：

1. **进程管理**: 使用 PM2 或类似的进程管理器
2. **环境变量**: 使用安全的密钥管理
3. **监控**: 设置健康检查和日志记录
4. **扩展**: 在负载均衡器后部署多个实例

## 🤝 贡献

1. Fork 仓库
2. 创建功能分支 (`git checkout -b feature/amazing-feature`)
3. 进行更改
4. 为新功能添加测试
5. 确保所有测试通过 (`npm test`)
6. 提交更改 (`git commit -m 'Add amazing feature'`)
7. 推送到分支 (`git push origin feature/amazing-feature`)
8. 打开 Pull Request

## 📄 许可证

本项目基于 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件。

## 🆘 支持

如果您遇到任何问题或有疑问：

1. 查看[故障排除指南](docs/troubleshooting.md)
2. 搜索现有的 [GitHub issues](https://github.com/im47cn/arch-scope/issues)
3. 创建包含详细信息的新 issue

## 🔗 相关项目

- [ArchScope 平台](https://github.com/im47cn/arch-scope) - 主要的 ArchScope 项目
- [模型上下文协议](https://github.com/modelcontextprotocol) - MCP 规范和 SDK

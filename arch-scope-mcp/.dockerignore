# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Source files (only include built dist/)
src/
tests/
*.ts
tsconfig.json

# Development files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage/
*.lcov

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Documentation
README.md
docs/
*.md

# CI/CD
.github/

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Temporary folders
tmp/
temp/

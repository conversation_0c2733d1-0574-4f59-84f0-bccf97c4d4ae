/**
 * ArchScope MCP Server Implementation
 * Main server class that implements the Model Context Protocol
 */

import { McpServer } from '@modelcontextprotocol/sdk/server/mcp.js';
import { z } from 'zod';
import { getConfig, Config } from './utils/config';
import { ArchScopeClient } from './services/archscopeClient';
import { createPullTaskHandler } from './tools/pullTask';
import { createSubmitResultHandler } from './tools/submitResult';
import {
  ConfigurationError,
  normalizeError
} from './utils/errors';

/**
 * ArchScope MCP Server class
 */
export class ArchScopeMcpServer {
  private readonly mcpServer: McpServer;
  private readonly config: Config;
  private readonly archscopeClient: ArchScopeClient;

  constructor() {
    try {
      // Load configuration
      this.config = getConfig();

      // Initialize ArchScope API client
      this.archscopeClient = new ArchScopeClient(this.config);

      // Initialize MCP server
      this.mcpServer = new McpServer({
        name: 'arch-scope-mcp',
        version: '1.1.5',
      });

      // Register tools
      this.registerTools();

      console.log('[ArchScopeMcpServer] Server initialized successfully');

    } catch (error) {
      const normalizedError = normalizeError(error);
      console.error('[ArchScopeMcpServer] Initialization failed:', normalizedError.message);
      throw new ConfigurationError(
        `Failed to initialize ArchScope MCP Server: ${normalizedError.message}`,
        normalizedError
      );
    }
  }

  /**
   * Register MCP tools
   */
  private registerTools(): void {
    // Register pullTask tool
    this.mcpServer.registerTool(
      'pullTask',
      {
        title: 'Pull Task',
        description: 'Pull a pending task from ArchScope platform for processing',
        inputSchema: {
          workerId: z.string().min(1, 'workerId is required'),
          workerVersion: z.string().optional().default('1.0.0'),
          supportedTaskTypes: z.array(z.string()).optional(),
          maxConcurrentTasks: z.number().int().positive().optional().default(3),
        },
      },
      createPullTaskHandler(this.archscopeClient)
    );

    // Register submitResult tool
    this.mcpServer.registerTool(
      'submitResult',
      {
        title: 'Submit Result',
        description: 'Submit completed task results back to ArchScope platform',
        inputSchema: {
          taskId: z.string().min(1, 'taskId is required'),
          overallStatus: z.enum(['COMPLETED', 'FAILED', 'PARTIAL_SUCCESS']),
          commitId: z.string().regex(/^[a-f0-9]{40}$/, 'commitId must be a 40-character hex string').optional(),
          results: z.array(z.object({
            documentType: z.string().min(1, 'documentType is required'),
            documentTitle: z.string().optional(),
            documentContent: z.string().optional(),
            filePath: z.string().optional(),
            status: z.enum(['SUCCESS', 'FAILED', 'SKIPPED']),
            errorMessage: z.string().optional(),
          })).optional(),
          startTime: z.string().datetime().optional(),
          endTime: z.string().datetime().optional(),
          executionTimeMs: z.number().int().nonnegative().optional(),
          errorMessage: z.string().optional(),
          errorDetail: z.string().optional(),
          workerInfo: z.object({
            workerId: z.string(),
            workerVersion: z.string(),
          }).optional(),
        },
      },
      createSubmitResultHandler(this.archscopeClient)
    );

    console.log('[ArchScopeMcpServer] Tools registered successfully');
  }

  /**
   * Connect to transport and start the MCP server
   */
  async connect(transport: any): Promise<void> {
    try {
      console.log('[ArchScopeMcpServer] Connecting to transport...');

      // Test connection to ArchScope platform
      const connectionOk = await this.archscopeClient.testConnection();
      if (!connectionOk) {
        console.warn('[ArchScopeMcpServer] Warning: Could not establish connection to ArchScope platform');
      } else {
        console.log('[ArchScopeMcpServer] Connection to ArchScope platform verified');
      }

      // Connect to the transport
      await this.mcpServer.connect(transport);

      console.log('[ArchScopeMcpServer] Server connected successfully');
      console.log(`[ArchScopeMcpServer] ArchScope API URL: ${this.config.archscopeApiUrl}`);
      console.log(`[ArchScopeMcpServer] Log Level: ${this.config.logLevel}`);

    } catch (error) {
      const normalizedError = normalizeError(error);
      console.error('[ArchScopeMcpServer] Failed to connect server:', normalizedError.message);
      throw normalizedError;
    }
  }

  /**
   * Close the MCP server
   */
  async close(): Promise<void> {
    try {
      console.log('[ArchScopeMcpServer] Closing server...');

      await this.mcpServer.close();

      console.log('[ArchScopeMcpServer] Server closed successfully');

    } catch (error) {
      const normalizedError = normalizeError(error);
      console.error('[ArchScopeMcpServer] Failed to close server:', normalizedError.message);
      throw normalizedError;
    }
  }

  /**
   * Get server information
   */
  getServerInfo(): {
    name: string;
    version: string;
    description: string;
    archscopeApiUrl: string;
    logLevel: string;
  } {
    return {
      name: 'arch-scope-mcp',
      version: '1.1.5',
      description: 'ArchScope MCP Service - A Model Context Protocol adapter for ArchScope platform task distribution',
      archscopeApiUrl: this.config.archscopeApiUrl,
      logLevel: this.config.logLevel,
    };
  }
}

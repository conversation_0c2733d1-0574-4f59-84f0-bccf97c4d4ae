/**
 * Error handling utilities for ArchScope MCP Service
 */

/**
 * Base error class for ArchScope MCP Service
 */
export abstract class ArchScopeMcpError extends Error {
  abstract readonly code: string;
  abstract readonly statusCode: number;

  constructor(message: string, public override readonly cause?: Error) {
    super(message);
    this.name = this.constructor.name;
    
    // Maintain proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, this.constructor);
    }
  }
}

/**
 * Configuration related errors
 */
export class ConfigurationError extends ArchScopeMcpError {
  readonly code = 'CONFIGURATION_ERROR';
  readonly statusCode = 500;
}

/**
 * Network/HTTP related errors
 */
export class NetworkError extends ArchScopeMcpError {
  readonly code = 'NETWORK_ERROR';
  readonly statusCode = 503;

  constructor(message: string, public readonly httpStatus?: number, cause?: Error) {
    super(message, cause);
  }
}

/**
 * API response related errors
 */
export class ApiError extends ArchScopeMcpError {
  readonly code = 'API_ERROR';
  readonly statusCode = 502;

  constructor(
    message: string,
    public readonly httpStatus: number,
    public readonly responseBody?: unknown,
    cause?: Error
  ) {
    super(message, cause);
  }
}

/**
 * Input validation related errors
 */
export class ValidationError extends ArchScopeMcpError {
  readonly code = 'VALIDATION_ERROR';
  readonly statusCode = 400;

  constructor(message: string, public readonly validationDetails?: unknown, cause?: Error) {
    super(message, cause);
  }
}

/**
 * Tool execution related errors
 */
export class ToolExecutionError extends ArchScopeMcpError {
  readonly code = 'TOOL_EXECUTION_ERROR';
  readonly statusCode = 500;

  constructor(
    message: string,
    public readonly toolName: string,
    cause?: Error
  ) {
    super(message, cause);
  }
}

/**
 * Convert an unknown error to a standardized error format
 */
export function normalizeError(error: unknown): ArchScopeMcpError {
  if (error instanceof ArchScopeMcpError) {
    return error;
  }

  if (error instanceof Error) {
    return new ToolExecutionError(
      `Unexpected error: ${error.message}`,
      'unknown',
      error
    );
  }

  return new ToolExecutionError(
    `Unexpected error: ${String(error)}`,
    'unknown'
  );
}

/**
 * Format error for MCP CallToolResult
 */
export function formatErrorForMcp(error: ArchScopeMcpError): {
  isError: true;
  content: Array<{ type: 'text'; text: string }>;
} {
  const errorMessage = `Error: ${error.message}`;
  const errorDetails = error.cause ? ` (Caused by: ${error.cause.message})` : '';
  
  return {
    isError: true,
    content: [
      {
        type: 'text',
        text: errorMessage + errorDetails,
      },
    ],
  };
}

/**
 * Check if an error is a network timeout
 */
export function isTimeoutError(error: unknown): boolean {
  if (error instanceof NetworkError) {
    return error.message.toLowerCase().includes('timeout');
  }
  
  if (error instanceof Error) {
    return error.message.toLowerCase().includes('timeout') ||
           error.message.toLowerCase().includes('econnaborted');
  }
  
  return false;
}

/**
 * Check if an error is a network connectivity issue
 */
export function isNetworkError(error: unknown): boolean {
  if (error instanceof NetworkError) {
    return true;
  }
  
  if (error instanceof Error) {
    const message = error.message.toLowerCase();
    return message.includes('econnrefused') ||
           message.includes('enotfound') ||
           message.includes('econnreset') ||
           message.includes('network');
  }
  
  return false;
}

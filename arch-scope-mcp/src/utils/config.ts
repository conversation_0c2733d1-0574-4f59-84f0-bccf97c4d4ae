/**
 * Configuration management for ArchScope MCP Service
 * Handles environment variable loading and validation
 */

import { ConfigurationError } from './errors';

export interface Config {
  archscopeApiUrl: string;
  archscopeApiToken: string;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  httpTimeout: number;
}

/**
 * Load and validate configuration from environment variables
 * @throws {Error} If required environment variables are missing
 */
export function loadConfig(): Config {
  const archscopeApiUrl = process.env['ARCHSCOPE_API_URL'];
  const archscopeApiToken = process.env['ARCHSCOPE_API_TOKEN'];
  const logLevel = (process.env['LOG_LEVEL'] as Config['logLevel']) || 'info';
  const httpTimeout = parseInt(process.env['HTTP_TIMEOUT'] || '30000', 10);

  // Validate required environment variables
  if (!archscopeApiUrl) {
    throw new ConfigurationError(
      'ARCHSCOPE_API_URL environment variable is required. Please set it to your ArchScope API base URL.'
    );
  }

  if (!archscopeApiToken) {
    throw new ConfigurationError(
      'ARCHSCOPE_API_TOKEN environment variable is required. Please set it to your ArchScope API bearer token.'
    );
  }

  // Validate URL format
  try {
    new URL(archscopeApiUrl);
  } catch (error) {
    throw new ConfigurationError(
      `ARCHSCOPE_API_URL must be a valid URL. Received: ${archscopeApiUrl}`
    );
  }

  // Validate log level
  const validLogLevels: Config['logLevel'][] = ['debug', 'info', 'warn', 'error'];
  if (!validLogLevels.includes(logLevel)) {
    throw new ConfigurationError(
      `LOG_LEVEL must be one of: ${validLogLevels.join(', ')}. Received: ${logLevel}`
    );
  }

  // Validate timeout
  if (isNaN(httpTimeout) || httpTimeout <= 0) {
    throw new Error(
      `HTTP_TIMEOUT must be a positive number. Received: ${process.env['HTTP_TIMEOUT']}`
    );
  }

  return {
    archscopeApiUrl: archscopeApiUrl.replace(/\/$/, ''), // Remove trailing slash
    archscopeApiToken,
    logLevel,
    httpTimeout,
  };
}

/**
 * Get configuration instance (singleton pattern)
 */
let configInstance: Config | null = null;

export function getConfig(): Config {
  if (!configInstance) {
    configInstance = loadConfig();
  }
  return configInstance;
}

/**
 * Reset configuration instance (useful for testing)
 */
export function resetConfig(): void {
  configInstance = null;
}

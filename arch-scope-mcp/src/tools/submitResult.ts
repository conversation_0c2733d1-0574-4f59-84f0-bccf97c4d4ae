/**
 * Submit Result Tool Implementation
 * Handles task result submission to ArchScope platform
 */

import { CallToolResult } from '@modelcontextprotocol/sdk/types.js';
import { ArchScopeClient } from '../services/archscopeClient';
import { 
  SubmitResultInputSchema, 
  SubmitResultInput,
  createSuccessResult,
  McpToolResult 
} from '../types/schemas';
import {
  ValidationError,
  formatErrorForMcp,
  normalizeError
} from '../utils/errors';



/**
 * Submit Result Tool Implementation
 */
export class SubmitResultTool {
  constructor(private readonly archscopeClient: ArchScopeClient) {}

  /**
   * Execute the submitResult tool
   */
  async execute(args: unknown): Promise<CallToolResult> {
    try {
      // Validate input arguments
      const validatedInput = this.validateInput(args);
      
      // Call ArchScope API to submit result
      const response = await this.archscopeClient.submitResult(validatedInput);
      
      // Return successful result with structured content
      return this.createSuccessResponse(response);
      
    } catch (error) {
      // Handle and format errors for MCP
      const normalizedError = normalizeError(error);
      console.error('[SubmitResultTool] Execution failed:', normalizedError.message);
      
      return formatErrorForMcp(normalizedError);
    }
  }

  /**
   * Validate input arguments using Zod schema
   */
  private validateInput(args: unknown): SubmitResultInput {
    try {
      return SubmitResultInputSchema.parse(args);
    } catch (error) {
      throw new ValidationError(
        `Invalid arguments for tool submitResult: ${error instanceof Error ? error.message : String(error)}`,
        error,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Create successful response for MCP
   */
  private createSuccessResponse(response: any): CallToolResult {
    // The response should be returned as structured content
    // matching the ArchScope API response format
    const result: McpToolResult = createSuccessResult(response);
    
    return {
      isError: result.isError,
      content: result.content,
    };
  }
}

/**
 * Factory function to create submitResult tool handler
 */
export function createSubmitResultHandler(archscopeClient: ArchScopeClient) {
  const tool = new SubmitResultTool(archscopeClient);
  
  return async (args: unknown): Promise<CallToolResult> => {
    return tool.execute(args);
  };
}

/**
 * Pull Task Tool Implementation
 * Handles task pulling from ArchScope platform
 */

import { CallToolResult } from '@modelcontextprotocol/sdk/types.js';
import { ArchScopeClient } from '../services/archscopeClient';
import { 
  PullTaskInputSchema, 
  PullTaskInput,
  createSuccessR<PERSON>ult,
  McpToolR<PERSON>ult 
} from '../types/schemas';
import {
  ValidationError,
  formatErrorForMcp,
  normalizeError
} from '../utils/errors';



/**
 * Pull Task Tool Implementation
 */
export class PullTaskTool {
  constructor(private readonly archscopeClient: ArchScopeClient) {}

  /**
   * Execute the pullTask tool
   */
  async execute(args: unknown): Promise<CallToolResult> {
    try {
      // Validate input arguments
      const validatedInput = this.validateInput(args);
      
      // Call ArchScope API to pull task
      const response = await this.archscopeClient.pullTask(validatedInput);
      
      // Return successful result with structured content
      return this.createSuccessResponse(response);
      
    } catch (error) {
      // Handle and format errors for MCP
      const normalizedError = normalizeError(error);
      console.error('[PullTaskTool] Execution failed:', normalizedError.message);
      
      return formatErrorForMcp(normalizedError);
    }
  }

  /**
   * Validate input arguments using Zod schema
   */
  private validateInput(args: unknown): PullTaskInput {
    try {
      return PullTaskInputSchema.parse(args);
    } catch (error) {
      throw new ValidationError(
        `Invalid arguments for tool pullTask: ${error instanceof Error ? error.message : String(error)}`,
        error,
        error instanceof Error ? error : undefined
      );
    }
  }

  /**
   * Create successful response for MCP
   */
  private createSuccessResponse(response: any): CallToolResult {
    // The response should be returned as structured content
    // matching the ArchScope API response format
    const result: McpToolResult = createSuccessResult(response);
    
    return {
      isError: result.isError,
      content: result.content,
    };
  }
}

/**
 * Factory function to create pullTask tool handler
 */
export function createPullTaskHandler(archscopeClient: ArchScopeClient) {
  const tool = new PullTaskTool(archscopeClient);
  
  return async (args: unknown): Promise<CallToolResult> => {
    return tool.execute(args);
  };
}

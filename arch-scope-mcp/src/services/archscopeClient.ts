/**
 * ArchScope API Client
 * Handles HTTP communication with ArchScope platform
 */

import axios, { AxiosInstance, AxiosError, AxiosResponse } from 'axios';
import { Config } from '../utils/config';
import { NetworkError, ApiError, normalizeError, ArchScopeMcpError } from '../utils/errors';
import {
  PullTaskInput,
  PullTaskResponse,
  PullTaskResponseSchema,
  SubmitResultInput,
  SubmitResultResponse,
  SubmitResultResponseSchema,
} from '../types/schemas';

/**
 * ArchScope API Client class
 */
export class ArchScopeClient {
  private readonly httpClient: AxiosInstance;

  constructor(private readonly config: Config) {
    this.httpClient = axios.create({
      baseURL: config.archscopeApiUrl,
      timeout: config.httpTimeout,
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.archscopeApiToken}`,
        'User-Agent': 'arch-scope-mcp/1.0.0',
      },
    });

    // Add request interceptor for logging
    this.httpClient.interceptors.request.use(
      (config) => {
        if (this.config.logLevel === 'debug') {
          console.log(`[ArchScopeClient] Request: ${config.method?.toUpperCase()} ${config.url}`);
        }
        return config;
      },
      (error) => {
        console.error('[ArchScopeClient] Request error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and error handling
    this.httpClient.interceptors.response.use(
      (response) => {
        if (this.config.logLevel === 'debug') {
          console.log(`[ArchScopeClient] Response: ${response.status} ${response.statusText}`);
        }
        return response;
      },
      (error) => {
        return Promise.reject(this.handleHttpError(error));
      }
    );
  }

  /**
   * Pull a task from ArchScope platform
   */
  async pullTask(input: PullTaskInput): Promise<PullTaskResponse> {
    try {
      const response: AxiosResponse<unknown> = await this.httpClient.post(
        '/api/v1/llm-tasks/pull',
        input
      );

      // Validate response data against schema
      const validatedData = PullTaskResponseSchema.parse(response.data);

      if (this.config.logLevel === 'debug') {
        console.log('[ArchScopeClient] Pull task response:', validatedData);
      }

      return validatedData;
    } catch (error) {
      // HTTP errors are already handled by the response interceptor
      // Only normalize non-HTTP errors
      if (error instanceof ArchScopeMcpError) {
        console.error('[ArchScopeClient] Pull task failed:', error.message);
        throw error;
      }

      const normalizedError = normalizeError(error);
      console.error('[ArchScopeClient] Pull task failed:', normalizedError.message);
      throw normalizedError;
    }
  }

  /**
   * Submit task result to ArchScope platform
   */
  async submitResult(input: SubmitResultInput): Promise<SubmitResultResponse> {
    try {
      const { taskId, ...requestBody } = input;
      const response: AxiosResponse<unknown> = await this.httpClient.post(
        `/api/v1/llm-tasks/${taskId}/callback`,
        requestBody
      );

      // Validate response data against schema
      const validatedData = SubmitResultResponseSchema.parse(response.data);

      if (this.config.logLevel === 'debug') {
        console.log('[ArchScopeClient] Submit result response:', validatedData);
      }

      return validatedData;
    } catch (error) {
      // HTTP errors are already handled by the response interceptor
      // Only normalize non-HTTP errors
      if (error instanceof ArchScopeMcpError) {
        console.error('[ArchScopeClient] Submit result failed:', error.message);
        throw error;
      }

      const normalizedError = normalizeError(error);
      console.error('[ArchScopeClient] Submit result failed:', normalizedError.message);
      throw normalizedError;
    }
  }

  /**
   * Handle HTTP errors and convert them to appropriate error types
   */
  private handleHttpError(error: unknown): Error {
    if (axios.isAxiosError(error)) {
      const axiosError = error as AxiosError;
      
      // Network/connection errors
      if (!axiosError.response) {
        if (axiosError.code === 'ECONNABORTED') {
          return new NetworkError(
            `Request timeout after ${this.config.httpTimeout}ms`,
            undefined,
            axiosError
          );
        }
        
        return new NetworkError(
          `Network error: ${axiosError.message}`,
          undefined,
          axiosError
        );
      }

      // HTTP response errors
      const response = axiosError.response;
      const status = response.status;
      const statusText = response.statusText;
      
      // Server errors (5xx)
      if (status >= 500) {
        return new NetworkError(
          `Server error: ${status} ${statusText}`,
          status,
          axiosError
        );
      }
      
      // Client errors (4xx)
      return new ApiError(
        `API error: ${status} ${statusText}`,
        status,
        response.data,
        axiosError
      );
    }

    // Non-axios errors
    if (error instanceof Error) {
      return new NetworkError(
        `Unexpected network error: ${error.message}`,
        undefined,
        error
      );
    }

    return new NetworkError(
      `Unknown network error: ${String(error)}`,
      undefined
    );
  }

  /**
   * Test connection to ArchScope platform
   */
  async testConnection(): Promise<boolean> {
    try {
      // Try to pull a task to test the connection and authentication
      await this.pullTask({
        workerId: 'test-connection',
        workerVersion: '1.0.0',
        maxConcurrentTasks: 1,
      });
      return true;
    } catch (error) {
      console.error('[ArchScopeClient] Connection test failed:', error);
      return false;
    }
  }
}

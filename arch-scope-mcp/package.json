{"name": "@yeepay/arch-scope-mcp", "version": "1.1.5", "description": "ArchScope MCP Service - A Model Context Protocol adapter for ArchScope platform task distribution", "main": "dist/index.js", "types": "dist/index.d.ts", "bin": {"@yeepay/arch-scope-mcp": "dist/index.js"}, "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "format": "prettier --write src/**/*.ts", "clean": "rm -rf dist", "prepare": "npm run build"}, "keywords": ["mcp", "model-context-protocol", "archscope", "llm", "typescript", "code-analysis"], "author": "ArchScope Team", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^1.13.0", "axios": "^1.6.7", "zod": "^3.22.0"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "ts-jest": "^29.1.0", "ts-node": "^10.9.0", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/im47cn/arch-scope.git", "directory": "arch-scope-mcp"}, "bugs": {"url": "https://github.com/im47cn/arch-scope/issues"}, "homepage": "https://github.com/im47cn/arch-scope/tree/main/arch-scope-mcp#readme"}
#!/usr/bin/env node

/**
 * 测试MCP服务完整工作流程
 * 1. 拉取任务
 * 2. 生成当前项目文档
 * 3. 提交任务结果
 */

const { ArchScopeClient } = require('./dist/services/archscopeClient');
const fs = require('fs');
const path = require('path');

// 配置
const config = {
  archscopeApiUrl: 'http://localhost:8080',
  archscopeApiToken: 'test-token', // 由于后端没有认证，使用测试token
  logLevel: 'debug',
  httpTimeout: 30000
};

// 创建客户端
const client = new ArchScopeClient(config);

// 生成文档内容的函数
function generateProjectDocumentation() {
  const projectRoot = path.resolve(__dirname, '..');
  
  // 读取项目基本信息
  const packageJsonPath = path.join(projectRoot, 'package.json');
  let projectInfo = {};
  
  if (fs.existsSync(packageJsonPath)) {
    try {
      projectInfo = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    } catch (error) {
      console.warn('无法读取package.json:', error.message);
    }
  }

  // 生成项目概览文档
  const documentation = `# ArchScope 项目文档

## 项目概览
- **项目名称**: ${projectInfo.name || 'ArchScope'}
- **版本**: ${projectInfo.version || '1.0.0'}
- **描述**: ${projectInfo.description || '代码架构分析平台'}

## 架构设计

### 系统架构
ArchScope是一个基于微服务架构的代码分析平台，主要包含以下模块：

1. **arch-scope-app**: Spring Boot应用主模块
2. **arch-scope-domain**: 领域模型和业务逻辑
3. **arch-scope-web**: Vue3前端应用
4. **arch-scope-mcp**: MCP服务适配器

### 技术栈
- **后端**: Spring Boot 3.x, Java 17
- **前端**: Vue 3, TypeScript, Element Plus
- **数据库**: MySQL 8.0
- **消息队列**: 基于数据库的任务队列
- **文档生成**: LLM + 提示工程

## 核心功能

### 1. 项目管理
- 项目注册和配置
- 代码仓库集成
- 版本管理

### 2. 任务调度
- 异步任务处理
- LLM工作节点管理
- 任务状态跟踪

### 3. 文档生成
- 自动化文档生成
- 多种文档类型支持
- Markdown渲染

### 4. MCP集成
- 模型上下文协议支持
- 任务拉取和提交
- 错误处理和重试

## API接口

### 任务管理API
- \`POST /api/v1/llm-tasks/pull\` - 拉取任务
- \`POST /api/v1/llm-tasks/{taskId}/callback\` - 提交任务结果

### 项目管理API
- \`GET /api/projects\` - 获取项目列表
- \`POST /api/projects\` - 创建项目
- \`PUT /api/projects/{id}\` - 更新项目

## 部署说明

### 环境要求
- Java 17+
- Node.js 18+
- MySQL 8.0+

### 配置说明
- 数据库连接配置
- LLM服务配置
- 文件存储配置

## 开发指南

### 本地开发
1. 启动后端服务: \`mvn spring-boot:run\`
2. 启动前端服务: \`npm run dev\`
3. 启动MCP服务: \`npm run dev\`

### 测试
- 单元测试: \`mvn test\`
- 集成测试: \`npm test\`

---
*文档生成时间: ${new Date().toISOString()}*
*生成工具: ArchScope MCP Service*
`;

  return documentation;
}

// 主测试函数
async function testWorkflow() {
  console.log('🚀 开始测试MCP服务工作流程...\n');

  try {
    // 步骤1: 直接拉取任务（不使用testConnection避免消耗任务）
    console.log('📥 拉取任务...');
    const pullResponse = await client.pullTask({
      workerId: 'test-mcp-worker',
      workerVersion: '1.0.0',
      maxConcurrentTasks: 1
    });

    console.log('拉取任务响应:', JSON.stringify(pullResponse, null, 2));

    if (!pullResponse.hasTask) {
      console.log('⚠️  当前没有待处理任务');
      return;
    }

    const task = pullResponse.task;
    console.log(`✅ 成功拉取任务: ${task.id} - ${task.name}\n`);

    // 步骤3: 生成文档
    console.log('📝 生成项目文档...');
    const documentation = generateProjectDocumentation();
    console.log('✅ 文档生成完成\n');

    // 步骤4: 提交任务结果
    console.log('📤 提交任务结果...');
    const submitResponse = await client.submitResult({
      taskId: task.id,
      overallStatus: 'COMPLETED',
      results: [
        {
          type: 'PRODUCT_OVERVIEW',
          content: documentation,
          format: 'markdown'
        }
      ]
    });

    console.log('提交结果响应:', JSON.stringify(submitResponse, null, 2));
    console.log('✅ 任务结果提交成功\n');

    console.log('🎉 MCP服务工作流程测试完成！');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('HTTP响应:', error.response.status, error.response.data);
    }
  }
}

// 运行测试
if (require.main === module) {
  testWorkflow();
}

module.exports = { testWorkflow, generateProjectDocumentation };

/**
 * Test setup and configuration
 */

// Set test environment variables
process.env['NODE_ENV'] = 'test';
process.env['ARCHSCOPE_API_URL'] = 'http://localhost:8080';
process.env['ARCHSCOPE_API_TOKEN'] = 'test-token';
process.env['LOG_LEVEL'] = 'error'; // Reduce log noise during tests
process.env['HTTP_TIMEOUT'] = '5000';

// Mock console methods to reduce test output noise
const originalConsoleLog = console.log;
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

beforeAll(() => {
  // Only show console output if explicitly enabled
  if (!process.env['SHOW_TEST_LOGS']) {
    console.log = jest.fn();
    console.error = jest.fn();
    console.warn = jest.fn();
  }
});

afterAll(() => {
  // Restore original console methods
  console.log = originalConsoleLog;
  console.error = originalConsoleError;
  console.warn = originalConsoleWarn;
});

// Global test timeout
jest.setTimeout(10000);

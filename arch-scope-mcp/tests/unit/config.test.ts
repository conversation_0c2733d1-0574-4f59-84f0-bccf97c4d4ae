/**
 * Unit tests for configuration management
 */

import { loadConfig, getConfig, resetConfig } from '../../src/utils/config';

describe('Configuration Management', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    // Reset environment variables
    jest.resetModules();
    process.env = { ...originalEnv };
    resetConfig();
  });

  afterAll(() => {
    process.env = originalEnv;
  });

  describe('loadConfig', () => {
    it('should load valid configuration from environment variables', () => {
      process.env['ARCHSCOPE_API_URL'] = 'https://api.archscope.com';
      process.env['ARCHSCOPE_API_TOKEN'] = 'test-token-123';
      process.env['LOG_LEVEL'] = 'debug';
      process.env['HTTP_TIMEOUT'] = '60000';

      const config = loadConfig();

      expect(config).toEqual({
        archscopeApiUrl: 'https://api.archscope.com',
        archscopeApiToken: 'test-token-123',
        logLevel: 'debug',
        httpTimeout: 60000,
      });
    });

    it('should use default values for optional environment variables', () => {
      process.env['ARCHSCOPE_API_URL'] = 'https://api.archscope.com';
      process.env['ARCHSCOPE_API_TOKEN'] = 'test-token-123';
      delete process.env['LOG_LEVEL']; // Clear to test default value
      delete process.env['HTTP_TIMEOUT']; // Clear to test default value

      const config = loadConfig();

      expect(config.logLevel).toBe('info');
      expect(config.httpTimeout).toBe(30000);
    });

    it('should throw error when ARCHSCOPE_API_URL is missing', () => {
      process.env['ARCHSCOPE_API_TOKEN'] = 'test-token-123';
      delete process.env['ARCHSCOPE_API_URL'];

      expect(() => loadConfig()).toThrow(
        'ARCHSCOPE_API_URL environment variable is required'
      );
    });

    it('should throw error when ARCHSCOPE_API_TOKEN is missing', () => {
      process.env['ARCHSCOPE_API_URL'] = 'https://api.archscope.com';
      delete process.env['ARCHSCOPE_API_TOKEN'];

      expect(() => loadConfig()).toThrow(
        'ARCHSCOPE_API_TOKEN environment variable is required'
      );
    });
  });

  describe('getConfig', () => {
    it('should return the same config instance on multiple calls', () => {
      process.env['ARCHSCOPE_API_URL'] = 'https://api.archscope.com';
      process.env['ARCHSCOPE_API_TOKEN'] = 'test-token-123';

      const config1 = getConfig();
      const config2 = getConfig();

      expect(config1).toBe(config2);
    });
  });
});

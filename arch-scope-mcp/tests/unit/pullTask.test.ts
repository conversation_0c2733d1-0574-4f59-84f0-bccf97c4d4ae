/**
 * Unit tests for pullTask tool
 */

import { PullTaskTool, createPullTaskHandler } from '../../src/tools/pullTask';
import { ArchScopeClient } from '../../src/services/archscopeClient';
import { NetworkError } from '../../src/utils/errors';

// Mock ArchScopeClient
jest.mock('../../src/services/archscopeClient');

describe('PullTask Tool', () => {
  let mockArchScopeClient: jest.Mocked<ArchScopeClient>;
  let pullTaskTool: PullTaskTool;

  beforeEach(() => {
    mockArchScopeClient = {
      pullTask: jest.fn(),
      submitResult: jest.fn(),
      testConnection: jest.fn(),
    } as any;

    pullTaskTool = new PullTaskTool(mockArchScopeClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('execute', () => {
    const validInput = {
      workerId: 'worker-123',
      workerVersion: '1.0.0',
      maxConcurrentTasks: 3,
    };

    it('should successfully execute with valid input and task available', async () => {
      const mockResponse = {
        hasTask: true,
        taskId: 'task-123',
        projectId: 'project-456',
        taskType: 'CODE_ANALYSIS',
        priority: 5,
        createdAt: '2023-01-01T10:00:00Z',
        timeoutAt: '2023-01-01T10:30:00Z',
        inputData: {
          schemaVersion: '1.2',
          repositoryInfo: {
            cloneUrl: 'https://github.com/example/repo.git',
            commitId: 'a1b2c3d4e5f6789012345678901234567890abcd',
            branchName: 'main',
          },
        },
      };

      mockArchScopeClient.pullTask.mockResolvedValue(mockResponse);

      const result = await pullTaskTool.execute(validInput);

      expect(mockArchScopeClient.pullTask).toHaveBeenCalledWith(validInput);
      expect(result.isError).toBe(false);
      expect(result.content).toHaveLength(1);
      expect(result.content[0]?.type).toBe('text');

      const parsedContent = JSON.parse((result.content[0] as any)?.text);
      expect(parsedContent).toEqual(mockResponse);
    });

    it('should successfully execute with no task available', async () => {
      const mockResponse = {
        hasTask: false as const,
        message: 'No pending tasks available',
      };

      mockArchScopeClient.pullTask.mockResolvedValue(mockResponse);

      const result = await pullTaskTool.execute(validInput);

      expect(result.isError).toBe(false);
      
      const parsedContent = JSON.parse((result.content[0] as any)?.text);
      expect(parsedContent).toEqual(mockResponse);
    });

    it('should handle validation error for invalid input', async () => {
      const invalidInput = {
        workerId: '', // Empty workerId should fail validation
      };

      const result = await pullTaskTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool pullTask');
      expect(mockArchScopeClient.pullTask).not.toHaveBeenCalled();
    });

    it('should handle missing required field', async () => {
      const invalidInput = {}; // Missing workerId

      const result = await pullTaskTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool pullTask');
      expect(mockArchScopeClient.pullTask).not.toHaveBeenCalled();
    });

    it('should handle network error from ArchScope client', async () => {
      const networkError = new NetworkError('Connection failed');
      mockArchScopeClient.pullTask.mockRejectedValue(networkError);

      const result = await pullTaskTool.execute(validInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Connection failed');
    });

    it('should handle unexpected error', async () => {
      const unexpectedError = new Error('Unexpected error');
      mockArchScopeClient.pullTask.mockRejectedValue(unexpectedError);

      const result = await pullTaskTool.execute(validInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Unexpected error');
    });

    it('should apply default values for optional fields', async () => {
      const minimalInput = {
        workerId: 'worker-123',
      };

      const mockResponse = {
        hasTask: false as const,
        message: 'No pending tasks available',
      };

      mockArchScopeClient.pullTask.mockResolvedValue(mockResponse);

      await pullTaskTool.execute(minimalInput);

      expect(mockArchScopeClient.pullTask).toHaveBeenCalledWith({
        workerId: 'worker-123',
        workerVersion: '1.0.0',
        maxConcurrentTasks: 3,
      });
    });

    it('should validate maxConcurrentTasks is positive', async () => {
      const invalidInput = {
        workerId: 'worker-123',
        maxConcurrentTasks: -1,
      };

      const result = await pullTaskTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool pullTask');
    });

    it('should accept supportedTaskTypes array', async () => {
      const inputWithTaskTypes = {
        workerId: 'worker-123',
        supportedTaskTypes: ['CODE_ANALYSIS', 'DOC_GENERATION'],
      };

      const mockResponse = {
        hasTask: false as const,
        message: 'No pending tasks available',
      };

      mockArchScopeClient.pullTask.mockResolvedValue(mockResponse);

      await pullTaskTool.execute(inputWithTaskTypes);

      expect(mockArchScopeClient.pullTask).toHaveBeenCalledWith({
        workerId: 'worker-123',
        workerVersion: '1.0.0',
        supportedTaskTypes: ['CODE_ANALYSIS', 'DOC_GENERATION'],
        maxConcurrentTasks: 3,
      });
    });
  });

  describe('createPullTaskHandler', () => {
    it('should create a handler function', () => {
      const handler = createPullTaskHandler(mockArchScopeClient);
      
      expect(typeof handler).toBe('function');
    });

    it('should execute tool through handler', async () => {
      const handler = createPullTaskHandler(mockArchScopeClient);
      const mockResponse = {
        hasTask: false as const,
        message: 'No pending tasks available',
      };

      mockArchScopeClient.pullTask.mockResolvedValue(mockResponse);

      const result = await handler({ workerId: 'worker-123' });

      expect(result.isError).toBe(false);
      expect(mockArchScopeClient.pullTask).toHaveBeenCalled();
    });
  });
});

/**
 * Unit tests for error handling utilities
 */

import {
  ArchScopeMcpError,
  ConfigurationError,
  NetworkError,
  ApiError,
  ValidationError,
  ToolExecutionError,
  normalizeError,
  formatErrorForMcp,
  isTimeoutError,
  isNetworkError,
} from '../../src/utils/errors';

describe('Error Handling', () => {
  describe('Error Classes', () => {
    it('should create ConfigurationError with correct properties', () => {
      const error = new ConfigurationError('Config error');
      
      expect(error).toBeInstanceOf(ArchScopeMcpError);
      expect(error.code).toBe('CONFIGURATION_ERROR');
      expect(error.statusCode).toBe(500);
      expect(error.message).toBe('Config error');
      expect(error.name).toBe('ConfigurationError');
    });

    it('should create NetworkError with correct properties', () => {
      const error = new NetworkError('Network error', 503);
      
      expect(error).toBeInstanceOf(ArchScopeMcpError);
      expect(error.code).toBe('NETWORK_ERROR');
      expect(error.statusCode).toBe(503);
      expect(error.message).toBe('Network error');
      expect(error.httpStatus).toBe(503);
    });

    it('should create ApiError with correct properties', () => {
      const responseBody = { error: 'Bad request' };
      const error = new ApiError('API error', 400, responseBody);
      
      expect(error).toBeInstanceOf(ArchScopeMcpError);
      expect(error.code).toBe('API_ERROR');
      expect(error.statusCode).toBe(502);
      expect(error.message).toBe('API error');
      expect(error.httpStatus).toBe(400);
      expect(error.responseBody).toBe(responseBody);
    });

    it('should create ValidationError with correct properties', () => {
      const validationDetails = { field: 'required' };
      const error = new ValidationError('Validation error', validationDetails);
      
      expect(error).toBeInstanceOf(ArchScopeMcpError);
      expect(error.code).toBe('VALIDATION_ERROR');
      expect(error.statusCode).toBe(400);
      expect(error.message).toBe('Validation error');
      expect(error.validationDetails).toBe(validationDetails);
    });

    it('should create ToolExecutionError with correct properties', () => {
      const error = new ToolExecutionError('Tool error', 'pullTask');
      
      expect(error).toBeInstanceOf(ArchScopeMcpError);
      expect(error.code).toBe('TOOL_EXECUTION_ERROR');
      expect(error.statusCode).toBe(500);
      expect(error.message).toBe('Tool error');
      expect(error.toolName).toBe('pullTask');
    });

    it('should preserve cause error', () => {
      const cause = new Error('Original error');
      const error = new NetworkError('Network error', undefined, cause);
      
      expect(error.cause).toBe(cause);
    });
  });

  describe('normalizeError', () => {
    it('should return ArchScopeMcpError as-is', () => {
      const originalError = new ValidationError('Validation failed');
      const normalized = normalizeError(originalError);
      
      expect(normalized).toBe(originalError);
    });

    it('should convert Error to ToolExecutionError', () => {
      const originalError = new Error('Some error');
      const normalized = normalizeError(originalError);
      
      expect(normalized).toBeInstanceOf(ToolExecutionError);
      expect(normalized.message).toBe('Unexpected error: Some error');
      expect(normalized.cause).toBe(originalError);
    });

    it('should convert unknown error to ToolExecutionError', () => {
      const originalError = 'String error';
      const normalized = normalizeError(originalError);
      
      expect(normalized).toBeInstanceOf(ToolExecutionError);
      expect(normalized.message).toBe('Unexpected error: String error');
    });
  });

  describe('formatErrorForMcp', () => {
    it('should format error for MCP response', () => {
      const error = new ValidationError('Invalid input');
      const formatted = formatErrorForMcp(error);
      
      expect(formatted).toEqual({
        isError: true,
        content: [
          {
            type: 'text',
            text: 'Error: Invalid input',
          },
        ],
      });
    });

    it('should include cause error in formatted message', () => {
      const cause = new Error('Root cause');
      const error = new NetworkError('Network failed', undefined, cause);
      const formatted = formatErrorForMcp(error);
      
      expect(formatted.content[0]?.text).toBe(
        'Error: Network failed (Caused by: Root cause)'
      );
    });
  });

  describe('isTimeoutError', () => {
    it('should detect timeout in NetworkError', () => {
      const error = new NetworkError('Request timeout');
      expect(isTimeoutError(error)).toBe(true);
    });

    it('should detect timeout in regular Error', () => {
      const error = new Error('ECONNABORTED timeout');
      expect(isTimeoutError(error)).toBe(true);
    });

    it('should not detect non-timeout errors', () => {
      const error = new NetworkError('Connection refused');
      expect(isTimeoutError(error)).toBe(false);
    });

    it('should handle non-error input', () => {
      expect(isTimeoutError('not an error')).toBe(false);
    });
  });

  describe('isNetworkError', () => {
    it('should detect NetworkError', () => {
      const error = new NetworkError('Network failed');
      expect(isNetworkError(error)).toBe(true);
    });

    it('should detect network-related Error messages', () => {
      expect(isNetworkError(new Error('ECONNREFUSED'))).toBe(true);
      expect(isNetworkError(new Error('ENOTFOUND'))).toBe(true);
      expect(isNetworkError(new Error('ECONNRESET'))).toBe(true);
      expect(isNetworkError(new Error('network error'))).toBe(true);
    });

    it('should not detect non-network errors', () => {
      const error = new ValidationError('Invalid input');
      expect(isNetworkError(error)).toBe(false);
    });

    it('should handle non-error input', () => {
      expect(isNetworkError('not an error')).toBe(false);
    });
  });
});

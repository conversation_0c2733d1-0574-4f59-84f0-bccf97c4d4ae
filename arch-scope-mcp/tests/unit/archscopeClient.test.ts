/**
 * Unit tests for ArchScope API Client
 */

import axios from 'axios';
import { ArchScopeClient } from '../../src/services/archscopeClient';
import { Config } from '../../src/utils/config';
import { NetworkError, ApiError } from '../../src/utils/errors';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('ArchScopeClient', () => {
  let client: ArchScopeClient;
  let mockConfig: Config;
  let mockAxiosInstance: jest.Mocked<any>;

  beforeEach(() => {
    mockConfig = {
      archscopeApiUrl: 'https://api.archscope.com',
      archscopeApiToken: 'test-token',
      logLevel: 'error',
      httpTimeout: 30000,
    };

    mockAxiosInstance = {
      post: jest.fn(),
      interceptors: {
        request: {
          use: jest.fn(),
        },
        response: {
          use: jest.fn(),
        },
      },
    };

    mockedAxios.create.mockReturnValue(mockAxiosInstance);

    client = new ArchScopeClient(mockConfig);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create axios instance with correct configuration', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        baseURL: 'https://api.archscope.com',
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer test-token',
          'User-Agent': 'arch-scope-mcp/1.0.0',
        },
      });
    });

    it('should set up request and response interceptors', () => {
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
    });
  });

  describe('pullTask', () => {
    const mockInput = {
      workerId: 'worker-123',
      workerVersion: '1.0.0',
      maxConcurrentTasks: 3,
    };

    it('should successfully pull task when task is available', async () => {
      const mockResponse = {
        data: {
          hasTask: true,
          taskId: 'task-123',
          projectId: 'project-456',
          taskType: 'CODE_ANALYSIS',
          priority: 5,
          createdAt: '2023-01-01T10:00:00Z',
          timeoutAt: '2023-01-01T10:30:00Z',
          inputData: {
            schemaVersion: '1.2',
            repositoryInfo: {
              cloneUrl: 'https://github.com/example/repo.git',
              commitId: 'a1b2c3d4e5f6789012345678901234567890abcd',
              branchName: 'main',
            },
          },
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await client.pullTask(mockInput);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/llm-tasks/pull',
        mockInput
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should successfully handle no task available', async () => {
      const mockResponse = {
        data: {
          hasTask: false,
          message: 'No pending tasks available',
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await client.pullTask(mockInput);

      expect(result).toEqual(mockResponse.data);
    });

    it('should throw NetworkError on network failure', async () => {
      const networkError = new Error('Network Error');
      networkError.name = 'AxiosError';
      (networkError as any).isAxiosError = true;
      (networkError as any).code = 'ECONNREFUSED';

      mockAxiosInstance.post.mockRejectedValue(networkError);

      await expect(client.pullTask(mockInput)).rejects.toThrow(NetworkError);
    });

    it('should throw NetworkError on timeout', async () => {
      const timeoutError = new Error('timeout of 30000ms exceeded');
      timeoutError.name = 'AxiosError';
      (timeoutError as any).isAxiosError = true;
      (timeoutError as any).code = 'ECONNABORTED';

      mockAxiosInstance.post.mockRejectedValue(timeoutError);

      await expect(client.pullTask(mockInput)).rejects.toThrow(NetworkError);
    });

    it('should throw ApiError on 4xx response', async () => {
      const apiError = new Error('Request failed with status code 400');
      apiError.name = 'AxiosError';
      (apiError as any).isAxiosError = true;
      (apiError as any).response = {
        status: 400,
        statusText: 'Bad Request',
        data: { error: 'Invalid request' },
      };

      mockAxiosInstance.post.mockRejectedValue(apiError);

      await expect(client.pullTask(mockInput)).rejects.toThrow(ApiError);
    });

    it('should throw NetworkError on 5xx response', async () => {
      const serverError = new Error('Request failed with status code 500');
      serverError.name = 'AxiosError';
      (serverError as any).isAxiosError = true;
      (serverError as any).response = {
        status: 500,
        statusText: 'Internal Server Error',
        data: { error: 'Server error' },
      };

      mockAxiosInstance.post.mockRejectedValue(serverError);

      await expect(client.pullTask(mockInput)).rejects.toThrow(NetworkError);
    });
  });

  describe('submitResult', () => {
    const mockInput = {
      taskId: 'task-123',
      overallStatus: 'COMPLETED' as const,
      results: [
        {
          documentType: 'README',
          documentTitle: 'Project README',
          documentContent: '# Project\n\nDescription...',
          status: 'SUCCESS' as const,
        },
      ],
    };

    it('should successfully submit result', async () => {
      const mockResponse = {
        data: {
          success: true,
          message: 'Task result processed successfully',
          data: {
            taskId: 'task-123',
            status: 'COMPLETED',
          },
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await client.submitResult(mockInput);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/llm-tasks/task-123/callback',
        {
          overallStatus: 'COMPLETED',
          results: mockInput.results,
        }
      );
      expect(result).toEqual(mockResponse.data);
    });

    it('should handle submit result failure', async () => {
      const apiError = new Error('Request failed with status code 422');
      apiError.name = 'AxiosError';
      (apiError as any).isAxiosError = true;
      (apiError as any).response = {
        status: 422,
        statusText: 'Unprocessable Entity',
        data: { error: 'Invalid task data' },
      };

      mockAxiosInstance.post.mockRejectedValue(apiError);

      await expect(client.submitResult(mockInput)).rejects.toThrow(ApiError);
    });
  });

  describe('testConnection', () => {
    it('should return true when connection test succeeds', async () => {
      const mockResponse = {
        data: {
          hasTask: false,
          message: 'No pending tasks available',
        },
      };

      mockAxiosInstance.post.mockResolvedValue(mockResponse);

      const result = await client.testConnection();

      expect(result).toBe(true);
      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        '/api/v1/llm-tasks/pull',
        {
          workerId: 'test-connection',
          workerVersion: '1.0.0',
          maxConcurrentTasks: 1,
        }
      );
    });

    it('should return false when connection test fails', async () => {
      const networkError = new Error('Network Error');
      mockAxiosInstance.post.mockRejectedValue(networkError);

      const result = await client.testConnection();

      expect(result).toBe(false);
    });
  });
});

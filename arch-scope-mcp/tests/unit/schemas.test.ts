/**
 * Unit tests for Zod schemas
 */

import {
  PullTaskInputSchema,
  SubmitResultInputSchema,
  PullTaskResponseSchema,
  SubmitResultResponseSchema,
  createSuccessResult,
  createErrorResult,
} from '../../src/types/schemas';

describe('Zod Schemas', () => {
  describe('PullTaskInputSchema', () => {
    it('should validate valid input with required fields only', () => {
      const input = {
        workerId: 'worker-123',
      };

      const result = PullTaskInputSchema.parse(input);

      expect(result).toEqual({
        workerId: 'worker-123',
        workerVersion: '1.0.0',
        maxConcurrentTasks: 3,
      });
    });

    it('should validate valid input with all fields', () => {
      const input = {
        workerId: 'worker-123',
        workerVersion: '2.0.0',
        supportedTaskTypes: ['TYPE_A', 'TYPE_B'],
        maxConcurrentTasks: 5,
      };

      const result = PullTaskInputSchema.parse(input);

      expect(result).toEqual(input);
    });

    it('should reject empty workerId', () => {
      const input = {
        workerId: '',
      };

      expect(() => PullTaskInputSchema.parse(input)).toThrow();
    });

    it('should reject missing workerId', () => {
      const input = {};

      expect(() => PullTaskInputSchema.parse(input)).toThrow();
    });

    it('should reject negative maxConcurrentTasks', () => {
      const input = {
        workerId: 'worker-123',
        maxConcurrentTasks: -1,
      };

      expect(() => PullTaskInputSchema.parse(input)).toThrow();
    });
  });

  describe('SubmitResultInputSchema', () => {
    it('should validate valid input with required fields only', () => {
      const input = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
      };

      const result = SubmitResultInputSchema.parse(input);

      expect(result).toEqual(input);
    });

    it('should validate valid input with all fields', () => {
      const input = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        commitId: 'a1b2c3d4e5f6789012345678901234567890abcd',
        results: [
          {
            documentType: 'README',
            documentTitle: 'Project README',
            documentContent: '# Project\n\nDescription...',
            filePath: 'README.md',
            status: 'SUCCESS' as const,
          },
        ],
        startTime: '2023-01-01T10:00:00Z',
        endTime: '2023-01-01T10:30:00Z',
        executionTimeMs: 1800000,
        errorMessage: 'No errors',
        errorDetail: 'All good',
        workerInfo: {
          workerId: 'worker-123',
          workerVersion: '1.0.0',
        },
      };

      const result = SubmitResultInputSchema.parse(input);

      expect(result).toEqual(input);
    });

    it('should reject empty taskId', () => {
      const input = {
        taskId: '',
        overallStatus: 'COMPLETED' as const,
      };

      expect(() => SubmitResultInputSchema.parse(input)).toThrow();
    });

    it('should reject invalid overallStatus', () => {
      const input = {
        taskId: 'task-123',
        overallStatus: 'INVALID_STATUS',
      };

      expect(() => SubmitResultInputSchema.parse(input)).toThrow();
    });

    it('should reject invalid commitId format', () => {
      const input = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        commitId: 'invalid-commit-id',
      };

      expect(() => SubmitResultInputSchema.parse(input)).toThrow();
    });

    it('should reject negative executionTimeMs', () => {
      const input = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        executionTimeMs: -1000,
      };

      expect(() => SubmitResultInputSchema.parse(input)).toThrow();
    });

    it('should validate document results', () => {
      const input = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        results: [
          {
            documentType: 'README',
            status: 'SUCCESS' as const,
          },
        ],
      };

      const result = SubmitResultInputSchema.parse(input);

      expect(result.results).toEqual([
        {
          documentType: 'README',
          status: 'SUCCESS',
        },
      ]);
    });

    it('should reject document result with empty documentType', () => {
      const input = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        results: [
          {
            documentType: '',
            status: 'SUCCESS' as const,
          },
        ],
      };

      expect(() => SubmitResultInputSchema.parse(input)).toThrow();
    });

    it('should reject document result with invalid status', () => {
      const input = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        results: [
          {
            documentType: 'README',
            status: 'INVALID_STATUS',
          },
        ],
      };

      expect(() => SubmitResultInputSchema.parse(input)).toThrow();
    });
  });

  describe('PullTaskResponseSchema', () => {
    it('should validate successful response with task', () => {
      const response = {
        hasTask: true,
        taskId: 'task-123',
        projectId: 'project-456',
        taskType: 'CODE_ANALYSIS',
        priority: 5,
        createdAt: '2023-01-01T10:00:00Z',
        timeoutAt: '2023-01-01T10:30:00Z',
        inputData: {
          schemaVersion: '1.2',
          repositoryInfo: {
            cloneUrl: 'https://github.com/example/repo.git',
            commitId: 'a1b2c3d4e5f6789012345678901234567890abcd',
            branchName: 'main',
          },
        },
      };

      const result = PullTaskResponseSchema.parse(response);

      expect(result).toEqual(response);
    });

    it('should validate no task response', () => {
      const response = {
        hasTask: false,
        message: 'No pending tasks available',
      };

      const result = PullTaskResponseSchema.parse(response);

      expect(result).toEqual(response);
    });

    it('should reject invalid commitId in repository info', () => {
      const response = {
        hasTask: true,
        taskId: 'task-123',
        projectId: 'project-456',
        taskType: 'CODE_ANALYSIS',
        priority: 5,
        createdAt: '2023-01-01T10:00:00Z',
        timeoutAt: '2023-01-01T10:30:00Z',
        inputData: {
          schemaVersion: '1.2',
          repositoryInfo: {
            cloneUrl: 'https://github.com/example/repo.git',
            commitId: 'invalid-commit',
          },
        },
      };

      expect(() => PullTaskResponseSchema.parse(response)).toThrow();
    });
  });

  describe('SubmitResultResponseSchema', () => {
    it('should validate successful response', () => {
      const response = {
        success: true,
        message: 'Task result processed successfully',
        data: {
          taskId: 'task-123',
          status: 'COMPLETED',
        },
      };

      const result = SubmitResultResponseSchema.parse(response);

      expect(result).toEqual(response);
    });

    it('should validate response without data', () => {
      const response = {
        success: false,
        message: 'Processing failed',
      };

      const result = SubmitResultResponseSchema.parse(response);

      expect(result).toEqual(response);
    });
  });

  describe('Utility Functions', () => {
    describe('createSuccessResult', () => {
      it('should create success result with structured content', () => {
        const data = { taskId: 'task-123', status: 'completed' };
        const result = createSuccessResult(data);

        expect(result).toEqual({
          isError: false,
          content: [
            {
              type: 'text',
              text: JSON.stringify(data, null, 2),
            },
          ],
        });
      });
    });

    describe('createErrorResult', () => {
      it('should create error result with message', () => {
        const message = 'Something went wrong';
        const result = createErrorResult(message);

        expect(result).toEqual({
          isError: true,
          content: [
            {
              type: 'text',
              text: message,
            },
          ],
        });
      });
    });
  });
});

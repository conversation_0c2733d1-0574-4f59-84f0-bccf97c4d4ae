/**
 * Unit tests for submitResult tool
 */

import { SubmitResultTool, createSubmitResultHandler } from '../../src/tools/submitResult';
import { ArchScopeClient } from '../../src/services/archscopeClient';
import { ApiError } from '../../src/utils/errors';

// Mock ArchScopeClient
jest.mock('../../src/services/archscopeClient');

describe('SubmitResult Tool', () => {
  let mockArchScopeClient: jest.Mocked<ArchScopeClient>;
  let submitResultTool: SubmitResultTool;

  beforeEach(() => {
    mockArchScopeClient = {
      pullTask: jest.fn(),
      submitResult: jest.fn(),
      testConnection: jest.fn(),
    } as any;

    submitResultTool = new SubmitResultTool(mockArchScopeClient);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('execute', () => {
    const validInput = {
      taskId: 'task-123',
      overallStatus: 'COMPLETED' as const,
      results: [
        {
          documentType: 'README',
          documentTitle: 'Project README',
          documentContent: '# Project\n\nDescription...',
          filePath: 'README.md',
          status: 'SUCCESS' as const,
        },
      ],
      startTime: '2023-01-01T10:00:00Z',
      endTime: '2023-01-01T10:30:00Z',
      executionTimeMs: 1800000,
      workerInfo: {
        workerId: 'worker-123',
        workerVersion: '1.0.0',
      },
    };

    it('should successfully execute with valid input', async () => {
      const mockResponse = {
        success: true,
        message: 'Task result processed successfully',
        data: {
          taskId: 'task-123',
          status: 'COMPLETED',
        },
      };

      mockArchScopeClient.submitResult.mockResolvedValue(mockResponse);

      const result = await submitResultTool.execute(validInput);

      expect(mockArchScopeClient.submitResult).toHaveBeenCalledWith(validInput);
      expect(result.isError).toBe(false);
      expect(result.content).toHaveLength(1);
      expect(result.content[0]?.type).toBe('text');

      const parsedContent = JSON.parse((result.content[0] as any)?.text);
      expect(parsedContent).toEqual(mockResponse);
    });

    it('should successfully execute with minimal input', async () => {
      const minimalInput = {
        taskId: 'task-123',
        overallStatus: 'FAILED' as const,
        errorMessage: 'Processing failed',
      };

      const mockResponse = {
        success: true,
        message: 'Task result processed successfully',
      };

      mockArchScopeClient.submitResult.mockResolvedValue(mockResponse);

      const result = await submitResultTool.execute(minimalInput);

      expect(mockArchScopeClient.submitResult).toHaveBeenCalledWith(minimalInput);
      expect(result.isError).toBe(false);
    });

    it('should handle validation error for missing taskId', async () => {
      const invalidInput = {
        overallStatus: 'COMPLETED' as const,
      };

      const result = await submitResultTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool submitResult');
      expect(mockArchScopeClient.submitResult).not.toHaveBeenCalled();
    });

    it('should handle validation error for empty taskId', async () => {
      const invalidInput = {
        taskId: '',
        overallStatus: 'COMPLETED' as const,
      };

      const result = await submitResultTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool submitResult');
    });

    it('should handle validation error for invalid overallStatus', async () => {
      const invalidInput = {
        taskId: 'task-123',
        overallStatus: 'INVALID_STATUS',
      };

      const result = await submitResultTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool submitResult');
    });

    it('should handle validation error for invalid commitId', async () => {
      const invalidInput = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        commitId: 'invalid-commit-id',
      };

      const result = await submitResultTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool submitResult');
    });

    it('should handle validation error for negative executionTimeMs', async () => {
      const invalidInput = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        executionTimeMs: -1000,
      };

      const result = await submitResultTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool submitResult');
    });

    it('should handle validation error for invalid document result', async () => {
      const invalidInput = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        results: [
          {
            documentType: '', // Empty documentType should fail
            status: 'SUCCESS' as const,
          },
        ],
      };

      const result = await submitResultTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool submitResult');
    });

    it('should handle validation error for invalid document status', async () => {
      const invalidInput = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        results: [
          {
            documentType: 'README',
            status: 'INVALID_STATUS',
          },
        ],
      };

      const result = await submitResultTool.execute(invalidInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Invalid arguments for tool submitResult');
    });

    it('should handle API error from ArchScope client', async () => {
      const apiError = new ApiError('API request failed', 422);
      mockArchScopeClient.submitResult.mockRejectedValue(apiError);

      const result = await submitResultTool.execute(validInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('API request failed');
    });

    it('should handle unexpected error', async () => {
      const unexpectedError = new Error('Unexpected error');
      mockArchScopeClient.submitResult.mockRejectedValue(unexpectedError);

      const result = await submitResultTool.execute(validInput);

      expect(result.isError).toBe(true);
      expect((result.content[0] as any)?.text).toContain('Unexpected error');
    });

    it('should validate valid commitId format', async () => {
      const inputWithCommitId = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        commitId: 'a1b2c3d4e5f6789012345678901234567890abcd',
      };

      const mockResponse = {
        success: true,
        message: 'Task result processed successfully',
      };

      mockArchScopeClient.submitResult.mockResolvedValue(mockResponse);

      const result = await submitResultTool.execute(inputWithCommitId);

      expect(result.isError).toBe(false);
      expect(mockArchScopeClient.submitResult).toHaveBeenCalledWith(inputWithCommitId);
    });

    it('should validate workerInfo structure', async () => {
      const inputWithWorkerInfo = {
        taskId: 'task-123',
        overallStatus: 'COMPLETED' as const,
        workerInfo: {
          workerId: 'worker-123',
          workerVersion: '1.0.0',
        },
      };

      const mockResponse = {
        success: true,
        message: 'Task result processed successfully',
      };

      mockArchScopeClient.submitResult.mockResolvedValue(mockResponse);

      const result = await submitResultTool.execute(inputWithWorkerInfo);

      expect(result.isError).toBe(false);
    });
  });

  describe('createSubmitResultHandler', () => {
    it('should create a handler function', () => {
      const handler = createSubmitResultHandler(mockArchScopeClient);
      
      expect(typeof handler).toBe('function');
    });

    it('should execute tool through handler', async () => {
      const handler = createSubmitResultHandler(mockArchScopeClient);
      const mockResponse = {
        success: true,
        message: 'Task result processed successfully',
      };

      mockArchScopeClient.submitResult.mockResolvedValue(mockResponse);

      const result = await handler({
        taskId: 'task-123',
        overallStatus: 'COMPLETED',
      });

      expect(result.isError).toBe(false);
      expect(mockArchScopeClient.submitResult).toHaveBeenCalled();
    });
  });
});

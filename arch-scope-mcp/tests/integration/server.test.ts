/**
 * Integration tests for ArchScope MCP Server
 */

import { ArchScopeMcpServer } from '../../src/server';
import { ConfigurationError } from '../../src/utils/errors';
import { resetConfig } from '../../src/utils/config';

describe('ArchScope MCP Server Integration', () => {
  beforeAll(() => {
    // Set up test environment variables
    process.env['ARCHSCOPE_API_URL'] = 'https://api.archscope.test';
    process.env['ARCHSCOPE_API_TOKEN'] = 'test-integration-token';
    process.env['LOG_LEVEL'] = 'error';
    process.env['HTTP_TIMEOUT'] = '10000';
  });

  beforeEach(() => {
    // Reset config cache before each test
    resetConfig();
  });

  afterAll(() => {
    // Clean up environment variables
    delete process.env['ARCHSCOPE_API_URL'];
    delete process.env['ARCHSCOPE_API_TOKEN'];
    delete process.env['LOG_LEVEL'];
    delete process.env['HTTP_TIMEOUT'];
    resetConfig();
  });

  describe('Server Initialization', () => {
    it('should initialize successfully with valid configuration', () => {
      expect(() => {
        const server = new ArchScopeMcpServer();
        expect(server).toBeDefined();
      }).not.toThrow();
    });

    it('should provide server information', () => {
      const server = new ArchScopeMcpServer();
      const serverInfo = server.getServerInfo();
      
      expect(serverInfo).toBeDefined();
      expect(serverInfo.name).toBe('arch-scope-mcp');
      expect(serverInfo.version).toBe('1.0.0');
      expect(serverInfo.description).toContain('ArchScope MCP Service');
      expect(serverInfo.archscopeApiUrl).toBe('https://api.archscope.test');
      expect(serverInfo.logLevel).toBe('error');
    });

    it('should fail initialization with missing API URL', () => {
      delete process.env['ARCHSCOPE_API_URL'];
      resetConfig(); // Reset config cache after changing env vars

      expect(() => {
        new ArchScopeMcpServer();
      }).toThrow(ConfigurationError);

      // Restore for other tests
      process.env['ARCHSCOPE_API_URL'] = 'https://api.archscope.test';
    });

    it('should fail initialization with missing API token', () => {
      delete process.env['ARCHSCOPE_API_TOKEN'];
      resetConfig(); // Reset config cache after changing env vars

      expect(() => {
        new ArchScopeMcpServer();
      }).toThrow(ConfigurationError);

      // Restore for other tests
      process.env['ARCHSCOPE_API_TOKEN'] = 'test-integration-token';
    });
  });

  describe('Server Lifecycle', () => {
    let server: ArchScopeMcpServer;

    beforeEach(() => {
      server = new ArchScopeMcpServer();
    });

    afterEach(async () => {
      if (server) {
        try {
          await server.close();
        } catch (error) {
          // Ignore cleanup errors in tests
        }
      }
    });

    it('should handle server lifecycle methods', () => {
      // Test that server methods exist and are callable
      expect(server.getServerInfo).toBeDefined();
      expect(server.connect).toBeDefined();
      expect(server.close).toBeDefined();
      
      const serverInfo = server.getServerInfo();
      expect(serverInfo).toBeDefined();
    });

    it('should handle close gracefully', async () => {
      await expect(server.close()).resolves.not.toThrow();
    });
  });

  describe('Configuration Validation', () => {
    it('should validate configuration on startup', () => {
      const server = new ArchScopeMcpServer();
      const serverInfo = server.getServerInfo();
      
      expect(serverInfo.archscopeApiUrl).toBe('https://api.archscope.test');
      expect(serverInfo.logLevel).toBe('error');
    });

    it('should handle different log levels', () => {
      process.env['LOG_LEVEL'] = 'debug';
      resetConfig(); // Reset config cache after changing env vars

      const server = new ArchScopeMcpServer();
      const serverInfo = server.getServerInfo();

      expect(serverInfo.logLevel).toBe('debug');

      // Restore
      process.env['LOG_LEVEL'] = 'error';
    });
  });
});

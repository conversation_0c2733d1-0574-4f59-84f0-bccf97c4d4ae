#!/usr/bin/env node

/**
 * 完整的MCP服务工作流程测试
 * 1. 创建新任务
 * 2. 拉取任务
 * 3. 生成文档
 * 4. 提交结果
 */

const { ArchScopeClient } = require('./dist/services/archscopeClient');
const axios = require('axios');
const fs = require('fs');
const path = require('path');

// 配置
const API_BASE_URL = 'http://localhost:8080';
const config = {
  archscopeApiUrl: API_BASE_URL,
  archscopeApiToken: 'test-token',
  logLevel: 'debug',
  httpTimeout: 30000
};

// 创建客户端
const client = new ArchScopeClient(config);

// 生成文档内容的函数
function generateProjectDocumentation() {
  const projectRoot = path.resolve(__dirname, '..');
  
  const documentation = `# ArchScope MCP 服务测试文档

## 测试概述
本文档由ArchScope MCP服务自动生成，用于验证完整的工作流程。

## 测试时间
- 生成时间: ${new Date().toISOString()}
- 测试环境: 本地开发环境

## MCP服务功能验证

### 1. 任务拉取功能 ✅
- 成功连接到ArchScope后端服务
- 正确拉取PENDING状态的任务
- 任务状态自动更新为PROCESSING

### 2. 文档生成功能 ✅
- 基于项目结构生成文档
- 支持Markdown格式输出
- 包含项目基本信息和架构说明

### 3. 任务提交功能 ✅
- 正确提交任务结果到后端
- 任务状态更新为COMPLETED
- 文档内容保存到数据库

## 技术架构

### MCP服务组件
1. **ArchScopeClient**: HTTP客户端，负责与后端API通信
2. **PullTaskTool**: 任务拉取工具
3. **SubmitResultTool**: 结果提交工具
4. **配置管理**: 环境变量和配置加载

### 数据流程
\`\`\`
1. MCP客户端 -> POST /api/v1/llm-tasks/pull -> 获取任务
2. 处理任务 -> 生成文档内容
3. MCP客户端 -> POST /api/v1/llm-tasks/{taskId}/callback -> 提交结果
\`\`\`

## 测试结果
- ✅ 后端连接正常
- ✅ 任务拉取成功
- ✅ 文档生成完成
- ✅ 结果提交成功

## 下一步计划
1. 集成到实际的LLM工作流程
2. 添加错误处理和重试机制
3. 支持更多文档类型
4. 性能优化和监控

---
*本文档由ArchScope MCP Service自动生成*
*测试工具版本: 1.0.0*
`;

  return documentation;
}

// 创建新任务
async function createNewTask() {
  console.log('🔧 创建新任务...');
  
  try {
    const response = await axios.post(`${API_BASE_URL}/api/projects/12/analyze`, {
      commitId: 'b2c3d4e5f6789012345678901234567890abcdef'
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });

    console.log('✅ 任务创建成功:', response.data);
    return response.data.taskId;

  } catch (error) {
    console.error('❌ 创建任务失败:', error.response?.data || error.message);
    throw error;
  }
}

// 等待任务变为PENDING状态
async function waitForTaskPending(taskId, maxWaitSeconds = 10) {
  console.log(`⏳ 等待任务 ${taskId} 变为PENDING状态...`);
  
  for (let i = 0; i < maxWaitSeconds; i++) {
    try {
      const response = await axios.get(`${API_BASE_URL}/api/tasks/${taskId}`);
      const task = response.data;
      
      console.log(`任务状态: ${task.status}`);
      
      if (task.status === 'PENDING') {
        console.log('✅ 任务已准备就绪');
        return true;
      }
      
      // 等待1秒
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.error('检查任务状态失败:', error.message);
    }
  }
  
  console.warn('⚠️ 任务未在预期时间内变为PENDING状态');
  return false;
}

// 主测试函数
async function testCompleteWorkflow() {
  console.log('🚀 开始完整MCP工作流程测试...\n');

  try {
    // 步骤1: 创建新任务
    const taskId = await createNewTask();
    console.log(`任务ID: ${taskId}\n`);

    // 步骤2: 等待任务准备就绪
    const isReady = await waitForTaskPending(taskId);
    if (!isReady) {
      console.log('任务未准备就绪，但继续尝试拉取...\n');
    }

    // 步骤3: 拉取任务
    console.log('📥 拉取任务...');
    const pullResponse = await client.pullTask({
      workerId: 'test-complete-workflow',
      workerVersion: '1.0.0',
      maxConcurrentTasks: 1
    });

    console.log('拉取任务响应:', JSON.stringify(pullResponse, null, 2));

    if (!pullResponse.hasTask) {
      console.log('⚠️ 没有拉取到任务，可能已被其他进程处理');
      return;
    }

    const task = pullResponse.task;
    console.log(`✅ 成功拉取任务: ${task.id} - ${task.name}\n`);

    // 步骤4: 生成文档
    console.log('📝 生成项目文档...');
    const documentation = generateProjectDocumentation();
    console.log('✅ 文档生成完成\n');

    // 步骤5: 提交任务结果
    console.log('📤 提交任务结果...');
    const submitResponse = await client.submitResult({
      taskId: task.id,
      overallStatus: 'COMPLETED',
      commitId: 'b2c3d4e5f6789012345678901234567890abcdef',
      startTime: new Date(Date.now() - 3 * 60 * 1000).toISOString(),
      endTime: new Date().toISOString(),
      executionTimeMs: 180000, // 3分钟
      results: [
        {
          documentType: 'PRODUCT_OVERVIEW',
          documentTitle: 'ArchScope MCP 服务测试文档',
          documentContent: documentation,
          filePath: 'docs/mcp-test.md',
          status: 'SUCCESS'
        }
      ]
    });

    console.log('提交结果响应:', JSON.stringify(submitResponse, null, 2));
    console.log('✅ 任务结果提交成功\n');

    console.log('🎉 完整MCP工作流程测试成功完成！');
    console.log(`📋 任务ID: ${task.id}`);
    console.log('📄 可以在后端查看生成的文档内容');

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error.message);
    if (error.response) {
      console.error('HTTP响应:', error.response.status, error.response.data);
    }
  }
}

// 运行测试
if (require.main === module) {
  testCompleteWorkflow();
}

module.exports = { testCompleteWorkflow };

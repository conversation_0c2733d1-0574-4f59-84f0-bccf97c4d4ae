# ArchScope MCP Service

[![TypeScript](https://img.shields.io/badge/TypeScript-5.0+-blue.svg)](https://www.typescriptlang.org/)
[![Node.js](https://img.shields.io/badge/Node.js-18.0+-green.svg)](https://nodejs.org/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

**English | [简体中文](README.zh-CN.md)**

A **Model Context Protocol (MCP)** adapter service that bridges ArchScope platform's task distribution capabilities with any MCP-compatible client, particularly LLM Agents.

## 🎯 Overview

`arch-scope-mcp` is a stateless protocol adapter built with Node.js and TypeScript. It implements the `@modelcontextprotocol/typescript-sdk` to expose ArchScope platform's task management as standardized MCP Tools, enabling seamless integration with AI assistants and other MCP clients.

### Key Features

- 🔄 **Stateless Design**: No local state management - all state handled by ArchScope platform
- 🛠️ **Two Core Tools**: `pullTask` and `submitResult` for complete task workflow
- 🔐 **Secure Authentication**: Bearer token-based authentication with ArchScope API
- ⚡ **TypeScript**: Full type safety with Zod schema validation
- 🧪 **Comprehensive Testing**: Unit and integration tests with high coverage
- 📝 **Rich Error Handling**: Detailed error messages and proper error categorization

## 🚀 Quick Start

### Prerequisites

- Node.js 18.0 or higher
- Access to an ArchScope platform instance
- Valid ArchScope API token
- MCP-compatible client (e.g., Claude Desktop, Cline)

### Installation

#### Option 1: NPM Package (Recommended)

```bash
# Install globally
npm install -g arch-scope-mcp

# Or install locally in your project
npm install arch-scope-mcp
```

#### Option 2: From Source

```bash
# Clone the repository
git clone https://github.com/im47cn/arch-scope.git
cd arch-scope/arch-scope-mcp

# Install dependencies and build
npm install
npm run build
```

## 🔧 MCP Client Configuration

### Claude Desktop Configuration

Add the following to your Claude Desktop configuration file:

**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`
**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`

```json
{
  "mcpServers": {
    "arch-scope": {
      "command": "npx",
      "args": ["arch-scope-mcp"],
      "env": {
        "ARCHSCOPE_API_URL": "https://your-archscope-instance.com",
        "ARCHSCOPE_API_TOKEN": "your_bearer_token_here",
        "LOG_LEVEL": "info"
      }
    }
  }
}
```

### Alternative: Using Node.js directly

If you installed from source or prefer to use Node.js directly:

```json
{
  "mcpServers": {
    "arch-scope": {
      "command": "node",
      "args": ["/path/to/arch-scope-mcp/dist/index.js"],
      "env": {
        "ARCHSCOPE_API_URL": "https://your-archscope-instance.com",
        "ARCHSCOPE_API_TOKEN": "your_bearer_token_here",
        "LOG_LEVEL": "info"
      }
    }
  }
}
```

### Cline (VS Code Extension) Configuration

Add to your Cline settings:

```json
{
  "cline.mcpServers": {
    "arch-scope": {
      "command": "npx",
      "args": ["arch-scope-mcp"],
      "env": {
        "ARCHSCOPE_API_URL": "https://your-archscope-instance.com",
        "ARCHSCOPE_API_TOKEN": "your_bearer_token_here"
      }
    }
  }
}
```

## ⚙️ Configuration Options

### Required Environment Variables

| Variable | Description | Example |
|----------|-------------|---------|
| `ARCHSCOPE_API_URL` | ArchScope platform API base URL | `https://api.archscope.com` |
| `ARCHSCOPE_API_TOKEN` | Bearer token for authentication | `eyJhbGciOiJIUzI1NiIs...` |

### Optional Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `LOG_LEVEL` | `info` | Logging level: `debug`, `info`, `warn`, `error` |
| `HTTP_TIMEOUT` | `30000` | HTTP request timeout in milliseconds |

### Getting Your API Token

1. Log in to your ArchScope platform instance
2. Navigate to **Settings** → **API Management**
3. Click **Generate New Token**
4. Copy the generated token and use it in your configuration

## 🔄 Restart and Verify

1. **Restart your MCP client** (Claude Desktop, VS Code, etc.)
2. **Verify connection**: You should see two new tools available:
   - `pullTask` - Pull pending tasks from ArchScope
   - `submitResult` - Submit task results to ArchScope

## 🧪 Testing the Connection

You can test the service manually:

```bash
# If installed globally
arch-scope-mcp

# If installed locally
npx arch-scope-mcp

# From source
npm run dev
```

Successful startup will show:

```text
🚀 Starting ArchScope MCP Service...
✅ ArchScope MCP Service started successfully!
📋 Server: arch-scope-mcp v1.0.0
🔗 ArchScope API: https://your-archscope-instance.com
🎯 Available tools: pullTask, submitResult
⏳ Waiting for MCP client connections...
```

## 🔍 Troubleshooting

### Common Issues

#### 1. Service Not Starting

**Error**: `ARCHSCOPE_API_URL environment variable is required`

**Solution**: Ensure all required environment variables are set in your MCP client configuration.

#### 2. Authentication Failed

**Error**: `401 Unauthorized` or `403 Forbidden`

**Solutions**:
- Verify your API token is correct and not expired
- Check that the token has necessary permissions
- Ensure the API URL is correct

#### 3. Connection Timeout

**Error**: `Request timeout after 30000ms`

**Solutions**:
- Check network connectivity to ArchScope platform
- Increase `HTTP_TIMEOUT` value
- Verify firewall settings

#### 4. Tools Not Appearing in MCP Client

**Solutions**:
- Restart your MCP client completely
- Check MCP client logs for errors
- Verify the configuration file syntax is correct
- Ensure the service path is accessible

### Debug Mode

Enable debug logging for detailed troubleshooting:

```json
{
  "mcpServers": {
    "arch-scope": {
      "command": "npx",
      "args": ["arch-scope-mcp"],
      "env": {
        "ARCHSCOPE_API_URL": "https://your-archscope-instance.com",
        "ARCHSCOPE_API_TOKEN": "your_bearer_token_here",
        "LOG_LEVEL": "debug"
      }
    }
  }
}
```

### Log Locations

- **Claude Desktop (macOS)**: `~/Library/Logs/Claude/`
- **Claude Desktop (Windows)**: `%APPDATA%\Claude\logs\`
- **VS Code**: Check the Output panel → Cline

### Manual Testing

Test the service independently:

```bash
# Set environment variables
export ARCHSCOPE_API_URL="https://your-archscope-instance.com"
export ARCHSCOPE_API_TOKEN="your_bearer_token_here"

# Run the service
npx arch-scope-mcp
```

## 🔧 Available Tools

### 1. `pullTask`

Pulls a pending task from the ArchScope platform for processing.

**Input Parameters:**
- `workerId` (required): Unique identifier for the worker node
- `workerVersion` (optional): Worker version (default: "1.0.0")
- `supportedTaskTypes` (optional): Array of supported task types
- `maxConcurrentTasks` (optional): Max concurrent tasks (default: 3)

**Example Usage:**
```json
{
  "name": "pullTask",
  "arguments": {
    "workerId": "my-llm-worker-001",
    "supportedTaskTypes": ["CODE_FULL_ANALYSIS_JAVA", "DOC_SITE_GENERATION_JAVA"]
  }
}
```

**Response (Task Available):**
```json
{
  "hasTask": true,
  "taskId": "12345",
  "projectId": "67890",
  "taskType": "CODE_FULL_ANALYSIS_JAVA",
  "inputData": {
    "repositoryInfo": {
      "cloneUrl": "https://github.com/example/project.git",
      "commitId": "a1b2c3d4e5f6789012345678901234567890abcd",
      "branchName": "main"
    }
  }
}
```

### 2. `submitResult`

Submits completed task results back to the ArchScope platform.

**Input Parameters:**
- `taskId` (required): ID of the task to submit results for
- `overallStatus` (required): "COMPLETED", "FAILED", or "PARTIAL_SUCCESS"
- `results` (optional): Array of document results
- `errorMessage` (optional): Error message if status is "FAILED"
- `executionTimeMs` (optional): Task execution time in milliseconds
- `workerInfo` (optional): Information about the worker that processed the task

**Example Usage:**
```json
{
  "name": "submitResult",
  "arguments": {
    "taskId": "12345",
    "overallStatus": "COMPLETED",
    "results": [
      {
        "documentType": "README",
        "documentTitle": "Project README",
        "documentContent": "# Project\n\nThis is the project documentation...",
        "status": "SUCCESS"
      }
    ],
    "executionTimeMs": 120000,
    "workerInfo": {
      "workerId": "my-llm-worker-001",
      "workerVersion": "1.0.0"
    }
  }
}
```

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MCP Client    │    │  arch-scope-mcp │    │ ArchScope API   │
│   (LLM Agent)   │◄──►│    Service      │◄──►│   Platform      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Components

- **MCP Server**: Implements the Model Context Protocol specification
- **Tool Handlers**: Process `pullTask` and `submitResult` operations
- **ArchScope Client**: HTTP client for ArchScope API communication
- **Schema Validation**: Zod-based input/output validation
- **Error Handling**: Comprehensive error categorization and formatting

## 🧪 Testing

The project includes comprehensive test coverage:

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run only unit tests
npm test tests/unit

# Run only integration tests
npm test tests/integration
```

### Test Structure

- **Unit Tests**: Test individual components in isolation
- **Integration Tests**: Test complete workflows end-to-end
- **Mocking**: Uses Jest mocks for external dependencies

## 🔍 Development

### Project Structure

```
arch-scope-mcp/
├── src/
│   ├── index.ts              # Main entry point
│   ├── server.ts             # MCP Server implementation
│   ├── tools/                # Tool implementations
│   │   ├── pullTask.ts
│   │   └── submitResult.ts
│   ├── services/             # External service clients
│   │   └── archscopeClient.ts
│   ├── types/                # Type definitions and schemas
│   │   └── schemas.ts
│   └── utils/                # Utility functions
│       ├── config.ts
│       └── errors.ts
├── tests/                    # Test files
│   ├── unit/
│   └── integration/
├── docs/                     # Documentation
└── package.json
```

### Code Quality

The project uses several tools to maintain code quality:

- **TypeScript**: Static type checking
- **ESLint**: Code linting with TypeScript rules
- **Prettier**: Code formatting
- **Jest**: Testing framework
- **Zod**: Runtime schema validation

```bash
# Lint code
npm run lint
npm run lint:fix

# Format code
npm run format

# Type check
npm run build
```

## 📚 API Reference

### Environment Variables

| Variable | Required | Default | Description |
|----------|----------|---------|-------------|
| `ARCHSCOPE_API_URL` | Yes | - | ArchScope API base URL |
| `ARCHSCOPE_API_TOKEN` | Yes | - | Bearer token for authentication |
| `LOG_LEVEL` | No | `info` | Logging level (debug/info/warn/error) |
| `HTTP_TIMEOUT` | No | `30000` | HTTP request timeout in milliseconds |

### Error Handling

The service categorizes errors into specific types:

- **ConfigurationError**: Invalid or missing configuration
- **ValidationError**: Invalid input parameters
- **NetworkError**: Network connectivity issues
- **ApiError**: ArchScope API errors
- **ToolExecutionError**: General tool execution failures

All errors are properly formatted for MCP clients with descriptive messages.

## 📦 Distribution & Deployment

### NPM Package

The service is distributed as an NPM package for easy installation:

```bash
# Install globally for system-wide access
npm install -g arch-scope-mcp

# Install locally in a project
npm install arch-scope-mcp

# Use without installation
npx arch-scope-mcp
```

### Docker Support

Run the service in a Docker container:

```bash
# Build the image
docker build -t arch-scope-mcp .

# Run with environment variables
docker run -e ARCHSCOPE_API_URL="https://your-instance.com" \
           -e ARCHSCOPE_API_TOKEN="your_token" \
           arch-scope-mcp
```

### Production Deployment

For production environments, consider:

1. **Process Management**: Use PM2 or similar process managers
2. **Environment Variables**: Use secure secret management
3. **Monitoring**: Set up health checks and logging
4. **Scaling**: Deploy multiple instances behind a load balancer

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes
4. Add tests for new functionality
5. Ensure all tests pass (`npm test`)
6. Commit your changes (`git commit -m 'Add amazing feature'`)
7. Push to the branch (`git push origin feature/amazing-feature`)
8. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

If you encounter any issues or have questions:

1. Check the [troubleshooting guide](docs/troubleshooting.md)
2. Search existing [GitHub issues](https://github.com/im47cn/arch-scope/issues)
3. Create a new issue with detailed information

## 🔗 Related Projects

- [ArchScope Platform](https://github.com/im47cn/arch-scope) - Main ArchScope project
- [Model Context Protocol](https://github.com/modelcontextprotocol) - MCP specification and SDKs

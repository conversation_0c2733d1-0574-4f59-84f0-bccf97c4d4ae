# 测试环境配置
spring:
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: ""
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false

# ArchScope 应用配置 - 测试环境
archscope:
  git:
    servers:
      # 默认配置
      default-config:
        supports-https: true
        supports-http: false
        suggest-https-on-ssh-failure: true
        https-url-template: "https://{host}/{owner}/{repo}.git"
        http-url-template: "http://{host}/{owner}/{repo}.git"

      # 具体服务器配置 - 使用列表格式避免点号问题
      server-list:
        - host: "github.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true

        - host: "gitlab.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true

        - host: "gitlab.yeepay.com"
          supports-https: true
          supports-http: false
          suggest-https-on-ssh-failure: false
          personal-access-token: "**************************"
          token-username: "oauth2"

        - host: "gitee.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true

        - host: "bitbucket.org"
          supports-https: true
          supports-http: false
          suggest-https-on-ssh-failure: true

# 日志配置
logging:
  level:
    com.archscope: DEBUG
    org.springframework.boot.context.properties: DEBUG

package com.archscope.integration.config;

import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.context.annotation.Profile;
import org.springframework.test.context.TestPropertySource;

/**
 * 服务发现集成测试配置
 */
@TestConfiguration
@Profile("test")
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:service_discovery_test;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.jpa.hibernate.ddl-auto=create-drop",
    "logging.level.com.archscope=DEBUG"
})
public class ServiceDiscoveryIntegrationTestConfig {

    /**
     * 测试环境下的特殊配置可以在这里添加
     */
}
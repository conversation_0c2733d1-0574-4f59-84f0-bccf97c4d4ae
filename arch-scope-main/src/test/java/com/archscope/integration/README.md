# 服务发现系统集成测试

## 概述

本目录包含服务发现系统的集成测试，用于验证服务注册到发现的完整流程、需求匹配流程和Maven坐标查询功能。

## 测试结构

### 1. ServiceDiscoveryIntegrationTest
- **目的**: 测试服务发现系统的核心业务逻辑集成
- **覆盖范围**:
  - 服务注册到发现的完整流程
  - 需求匹配流程
  - Maven坐标查询功能
  - 数据库集成和事务管理

### 2. ServiceDiscoveryApiIntegrationTest
- **目的**: 测试服务发现REST API的完整功能
- **覆盖范围**:
  - 服务注册API端点
  - 服务发现API端点
  - 需求匹配API端点
  - 错误处理和参数验证
  - API响应格式和性能

### 3. ServiceDiscoveryIntegrationTestSuite
- **目的**: 组织和运行所有服务发现相关的集成测试
- **功能**: 提供统一的测试入口点

## 测试配置

### 测试环境
- **Profile**: `test`
- **数据库**: H2内存数据库
- **事务**: 每个测试方法都在事务中运行，测试结束后自动回滚

### 依赖注入
测试使用Spring Boot的测试框架，支持完整的依赖注入和Spring上下文加载。

## 测试数据

### 测试服务数据
集成测试会创建以下测试服务：
1. **TestUserAuthService** - 用户认证服务
2. **TestDataProcessingService** - 数据处理服务  
3. **TestNotificationService** - 通知服务

### Maven坐标测试数据
- GroupId: `com.archscope.test`
- ArtifactId: 各种测试服务的artifact ID
- Version: 不同的版本号用于测试版本匹配

## 测试场景

### 1. 服务注册到发现的完整流程
- 服务注册成功验证
- 服务发现查询验证
- 多种查询方式测试（ID、名称、类型、标签）
- 分页查询测试
- 数据持久化验证

### 2. 需求匹配流程
- 基于需求描述的服务匹配
- 基于能力的需求匹配
- 匹配结果排序和过滤
- 能力需求自动生成

### 3. Maven坐标查询功能
- 基于groupId和artifactId的查询
- 精确坐标查询（包含版本）
- 模糊查询和部分匹配
- 边界情况和性能测试

### 4. API集成测试
- REST端点功能测试
- 请求参数验证
- 响应格式验证
- 错误处理测试
- 性能和安全性测试

## 运行测试

### 运行单个测试类
```bash
mvn test -Dtest=ServiceDiscoveryIntegrationTest
mvn test -Dtest=ServiceDiscoveryApiIntegrationTest
```

### 运行测试套件
```bash
mvn test -Dtest=ServiceDiscoveryIntegrationTestSuite
```

### 运行所有集成测试
```bash
mvn test -Dtest="*IntegrationTest"
```

## 测试报告

测试运行后会生成以下报告：
- JUnit测试报告: `target/surefire-reports/`
- 覆盖率报告: `target/site/jacoco/` (如果配置了JaCoCo)

## 注意事项

### 当前状态
由于服务发现功能还在开发中，当前的集成测试主要验证：
1. 测试框架的正确性
2. Spring上下文的加载
3. 数据库连接和事务管理
4. 测试结构和模式

### 待完善的功能
当相关服务实现完成后，需要添加以下实际测试：
1. 真实的服务注册和发现逻辑测试
2. 实际的数据库操作测试
3. 完整的API端点测试
4. 真实的需求匹配算法测试

### 扩展指南
添加新的集成测试时，请遵循以下原则：
1. 使用描述性的测试名称
2. 每个测试方法只测试一个特定场景
3. 使用适当的断言和验证
4. 添加必要的测试数据清理
5. 更新相关文档

## 依赖关系

### 测试依赖
- Spring Boot Test
- JUnit 5
- AssertJ
- MockMvc (用于API测试)
- H2 Database (测试数据库)
- Testcontainers (如果需要真实数据库测试)

### 业务依赖
- arch-scope-domain (领域层)
- arch-scope-app (应用层)
- arch-scope-infrastructure (基础设施层)
- arch-scope-facade (接口层)

## 故障排除

### 常见问题
1. **Spring上下文加载失败**: 检查测试配置和依赖注入
2. **数据库连接问题**: 确认H2数据库配置正确
3. **事务问题**: 检查@Transactional注解的使用
4. **MockMvc配置问题**: 确认Web层配置正确

### 调试技巧
1. 使用`@Sql`注解预加载测试数据
2. 使用`@TestPropertySource`覆盖配置
3. 启用DEBUG日志查看详细信息
4. 使用`@DirtiesContext`重置Spring上下文
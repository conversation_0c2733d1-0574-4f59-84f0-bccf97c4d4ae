package com.archscope;

import com.archscope.domain.service.DocumentStorageService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Profile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.List;

/**
 * 演示文档加载器
 * 用于将演示文档加载到系统中进行测试
 */
@SpringBootApplication
@Profile("demo")
public class DemoDocumentLoader implements CommandLineRunner {

    @Autowired
    private DocumentStorageService documentStorageService;

    public static void main(String[] args) {
        System.setProperty("spring.profiles.active", "demo");
        SpringApplication.run(DemoDocumentLoader.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        System.out.println("🚀 开始加载演示文档...");
        
        // 定义演示文档映射
        DemoDocument[] demoDocuments = {
            new DemoDocument("prompts/demo/documentation_index.md", "PRODUCT_INTRO", "产品简介"),
            new DemoDocument("prompts/demo/api_documentation.md", "API", "API文档"),
            new DemoDocument("prompts/demo/user_manual.md", "USER_MANUAL", "用户手册"),
            new DemoDocument("prompts/demo/extension_guide.md", "EXTENSION", "扩展指南"),
            new DemoDocument("demo-architecture-with-mermaid.md", "ARCHITECTURE", "架构设计")
        };

        Long projectId = 999L; // 演示项目ID
        String version = "demo-v1.0.0";

        int successCount = 0;
        int totalCount = 0;

        for (DemoDocument doc : demoDocuments) {
            totalCount++;
            try {
                if (loadDocument(projectId, doc, version)) {
                    successCount++;
                    System.out.println("✅ 成功加载: " + doc.description);
                } else {
                    System.out.println("⚠️  跳过文件: " + doc.filePath + " (文件不存在)");
                }
            } catch (Exception e) {
                System.err.println("❌ 加载失败: " + doc.description + " - " + e.getMessage());
            }
        }

        System.out.println("\n📊 加载完成统计:");
        System.out.println("总文档数: " + totalCount);
        System.out.println("成功加载: " + successCount);
        System.out.println("项目ID: " + projectId);
        System.out.println("版本: " + version);

        // 验证加载结果
        System.out.println("\n🔍 验证加载结果:");
        List<String> versions = documentStorageService.getProjectVersions(projectId);
        List<String> docTypes = documentStorageService.getDocumentTypes(projectId, version);
        
        System.out.println("可用版本: " + versions);
        System.out.println("文档类型: " + docTypes);

        System.out.println("\n🌐 API测试命令:");
        System.out.println("# 获取文档类型列表");
        System.out.println("curl http://localhost:8080/api/documents/projects/" + projectId + "/types");
        System.out.println();
        System.out.println("# 获取版本列表");
        System.out.println("curl http://localhost:8080/api/documents/projects/" + projectId + "/versions");
        System.out.println();
        
        for (String docType : docTypes) {
            System.out.println("# 获取" + docType + "文档内容");
            System.out.println("curl http://localhost:8080/api/documents/projects/" + projectId + "/documents/" + docType + "/content");
            System.out.println();
            System.out.println("# 获取" + docType + "文档HTML");
            System.out.println("curl http://localhost:8080/api/documents/projects/" + projectId + "/documents/" + docType + "/html");
            System.out.println();
        }
    }

    private boolean loadDocument(Long projectId, DemoDocument doc, String version) throws IOException {
        Path filePath = Paths.get(doc.filePath);
        if (!Files.exists(filePath)) {
            return false;
        }

        // 读取文件内容
        byte[] bytes = Files.readAllBytes(filePath);
        String content = new String(bytes, "UTF-8");

        // 存储到DocumentStorageService
        String storedPath = documentStorageService.storeDocument(projectId, doc.docType, content, version);
        
        System.out.println("📁 存储路径: " + storedPath);
        return true;
    }

    private static class DemoDocument {
        final String filePath;
        final String docType;
        final String description;

        DemoDocument(String filePath, String docType, String description) {
            this.filePath = filePath;
            this.docType = docType;
            this.description = description;
        }
    }
}

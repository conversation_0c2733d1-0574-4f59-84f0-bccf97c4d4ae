package com.archscope.config;

import com.archscope.domain.config.GitServerConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.boot.context.properties.source.ConfigurationPropertySource;
import org.springframework.boot.context.properties.source.MapConfigurationPropertySource;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GitServerConfig 配置绑定测试
 * 验证配置属性是否正确绑定到GitServerConfig类中
 */
class GitServerConfigIntegrationTest {

    private GitServerConfig gitServerConfig;

    @BeforeEach
    void setUp() {
        // 手动创建GitServerConfig对象，模拟配置绑定
        gitServerConfig = new GitServerConfig();

        // 设置默认配置
        GitServerConfig.ServerConfig defaultConfig = new GitServerConfig.ServerConfig();
        defaultConfig.setSupportsHttps(true);
        defaultConfig.setSupportsHttp(false);
        defaultConfig.setSuggestHttpsOnSshFailure(true);
        defaultConfig.setHttpsUrlTemplate("https://{host}/{owner}/{repo}.git");
        defaultConfig.setHttpUrlTemplate("http://{host}/{owner}/{repo}.git");
        gitServerConfig.setDefaultConfig(defaultConfig);

        // 创建hosts映射
        Map<String, GitServerConfig.ServerConfig> hosts = new HashMap<>();

        // GitHub配置
        GitServerConfig.ServerConfig githubConfig = new GitServerConfig.ServerConfig();
        githubConfig.setSupportsHttps(true);
        githubConfig.setSupportsHttp(true);
        githubConfig.setSuggestHttpsOnSshFailure(true);
        hosts.put("github.com", githubConfig);

        // GitLab Yeepay配置
        GitServerConfig.ServerConfig yeepayConfig = new GitServerConfig.ServerConfig();
        yeepayConfig.setSupportsHttps(true);
        yeepayConfig.setSupportsHttp(false);
        yeepayConfig.setSuggestHttpsOnSshFailure(false);
        yeepayConfig.setPersonalAccessToken("**************************");
        yeepayConfig.setTokenUsername("oauth2");
        hosts.put("gitlab.yeepay.com", yeepayConfig);

        // GitLab.com配置
        GitServerConfig.ServerConfig gitlabConfig = new GitServerConfig.ServerConfig();
        gitlabConfig.setSupportsHttps(true);
        gitlabConfig.setSupportsHttp(true);
        gitlabConfig.setSuggestHttpsOnSshFailure(true);
        hosts.put("gitlab.com", gitlabConfig);

        // Gitee配置
        GitServerConfig.ServerConfig giteeConfig = new GitServerConfig.ServerConfig();
        giteeConfig.setSupportsHttps(true);
        giteeConfig.setSupportsHttp(true);
        giteeConfig.setSuggestHttpsOnSshFailure(true);
        hosts.put("gitee.com", giteeConfig);

        // Bitbucket配置
        GitServerConfig.ServerConfig bitbucketConfig = new GitServerConfig.ServerConfig();
        bitbucketConfig.setSupportsHttps(true);
        bitbucketConfig.setSupportsHttp(false);
        bitbucketConfig.setSuggestHttpsOnSshFailure(true);
        hosts.put("bitbucket.org", bitbucketConfig);

        gitServerConfig.setHosts(hosts);
    }

    @Test
    void testConfigurationLoading() {
        assertNotNull(gitServerConfig, "GitServerConfig应该被正确创建");

        // 验证默认配置
        GitServerConfig.ServerConfig defaultConfig = gitServerConfig.getDefaultConfig();
        assertNotNull(defaultConfig, "默认配置不应该为null");
        assertTrue(defaultConfig.isSupportsHttps(), "默认配置应该支持HTTPS");
        assertFalse(defaultConfig.isSupportsHttp(), "默认配置不应该支持HTTP");
        assertTrue(defaultConfig.isSuggestHttpsOnSshFailure(), "默认配置应该在SSH失败时建议HTTPS");
    }

    @Test
    void testGitLabYeepayConfiguration() {
        // 获取gitlab.yeepay.com的配置
        GitServerConfig.ServerConfig config = gitServerConfig.getServerConfig("gitlab.yeepay.com");
        assertNotNull(config, "gitlab.yeepay.com配置不应该为null");
        
        // 验证基本配置
        assertTrue(config.isSupportsHttps(), "gitlab.yeepay.com应该支持HTTPS");
        assertFalse(config.isSupportsHttp(), "gitlab.yeepay.com不应该支持HTTP");
        assertFalse(config.isSuggestHttpsOnSshFailure(), "gitlab.yeepay.com不应该在SSH失败时建议HTTPS");
        
        // 验证个人访问令牌配置
        assertTrue(config.hasPersonalAccessToken(), "gitlab.yeepay.com应该配置了个人访问令牌");
        assertEquals("**************************", config.getPersonalAccessToken(), "个人访问令牌应该匹配");
        assertEquals("oauth2", config.getTokenUsername(), "令牌用户名应该为oauth2");
    }

    @Test
    void testGitHubConfiguration() {
        // 获取github.com的配置
        GitServerConfig.ServerConfig config = gitServerConfig.getServerConfig("github.com");
        assertNotNull(config, "github.com配置不应该为null");
        
        // 验证基本配置
        assertTrue(config.isSupportsHttps(), "github.com应该支持HTTPS");
        assertTrue(config.isSupportsHttp(), "github.com应该支持HTTP");
        assertTrue(config.isSuggestHttpsOnSshFailure(), "github.com应该在SSH失败时建议HTTPS");
        
        // GitHub配置中的个人访问令牌是注释掉的，所以应该没有配置
        assertFalse(config.hasPersonalAccessToken(), "github.com不应该配置个人访问令牌（注释状态）");
    }

    @Test
    void testUnknownServerConfiguration() {
        // 获取不存在的服务器配置，应该返回默认配置
        GitServerConfig.ServerConfig config = gitServerConfig.getServerConfig("unknown.server.com");
        assertNotNull(config, "未知服务器应该返回默认配置");
        
        // 应该与默认配置相同
        GitServerConfig.ServerConfig defaultConfig = gitServerConfig.getDefaultConfig();
        assertEquals(defaultConfig.isSupportsHttps(), config.isSupportsHttps());
        assertEquals(defaultConfig.isSupportsHttp(), config.isSupportsHttp());
        assertEquals(defaultConfig.isSuggestHttpsOnSshFailure(), config.isSuggestHttpsOnSshFailure());
        assertFalse(config.hasPersonalAccessToken(), "默认配置不应该有个人访问令牌");
    }

    @Test
    void testHostsMapLoading() {
        // 验证hosts映射是否正确加载
        assertNotNull(gitServerConfig.getHosts(), "hosts映射不应该为null");
        assertFalse(gitServerConfig.getHosts().isEmpty(), "hosts映射不应该为空");
        
        // 验证包含预期的主机
        assertTrue(gitServerConfig.getHosts().containsKey("gitlab.yeepay.com"), "应该包含gitlab.yeepay.com");
        assertTrue(gitServerConfig.getHosts().containsKey("github.com"), "应该包含github.com");
        assertTrue(gitServerConfig.getHosts().containsKey("gitlab.com"), "应该包含gitlab.com");
        assertTrue(gitServerConfig.getHosts().containsKey("gitee.com"), "应该包含gitee.com");
        assertTrue(gitServerConfig.getHosts().containsKey("bitbucket.org"), "应该包含bitbucket.org");
    }

    @Test
    void testUrlTemplates() {
        GitServerConfig.ServerConfig defaultConfig = gitServerConfig.getDefaultConfig();
        
        // 验证URL模板
        assertEquals("https://{host}/{owner}/{repo}.git", defaultConfig.getHttpsUrlTemplate());
        assertEquals("http://{host}/{owner}/{repo}.git", defaultConfig.getHttpUrlTemplate());
    }
}

server:
  port: 8080

spring:
  application:
    name: arch-scope
  aop:
    auto: false # 暂时禁用AOP自动配置以解决启动问题
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: root
    password: root
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      initial-size: 5
      min-idle: 10
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  redis:
    host: localhost
    port: 6379
    # password: "test"  # 本地开发环境不需要密码
    database: 0
    timeout: 10s
    lettuce:
      pool:
        max-active: 200
        max-wait: -1ms
        max-idle: 10
        min-idle: 0
  cache:
    type: redis
    redis:
      time-to-live: 3600000
      cache-null-values: true
      use-key-prefix: true
      key-prefix: "arch-scope:"

# Redisson配置
redisson:
  # 线程池数量
  threads: 16
  # Netty线程池数量
  nettyThreads: 32
  # 传输模式
  transportMode: NIO
  # 单节点配置
  singleServerConfig:
    # 连接空闲超时，单位：毫秒
    idleConnectionTimeout: 10000
    # 连接超时，单位：毫秒
    connectTimeout: 10000
    # 命令等待超时，单位：毫秒
    timeout: 3000
    # 命令失败重试次数
    retryAttempts: 3
    # 命令重试发送时间间隔，单位：毫秒
    retryInterval: 1500
    # 发布和订阅连接的最小空闲连接数
    subscriptionConnectionMinimumIdleSize: 1
    # 发布和订阅连接池大小
    subscriptionConnectionPoolSize: 50
    # 最小空闲连接数
    connectionMinimumIdleSize: 32
    # 连接池大小
    connectionPoolSize: 64
    # 数据库编号
    database: 0
    # DNS监测时间间隔，单位：毫秒
    dnsMonitoringInterval: 5000

mybatis-plus:
  mapper-locations: classpath*:/mapper/**/*.xml
  type-aliases-package: com.archscope.domain.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      id-type: auto
      # 表名下划线命名
      table-underline: true

rocketmq:
  name-server: ${ROCKETMQ_NAME_SERVER:localhost:9876}
  producer:
    group: ${ROCKETMQ_PRODUCER_GROUP:arch-scope-producer}
    send-message-timeout: 3000
    retry-times-when-send-failed: 2
    retry-times-when-send-async-failed: 2
    compress-message-body-threshold: 4096
    retry-next-server: true
    max-message-size: 4194304
  # 容错模式 - 当 RocketMQ 不可用时允许应用正常启动
  fault-tolerant: true
  # 消费者相关配置
  consumer:
    # 是否启用消费者
    # 由应用启动时自动设置，也可以手动配置
    enabled: true
    # 是否在启动时立即注册消费者
    pull-timeout: 1000
    # 启动失败时的等待时间(毫秒)
    max-reconnect-wait: 5000

logging:
  level:
    root: INFO
    com.archscope: DEBUG

# ArchScope应用配置
archscope:
  prompts:
    directory: prompts

  openai:
    api:
      key: ${OPENAI_API_KEY:your-api-key}
      model: gpt-4
  git:
    servers:
      # 默认配置
      default-config:
        supports-https: true
        supports-http: false
        suggest-https-on-ssh-failure: true
        https-url-template: "https://{host}/{owner}/{repo}.git"
        http-url-template: "http://{host}/{owner}/{repo}.git"

      # 具体服务器配置 - 使用列表格式避免点号问题
      server-list:
        - host: "github.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true
          # personal-access-token: "ghp_xxxxxxxxxxxxxxxxxxxx"  # GitHub个人访问令牌（可选配置）
          # token-username: "token"  # GitHub使用token作为用户名

        - host: "gitlab.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true
          # personal-access-token: "glpat-xxxxxxxxxxxxxxxxxxxx"  # GitLab个人访问令牌（可选配置）
          # token-username: "oauth2"  # GitLab推荐使用oauth2作为用户名

        - host: "gitlab.yeepay.com"
          supports-https: true
          supports-http: false
          suggest-https-on-ssh-failure: false # 不建议转换，因为内网可能有访问限制
          personal-access-token: "**************************" # 个人访问令牌
          token-username: "oauth2" # GitLab推荐使用oauth2作为用户名

        - host: "gitee.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true

        - host: "bitbucket.org"
          supports-https: true
          supports-http: false
          suggest-https-on-ssh-failure: true

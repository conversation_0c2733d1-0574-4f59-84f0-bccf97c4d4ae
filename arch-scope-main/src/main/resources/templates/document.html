<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${title} + ' - ' + ${project.name}">文档标题 - 项目名称</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <!-- 其他头部元素将由MarkdownService动态添加 -->
    <th:block th:each="entry : ${additionalHeadElements}">
        <th:block th:utext="${entry.value}"></th:block>
    </th:block>
</head>
<body class="flex">
    <!-- 左侧边栏导航 -->
    <div class="sidebar fixed h-screen bg-gray-900 text-gray-300 flex flex-col shadow-lg">
        <!-- 项目导航栏 -->
        <div class="px-4 py-3 border-b border-gray-700 bg-gray-800">
            <div class="flex items-center justify-between">
                <a th:href="@{'/projects/' + ${project.id}}" class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center" title="返回项目主页">
                    <i class="fas fa-arrow-circle-left"></i>
                </a>
                <div class="relative inline-block text-left flex-grow mx-2">
                    <select id="project-select-sidebar" class="form-select block w-full py-1 px-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                        <option th:value="${project.id}" th:text="${project.name}">项目名称</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="p-4 border-b border-gray-700">
            <h1 class="text-xl font-bold text-white truncate" th:text="${project.name} + ' 文档'">项目名称 文档</h1>
        </div>
        <nav class="flex-grow p-6 overflow-y-auto">
            <ul class="space-y-2">
                <!-- 文档类型导航 -->
                <li th:each="docType : ${T(com.archscope.domain.valueobject.DocumentType).values()}">
                    <a th:href="@{${docType.name().toLowerCase()} + '.html'}"
                       th:class="${docType == currentDocType ? 'flex items-center justify-between text-white bg-gray-700 px-4 py-2 rounded-md transition duration-200 active-link' : 'flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200'}">
                        <span class="flex items-center">
                            <i th:class="${'fas ' + (docType.name() == 'PRODUCT_INTRO' ? 'fa-home' :
                                            docType.name() == 'ARCHITECTURE' ? 'fa-sitemap' :
                                            docType.name() == 'API' ? 'fa-file-code' :
                                            docType.name() == 'USER_MANUAL' ? 'fa-book-open' :
                                            docType.name() == 'EXTENSION' ? 'fa-puzzle-piece' :
                                            docType.name() == 'LLMS_TXT' ? 'fa-file-alt' : 'fa-file') + ' w-5 mr-3'}"></i>
                            <span th:text="${docType.displayName}">文档类型</span>
                        </span>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- 版本选择和比较 -->
        <div class="p-4 border-t border-gray-700 mt-auto">
            <div class="flex items-center mb-3">
                <label for="version-select-sidebar" class="text-gray-400 text-sm font-medium whitespace-nowrap mr-2">版本:</label>
                <select id="version-select-sidebar" class="form-select flex-grow px-2 py-1 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                    <option th:each="ver : ${versions}" th:value="${ver.versionTag}" th:text="${ver.versionTag}" th:selected="${ver.versionTag == version}">版本号</option>
                </select>
            </div>
            <button class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 border border-gray-600 rounded shadow transition duration-200 text-sm" onclick="compareVersions()">
                <i class="fas fa-code-branch mr-2"></i> 版本对比
            </button>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="content flex-grow py-8 px-8 ml-72">
        <div class="content-card bg-white shadow-xl rounded-lg p-8 prose max-w-none">
            <div class="document-header">
                <h1 th:text="${title}" class="text-3xl font-bold text-gray-800 mb-6 pb-2 border-b border-gray-200">文档标题</h1>
                <div class="document-meta">
                    <span class="version" th:text="'版本: ' + ${version}">版本: 1.0.0</span>
                    <span class="last-updated" th:text="'最后更新: ' + ${#temporals.format(lastUpdated, 'yyyy-MM-dd HH:mm')}">最后更新: 2023-11-01 12:00</span>
                </div>
            </div>

            <div class="document-content" th:utext="${content}">
                <!-- 文档内容将在这里显示 -->
            </div>
        </div>
    </div>

    <footer class="fixed bottom-0 w-full bg-gray-900 text-white py-2 text-center">
        <p>&copy; 2023 ArchScope. 保留所有权利。</p>
    </footer>

    <!-- 版本比较脚本 -->
    <script>
        function compareVersions() {
            const currentVersion = document.getElementById('version-select-sidebar').value;
            const docType = window.location.pathname.split('/').pop().replace('.html', '');
            window.location.href = `compare.html?docType=${docType}&version=${currentVersion}`;
        }

        // 版本选择变更处理
        document.getElementById('version-select-sidebar').addEventListener('change', function() {
            const selectedVersion = this.value;
            const currentPath = window.location.pathname;
            const docType = currentPath.split('/').pop().replace('.html', '');
            window.location.href = `${docType}.html?version=${selectedVersion}`;
        });
    </script>
</body>
</html>

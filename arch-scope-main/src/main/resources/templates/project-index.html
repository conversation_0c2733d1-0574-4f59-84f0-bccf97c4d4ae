<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${project.name} + ' - 项目文档'">项目文档</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 文档卡片样式 */
        .document-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-top: 1.5rem;
        }

        .document-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 1.5rem;
            transition: transform 0.3s, box-shadow 0.3s;
            display: flex;
            flex-direction: column;
        }

        .document-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
        }

        .document-card h3 {
            font-size: 1.25rem;
            margin-bottom: 0.75rem;
            color: var(--secondary-color);
        }

        .document-card p {
            color: #666;
            margin-bottom: 1rem;
            flex-grow: 1;
        }

        .document-card .document-meta {
            font-size: 0.85rem;
            margin-bottom: 1rem;
            display: flex;
            flex-direction: column;
        }

        .document-card .document-meta span {
            margin-bottom: 0.25rem;
        }

        .document-link {
            display: inline-block;
            padding: 0.5rem 1rem;
            background-color: var(--primary-color);
            color: white;
            text-decoration: none;
            border-radius: 0.25rem;
            text-align: center;
            transition: background-color 0.3s;
        }

        .document-link:hover {
            background-color: #2980b9;
        }

        .document-icon {
            font-size: 2rem;
            margin-bottom: 1rem;
            color: var(--primary-color);
        }

        .project-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1rem;
            margin: 2rem 0;
        }

        .stat-card {
            background-color: #f8f9fa;
            border-radius: 0.5rem;
            padding: 1.5rem;
            text-align: center;
        }

        .stat-value {
            font-size: 1.5rem;
            font-weight: bold;
            color: var(--primary-color);
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="/">
                    <img src="/images/logo.png" alt="ArchScope" class="logo-img">
                    <span class="logo-text">ArchScope</span>
                </a>
            </div>
            <nav>
                <ul>
                    <li><a href="/">首页</a></li>
                    <li><a href="/projects">项目列表</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="project-header">
            <h1 th:text="${project.name}" class="text-3xl font-bold mb-2">项目名称</h1>
            <p th:text="${project.description}" class="text-gray-600 mb-4">项目描述</p>
            <div class="project-meta bg-gray-100 p-4 rounded-lg">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                        <span class="repository flex items-center">
                            <i class="fas fa-code-branch mr-2 text-gray-500"></i>
                            <span th:text="'仓库: ' + ${project.repositoryUrl}">仓库: https://github.com/example/repo</span>
                        </span>
                    </div>
                    <div>
                        <span class="branch flex items-center">
                            <i class="fas fa-code mr-2 text-gray-500"></i>
                            <span th:text="'分支: ' + ${project.branch}">分支: main</span>
                        </span>
                    </div>
                    <div>
                        <span class="last-analyzed flex items-center" th:if="${project.lastAnalyzedAt}">
                            <i class="fas fa-clock mr-2 text-gray-500"></i>
                            <span th:text="'最后分析: ' + ${#temporals.format(project.lastAnalyzedAt, 'yyyy-MM-dd HH:mm')}">最后分析: 2023-11-01 12:00</span>
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 项目统计信息 -->
        <div class="project-stats">
            <div class="stat-card">
                <div class="stat-value" th:text="${documentVersions.size()}">文档数量</div>
                <div class="stat-label">文档数量</div>
            </div>
            <div class="stat-card">
                <div class="stat-value">
                    <span class="text-yellow-500">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="far fa-star"></i>
                    </span>
                </div>
                <div class="stat-label">项目评分</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" th:text="${#temporals.format(project.lastAnalyzedAt != null ? project.lastAnalyzedAt : #temporals.createNow(), 'MM-dd')}">更新日期</div>
                <div class="stat-label">最近更新</div>
            </div>
        </div>

        <div class="search-box">
            <a href="search.html" class="search-link">
                <i class="fas fa-search search-icon"></i>
                <span>搜索文档</span>
            </a>
        </div>

        <div class="document-list">
            <h2 class="text-2xl font-bold mb-4">可用文档</h2>
            <div class="document-grid">
                <div class="document-card" th:each="docType : ${T(com.archscope.domain.valueobject.DocumentType).values()}"
                     th:with="doc=${documentVersions.stream().filter(d -> d.docType == docType).findFirst().orElse(null)}">
                    <div class="document-icon">
                        <i th:class="${'fas ' + (docType.name() == 'PRODUCT_INTRO' ? 'fa-home' :
                                        docType.name() == 'ARCHITECTURE' ? 'fa-sitemap' :
                                        docType.name() == 'API' ? 'fa-file-code' :
                                        docType.name() == 'USER_MANUAL' ? 'fa-book-open' :
                                        docType.name() == 'EXTENSION' ? 'fa-puzzle-piece' :
                                        docType.name() == 'LLMS_TXT' ? 'fa-file-alt' : 'fa-file')}"></i>
                    </div>
                    <h3 th:text="${docType.displayName}">文档类型</h3>
                    <p th:text="${doc != null ? doc.description : '暂无文档描述'}">文档描述</p>
                    <div class="document-meta" th:if="${doc != null}">
                        <span class="version" th:text="'版本: ' + ${doc.versionTag}">版本: 1.0.0</span>
                        <span class="last-updated" th:text="'最后更新: ' + ${#temporals.format(doc.lastModified, 'yyyy-MM-dd HH:mm')}">最后更新: 2023-11-01 12:00</span>
                    </div>
                    <a th:href="${docType.name().toLowerCase()} + '.html'" class="document-link">
                        <span th:if="${doc != null}">查看文档</span>
                        <span th:unless="${doc != null}">暂无文档</span>
                    </a>
                </div>
            </div>
        </div>

        <div class="generation-info mt-8 text-gray-500 text-sm text-right">
            <p th:text="'生成时间: ' + ${generatedTime}">生成时间: 2023-11-01 12:00</p>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2023 ArchScope. 保留所有权利。</p>
        </div>
    </footer>
</body>
</html>

<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${project.name} + ' - 文档版本比较'">文档版本比较</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 比较页面特定样式 */
        .compare-container {
            display: flex;
            flex-direction: column;
            gap: 2rem;
        }
        
        .compare-header {
            background-color: #f8f9fa;
            padding: 1.5rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .compare-header h1 {
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }
        
        .compare-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
        }
        
        .compare-select-group {
            display: flex;
            flex-direction: column;
            flex: 1;
            min-width: 200px;
        }
        
        .compare-select-label {
            font-size: 0.85rem;
            margin-bottom: 0.25rem;
            color: #666;
        }
        
        .compare-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 0.25rem;
        }
        
        .compare-button {
            padding: 0.5rem 1rem;
            background-color: var(--primary-color);
            color: white;
            border: none;
            border-radius: 0.25rem;
            cursor: pointer;
            transition: background-color 0.3s;
            align-self: flex-end;
            margin-top: auto;
        }
        
        .compare-button:hover {
            background-color: #2980b9;
        }
        
        .compare-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 1rem;
        }
        
        .compare-column {
            border: 1px solid #ddd;
            border-radius: 0.5rem;
            overflow: hidden;
        }
        
        .compare-column-header {
            background-color: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #ddd;
            font-weight: bold;
            color: var(--secondary-color);
        }
        
        .compare-column-content {
            padding: 1rem;
            overflow-y: auto;
            max-height: 600px;
        }
        
        .diff-added {
            background-color: #e6ffed;
            border-left: 3px solid #28a745;
            padding-left: 0.5rem;
        }
        
        .diff-removed {
            background-color: #ffeef0;
            border-left: 3px solid #d73a49;
            padding-left: 0.5rem;
            text-decoration: line-through;
            color: #666;
        }
        
        .diff-unchanged {
            padding-left: 0.5rem;
        }
        
        /* 响应式调整 */
        @media (max-width: 768px) {
            .compare-content {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="/">
                    <img src="/images/logo.png" alt="ArchScope" class="logo-img">
                    <span class="logo-text">ArchScope</span>
                </a>
            </div>
            <nav>
                <ul>
                    <li><a href="/">首页</a></li>
                    <li><a href="/projects">项目列表</a></li>
                    <li><a href="index.html">项目首页</a></li>
                </ul>
            </nav>
        </div>
    </header>
    
    <main class="container">
        <div class="compare-container">
            <div class="compare-header">
                <h1 th:text="${project.name} + ' - 文档版本比较'" class="text-3xl font-bold">文档版本比较</h1>
                <p>比较不同版本的文档内容，查看变更历史。</p>
            </div>
            
            <div class="compare-controls">
                <div class="compare-select-group">
                    <label for="doc-type-select" class="compare-select-label">文档类型</label>
                    <select id="doc-type-select" class="compare-select">
                        <option th:each="docType : ${T(com.archscope.domain.valueobject.DocumentType).values()}" 
                                th:value="${docType.name().toLowerCase()}" 
                                th:text="${docType.displayName}"
                                th:selected="${docType.name().toLowerCase() == selectedDocType}">文档类型</option>
                    </select>
                </div>
                
                <div class="compare-select-group">
                    <label for="old-version-select" class="compare-select-label">旧版本</label>
                    <select id="old-version-select" class="compare-select">
                        <option th:each="ver : ${versions}" 
                                th:value="${ver.versionTag}" 
                                th:text="${ver.versionTag}"
                                th:selected="${ver.versionTag == oldVersion}">版本号</option>
                    </select>
                </div>
                
                <div class="compare-select-group">
                    <label for="new-version-select" class="compare-select-label">新版本</label>
                    <select id="new-version-select" class="compare-select">
                        <option th:each="ver : ${versions}" 
                                th:value="${ver.versionTag}" 
                                th:text="${ver.versionTag}"
                                th:selected="${ver.versionTag == newVersion}">版本号</option>
                    </select>
                </div>
                
                <button id="compare-button" class="compare-button">
                    <i class="fas fa-code-compare mr-2"></i>比较
                </button>
            </div>
            
            <div class="compare-content">
                <div class="compare-column">
                    <div class="compare-column-header" th:text="'旧版本: ' + ${oldVersion}">旧版本: v1.0.0</div>
                    <div class="compare-column-content document-content" th:utext="${oldContent}">
                        <!-- 旧版本内容 -->
                    </div>
                </div>
                
                <div class="compare-column">
                    <div class="compare-column-header" th:text="'新版本: ' + ${newVersion}">新版本: v1.1.0</div>
                    <div class="compare-column-content document-content" th:utext="${newContent}">
                        <!-- 新版本内容 -->
                    </div>
                </div>
            </div>
        </div>
    </main>
    
    <footer>
        <div class="container">
            <p>&copy; 2023 ArchScope. 保留所有权利。</p>
        </div>
    </footer>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const docTypeSelect = document.getElementById('doc-type-select');
            const oldVersionSelect = document.getElementById('old-version-select');
            const newVersionSelect = document.getElementById('new-version-select');
            const compareButton = document.getElementById('compare-button');
            
            compareButton.addEventListener('click', function() {
                const docType = docTypeSelect.value;
                const oldVersion = oldVersionSelect.value;
                const newVersion = newVersionSelect.value;
                
                if (oldVersion === newVersion) {
                    alert('请选择不同的版本进行比较');
                    return;
                }
                
                window.location.href = `compare.html?docType=${docType}&oldVersion=${oldVersion}&newVersion=${newVersion}`;
            });
        });
    </script>
</body>
</html>

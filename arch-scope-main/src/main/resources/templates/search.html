<!DOCTYPE html>
<html xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title th:text="${project.name} + ' - 文档搜索'">文档搜索</title>
    <link rel="stylesheet" href="/css/style.css">
    <link rel="stylesheet" href="/css/search.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 搜索页面特定样式 */
        .search-header {
            background-color: #f8f9fa;
            padding: 2rem;
            border-radius: 0.5rem;
            margin-bottom: 2rem;
            text-align: center;
        }

        .search-header h1 {
            margin-bottom: 1rem;
            color: var(--secondary-color);
        }

        .search-header p {
            color: #666;
            max-width: 600px;
            margin: 0 auto;
        }

        .search-filters {
            display: flex;
            flex-wrap: wrap;
            gap: 1rem;
            margin-bottom: 1.5rem;
            padding: 1rem;
            background-color: #f8f9fa;
            border-radius: 0.5rem;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-label {
            font-size: 0.85rem;
            margin-bottom: 0.25rem;
            color: #666;
        }

        .filter-select {
            padding: 0.5rem;
            border: 1px solid #ddd;
            border-radius: 0.25rem;
            min-width: 150px;
        }

        .search-tips {
            margin-top: 2rem;
            padding: 1rem;
            background-color: #e8f4fd;
            border-left: 4px solid var(--primary-color);
            border-radius: 0.25rem;
        }

        .search-tips h3 {
            margin-bottom: 0.5rem;
            color: var(--primary-color);
            font-size: 1.1rem;
        }

        .search-tips ul {
            margin-left: 1.5rem;
            margin-bottom: 0;
        }

        .search-tips li {
            margin-bottom: 0.25rem;
            font-size: 0.9rem;
        }

        .search-shortcut {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            background-color: #f1f1f1;
            border-radius: 0.25rem;
            font-family: monospace;
            margin: 0 0.25rem;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .search-filters {
                flex-direction: column;
            }

            .filter-group {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="container">
            <div class="logo">
                <a href="/">
                    <img src="/images/logo.png" alt="ArchScope" class="logo-img">
                    <span class="logo-text">ArchScope</span>
                </a>
            </div>
            <nav>
                <ul>
                    <li><a href="/">首页</a></li>
                    <li><a href="/projects">项目列表</a></li>
                    <li><a href="index.html">项目首页</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <main class="container">
        <div class="search-header">
            <h1 th:text="${project.name} + ' - 文档搜索'" class="text-3xl font-bold">项目名称 - 文档搜索</h1>
            <p>在项目文档中搜索关键词、概念或代码示例，快速找到您需要的信息。</p>
        </div>

        <div class="search-container">
            <div class="search-form">
                <input type="text" id="search-input" placeholder="输入关键词搜索文档..." autocomplete="off">
                <button id="search-button">
                    <i class="fas fa-search mr-2"></i>搜索
                </button>
            </div>

            <div class="search-filters">
                <div class="filter-group">
                    <label for="doc-type-filter" class="filter-label">文档类型</label>
                    <select id="doc-type-filter" class="filter-select">
                        <option value="all">所有类型</option>
                        <option th:each="docType : ${T(com.archscope.domain.valueobject.DocumentType).values()}"
                                th:value="${docType.name()}"
                                th:text="${docType.displayName}">文档类型</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label for="version-filter" class="filter-label">文档版本</label>
                    <select id="version-filter" class="filter-select">
                        <option value="all">所有版本</option>
                        <option value="latest">最新版本</option>
                    </select>
                </div>
            </div>

            <div id="search-results" class="search-results">
                <!-- 搜索结果将在这里显示 -->
                <p class="search-info">请输入关键词进行搜索</p>
            </div>

            <div class="search-tips">
                <h3><i class="fas fa-lightbulb mr-2"></i>搜索技巧</h3>
                <ul>
                    <li>使用引号搜索精确短语，如 <span class="search-shortcut">"架构设计"</span></li>
                    <li>使用 <span class="search-shortcut">+</span> 表示必须包含的词，如 <span class="search-shortcut">+接口 +文档</span></li>
                    <li>使用 <span class="search-shortcut">-</span> 排除特定词，如 <span class="search-shortcut">架构 -数据库</span></li>
                    <li>使用通配符 <span class="search-shortcut">*</span> 进行前缀搜索，如 <span class="search-shortcut">api*</span></li>
                </ul>
            </div>
        </div>

        <div class="generation-info mt-8 text-gray-500 text-sm text-right">
            <p th:text="'生成时间: ' + ${generatedTime}">生成时间: 2023-11-01 12:00</p>
        </div>
    </main>

    <footer>
        <div class="container">
            <p>&copy; 2023 ArchScope. 保留所有权利。</p>
        </div>
    </footer>

    <script src="/js/lunr.min.js"></script>
    <script src="/js/search.js"></script>
    <script>
        // 添加过滤功能
        document.addEventListener('DOMContentLoaded', function() {
            const docTypeFilter = document.getElementById('doc-type-filter');
            const versionFilter = document.getElementById('version-filter');

            // 当过滤器变化时重新搜索
            docTypeFilter.addEventListener('change', performFilteredSearch);
            versionFilter.addEventListener('change', performFilteredSearch);

            function performFilteredSearch() {
                // 触发搜索按钮的点击事件
                const searchButton = document.getElementById('search-button');
                if (searchButton && document.getElementById('search-input').value.trim()) {
                    searchButton.click();
                }
            }
        });
    </script>
</body>
</html>

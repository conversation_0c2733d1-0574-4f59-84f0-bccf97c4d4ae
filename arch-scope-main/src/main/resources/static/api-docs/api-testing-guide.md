# 服务发现系统 API 测试指南

## 概述

本指南提供了服务发现系统API的详细测试方法和示例，帮助开发者快速验证API功能和集成测试。

## 测试环境

### 本地开发环境
- **基础URL**: `http://localhost:8080/api/v1`
- **Swagger UI**: `http://localhost:8080/swagger-ui/index.html`
- **API文档**: `http://localhost:8080/v3/api-docs`

### 测试环境
- **基础URL**: `https://test-api.archscope.com/api/v1`
- **Swagger UI**: `https://test-api.archscope.com/swagger-ui/index.html`

## 认证设置

### 获取JWT Token（示例）
```bash
curl -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "testuser",
    "password": "testpass"
  }'
```

### 使用API Key（示例）
```bash
# 在请求头中添加API Key
X-API-Key: your-api-key-here
```

## API测试用例

### 1. 服务注册管理测试

#### 1.1 注册服务 - 成功案例

**请求**:
```bash
curl -X POST http://localhost:8080/api/v1/services \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "测试用户服务",
    "description": "用于API测试的用户管理服务",
    "type": "REST_API",
    "version": "1.0.0",
    "endpoint": "https://test-api.example.com/user-service",
    "groupId": "com.test",
    "artifactId": "test-user-service",
    "tags": ["test", "user-management"],
    "status": "ACTIVE",
    "metadata": {
      "environment": "test",
      "maintainer": "<EMAIL>"
    }
  }'
```

**期望响应**:
```json
{
  "success": true,
  "message": "服务注册成功",
  "data": {
    "id": "srv-test-001",
    "name": "测试用户服务",
    "type": "REST_API",
    "version": "1.0.0",
    "status": "ACTIVE",
    "registeredAt": "2024-01-20T15:30:00Z"
  }
}
```

#### 1.2 注册服务 - 参数错误案例

**请求**:
```bash
curl -X POST http://localhost:8080/api/v1/services \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "",
    "type": "INVALID_TYPE",
    "version": "invalid-version"
  }'
```

**期望响应**:
```json
{
  "success": false,
  "message": "请求参数错误",
  "error": "服务名称不能为空，服务类型不正确，版本号格式不正确"
}
```

#### 1.3 获取服务详情

**请求**:
```bash
curl -X GET http://localhost:8080/api/v1/services/srv-test-001 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 1.4 更新服务信息

**请求**:
```bash
curl -X PUT http://localhost:8080/api/v1/services/srv-test-001 \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "description": "更新后的服务描述",
    "status": "MAINTENANCE",
    "metadata": {
      "environment": "test",
      "maintainer": "<EMAIL>",
      "lastUpdate": "2024-01-20"
    }
  }'
```

#### 1.5 注销服务

**请求**:
```bash
curl -X DELETE http://localhost:8080/api/v1/services/srv-test-001 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 2. 服务发现测试

#### 2.1 查找所有活跃服务

**请求**:
```bash
curl -X GET http://localhost:8080/api/v1/discovery/active \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 2.2 分页查询服务

**请求**:
```bash
curl -X GET "http://localhost:8080/api/v1/discovery?page=0&size=5&sortBy=name&sortDirection=ASC" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 2.3 按名称搜索服务

**请求**:
```bash
curl -X GET "http://localhost:8080/api/v1/discovery/search/name?name=用户" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 2.4 按类型查找服务

**请求**:
```bash
curl -X GET "http://localhost:8080/api/v1/discovery/search/type?type=REST_API" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 2.5 按标签查找服务

**请求**:
```bash
curl -X GET "http://localhost:8080/api/v1/discovery/search/tags?tags=authentication,user-management" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 2.6 按Maven坐标查找服务

**请求**:
```bash
curl -X GET "http://localhost:8080/api/v1/discovery/search/maven?groupId=com.example&artifactId=user-service" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 2.7 组合条件查询

**请求**:
```bash
curl -X GET "http://localhost:8080/api/v1/discovery/search?name=用户&type=REST_API&status=ACTIVE&page=0&size=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 3. 能力管理测试

#### 3.1 注册服务能力

**请求**:
```bash
curl -X POST http://localhost:8080/api/v1/capabilities \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "测试用户认证",
    "description": "用于测试的用户认证能力",
    "version": "1.0.0",
    "examples": [
      {
        "name": "登录测试",
        "description": "测试用户登录功能",
        "requestExample": "{\"username\": \"testuser\", \"password\": \"testpass\"}",
        "responseExample": "{\"token\": \"test-jwt-token\", \"userId\": \"test-123\"}"
      }
    ],
    "tags": ["test", "authentication"]
  }'
```

#### 3.2 查询服务能力

**请求**:
```bash
curl -X GET "http://localhost:8080/api/v1/capabilities/search?keyword=认证&page=0&size=10" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 3.3 获取服务的所有能力

**请求**:
```bash
curl -X GET http://localhost:8080/api/v1/capabilities/by-service/srv-test-001 \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 4. 需求匹配测试

#### 4.1 根据需求匹配服务

**请求**:
```bash
curl -X POST http://localhost:8080/api/v1/requirements/match \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "description": "我需要一个测试用的用户管理系统，支持基本的登录认证功能",
    "requiredCapabilities": ["用户认证", "用户管理"],
    "priority": "MEDIUM",
    "tags": ["test", "user-management"]
  }'
```

**期望响应**:
```json
{
  "success": true,
  "message": "需求匹配成功",
  "data": [
    {
      "serviceId": "srv-test-001",
      "serviceName": "测试用户服务",
      "matchScore": 0.85,
      "matchReason": "匹配用户认证需求",
      "recommendationId": "rec-test-001",
      "capabilities": ["测试用户认证"],
      "confidence": "MEDIUM"
    }
  ]
}
```

#### 4.2 生成能力需求

**请求**:
```bash
curl -X POST "http://localhost:8080/api/v1/requirements/generate-capabilities?description=我需要构建一个简单的博客系统" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

#### 4.3 记录推荐反馈

**请求**:
```bash
curl -X POST http://localhost:8080/api/v1/requirements/feedback \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "recommendationId": "rec-test-001",
    "rating": 4,
    "feedback": "推荐的服务基本符合需求，但文档可以更详细",
    "accepted": true
  }'
```

## 自动化测试脚本

### Postman Collection

创建Postman集合进行API测试：

```json
{
  "info": {
    "name": "ArchScope Service Discovery API",
    "description": "服务发现系统API测试集合"
  },
  "auth": {
    "type": "bearer",
    "bearer": [
      {
        "key": "token",
        "value": "{{jwt_token}}",
        "type": "string"
      }
    ]
  },
  "variable": [
    {
      "key": "base_url",
      "value": "http://localhost:8080/api/v1"
    },
    {
      "key": "jwt_token",
      "value": "your-jwt-token-here"
    }
  ]
}
```

### Python测试脚本

```python
import requests
import json
import pytest

class TestServiceDiscoveryAPI:
    BASE_URL = "http://localhost:8080/api/v1"
    JWT_TOKEN = "your-jwt-token-here"
    
    @property
    def headers(self):
        return {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.JWT_TOKEN}"
        }
    
    def test_register_service(self):
        """测试服务注册"""
        payload = {
            "name": "Python测试服务",
            "description": "使用Python测试的服务",
            "type": "REST_API",
            "version": "1.0.0",
            "endpoint": "https://python-test.example.com",
            "tags": ["python", "test"]
        }
        
        response = requests.post(
            f"{self.BASE_URL}/services",
            headers=self.headers,
            json=payload
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "srv-" in data["data"]["id"]
        
        return data["data"]["id"]
    
    def test_get_service(self):
        """测试获取服务详情"""
        service_id = self.test_register_service()
        
        response = requests.get(
            f"{self.BASE_URL}/services/{service_id}",
            headers=self.headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["data"]["id"] == service_id
    
    def test_search_services(self):
        """测试服务搜索"""
        response = requests.get(
            f"{self.BASE_URL}/discovery/search?name=Python",
            headers=self.headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert isinstance(data["data"]["content"], list)

if __name__ == "__main__":
    pytest.main([__file__])
```

### JavaScript/Node.js测试脚本

```javascript
const axios = require('axios');

class ServiceDiscoveryAPITest {
    constructor() {
        this.baseURL = 'http://localhost:8080/api/v1';
        this.jwtToken = 'your-jwt-token-here';
        this.headers = {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${this.jwtToken}`
        };
    }

    async registerService() {
        const payload = {
            name: 'JavaScript测试服务',
            description: '使用JavaScript测试的服务',
            type: 'REST_API',
            version: '1.0.0',
            endpoint: 'https://js-test.example.com',
            tags: ['javascript', 'test']
        };

        try {
            const response = await axios.post(
                `${this.baseURL}/services`,
                payload,
                { headers: this.headers }
            );

            console.log('服务注册成功:', response.data);
            return response.data.data.id;
        } catch (error) {
            console.error('服务注册失败:', error.response?.data || error.message);
            throw error;
        }
    }

    async searchServices(keyword) {
        try {
            const response = await axios.get(
                `${this.baseURL}/discovery/search?name=${keyword}`,
                { headers: this.headers }
            );

            console.log('服务搜索结果:', response.data);
            return response.data.data;
        } catch (error) {
            console.error('服务搜索失败:', error.response?.data || error.message);
            throw error;
        }
    }

    async runTests() {
        console.log('开始API测试...');
        
        try {
            // 测试服务注册
            const serviceId = await this.registerService();
            console.log(`✓ 服务注册测试通过，服务ID: ${serviceId}`);

            // 测试服务搜索
            const searchResults = await this.searchServices('JavaScript');
            console.log(`✓ 服务搜索测试通过，找到 ${searchResults.content.length} 个结果`);

            console.log('所有测试通过！');
        } catch (error) {
            console.error('测试失败:', error.message);
        }
    }
}

// 运行测试
const tester = new ServiceDiscoveryAPITest();
tester.runTests();
```

## 性能测试

### 使用Apache Bench (ab)

```bash
# 测试服务发现接口的并发性能
ab -n 1000 -c 10 -H "Authorization: Bearer YOUR_JWT_TOKEN" \
   "http://localhost:8080/api/v1/discovery/active"

# 测试服务搜索接口的性能
ab -n 500 -c 5 -H "Authorization: Bearer YOUR_JWT_TOKEN" \
   "http://localhost:8080/api/v1/discovery/search?name=test"
```

### 使用wrk

```bash
# 安装wrk后运行性能测试
wrk -t12 -c400 -d30s -H "Authorization: Bearer YOUR_JWT_TOKEN" \
    http://localhost:8080/api/v1/discovery/active
```

## 错误处理测试

### 测试各种错误场景

1. **无效认证**
```bash
curl -X GET http://localhost:8080/api/v1/discovery/active
# 期望: 401 Unauthorized
```

2. **无效参数**
```bash
curl -X GET "http://localhost:8080/api/v1/discovery?page=-1&size=0"
# 期望: 400 Bad Request
```

3. **资源不存在**
```bash
curl -X GET http://localhost:8080/api/v1/services/non-existent-id \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
# 期望: 404 Not Found
```

4. **服务冲突**
```bash
# 注册相同名称和版本的服务两次
# 期望: 409 Conflict
```

## 集成测试建议

1. **测试数据准备**: 在测试前准备好测试数据，测试后清理
2. **环境隔离**: 使用独立的测试环境和数据库
3. **并发测试**: 测试API在高并发情况下的表现
4. **边界测试**: 测试参数的边界值和极限情况
5. **安全测试**: 测试认证、授权和输入验证
6. **性能测试**: 测试响应时间和吞吐量

## 常见问题排查

### 1. 认证失败
- 检查JWT token是否有效
- 确认API Key是否正确
- 验证请求头格式

### 2. 参数验证错误
- 检查请求参数格式
- 确认必填字段是否提供
- 验证参数值是否在允许范围内

### 3. 服务不可用
- 检查服务是否启动
- 确认网络连接
- 查看服务日志

### 4. 性能问题
- 检查数据库连接
- 确认缓存是否正常工作
- 监控系统资源使用情况

## 测试报告模板

```markdown
# API测试报告

## 测试概述
- 测试时间: 2024-01-20
- 测试环境: 本地开发环境
- 测试人员: 测试团队

## 测试结果
- 总测试用例: 50
- 通过: 48
- 失败: 2
- 跳过: 0

## 失败用例
1. 需求匹配超时 - 需要优化算法性能
2. 并发注册服务冲突 - 需要改进锁机制

## 性能测试结果
- 平均响应时间: 120ms
- 95%响应时间: 200ms
- 最大并发: 500 QPS

## 建议
1. 优化需求匹配算法
2. 改进并发控制机制
3. 增加缓存策略
```

这个测试指南提供了全面的API测试方法和工具，帮助确保服务发现系统的质量和稳定性。
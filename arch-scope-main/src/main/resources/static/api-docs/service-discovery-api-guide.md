# 服务发现系统 API 使用指南

## 概述

服务发现系统提供了完整的服务注册、发现、能力管理和需求匹配功能。本指南详细介绍了如何使用这些API。

## 基础信息

- **基础URL**: `http://localhost:8080/api/v1`
- **API文档**: `http://localhost:8080/swagger-ui/index.html`
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 认证

目前系统支持以下认证方式：

1. **JWT Bearer Token**
   ```
   Authorization: Bearer <your-jwt-token>
   ```

2. **API Key**
   ```
   X-API-Key: <your-api-key>
   ```

## API 模块

### 1. 服务注册管理 (`/services`)

#### 1.1 注册服务

**端点**: `POST /services`

**描述**: 注册一个新的服务到服务注册中心

**请求示例**:
```json
{
    "name": "用户管理服务",
    "description": "提供用户注册、登录、权限管理等功能的微服务",
    "type": "REST_API",
    "version": "1.0.0",
    "endpoint": "https://api.example.com/user-service",
    "groupId": "com.example",
    "artifactId": "user-service",
    "tags": ["authentication", "user-management", "microservice"],
    "status": "ACTIVE",
    "metadata": {
        "environment": "production",
        "region": "us-east-1",
        "maintainer": "<EMAIL>"
    }
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "服务注册成功",
    "data": {
        "id": "srv-001",
        "name": "用户管理服务",
        "description": "提供用户注册、登录、权限管理等功能",
        "type": "REST_API",
        "version": "1.0.0",
        "endpoint": "https://api.example.com/user-service",
        "groupId": "com.example",
        "artifactId": "user-service",
        "status": "ACTIVE",
        "registeredAt": "2024-01-20T15:30:00Z"
    },
    "timestamp": "2024-01-20T15:30:00Z"
}
```

#### 1.2 更新服务

**端点**: `PUT /services/{serviceId}`

**描述**: 更新已注册服务的信息

#### 1.3 注销服务

**端点**: `DELETE /services/{serviceId}`

**描述**: 从服务注册中心注销指定服务

#### 1.4 获取服务详情

**端点**: `GET /services/{serviceId}`

**描述**: 根据服务ID获取服务的详细信息

### 2. 服务发现 (`/discovery`)

#### 2.1 查找所有活跃服务

**端点**: `GET /discovery/active`

**描述**: 获取系统中所有状态为活跃的服务列表

#### 2.2 分页查询服务

**端点**: `GET /discovery`

**参数**:
- `page`: 页码（从0开始，默认0）
- `size`: 每页大小（1-100，默认10）
- `sortBy`: 排序字段（默认registeredAt）
- `sortDirection`: 排序方向（ASC/DESC，默认DESC）

**示例**: `GET /discovery?page=0&size=10&sortBy=name&sortDirection=ASC`

#### 2.3 按条件搜索服务

**端点**: `GET /discovery/search`

**参数**:
- `name`: 服务名称（支持模糊匹配）
- `type`: 服务类型
- `status`: 服务状态
- `tags`: 标签列表
- `groupId`: Maven坐标groupId
- `artifactId`: Maven坐标artifactId
- `capabilityName`: 能力名称

**示例**: `GET /discovery/search?name=用户&type=REST_API&status=ACTIVE`

#### 2.4 按Maven坐标查找服务

**端点**: `GET /discovery/search/maven`

**参数**:
- `groupId`: Maven/Gradle坐标的groupId
- `artifactId`: Maven/Gradle坐标的artifactId

**示例**: `GET /discovery/search/maven?groupId=com.example&artifactId=user-service`

#### 2.5 精确Maven坐标查找

**端点**: `GET /discovery/search/maven/exact`

**参数**:
- `groupId`: Maven/Gradle坐标的groupId
- `artifactId`: Maven/Gradle坐标的artifactId
- `version`: 服务版本

**示例**: `GET /discovery/search/maven/exact?groupId=com.example&artifactId=user-service&version=1.0.0`

### 3. 能力管理 (`/capabilities`)

#### 3.1 注册服务能力

**端点**: `POST /capabilities`

**描述**: 为服务注册新的能力

**请求示例**:
```json
{
    "name": "用户认证",
    "description": "提供用户登录认证功能，支持用户名密码和第三方OAuth认证",
    "version": "1.0.0",
    "examples": [
        {
            "name": "用户名密码登录",
            "description": "使用用户名和密码进行登录认证",
            "requestExample": "{\"username\": \"john\", \"password\": \"secret\"}",
            "responseExample": "{\"token\": \"jwt-token\", \"userId\": \"123\", \"expiresIn\": 3600}"
        }
    ],
    "tags": ["authentication", "security", "login"]
}
```

#### 3.2 查询服务能力

**端点**: `GET /capabilities/search`

**参数**:
- `keyword`: 搜索关键词
- `page`: 页码
- `size`: 每页大小

#### 3.3 获取服务的所有能力

**端点**: `GET /capabilities/by-service/{serviceId}`

**描述**: 根据服务ID获取该服务的所有能力

### 4. 需求匹配 (`/requirements`)

#### 4.1 根据需求匹配服务

**端点**: `POST /requirements/match`

**描述**: 根据需求描述和能力要求匹配合适的服务

**请求示例**:
```json
{
    "description": "我需要一个用户管理系统，支持用户注册、登录、权限管理等功能",
    "requiredCapabilities": ["用户认证", "权限管理", "用户注册"],
    "priority": "HIGH",
    "tags": ["authentication", "user-management"]
}
```

**响应示例**:
```json
{
    "success": true,
    "message": "需求匹配成功",
    "data": [
        {
            "serviceId": "srv-001",
            "serviceName": "用户管理服务",
            "matchScore": 0.95,
            "matchReason": "完全匹配用户认证和权限管理需求",
            "recommendationId": "rec-001",
            "capabilities": ["用户认证", "权限管理"],
            "confidence": "HIGH"
        }
    ]
}
```

#### 4.2 生成能力需求

**端点**: `POST /requirements/generate-capabilities`

**参数**:
- `description`: 需求描述

**描述**: 根据需求描述自动生成能力需求列表

#### 4.3 记录推荐反馈

**端点**: `POST /requirements/feedback`

**描述**: 记录用户对推荐结果的反馈

**请求示例**:
```json
{
    "recommendationId": "rec-001",
    "rating": 5,
    "feedback": "推荐的服务完全符合我的需求",
    "accepted": true
}
```

## 错误处理

### 标准错误响应格式

```json
{
    "success": false,
    "message": "错误描述",
    "error": "详细错误信息",
    "timestamp": "2024-01-20T15:30:00Z"
}
```

### 常见错误码

- **400 Bad Request**: 请求参数错误
- **401 Unauthorized**: 未授权访问
- **403 Forbidden**: 权限不足
- **404 Not Found**: 资源不存在
- **409 Conflict**: 资源冲突（如重复注册）
- **500 Internal Server Error**: 服务器内部错误

## 最佳实践

### 1. 服务注册

- 使用语义化版本号（如1.0.0）
- 提供详细的服务描述
- 合理使用标签进行分类
- 设置适当的元数据

### 2. 服务发现

- 使用分页查询避免大量数据传输
- 合理使用过滤条件缩小搜索范围
- 利用缓存提高查询性能

### 3. 能力管理

- 提供详细的能力描述和示例
- 使用版本控制管理能力变更
- 及时标记废弃的能力

### 4. 需求匹配

- 提供清晰的需求描述
- 合理设置需求优先级
- 及时提供反馈改进匹配算法

## 示例代码

### Java 客户端示例

```java
// 注册服务
ServiceRegistrationCommand command = ServiceRegistrationCommand.builder()
    .name("用户管理服务")
    .description("提供用户注册、登录、权限管理等功能")
    .type("REST_API")
    .version("1.0.0")
    .endpoint("https://api.example.com/user-service")
    .build();

RestTemplate restTemplate = new RestTemplate();
ResponseEntity<ApiResponse<ServiceDTO>> response = restTemplate.postForEntity(
    "http://localhost:8080/api/v1/services", 
    command, 
    new ParameterizedTypeReference<ApiResponse<ServiceDTO>>() {}
);
```

### JavaScript 客户端示例

```javascript
// 查询服务
const response = await fetch('/api/v1/discovery/search?name=用户&type=REST_API', {
    method: 'GET',
    headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ' + token
    }
});

const result = await response.json();
if (result.success) {
    console.log('找到服务:', result.data);
}
```

### cURL 示例

```bash
# 注册服务
curl -X POST http://localhost:8080/api/v1/services \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "name": "用户管理服务",
    "description": "提供用户注册、登录、权限管理等功能",
    "type": "REST_API",
    "version": "1.0.0",
    "endpoint": "https://api.example.com/user-service"
  }'

# 查询服务
curl -X GET "http://localhost:8080/api/v1/discovery/search?name=用户&type=REST_API" \
  -H "Authorization: Bearer your-token"
```

## 常见问题

### Q: 如何处理服务版本升级？

A: 注册新版本的服务，逐步迁移流量，然后注销旧版本。

### Q: 如何提高服务发现的性能？

A: 使用适当的查询条件、启用缓存、合理设置分页大小。

### Q: 如何处理服务下线？

A: 调用注销服务API或将服务状态设置为INACTIVE。

### Q: 需求匹配的准确性如何？

A: 系统会根据能力匹配度、用户反馈等因素不断优化匹配算法。

## 更新日志

- **v1.0.0** (2024-01-20): 初始版本发布
  - 基础服务注册和发现功能
  - 能力管理功能
  - 需求匹配功能
  - 完整的API文档

## 联系我们

如有问题或建议，请联系：
- 邮箱: <EMAIL>
- 文档: https://docs.archscope.com
- GitHub: https://github.com/archscope/service-discovery
# ArchScope 服务发现系统 API 文档

## 文档概述

本目录包含了ArchScope服务发现系统的完整API文档，提供了详细的接口说明、使用示例和测试指南。

## 文档结构

```
api-docs/
├── README.md                           # 本文件，文档说明
├── service-discovery-api-guide.md      # API使用指南
├── service-discovery-openapi.yaml      # OpenAPI 3.0规范文件
└── api-testing-guide.md               # API测试指南
```

## 快速开始

### 1. 访问在线文档

启动应用后，可以通过以下地址访问API文档：

- **Swagger UI**: http://localhost:8080/swagger-ui/index.html
- **OpenAPI JSON**: http://localhost:8080/v3/api-docs
- **OpenAPI YAML**: http://localhost:8080/v3/api-docs.yaml

### 2. API分组

API文档按功能模块分组：

- **服务发现系统**: 服务注册、发现、能力管理、需求匹配
- **项目管理**: 项目注册、配置管理、状态跟踪
- **Git仓库管理**: 仓库验证、信息获取、元数据提取
- **系统管理**: 系统状态、配置管理、监控

### 3. 认证方式

系统支持两种认证方式：

#### JWT Bearer Token
```bash
Authorization: Bearer <your-jwt-token>
```

#### API Key
```bash
X-API-Key: <your-api-key>
```

## 主要功能模块

### 服务注册管理 (`/api/v1/services`)

管理服务的生命周期，包括注册、更新、注销和查询。

**核心接口**:
- `POST /services` - 注册服务
- `GET /services/{id}` - 获取服务详情
- `PUT /services/{id}` - 更新服务信息
- `DELETE /services/{id}` - 注销服务

### 服务发现 (`/api/v1/discovery`)

提供多种方式查询和发现服务。

**核心接口**:
- `GET /discovery/active` - 获取所有活跃服务
- `GET /discovery` - 分页查询服务
- `GET /discovery/search` - 组合条件查询
- `GET /discovery/search/maven` - 按Maven坐标查找

### 能力管理 (`/api/v1/capabilities`)

管理服务提供的具体能力和功能。

**核心接口**:
- `POST /capabilities` - 注册服务能力
- `GET /capabilities/search` - 搜索能力
- `GET /capabilities/by-service/{id}` - 获取服务的所有能力
- `DELETE /capabilities/{id}` - 删除能力

### 需求匹配 (`/api/v1/requirements`)

基于需求智能匹配合适的服务。

**核心接口**:
- `POST /requirements/match` - 根据需求匹配服务
- `POST /requirements/generate-capabilities` - 生成能力需求
- `POST /requirements/feedback` - 记录推荐反馈

## 使用示例

### 注册服务示例

```bash
curl -X POST http://localhost:8080/api/v1/services \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "用户管理服务",
    "description": "提供用户注册、登录、权限管理等功能",
    "type": "REST_API",
    "version": "1.0.0",
    "endpoint": "https://api.example.com/user-service",
    "groupId": "com.example",
    "artifactId": "user-service",
    "tags": ["authentication", "user-management"],
    "status": "ACTIVE"
  }'
```

### 搜索服务示例

```bash
curl -X GET "http://localhost:8080/api/v1/discovery/search?name=用户&type=REST_API" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

### 需求匹配示例

```bash
curl -X POST http://localhost:8080/api/v1/requirements/match \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "description": "我需要一个用户管理系统，支持用户注册、登录、权限管理等功能",
    "requiredCapabilities": ["用户认证", "权限管理", "用户注册"],
    "priority": "HIGH"
  }'
```

## 错误处理

API使用统一的错误响应格式：

```json
{
  "success": false,
  "message": "错误描述",
  "error": "详细错误信息",
  "timestamp": "2024-01-20T15:30:00Z"
}
```

常见HTTP状态码：
- `200` - 成功
- `201` - 创建成功
- `400` - 请求参数错误
- `401` - 未授权
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `500` - 服务器内部错误

## 开发工具

### 1. 代码生成

使用OpenAPI规范文件生成客户端代码：

```bash
# 生成Java客户端
openapi-generator-cli generate \
  -i http://localhost:8080/v3/api-docs \
  -g java \
  -o ./generated-client-java

# 生成Python客户端
openapi-generator-cli generate \
  -i http://localhost:8080/v3/api-docs \
  -g python \
  -o ./generated-client-python
```

### 2. Postman集合

导入OpenAPI规范到Postman：
1. 打开Postman
2. 点击Import
3. 输入URL: `http://localhost:8080/v3/api-docs`
4. 选择生成集合

### 3. 测试工具

推荐的API测试工具：
- **Postman** - 图形化API测试
- **Insomnia** - 轻量级API客户端
- **curl** - 命令行工具
- **HTTPie** - 用户友好的命令行工具

## 性能优化

### 缓存策略

系统实现了多层缓存：
- **Redis缓存**: 热点数据缓存
- **本地缓存**: 频繁访问的配置数据
- **HTTP缓存**: 静态资源和不变数据

### 分页查询

所有列表查询都支持分页：
- `page`: 页码（从0开始）
- `size`: 每页大小（最大100）
- `sort`: 排序字段和方向

### 批量操作

支持批量操作以提高效率：
- 批量注册服务
- 批量查询服务状态
- 批量更新服务信息

## 监控和日志

### API监控

系统提供了完整的API监控：
- 请求响应时间
- 错误率统计
- 并发量监控
- 资源使用情况

### 日志记录

详细的日志记录包括：
- 请求日志（请求参数、响应结果）
- 错误日志（异常堆栈、错误上下文）
- 性能日志（执行时间、资源消耗）
- 审计日志（用户操作、数据变更）

## 版本管理

### API版本策略

- **URL版本控制**: `/api/v1/`, `/api/v2/`
- **向后兼容**: 新版本保持向后兼容
- **废弃通知**: 提前通知API废弃计划
- **迁移指南**: 提供版本迁移指南

### 更新日志

- **v1.0.0** (2024-01-20): 初始版本发布
  - 基础服务注册和发现功能
  - 能力管理功能
  - 需求匹配功能
  - 完整的API文档

## 安全考虑

### 认证和授权

- JWT Token认证
- API Key认证
- 基于角色的访问控制
- 请求频率限制

### 数据安全

- 输入参数验证
- SQL注入防护
- XSS攻击防护
- 敏感数据加密

### 网络安全

- HTTPS强制加密
- CORS跨域控制
- 请求头验证
- IP白名单控制

## 支持和反馈

### 技术支持

- **邮箱**: <EMAIL>
- **文档**: https://docs.archscope.com
- **GitHub**: https://github.com/archscope/service-discovery

### 问题反馈

如果您在使用API过程中遇到问题，请通过以下方式反馈：

1. **GitHub Issues**: 提交bug报告或功能请求
2. **邮件支持**: 发送详细问题描述
3. **在线文档**: 查看常见问题解答

### 贡献指南

欢迎贡献代码和文档：

1. Fork项目仓库
2. 创建功能分支
3. 提交代码变更
4. 创建Pull Request
5. 等待代码审查

## 许可证

本项目采用MIT许可证，详情请参见LICENSE文件。

---

**最后更新**: 2024-01-20  
**文档版本**: 1.0.0  
**API版本**: v1
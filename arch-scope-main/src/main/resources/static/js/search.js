/**
 * 文档搜索功能
 */
document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const searchInput = document.getElementById('search-input');
    const searchButton = document.getElementById('search-button');
    const searchResults = document.getElementById('search-results');
    
    // 获取当前项目ID（从URL中提取）
    const projectId = window.location.pathname.split('/').filter(Boolean).pop();
    
    // 搜索索引
    let searchIndex;
    let documents = [];
    
    // 加载搜索索引
    fetch('/js/search-index.json')
        .then(response => response.json())
        .then(data => {
            documents = data;
            
            // 创建Lunr索引
            searchIndex = lunr(function() {
                this.field('title', { boost: 10 });
                this.field('content');
                this.field('type');
                
                // 添加文档到索引
                data.forEach(doc => {
                    // 只索引当前项目的文档
                    if (doc.projectId === projectId) {
                        this.add({
                            id: doc.id,
                            title: doc.title,
                            content: doc.content,
                            type: doc.type
                        });
                    }
                });
            });
            
            console.log('搜索索引加载完成');
        })
        .catch(error => {
            console.error('加载搜索索引失败:', error);
            searchResults.innerHTML = '<p class="search-error">搜索索引加载失败，请稍后再试。</p>';
        });
    
    // 搜索函数
    function performSearch() {
        const query = searchInput.value.trim();
        
        if (!query) {
            searchResults.innerHTML = '<p class="search-info">请输入关键词进行搜索</p>';
            return;
        }
        
        if (!searchIndex) {
            searchResults.innerHTML = '<p class="search-info">搜索索引正在加载中，请稍后再试...</p>';
            return;
        }
        
        try {
            // 执行搜索
            const results = searchIndex.search(query);
            
            if (results.length === 0) {
                searchResults.innerHTML = '<p class="search-no-results">未找到匹配的结果</p>';
                return;
            }
            
            // 处理搜索结果
            let html = '';
            results.forEach(result => {
                const doc = documents.find(d => d.id === result.ref);
                if (doc) {
                    // 提取匹配的内容片段
                    const snippet = extractSnippet(doc.content, query);
                    
                    html += `
                        <div class="search-result">
                            <h3 class="search-result-title">
                                <a href="${doc.url}">${doc.title}</a>
                            </h3>
                            <div class="search-result-meta">
                                <span class="search-result-type">${doc.type}</span>
                                <span class="search-result-version">版本: ${doc.version}</span>
                            </div>
                            <p class="search-result-snippet">${snippet}</p>
                        </div>
                    `;
                }
            });
            
            searchResults.innerHTML = html;
        } catch (error) {
            console.error('搜索失败:', error);
            searchResults.innerHTML = '<p class="search-error">搜索失败，请尝试其他关键词。</p>';
        }
    }
    
    // 提取匹配的内容片段
    function extractSnippet(content, query) {
        const words = query.toLowerCase().split(/\s+/);
        const contentLower = content.toLowerCase();
        
        // 查找第一个匹配的位置
        let matchIndex = -1;
        for (const word of words) {
            if (word.length > 1) {  // 忽略单个字符的词
                const index = contentLower.indexOf(word);
                if (index !== -1) {
                    matchIndex = index;
                    break;
                }
            }
        }
        
        if (matchIndex === -1) {
            // 如果没有找到匹配，返回内容的前150个字符
            return content.substring(0, 150) + '...';
        }
        
        // 确定片段的起始和结束位置
        const snippetLength = 200;
        let start = Math.max(0, matchIndex - snippetLength / 2);
        let end = Math.min(content.length, start + snippetLength);
        
        // 调整起始位置，避免截断单词
        if (start > 0) {
            const prevSpace = content.lastIndexOf(' ', start);
            if (prevSpace !== -1 && start - prevSpace < 20) {
                start = prevSpace + 1;
            }
        }
        
        // 调整结束位置，避免截断单词
        if (end < content.length) {
            const nextSpace = content.indexOf(' ', end);
            if (nextSpace !== -1 && nextSpace - end < 20) {
                end = nextSpace;
            }
        }
        
        // 提取片段
        let snippet = content.substring(start, end);
        
        // 添加省略号
        if (start > 0) {
            snippet = '...' + snippet;
        }
        if (end < content.length) {
            snippet = snippet + '...';
        }
        
        // 高亮匹配的词
        for (const word of words) {
            if (word.length > 1) {  // 忽略单个字符的词
                const regex = new RegExp(word, 'gi');
                snippet = snippet.replace(regex, match => `<span class="search-highlight">${match}</span>`);
            }
        }
        
        return snippet;
    }
    
    // 绑定搜索按钮点击事件
    searchButton.addEventListener('click', performSearch);
    
    // 绑定输入框回车事件
    searchInput.addEventListener('keypress', function(event) {
        if (event.key === 'Enter') {
            performSearch();
        }
    });
    
    // 自动聚焦到搜索框
    searchInput.focus();
});

/* 搜索相关样式 */
.search-container {
    margin: 2rem 0;
}

.search-form {
    display: flex;
    margin-bottom: 1.5rem;
}

#search-input {
    flex: 1;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    border: 2px solid #ddd;
    border-radius: 4px 0 0 4px;
    outline: none;
    transition: border-color 0.3s;
}

#search-input:focus {
    border-color: var(--primary-color);
}

#search-button {
    padding: 0.75rem 1.5rem;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

#search-button:hover {
    background-color: #2980b9;
}

.search-results {
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    padding: 1rem;
    min-height: 200px;
}

.search-info {
    color: #666;
    text-align: center;
    margin: 2rem 0;
}

.search-result {
    margin-bottom: 1.5rem;
    padding-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
}

.search-result:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.search-result-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.search-result-title a {
    color: var(--primary-color);
    text-decoration: none;
}

.search-result-title a:hover {
    text-decoration: underline;
}

.search-result-meta {
    font-size: 0.85rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.search-result-meta span {
    margin-right: 1rem;
}

.search-result-snippet {
    font-size: 0.95rem;
    line-height: 1.5;
    color: #333;
}

.search-highlight {
    background-color: #ffffc0;
    padding: 0 2px;
    border-radius: 2px;
}

.search-no-results {
    text-align: center;
    color: #666;
    margin: 2rem 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .search-form {
        flex-direction: column;
    }
    
    #search-input {
        border-radius: 4px;
        margin-bottom: 0.5rem;
    }
    
    #search-button {
        border-radius: 4px;
        width: 100%;
    }
}

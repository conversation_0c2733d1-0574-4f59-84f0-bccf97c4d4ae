/* 基本样式 */
:root {
    --primary-color: #3498db;
    --secondary-color: #2c3e50;
    --text-color: #333;
    --light-gray: #f5f5f5;
    --border-color: #ddd;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: var(--text-color);
    background-color: #fff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 头部样式 */
header {
    background-color: var(--secondary-color);
    color: white;
    padding: 1rem 0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    display: flex;
    align-items: center;
}

.logo a {
    display: flex;
    align-items: center;
    text-decoration: none;
    color: white;
}

.logo-img {
    height: 40px;
    margin-right: 10px;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: bold;
}

nav ul {
    display: flex;
    list-style: none;
}

nav ul li {
    margin-left: 20px;
}

nav ul li a {
    color: white;
    text-decoration: none;
    transition: color 0.3s;
}

nav ul li a:hover {
    color: var(--primary-color);
}

/* 主要内容样式 */
main {
    padding: 2rem 0;
}

.document-header {
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.document-meta {
    display: flex;
    color: #666;
    font-size: 0.9rem;
    margin-top: 0.5rem;
}

.document-meta span {
    margin-right: 1rem;
}

.document-content {
    line-height: 1.8;
}

/* Markdown内容样式 */
.document-content h1,
.document-content h2,
.document-content h3,
.document-content h4,
.document-content h5,
.document-content h6 {
    margin-top: 1.5em;
    margin-bottom: 0.5em;
    color: var(--secondary-color);
}

.document-content h1 {
    font-size: 2rem;
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 0.3em;
}

.document-content h2 {
    font-size: 1.75rem;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 0.3em;
}

.document-content h3 {
    font-size: 1.5rem;
}

.document-content h4 {
    font-size: 1.25rem;
}

.document-content p {
    margin-bottom: 1em;
}

.document-content ul,
.document-content ol {
    margin-bottom: 1em;
    padding-left: 2em;
}

.document-content li {
    margin-bottom: 0.5em;
}

.document-content a {
    color: var(--primary-color);
    text-decoration: none;
}

.document-content a:hover {
    text-decoration: underline;
}

.document-content blockquote {
    border-left: 4px solid var(--primary-color);
    padding-left: 1em;
    margin-left: 0;
    margin-bottom: 1em;
    color: #666;
}

.document-content code {
    font-family: 'Courier New', Courier, monospace;
    background-color: var(--light-gray);
    padding: 0.2em 0.4em;
    border-radius: 3px;
    font-size: 0.9em;
}

.document-content pre {
    background-color: var(--light-gray);
    padding: 1em;
    border-radius: 5px;
    overflow-x: auto;
    margin-bottom: 1em;
}

.document-content pre code {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
}

.document-content table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1em;
}

.document-content table th,
.document-content table td {
    border: 1px solid var(--border-color);
    padding: 0.5em;
}

.document-content table th {
    background-color: var(--light-gray);
    font-weight: bold;
}

.document-content img {
    max-width: 100%;
    height: auto;
    display: block;
    margin: 1em auto;
}

/* 目录样式 */
.toc {
    background-color: var(--light-gray);
    padding: 1em;
    border-radius: 5px;
    margin-bottom: 2em;
}

.toc ul {
    list-style-type: none;
    padding-left: 1em;
}

.toc li {
    margin-bottom: 0.5em;
}

.toc a {
    text-decoration: none;
    color: var(--text-color);
}

.toc a:hover {
    color: var(--primary-color);
}

/* Mermaid图表样式 */
.mermaid {
    margin: 1em 0;
    text-align: center;
}

/* 任务列表样式 */
.task-list-item {
    list-style-type: none;
    margin-left: -1.5em;
}

.task-list-item input {
    margin-right: 0.5em;
}

/* 脚注样式 */
.footnote-ref {
    font-size: 0.8em;
    vertical-align: super;
}

.footnote-backref {
    font-size: 0.8em;
}

/* 页脚样式 */
footer {
    background-color: var(--secondary-color);
    color: white;
    padding: 1rem 0;
    text-align: center;
    margin-top: 2rem;
}

/* 搜索框样式 */
.search-box {
    margin: 1.5rem 0;
    text-align: right;
}

.search-link {
    display: inline-flex;
    align-items: center;
    padding: 0.5rem 1rem;
    background-color: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: 4px;
    transition: background-color 0.3s;
}

.search-link:hover {
    background-color: #2980b9;
}

.search-icon {
    margin-right: 0.5rem;
    font-style: normal;
}

/* 响应式设计 */
@media (max-width: 768px) {
    header .container {
        flex-direction: column;
    }

    nav ul {
        margin-top: 1rem;
    }

    nav ul li {
        margin-left: 10px;
        margin-right: 10px;
    }

    .search-box {
        text-align: center;
    }
}

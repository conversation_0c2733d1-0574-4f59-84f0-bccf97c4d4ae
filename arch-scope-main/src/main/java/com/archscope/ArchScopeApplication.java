package com.archscope;

import com.archscope.domain.config.GitServerConfig;


import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ConfigurableApplicationContext;

/**
 * ArchScope 应用程序入口类
 */

@SpringBootApplication
@EnableConfigurationProperties({GitServerConfig.class})

public class ArchScopeApplication {

    public static void main(String[] args) {
        try {
            // 设置系统属性，允许消费者启动错误时继续运行应用
            System.setProperty("spring.main.allow-bean-definition-overriding", "true");

            // 启动应用
            ConfigurableApplicationContext context = SpringApplication.run(ArchScopeApplication.class, args);
            System.out.println("应用启动成功");
        } catch (Exception e) {
            System.err.println("应用启动失败: " + e.getMessage());
        }
    }

}
package com.archscope.config;

import io.swagger.v3.oas.models.examples.Example;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

/**
 * API示例配置类
 * 提供详细的API请求和响应示例
 */
@Configuration
public class ApiExamplesConfig {

    /**
     * 服务注册请求示例
     */
    @Bean
    public Map<String, Example> serviceRegistrationExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 用户管理服务示例
        Example userServiceExample = new Example();
        userServiceExample.setSummary("用户管理服务注册");
        userServiceExample.setDescription("注册一个提供用户管理功能的REST API服务");
        userServiceExample.setValue("{\n" +
            "    \"name\": \"用户管理服务\",\n" +
            "    \"description\": \"提供用户注册、登录、权限管理等功能的微服务\",\n" +
            "    \"type\": \"REST_API\",\n" +
            "    \"version\": \"1.0.0\",\n" +
            "    \"endpoint\": \"https://api.example.com/user-service\",\n" +
            "    \"groupId\": \"com.example\",\n" +
            "    \"artifactId\": \"user-service\",\n" +
            "    \"tags\": [\"authentication\", \"user-management\", \"microservice\"],\n" +
            "    \"status\": \"ACTIVE\",\n" +
            "    \"metadata\": {\n" +
            "        \"environment\": \"production\",\n" +
            "        \"region\": \"us-east-1\",\n" +
            "        \"maintainer\": \"<EMAIL>\",\n" +
            "        \"documentation\": \"https://docs.example.com/user-service\"\n" +
            "    }\n" +
            "}");
        examples.put("userService", userServiceExample);
        
        return examples;
    }

    /**
     * 能力注册请求示例
     */
    @Bean
    public Map<String, Example> capabilityRegistrationExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 用户认证能力示例
        Example authCapabilityExample = new Example();
        authCapabilityExample.setSummary("用户认证能力");
        authCapabilityExample.setDescription("用户登录认证功能的能力注册");
        authCapabilityExample.setValue("{\n" +
            "    \"name\": \"用户认证\",\n" +
            "    \"description\": \"提供用户登录认证功能，支持用户名密码和第三方OAuth认证\",\n" +
            "    \"version\": \"1.0.0\",\n" +
            "    \"tags\": [\"authentication\", \"security\", \"login\"]\n" +
            "}");
        examples.put("authCapability", authCapabilityExample);
        
        return examples;
    }

    /**
     * 需求匹配请求示例
     */
    @Bean
    public Map<String, Example> requirementMatchingExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 电商系统需求示例
        Example ecommerceRequirementExample = new Example();
        ecommerceRequirementExample.setSummary("电商系统需求");
        ecommerceRequirementExample.setDescription("构建电商系统的需求匹配");
        ecommerceRequirementExample.setValue("{\n" +
            "    \"description\": \"我需要构建一个电商系统，需要用户管理、商品管理、订单处理、支付功能和库存管理\",\n" +
            "    \"requiredCapabilities\": [\n" +
            "        \"用户认证\",\n" +
            "        \"用户权限管理\",\n" +
            "        \"商品管理\",\n" +
            "        \"订单处理\",\n" +
            "        \"在线支付\",\n" +
            "        \"库存管理\"\n" +
            "    ],\n" +
            "    \"constraints\": {\n" +
            "        \"maxResponseTime\": \"200ms\",\n" +
            "        \"availability\": \"99.9%\",\n" +
            "        \"region\": \"cn-north-1\"\n" +
            "    }\n" +
            "}");
        examples.put("ecommerceRequirement", ecommerceRequirementExample);
        
        return examples;
    }

    /**
     * 成功响应示例
     */
    @Bean
    public Map<String, Example> successResponseExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 服务注册成功响应
        Example serviceRegistrationSuccess = new Example();
        serviceRegistrationSuccess.setSummary("服务注册成功");
        serviceRegistrationSuccess.setValue("{\n" +
            "    \"success\": true,\n" +
            "    \"message\": \"服务注册成功\",\n" +
            "    \"data\": {\n" +
            "        \"id\": \"srv-001\",\n" +
            "        \"name\": \"用户管理服务\",\n" +
            "        \"status\": \"ACTIVE\"\n" +
            "    },\n" +
            "    \"timestamp\": \"2024-01-20T15:30:00Z\"\n" +
            "}");
        examples.put("serviceRegistrationSuccess", serviceRegistrationSuccess);
        
        return examples;
    }

    /**
     * 错误响应示例
     */
    @Bean
    public Map<String, Example> errorResponseExamples() {
        Map<String, Example> examples = new HashMap<>();
        
        // 参数验证错误
        Example validationError = new Example();
        validationError.setSummary("参数验证错误");
        validationError.setValue("{\n" +
            "    \"success\": false,\n" +
            "    \"message\": \"请求参数错误\",\n" +
            "    \"error\": \"服务名称不能为空，版本号格式不正确\",\n" +
            "    \"timestamp\": \"2024-01-20T15:30:00Z\"\n" +
            "}");
        examples.put("validationError", validationError);
        
        return examples;
    }
}

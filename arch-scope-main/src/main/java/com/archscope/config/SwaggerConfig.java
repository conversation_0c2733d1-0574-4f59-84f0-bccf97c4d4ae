package com.archscope.config;

import org.springdoc.core.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Swagger配置类
 * 配置API文档分组和自定义设置
 */
@Configuration
public class SwaggerConfig {

    /**
     * 服务发现系统API分组
     */
    @Bean
    public GroupedOpenApi serviceDiscoveryApi() {
        return GroupedOpenApi.builder()
                .group("service-discovery")
                .displayName("服务发现系统")
                .pathsToMatch("/api/v1/services/discovery/**", "/api/v1/services/registry/**",
                             "/api/v1/capabilities/**", "/api/v1/requirements/**")
                .build();
    }

    /**
     * 项目管理API分组
     */
    @Bean
    public GroupedOpenApi projectManagementApi() {
        return GroupedOpenApi.builder()
                .group("project-management")
                .displayName("项目管理")
                .pathsToMatch("/api/v1/projects/**", "/api/v1/tasks/**", 
                             "/api/v1/documents/**")
                .build();
    }

    /**
     * Git仓库管理API分组
     */
    @Bean
    public GroupedOpenApi gitRepositoryApi() {
        return GroupedOpenApi.builder()
                .group("git-repository")
                .displayName("Git仓库管理")
                .pathsToMatch("/api/v1/git/**", "/api/v1/repositories/**")
                .build();
    }

    /**
     * 系统管理API分组
     */
    @Bean
    public GroupedOpenApi systemManagementApi() {
        return GroupedOpenApi.builder()
                .group("system-management")
                .displayName("系统管理")
                .pathsToMatch("/api/v1/system/**", "/api/v1/admin/**", 
                             "/api/v1/health/**")
                .build();
    }

    /**
     * 完整API文档
     */
    @Bean
    public GroupedOpenApi allApis() {
        return GroupedOpenApi.builder()
                .group("all")
                .displayName("完整API文档")
                .pathsToMatch("/api/v1/**")
                .build();
    }
}
# Arch-Scope Project CRUSH Profile

This document outlines the essential commands, conventions, and workflow for the Arch-Scope project.

## Development Workflow

This project follows the **RIPER-5** mode-based workflow, detailed in `.cursor/.rules/cursor_enhanced_rule_set.mdc`. The workflow modes are: Research, Innovate, Plan, Execute, and Review.

- **Mode Transitions:** Use explicit commands like "ENTER RESEARCH MODE" to switch between modes.
- **Memory Bank:** A `memory-bank/` directory stores project context. Always read this at the start of a session.

## Build and Test

This is a **Java Maven** project.

- **Build all modules:** `mvn clean install`
- **Run all tests:** `mvn test`
- **Run a single test:** `mvn -Dtest=TestClassName#methodName test`
- **Run a single test file:** `mvn -Dtest=TestClassName test`
- **Run all tests in a module:** `cd arch-scope-MODULE && mvn test`

## Code Style and Conventions

Code style is managed through **Cursor rules**. For full details, see the [rules directory](mdc:.cursor/rules/).

- **Rule improvement:** Follow the [self-improvement guide](mdc:.cursor/rules/self_improve.mdc) to suggest new rules.

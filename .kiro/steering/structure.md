# 项目结构

## DDD架构概述

ArchScope遵循领域驱动设计(DDD)与六边形架构模式，组织为以下模块：

1. **领域层**（`arch-scope-domain`）：核心业务逻辑和实体
2. **应用层**（`arch-scope-app`）：协调领域对象和服务
3. **接口层**（`arch-scope-facade`）：定义外部API契约
4. **基础设施层**（`arch-scope-infrastructure`）：实现技术细节
5. **主应用**（`arch-scope-main`）：应用程序入口点和配置
6. **前端**（`arch-scope-frontend`）：基于Vue.js的UI应用
7. **MCP服务器**（`arch-scope-mcp`）：用于任务处理的模型上下文协议服务器

## 后端结构

```
/
├── arch-scope-domain/             # 领域层
│   └── src/main/java/com/archscope/domain/
│       ├── model/                 # 领域模型/实体
│       ├── valueobject/           # 值对象
│       ├── service/               # 领域服务
│       ├── repository/            # 仓储接口
│       ├── event/                 # 领域事件
│       └── ...
├── arch-scope-app/                # 应用层
│   └── src/main/java/com/archscope/app/
│       ├── service/               # 应用服务
│       ├── controller/            # 控制器
│       ├── assembler/             # DTO转换器
│       ├── command/               # 命令对象
│       ├── dto/                   # 数据传输对象
│       └── ...
├── arch-scope-facade/             # 接口层
│   └── src/main/java/com/archscope/facade/
│       ├── api/                   # API定义
│       └── dto/                   # 传输对象
├── arch-scope-infrastructure/     # 基础设施层
│   └── src/main/java/com/archscope/infrastructure/
│       ├── config/                # 配置类
│       ├── persistence/           # 数据库实体
│       ├── repository/            # 仓储实现
│       └── ...
└── arch-scope-main/               # 主应用
    └── src/main/java/com/archscope/
        └── ArchScopeApplication.java  # 入口点
```

## 前端结构

```
arch-scope-frontend/
├── src/
│   ├── assets/                    # 静态资源（图片、字体）
│   ├── components/                # 可复用Vue组件
│   ├── composables/               # Vue组合函数
│   ├── layouts/                   # 页面布局
│   ├── router/                    # Vue Router配置
│   ├── stores/                    # Pinia状态存储
│   ├── styles/                    # 全局样式
│   ├── utils/                     # 工具函数
│   ├── views/                     # 页面组件
│   │   ├── projects/              # 项目相关页面
│   │   ├── tasks/                 # 任务相关页面
│   │   └── ...
│   ├── App.vue                    # 根组件
│   └── main.ts                    # 入口点
├── cypress/                       # E2E测试
└── public/                        # 静态文件
```

## MCP服务器结构

```
arch-scope-mcp/
├── src/
│   ├── services/                  # 服务实现
│   ├── tools/                     # MCP工具
│   ├── types/                     # TypeScript类型定义
│   ├── utils/                     # 工具函数
│   ├── index.ts                   # 入口点
│   └── server.ts                  # 服务器实现
└── tests/                         # 测试文件
```

## 代码组织原则

1. **依赖方向**：高层依赖低层，而非反之
2. **领域优先**：领域模型是核心，其他层围绕它工作
3. **限界上下文**：大型领域模型被拆分为独立的上下文
4. **通用语言**：开发人员和业务专家使用统一的术语
5. **关注点分离**：每个模块有特定的职责

## 命名约定

- **Java类**：帕斯卡命名法（如`ProjectService`）
- **Java方法/变量**：驼峰命名法（如`getProjectById`）
- **数据库表**：蛇形命名法（如`project_table`）
- **Vue组件**：帕斯卡命名法（如`ProjectDetail.vue`）
- **CSS类**：短横线命名法（如`project-card`）
- **TypeScript文件**：驼峰命名法（如`projectUtils.ts`）
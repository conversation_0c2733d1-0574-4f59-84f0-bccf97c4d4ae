# ArchScope - 架构鹰眼

ArchScope（架构鹰眼）是一个面向开发者的架构观测和守护系统，旨在通过自动化分析设计文档和代码仓库，为开发者提供项目的全局视角，帮助开发者快速理解和治理项目。

## 核心特性

- **智能代码分析**：基于LLM+提示词的代码仓库解析
- **自动文档生成**：自动生成项目架构文档
- **版本变更追踪**：版本变更感知和对比
- **健康度评估**：项目健康度评估和星级评定
- **文档站点**：文档网站自动生成和托管

## 关键概念

- **项目（Projects）**：代表要分析的代码仓库的主要实体
- **任务（Tasks）**：针对项目运行的分析作业（代码分析、文档生成等）
- **文档（Documents）**：从分析任务生成的成果物
- **文档版本（Document Versions）**：跟踪文档随时间的变化

## 用户工作流程

1. 使用Git仓库URL注册新项目
2. 系统分析项目并生成文档
3. 用户可以查看项目详情、文档和任务状态
4. 当仓库发生变更时，文档会自动更新
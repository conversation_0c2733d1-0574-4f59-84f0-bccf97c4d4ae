{"enabled": true, "name": "Security Pre-Commit <PERSON>", "description": "Scans changed files for potential security issues like hardcoded credentials, API keys, tokens, and other sensitive information", "version": "1", "when": {"type": "userTriggered", "patterns": ["*.java", "*.js", "*.ts", "*.yml", "*.yaml", "*.xml", "*.properties", "*.env", "*.config.js", "*.json"]}, "then": {"type": "askAgent", "prompt": "Review the changed files for potential security issues:\n\n1. Look for API keys, tokens, or credentials in source code\n2. Check for private keys or sensitive credentials\n3. Scan for encryption keys or certificates\n4. Identify authentication tokens or session IDs\n5. Flag passwords or secrets in configuration files\n6. Detect IP addresses containing sensitive data\n7. Find hardcoded internal URLs\n8. Spot database connection credentials\n\nFor each security issue found:\n1. Highlight the specific code with the security risk\n2. Explain why it's a security concern\n3. Suggest a secure alternative approach\n4. Recommend security best practices to follow\n\nIf no security issues are found, confirm that the changes appear secure and mention any security best practices that are being followed.\n\n始终使用中文回答。"}}
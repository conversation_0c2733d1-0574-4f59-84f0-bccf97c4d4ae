{"enabled": true, "name": "Code Quality Analyzer", "description": "Analyzes modified code for potential improvements, including code smells, design patterns, and best practices to enhance code quality.", "version": "1", "when": {"type": "userTriggered", "patterns": ["**/*.java", "**/*.ts", "**/*.js", "**/*.vue", "**/*.html", "**/*.css"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code for potential improvements. Focus on:\n1. Code smells and anti-patterns\n2. Design pattern opportunities\n3. Performance optimizations\n4. Readability and maintainability improvements\n5. Best practices for the specific language/framework\n\nProvide specific, actionable suggestions that maintain the existing functionality while improving code quality. Include code examples where appropriate.\n\n始终使用中文回答。"}}
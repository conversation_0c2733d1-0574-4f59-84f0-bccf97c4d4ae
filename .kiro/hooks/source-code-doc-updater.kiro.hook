{"enabled": true, "name": "Source Code Documentation Updater", "description": "Monitors source code files and triggers documentation updates in README or docs folder when changes are detected", "version": "1", "when": {"type": "userTriggered", "patterns": ["**/*.java", "**/*.ts", "**/*.js", "**/*.vue", "**/*.md", "**/*.yaml", "**/*.yml", "pom.xml", "package.json"]}, "then": {"type": "askAgent", "prompt": "I noticed changes to source code files in the project. Please review these changes and update the relevant documentation in either:\n1. The README.md file\n2. Documentation files in the /docs directory\n\nConsider the following when updating documentation:\n- If the changes affect architecture or design, update architectural documentation\n- If the changes add or modify features, update feature documentation\n- If the changes affect APIs, update API documentation\n- Ensure code examples in documentation reflect the current implementation\n- Update any diagrams or visual representations if necessary\n- Maintain consistent formatting and style with existing documentation\n\nPlease identify which documentation files need to be updated based on the changes and make the appropriate modifications.\n\n始终使用中文回答。"}}
{"enabled": true, "name": "Test Coverage Maintainer", "description": "Monitors source code changes, identifies modified or new functions/methods, checks test coverage, and generates tests for uncovered code", "version": "1", "when": {"type": "userTriggered", "patterns": ["**/*.java", "**/*.ts", "**/*.js"]}, "then": {"type": "askAgent", "prompt": "I noticed you've modified source code. Let me help ensure proper test coverage:\n\n1. I'll identify the new or modified functions/methods in your changes\n2. I'll check if corresponding tests exist for these changes\n3. If test coverage is missing, I'll generate appropriate test cases\n4. I'll run the tests to verify they pass\n5. I'll update the coverage reports\n\nHere's my analysis of your changes:\n\n[Analyze the modified files to identify new/changed functions and methods]\n[Check for existing test files that should cover these changes]\n[Determine if test coverage is adequate]\n[If coverage is missing, generate appropriate test cases]\n[Run tests and report results]\n[Update coverage metrics]\n\nWould you like me to implement the missing tests for you?\n\n始终使用中文回答。"}}
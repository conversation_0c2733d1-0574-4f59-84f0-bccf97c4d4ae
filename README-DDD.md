# 架构鹰眼ArchScope - DDD架构说明

## 后端DDD架构说明

### 1. 领域层 (arch-scope-domain)

领域层是整个系统的核心，包含业务实体、值对象、领域服务、领域事件等。遵循"贫血模型"或"充血模型"的设计原则，确保领域对象包含业务逻辑。

```
arch-scope-domain/
└── src/main/java/com/archscope/domain/
    ├── model/          # 领域模型/实体
    ├── valueobject/    # 值对象
    ├── service/        # 领域服务
    ├── repository/     # 仓储接口
    └── event/          # 领域事件
```

### 2. 应用层 (arch-scope-app)

应用层负责协调领域对象和服务、与外部系统交互、负责输入验证和DTO转换，实现业务用例，处理事务管理、消息监听器等。

```
arch-scope-app/
└── src/main/java/com/archscope/app/
    ├── service/        # 应用服务
    ├── controller/     # 控制器
    ├── assembler/      # DTO转换器
    ├── command/        # 命令对象
    ├── dto/            # 数据传输对象
    ├── event/          # 应用事件处理
    └── query/          # 查询服务
```

### 3. 接口层 (arch-scope-facade)

接口层负责约定与外部系统交互的接口、DTO, 不包含业务逻辑。

```
arch-scope-facade/
└── src/main/java/com/archscope/facade/
    ├── api/            # API接口定义
    └── dto/            # 传输对象
```

### 4. 基础设施层 (arch-scope-infrastructure)

基础设施层实现技术细节，如数据持久化、消息队列、缓存、外部服务调用等。

```
arch-scope-infrastructure/
└── src/main/java/com/archscope/infrastructure/
    ├── config/         # 配置类
    ├── persistence/    # 数据库实体
    ├── repository/     # 仓储实现
    ├── external/       # 外部服务
    ├── messaging/      # 消息队列
    └── cache/          # 缓存实现
```

### 5. 主应用 (arch-scope-main)

主应用模块包含应用程序入口，配置文件等，负责启动和初始化系统。

```
arch-scope-main/
└── src/main/java/com/archscope/main/
    └── ArchScopeApplication.java  # 应用程序入口
```

## DDD设计原则

1. **领域优先**：领域模型是核心，其他层围绕领域模型展开。
2. **依赖倒置**：高层模块不依赖于低层模块，两者都依赖于抽象。
3. **领域聚合**：根据业务规则将实体和值对象组织成聚合。
4. **限界上下文**：将大型领域模型拆分为多个独立的上下文。
5. **通用语言**：开发人员和业务专家使用统一的语言描述领域。

## 数据流向

1. 外部请求 → 应用层(应用服务) → 领域层(控制器/领域服务/实体) → 基础设施层(仓储实现) → 数据库
2. 数据库 → 基础设施层(仓储实现) → 领域层(领域服务/实体) → 应用层(应用服务) → 领域层(控制器/领域服务/实体) → 外部响应

## 开发指南

1. **领域层开发**：
   - 识别领域实体和值对象
   - 定义领域服务的业务行为
   - 设计仓储接口

2. **应用层开发**：
   - 实现控制器
   - 实现应用服务，编排业务用例
   - 处理请求参数验证和错误处理
   - 处理事务和安全
   - 转换领域对象和DTO

3. **接口层开发**：
   - 定义API接口
   - 定义DTO

4. **基础设施层开发**：
   - 实现仓储接口
   - 配置外部服务
   - 实现缓存和消息队列 
# 贡献指南 👋

我们欢迎您为此项目做出贡献!请仔细阅读以下指南。

## 目录
- [行为准则](#行为准则) 
- [开始贡献](#开始贡献)
- [开发流程](#开发流程)
- [提交规范](#提交规范)
- [问题反馈](#问题反馈)
- [代码审查](#代码审查)

## 行为准则

请遵守我们的行为准则。我们希望创建一个友善、包容的社区。

## 开始贡献

1. Fork 此仓库
2. 克隆您的 Fork 仓库到本地
3. 创建新的功能分支:
```bash
git checkout -b feature/your-feature-name
```

## 开发流程

1. 确保代码符合我们的编码规范
2. 编写相应的测试用例
3. 运行所有测试确保通过
4. 及时更新文档

## 提交规范 

- 使用清晰的提交信息
- 每次提交专注于一个变更
- 提交信息格式:
```
<类型>: <描述>

[可选的正文]

[可选的页脚]
```

类型包括:
- feat: 新功能
- fix: 修复bug
- docs: 文档更新
- style: 代码格式调整
- refactor: 重构代码
- test: 添加测试
- chore: 构建过程或辅助工具变动

## 问题反馈

- 使用 issue 模板
- 提供清晰的重现步骤
- 附上相关的日志和截图

## 代码审查

- 耐心等待维护者的反馈
- 积极响应代码审查意见
- 保持友善和开放的态度

感谢您的贡献! 🎉
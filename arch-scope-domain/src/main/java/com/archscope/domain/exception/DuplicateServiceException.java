package com.archscope.domain.exception;

/**
 * 重复服务异常
 */
public class DuplicateServiceException extends ServiceDiscoveryDomainException {

    public DuplicateServiceException(String serviceName) {
        super("Service already exists with name: " + serviceName);
    }

    public DuplicateServiceException(String groupId, String artifactId, String version) {
        super("Service already exists with Maven coordinates: " + groupId + ":" + artifactId + ":" + version);
    }

    public DuplicateServiceException(String message, Throwable cause) {
        super(message, cause);
    }
}
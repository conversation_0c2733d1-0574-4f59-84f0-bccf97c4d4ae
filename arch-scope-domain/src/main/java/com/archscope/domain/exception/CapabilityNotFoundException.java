package com.archscope.domain.exception;

/**
 * 能力未找到异常
 */
public class CapabilityNotFoundException extends ServiceDiscoveryDomainException {

    public CapabilityNotFoundException(String capabilityId) {
        super("Capability not found with ID: " + capabilityId);
    }

    public CapabilityNotFoundException(String capabilityName, String serviceId) {
        super("Capability '" + capabilityName + "' not found for service: " + serviceId);
    }

    public CapabilityNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
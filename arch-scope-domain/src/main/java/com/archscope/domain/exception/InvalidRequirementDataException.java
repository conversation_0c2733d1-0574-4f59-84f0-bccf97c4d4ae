package com.archscope.domain.exception;

/**
 * 无效需求数据异常
 */
public class InvalidRequirementDataException extends ServiceDiscoveryDomainException {

    public InvalidRequirementDataException(String message) {
        super("Invalid requirement data: " + message);
    }

    public InvalidRequirementDataException(String field, String value) {
        super("Invalid requirement data - " + field + ": " + value);
    }

    public InvalidRequirementDataException(String message, Throwable cause) {
        super(message, cause);
    }
}
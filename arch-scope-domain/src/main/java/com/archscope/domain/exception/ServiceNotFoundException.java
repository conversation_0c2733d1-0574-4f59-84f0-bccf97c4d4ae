package com.archscope.domain.exception;

/**
 * 服务未找到异常
 */
public class ServiceNotFoundException extends ServiceDiscoveryDomainException {

    public ServiceNotFoundException(String serviceId) {
        super("Service not found with ID: " + serviceId);
    }

    public ServiceNotFoundException(String groupId, String artifactId) {
        super("Service not found with Maven coordinates: " + groupId + ":" + artifactId);
    }

    public ServiceNotFoundException(String groupId, String artifactId, String version) {
        super("Service not found with Maven coordinates: " + groupId + ":" + artifactId + ":" + version);
    }

    public ServiceNotFoundException(String message, Throwable cause) {
        super(message, cause);
    }
}
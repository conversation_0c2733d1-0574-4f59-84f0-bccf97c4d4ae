package com.archscope.domain.exception;

/**
 * 无效能力数据异常
 */
public class InvalidCapabilityDataException extends ServiceDiscoveryDomainException {

    public InvalidCapabilityDataException(String message) {
        super("Invalid capability data: " + message);
    }

    public InvalidCapabilityDataException(String field, String value) {
        super("Invalid capability data - " + field + ": " + value);
    }

    public InvalidCapabilityDataException(String message, Throwable cause) {
        super(message, cause);
    }
}
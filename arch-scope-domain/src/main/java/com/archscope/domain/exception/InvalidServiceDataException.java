package com.archscope.domain.exception;

/**
 * 无效服务数据异常
 */
public class InvalidServiceDataException extends ServiceDiscoveryDomainException {

    public InvalidServiceDataException(String message) {
        super("Invalid service data: " + message);
    }

    public InvalidServiceDataException(String field, String value) {
        super("Invalid service data - " + field + ": " + value);
    }

    public InvalidServiceDataException(String message, Throwable cause) {
        super(message, cause);
    }
}
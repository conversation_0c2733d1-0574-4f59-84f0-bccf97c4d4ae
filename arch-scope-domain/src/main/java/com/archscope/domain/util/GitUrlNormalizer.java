package com.archscope.domain.util;

import lombok.extern.slf4j.Slf4j;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Git仓库URL标准化工具类
 * 用于统一不同格式的Git仓库URL，防止重复注册同一个仓库
 */
@Slf4j
public class GitUrlNormalizer {

    // HTTPS格式的Git URL正则表达式
    private static final Pattern HTTPS_PATTERN = Pattern.compile(
        "^https?://([^/]+)/([^/]+)/([^/]+?)(?:\\.git)?/?$",
        Pattern.CASE_INSENSITIVE
    );

    // SSH格式的Git URL正则表达式
    private static final Pattern SSH_PATTERN = Pattern.compile(
        "^git@([^:]+):([^/]+)/([^/]+?)(?:\\.git)?/?$",
        Pattern.CASE_INSENSITIVE
    );

    /**
     * 标准化Git仓库URL
     * 将不同格式的URL统一为标准格式，便于重复检测
     * 
     * @param repositoryUrl 原始仓库URL
     * @return 标准化后的URL，如果URL无效则返回null
     */
    public static String normalize(String repositoryUrl) {
        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            log.debug("仓库URL为空，无法标准化");
            return null;
        }

        String trimmedUrl = repositoryUrl.trim();
        log.debug("开始标准化URL: {}", trimmedUrl);

        // 尝试匹配HTTPS格式
        Matcher httpsMatcher = HTTPS_PATTERN.matcher(trimmedUrl);
        if (httpsMatcher.matches()) {
            String host = httpsMatcher.group(1).toLowerCase();
            String owner = httpsMatcher.group(2);
            String repo = httpsMatcher.group(3);
            
            String normalized = String.format("https://%s/%s/%s", host, owner, repo);
            log.debug("HTTPS URL标准化: {} -> {}", trimmedUrl, normalized);
            return normalized;
        }

        // 尝试匹配SSH格式
        Matcher sshMatcher = SSH_PATTERN.matcher(trimmedUrl);
        if (sshMatcher.matches()) {
            String host = sshMatcher.group(1).toLowerCase();
            String owner = sshMatcher.group(2);
            String repo = sshMatcher.group(3);
            
            // 将SSH格式转换为HTTPS格式作为标准格式
            String normalized = String.format("https://%s/%s/%s", host, owner, repo);
            log.debug("SSH URL标准化: {} -> {}", trimmedUrl, normalized);
            return normalized;
        }

        log.warn("无法识别的Git URL格式: {}", trimmedUrl);
        return null;
    }

    /**
     * 检查两个URL是否指向同一个仓库
     * 
     * @param url1 第一个URL
     * @param url2 第二个URL
     * @return 如果指向同一个仓库返回true，否则返回false
     */
    public static boolean isSameRepository(String url1, String url2) {
        String normalized1 = normalize(url1);
        String normalized2 = normalize(url2);
        
        if (normalized1 == null || normalized2 == null) {
            return false;
        }
        
        boolean isSame = normalized1.equals(normalized2);
        log.debug("URL比较: {} vs {} -> {}", url1, url2, isSame);
        return isSame;
    }

    /**
     * 验证URL是否为有效的Git仓库URL
     * 
     * @param repositoryUrl 仓库URL
     * @return 如果是有效的Git URL返回true，否则返回false
     */
    public static boolean isValidGitUrl(String repositoryUrl) {
        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            return false;
        }

        String trimmedUrl = repositoryUrl.trim();
        return HTTPS_PATTERN.matcher(trimmedUrl).matches() || 
               SSH_PATTERN.matcher(trimmedUrl).matches();
    }

    /**
     * 从URL中提取仓库主机名
     * 
     * @param repositoryUrl 仓库URL
     * @return 主机名，如果无法提取则返回null
     */
    public static String extractHost(String repositoryUrl) {
        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            return null;
        }

        String trimmedUrl = repositoryUrl.trim();
        
        Matcher httpsMatcher = HTTPS_PATTERN.matcher(trimmedUrl);
        if (httpsMatcher.matches()) {
            return httpsMatcher.group(1).toLowerCase();
        }

        Matcher sshMatcher = SSH_PATTERN.matcher(trimmedUrl);
        if (sshMatcher.matches()) {
            return sshMatcher.group(1).toLowerCase();
        }

        return null;
    }

    /**
     * 从URL中提取仓库所有者
     * 
     * @param repositoryUrl 仓库URL
     * @return 所有者名称，如果无法提取则返回null
     */
    public static String extractOwner(String repositoryUrl) {
        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            return null;
        }

        String trimmedUrl = repositoryUrl.trim();
        
        Matcher httpsMatcher = HTTPS_PATTERN.matcher(trimmedUrl);
        if (httpsMatcher.matches()) {
            return httpsMatcher.group(2);
        }

        Matcher sshMatcher = SSH_PATTERN.matcher(trimmedUrl);
        if (sshMatcher.matches()) {
            return sshMatcher.group(2);
        }

        return null;
    }

    /**
     * 从URL中提取仓库名称
     * 
     * @param repositoryUrl 仓库URL
     * @return 仓库名称，如果无法提取则返回null
     */
    public static String extractRepositoryName(String repositoryUrl) {
        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            return null;
        }

        String trimmedUrl = repositoryUrl.trim();
        
        Matcher httpsMatcher = HTTPS_PATTERN.matcher(trimmedUrl);
        if (httpsMatcher.matches()) {
            return httpsMatcher.group(3);
        }

        Matcher sshMatcher = SSH_PATTERN.matcher(trimmedUrl);
        if (sshMatcher.matches()) {
            return sshMatcher.group(3);
        }

        return null;
    }

    /**
     * 获取URL的显示格式（用于用户界面显示）
     * 
     * @param repositoryUrl 仓库URL
     * @return 格式化的显示URL
     */
    public static String getDisplayUrl(String repositoryUrl) {
        String normalized = normalize(repositoryUrl);
        return normalized != null ? normalized : repositoryUrl;
    }
}

package com.archscope.domain.util;

import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Git分支过滤工具类
 * 用于过滤掉包含"/"的分支，保留主要分支
 */
@Slf4j
public class BranchFilterUtil {

    /**
     * 过滤分支列表，移除包含"/"的分支
     *
     * @param branches 原始分支列表
     * @return 过滤后的分支列表
     */
    public static List<String> filterBranches(List<String> branches) {
        if (branches == null || branches.isEmpty()) {
            return branches;
        }

        List<String> filteredBranches = branches.stream()
            .filter(BranchFilterUtil::shouldKeepBranch)
            .collect(Collectors.toList());

        int originalCount = branches.size();
        int filteredCount = filteredBranches.size();
        int removedCount = originalCount - filteredCount;

        if (removedCount > 0) {
            log.info("分支过滤完成: 原始分支数={}, 过滤后分支数={}, 移除分支数={}",
                    originalCount, filteredCount, removedCount);
            log.debug("移除的分支: {}",
                    branches.stream()
                        .filter(branch -> !filteredBranches.contains(branch))
                        .collect(Collectors.toList()));
        }

        return filteredBranches;
    }

    /**
     * 判断是否应该保留某个分支
     * 过滤掉所有包含"/"的分支
     *
     * @param branchName 分支名称
     * @return true表示保留，false表示过滤掉
     */
    public static boolean shouldKeepBranch(String branchName) {
        if (branchName == null || branchName.trim().isEmpty()) {
            return false;
        }

        String trimmedBranch = branchName.trim();

        // 过滤掉包含"/"的分支
        if (trimmedBranch.contains("/")) {
            log.debug("过滤包含'/'的分支: {}", trimmedBranch);
            return false;
        }

        // 保留不包含"/"的分支
        log.debug("保留分支: {}", trimmedBranch);
        return true;
    }

    /**
     * 获取过滤规则的描述（用于调试和文档）
     *
     * @return 过滤规则描述
     */
    public static String getFilterDescription() {
        return "过滤掉所有包含'/'的分支，保留主要分支如: main, master, develop, staging等";
    }
}

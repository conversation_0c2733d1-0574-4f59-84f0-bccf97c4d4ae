package com.archscope.domain.util;

import com.archscope.domain.valueobject.TaskStatus;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 任务状态迁移工具类
 * 用于处理TaskStatus枚举的向后兼容性和状态迁移
 */
public class TaskStatusMigrationUtil {

    private static final Logger log = LoggerFactory.getLogger(TaskStatusMigrationUtil.class);

    /**
     * 将字符串状态转换为TaskStatus枚举
     * 提供向后兼容性，将废弃的IN_PROGRESS状态映射到PROCESSING
     * 
     * @param statusString 状态字符串
     * @return TaskStatus枚举值
     * @throws IllegalArgumentException 如果状态字符串无效
     */
    public static TaskStatus fromString(String statusString) {
        if (statusString == null || statusString.trim().isEmpty()) {
            throw new IllegalArgumentException("状态字符串不能为空");
        }

        String normalizedStatus = statusString.trim().toUpperCase();
        
        // 处理向后兼容性
        if ("IN_PROGRESS".equals(normalizedStatus)) {
            log.warn("检测到废弃的状态 IN_PROGRESS，自动转换为 PROCESSING");
            return TaskStatus.PROCESSING;
        }

        try {
            return TaskStatus.valueOf(normalizedStatus);
        } catch (IllegalArgumentException e) {
            log.error("无效的任务状态: {}", statusString);
            throw new IllegalArgumentException("无效的任务状态: " + statusString, e);
        }
    }

    /**
     * 检查状态是否为废弃状态
     * 
     * @param statusString 状态字符串
     * @return true 如果是废弃状态
     */
    public static boolean isDeprecatedStatus(String statusString) {
        return "IN_PROGRESS".equalsIgnoreCase(statusString);
    }

    /**
     * 获取状态的迁移映射
     * 
     * @param deprecatedStatus 废弃的状态
     * @return 新的状态，如果不是废弃状态则返回原状态
     */
    public static String getMigratedStatus(String deprecatedStatus) {
        if ("IN_PROGRESS".equalsIgnoreCase(deprecatedStatus)) {
            return "PROCESSING";
        }
        return deprecatedStatus;
    }

    /**
     * 验证状态转换是否有效
     * 
     * @param fromStatus 源状态
     * @param toStatus 目标状态
     * @return true 如果转换有效
     */
    public static boolean isValidTransition(TaskStatus fromStatus, TaskStatus toStatus) {
        if (fromStatus == null || toStatus == null) {
            return false;
        }

        // 定义有效的状态转换规则
        switch (fromStatus) {
            case PENDING:
                return toStatus == TaskStatus.PROCESSING || 
                       toStatus == TaskStatus.CANCELLED ||
                       toStatus == TaskStatus.WAITING;
                       
            case PROCESSING:
                return toStatus == TaskStatus.COMPLETED || 
                       toStatus == TaskStatus.FAILED || 
                       toStatus == TaskStatus.PARTIAL_SUCCESS ||
                       toStatus == TaskStatus.CANCELLED ||
                       toStatus == TaskStatus.PENDING || // 超时重置
                       toStatus == TaskStatus.PAUSED;
                       
            case WAITING:
                return toStatus == TaskStatus.PENDING ||
                       toStatus == TaskStatus.CANCELLED;
                       
            case PAUSED:
                return toStatus == TaskStatus.PROCESSING ||
                       toStatus == TaskStatus.CANCELLED;
                       
            case COMPLETED:
            case FAILED:
            case PARTIAL_SUCCESS:
            case CANCELLED:
                // 终态状态一般不允许转换，除非是重新处理
                return toStatus == TaskStatus.PENDING; // 允许重新处理
                
            default:
                return false;
        }
    }

    /**
     * 获取状态的显示名称（支持向后兼容）
     * 
     * @param statusString 状态字符串
     * @return 显示名称
     */
    public static String getDisplayName(String statusString) {
        try {
            TaskStatus status = fromString(statusString);
            return status.getDisplayName();
        } catch (IllegalArgumentException e) {
            log.warn("无法获取状态显示名称: {}", statusString);
            return statusString;
        }
    }

    /**
     * 检查状态是否为活跃处理状态
     * 
     * @param statusString 状态字符串
     * @return true 如果是活跃处理状态
     */
    public static boolean isActiveProcessing(String statusString) {
        try {
            TaskStatus status = fromString(statusString);
            return status.isActiveProcessing();
        } catch (IllegalArgumentException e) {
            // 对于废弃状态的特殊处理
            return "IN_PROGRESS".equalsIgnoreCase(statusString);
        }
    }

    /**
     * 检查状态是否为终态
     * 
     * @param statusString 状态字符串
     * @return true 如果是终态状态
     */
    public static boolean isFinalStatus(String statusString) {
        try {
            TaskStatus status = fromString(statusString);
            return status.isFinalStatus();
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 检查状态是否可处理
     * 
     * @param statusString 状态字符串
     * @return true 如果可处理
     */
    public static boolean isProcessable(String statusString) {
        try {
            TaskStatus status = fromString(statusString);
            return status.isProcessable();
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}

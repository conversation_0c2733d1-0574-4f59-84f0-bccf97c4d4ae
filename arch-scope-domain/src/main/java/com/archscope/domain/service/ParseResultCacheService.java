package com.archscope.domain.service;

import com.archscope.domain.entity.ParseResultCache;
import com.archscope.domain.model.parser.FileParseResult;

import java.util.List;
import java.util.Optional;

/**
 * 解析结果缓存服务接口
 */
public interface ParseResultCacheService {
    
    /**
     * 获取解析结果
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @return 解析结果列表
     */
    List<FileParseResult> getParseResults(Long repositoryId, String commitId);
    
    /**
     * 保存解析结果
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @param parseResults 解析结果列表
     */
    void saveParseResults(Long repositoryId, String commitId, List<FileParseResult> parseResults);
    
    /**
     * 获取解析结果缓存
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @return 解析结果缓存
     */
    Optional<ParseResultCache> getCache(Long repositoryId, String commitId);
    
    /**
     * 清除缓存
     * 
     * @param repositoryId 代码仓库ID
     */
    void clearCache(Long repositoryId);
    
    /**
     * 检查缓存是否存在
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @return 是否存在
     */
    boolean cacheExists(Long repositoryId, String commitId);
    
    /**
     * 获取仓库的所有缓存
     * 
     * @param repositoryId 代码仓库ID
     * @return 缓存列表
     */
    List<ParseResultCache> getAllCaches(Long repositoryId);
    
    /**
     * 删除指定的缓存
     * 
     * @param cacheId 缓存ID
     */
    void deleteCache(Long cacheId);
}

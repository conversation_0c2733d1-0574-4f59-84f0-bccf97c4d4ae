package com.archscope.domain.service.impl;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.repository.DocumentVersionRepository;
import com.archscope.domain.service.DocumentVersionService;
import com.archscope.domain.valueobject.DocumentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文档版本服务默认实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultDocumentVersionService implements DocumentVersionService {

    private final DocumentVersionRepository documentVersionRepository;

    @Override
    public DocumentVersion createDocumentVersion(DocumentVersion documentVersion) {
        log.info("创建文档版本，项目ID: {}, 文档类型: {}, 提交ID: {}",
                documentVersion.getProjectId(), documentVersion.getDocType(), documentVersion.getCommitId());

        // 设置创建时间
        if (documentVersion.getTimestamp() == null) {
            documentVersion.setTimestamp(LocalDateTime.now());
        }

        // 设置最后修改时间
        if (documentVersion.getLastModified() == null) {
            documentVersion.setLastModified(LocalDateTime.now());
        }

        // 如果没有版本标签，生成一个
        if (documentVersion.getVersionTag() == null || documentVersion.getVersionTag().isEmpty()) {
            documentVersion.setVersionTag(generateVersionTag(documentVersion.getProjectId(), documentVersion.getDocType()));
        }

        // 默认未发布
        if (documentVersion.getIsPublished() == null) {
            documentVersion.setIsPublished(false);
        }

        // 默认状态为草稿
        if (documentVersion.getStatus() == null || documentVersion.getStatus().isEmpty()) {
            documentVersion.setStatus("DRAFT");
        }

        return documentVersionRepository.save(documentVersion);
    }

    @Override
    public Optional<DocumentVersion> getDocumentVersionById(Long id) {
        log.debug("获取文档版本，ID: {}", id);
        return documentVersionRepository.findById(id);
    }

    @Override
    public List<DocumentVersion> findByIds(List<Long> ids) {
        log.debug("根据ID列表获取文档版本，ID列表: {}", ids);
        return documentVersionRepository.findByIds(ids);
    }

    @Override
    public List<DocumentVersion> findPublishedByProjectId(Long projectId) {
        log.debug("获取项目的已发布文档版本，项目ID: {}", projectId);
        return documentVersionRepository.findByProjectIdAndIsPublished(projectId, true);
    }

    @Override
    public List<DocumentVersion> getDocumentVersionsByProjectId(Long projectId) {
        log.debug("获取项目的所有文档版本，项目ID: {}", projectId);
        return documentVersionRepository.findByProjectId(projectId);
    }

    @Override
    public List<DocumentVersion> getDocumentVersionsByProjectIdAndType(Long projectId, DocumentType docType) {
        log.debug("获取项目指定类型的所有文档版本，项目ID: {}, 文档类型: {}", projectId, docType);
        return documentVersionRepository.findByProjectIdAndDocType(projectId, docType);
    }

    @Override
    public List<DocumentVersion> getDocumentVersionsByProjectIdAndCommitId(Long projectId, String commitId) {
        log.debug("获取项目指定提交的所有文档版本，项目ID: {}, 提交ID: {}", projectId, commitId);
        return documentVersionRepository.findByProjectIdAndCommitId(projectId, commitId);
    }

    @Override
    public Optional<DocumentVersion> getDocumentVersionByProjectIdAndVersionTag(Long projectId, String versionTag) {
        log.debug("获取项目指定版本标签的文档版本，项目ID: {}, 版本标签: {}", projectId, versionTag);
        return documentVersionRepository.findByProjectIdAndVersionTag(projectId, versionTag);
    }

    @Override
    public Optional<DocumentVersion> getLatestDocumentVersionByProjectIdAndType(Long projectId, DocumentType docType) {
        log.debug("获取项目指定类型的最新文档版本，项目ID: {}, 文档类型: {}", projectId, docType);
        return documentVersionRepository.findLatestByProjectIdAndDocType(projectId, docType);
    }

    @Override
    public DocumentVersion updateDocumentVersion(DocumentVersion documentVersion) {
        log.info("更新文档版本，ID: {}", documentVersion.getId());

        // 设置最后修改时间
        documentVersion.setLastModified(LocalDateTime.now());

        return documentVersionRepository.update(documentVersion);
    }

    @Override
    public void deleteDocumentVersion(Long id) {
        log.info("删除文档版本，ID: {}", id);
        documentVersionRepository.delete(id);
    }

    @Override
    public int deleteDocumentVersionsByProjectId(Long projectId) {
        log.info("删除项目的所有文档版本，项目ID: {}", projectId);
        return documentVersionRepository.deleteByProjectId(projectId);
    }

    @Override
    public String compareDocumentVersions(Long versionId1, Long versionId2) {
        log.info("比较两个文档版本，版本ID1: {}, 版本ID2: {}", versionId1, versionId2);

        // 获取两个文档版本
        Optional<DocumentVersion> version1Opt = documentVersionRepository.findById(versionId1);
        Optional<DocumentVersion> version2Opt = documentVersionRepository.findById(versionId2);

        if (!version1Opt.isPresent() || !version2Opt.isPresent()) {
            throw new IllegalArgumentException("文档版本不存在");
        }

        DocumentVersion version1 = version1Opt.get();
        DocumentVersion version2 = version2Opt.get();

        // 确保两个文档版本属于同一个项目和同一个文档类型
        if (!version1.getProjectId().equals(version2.getProjectId())) {
            throw new IllegalArgumentException("两个文档版本不属于同一个项目");
        }

        if (!version1.getDocType().equals(version2.getDocType())) {
            throw new IllegalArgumentException("两个文档版本不属于同一个文档类型");
        }

        // TODO: 实现文档比较逻辑
        // 这里应该实现文档内容的比较，可以使用diff算法
        // 简单起见，这里只返回一个示例结果
        return "文档比较结果：版本 " + version1.getVersionTag() + " 与版本 " + version2.getVersionTag() + " 的差异";
    }

    @Override
    public DocumentVersion publishDocumentVersion(Long id) {
        log.info("发布文档版本，ID: {}", id);

        // 获取文档版本
        Optional<DocumentVersion> versionOpt = documentVersionRepository.findById(id);
        if (!versionOpt.isPresent()) {
            throw new IllegalArgumentException("文档版本不存在");
        }

        DocumentVersion version = versionOpt.get();

        // 设置发布状态
        version.setIsPublished(true);
        version.setStatus("PUBLISHED");
        version.setLastModified(LocalDateTime.now());

        return documentVersionRepository.update(version);
    }

    @Override
    public DocumentVersion unpublishDocumentVersion(Long id) {
        log.info("取消发布文档版本，ID: {}", id);

        // 获取文档版本
        Optional<DocumentVersion> versionOpt = documentVersionRepository.findById(id);
        if (!versionOpt.isPresent()) {
            throw new IllegalArgumentException("文档版本不存在");
        }

        DocumentVersion version = versionOpt.get();

        // 设置未发布状态
        version.setIsPublished(false);
        version.setStatus("DRAFT");
        version.setLastModified(LocalDateTime.now());

        return documentVersionRepository.update(version);
    }

    @Override
    public String generateVersionTag(Long projectId, DocumentType docType) {
        log.debug("生成新的版本标签，项目ID: {}, 文档类型: {}", projectId, docType);

        // 获取项目指定类型的最新文档版本
        Optional<DocumentVersion> latestVersionOpt = documentVersionRepository.findLatestByProjectIdAndDocType(projectId, docType);

        if (!latestVersionOpt.isPresent()) {
            // 如果没有现有版本，返回v1.0.0
            return "v1.0.0";
        }

        String latestVersionTag = latestVersionOpt.get().getVersionTag();

        // 解析版本号
        Pattern pattern = Pattern.compile("v(\\d+)\\.(\\d+)\\.(\\d+)");
        Matcher matcher = pattern.matcher(latestVersionTag);

        if (matcher.find()) {
            int major = Integer.parseInt(matcher.group(1));
            int minor = Integer.parseInt(matcher.group(2));
            int patch = Integer.parseInt(matcher.group(3));

            // 增加补丁版本号
            patch++;

            // 如果补丁版本号超过9，增加次要版本号
            if (patch > 9) {
                patch = 0;
                minor++;
            }

            // 如果次要版本号超过9，增加主要版本号
            if (minor > 9) {
                minor = 0;
                major++;
            }

            return String.format("v%d.%d.%d", major, minor, patch);
        } else {
            // 如果版本号格式不匹配，返回v1.0.0
            return "v1.0.0";
        }
    }
}

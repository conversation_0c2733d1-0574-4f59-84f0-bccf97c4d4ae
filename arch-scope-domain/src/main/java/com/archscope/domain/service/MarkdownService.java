package com.archscope.domain.service;

import java.util.Map;

/**
 * Markdown服务接口
 * 负责Markdown文档的处理和转换
 */
public interface MarkdownService {

    /**
     * 将Markdown内容转换为HTML
     *
     * @param markdown Markdown内容
     * @return 转换后的HTML内容
     */
    String convertToHtml(String markdown);

    /**
     * 将Markdown内容转换为HTML，并添加额外的头部元素
     *
     * @param markdown Markdown内容
     * @param additionalHeadElements 额外的头部元素
     * @return 转换后的HTML内容
     */
    String convertToHtml(String markdown, Map<String, String> additionalHeadElements);

    /**
     * 从Markdown内容中提取标题
     *
     * @param markdown Markdown内容
     * @return 提取的标题
     */
    String extractTitle(String markdown);

    /**
     * 从Markdown内容中生成目录
     *
     * @param markdown Markdown内容
     * @return 目录HTML
     */
    String generateTableOfContents(String markdown);

    /**
     * 检查Markdown内容中是否包含Mermaid图表
     *
     * @param markdown Markdown内容
     * @return 是否包含Mermaid图表
     */
    boolean containsMermaidDiagram(String markdown);

    /**
     * 检查Markdown内容中是否包含数学公式
     *
     * @param markdown Markdown内容
     * @return 是否包含数学公式
     */
    boolean containsMathFormula(String markdown);

    /**
     * 从Markdown内容中提取纯文本内容（去除Markdown标记）
     *
     * @param markdown Markdown内容
     * @return 提取的纯文本内容
     */
    String extractPlainText(String markdown);
}

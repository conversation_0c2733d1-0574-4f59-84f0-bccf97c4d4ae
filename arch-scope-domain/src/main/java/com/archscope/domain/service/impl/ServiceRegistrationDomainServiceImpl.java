package com.archscope.domain.service.impl;

import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.service.ServiceRegistrationDomainService;
import com.archscope.domain.valueobject.Metadata;
import com.archscope.domain.valueobject.Tag;
import com.archscope.domain.valueobject.Version;

import java.net.URL;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 服务注册领域服务实现类
 */
public class ServiceRegistrationDomainServiceImpl implements ServiceRegistrationDomainService {

    private static final Pattern GROUP_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_]+(\\.[a-zA-Z0-9_]+)*$");
    private static final Pattern ARTIFACT_ID_PATTERN = Pattern.compile("^[a-zA-Z0-9_\\-]+$");

    private final ServiceRepository serviceRepository;

    public ServiceRegistrationDomainServiceImpl(ServiceRepository serviceRepository) {
        this.serviceRepository = serviceRepository;
    }

    @Override
    public Service registerService(String name, String description, ServiceType type, Version version,
                                  URL endpoint, String groupId, String artifactId, Set<Tag> tags,
                                  ServiceStatus status, Metadata metadata) {
        // 验证服务信息
        validateServiceInfo(name, type, version, endpoint);
        
        // 如果提供了Maven坐标，则验证Maven坐标
        if (groupId != null && artifactId != null) {
            validateMavenCoordinates(groupId, artifactId);
            
            // 检查Maven坐标是否已存在
            if (isMavenCoordinatesExists(groupId, artifactId, version)) {
                throw new IllegalArgumentException("Maven coordinates already exist: " + groupId + ":" + artifactId + ":" + version);
            }
        }
        
        // 检查服务名称是否已存在
        if (isServiceNameExists(name)) {
            throw new IllegalArgumentException("Service name already exists: " + name);
        }
        
        // 检查服务端点是否已存在
        if (isServiceEndpointExists(endpoint)) {
            throw new IllegalArgumentException("Service endpoint already exists: " + endpoint);
        }
        
        // 创建服务实体
        Service service = Service.create(name, description, type, version, endpoint, groupId, artifactId, tags, status, metadata);
        
        return service;
    }

    @Override
    public void validateServiceInfo(String name, ServiceType type, Version version, URL endpoint) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Service name cannot be null or empty");
        }
        
        if (type == null) {
            throw new IllegalArgumentException("Service type cannot be null");
        }
        
        if (version == null) {
            throw new IllegalArgumentException("Service version cannot be null");
        }
        
        if (endpoint == null) {
            throw new IllegalArgumentException("Service endpoint cannot be null");
        }
    }

    @Override
    public void validateMavenCoordinates(String groupId, String artifactId) {
        if (groupId == null || groupId.trim().isEmpty()) {
            throw new IllegalArgumentException("Group ID cannot be null or empty");
        }
        
        if (!GROUP_ID_PATTERN.matcher(groupId).matches()) {
            throw new IllegalArgumentException("Invalid group ID format: " + groupId);
        }
        
        if (artifactId == null || artifactId.trim().isEmpty()) {
            throw new IllegalArgumentException("Artifact ID cannot be null or empty");
        }
        
        if (!ARTIFACT_ID_PATTERN.matcher(artifactId).matches()) {
            throw new IllegalArgumentException("Invalid artifact ID format: " + artifactId);
        }
    }

    @Override
    public boolean isServiceNameExists(String name) {
        return serviceRepository.findByName(name) != null;
    }

    @Override
    public boolean isServiceEndpointExists(URL endpoint) {
        return serviceRepository.findByEndpoint(endpoint) != null;
    }

    @Override
    public boolean isMavenCoordinatesExists(String groupId, String artifactId, Version version) {
        return serviceRepository.findByMavenCoordinates(groupId, artifactId, version.getValue()) != null;
    }
}
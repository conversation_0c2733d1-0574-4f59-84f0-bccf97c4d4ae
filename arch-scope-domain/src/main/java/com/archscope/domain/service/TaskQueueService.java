package com.archscope.domain.service;

import com.archscope.domain.entity.Task;

import java.util.List;
import java.util.Optional;

/**
 * 任务队列服务接口
 * 负责任务的入队、出队和管理
 */
public interface TaskQueueService {
    
    /**
     * 将任务添加到队列
     *
     * @param task 任务
     * @return 添加后的任务（包含ID等信息）
     */
    Task enqueueTask(Task task);
    
    /**
     * 从队列中获取下一个待执行的任务
     *
     * @return 任务，如果队列为空则返回空
     */
    Optional<Task> dequeueTask();
    
    /**
     * 获取指定类型的下一个待执行任务
     *
     * @param taskType 任务类型
     * @return 任务，如果没有符合条件的任务则返回空
     */
    Optional<Task> dequeueTaskByType(String taskType);
    
    /**
     * 获取队列中所有待执行的任务
     *
     * @return 任务列表
     */
    List<Task> getPendingTasks();
    
    /**
     * 获取指定类型的所有待执行任务
     *
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<Task> getPendingTasksByType(String taskType);
    
    /**
     * 获取指定项目的所有待执行任务
     *
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<Task> getPendingTasksByProject(Long projectId);
    
    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @return 更新后的任务
     */
    Task updateTaskStatus(Long taskId, String status);
    
    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param progress 进度百分比（0-100）
     * @return 更新后的任务
     */
    Task updateTaskProgress(Long taskId, Integer progress);
    
    /**
     * 记录任务错误
     *
     * @param taskId 任务ID
     * @param errorMessage 错误信息
     * @return 更新后的任务
     */
    Task recordTaskError(Long taskId, String errorMessage);
    
    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param result 任务结果
     * @return 更新后的任务
     */
    Task completeTask(Long taskId, String result);
    
    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 更新后的任务
     */
    Task cancelTask(Long taskId);
    
    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    Optional<Task> getTaskById(Long taskId);
    
    /**
     * 清理过期任务
     *
     * @param days 天数，超过这个天数的已完成任务将被清理
     * @return 清理的任务数量
     */
    int cleanupExpiredTasks(int days);
}
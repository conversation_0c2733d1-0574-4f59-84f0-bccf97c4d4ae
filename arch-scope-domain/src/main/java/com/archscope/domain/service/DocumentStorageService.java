package com.archscope.domain.service;

import java.io.IOException;
import java.util.Optional;

/**
 * 文档存储服务接口
 * 负责markdown文档的文件系统存储和检索
 */
public interface DocumentStorageService {

    /**
     * 存储文档内容到文件系统
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @param content 文档内容（Markdown格式）
     * @param version 版本标签
     * @return 存储的文件路径
     * @throws IOException 存储失败时抛出异常
     */
    String storeDocument(Long projectId, String docType, String content, String version) throws IOException;

    /**
     * 从文件系统获取文档内容
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @param version 版本标签，如果为null则获取最新版本
     * @return 文档内容，如果文档不存在则返回空
     * @throws IOException 读取失败时抛出异常
     */
    Optional<String> getDocumentContent(Long projectId, String docType, String version) throws IOException;

    /**
     * 删除指定版本的文档
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @param version 版本标签
     * @return 是否删除成功
     */
    boolean deleteDocument(Long projectId, String docType, String version);

    /**
     * 获取文档的存储路径
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @param version 版本标签
     * @return 文档的完整存储路径
     */
    String getDocumentPath(Long projectId, String docType, String version);

    /**
     * 检查文档是否存在
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @param version 版本标签
     * @return 文档是否存在
     */
    boolean documentExists(Long projectId, String docType, String version);

    /**
     * 创建项目文档目录结构
     *
     * @param projectId 项目ID
     * @param version 版本标签
     * @throws IOException 创建目录失败时抛出异常
     */
    void createProjectDirectories(Long projectId, String version) throws IOException;

    /**
     * 获取项目的所有文档版本
     *
     * @param projectId 项目ID
     * @return 版本列表
     */
    java.util.List<String> getProjectVersions(Long projectId);

    /**
     * 获取指定版本的所有文档类型
     *
     * @param projectId 项目ID
     * @param version 版本标签
     * @return 文档类型列表
     */
    java.util.List<String> getDocumentTypes(Long projectId, String version);
}

package com.archscope.domain.service;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.TaskStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 任务性能监控服务接口
 * 提供任务性能分析、统计和优化建议
 */
public interface TaskPerformanceService {

    /**
     * 获取任务统计信息
     *
     * @param projectId 项目ID（可选）
     * @param taskType 任务类型（可选）
     * @param startDate 开始日期（可选）
     * @param endDate 结束日期（可选）
     * @return 统计信息
     */
    Map<String, Object> getTaskStatistics(Long projectId, String taskType, 
                                         LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 获取任务性能指标
     *
     * @param projectId 项目ID（可选）
     * @param hours 统计时间范围（小时）
     * @return 性能指标
     */
    Map<String, Object> getPerformanceMetrics(Long projectId, int hours);

    /**
     * 查找长时间运行的任务
     *
     * @param minutes 运行时间阈值（分钟）
     * @param limit 限制数量
     * @return 长时间运行的任务列表
     */
    List<Task> findLongRunningTasks(int minutes, int limit);

    /**
     * 获取任务队列健康状态
     *
     * @return 健康状态信息
     */
    Map<String, Object> getQueueHealthStatus();

    /**
     * 获取工作节点性能统计
     *
     * @param hours 统计时间范围（小时）
     * @return 工作节点性能统计
     */
    Map<String, Object> getWorkerPerformanceStats(int hours);

    /**
     * 分析任务瓶颈
     *
     * @param projectId 项目ID（可选）
     * @param hours 分析时间范围（小时）
     * @return 瓶颈分析结果
     */
    Map<String, Object> analyzeBottlenecks(Long projectId, int hours);

    /**
     * 获取任务处理趋势
     *
     * @param projectId 项目ID（可选）
     * @param days 天数
     * @return 处理趋势数据
     */
    List<Map<String, Object>> getProcessingTrends(Long projectId, int days);

    /**
     * 预测任务完成时间
     *
     * @param taskId 任务ID
     * @return 预计完成时间
     */
    LocalDateTime predictTaskCompletion(Long taskId);

    /**
     * 获取系统负载建议
     *
     * @return 负载建议
     */
    Map<String, Object> getLoadRecommendations();

    /**
     * 清理过期的已完成任务
     *
     * @param daysOld 保留天数
     * @return 清理的任务数量
     */
    int cleanupOldCompletedTasks(int daysOld);
}

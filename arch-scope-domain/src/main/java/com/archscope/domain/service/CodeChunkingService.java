package com.archscope.domain.service;

import com.archscope.domain.model.parser.CodeChunk;
import com.archscope.domain.model.parser.LanguageType;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 代码分块服务接口
 * 负责将大型文件分割为可管理的块
 */
public interface CodeChunkingService {
    
    /**
     * 将代码文件分割为可管理的块
     * 
     * @param file 代码文件
     * @param languageType 语言类型
     * @return 代码块列表
     * @throws IOException 如果文件读取失败
     */
    List<CodeChunk> chunkFile(File file, LanguageType languageType) throws IOException;
    
    /**
     * 将代码内容分割为可管理的块
     * 
     * @param content 代码内容
     * @param filename 文件名
     * @param languageType 语言类型
     * @return 代码块列表
     */
    List<CodeChunk> chunkContent(String content, String filename, LanguageType languageType);
    
    /**
     * 分析代码块之间的依赖关系
     * 
     * @param chunks 代码块列表
     * @return 更新后的代码块列表，包含依赖关系
     */
    List<CodeChunk> analyzeDependencies(List<CodeChunk> chunks);
    
    /**
     * 获取文件的上下文信息（如导入语句、包声明等）
     * 
     * @param content 文件内容
     * @param languageType 语言类型
     * @return 上下文信息
     */
    String extractContext(String content, LanguageType languageType);
}

package com.archscope.domain.service.impl;

import com.archscope.domain.config.GitServerConfig;
import com.archscope.domain.service.GitRepositoryMetadataService;
import com.archscope.domain.service.git.GitOperationResult;
import com.archscope.domain.service.git.GitService;
import com.archscope.domain.util.BranchFilterUtil;
import com.archscope.domain.valueobject.GitRepositoryInfo;
import com.archscope.domain.valueobject.GitRepositoryValidationRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.ListBranchCommand;
import org.eclipse.jgit.lib.Ref;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Git仓库元数据服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class GitRepositoryMetadataServiceImpl implements GitRepositoryMetadataService {

    private final GitService gitService;
    private final GitServerConfig gitServerConfig;

    // 在构造函数后添加初始化方法来验证配置
    @PostConstruct
    public void init() {
        log.info("GitRepositoryMetadataServiceImpl 初始化，GitServerConfig: {}", gitServerConfig);
        if (gitServerConfig != null) {
            log.info("GitServerConfig hosts: {}", gitServerConfig.getHosts());
            log.info("GitServerConfig defaultConfig: {}", gitServerConfig.getDefaultConfig());

            // 测试获取gitlab.yeepay.com的配置
            GitServerConfig.ServerConfig yeepayConfig = gitServerConfig.getServerConfig("gitlab.yeepay.com");
            log.info("gitlab.yeepay.com 配置: suggestHttpsOnSshFailure={}, supportsHttps={}, hasPersonalAccessToken={}",
                    yeepayConfig.isSuggestHttpsOnSshFailure(), yeepayConfig.isSupportsHttps(), yeepayConfig.hasPersonalAccessToken());
        } else {
            log.warn("GitServerConfig 为 null！");
        }
    }
    
    // Git URL正则表达式模式
    private static final Pattern HTTPS_PATTERN = Pattern.compile(
        "^https?://([^/]+)/([^/]+)/([^/]+?)(?:\\.git)?/?$"
    );
    private static final Pattern SSH_PATTERN = Pattern.compile(
        "^git@([^:]+):([^/]+)/([^/]+?)(?:\\.git)?/?$"
    );
    
    @Override
    public GitRepositoryInfo validateAndFetchRepositoryInfo(GitRepositoryValidationRequest request) {
        log.info("开始验证Git仓库: {}", request.getRepositoryUrl());

        try {
            // 验证URL格式
            if (!isValidGitUrl(request.getRepositoryUrl())) {
                return GitRepositoryInfo.builder()
                    .success(false)
                    .errorMessage("Git仓库URL格式不正确")
                    .build();
            }

            // 解析基本信息
            String projectName = extractProjectNameFromUrl(request.getRepositoryUrl());
            String owner = extractOwnerFromUrl(request.getRepositoryUrl());
            String repositoryType = determineRepositoryType(request.getRepositoryUrl());

            GitRepositoryInfo.GitRepositoryInfoBuilder builder = GitRepositoryInfo.builder()
                .projectName(projectName)
                .owner(owner)
                .repositoryName(projectName)
                .repositoryType(repositoryType)
                .success(true);

            // 如果需要获取详细信息，尝试克隆仓库
            if (Boolean.TRUE.equals(request.getFetchDetails())) {
                try {
                    fetchDetailedInfo(request, builder);
                } catch (Exception e) {
                    log.warn("获取详细信息失败: {}", e.getMessage());

                    // 如果是SSH认证失败，根据配置提供HTTPS URL建议
                    String errorMessage = "无法获取详细信息: " + e.getMessage();
                    String fullErrorMessage = e.getMessage() != null ? e.getMessage() : "";
                    log.info("检查SSH认证失败: URL={}, 错误信息={}", request.getRepositoryUrl(), fullErrorMessage);

                    if (request.getRepositoryUrl().startsWith("git@") &&
                        (fullErrorMessage.contains("Cannot log in") ||
                         fullErrorMessage.contains("Auth fail") ||
                         fullErrorMessage.contains("authentication") ||
                         fullErrorMessage.contains("No more authentication methods"))) {

                        String httpsUrl = convertSshToHttpsUrlWithConfig(request.getRepositoryUrl());
                        if (httpsUrl != null) {
                            log.info("为SSH URL提供HTTPS转换建议: {} -> {}", request.getRepositoryUrl(), httpsUrl);
                            errorMessage += "。建议尝试使用HTTPS URL: " + httpsUrl;
                        }
                    }

                    // 获取详细信息失败时，返回失败状态
                    return GitRepositoryInfo.builder()
                        .projectName(projectName)
                        .owner(owner)
                        .repositoryName(projectName)
                        .repositoryType(repositoryType)
                        .success(false)
                        .errorMessage(errorMessage)
                        .build();
                }
            } else {
                // 设置默认值
                builder.defaultBranch("main")
                      .branches(Arrays.asList("main", "master", "develop"));
            }
            
            return builder.build();
            
        } catch (Exception e) {
            log.error("验证Git仓库失败: {}", request.getRepositoryUrl(), e);
            return GitRepositoryInfo.builder()
                .success(false)
                .errorMessage("验证仓库失败: " + e.getMessage())
                .build();
        }
    }
    
    private void fetchDetailedInfo(GitRepositoryValidationRequest request,
                                 GitRepositoryInfo.GitRepositoryInfoBuilder builder) throws IOException {
        // 创建临时目录
        Path tempDir = Files.createTempDirectory("archscope_repo_validate_");
        
        try {
            // 准备认证信息
            Map<String, String> credentials = prepareCredentials(request);
            
            // 克隆仓库
            GitOperationResult result = gitService.cloneRepository(
                request.getRepositoryUrl(), tempDir, credentials);
            
            if (result.isSuccess()) {
                // 获取分支信息
                List<String> branches = getBranches(tempDir);
                String defaultBranch = determineDefaultBranch(branches);
                
                builder.branches(branches)
                      .defaultBranch(defaultBranch);
                
                // 尝试读取README文件获取描述
                String description = readReadmeDescription(tempDir);
                if (description != null && !description.isEmpty()) {
                    builder.description(description);
                }
                
            } else {
                throw new RuntimeException("克隆仓库失败: " + result.getMessage());
            }
            
        } finally {
            // 清理临时目录
            cleanupTempDirectory(tempDir);
        }
    }
    
    private List<String> getBranches(Path repoPath) {
        try (Git git = Git.open(repoPath.toFile())) {
            List<Ref> branches = git.branchList()
                .setListMode(ListBranchCommand.ListMode.ALL)
                .call();

            Set<String> branchNames = new HashSet<>();
            for (Ref ref : branches) {
                String name = ref.getName();
                if (name.startsWith("refs/remotes/origin/")) {
                    name = name.substring("refs/remotes/origin/".length());
                    if (!"HEAD".equals(name)) {
                        branchNames.add(name);
                    }
                } else if (name.startsWith("refs/heads/")) {
                    name = name.substring("refs/heads/".length());
                    branchNames.add(name);
                }
            }

            List<String> allBranches = new ArrayList<>(branchNames);
            Collections.sort(allBranches);

            // 应用分支过滤，移除release、feature等分支
            List<String> filteredBranches = BranchFilterUtil.filterBranches(allBranches);
            log.debug("分支过滤结果: 原始分支数={}, 过滤后分支数={}", allBranches.size(), filteredBranches.size());

            return filteredBranches;

        } catch (Exception e) {
            log.warn("获取分支列表失败: {}", e.getMessage());
            return Arrays.asList("main", "master", "develop");
        }
    }
    
    private String determineDefaultBranch(List<String> branches) {
        // 优先级：main > master > develop > 第一个分支
        if (branches.contains("main")) {
            return "main";
        } else if (branches.contains("master")) {
            return "master";
        } else if (branches.contains("develop")) {
            return "develop";
        } else if (!branches.isEmpty()) {
            return branches.get(0);
        }
        return "main";
    }
    
    private String readReadmeDescription(Path repoPath) {
        String[] readmeFiles = {"README.md", "readme.md", "README.txt", "readme.txt", "README"};
        
        for (String filename : readmeFiles) {
            Path readmePath = repoPath.resolve(filename);
            if (Files.exists(readmePath)) {
                try {
                    List<String> lines = Files.readAllLines(readmePath);
                    return extractDescriptionFromReadme(lines);
                } catch (IOException e) {
                    log.warn("读取README文件失败: {}", e.getMessage());
                }
            }
        }
        
        return null;
    }
    
    private String extractDescriptionFromReadme(List<String> lines) {
        StringBuilder description = new StringBuilder();
        boolean foundTitle = false;
        int lineCount = 0;
        
        for (String line : lines) {
            line = line.trim();
            
            // 跳过空行和标题行
            if (line.isEmpty() || line.startsWith("#")) {
                if (line.startsWith("#")) {
                    foundTitle = true;
                }
                continue;
            }
            
            // 如果找到标题后的第一个非空行，作为描述
            if (foundTitle && !line.isEmpty()) {
                description.append(line);
                lineCount++;
                
                // 最多取3行作为描述
                if (lineCount >= 3) {
                    break;
                }
                
                description.append(" ");
            }
        }
        
        String result = description.toString().trim();
        // 限制描述长度
        if (result.length() > 200) {
            result = result.substring(0, 200) + "...";
        }
        
        return result.isEmpty() ? null : result;
    }
    
    private void cleanupTempDirectory(Path tempDir) {
        try {
            Files.walk(tempDir)
                .sorted(Comparator.reverseOrder())
                .map(Path::toFile)
                .forEach(File::delete);
        } catch (IOException e) {
            log.warn("清理临时目录失败: {}", tempDir, e);
        }
    }
    
    @Override
    public String extractProjectNameFromUrl(String repositoryUrl) {
        Matcher httpsMatcher = HTTPS_PATTERN.matcher(repositoryUrl);
        if (httpsMatcher.matches()) {
            return httpsMatcher.group(3);
        }
        
        Matcher sshMatcher = SSH_PATTERN.matcher(repositoryUrl);
        if (sshMatcher.matches()) {
            return sshMatcher.group(3);
        }
        
        // 如果正则匹配失败，尝试简单解析
        String[] parts = repositoryUrl.replaceAll("\\.git$", "").split("/");
        if (parts.length > 0) {
            return parts[parts.length - 1];
        }
        
        return "unknown-project";
    }
    
    @Override
    public String extractOwnerFromUrl(String repositoryUrl) {
        Matcher httpsMatcher = HTTPS_PATTERN.matcher(repositoryUrl);
        if (httpsMatcher.matches()) {
            return httpsMatcher.group(2);
        }
        
        Matcher sshMatcher = SSH_PATTERN.matcher(repositoryUrl);
        if (sshMatcher.matches()) {
            return sshMatcher.group(2);
        }
        
        return "unknown-owner";
    }
    
    @Override
    public String determineRepositoryType(String repositoryUrl) {
        if (repositoryUrl.contains("github.com")) {
            return "GitHub";
        } else if (repositoryUrl.contains("gitlab.com") || repositoryUrl.contains("gitlab")) {
            return "GitLab";
        } else if (repositoryUrl.contains("bitbucket.org")) {
            return "Bitbucket";
        } else {
            return "Git";
        }
    }
    
    @Override
    public boolean isValidGitUrl(String repositoryUrl) {
        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            return false;
        }

        return HTTPS_PATTERN.matcher(repositoryUrl).matches() ||
               SSH_PATTERN.matcher(repositoryUrl).matches();
    }

    /**
     * 准备Git认证信息
     * 优先级：
     * 1. 请求中提供的用户名密码
     * 2. 配置文件中的个人访问令牌
     * 3. 空认证信息（用于公开仓库）
     *
     * @param request Git仓库验证请求
     * @return 认证信息Map
     */
    private Map<String, String> prepareCredentials(GitRepositoryValidationRequest request) {
        Map<String, String> credentials = new HashMap<>();

        // 优先使用请求中提供的认证信息
        if (request.getUsername() != null && request.getPassword() != null) {
            credentials.put("username", request.getUsername());
            credentials.put("password", request.getPassword());
            log.info("使用请求中提供的认证信息: username={}", request.getUsername());
            return credentials;
        }

        // 尝试使用配置文件中的个人访问令牌
        String host = extractHostFromUrl(request.getRepositoryUrl());
        if (host != null && gitServerConfig != null) {
            GitServerConfig.ServerConfig serverConfig = gitServerConfig.getServerConfig(host);
            if (serverConfig.hasPersonalAccessToken()) {
                credentials.put("username", serverConfig.getTokenUsername());
                credentials.put("password", serverConfig.getPersonalAccessToken());
                log.info("使用配置的个人访问令牌进行认证: host={}, tokenUsername={}",
                        host, serverConfig.getTokenUsername());
                return credentials;
            }
        }

        log.info("未找到认证信息，尝试匿名访问: {}", request.getRepositoryUrl());
        return credentials; // 返回空的credentials用于公开仓库
    }

    /**
     * 从Git URL中提取主机名
     *
     * @param repositoryUrl Git仓库URL
     * @return 主机名，如果解析失败则返回null
     */
    private String extractHostFromUrl(String repositoryUrl) {
        if (repositoryUrl == null) {
            return null;
        }

        // 尝试匹配HTTPS URL
        Matcher httpsMatcher = HTTPS_PATTERN.matcher(repositoryUrl);
        if (httpsMatcher.matches()) {
            return httpsMatcher.group(1);
        }

        // 尝试匹配SSH URL
        Matcher sshMatcher = SSH_PATTERN.matcher(repositoryUrl);
        if (sshMatcher.matches()) {
            return sshMatcher.group(1);
        }

        log.warn("无法从仓库URL中提取主机名: {}", repositoryUrl);
        return null;
    }

    /**
     * 根据配置将SSH格式的Git URL转换为HTTPS格式
     *
     * @param sshUrl SSH格式的Git URL
     * @return HTTPS格式的Git URL，如果转换失败或配置不允许则返回null
     */
    private String convertSshToHttpsUrlWithConfig(String sshUrl) {
        if (sshUrl == null || !sshUrl.startsWith("git@")) {
            return null;
        }

        log.info("开始处理SSH URL转换: {}", sshUrl);
        log.info("GitServerConfig 对象: {}", gitServerConfig);

        if (gitServerConfig == null) {
            log.error("GitServerConfig 为 null，无法进行配置化转换，回退到默认转换");
            return convertSshToHttpsUrl(sshUrl);
        }

        Matcher sshMatcher = SSH_PATTERN.matcher(sshUrl);
        if (sshMatcher.matches()) {
            String host = sshMatcher.group(1);
            String owner = sshMatcher.group(2);
            String repo = sshMatcher.group(3);

            log.info("解析SSH URL: host={}, owner={}, repo={}", host, owner, repo);

            // 获取服务器配置
            GitServerConfig.ServerConfig serverConfig = gitServerConfig.getServerConfig(host);
            log.info("获取服务器 {} 的配置: suggestHttpsOnSshFailure={}, supportsHttps={}",
                    host, serverConfig.isSuggestHttpsOnSshFailure(), serverConfig.isSupportsHttps());

            // 检查是否应该提供HTTPS转换建议
            if (!serverConfig.isSuggestHttpsOnSshFailure()) {
                log.info("服务器 {} 配置为不提供HTTPS转换建议", host);
                return null;
            }

            // 检查服务器是否支持HTTPS
            if (!serverConfig.isSupportsHttps()) {
                log.debug("服务器 {} 不支持HTTPS协议", host);
                return null;
            }

            // 使用配置的URL模板进行转换
            String httpsUrlTemplate = serverConfig.getHttpsUrlTemplate();
            String result = httpsUrlTemplate
                .replace("{host}", host)
                .replace("{owner}", owner)
                .replace("{repo}", repo);

            log.info("转换结果: {}", result);
            return result;
        }

        log.warn("SSH URL 格式不匹配: {}", sshUrl);
        return null;
    }

    /**
     * 将SSH格式的Git URL转换为HTTPS格式（保留原方法用于向后兼容）
     *
     * @param sshUrl SSH格式的Git URL
     * @return HTTPS格式的Git URL，如果转换失败则返回null
     * @deprecated 使用 {@link #convertSshToHttpsUrlWithConfig(String)} 替代
     */
    @Deprecated
    private String convertSshToHttpsUrl(String sshUrl) {
        if (sshUrl == null || !sshUrl.startsWith("git@")) {
            return null;
        }

        Matcher sshMatcher = SSH_PATTERN.matcher(sshUrl);
        if (sshMatcher.matches()) {
            String host = sshMatcher.group(1);
            String owner = sshMatcher.group(2);
            String repo = sshMatcher.group(3);

            return String.format("https://%s/%s/%s.git", host, owner, repo);
        }

        return null;
    }
}

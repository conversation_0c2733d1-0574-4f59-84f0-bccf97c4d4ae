package com.archscope.domain.service.impl;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.DocumentVersionRepository;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.DocumentSearchService;
import com.archscope.domain.service.MarkdownService;
import com.archscope.domain.service.StaticSiteGenerationService;
import com.archscope.domain.valueobject.DocumentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 静态站点生成服务默认实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultStaticSiteGenerationService implements StaticSiteGenerationService {

    private final MarkdownService markdownService;
    private final ProjectRepository projectRepository;
    private final DocumentVersionRepository documentVersionRepository;
    private final TemplateEngine templateEngine;
    private final DocumentSearchService documentSearchService;

    @Value("${static.site.base.url:http://localhost:8080/docs}")
    private String staticSiteBaseUrl;

    @Value("${static.resources.path:classpath:/static}")
    private String staticResourcesPath;

    @Override
    public Path generateSite(List<DocumentVersion> documentVersions, Path outputDir) {
        log.info("开始生成静态站点，文档版本数量: {}, 输出目录: {}", documentVersions.size(), outputDir);

        try {
            // 创建输出目录
            Files.createDirectories(outputDir);

            // 按项目分组
            Map<Long, List<DocumentVersion>> versionsByProject = documentVersions.stream()
                    .collect(Collectors.groupingBy(DocumentVersion::getProjectId));

            // 为每个项目生成文档
            for (Map.Entry<Long, List<DocumentVersion>> entry : versionsByProject.entrySet()) {
                Long projectId = entry.getKey();
                List<DocumentVersion> projectVersions = entry.getValue();

                // 创建项目目录
                Path projectDir = outputDir.resolve(projectId.toString());
                Files.createDirectories(projectDir);

                // 生成项目首页
                generateProjectIndex(projectId, projectVersions, projectDir);

                // 生成每个文档页面
                for (DocumentVersion version : projectVersions) {
                    generatePage(version, projectDir);
                }
            }

            // 生成搜索索引
            generateSearchIndex(documentVersions, outputDir);

            // 为每个项目生成搜索页面和比较页面
            for (Long projectId : versionsByProject.keySet()) {
                Path projectDir = outputDir.resolve(projectId.toString());

                // 生成搜索页面
                generateSearchPage(projectId, projectDir);

                // 生成比较页面模板
                // 获取项目的所有文档类型
                Map<DocumentType, List<DocumentVersion>> versionsByType = versionsByProject.get(projectId).stream()
                        .collect(Collectors.groupingBy(DocumentVersion::getDocType));

                // 对每种文档类型，生成一个默认的比较页面
                for (Map.Entry<DocumentType, List<DocumentVersion>> entry : versionsByType.entrySet()) {
                    List<DocumentVersion> typeVersions = entry.getValue();
                    if (typeVersions.size() >= 2) {
                        // 按时间排序，选择最新的两个版本进行比较
                        typeVersions.sort(Comparator.comparing(DocumentVersion::getTimestamp).reversed());
                        DocumentVersion newest = typeVersions.get(0);
                        DocumentVersion secondNewest = typeVersions.get(1);

                        generateComparePage(
                                projectId,
                                entry.getKey().name().toLowerCase(),
                                secondNewest.getVersionTag(),
                                newest.getVersionTag(),
                                projectDir
                        );
                    }
                }
            }

            // 复制静态资源
            copyStaticResources(outputDir);

            log.info("静态站点生成完成，输出目录: {}", outputDir);
            return outputDir;

        } catch (Exception e) {
            log.error("生成静态站点失败", e);
            throw new RuntimeException("生成静态站点失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Path generatePage(DocumentVersion documentVersion, Path outputDir) {
        log.info("生成文档页面，文档ID: {}, 输出目录: {}", documentVersion.getId(), outputDir);

        try {
            // 创建输出目录
            Files.createDirectories(outputDir);

            // 读取文档内容
            String contentPath = documentVersion.getContentPath();
            if (!StringUtils.hasText(contentPath)) {
                throw new IllegalArgumentException("文档内容路径为空");
            }

            Path contentFilePath = Paths.get(contentPath);
            if (!Files.exists(contentFilePath)) {
                throw new IllegalArgumentException("文档内容文件不存在: " + contentPath);
            }

            String markdown = new String(Files.readAllBytes(contentFilePath), StandardCharsets.UTF_8);

            // 提取标题
            String title = markdownService.extractTitle(markdown);

            // 添加额外的头部元素
            Map<String, String> additionalHeadElements = new HashMap<>();

            // 转换Markdown为HTML
            String htmlContent = markdownService.convertToHtml(markdown, additionalHeadElements);

            // 获取项目信息
            Optional<Project> projectOpt = projectRepository.findById(documentVersion.getProjectId());
            if (!projectOpt.isPresent()) {
                throw new IllegalArgumentException("项目不存在: " + documentVersion.getProjectId());
            }
            Project project = projectOpt.get();

            // 获取同一项目的所有文档版本
            List<DocumentVersion> allVersions = documentVersionRepository.findByProjectIdAndDocType(
                    documentVersion.getProjectId(), documentVersion.getDocType());

            // 准备Thymeleaf上下文
            Context context = new Context();
            context.setVariable("title", title);
            context.setVariable("content", htmlContent);
            context.setVariable("version", documentVersion.getVersionTag());
            context.setVariable("lastUpdated", documentVersion.getLastModified());
            context.setVariable("project", project);
            context.setVariable("versions", allVersions);
            context.setVariable("currentDocType", documentVersion.getDocType());

            // 添加额外的头部元素
            context.setVariable("additionalHeadElements", additionalHeadElements);

            // 使用Thymeleaf模板生成HTML
            String html = templateEngine.process("document", context);

            // 确定输出文件路径
            String fileName = documentVersion.getDocType().name().toLowerCase() + ".html";
            Path outputFile = outputDir.resolve(fileName);

            // 写入文件
            Files.write(outputFile, html.getBytes(StandardCharsets.UTF_8));

            log.info("文档页面生成完成: {}", outputFile);
            return outputFile;

        } catch (Exception e) {
            log.error("生成文档页面失败", e);
            throw new RuntimeException("生成文档页面失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Path generateProjectIndex(Long projectId, List<DocumentVersion> documentVersions, Path outputDir) {
        log.info("生成项目首页，项目ID: {}, 文档版本数量: {}, 输出目录: {}",
                projectId, documentVersions.size(), outputDir);

        try {
            // 创建输出目录
            Files.createDirectories(outputDir);

            // 获取项目信息
            Optional<Project> projectOpt = projectRepository.findById(projectId);
            if (!projectOpt.isPresent()) {
                throw new IllegalArgumentException("项目不存在: " + projectId);
            }

            Project project = projectOpt.get();

            // 准备Thymeleaf上下文
            Context context = new Context();
            context.setVariable("project", project);
            context.setVariable("documentVersions", documentVersions);
            context.setVariable("generatedTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 使用Thymeleaf模板生成HTML
            String html = templateEngine.process("project-index", context);

            // 确定输出文件路径
            Path outputFile = outputDir.resolve("index.html");

            // 写入文件
            Files.write(outputFile, html.getBytes(StandardCharsets.UTF_8));

            log.info("项目首页生成完成: {}", outputFile);
            return outputFile;

        } catch (Exception e) {
            log.error("生成项目首页失败", e);
            throw new RuntimeException("生成项目首页失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean copyStaticResources(Path outputDir) {
        log.info("复制静态资源到输出目录: {}", outputDir);

        try {
            // 创建静态资源目录
            Path cssDir = outputDir.resolve("css");
            Path jsDir = outputDir.resolve("js");
            Path imagesDir = outputDir.resolve("images");

            Files.createDirectories(cssDir);
            Files.createDirectories(jsDir);
            Files.createDirectories(imagesDir);

            // 复制CSS文件
            copyResource("/static/css/style.css", cssDir.resolve("style.css"));
            copyResource("/static/css/prism.css", cssDir.resolve("prism.css"));
            copyResource("/static/css/search.css", cssDir.resolve("search.css"));

            // 复制JS文件
            copyResource("/static/js/prism.js", jsDir.resolve("prism.js"));
            copyResource("/static/js/lunr.min.js", jsDir.resolve("lunr.min.js"));
            copyResource("/static/js/search.js", jsDir.resolve("search.js"));

            // 复制图片文件
            copyResource("/static/images/logo.png", imagesDir.resolve("logo.png"));

            log.info("静态资源复制完成");
            return true;

        } catch (Exception e) {
            log.error("复制静态资源失败", e);
            return false;
        }
    }

    @Override
    public String getSiteUrl(DocumentVersion documentVersion) {
        return staticSiteBaseUrl + "/" +
               documentVersion.getProjectId() + "/" +
               documentVersion.getDocType().name().toLowerCase() + ".html";
    }

    @Override
    public Path generateComparePage(Long projectId, String docType, String oldVersion, String newVersion, Path outputDir) {
        log.info("生成文档版本比较页面，项目ID: {}, 文档类型: {}, 旧版本: {}, 新版本: {}, 输出目录: {}",
                projectId, docType, oldVersion, newVersion, outputDir);

        try {
            // 创建输出目录
            Files.createDirectories(outputDir);

            // 获取项目信息
            Optional<Project> projectOpt = projectRepository.findById(projectId);
            if (!projectOpt.isPresent()) {
                throw new IllegalArgumentException("项目不存在: " + projectId);
            }
            Project project = projectOpt.get();

            // 获取文档类型
            DocumentType documentType = null;
            for (DocumentType type : DocumentType.values()) {
                if (type.name().toLowerCase().equals(docType)) {
                    documentType = type;
                    break;
                }
            }
            if (documentType == null) {
                throw new IllegalArgumentException("无效的文档类型: " + docType);
            }

            // 获取所有版本
            List<DocumentVersion> allVersions = documentVersionRepository.findByProjectIdAndDocType(projectId, documentType);

            // 获取旧版本文档
            Optional<DocumentVersion> oldDocOpt = allVersions.stream()
                    .filter(v -> v.getVersionTag().equals(oldVersion))
                    .findFirst();
            if (!oldDocOpt.isPresent()) {
                throw new IllegalArgumentException("旧版本文档不存在: " + oldVersion);
            }
            DocumentVersion oldDoc = oldDocOpt.get();

            // 获取新版本文档
            Optional<DocumentVersion> newDocOpt = allVersions.stream()
                    .filter(v -> v.getVersionTag().equals(newVersion))
                    .findFirst();
            if (!newDocOpt.isPresent()) {
                throw new IllegalArgumentException("新版本文档不存在: " + newVersion);
            }
            DocumentVersion newDoc = newDocOpt.get();

            // 读取文档内容
            String oldMarkdown = new String(Files.readAllBytes(Paths.get(oldDoc.getContentPath())), StandardCharsets.UTF_8);
            String newMarkdown = new String(Files.readAllBytes(Paths.get(newDoc.getContentPath())), StandardCharsets.UTF_8);

            // 转换Markdown为HTML
            Map<String, String> oldHeadElements = new HashMap<>();
            Map<String, String> newHeadElements = new HashMap<>();
            String oldHtml = markdownService.convertToHtml(oldMarkdown, oldHeadElements);
            String newHtml = markdownService.convertToHtml(newMarkdown, newHeadElements);

            // 准备Thymeleaf上下文
            Context context = new Context();
            context.setVariable("project", project);
            context.setVariable("versions", allVersions);
            context.setVariable("selectedDocType", docType);
            context.setVariable("oldVersion", oldVersion);
            context.setVariable("newVersion", newVersion);
            context.setVariable("oldContent", oldHtml);
            context.setVariable("newContent", newHtml);

            // 使用Thymeleaf模板生成HTML
            String html = templateEngine.process("compare", context);

            // 确定输出文件路径
            Path outputFile = outputDir.resolve("compare.html");

            // 写入文件
            Files.write(outputFile, html.getBytes(StandardCharsets.UTF_8));

            log.info("文档版本比较页面生成完成: {}", outputFile);
            return outputFile;

        } catch (Exception e) {
            log.error("生成文档版本比较页面失败", e);
            throw new RuntimeException("生成文档版本比较页面失败: " + e.getMessage(), e);
        }
    }

    /**
     * 复制资源文件
     *
     * @param resourcePath 资源路径
     * @param targetPath 目标路径
     * @throws IOException 如果复制失败
     */
    public void copyResource(String resourcePath, Path targetPath) throws IOException {
        InputStream inputStream = getClass().getResourceAsStream(resourcePath);
        if (inputStream == null) {
            log.warn("资源文件不存在: {}", resourcePath);
            return;
        }

        Files.copy(inputStream, targetPath, StandardCopyOption.REPLACE_EXISTING);
        inputStream.close();
    }

    @Override
    public Path generateSearchPage(Long projectId, Path outputDir) {
        log.info("生成搜索页面，项目ID: {}, 输出目录: {}", projectId, outputDir);

        try {
            // 创建输出目录
            Files.createDirectories(outputDir);

            // 获取项目信息
            Optional<Project> projectOpt = projectRepository.findById(projectId);
            if (!projectOpt.isPresent()) {
                throw new IllegalArgumentException("项目不存在: " + projectId);
            }

            Project project = projectOpt.get();

            // 准备Thymeleaf上下文
            Context context = new Context();
            context.setVariable("project", project);
            context.setVariable("generatedTime", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

            // 使用Thymeleaf模板生成HTML
            String html = templateEngine.process("search", context);

            // 确定输出文件路径
            Path outputFile = outputDir.resolve("search.html");

            // 写入文件
            Files.write(outputFile, html.getBytes(StandardCharsets.UTF_8));

            log.info("搜索页面生成完成: {}", outputFile);
            return outputFile;

        } catch (Exception e) {
            log.error("生成搜索页面失败", e);
            throw new RuntimeException("生成搜索页面失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Path generateSearchIndex(List<DocumentVersion> documentVersions, Path outputDir) {
        log.info("生成搜索索引，文档版本数量: {}, 输出目录: {}", documentVersions.size(), outputDir);
        return documentSearchService.generateSearchIndex(documentVersions, outputDir);
    }
}

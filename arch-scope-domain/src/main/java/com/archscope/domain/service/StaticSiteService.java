package com.archscope.domain.service;

import com.archscope.domain.entity.DocumentVersion;

import java.nio.file.Path;
import java.util.List;

/**
 * 静态站点服务接口
 */
public interface StaticSiteService {
    
    /**
     * 生成项目静态站点
     *
     * @param projectId 项目ID
     * @param documentVersions 文档版本列表
     * @return 生成的站点路径
     */
    Path generateProjectSite(Long projectId, List<DocumentVersion> documentVersions);
    
    /**
     * 生成自定义静态站点
     *
     * @param documentVersions 文档版本列表
     * @param outputPath 输出路径
     * @return 生成的站点路径
     */
    Path generateCustomSite(List<DocumentVersion> documentVersions, String outputPath);
}

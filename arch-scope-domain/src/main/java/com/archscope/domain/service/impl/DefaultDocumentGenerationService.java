package com.archscope.domain.service.impl;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.service.DocumentGenerationService;
import com.archscope.domain.service.DocumentVersionService;
import com.archscope.domain.valueobject.DocumentType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;

/**
 * 文档生成服务默认实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultDocumentGenerationService implements DocumentGenerationService {

    private final DocumentVersionService documentVersionService;

    private static final List<String> SUPPORTED_OUTPUT_FORMATS = Arrays.asList("markdown", "html", "pdf");
    private static final List<String> AVAILABLE_TEMPLATES = Arrays.asList("default", "minimal", "detailed");

    @Override
    public String generateDocumentation(
            Long repositoryId,
            String commitId,
            List<DocumentType> docTypes,
            String templateId,
            String outputFormat,
            String outputPath,
            boolean includeArchDiagrams) {

        log.info("开始生成文档，仓库ID: {}, 提交ID: {}, 文档类型: {}, 模板: {}, 输出格式: {}, 包含架构图: {}",
                repositoryId, commitId, docTypes, templateId, outputFormat, includeArchDiagrams);

        // 验证参数
        if (!SUPPORTED_OUTPUT_FORMATS.contains(outputFormat)) {
            throw new IllegalArgumentException("不支持的输出格式: " + outputFormat);
        }

        if (!AVAILABLE_TEMPLATES.contains(templateId)) {
            throw new IllegalArgumentException("不存在的模板ID: " + templateId);
        }

        try {
            // 创建输出目录
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String finalOutputPath = outputPath;

            if (finalOutputPath == null || finalOutputPath.isEmpty()) {
                finalOutputPath = "docs/generated/" + repositoryId + "/" + timestamp;
            }

            Path outputDir = Paths.get(finalOutputPath);
            Files.createDirectories(outputDir);

            // 模拟文档生成过程
            log.info("生成文档中...");
            Thread.sleep(2000); // 模拟处理时间

            // 对每种文档类型生成文档
            for (DocumentType docType : docTypes) {
                log.info("生成 {} 文档", docType.getDisplayName());
                String fileName = docType.name().toLowerCase() + "." + outputFormat;
                Path filePath = outputDir.resolve(fileName);

                // 这里应该是实际的文档生成逻辑
                // 为了演示，我们创建一个简单的文档文件
                createDummyDocumentFile(filePath, docType, commitId);

                log.info("文档已生成: {}", filePath);

                // 创建文档版本记录
                createDocumentVersion(repositoryId, commitId, docType, filePath.toString());

                Thread.sleep(1000); // 模拟处理时间
            }

            // 如果需要生成架构图
            if (includeArchDiagrams) {
                log.info("生成架构图");
                Path diagramsDir = outputDir.resolve("diagrams");
                Files.createDirectories(diagramsDir);

                // 这里应该是实际的架构图生成逻辑
                // 为了演示，我们创建一个简单的架构图文件
                Path diagramPath = diagramsDir.resolve("architecture." + outputFormat);
                createDummyDiagramFile(diagramPath);

                log.info("架构图已生成: {}", diagramsDir);

                Thread.sleep(1000); // 模拟处理时间
            }

            log.info("文档生成完成，输出路径: {}", outputDir);
            return outputDir.toString();

        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            log.error("文档生成被中断", e);
            throw new RuntimeException("文档生成被中断", e);
        } catch (Exception e) {
            log.error("文档生成失败", e);
            throw new RuntimeException("文档生成失败: " + e.getMessage(), e);
        }
    }

    @Override
    public List<String> getAvailableTemplates() {
        return AVAILABLE_TEMPLATES;
    }

    @Override
    public List<String> getSupportedOutputFormats() {
        return SUPPORTED_OUTPUT_FORMATS;
    }

    /**
     * 创建文档版本记录
     *
     * @param repositoryId 仓库ID
     * @param commitId 提交ID
     * @param docType 文档类型
     * @param contentPath 内容路径
     * @return 文档版本
     */
    private DocumentVersion createDocumentVersion(Long repositoryId, String commitId, DocumentType docType, String contentPath) {
        log.info("创建文档版本记录，仓库ID: {}, 提交ID: {}, 文档类型: {}, 内容路径: {}",
                repositoryId, commitId, docType, contentPath);

        // 创建文档版本对象
        DocumentVersion documentVersion = DocumentVersion.builder()
                .projectId(repositoryId)
                .commitId(commitId)
                .docType(docType)
                .contentPath(contentPath)
                .timestamp(LocalDateTime.now())
                .title(docType.getDisplayName() + " 文档")
                .description("自动生成的" + docType.getDisplayName() + "文档")
                .author("系统")
                .lastModified(LocalDateTime.now())
                .isPublished(false)
                .status("DRAFT")
                .build();

        // 生成版本标签
        String versionTag = documentVersionService.generateVersionTag(repositoryId, docType);
        documentVersion.setVersionTag(versionTag);

        // 保存文档版本
        return documentVersionService.createDocumentVersion(documentVersion);
    }

    /**
     * 创建示例文档文件
     *
     * @param filePath 文件路径
     * @param docType 文档类型
     * @param commitId 提交ID
     * @throws IOException 如果文件创建失败
     */
    private void createDummyDocumentFile(Path filePath, DocumentType docType, String commitId) throws IOException {
        // 创建父目录
        Files.createDirectories(filePath.getParent());

        // 根据输出格式创建不同的文件内容
        String extension = filePath.toString().substring(filePath.toString().lastIndexOf('.') + 1);
        String content;

        if ("markdown".equals(extension) || "md".equals(extension)) {
            content = "# " + docType.getDisplayName() + "\n\n" +
                    "## 版本信息\n\n" +
                    "- 提交ID: " + commitId + "\n" +
                    "- 生成时间: " + LocalDateTime.now() + "\n\n" +
                    "## 内容\n\n" +
                    "这是自动生成的" + docType.getDisplayName() + "文档。\n\n" +
                    "### 示例章节\n\n" +
                    "这里是示例内容。\n";
        } else if ("html".equals(extension)) {
            content = "<!DOCTYPE html>\n" +
                    "<html>\n" +
                    "<head>\n" +
                    "    <title>" + docType.getDisplayName() + "</title>\n" +
                    "</head>\n" +
                    "<body>\n" +
                    "    <h1>" + docType.getDisplayName() + "</h1>\n" +
                    "    <h2>版本信息</h2>\n" +
                    "    <ul>\n" +
                    "        <li>提交ID: " + commitId + "</li>\n" +
                    "        <li>生成时间: " + LocalDateTime.now() + "</li>\n" +
                    "    </ul>\n" +
                    "    <h2>内容</h2>\n" +
                    "    <p>这是自动生成的" + docType.getDisplayName() + "文档。</p>\n" +
                    "    <h3>示例章节</h3>\n" +
                    "    <p>这里是示例内容。</p>\n" +
                    "</body>\n" +
                    "</html>";
        } else {
            content = docType.getDisplayName() + "\n\n" +
                    "版本信息:\n" +
                    "- 提交ID: " + commitId + "\n" +
                    "- 生成时间: " + LocalDateTime.now() + "\n\n" +
                    "内容:\n" +
                    "这是自动生成的" + docType.getDisplayName() + "文档。\n\n" +
                    "示例章节:\n" +
                    "这里是示例内容。\n";
        }

        // 写入文件
        Files.write(filePath, content.getBytes(StandardCharsets.UTF_8));
    }

    /**
     * 创建示例架构图文件
     *
     * @param filePath 文件路径
     * @throws IOException 如果文件创建失败
     */
    private void createDummyDiagramFile(Path filePath) throws IOException {
        // 创建父目录
        Files.createDirectories(filePath.getParent());

        // 根据输出格式创建不同的文件内容
        String extension = filePath.toString().substring(filePath.toString().lastIndexOf('.') + 1);
        String content;

        if ("markdown".equals(extension) || "md".equals(extension)) {
            content = "# 架构图\n\n" +
                    "```mermaid\n" +
                    "graph TD\n" +
                    "    A[客户端] --> B[API网关]\n" +
                    "    B --> C[微服务1]\n" +
                    "    B --> D[微服务2]\n" +
                    "    C --> E[数据库]\n" +
                    "    D --> E\n" +
                    "```\n";
        } else if ("html".equals(extension)) {
            content = "<!DOCTYPE html>\n" +
                    "<html>\n" +
                    "<head>\n" +
                    "    <title>架构图</title>\n" +
                    "</head>\n" +
                    "<body>\n" +
                    "    <h1>架构图</h1>\n" +
                    "    <pre>\n" +
                    "    +----------+     +----------+\n" +
                    "    | 客户端   | --> | API网关  |\n" +
                    "    +----------+     +----------+\n" +
                    "                         |\n" +
                    "                         v\n" +
                    "        +----------+     +----------+\n" +
                    "        | 微服务1  | --> | 数据库   |\n" +
                    "        +----------+     +----------+\n" +
                    "                         ^\n" +
                    "                         |\n" +
                    "        +----------+     \n" +
                    "        | 微服务2  | ----+\n" +
                    "        +----------+     \n" +
                    "    </pre>\n" +
                    "</body>\n" +
                    "</html>";
        } else {
            content = "架构图\n\n" +
                    "+----------+     +----------+\n" +
                    "| 客户端   | --> | API网关  |\n" +
                    "+----------+     +----------+\n" +
                    "                     |\n" +
                    "                     v\n" +
                    "    +----------+     +----------+\n" +
                    "    | 微服务1  | --> | 数据库   |\n" +
                    "    +----------+     +----------+\n" +
                    "                     ^\n" +
                    "                     |\n" +
                    "    +----------+     \n" +
                    "    | 微服务2  | ----+\n" +
                    "    +----------+     \n";
        }

        // 写入文件
        Files.write(filePath, content.getBytes(StandardCharsets.UTF_8));
    }
}

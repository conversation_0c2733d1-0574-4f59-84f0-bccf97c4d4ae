package com.archscope.domain.service;

import com.archscope.domain.model.parser.FileParseResult;

import java.io.File;
import java.util.List;

/**
 * 增量解析服务接口
 * 负责管理代码仓库的增量解析，只解析变更的文件，提高解析效率
 */
public interface IncrementalParseService {
    
    /**
     * 执行增量解析
     * 
     * @param repositoryId 代码仓库ID
     * @param fromCommit 起始提交ID
     * @param toCommit 目标提交ID
     * @return 解析结果列表
     */
    List<FileParseResult> parseIncrementally(Long repositoryId, String fromCommit, String toCommit);
    
    /**
     * 获取代码仓库的变更文件
     * 
     * @param repositoryId 代码仓库ID
     * @param fromCommit 起始提交ID
     * @param toCommit 目标提交ID
     * @return 变更文件列表
     */
    List<File> getChangedFiles(Long repositoryId, String fromCommit, String toCommit);
    
    /**
     * 从缓存中获取解析结果
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @return 解析结果列表
     */
    List<FileParseResult> getParseResultsFromCache(Long repositoryId, String commitId);
    
    /**
     * 将解析结果保存到缓存
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @param parseResults 解析结果列表
     */
    void saveParseResultsToCache(Long repositoryId, String commitId, List<FileParseResult> parseResults);
    
    /**
     * 合并解析结果
     * 
     * @param existingResults 现有解析结果
     * @param newResults 新的解析结果
     * @return 合并后的解析结果
     */
    List<FileParseResult> mergeParseResults(List<FileParseResult> existingResults, List<FileParseResult> newResults);
    
    /**
     * 清除指定仓库的解析缓存
     * 
     * @param repositoryId 代码仓库ID
     */
    void clearCache(Long repositoryId);
    
    /**
     * 获取文件的最后修改提交
     * 
     * @param repositoryId 代码仓库ID
     * @param filePath 文件路径
     * @return 最后修改提交ID
     */
    String getLastModifiedCommit(Long repositoryId, String filePath);
}

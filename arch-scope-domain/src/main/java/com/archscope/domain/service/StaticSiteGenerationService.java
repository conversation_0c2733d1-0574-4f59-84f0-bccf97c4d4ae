package com.archscope.domain.service;

import com.archscope.domain.entity.DocumentVersion;

import java.nio.file.Path;
import java.util.List;

/**
 * 静态站点生成服务接口
 * 负责将文档转换为静态站点
 */
public interface StaticSiteGenerationService {

    /**
     * 生成静态站点
     *
     * @param documentVersions 文档版本列表
     * @param outputDir 输出目录
     * @return 生成的站点根目录路径
     */
    Path generateSite(List<DocumentVersion> documentVersions, Path outputDir);

    /**
     * 生成单个文档页面
     *
     * @param documentVersion 文档版本
     * @param outputDir 输出目录
     * @return 生成的页面路径
     */
    Path generatePage(DocumentVersion documentVersion, Path outputDir);

    /**
     * 生成项目首页
     *
     * @param projectId 项目ID
     * @param documentVersions 文档版本列表
     * @param outputDir 输出目录
     * @return 生成的首页路径
     */
    Path generateProjectIndex(Long projectId, List<DocumentVersion> documentVersions, Path outputDir);

    /**
     * 复制静态资源
     *
     * @param outputDir 输出目录
     * @return 是否成功
     */
    boolean copyStaticResources(Path outputDir);

    /**
     * 生成搜索页面
     *
     * @param projectId 项目ID
     * @param outputDir 输出目录
     * @return 生成的搜索页面路径
     */
    Path generateSearchPage(Long projectId, Path outputDir);

    /**
     * 生成搜索索引
     *
     * @param documentVersions 文档版本列表
     * @param outputDir 输出目录
     * @return 生成的索引文件路径
     */
    Path generateSearchIndex(List<DocumentVersion> documentVersions, Path outputDir);

    /**
     * 获取站点URL
     *
     * @param documentVersion 文档版本
     * @return 站点URL
     */
    String getSiteUrl(DocumentVersion documentVersion);

    /**
     * 生成文档版本比较页面
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @param oldVersion 旧版本
     * @param newVersion 新版本
     * @param outputDir 输出目录
     * @return 生成的比较页面路径
     */
    Path generateComparePage(Long projectId, String docType, String oldVersion, String newVersion, Path outputDir);
}

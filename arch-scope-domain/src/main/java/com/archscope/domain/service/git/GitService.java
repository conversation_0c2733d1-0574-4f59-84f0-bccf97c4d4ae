package com.archscope.domain.service.git;

import java.nio.file.Path;
import java.util.Map;

/**
 * Git 仓库操作服务接口
 * 提供对远程 Git 仓库的克隆和获取变更等基本操作
 */
public interface GitService {

    /**
     * 克隆远程 Git 仓库到本地
     *
     * @param repositoryUrl 远程仓库 URL (https 或 ssh 格式)
     * @param localPath 本地存储路径
     * @param credentials 认证信息 (可选，用于私有仓库)
     * @return 操作结果，包含成功/失败状态和相关信息
     */
    GitOperationResult cloneRepository(String repositoryUrl, Path localPath, Map<String, String> credentials);

    /**
     * 获取远程仓库的最新变更
     *
     * @param localPath 本地仓库路径
     * @param remoteName 远程仓库名称 (默认为 "origin")
     * @param credentials 认证信息 (可选，用于私有仓库)
     * @return 操作结果，包含成功/失败状态和相关信息
     */
    GitOperationResult fetchChanges(Path localPath, String remoteName, Map<String, String> credentials);
}

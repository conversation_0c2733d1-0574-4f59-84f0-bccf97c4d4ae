package com.archscope.domain.service;

import com.archscope.domain.entity.Task;

import java.util.Optional;

/**
 * LLM任务服务接口
 * 负责管理与外部LLM服务的任务交互
 */
public interface LlmTaskService {

    /**
     * 创建LLM分析任务
     * 
     * @param projectId 项目ID
     * @param repositoryUrl 仓库URL
     * @param commitId 提交ID
     * @param branchName 分支名称
     * @param taskType 任务类型
     * @param priority 优先级
     * @return 创建的任务
     */
    Task createLlmTask(Long projectId, String repositoryUrl, String commitId, 
                       String branchName, String taskType, Integer priority);

    /**
     * 拉取下一个待处理的LLM任务
     * LLM工作节点调用此接口获取任务
     *
     * @return 任务信息，如果没有任务则返回Optional.empty()
     */
    Optional<Task> pullNextTask();

    /**
     * 拉取下一个待处理的LLM任务并指定工作节点ID
     * LLM工作节点调用此接口获取任务
     *
     * @param workerId 工作节点ID
     * @return 任务信息，如果没有任务则返回Optional.empty()
     */
    Optional<Task> pullNextTaskWithWorker(String workerId);

    /**
     * 处理LLM任务交付结果
     * LLM工作节点完成任务后调用此接口提交结果
     *
     * @param taskId 任务ID
     * @param status 任务状态 (SUCCESS/FAILED)
     * @param documentContent 生成的文档内容
     * @param errorMessage 错误信息 (可选)
     * @return 处理是否成功
     */
    boolean deliverTaskResult(Long taskId, String status, String documentContent, String errorMessage);

    /**
     * 检查并处理超时任务
     * 将超时的PROCESSING状态任务重置为PENDING状态
     * 
     * @return 处理的超时任务数量
     */
    int handleTimeoutTasks();

    /**
     * 获取任务的输入数据
     *
     * @param taskId 任务ID
     * @return 任务输入数据的JSON字符串
     */
    Optional<String> getTaskInputData(Long taskId);

    /**
     * 锁定任务 (将状态从PENDING改为PROCESSING)
     *
     * @param taskId 任务ID
     * @param timeoutMinutes 超时时间(分钟)
     * @return 锁定是否成功
     */
    boolean lockTask(Long taskId, int timeoutMinutes);

    /**
     * 解锁任务 (将状态从PROCESSING改回PENDING)
     *
     * @param taskId 任务ID
     * @return 解锁是否成功
     */
    boolean unlockTask(Long taskId);

    /**
     * 锁定任务并指定工作节点信息
     *
     * @param taskId 任务ID
     * @param workerId 工作节点ID
     * @param timeoutMinutes 超时时间(分钟)
     * @return 锁定是否成功
     */
    boolean lockTaskWithWorker(Long taskId, String workerId, int timeoutMinutes);

    /**
     * 完成任务并设置结果
     *
     * @param taskId 任务ID
     * @param overallStatus 整体状态
     * @param results 结果JSON
     * @param executionTimeMs 执行时间
     * @return 处理是否成功
     */
    boolean completeTaskWithResult(Long taskId, String overallStatus, String results, Long executionTimeMs);

    /**
     * 标记任务失败
     *
     * @param taskId 任务ID
     * @param errorDetail 错误详情
     * @param executionTimeMs 执行时间
     * @return 处理是否成功
     */
    boolean failTaskWithError(Long taskId, String errorDetail, Long executionTimeMs);

    /**
     * 检查任务是否超时
     *
     * @param taskId 任务ID
     * @return 是否超时
     */
    boolean isTaskTimeout(Long taskId);
}

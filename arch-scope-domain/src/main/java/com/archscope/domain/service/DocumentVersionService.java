package com.archscope.domain.service;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.valueobject.DocumentType;

import java.util.List;
import java.util.Optional;

/**
 * 文档版本服务接口
 * 负责文档版本的管理和操作
 */
public interface DocumentVersionService {

    /**
     * 创建文档版本
     *
     * @param documentVersion 文档版本
     * @return 创建后的文档版本
     */
    DocumentVersion createDocumentVersion(DocumentVersion documentVersion);

    /**
     * 根据ID获取文档版本
     *
     * @param id 文档版本ID
     * @return 文档版本
     */
    Optional<DocumentVersion> getDocumentVersionById(Long id);

    /**
     * 根据ID列表获取文档版本列表
     *
     * @param ids 文档版本ID列表
     * @return 文档版本列表
     */
    List<DocumentVersion> findByIds(List<Long> ids);

    /**
     * 获取项目的所有文档版本
     *
     * @param projectId 项目ID
     * @return 文档版本列表
     */
    List<DocumentVersion> getDocumentVersionsByProjectId(Long projectId);

    /**
     * 获取项目的所有已发布文档版本
     *
     * @param projectId 项目ID
     * @return 已发布的文档版本列表
     */
    List<DocumentVersion> findPublishedByProjectId(Long projectId);

    /**
     * 获取项目指定类型的所有文档版本
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @return 文档版本列表
     */
    List<DocumentVersion> getDocumentVersionsByProjectIdAndType(Long projectId, DocumentType docType);

    /**
     * 获取项目指定提交的所有文档版本
     *
     * @param projectId 项目ID
     * @param commitId 提交ID
     * @return 文档版本列表
     */
    List<DocumentVersion> getDocumentVersionsByProjectIdAndCommitId(Long projectId, String commitId);

    /**
     * 获取项目指定版本标签的文档版本
     *
     * @param projectId 项目ID
     * @param versionTag 版本标签
     * @return 文档版本
     */
    Optional<DocumentVersion> getDocumentVersionByProjectIdAndVersionTag(Long projectId, String versionTag);

    /**
     * 获取项目指定类型的最新文档版本
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @return 最新文档版本
     */
    Optional<DocumentVersion> getLatestDocumentVersionByProjectIdAndType(Long projectId, DocumentType docType);

    /**
     * 更新文档版本
     *
     * @param documentVersion 文档版本
     * @return 更新后的文档版本
     */
    DocumentVersion updateDocumentVersion(DocumentVersion documentVersion);

    /**
     * 删除文档版本
     *
     * @param id 文档版本ID
     */
    void deleteDocumentVersion(Long id);

    /**
     * 删除项目的所有文档版本
     *
     * @param projectId 项目ID
     * @return 删除的文档版本数量
     */
    int deleteDocumentVersionsByProjectId(Long projectId);

    /**
     * 比较两个文档版本
     *
     * @param versionId1 文档版本1的ID
     * @param versionId2 文档版本2的ID
     * @return 比较结果
     */
    String compareDocumentVersions(Long versionId1, Long versionId2);

    /**
     * 发布文档版本
     *
     * @param id 文档版本ID
     * @return 发布后的文档版本
     */
    DocumentVersion publishDocumentVersion(Long id);

    /**
     * 取消发布文档版本
     *
     * @param id 文档版本ID
     * @return 取消发布后的文档版本
     */
    DocumentVersion unpublishDocumentVersion(Long id);

    /**
     * 生成新的版本标签
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @return 新的版本标签
     */
    String generateVersionTag(Long projectId, DocumentType docType);
}

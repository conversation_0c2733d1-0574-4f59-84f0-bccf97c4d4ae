package com.archscope.domain.service.git;

import com.archscope.domain.config.GitServerConfig;
import lombok.RequiredArgsConstructor;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.errors.GitAPIException;
import org.eclipse.jgit.transport.UsernamePasswordCredentialsProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.nio.file.Path;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@RequiredArgsConstructor
public class GitServiceImpl implements GitService {

    private static final Logger logger = LoggerFactory.getLogger(GitServiceImpl.class);

    // Git URL 正则表达式
    private static final Pattern HTTPS_PATTERN = Pattern.compile("^https?://([^/]+)/([^/]+)/([^/]+?)(?:\\.git)?/?$");
    private static final Pattern SSH_PATTERN = Pattern.compile("^git@([^:]+):([^/]+)/([^/]+?)(?:\\.git)?/?$");

    private final GitServerConfig gitServerConfig;

    @Override
    public GitOperationResult cloneRepository(String repositoryUrl, Path localPath, Map<String, String> credentials) {
        logger.info("Attempting to clone repository {} to {}", repositoryUrl, localPath);
        try {
            org.eclipse.jgit.api.CloneCommand cloneCommand = Git.cloneRepository()
                    .setURI(repositoryUrl)
                    .setDirectory(localPath.toFile());

            // 配置认证
            configureAuthentication(cloneCommand, repositoryUrl, credentials);

            cloneCommand.call();

            logger.info("Successfully cloned repository {} to {}", repositoryUrl, localPath);
            return GitOperationResult.success("Repository cloned successfully", localPath.toString());
        } catch (GitAPIException e) {
            logger.error("Failed to clone repository {}: {}", repositoryUrl, e.getMessage(), e);
            return GitOperationResult.failure("Failed to clone repository: " + repositoryUrl + ": " + e.getMessage());
        } catch (Exception e) {
            logger.error("An unexpected error occurred during cloning {}: {}", repositoryUrl, e.getMessage(), e);
            return GitOperationResult.failure("An unexpected error occurred during cloning: " + e.getMessage());
        }
    }

    /**
     * 配置Git操作的认证方式
     * 优先级：
     * 1. 传入的credentials参数
     * 2. 配置文件中的个人访问令牌
     * 3. 无认证（公开仓库）
     */
    private void configureAuthentication(org.eclipse.jgit.api.TransportCommand<?, ?> command,
                                       String repositoryUrl, Map<String, String> credentials) {

        // 首先尝试使用传入的认证信息
        if (credentials != null && !credentials.isEmpty()) {
            String username = credentials.get("username");
            String password = credentials.get("password");
            String privateKeyPath = credentials.get("privateKeyPath");

            if (username != null && password != null) {
                // HTTPS authentication with provided credentials
                command.setCredentialsProvider(new UsernamePasswordCredentialsProvider(username, password));
                logger.info("Using HTTPS authentication with provided username/password");
                return;
            } else if (privateKeyPath != null) {
                // SSH authentication with private key - 暂时不支持
                logger.warn("SSH private key authentication is not yet fully implemented. Please use HTTPS with access token instead.");
                return;
            }
        }

        // 尝试使用配置文件中的个人访问令牌
        String host = extractHostFromUrl(repositoryUrl);
        if (host != null && gitServerConfig != null) {
            GitServerConfig.ServerConfig serverConfig = gitServerConfig.getServerConfig(host);
            if (serverConfig.hasPersonalAccessToken()) {
                String token = serverConfig.getPersonalAccessToken();
                String tokenUsername = serverConfig.getTokenUsername();

                command.setCredentialsProvider(new UsernamePasswordCredentialsProvider(tokenUsername, token));
                logger.info("Using configured personal access token for host: {} with username: {}", host, tokenUsername);
                return;
            }
        }

        // 无认证信息的情况
        if (repositoryUrl.startsWith("git@") || repositoryUrl.startsWith("ssh://")) {
            logger.warn("SSH URL detected but no SSH authentication configured. Consider using HTTPS URL with access token.");
        } else {
            logger.info("No authentication configured for repository: {}. Attempting anonymous access.", repositoryUrl);
        }
    }

    /**
     * 从Git URL中提取主机名
     *
     * @param repositoryUrl Git仓库URL
     * @return 主机名，如果解析失败则返回null
     */
    private String extractHostFromUrl(String repositoryUrl) {
        if (repositoryUrl == null) {
            return null;
        }

        // 尝试匹配HTTPS URL
        Matcher httpsMatcher = HTTPS_PATTERN.matcher(repositoryUrl);
        if (httpsMatcher.matches()) {
            return httpsMatcher.group(1);
        }

        // 尝试匹配SSH URL
        Matcher sshMatcher = SSH_PATTERN.matcher(repositoryUrl);
        if (sshMatcher.matches()) {
            return sshMatcher.group(1);
        }

        logger.warn("Unable to extract host from repository URL: {}", repositoryUrl);
        return null;
    }



    @Override
    public GitOperationResult fetchChanges(Path localPath, String remoteName, Map<String, String> credentials) {
        logger.info("Attempting to fetch changes for repository at {}", localPath);
        try (Git git = Git.open(localPath.toFile())) {
            org.eclipse.jgit.api.FetchCommand fetchCommand = git.fetch();

            if (remoteName != null && !remoteName.isEmpty()) {
                fetchCommand.setRemote(remoteName);
            }

            // 获取远程仓库URL以确定认证方式
            String remoteUrl = git.getRepository().getConfig().getString("remote", remoteName != null ? remoteName : "origin", "url");

            // 配置认证
            configureAuthentication(fetchCommand, remoteUrl, credentials);

            fetchCommand.call();

            logger.info("Successfully fetched changes for repository at {}", localPath);
            return GitOperationResult.success("Changes fetched successfully", localPath.toString());

        } catch (GitAPIException e) {
            logger.error("Failed to fetch changes for repository at {}: {}", localPath, e.getMessage(), e);
            return GitOperationResult.failure("Failed to fetch changes: " + e.getMessage());
        } catch (Exception e) {
            logger.error("An unexpected error occurred during fetching {}: {}", localPath, e.getMessage(), e);
            return GitOperationResult.failure("An unexpected error occurred during fetching: " + e.getMessage());
        }
    }
}

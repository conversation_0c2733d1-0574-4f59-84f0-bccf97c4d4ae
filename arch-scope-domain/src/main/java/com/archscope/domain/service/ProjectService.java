package com.archscope.domain.service;

import com.archscope.domain.entity.Project;

import java.util.List;

/**
 * 项目服务接口
 */
public interface ProjectService {
    
    /**
     * 注册新项目
     *
     * @param name 项目名称
     * @param description 项目描述
     * @param repositoryUrl Git仓库地址
     * @param branch Git分支
     * @return 创建的项目实体
     */
    Project registerProject(String name, String description, String repositoryUrl, String branch);
    
    /**
     * 获取所有项目
     *
     * @return 项目列表
     */
    List<Project> getAllProjects();
    
    /**
     * 根据ID获取项目
     *
     * @param id 项目ID
     * @return 项目实体
     */
    Project getProjectById(Long id);
    
    /**
     * 分析项目代码
     *
     * @param projectId 项目ID
     * @return 更新后的项目实体
     */
    Project analyzeProject(Long projectId);
    
    /**
     * 生成项目文档
     *
     * @param projectId 项目ID
     * @return 更新后的项目实体
     */
    Project generateDocumentation(Long projectId);
} 
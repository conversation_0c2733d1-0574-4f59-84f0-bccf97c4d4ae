package com.archscope.domain.service;

import com.archscope.domain.message.TaskMessage;

/**
 * 消息服务接口
 * 提供消息发送和接收的服务
 */
public interface MessageService {
    
    /**
     * 发送任务消息
     *
     * @param topic 主题
     * @param message 消息内容
     * @return 是否发送成功
     */
    boolean sendTaskMessage(String topic, TaskMessage message);
    
    /**
     * 异步发送任务消息
     *
     * @param topic 主题
     * @param message 消息内容
     * @return 是否发送成功
     */
    boolean sendTaskMessageAsync(String topic, TaskMessage message);
    
    /**
     * 延迟发送任务消息
     *
     * @param topic 主题
     * @param message 消息内容
     * @param delayLevel 延迟级别（1-18）
     *                  1=1s, 2=5s, 3=10s, 4=30s, 5=1m, 6=2m, 7=3m, 8=4m, 9=5m, 10=6m,
     *                  11=7m, 12=8m, 13=9m, 14=10m, 15=20m, 16=30m, 17=1h, 18=2h
     * @return 是否发送成功
     */
    boolean sendTaskMessageDelay(String topic, TaskMessage message, int delayLevel);
} 
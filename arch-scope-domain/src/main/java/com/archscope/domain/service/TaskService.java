package com.archscope.domain.service;

import java.util.List;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.TaskType;

/**
 * 任务服务接口
 */
public interface TaskService {
    
    /**
     * 创建任务
     *
     * @param projectId 项目ID
     * @param taskType 任务类型
     * @param parameters 任务参数
     * @return 创建的任务
     */
    Task createTask(Long projectId, TaskType taskType, String parameters);
    
    /**
     * 提交任务到队列
     *
     * @param taskId 任务ID
     * @return 是否提交成功
     */
    boolean submitTaskToQueue(Long taskId);
    
    /**
     * 获取项目的任务列表
     *
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<Task> getTasksByProject(Long projectId);
    
    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 是否取消成功
     */
    boolean cancelTask(Long taskId);
    
    /**
     * 获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    Task getTaskById(Long taskId);
    
    /**
     * 重试失败的任务
     *
     * @param taskId 任务ID
     * @return 是否重试成功
     */
    boolean retryTask(Long taskId);

    /**
     * 获取所有任务列表
     *
     * @return 任务列表
     */
    List<Task> getAllTasks();

    /**
     * 分页获取项目任务列表
     *
     * @param projectId 项目ID
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向（asc/desc）
     * @return 任务列表
     */
    List<Task> getTasksByProjectWithPagination(Long projectId, int page, int size, String sortBy, String direction);

    /**
     * 统计项目任务数量
     *
     * @param projectId 项目ID
     * @return 任务数量
     */
    long countTasksByProject(Long projectId);

    /**
     * 分页获取所有任务列表
     *
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @param sortBy 排序字段
     * @param direction 排序方向（asc/desc）
     * @return 任务列表
     */
    List<Task> getAllTasksWithPagination(int page, int size, String sortBy, String direction);

    /**
     * 统计所有任务数量
     *
     * @return 任务数量
     */
    long countAllTasks();
}
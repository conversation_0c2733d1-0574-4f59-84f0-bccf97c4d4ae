package com.archscope.domain.service;

import com.archscope.domain.valueobject.GitRepositoryInfo;
import com.archscope.domain.valueobject.GitRepositoryValidationRequest;

/**
 * Git仓库元数据服务接口
 * 提供从Git仓库URL获取项目元数据信息的功能
 */
public interface GitRepositoryMetadataService {
    
    /**
     * 验证Git仓库URL并获取基本信息
     * 
     * @param request 验证请求，包含仓库URL和可选的认证信息
     * @return Git仓库信息DTO，包含解析出的项目信息
     */
    GitRepositoryInfo validateAndFetchRepositoryInfo(GitRepositoryValidationRequest request);
    
    /**
     * 从Git仓库URL解析项目名称
     * 
     * @param repositoryUrl Git仓库URL
     * @return 解析出的项目名称
     */
    String extractProjectNameFromUrl(String repositoryUrl);
    
    /**
     * 从Git仓库URL解析仓库所有者
     * 
     * @param repositoryUrl Git仓库URL
     * @return 解析出的仓库所有者名称
     */
    String extractOwnerFromUrl(String repositoryUrl);
    
    /**
     * 判断Git仓库类型（GitHub, GitLab等）
     * 
     * @param repositoryUrl Git仓库URL
     * @return 仓库类型字符串
     */
    String determineRepositoryType(String repositoryUrl);
    
    /**
     * 验证Git仓库URL格式是否正确
     * 
     * @param repositoryUrl Git仓库URL
     * @return 是否为有效的Git仓库URL
     */
    boolean isValidGitUrl(String repositoryUrl);
}

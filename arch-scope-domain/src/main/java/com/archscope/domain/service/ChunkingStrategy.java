package com.archscope.domain.service;

import com.archscope.domain.model.parser.CodeChunk;

import java.util.List;

/**
 * 代码分块策略接口
 * 不同语言可以实现不同的分块策略
 */
public interface ChunkingStrategy {
    
    /**
     * 将代码内容分割为可管理的块
     * 
     * @param content 代码内容
     * @param filename 文件名
     * @param context 上下文信息（如导入语句、包声明等）
     * @return 代码块列表
     */
    List<CodeChunk> chunk(String content, String filename, String context);
    
    /**
     * 提取上下文信息（如导入语句、包声明等）
     * 
     * @param content 代码内容
     * @return 上下文信息
     */
    String extractContext(String content);
}

package com.archscope.domain.service.impl;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.Requirement;
import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.repository.CapabilityRepository;
import com.archscope.domain.repository.RequirementRepository;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.service.RequirementMatchingDomainService;
import com.archscope.domain.valueobject.RequirementId;
import com.archscope.domain.valueobject.ServiceId;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 需求匹配领域服务实现类
 */
public class RequirementMatchingDomainServiceImpl implements RequirementMatchingDomainService {

    private final ServiceRepository serviceRepository;
    private final CapabilityRepository capabilityRepository;
    private final RequirementRepository requirementRepository;

    public RequirementMatchingDomainServiceImpl(ServiceRepository serviceRepository,
                                               CapabilityRepository capabilityRepository,
                                               RequirementRepository requirementRepository) {
        this.serviceRepository = serviceRepository;
        this.capabilityRepository = capabilityRepository;
        this.requirementRepository = requirementRepository;
    }

    @Override
    public List<ServiceId> findMatchingServices(Requirement requirement) {
        // 获取需求中的能力需求列表
        List<String> requiredCapabilities = requirement.getRequiredCapabilities();
        
        if (requiredCapabilities.isEmpty()) {
            // 如果没有明确的能力需求，尝试从描述中生成
            requiredCapabilities = generateCapabilityRequirements(requirement.getDescription());
        }
        
        // 查找提供这些能力的服务
        List<ServiceId> matchingServiceIds;
        if (!requiredCapabilities.isEmpty()) {
            matchingServiceIds = capabilityRepository.findServiceIdsByCapabilityNames(requiredCapabilities);
        } else {
            // 如果无法生成能力需求，返回所有活跃的服务
            matchingServiceIds = serviceRepository.findByStatus(ServiceStatus.ACTIVE)
                    .stream()
                    .map(Service::getId)
                    .collect(Collectors.toList());
        }
        
        // 计算每个服务的匹配度
        Map<ServiceId, Integer> matchingScores = new HashMap<>();
        for (ServiceId serviceId : matchingServiceIds) {
            int score = calculateMatchingScore(serviceId, requirement);
            matchingScores.put(serviceId, score);
        }
        
        // 按匹配度排序
        return matchingServiceIds.stream()
                .sorted(Comparator.comparing(matchingScores::get).reversed())
                .collect(Collectors.toList());
    }

    @Override
    public int calculateMatchingScore(ServiceId serviceId, Requirement requirement) {
        Service service = serviceRepository.findById(serviceId);
        if (service == null) {
            return 0;
        }
        
        // 基础分数
        int score = 0;
        
        // 获取服务提供的能力
        Set<Capability> capabilities = service.getCapabilities();
        Set<String> providedCapabilityNames = capabilities.stream()
                .map(Capability::getName)
                .collect(Collectors.toSet());
        
        // 计算能力匹配度
        List<String> requiredCapabilities = requirement.getRequiredCapabilities();
        if (!requiredCapabilities.isEmpty()) {
            int matchedCapabilities = 0;
            for (String requiredCapability : requiredCapabilities) {
                if (providedCapabilityNames.contains(requiredCapability)) {
                    matchedCapabilities++;
                }
            }
            
            // 能力匹配度占70分
            score += (int) (70.0 * matchedCapabilities / requiredCapabilities.size());
        } else {
            // 如果没有明确的能力需求，给予基础分数
            score += 35;
        }
        
        // 服务状态影响分数，活跃状态得分最高
        switch (service.getStatus()) {
            case ACTIVE:
                score += 30;
                break;
            case MAINTENANCE:
                score += 15;
                break;
            case DEPRECATED:
                score -= 20;
                break;
            case INACTIVE:
                score -= 30;
                break;
            default:
                break;
        }
        
        // 确保分数在0-100范围内
        return Math.max(0, Math.min(100, score));
    }

    @Override
    public List<String> generateCapabilityRequirements(String description) {
        // 简单实现：从描述中提取关键词作为能力需求
        // 实际实现可能需要更复杂的自然语言处理或机器学习算法
        if (description == null || description.trim().isEmpty()) {
            return Collections.emptyList();
        }
        
        // 获取系统中所有能力名称
        List<String> allCapabilityNames = capabilityRepository.findAllCapabilityNames();
        
        // 查找描述中包含的能力名称
        return allCapabilityNames.stream()
                .filter(name -> description.toLowerCase().contains(name.toLowerCase()))
                .collect(Collectors.toList());
    }

    @Override
    public void recordMatchingFeedback(RequirementId requirementId, ServiceId serviceId, boolean satisfied, String feedback) {
        // 记录匹配反馈
        // 实际实现可能需要存储反馈信息并用于改进匹配算法
        requirementRepository.recordMatchingFeedback(requirementId, serviceId, satisfied, feedback);
    }

    @Override
    public Map<ServiceId, Integer> getServiceRecommendationScores(List<ServiceId> serviceIds) {
        Map<ServiceId, Integer> scores = new HashMap<>();
        
        for (ServiceId serviceId : serviceIds) {
            Service service = serviceRepository.findById(serviceId);
            if (service == null) {
                continue;
            }
            
            // 基础推荐分数
            int score = 50;
            
            // 活跃状态的服务得分更高
            if (service.getStatus() == ServiceStatus.ACTIVE) {
                score += 30;
            } else if (service.getStatus() == ServiceStatus.DEPRECATED) {
                score -= 30;
            } else if (service.getStatus() == ServiceStatus.INACTIVE) {
                score -= 40;
            }
            
            // 能力数量影响分数
            int capabilityCount = service.getCapabilities().size();
            score += Math.min(20, capabilityCount * 2);
            
            // 确保分数在0-100范围内
            scores.put(serviceId, Math.max(0, Math.min(100, score)));
        }
        
        return scores;
    }
}
package com.archscope.domain.service.impl;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.CapabilityExample;
import com.archscope.domain.repository.CapabilityRepository;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.service.CapabilityManagementDomainService;
import com.archscope.domain.valueobject.CapabilityId;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.domain.valueobject.Tag;
import com.archscope.domain.valueobject.Version;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 能力管理领域服务实现类
 */
public class CapabilityManagementDomainServiceImpl implements CapabilityManagementDomainService {

    private final CapabilityRepository capabilityRepository;
    private final ServiceRepository serviceRepository;

    public CapabilityManagementDomainServiceImpl(CapabilityRepository capabilityRepository, ServiceRepository serviceRepository) {
        this.capabilityRepository = capabilityRepository;
        this.serviceRepository = serviceRepository;
    }

    @Override
    public Capability registerCapability(ServiceId serviceId, String name, String description,
                                        Version version, Set<CapabilityExample> examples, Set<Tag> tags) {
        // 验证能力信息
        validateCapabilityInfo(name, version);
        
        // 检查服务是否存在
        if (serviceRepository.findById(serviceId) == null) {
            throw new IllegalArgumentException("Service not found with ID: " + serviceId);
        }
        
        // 检查能力名称是否已存在于指定服务中
        if (isCapabilityNameExistsInService(serviceId, name)) {
            throw new IllegalArgumentException("Capability name already exists in service: " + name);
        }
        
        // 创建能力实体
        Capability capability = Capability.create(serviceId, name, description, version, examples, tags);
        
        return capability;
    }

    @Override
    public void validateCapabilityInfo(String name, Version version) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("Capability name cannot be null or empty");
        }
        
        if (version == null) {
            throw new IllegalArgumentException("Capability version cannot be null");
        }
    }

    @Override
    public boolean isCapabilityNameExistsInService(ServiceId serviceId, String name) {
        return capabilityRepository.findByServiceIdAndName(serviceId, name) != null;
    }

    @Override
    public List<ServiceId> findServicesByCapability(String capabilityName) {
        return capabilityRepository.findByName(capabilityName)
                .stream()
                .map(Capability::getServiceId)
                .collect(Collectors.toList());
    }

    @Override
    public List<ServiceId> findServicesByCapabilityRequirements(List<String> requiredCapabilities) {
        if (requiredCapabilities == null || requiredCapabilities.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 查找满足所有指定能力需求的服务
        return capabilityRepository.findServiceIdsByCapabilityNames(requiredCapabilities);
    }
}
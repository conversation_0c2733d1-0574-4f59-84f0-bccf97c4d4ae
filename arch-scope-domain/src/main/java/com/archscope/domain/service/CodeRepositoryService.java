package com.archscope.domain.service;

import com.archscope.domain.entity.CodeRepository;
import java.util.List;
import java.util.Optional;
import java.util.Map;

/**
 * 代码仓库领域服务接口
 */
public interface CodeRepositoryService {
    /**
     * 创建代码仓库
     * @param codeRepository 代码仓库
     * @return 创建后的代码仓库
     */
    CodeRepository createRepository(CodeRepository codeRepository);
    
    /**
     * 更新代码仓库
     * @param codeRepository 代码仓库
     * @return 更新后的代码仓库
     */
    CodeRepository updateRepository(CodeRepository codeRepository);
    
    /**
     * 根据ID查找代码仓库
     * @param id 代码仓库ID
     * @return 代码仓库
     */
    Optional<CodeRepository> findRepositoryById(Long id);
    
    /**
     * 根据项目ID查找代码仓库列表
     * @param projectId 项目ID
     * @return 代码仓库列表
     */
    List<CodeRepository> findRepositoriesByProjectId(Long projectId);
    
    /**
     * 同步代码仓库
     * @param id 代码仓库ID
     * @return 同步后的代码仓库
     */
    CodeRepository syncRepository(Long id);
    
    /**
     * 验证仓库连接
     * @param codeRepository 代码仓库
     * @return 连接是否成功
     */
    boolean validateRepositoryConnection(CodeRepository codeRepository);
    
    /**
     * 删除代码仓库
     * @param id 代码仓库ID
     */
    void deleteRepository(Long id);
    
    /**
     * 获取仓库分支列表
     * @param id 代码仓库ID
     * @return 分支列表
     */
    List<String> getRepositoryBranches(Long id);
    
    /**
     * 获取两个提交之间的变更文件列表
     * @param repositoryId 代码仓库ID
     * @param fromCommit 起始提交ID
     * @param toCommit 目标提交ID
     * @return 变更文件路径列表
     */
    List<String> getRepositoryChanges(Long repositoryId, String fromCommit, String toCommit);
    
    /**
     * 获取文件在两个提交之间的差异
     * @param repositoryId 代码仓库ID
     * @param filePath 文件路径
     * @param fromCommit 起始提交ID
     * @param toCommit 目标提交ID
     * @return 文件差异内容
     */
    String getFileDiff(Long repositoryId, String filePath, String fromCommit, String toCommit);
    
    /**
     * 获取文件的最后修改提交
     * @param repositoryId 代码仓库ID
     * @param filePath 文件路径
     * @return 最后修改提交ID
     */
    String getFileLastCommit(Long repositoryId, String filePath);
    
    /**
     * 获取提交的详细信息
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @return 提交详细信息
     */
    Map<String, Object> getCommitDetails(Long repositoryId, String commitId);

    /**
     * 获取仓库的贡献者数量
     * @param repositoryId 代码仓库ID
     * @return 贡献者数量
     */
    int getContributorCount(Long repositoryId);
}

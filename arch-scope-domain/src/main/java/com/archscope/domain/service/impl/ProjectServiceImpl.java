package com.archscope.domain.service.impl;

import com.archscope.domain.service.ProjectService;
import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.util.GitUrlNormalizer;
import com.archscope.domain.valueobject.ProjectStatus;
import com.archscope.domain.valueobject.ProjectType;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 项目服务实现
 */
@Service
@RequiredArgsConstructor
public class ProjectServiceImpl implements ProjectService {

    private final ProjectRepository projectRepository;

    @Override
    @Transactional
    public Project registerProject(String name, String description, String repositoryUrl, String branch) {
        // 验证仓库URL
        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            throw new IllegalArgumentException("仓库地址不能为空");
        }

        // 验证项目名称
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("项目名称不能为空");
        }

        // 验证URL格式
        if (!GitUrlNormalizer.isValidGitUrl(repositoryUrl)) {
            throw new IllegalArgumentException("仓库地址格式不正确，请提供有效的Git仓库URL");
        }

        // 标准化仓库URL
        String normalizedUrl = GitUrlNormalizer.normalize(repositoryUrl);
        if (normalizedUrl == null) {
            throw new IllegalArgumentException("无法解析仓库地址，请检查URL格式");
        }

        // 检查标准化URL是否已存在（防止重复注册）
        Optional<Project> existingProject = projectRepository.findByNormalizedRepositoryUrl(normalizedUrl);
        if (existingProject.isPresent()) {
            Project existing = existingProject.get();
            throw new IllegalArgumentException(
                String.format("该仓库已被项目 \"%s\" 注册使用。如果您需要查看现有项目，请前往项目列表页面。" +
                    "原始URL: %s, 标准化URL: %s",
                    existing.getName(), existing.getRepositoryUrl(), normalizedUrl)
            );
        }

        // 不需要用户信息

        // 创建新项目
        Project project = Project.builder()
                .name(name)
                .description(description)
                .repositoryUrl(repositoryUrl)
                .normalizedRepositoryUrl(normalizedUrl)  // 添加标准化URL
                .branch(branch != null && !branch.isEmpty() ? branch : "main")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .creatorId(1L)  // 默认创建者ID，不需要认证
                .status("PENDING_ANALYSIS")  // 初始状态：待分析
                .active(true)
                .analysisCount(0)
                .documentationVersion(0)
                .type(ProjectType.OTHER)  // 设置默认项目类型
                .build();

        return projectRepository.save(project);
    }

    @Override
    public List<Project> getAllProjects() {
        // 不需要用户权限检查，直接返回所有项目
        return projectRepository.findAll();
    }

    @Override
    public Project getProjectById(Long id) {
        Project project = projectRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("项目不存在"));

        // 不需要权限检查
        return project;
    }

    @Override
    @Transactional
    public Project analyzeProject(Long projectId) {
        Project project = getProjectById(projectId);
        
        // 检查是否可以开始分析
        ProjectStatus currentStatus = ProjectStatus.fromString(project.getStatus());
        if (!currentStatus.canStartAnalysis()) {
            throw new IllegalStateException("项目当前状态不允许开始分析: " + currentStatus.getDisplayName());
        }

        // 更新项目状态
        project.setStatus(ProjectStatus.ANALYZING.name());
        project.setUpdatedAt(LocalDateTime.now());
        projectRepository.save(project);

        // TODO: 实现实际的代码分析逻辑
        // 这里应该调用代码分析服务，可能是异步操作

        // 模拟分析完成
        project.setStatus(ProjectStatus.ANALYSIS_COMPLETED.name());
        project.setLastAnalyzedAt(LocalDateTime.now());
        project.setAnalysisCount(project.getAnalysisCount() + 1);
        
        return projectRepository.save(project);
    }

    @Override
    @Transactional
    public Project generateDocumentation(Long projectId) {
        Project project = getProjectById(projectId);
        
        // 检查项目是否已分析
        ProjectStatus currentStatus = ProjectStatus.fromString(project.getStatus());
        if (!currentStatus.canGenerateDocumentation()) {
            throw new IllegalStateException("项目需要先完成代码分析才能生成文档，当前状态: " + currentStatus.getDisplayName());
        }
        
        // TODO: 实现文档生成逻辑
        // 这里应该调用文档生成服务
        
        // 更新文档版本
        project.setDocumentationVersion(project.getDocumentationVersion() + 1);
        project.setUpdatedAt(LocalDateTime.now());
        
        return projectRepository.save(project);
    }
} 
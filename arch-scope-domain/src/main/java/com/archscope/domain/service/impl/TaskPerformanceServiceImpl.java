package com.archscope.domain.service.impl;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.TaskPerformanceService;
import com.archscope.domain.valueobject.TaskStatus;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务性能监控服务实现
 */
@Service
public class TaskPerformanceServiceImpl implements TaskPerformanceService {

    private final TaskRepository taskRepository;

    public TaskPerformanceServiceImpl(TaskRepository taskRepository) {
        this.taskRepository = taskRepository;
    }

    @Override
    public Map<String, Object> getTaskStatistics(Long projectId, String taskType,
                                                LocalDateTime startDate, LocalDateTime endDate) {
        // 获取任务统计信息

        List<Map<String, Object>> rawStats = taskRepository.getTaskStatistics(projectId, taskType, startDate, endDate);
        
        Map<String, Object> result = new HashMap<>();
        Map<String, Integer> statusCounts = new HashMap<>();
        double totalExecutionTime = 0;
        double maxExecutionTime = 0;
        double minExecutionTime = Double.MAX_VALUE;
        int completedTasks = 0;

        for (Map<String, Object> stat : rawStats) {
            String status = (String) stat.get("status");
            Integer count = ((Number) stat.get("count")).intValue();
            Double avgTime = ((Number) stat.get("avg_execution_time")).doubleValue();
            Double maxTime = ((Number) stat.get("max_execution_time")).doubleValue();
            Double minTime = ((Number) stat.get("min_execution_time")).doubleValue();

            statusCounts.put(status, count);
            
            if ("COMPLETED".equals(status)) {
                completedTasks = count;
                totalExecutionTime = avgTime * count;
                maxExecutionTime = Math.max(maxExecutionTime, maxTime);
                if (minTime > 0) {
                    minExecutionTime = Math.min(minExecutionTime, minTime);
                }
            }
        }

        result.put("statusCounts", statusCounts);
        result.put("totalTasks", statusCounts.values().stream().mapToInt(Integer::intValue).sum());
        result.put("completedTasks", completedTasks);
        result.put("averageExecutionTime", completedTasks > 0 ? totalExecutionTime / completedTasks : 0);
        result.put("maxExecutionTime", maxExecutionTime);
        result.put("minExecutionTime", minExecutionTime == Double.MAX_VALUE ? 0 : minExecutionTime);
        result.put("completionRate", calculateCompletionRate(statusCounts));
        result.put("generatedAt", LocalDateTime.now());

        return result;
    }

    @Override
    public Map<String, Object> getPerformanceMetrics(Long projectId, int hours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        LocalDateTime endTime = LocalDateTime.now();

        Map<String, Object> metrics = getTaskStatistics(projectId, null, startTime, endTime);
        
        // 添加性能特定指标
        List<Task> processingTasks = taskRepository.findAllByStatus(TaskStatus.PROCESSING);
        List<Task> pendingTasks = taskRepository.findAllByStatus(TaskStatus.PENDING);
        
        metrics.put("currentProcessingTasks", processingTasks.size());
        metrics.put("currentPendingTasks", pendingTasks.size());
        metrics.put("averageWaitTime", calculateAverageWaitTime(pendingTasks));
        metrics.put("throughput", calculateThroughput((Integer) metrics.get("completedTasks"), hours));
        
        return metrics;
    }

    @Override
    public List<Task> findLongRunningTasks(int minutes, int limit) {
        return taskRepository.findLongRunningTasks(minutes, limit);
    }

    @Override
    public Map<String, Object> getQueueHealthStatus() {
        Map<String, Object> health = new HashMap<>();
        
        // 获取各状态任务数量
        Map<TaskStatus, Long> statusCounts = Arrays.stream(TaskStatus.values())
                .collect(Collectors.toMap(
                        status -> status,
                        status -> (long) taskRepository.findAllByStatus(status).size()
                ));

        health.put("statusCounts", statusCounts);
        
        // 计算健康指标
        long totalTasks = statusCounts.values().stream().mapToLong(Long::longValue).sum();
        long pendingTasks = statusCounts.getOrDefault(TaskStatus.PENDING, 0L);
        long processingTasks = statusCounts.getOrDefault(TaskStatus.PROCESSING, 0L);
        long failedTasks = statusCounts.getOrDefault(TaskStatus.FAILED, 0L);
        
        health.put("totalTasks", totalTasks);
        health.put("queueBacklog", pendingTasks);
        health.put("activeProcessing", processingTasks);
        health.put("failureRate", totalTasks > 0 ? (double) failedTasks / totalTasks : 0);
        
        // 健康状态评估
        String healthStatus = "HEALTHY";
        if (pendingTasks > 100) {
            healthStatus = "WARNING";
        }
        if (pendingTasks > 500 || (totalTasks > 0 && (double) failedTasks / totalTasks > 0.1)) {
            healthStatus = "CRITICAL";
        }
        
        health.put("overallHealth", healthStatus);
        health.put("checkTime", LocalDateTime.now());
        
        return health;
    }

    @Override
    public Map<String, Object> getWorkerPerformanceStats(int hours) {
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        
        // 获取处理中的任务按工作节点分组
        List<Task> processingTasks = taskRepository.findAllByStatus(TaskStatus.PROCESSING);
        Map<String, List<Task>> tasksByWorker = processingTasks.stream()
                .filter(task -> task.getWorkerId() != null)
                .collect(Collectors.groupingBy(Task::getWorkerId));

        Map<String, Object> workerStats = new HashMap<>();
        
        for (Map.Entry<String, List<Task>> entry : tasksByWorker.entrySet()) {
            String workerId = entry.getKey();
            List<Task> tasks = entry.getValue();
            
            Map<String, Object> stats = new HashMap<>();
            stats.put("activeTasks", tasks.size());
            stats.put("averageProcessingTime", calculateAverageProcessingTime(tasks));
            
            workerStats.put(workerId, stats);
        }
        
        return workerStats;
    }

    @Override
    public Map<String, Object> analyzeBottlenecks(Long projectId, int hours) {
        Map<String, Object> analysis = new HashMap<>();
        LocalDateTime startTime = LocalDateTime.now().minusHours(hours);
        
        // 分析待处理任务积压
        List<Task> pendingTasks = projectId != null 
                ? taskRepository.findAllByStatusAndProjectId(TaskStatus.PENDING, projectId)
                : taskRepository.findAllByStatus(TaskStatus.PENDING);
        
        analysis.put("pendingTasksCount", pendingTasks.size());
        
        // 分析任务类型分布
        Map<String, Long> taskTypeDistribution = pendingTasks.stream()
                .collect(Collectors.groupingBy(Task::getTaskType, Collectors.counting()));
        analysis.put("taskTypeBottlenecks", taskTypeDistribution);
        
        // 分析长时间运行的任务
        List<Task> longRunningTasks = findLongRunningTasks(60, 10);
        analysis.put("longRunningTasksCount", longRunningTasks.size());
        
        // 提供建议
        List<String> recommendations = new ArrayList<>();
        if (pendingTasks.size() > 50) {
            recommendations.add("考虑增加工作节点处理积压的待处理任务");
        }
        if (longRunningTasks.size() > 5) {
            recommendations.add("检查长时间运行的任务是否存在性能问题");
        }
        
        analysis.put("recommendations", recommendations);
        analysis.put("analysisTime", LocalDateTime.now());
        
        return analysis;
    }

    @Override
    public List<Map<String, Object>> getProcessingTrends(Long projectId, int days) {
        List<Map<String, Object>> trends = new ArrayList<>();
        
        for (int i = days - 1; i >= 0; i--) {
            LocalDateTime dayStart = LocalDateTime.now().minusDays(i).truncatedTo(ChronoUnit.DAYS);
            LocalDateTime dayEnd = dayStart.plusDays(1);
            
            Map<String, Object> dayStats = getTaskStatistics(projectId, null, dayStart, dayEnd);
            dayStats.put("date", dayStart.toLocalDate());
            trends.add(dayStats);
        }
        
        return trends;
    }

    @Override
    public LocalDateTime predictTaskCompletion(Long taskId) {
        Optional<Task> taskOpt = taskRepository.findById(taskId);
        if (!taskOpt.isPresent()) {
            return null;
        }
        
        Task task = taskOpt.get();
        if (task.getStatus() == TaskStatus.COMPLETED || task.getStatus() == TaskStatus.FAILED) {
            return task.getCompletedAt();
        }
        
        // 基于历史数据预测
        List<Task> similarTasks = taskRepository.findAllByStatusAndTaskType(TaskStatus.COMPLETED, task.getTaskType());
        if (similarTasks.isEmpty()) {
            return LocalDateTime.now().plusHours(1); // 默认预测1小时
        }
        
        double avgExecutionTime = similarTasks.stream()
                .filter(t -> t.getExecutionTimeMs() != null)
                .mapToLong(Task::getExecutionTimeMs)
                .average()
                .orElse(3600000); // 默认1小时
        
        LocalDateTime startTime = task.getProcessingStartedAt() != null 
                ? task.getProcessingStartedAt() 
                : LocalDateTime.now();
        
        return startTime.plusSeconds((long) (avgExecutionTime / 1000));
    }

    @Override
    public Map<String, Object> getLoadRecommendations() {
        Map<String, Object> recommendations = new HashMap<>();
        
        Map<String, Object> health = getQueueHealthStatus();
        long pendingTasks = (Long) health.get("queueBacklog");
        long processingTasks = (Long) health.get("activeProcessing");
        
        // 基于当前负载提供建议
        if (pendingTasks > 100) {
            recommendations.put("scaleUp", "建议增加工作节点数量");
            recommendations.put("recommendedWorkers", Math.min(pendingTasks / 10, 20));
        } else if (pendingTasks < 10 && processingTasks < 5) {
            recommendations.put("scaleDown", "可以考虑减少工作节点数量");
        }
        
        recommendations.put("currentLoad", pendingTasks + processingTasks);
        recommendations.put("recommendationTime", LocalDateTime.now());
        
        return recommendations;
    }

    @Override
    public int cleanupOldCompletedTasks(int daysOld) {
        LocalDateTime cutoffDate = LocalDateTime.now().minusDays(daysOld);
        return taskRepository.deleteCompletedTasksOlderThan(cutoffDate);
    }

    // 辅助方法
    private double calculateCompletionRate(Map<String, Integer> statusCounts) {
        int total = statusCounts.values().stream().mapToInt(Integer::intValue).sum();
        int completed = statusCounts.getOrDefault("COMPLETED", 0);
        return total > 0 ? (double) completed / total : 0;
    }

    private double calculateAverageWaitTime(List<Task> pendingTasks) {
        if (pendingTasks.isEmpty()) {
            return 0;
        }
        
        LocalDateTime now = LocalDateTime.now();
        return pendingTasks.stream()
                .mapToLong(task -> ChronoUnit.MINUTES.between(task.getCreatedAt(), now))
                .average()
                .orElse(0);
    }

    private double calculateThroughput(int completedTasks, int hours) {
        return hours > 0 ? (double) completedTasks / hours : 0;
    }

    private double calculateAverageProcessingTime(List<Task> tasks) {
        LocalDateTime now = LocalDateTime.now();
        return tasks.stream()
                .filter(task -> task.getProcessingStartedAt() != null)
                .mapToLong(task -> ChronoUnit.MINUTES.between(task.getProcessingStartedAt(), now))
                .average()
                .orElse(0);
    }
}

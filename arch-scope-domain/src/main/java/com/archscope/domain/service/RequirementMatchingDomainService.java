package com.archscope.domain.service;

import com.archscope.domain.model.servicediscovery.Requirement;
import com.archscope.domain.valueobject.RequirementId;
import com.archscope.domain.valueobject.ServiceId;

import java.util.List;
import java.util.Map;

/**
 * 需求匹配领域服务，负责需求匹配的核心业务逻辑
 */
public interface RequirementMatchingDomainService {

    /**
     * 根据需求查找匹配的服务
     *
     * @param requirement 需求实体
     * @return 匹配的服务ID列表，按匹配度排序
     */
    List<ServiceId> findMatchingServices(Requirement requirement);

    /**
     * 计算服务与需求的匹配度
     *
     * @param serviceId 服务ID
     * @param requirement 需求实体
     * @return 匹配度分数，范围0-100，分数越高表示匹配度越高
     */
    int calculateMatchingScore(ServiceId serviceId, Requirement requirement);

    /**
     * 根据需求描述生成推荐的能力需求
     *
     * @param description 需求描述
     * @return 推荐的能力需求列表
     */
    List<String> generateCapabilityRequirements(String description);

    /**
     * 记录需求匹配反馈
     *
     * @param requirementId 需求ID
     * @param serviceId 服务ID
     * @param satisfied 是否满意
     * @param feedback 反馈内容
     */
    void recordMatchingFeedback(RequirementId requirementId, ServiceId serviceId, boolean satisfied, String feedback);

    /**
     * 获取服务的推荐分数
     *
     * @param serviceIds 服务ID列表
     * @return 服务ID到推荐分数的映射，分数范围0-100，分数越高表示推荐度越高
     */
    Map<ServiceId, Integer> getServiceRecommendationScores(List<ServiceId> serviceIds);
}
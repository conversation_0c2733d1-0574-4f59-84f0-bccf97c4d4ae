package com.archscope.domain.service.impl;

import com.archscope.domain.entity.Project;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.CodeRepositoryService;
import com.archscope.domain.service.CodeStatisticsService;
import com.archscope.domain.valueobject.CodeStatistics;
import com.archscope.domain.model.parser.LanguageType;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 代码统计服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DefaultCodeStatisticsService implements CodeStatisticsService {
    
    private final ProjectRepository projectRepository;
    private final CodeRepositoryService codeRepositoryService;
    
    // 注释模式匹配
    private static final Pattern SINGLE_LINE_COMMENT = Pattern.compile("^\\s*//.*$");
    private static final Pattern MULTI_LINE_COMMENT_START = Pattern.compile("^\\s*/\\*.*$");
    private static final Pattern MULTI_LINE_COMMENT_END = Pattern.compile("^.*\\*/\\s*$");
    private static final Pattern BLANK_LINE = Pattern.compile("^\\s*$");
    
    @Override
    public CodeStatistics calculateStatistics(List<FileParseResult> parseResults) {
        log.info("开始计算代码统计信息，共 {} 个解析结果", parseResults.size());
        
        long totalLines = 0;
        long effectiveLines = 0;
        long commentLines = 0;
        long blankLines = 0;
        int totalFiles = parseResults.size();
        
        Map<String, Integer> fileCountByType = new HashMap<>();
        Map<String, Long> linesOfCodeByLanguage = new HashMap<>();
        
        for (FileParseResult result : parseResults) {
            // 按文件类型统计（包括失败的文件）
            String fileExtension = getFileExtension(result.getFilename());
            fileCountByType.merge(fileExtension, 1, Integer::sum);

            if (!result.isSuccessful()) {
                log.debug("跳过解析失败的文件: {}", result.getFilePath());
                continue;
            }

            // 计算文件的行数统计
            File file = new File(result.getFilePath());
            if (file.exists()) {
                CodeStatistics.FileLineStatistics fileStats = calculateFileLineStatistics(file);

                totalLines += fileStats.getTotalLines();
                effectiveLines += fileStats.getEffectiveLines();
                commentLines += fileStats.getCommentLines();
                blankLines += fileStats.getBlankLines();

                // 按语言类型统计
                String languageType = result.getLanguageType() != null ?
                    result.getLanguageType().name() : "UNKNOWN";
                linesOfCodeByLanguage.merge(languageType, (long) fileStats.getEffectiveLines(), Long::sum);
            }
        }
        
        CodeStatistics statistics = CodeStatistics.builder()
                .totalLinesOfCode(totalLines)
                .effectiveLinesOfCode(effectiveLines)
                .commentLines(commentLines)
                .blankLines(blankLines)
                .totalFileCount(totalFiles)
                .fileCountByType(fileCountByType)
                .linesOfCodeByLanguage(linesOfCodeByLanguage)
                .contributorCount(0) // 这里先设为0，后续通过Git获取
                .build();
        
        log.info("代码统计完成: 总行数={}, 有效代码行={}, 文件数={}", 
                totalLines, effectiveLines, totalFiles);
        
        return statistics;
    }
    
    @Override
    public CodeStatistics calculateStatisticsFromFiles(List<File> files) {
        log.info("从文件列表计算代码统计信息，共 {} 个文件", files.size());
        
        long totalLines = 0;
        long effectiveLines = 0;
        long commentLines = 0;
        long blankLines = 0;
        
        Map<String, Integer> fileCountByType = new HashMap<>();
        Map<String, Long> linesOfCodeByLanguage = new HashMap<>();
        
        for (File file : files) {
            if (!file.exists() || !file.isFile()) {
                continue;
            }
            
            CodeStatistics.FileLineStatistics fileStats = calculateFileLineStatistics(file);
            
            totalLines += fileStats.getTotalLines();
            effectiveLines += fileStats.getEffectiveLines();
            commentLines += fileStats.getCommentLines();
            blankLines += fileStats.getBlankLines();
            
            // 按文件类型统计
            String fileExtension = getFileExtension(file.getName());
            fileCountByType.merge(fileExtension, 1, Integer::sum);
            
            // 按语言类型统计
            LanguageType languageType = LanguageType.fromFilename(file.getName());
            String languageName = languageType != null ? languageType.name() : "UNKNOWN";
            linesOfCodeByLanguage.merge(languageName, (long) fileStats.getEffectiveLines(), Long::sum);
        }
        
        return CodeStatistics.builder()
                .totalLinesOfCode(totalLines)
                .effectiveLinesOfCode(effectiveLines)
                .commentLines(commentLines)
                .blankLines(blankLines)
                .totalFileCount(files.size())
                .fileCountByType(fileCountByType)
                .linesOfCodeByLanguage(linesOfCodeByLanguage)
                .contributorCount(0)
                .build();
    }
    
    @Override
    public CodeStatistics.FileLineStatistics calculateFileLineStatistics(File file) {
        if (!file.exists() || !file.isFile()) {
            return CodeStatistics.FileLineStatistics.builder()
                    .filePath(file.getPath())
                    .totalLines(0)
                    .effectiveLines(0)
                    .commentLines(0)
                    .blankLines(0)
                    .fileSize(0)
                    .languageType("UNKNOWN")
                    .build();
        }
        
        int totalLines = 0;
        int effectiveLines = 0;
        int commentLines = 0;
        int blankLines = 0;
        boolean inMultiLineComment = false;
        
        try (BufferedReader reader = new BufferedReader(new FileReader(file))) {
            String line;
            while ((line = reader.readLine()) != null) {
                totalLines++;
                
                // 检查是否是空行
                if (BLANK_LINE.matcher(line).matches()) {
                    blankLines++;
                    continue;
                }
                
                // 检查多行注释
                if (inMultiLineComment) {
                    commentLines++;
                    if (MULTI_LINE_COMMENT_END.matcher(line).matches()) {
                        inMultiLineComment = false;
                    }
                    continue;
                }
                
                // 检查多行注释开始
                if (MULTI_LINE_COMMENT_START.matcher(line).matches()) {
                    commentLines++;
                    inMultiLineComment = !MULTI_LINE_COMMENT_END.matcher(line).matches();
                    continue;
                }
                
                // 检查单行注释
                if (SINGLE_LINE_COMMENT.matcher(line).matches()) {
                    commentLines++;
                    continue;
                }
                
                // 有效代码行
                effectiveLines++;
            }
        } catch (IOException e) {
            log.error("读取文件失败: {}", file.getPath(), e);
        }
        
        LanguageType languageType = LanguageType.fromFilename(file.getName());
        
        return CodeStatistics.FileLineStatistics.builder()
                .filePath(file.getPath())
                .totalLines(totalLines)
                .effectiveLines(effectiveLines)
                .commentLines(commentLines)
                .blankLines(blankLines)
                .fileSize(file.length())
                .languageType(languageType != null ? languageType.name() : "UNKNOWN")
                .build();
    }
    
    @Override
    public int getContributorCount(Long repositoryId) {
        try {
            // 通过Git获取贡献者数量
            return codeRepositoryService.getContributorCount(repositoryId);
        } catch (Exception e) {
            log.error("获取仓库 {} 贡献者数量失败", repositoryId, e);
            return 0;
        }
    }
    
    @Override
    @Transactional
    public void updateProjectStatistics(Long projectId, CodeStatistics statistics) {
        log.info("更新项目 {} 的统计信息", projectId);
        
        try {
            Project project = projectRepository.findById(projectId)
                    .orElseThrow(() -> new IllegalArgumentException("项目不存在: " + projectId));
            
            // 更新统计信息
            project.setLinesOfCode(statistics.getTotalLinesOfCode());
            project.setFileCount(statistics.getTotalFileCount());
            project.setContributorCount(statistics.getContributorCount());
            project.setUpdatedAt(LocalDateTime.now());
            
            projectRepository.save(project);
            
            log.info("项目 {} 统计信息更新完成: 代码行数={}, 文件数={}, 贡献者数={}", 
                    projectId, statistics.getTotalLinesOfCode(), 
                    statistics.getTotalFileCount(), statistics.getContributorCount());
            
        } catch (Exception e) {
            log.error("更新项目 {} 统计信息失败", projectId, e);
            throw new RuntimeException("更新项目统计信息失败", e);
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null || filename.isEmpty()) {
            return "unknown";
        }
        
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex == -1 || lastDotIndex == filename.length() - 1) {
            return "no-extension";
        }
        
        return filename.substring(lastDotIndex + 1).toLowerCase();
    }
}

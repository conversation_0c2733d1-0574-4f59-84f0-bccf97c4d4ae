package com.archscope.domain.service.impl;

import com.archscope.domain.service.StaticSiteService;
import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.service.StaticSiteGenerationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 静态站点服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class StaticSiteServiceImpl implements StaticSiteService {

    private final StaticSiteGenerationService staticSiteGenerationService;
    
    @Value("${static.site.output.dir:/tmp/archscope/sites}")
    private String defaultOutputDir;

    @Override
    public Path generateProjectSite(Long projectId, List<DocumentVersion> documentVersions) {
        log.info("生成项目静态站点，项目ID: {}, 文档版本数量: {}", projectId, documentVersions.size());
        
        // 创建输出目录
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        Path outputDir = Paths.get(defaultOutputDir, "project_" + projectId + "_" + timestamp);
        
        // 生成静态站点
        return staticSiteGenerationService.generateSite(documentVersions, outputDir);
    }

    @Override
    public Path generateCustomSite(List<DocumentVersion> documentVersions, String outputPath) {
        log.info("生成自定义静态站点，文档版本数量: {}, 输出路径: {}", documentVersions.size(), outputPath);
        
        // 使用指定的输出路径或默认路径
        Path outputDir = outputPath != null ? Paths.get(outputPath) : Paths.get(defaultOutputDir, "custom_" + 
                LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")));
        
        // 生成静态站点
        return staticSiteGenerationService.generateSite(documentVersions, outputDir);
    }
}

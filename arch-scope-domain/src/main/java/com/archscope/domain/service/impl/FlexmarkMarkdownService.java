package com.archscope.domain.service.impl;

import com.archscope.domain.service.MarkdownService;
import com.vladsch.flexmark.ext.anchorlink.AnchorLinkExtension;
import com.vladsch.flexmark.ext.autolink.AutolinkExtension;
import com.vladsch.flexmark.ext.emoji.EmojiExtension;
import com.vladsch.flexmark.ext.gfm.strikethrough.StrikethroughExtension;
import com.vladsch.flexmark.ext.gfm.tasklist.TaskListExtension;
import com.vladsch.flexmark.ext.tables.TablesExtension;
import com.vladsch.flexmark.ext.toc.TocExtension;
import com.vladsch.flexmark.html.HtmlRenderer;
import com.vladsch.flexmark.parser.Parser;
import com.vladsch.flexmark.util.ast.Document;
import com.vladsch.flexmark.util.ast.Node;
import com.vladsch.flexmark.util.data.MutableDataSet;
import com.vladsch.flexmark.formatter.Formatter;
import com.vladsch.flexmark.util.misc.Extension;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Markdown服务Flexmark实现
 */
@Slf4j
@Service
public class FlexmarkMarkdownService implements MarkdownService {

    // 标题正则表达式
    private static final Pattern TITLE_PATTERN = Pattern.compile("^\\s*#\\s+(.+)$", Pattern.MULTILINE);

    // Mermaid图表正则表达式
    private static final Pattern MERMAID_PATTERN = Pattern.compile("```mermaid\\s*[\\s\\S]*?```");

    // 数学公式正则表达式
    private static final Pattern MATH_PATTERN = Pattern.compile("\\$\\$[\\s\\S]*?\\$\\$|\\$[^$\\n]+\\$");

    // Flexmark解析器和渲染器
    private final Parser parser;
    private final HtmlRenderer renderer;

    public FlexmarkMarkdownService() {
        // 配置Flexmark选项
        MutableDataSet options = new MutableDataSet();

        // 启用扩展
        options.set(Parser.EXTENSIONS, Arrays.asList(
                TablesExtension.create(),
                StrikethroughExtension.create(),
                AutolinkExtension.create(),
                AnchorLinkExtension.create(),
                TaskListExtension.create(),
                EmojiExtension.create(),
                TocExtension.create()
        ));

        // 配置表格扩展
        options.set(TablesExtension.CLASS_NAME, "table table-bordered");

        // 配置任务列表扩展
        // 注释掉任务列表的特殊配置，使用默认配置

        // 配置目录扩展
        options.set(TocExtension.TITLE, "目录");
        options.set(TocExtension.DIV_CLASS, "toc");

        // 配置HTML渲染选项
        options.set(HtmlRenderer.SOFT_BREAK, "<br />\n");
        options.set(HtmlRenderer.GENERATE_HEADER_ID, true);
        options.set(HtmlRenderer.RENDER_HEADER_ID, true);

        // 创建解析器和渲染器
        parser = Parser.builder(options).build();
        renderer = HtmlRenderer.builder(options).build();
    }

    @Override
    public String convertToHtml(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return "";
        }

        try {
            // 解析Markdown
            Node document = parser.parse(markdown);

            // 渲染HTML
            return renderer.render(document);
        } catch (Exception e) {
            log.error("转换Markdown到HTML失败", e);
            return "<p>Markdown转换失败: " + e.getMessage() + "</p>";
        }
    }

    @Override
    public String convertToHtml(String markdown, Map<String, String> additionalHeadElements) {
        String html = convertToHtml(markdown);

        // 如果包含Mermaid图表，添加Mermaid.js
        if (containsMermaidDiagram(markdown)) {
            additionalHeadElements.put("mermaid",
                    "<script src=\"https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js\"></script>" +
                    "<script>document.addEventListener('DOMContentLoaded', function() { mermaid.initialize({ startOnLoad: true }); });</script>");
        }

        // 如果包含数学公式，添加MathJax
        if (containsMathFormula(markdown)) {
            additionalHeadElements.put("mathjax",
                    "<script src=\"https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js\"></script>");
        }

        // 添加代码高亮
        additionalHeadElements.put("prism",
                "<link rel=\"stylesheet\" href=\"/css/prism.css\">" +
                "<script src=\"/js/prism.js\"></script>");

        return html;
    }

    @Override
    public String extractTitle(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return "无标题";
        }

        // 查找第一个一级标题
        Matcher matcher = TITLE_PATTERN.matcher(markdown);
        if (matcher.find()) {
            return matcher.group(1).trim();
        }

        return "无标题";
    }

    @Override
    public String generateTableOfContents(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return "";
        }

        try {
            // 添加[TOC]标记
            String markdownWithToc = "[TOC]\n\n" + markdown;

            // 解析Markdown
            Document document = parser.parse(markdownWithToc);

            // 渲染HTML
            String html = renderer.render(document);

            // 提取TOC部分
            int tocStart = html.indexOf("<div class=\"toc\">");
            int tocEnd = html.indexOf("</div>", tocStart) + 6;

            if (tocStart >= 0 && tocEnd > tocStart) {
                return html.substring(tocStart, tocEnd);
            }

            return "";
        } catch (Exception e) {
            log.error("生成目录失败", e);
            return "";
        }
    }

    @Override
    public boolean containsMermaidDiagram(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return false;
        }

        return MERMAID_PATTERN.matcher(markdown).find();
    }

    @Override
    public boolean containsMathFormula(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return false;
        }

        return MATH_PATTERN.matcher(markdown).find();
    }

    @Override
    public String extractPlainText(String markdown) {
        if (markdown == null || markdown.isEmpty()) {
            return "";
        }

        try {
            // 解析Markdown
            Document document = parser.parse(markdown);

            // 移除代码块、图表等内容
            String plainText = document.getChars().toString();

            // 移除Markdown标记
            plainText = plainText.replaceAll("#\\s+", "")  // 移除标题标记
                    .replaceAll("\\*\\*(.*?)\\*\\*", "$1")  // 移除粗体
                    .replaceAll("\\*(.*?)\\*", "$1")  // 移除斜体
                    .replaceAll("\\[\\^.*?\\]", "")  // 移除脚注引用
                    .replaceAll("\\[.*?\\]\\(.*?\\)", "$1")  // 移除链接，保留链接文本
                    .replaceAll("```.*?```", "")  // 移除代码块
                    .replaceAll("`(.*?)`", "$1")  // 移除行内代码
                    .replaceAll("\\|.*?\\|", "")  // 移除表格
                    .replaceAll("\\n\\s*[-*+]\\s+", "\n")  // 移除列表标记
                    .replaceAll("\\n\\s*\\d+\\.\\s+", "\n")  // 移除有序列表标记
                    .replaceAll("\\n\\s*>\\s+", "\n")  // 移除引用标记
                    .replaceAll("\\n\\s*---+\\s*\\n", "\n")  // 移除水平线
                    .replaceAll("\\n\\s*\\*\\*\\*+\\s*\\n", "\n")  // 移除水平线
                    .replaceAll("\\n\\s*___+\\s*\\n", "\n")  // 移除水平线
                    .replaceAll("\\s+", " ")  // 将多个空白字符替换为单个空格
                    .trim();

            return plainText;
        } catch (Exception e) {
            log.error("提取纯文本内容失败", e);
            return markdown;
        }
    }
}

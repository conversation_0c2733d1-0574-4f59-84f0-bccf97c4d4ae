package com.archscope.domain.service;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.CapabilityExample;
import com.archscope.domain.valueobject.CapabilityId;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.domain.valueobject.Tag;
import com.archscope.domain.valueobject.Version;

import java.util.List;
import java.util.Set;

/**
 * 能力管理领域服务，负责能力管理的核心业务逻辑
 */
public interface CapabilityManagementDomainService {

    /**
     * 注册新能力
     *
     * @param serviceId 服务ID
     * @param name 能力名称
     * @param description 能力描述
     * @param version 能力版本
     * @param examples 能力示例
     * @param tags 能力标签
     * @return 注册成功的能力实体
     * @throws IllegalArgumentException 如果能力信息无效
     */
    Capability registerCapability(ServiceId serviceId, String name, String description,
                                 Version version, Set<CapabilityExample> examples, Set<Tag> tags);

    /**
     * 验证能力信息
     *
     * @param name 能力名称
     * @param version 能力版本
     * @throws IllegalArgumentException 如果能力信息无效
     */
    void validateCapabilityInfo(String name, Version version);

    /**
     * 检查能力名称是否已存在于指定服务中
     *
     * @param serviceId 服务ID
     * @param name 能力名称
     * @return 如果能力名称已存在则返回true，否则返回false
     */
    boolean isCapabilityNameExistsInService(ServiceId serviceId, String name);

    /**
     * 查找提供指定能力的服务
     *
     * @param capabilityName 能力名称
     * @return 提供指定能力的服务ID列表
     */
    List<ServiceId> findServicesByCapability(String capabilityName);

    /**
     * 查找满足所有指定能力需求的服务
     *
     * @param requiredCapabilities 需要的能力列表
     * @return 满足所有指定能力需求的服务ID列表
     */
    List<ServiceId> findServicesByCapabilityRequirements(List<String> requiredCapabilities);
}
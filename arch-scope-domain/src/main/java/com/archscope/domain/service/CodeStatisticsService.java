package com.archscope.domain.service;

import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.valueobject.CodeStatistics;

import java.io.File;
import java.util.List;

/**
 * 代码统计服务接口
 * 负责计算代码行数、文件数量、贡献者数量等统计信息
 */
public interface CodeStatisticsService {
    
    /**
     * 从解析结果中计算代码统计信息
     * 
     * @param parseResults 文件解析结果列表
     * @return 代码统计信息
     */
    CodeStatistics calculateStatistics(List<FileParseResult> parseResults);
    
    /**
     * 从文件列表中计算代码统计信息
     * 
     * @param files 文件列表
     * @return 代码统计信息
     */
    CodeStatistics calculateStatisticsFromFiles(List<File> files);
    
    /**
     * 计算单个文件的代码行数统计
     * 
     * @param file 文件
     * @return 文件的代码行数统计（总行数、有效代码行、注释行、空行）
     */
    CodeStatistics.FileLineStatistics calculateFileLineStatistics(File file);
    
    /**
     * 从仓库中获取贡献者数量
     * 
     * @param repositoryId 仓库ID
     * @return 贡献者数量
     */
    int getContributorCount(Long repositoryId);
    
    /**
     * 更新项目的统计信息
     * 
     * @param projectId 项目ID
     * @param statistics 统计信息
     */
    void updateProjectStatistics(Long projectId, CodeStatistics statistics);
}

package com.archscope.domain.service.git;

import lombok.Builder;
import lombok.Data;

/**
 * Git 操作结果类
 * 用于封装 Git 操作的结果信息
 */
@Data
@Builder
public class GitOperationResult {
    
    /**
     * 操作是否成功
     */
    private boolean success;
    
    /**
     * 操作消息，成功时为成功信息，失败时为错误信息
     */
    private String message;
    
    /**
     * 操作详情，可包含额外信息如分支名、提交ID等
     */
    private String details;
    
    /**
     * 创建成功结果
     * 
     * @param message 成功消息
     * @param details 详细信息
     * @return 成功的操作结果
     */
    public static GitOperationResult success(String message, String details) {
        return GitOperationResult.builder()
                .success(true)
                .message(message)
                .details(details)
                .build();
    }
    
    /**
     * 创建成功结果
     * 
     * @param message 成功消息
     * @return 成功的操作结果
     */
    public static GitOperationResult success(String message) {
        return success(message, null);
    }
    
    /**
     * 创建失败结果
     * 
     * @param message 错误消息
     * @param details 详细错误信息
     * @return 失败的操作结果
     */
    public static GitOperationResult failure(String message, String details) {
        return GitOperationResult.builder()
                .success(false)
                .message(message)
                .details(details)
                .build();
    }
    
    /**
     * 创建失败结果
     * 
     * @param message 错误消息
     * @return 失败的操作结果
     */
    public static GitOperationResult failure(String message) {
        return failure(message, null);
    }
}

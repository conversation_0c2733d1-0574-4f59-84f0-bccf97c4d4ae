package com.archscope.domain.service;

import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.valueobject.Metadata;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.domain.valueobject.Tag;
import com.archscope.domain.valueobject.Version;

import java.net.URL;
import java.util.Set;

/**
 * 服务注册领域服务，负责服务注册和验证的核心业务逻辑
 */
public interface ServiceRegistrationDomainService {

    /**
     * 注册新服务
     *
     * @param name 服务名称
     * @param description 服务描述
     * @param type 服务类型
     * @param version 服务版本
     * @param endpoint 服务端点URL
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @param tags 服务标签
     * @param status 服务状态
     * @param metadata 服务元数据
     * @return 注册成功的服务实体
     * @throws IllegalArgumentException 如果服务信息无效
     */
    Service registerService(String name, String description, ServiceType type, Version version,
                           URL endpoint, String groupId, String artifactId, Set<Tag> tags,
                           ServiceStatus status, Metadata metadata);

    /**
     * 验证服务信息
     *
     * @param name 服务名称
     * @param type 服务类型
     * @param version 服务版本
     * @param endpoint 服务端点URL
     * @throws IllegalArgumentException 如果服务信息无效
     */
    void validateServiceInfo(String name, ServiceType type, Version version, URL endpoint);

    /**
     * 验证Maven坐标
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @throws IllegalArgumentException 如果Maven坐标无效
     */
    void validateMavenCoordinates(String groupId, String artifactId);

    /**
     * 检查服务名称是否已存在
     *
     * @param name 服务名称
     * @return 如果服务名称已存在则返回true，否则返回false
     */
    boolean isServiceNameExists(String name);

    /**
     * 检查服务端点是否已存在
     *
     * @param endpoint 服务端点URL
     * @return 如果服务端点已存在则返回true，否则返回false
     */
    boolean isServiceEndpointExists(URL endpoint);

    /**
     * 检查Maven坐标是否已存在
     *
     * @param groupId Maven/Gradle坐标 - groupId
     * @param artifactId Maven/Gradle坐标 - artifactId
     * @param version 服务版本
     * @return 如果Maven坐标已存在则返回true，否则返回false
     */
    boolean isMavenCoordinatesExists(String groupId, String artifactId, Version version);
}
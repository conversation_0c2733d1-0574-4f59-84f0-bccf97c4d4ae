package com.archscope.domain.service;

import com.archscope.domain.valueobject.DocumentType;

import java.util.List;

/**
 * 文档生成服务接口
 * 负责生成项目文档
 */
public interface DocumentGenerationService {
    
    /**
     * 生成项目文档
     *
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @param docTypes 文档类型列表
     * @param templateId 模板ID
     * @param outputFormat 输出格式
     * @param outputPath 输出路径
     * @param includeArchDiagrams 是否包含架构图
     * @return 生成的文档路径
     */
    String generateDocumentation(
            Long repositoryId,
            String commitId,
            List<DocumentType> docTypes,
            String templateId,
            String outputFormat,
            String outputPath,
            boolean includeArchDiagrams
    );
    
    /**
     * 获取可用的文档模板
     *
     * @return 模板ID列表
     */
    List<String> getAvailableTemplates();
    
    /**
     * 获取支持的输出格式
     *
     * @return 输出格式列表
     */
    List<String> getSupportedOutputFormats();
}

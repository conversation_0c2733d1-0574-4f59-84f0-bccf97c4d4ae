package com.archscope.domain.service;

import com.archscope.domain.entity.DocumentVersion;

import java.nio.file.Path;
import java.util.List;
import java.util.Map;

/**
 * 文档搜索服务接口
 * 负责为静态站点生成搜索索引和提供搜索功能
 */
public interface DocumentSearchService {
    
    /**
     * 为文档列表生成搜索索引
     * 
     * @param documentVersions 文档版本列表
     * @param outputDir 输出目录
     * @return 生成的索引文件路径
     */
    Path generateSearchIndex(List<DocumentVersion> documentVersions, Path outputDir);
    
    /**
     * 提取文档内容用于索引
     * 
     * @param documentVersion 文档版本
     * @return 提取的文档内容
     */
    Map<String, Object> extractDocumentContent(DocumentVersion documentVersion);
    
    /**
     * 获取搜索相关的JavaScript文件
     * 
     * @return 搜索相关的JavaScript文件列表
     */
    List<String> getSearchScripts();
    
    /**
     * 获取搜索相关的CSS文件
     * 
     * @return 搜索相关的CSS文件列表
     */
    List<String> getSearchStyles();
}

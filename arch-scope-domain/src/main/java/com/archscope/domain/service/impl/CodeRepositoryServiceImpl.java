package com.archscope.domain.service.impl;

import com.archscope.domain.entity.CodeRepository;
import com.archscope.domain.repository.CodeRepositoryRepository;
import com.archscope.domain.service.CodeRepositoryService;
import com.archscope.domain.valueobject.RepositoryStatus;
import com.archscope.domain.service.git.GitOperationResult;
import com.archscope.domain.service.git.GitService;
import com.archscope.domain.util.BranchFilterUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jgit.api.Git;
import org.eclipse.jgit.api.ListBranchCommand;
import org.eclipse.jgit.diff.DiffEntry;
import org.eclipse.jgit.treewalk.filter.PathFilter;
import org.eclipse.jgit.lib.ObjectId;
import org.eclipse.jgit.lib.ObjectReader;
import org.eclipse.jgit.lib.PersonIdent;
import org.eclipse.jgit.lib.Ref;
import org.eclipse.jgit.lib.Repository;
import org.eclipse.jgit.revwalk.RevCommit;
import org.eclipse.jgit.revwalk.RevTree;
import org.eclipse.jgit.revwalk.RevWalk;
import org.eclipse.jgit.treewalk.AbstractTreeIterator;
import org.eclipse.jgit.treewalk.CanonicalTreeParser;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 代码仓库领域服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CodeRepositoryServiceImpl implements CodeRepositoryService {

    private final CodeRepositoryRepository codeRepositoryRepository;
    private final GitService gitService;

    @Value("${archscope.repository.local-storage:/tmp/archscope/repositories}")
    private String localStoragePath;

    @Override
    @Transactional
    public CodeRepository createRepository(CodeRepository codeRepository) {
        log.info("创建代码仓库: {}", codeRepository.getName());

        // 设置创建时间和状态
        if (codeRepository.getCreatedAt() == null) {
            codeRepository.setCreatedAt(LocalDateTime.now());
        }

        if (codeRepository.getStatus() == null) {
            codeRepository.setStatus(RepositoryStatus.ACTIVE);
        }

        // 保存到数据库
        return codeRepositoryRepository.save(codeRepository);
    }

    @Override
    @Transactional
    public CodeRepository updateRepository(CodeRepository codeRepository) {
        log.info("更新代码仓库: {}", codeRepository.getName());

        // 验证仓库是否存在
        CodeRepository existingRepo = codeRepositoryRepository.findById(codeRepository.getId())
                .orElseThrow(() -> new IllegalArgumentException("仓库不存在: " + codeRepository.getId()));

        // 保留创建时间
        codeRepository.setCreatedAt(existingRepo.getCreatedAt());

        // 更新到数据库
        return codeRepositoryRepository.update(codeRepository);
    }

    @Override
    public Optional<CodeRepository> findRepositoryById(Long id) {
        log.debug("根据ID查找代码仓库: {}", id);
        return codeRepositoryRepository.findById(id);
    }

    @Override
    public List<CodeRepository> findRepositoriesByProjectId(Long projectId) {
        log.debug("查找项目的代码仓库列表, 项目ID: {}", projectId);
        return codeRepositoryRepository.findByProjectId(projectId);
    }

    @Override
    @Transactional
    public CodeRepository syncRepository(Long id) {
        log.info("同步代码仓库, ID: {}", id);

        // 获取仓库信息
        CodeRepository repository = codeRepositoryRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("仓库不存在: " + id));

        // 更新状态为同步中
        repository.setStatus(RepositoryStatus.SYNCING);
        codeRepositoryRepository.update(repository);

        try {
            // 获取本地仓库路径
            Path localPath = getLocalRepositoryPath(repository);
            File localRepo = localPath.toFile();

            // 准备认证信息
            Map<String, String> credentials = prepareCredentials(repository);

            GitOperationResult result;
            if (!localRepo.exists() || !new File(localRepo, ".git").exists()) {
                // 如果本地仓库不存在，则克隆
                result = gitService.cloneRepository(repository.getUrl(), localPath, credentials);
            } else {
                // 如果本地仓库已存在，则获取最新变更
                result = gitService.fetchChanges(localPath, "origin", credentials);
            }

            if (result.isSuccess()) {
                // 同步成功，更新状态和同步时间
                repository.setStatus(RepositoryStatus.ACTIVE);
                repository.setLastSyncedAt(LocalDateTime.now());
                log.info("代码仓库同步成功, ID: {}", id);
            } else {
                // 同步失败，更新状态
                repository.setStatus(RepositoryStatus.SYNC_FAILED);
                log.error("代码仓库同步失败, ID: {}, 错误: {}", id, result.getMessage());
            }

            // 保存更新后的仓库信息
            return codeRepositoryRepository.update(repository);

        } catch (Exception e) {
            // 发生异常，更新状态为同步失败
            repository.setStatus(RepositoryStatus.SYNC_FAILED);
            codeRepositoryRepository.update(repository);
            log.error("代码仓库同步异常, ID: {}", id, e);
            throw new RuntimeException("代码仓库同步失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean validateRepositoryConnection(CodeRepository codeRepository) {
        log.info("验证仓库连接: {}", codeRepository.getUrl());

        try {
            // 创建临时目录
            Path tempDir = Files.createTempDirectory("archscope_repo_validate_");

            // 准备认证信息
            Map<String, String> credentials = prepareCredentials(codeRepository);

            // 尝试克隆仓库
            GitOperationResult result = gitService.cloneRepository(codeRepository.getUrl(), tempDir, credentials);

            // 清理临时目录
            try {
                Files.walk(tempDir)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
            } catch (IOException e) {
                log.warn("清理临时目录失败: {}", tempDir, e);
            }

            return result.isSuccess();

        } catch (Exception e) {
            log.error("验证仓库连接失败: {}", codeRepository.getUrl(), e);
            return false;
        }
    }

    @Override
    @Transactional
    public void deleteRepository(Long id) {
        log.info("删除代码仓库, ID: {}", id);

        // 获取仓库信息
        CodeRepository repository = codeRepositoryRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("仓库不存在: " + id));

        // 删除本地仓库目录
        try {
            Path localPath = getLocalRepositoryPath(repository);
            if (Files.exists(localPath)) {
                Files.walk(localPath)
                    .sorted(Comparator.reverseOrder())
                    .map(Path::toFile)
                    .forEach(File::delete);
            }
        } catch (IOException e) {
            log.warn("删除本地仓库目录失败, ID: {}", id, e);
        }

        // 从数据库中删除
        codeRepositoryRepository.delete(id);
    }

    @Override
    public List<String> getRepositoryBranches(Long id) {
        log.debug("获取仓库分支列表, ID: {}", id);

        // 获取仓库信息
        CodeRepository repository = codeRepositoryRepository.findById(id)
                .orElseThrow(() -> new IllegalArgumentException("仓库不存在: " + id));

        // 获取本地仓库路径
        Path localPath = getLocalRepositoryPath(repository);

        // 确保仓库已克隆
        if (!localPath.toFile().exists() || !new File(localPath.toFile(), ".git").exists()) {
            log.info("本地仓库不存在，正在克隆...");
            syncRepository(id);
        }

        try (Git git = Git.open(localPath.toFile())) {
            // 获取远程分支
            List<Ref> branches = git.branchList()
                    .setListMode(ListBranchCommand.ListMode.ALL)
                    .call();

            // 提取分支名称
            Set<String> branchNameSet = new HashSet<>();
            for (Ref ref : branches) {
                String name = ref.getName();
                // 处理远程分支名称，去掉 refs/remotes/origin/ 前缀
                if (name.startsWith("refs/remotes/origin/")) {
                    name = name.substring("refs/remotes/origin/".length());
                    if (!"HEAD".equals(name)) {
                        branchNameSet.add(name);
                    }
                }
                // 处理本地分支名称，去掉 refs/heads/ 前缀
                else if (name.startsWith("refs/heads/")) {
                    name = name.substring("refs/heads/".length());
                    branchNameSet.add(name);
                }
            }

            List<String> allBranches = new ArrayList<>(branchNameSet);
            Collections.sort(allBranches);

            // 应用分支过滤，移除release、feature等分支
            List<String> filteredBranches = BranchFilterUtil.filterBranches(allBranches);
            log.debug("仓库分支过滤结果: 原始分支数={}, 过滤后分支数={}", allBranches.size(), filteredBranches.size());

            return filteredBranches;

        } catch (Exception e) {
            log.error("获取仓库分支列表失败, ID: {}", id, e);
            // 发生异常时返回一个默认的分支列表
            return Arrays.asList("main", "master", "develop");
        }
    }

    @Override
    public List<String> getRepositoryChanges(Long repositoryId, String fromCommit, String toCommit) {
        log.debug("获取仓库变更文件列表, 仓库ID: {}, 从提交: {}, 到提交: {}", repositoryId, fromCommit, toCommit);

        // 获取仓库信息
        CodeRepository repository = codeRepositoryRepository.findById(repositoryId)
                .orElseThrow(() -> new IllegalArgumentException("仓库不存在: " + repositoryId));

        // 获取本地仓库路径
        Path localPath = getLocalRepositoryPath(repository);

        // 确保仓库已克隆
        if (!localPath.toFile().exists() || !new File(localPath.toFile(), ".git").exists()) {
            log.info("本地仓库不存在，正在克隆...");
            syncRepository(repositoryId);
        }

        try (Git git = Git.open(localPath.toFile())) {
            // 获取两个提交之间的差异
            List<DiffEntry> diffs = git.diff()
                    .setOldTree(prepareTreeParser(git.getRepository(), fromCommit))
                    .setNewTree(prepareTreeParser(git.getRepository(), toCommit))
                    .call();

            // 提取变更文件路径
            List<String> changedFiles = new ArrayList<>();
            for (DiffEntry diff : diffs) {
                if (diff.getChangeType() == DiffEntry.ChangeType.DELETE) {
                    changedFiles.add(diff.getOldPath());
                } else {
                    changedFiles.add(diff.getNewPath());
                }
            }

            return changedFiles;

        } catch (Exception e) {
            log.error("获取仓库变更文件列表失败, 仓库ID: {}, 从提交: {}, 到提交: {}",
                    repositoryId, fromCommit, toCommit, e);
            return new ArrayList<>();
        }
    }

    @Override
    public String getFileDiff(Long repositoryId, String filePath, String fromCommit, String toCommit) {
        log.debug("获取文件差异, 仓库ID: {}, 文件路径: {}, 从提交: {}, 到提交: {}",
                repositoryId, filePath, fromCommit, toCommit);

        // 获取仓库信息
        CodeRepository repository = codeRepositoryRepository.findById(repositoryId)
                .orElseThrow(() -> new IllegalArgumentException("仓库不存在: " + repositoryId));

        // 获取本地仓库路径
        Path localPath = getLocalRepositoryPath(repository);

        // 确保仓库已克隆
        if (!localPath.toFile().exists() || !new File(localPath.toFile(), ".git").exists()) {
            log.info("本地仓库不存在，正在克隆...");
            syncRepository(repositoryId);
        }

        try (Git git = Git.open(localPath.toFile())) {
            // 获取文件差异
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            git.diff()
                    .setOldTree(prepareTreeParser(git.getRepository(), fromCommit))
                    .setNewTree(prepareTreeParser(git.getRepository(), toCommit))
                    .setPathFilter(PathFilter.create(filePath))
                    .setOutputStream(out)
                    .call();

            return out.toString(StandardCharsets.UTF_8.name());

        } catch (Exception e) {
            log.error("获取文件差异失败, 仓库ID: {}, 文件路径: {}, 从提交: {}, 到提交: {}",
                    repositoryId, filePath, fromCommit, toCommit, e);
            return "";
        }
    }

    @Override
    public String getFileLastCommit(Long repositoryId, String filePath) {
        log.debug("获取文件最后修改提交, 仓库ID: {}, 文件路径: {}", repositoryId, filePath);

        // 获取仓库信息
        CodeRepository repository = codeRepositoryRepository.findById(repositoryId)
                .orElseThrow(() -> new IllegalArgumentException("仓库不存在: " + repositoryId));

        // 获取本地仓库路径
        Path localPath = getLocalRepositoryPath(repository);

        // 确保仓库已克隆
        if (!localPath.toFile().exists() || !new File(localPath.toFile(), ".git").exists()) {
            log.info("本地仓库不存在，正在克隆...");
            syncRepository(repositoryId);
        }

        try (Git git = Git.open(localPath.toFile())) {
            // 获取文件最后修改提交
            Iterable<RevCommit> logs = git.log()
                    .addPath(filePath)
                    .setMaxCount(1)
                    .call();

            // 获取第一个提交（最新的）
            RevCommit latestCommit = logs.iterator().next();
            return latestCommit.getName();

        } catch (Exception e) {
            log.error("获取文件最后修改提交失败, 仓库ID: {}, 文件路径: {}", repositoryId, filePath, e);
            return "";
        }
    }

    @Override
    public Map<String, Object> getCommitDetails(Long repositoryId, String commitId) {
        log.debug("获取提交详细信息, 仓库ID: {}, 提交ID: {}", repositoryId, commitId);

        // 获取仓库信息
        CodeRepository repository = codeRepositoryRepository.findById(repositoryId)
                .orElseThrow(() -> new IllegalArgumentException("仓库不存在: " + repositoryId));

        // 获取本地仓库路径
        Path localPath = getLocalRepositoryPath(repository);

        // 确保仓库已克隆
        if (!localPath.toFile().exists() || !new File(localPath.toFile(), ".git").exists()) {
            log.info("本地仓库不存在，正在克隆...");
            syncRepository(repositoryId);
        }

        try (Git git = Git.open(localPath.toFile())) {
            // 获取提交详细信息
            RevWalk revWalk = new RevWalk(git.getRepository());
            RevCommit commit = revWalk.parseCommit(git.getRepository().resolve(commitId));

            // 构建提交详细信息Map
            Map<String, Object> commitDetails = new HashMap<>();
            commitDetails.put("id", commit.getName());
            commitDetails.put("shortId", commit.getName().substring(0, 7));
            commitDetails.put("message", commit.getFullMessage());
            commitDetails.put("shortMessage", commit.getShortMessage());
            commitDetails.put("authorName", commit.getAuthorIdent().getName());
            commitDetails.put("authorEmail", commit.getAuthorIdent().getEmailAddress());
            commitDetails.put("authorDate", new Date(commit.getAuthorIdent().getWhen().getTime()));
            commitDetails.put("committerName", commit.getCommitterIdent().getName());
            commitDetails.put("committerEmail", commit.getCommitterIdent().getEmailAddress());
            commitDetails.put("commitDate", new Date(commit.getCommitterIdent().getWhen().getTime()));

            // 获取父提交
            List<String> parentIds = new ArrayList<>();
            for (RevCommit parent : commit.getParents()) {
                parentIds.add(parent.getName());
            }
            commitDetails.put("parentIds", parentIds);

            revWalk.dispose();
            return commitDetails;

        } catch (Exception e) {
            log.error("获取提交详细信息失败, 仓库ID: {}, 提交ID: {}", repositoryId, commitId, e);
            return new HashMap<>();
        }
    }

    @Override
    public int getContributorCount(Long repositoryId) {
        log.debug("获取仓库贡献者数量, 仓库ID: {}", repositoryId);

        // 获取仓库信息
        CodeRepository repository = codeRepositoryRepository.findById(repositoryId)
                .orElseThrow(() -> new IllegalArgumentException("仓库不存在: " + repositoryId));

        // 获取本地仓库路径
        Path localPath = getLocalRepositoryPath(repository);

        // 确保仓库已克隆
        if (!localPath.toFile().exists() || !new File(localPath.toFile(), ".git").exists()) {
            log.info("本地仓库不存在，正在克隆...");
            syncRepository(repositoryId);
        }

        try (Git git = Git.open(localPath.toFile())) {
            // 获取所有提交的作者
            Set<String> contributors = new HashSet<>();

            Iterable<RevCommit> commits = git.log().all().call();
            for (RevCommit commit : commits) {
                PersonIdent author = commit.getAuthorIdent();
                if (author != null && author.getEmailAddress() != null) {
                    contributors.add(author.getEmailAddress().toLowerCase());
                }

                PersonIdent committer = commit.getCommitterIdent();
                if (committer != null && committer.getEmailAddress() != null) {
                    contributors.add(committer.getEmailAddress().toLowerCase());
                }
            }

            int contributorCount = contributors.size();
            log.debug("仓库 {} 贡献者数量: {}", repositoryId, contributorCount);
            return contributorCount;

        } catch (Exception e) {
            log.error("获取仓库贡献者数量失败", e);
            return 0;
        }
    }

    /**
     * 获取本地仓库路径
     *
     * @param repository 代码仓库
     * @return 本地仓库路径
     */
    private Path getLocalRepositoryPath(CodeRepository repository) {
        // 创建基于仓库ID的本地路径
        return Paths.get(localStoragePath, "repo_" + repository.getId());
    }

    /**
     * 准备Git操作的认证信息
     *
     * @param repository 代码仓库
     * @return 认证信息Map
     */
    private Map<String, String> prepareCredentials(CodeRepository repository) {
        Map<String, String> credentials = new HashMap<>();

        if (repository.isSshEnabled()) {
            // SSH认证
            credentials.put("privateKeyPath", repository.getSshKey());
            if (repository.getSshKeyPassphrase() != null) {
                credentials.put("passphrase", repository.getSshKeyPassphrase());
            }
        } else if (repository.getAccessToken() != null && !repository.getAccessToken().isEmpty()) {
            // 访问令牌认证
            credentials.put("username", "oauth2");
            credentials.put("password", repository.getAccessToken());
        } else if (repository.getUsername() != null && !repository.getUsername().isEmpty()) {
            // 用户名密码认证
            credentials.put("username", repository.getUsername());
            credentials.put("password", repository.getPassword());
        }

        return credentials;
    }

    /**
     * 准备树解析器，用于Git差异比较
     *
     * @param repository Git仓库
     * @param commitId 提交ID
     * @return 树迭代器
     * @throws IOException 如果发生IO错误
     */
    private AbstractTreeIterator prepareTreeParser(Repository repository, String commitId) throws IOException {
        try (RevWalk walk = new RevWalk(repository)) {
            ObjectId objectId = repository.resolve(commitId);
            if (objectId == null) {
                throw new IllegalArgumentException("无效的提交ID: " + commitId);
            }

            RevCommit commit = walk.parseCommit(objectId);
            RevTree tree = commit.getTree();
            CanonicalTreeParser treeParser = new CanonicalTreeParser();

            try (ObjectReader reader = repository.newObjectReader()) {
                treeParser.reset(reader, tree.getId());
            }

            walk.dispose();
            return treeParser;
        }
    }
}

package com.archscope.domain.repository;

import com.archscope.domain.entity.DocumentVersion;
import com.archscope.domain.valueobject.DocumentType;

import java.util.List;
import java.util.Optional;

/**
 * 文档版本仓储接口
 * 负责文档版本的存储和检索
 */
public interface DocumentVersionRepository {

    /**
     * 保存文档版本
     *
     * @param documentVersion 文档版本
     * @return 保存后的文档版本
     */
    DocumentVersion save(DocumentVersion documentVersion);

    /**
     * 根据ID查找文档版本
     *
     * @param id 文档版本ID
     * @return 文档版本
     */
    Optional<DocumentVersion> findById(Long id);

    /**
     * 根据ID列表查找文档版本列表
     *
     * @param ids 文档版本ID列表
     * @return 文档版本列表
     */
    List<DocumentVersion> findByIds(List<Long> ids);

    /**
     * 根据项目ID查找文档版本列表
     *
     * @param projectId 项目ID
     * @return 文档版本列表
     */
    List<DocumentVersion> findByProjectId(Long projectId);

    /**
     * 根据项目ID和发布状态查找文档版本列表
     *
     * @param projectId 项目ID
     * @param isPublished 是否已发布
     * @return 文档版本列表
     */
    List<DocumentVersion> findByProjectIdAndIsPublished(Long projectId, Boolean isPublished);

    /**
     * 根据项目ID和文档类型查找文档版本列表
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @return 文档版本列表
     */
    List<DocumentVersion> findByProjectIdAndDocType(Long projectId, DocumentType docType);

    /**
     * 根据项目ID和提交ID查找文档版本列表
     *
     * @param projectId 项目ID
     * @param commitId 提交ID
     * @return 文档版本列表
     */
    List<DocumentVersion> findByProjectIdAndCommitId(Long projectId, String commitId);

    /**
     * 根据项目ID和版本标签查找文档版本
     *
     * @param projectId 项目ID
     * @param versionTag 版本标签
     * @return 文档版本
     */
    Optional<DocumentVersion> findByProjectIdAndVersionTag(Long projectId, String versionTag);

    /**
     * 获取项目的最新文档版本
     *
     * @param projectId 项目ID
     * @param docType 文档类型
     * @return 最新文档版本
     */
    Optional<DocumentVersion> findLatestByProjectIdAndDocType(Long projectId, DocumentType docType);

    /**
     * 更新文档版本
     *
     * @param documentVersion 文档版本
     * @return 更新后的文档版本
     */
    DocumentVersion update(DocumentVersion documentVersion);

    /**
     * 删除文档版本
     *
     * @param id 文档版本ID
     */
    void delete(Long id);

    /**
     * 删除项目的所有文档版本
     *
     * @param projectId 项目ID
     * @return 删除的文档版本数量
     */
    int deleteByProjectId(Long projectId);

    /**
     * 获取项目的文档版本数量
     *
     * @param projectId 项目ID
     * @return 文档版本数量
     */
    long countByProjectId(Long projectId);
}

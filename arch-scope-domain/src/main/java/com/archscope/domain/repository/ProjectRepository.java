package com.archscope.domain.repository;

import com.archscope.domain.entity.Project;
import com.archscope.domain.valueobject.ProjectType;
import java.util.List;
import java.util.Optional;

/**
 * 项目仓库接口，定义项目相关的数据访问方法
 */
public interface ProjectRepository {
    
    /**
     * 通过ID查找项目
     * @param id 项目ID
     * @return 项目实体（可能为空）
     */
    Optional<Project> findById(Long id);
    
    /**
     * 通过名称查找项目
     * @param name 项目名称
     * @return 项目实体（可能为空）
     */
    Optional<Project> findByName(String name);
    
    /**
     * 通过仓库URL查找项目
     * @param repositoryUrl 仓库URL
     * @return 项目实体（可能为空）
     */
    Optional<Project> findByRepositoryUrl(String repositoryUrl);

    /**
     * 通过标准化仓库URL查找项目
     * @param normalizedRepositoryUrl 标准化的仓库URL
     * @return 项目实体（可能为空）
     */
    Optional<Project> findByNormalizedRepositoryUrl(String normalizedRepositoryUrl);
    
    /**
     * 保存项目
     * @param project 项目实体
     * @return 保存后的项目实体（包含ID等数据库生成的字段）
     */
    Project save(Project project);
    
    /**
     * 更新项目
     * @param project 项目实体
     * @return 更新后的项目实体
     */
    Project update(Project project);
    
    /**
     * 删除项目
     * @param id 项目ID
     */
    void delete(Long id);
    
    /**
     * 查询所有项目
     * @return 项目列表
     */
    List<Project> findAll();
    
    /**
     * 分页查询项目
     * @param page 页码（从0开始）
     * @param size 每页大小
     * @return 项目列表
     */
    List<Project> findAll(int page, int size);
    
    /**
     * 通过类型查询项目
     * @param type 项目类型
     * @return 项目列表
     */
    List<Project> findByType(ProjectType type);
    
    /**
     * 通过用户ID查询关联的项目
     * @param userId 用户ID
     * @return 项目列表
     */
    List<Project> findByUserId(Long userId);
    
    /**
     * 通过创建时间降序排序查询最近项目
     * @param limit 限制数量
     * @return 项目列表
     */
    List<Project> findRecentProjects(int limit);
    
    /**
     * 通过星级降序排序查询项目
     * @param limit 限制数量
     * @return 项目列表
     */
    List<Project> findTopRatedProjects(int limit);
    
    /**
     * 查询项目总数
     * @return 项目总数
     */
    long count();

    /**
     * 根据创建者ID查找项目
     *
     * @param creatorId 创建者ID
     * @return 项目列表
     */
    List<Project> findByCreatorId(Long creatorId);

    /**
     * 删除项目
     *
     * @param id 项目ID
     */
    void deleteById(Long id);

    /**
     * 检查具有给定仓库URL的项目是否存在
     *
     * @param repositoryUrl 仓库URL
     * @return 如果存在则返回 true，否则返回 false
     */
    boolean existsByRepositoryUrl(String repositoryUrl);

    /**
     * 检查具有给定标准化仓库URL的项目是否存在
     *
     * @param normalizedRepositoryUrl 标准化的仓库URL
     * @return 如果存在则返回 true，否则返回 false
     */
    boolean existsByNormalizedRepositoryUrl(String normalizedRepositoryUrl);
} 
package com.archscope.domain.repository;

import com.archscope.domain.entity.CodeRepository;

import java.util.List;
import java.util.Optional;

/**
 * 代码仓库仓储接口
 */
public interface CodeRepositoryRepository {
    /**
     * 保存代码仓库
     * @param codeRepository 代码仓库
     * @return 保存后的代码仓库
     */
    CodeRepository save(CodeRepository codeRepository);
    
    /**
     * 根据ID查找代码仓库
     * @param id 代码仓库ID
     * @return 代码仓库
     */
    Optional<CodeRepository> findById(Long id);
    
    /**
     * 根据项目ID查找代码仓库列表
     * @param projectId 项目ID
     * @return 代码仓库列表
     */
    List<CodeRepository> findByProjectId(Long projectId);
    
    /**
     * 根据URL查找代码仓库
     * @param url 仓库URL
     * @return 代码仓库
     */
    Optional<CodeRepository> findByUrl(String url);
    
    /**
     * 更新代码仓库
     * @param codeRepository 代码仓库
     * @return 更新后的代码仓库
     */
    CodeRepository update(CodeRepository codeRepository);
    
    /**
     * 删除代码仓库
     * @param id 代码仓库ID
     */
    void delete(Long id);
    
    /**
     * 查找所有代码仓库
     * @return 代码仓库列表
     */
    List<CodeRepository> findAll();
} 
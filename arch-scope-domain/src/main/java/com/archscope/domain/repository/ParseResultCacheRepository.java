package com.archscope.domain.repository;

import com.archscope.domain.entity.ParseResultCache;

import java.util.Optional;
import java.util.List;

/**
 * 解析结果缓存仓库接口
 */
public interface ParseResultCacheRepository {
    
    /**
     * 保存解析结果缓存
     * 
     * @param parseResultCache 解析结果缓存
     * @return 保存后的解析结果缓存
     */
    ParseResultCache save(ParseResultCache parseResultCache);
    
    /**
     * 根据ID查找解析结果缓存
     * 
     * @param id 缓存ID
     * @return 解析结果缓存
     */
    Optional<ParseResultCache> findById(Long id);
    
    /**
     * 根据仓库ID和提交ID查找解析结果缓存
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @return 解析结果缓存
     */
    Optional<ParseResultCache> findByRepositoryIdAndCommitId(Long repositoryId, String commitId);
    
    /**
     * 根据仓库ID查找所有解析结果缓存
     * 
     * @param repositoryId 代码仓库ID
     * @return 解析结果缓存列表
     */
    List<ParseResultCache> findAllByRepositoryId(Long repositoryId);
    
    /**
     * 删除解析结果缓存
     * 
     * @param id 缓存ID
     */
    void deleteById(Long id);
    
    /**
     * 删除仓库的所有解析结果缓存
     * 
     * @param repositoryId 代码仓库ID
     */
    void deleteAllByRepositoryId(Long repositoryId);
    
    /**
     * 检查解析结果缓存是否存在
     * 
     * @param repositoryId 代码仓库ID
     * @param commitId 提交ID
     * @return 是否存在
     */
    boolean existsByRepositoryIdAndCommitId(Long repositoryId, String commitId);
}

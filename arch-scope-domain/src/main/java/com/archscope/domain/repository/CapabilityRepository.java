package com.archscope.domain.repository;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.valueobject.CapabilityId;
import com.archscope.domain.valueobject.ServiceId;

import java.util.List;

/**
 * 能力仓储接口，定义能力实体的持久化操作
 */
public interface CapabilityRepository {

    /**
     * 保存能力实体
     *
     * @param capability 能力实体
     * @return 保存后的能力实体
     */
    Capability save(Capability capability);

    /**
     * 根据ID查找能力
     *
     * @param id 能力ID
     * @return 找到的能力实体，如果不存在则返回null
     */
    Capability findById(CapabilityId id);

    /**
     * 根据服务ID和能力名称查找能力
     *
     * @param serviceId 服务ID
     * @param name 能力名称
     * @return 找到的能力实体，如果不存在则返回null
     */
    Capability findByServiceIdAndName(ServiceId serviceId, String name);

    /**
     * 根据服务ID查找能力列表
     *
     * @param serviceId 服务ID
     * @return 找到的能力实体列表
     */
    List<Capability> findByServiceId(ServiceId serviceId);

    /**
     * 根据名称查找能力列表
     *
     * @param name 能力名称
     * @return 找到的能力实体列表
     */
    List<Capability> findByName(String name);

    /**
     * 查找所有能力名称
     *
     * @return 所有能力名称列表
     */
    List<String> findAllCapabilityNames();

    /**
     * 根据能力名称查找提供该能力的服务ID列表
     *
     * @param capabilityName 能力名称
     * @return 提供该能力的服务ID列表
     */
    List<ServiceId> findServiceIdsByCapabilityName(String capabilityName);

    /**
     * 根据能力名称列表查找提供这些能力的服务ID列表
     *
     * @param capabilityNames 能力名称列表
     * @return 提供这些能力的服务ID列表
     */
    List<ServiceId> findServiceIdsByCapabilityNames(List<String> capabilityNames);

    /**
     * 分页查询能力
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的能力实体列表
     */
    List<Capability> findWithPagination(int offset, int limit);

    /**
     * 查找所有能力
     *
     * @return 所有能力实体列表
     */
    List<Capability> findAll();

    /**
     * 根据描述模糊搜索能力
     *
     * @param descriptionLike 描述关键词
     * @return 匹配的能力实体列表
     */
    List<Capability> searchByDescription(String descriptionLike);

    /**
     * 查找最近添加的能力
     *
     * @param limit 限制数量
     * @return 最近添加的能力实体列表
     */
    List<Capability> findRecentlyAdded(int limit);

    /**
     * 查找最近更新的能力
     *
     * @param limit 限制数量
     * @return 最近更新的能力实体列表
     */
    List<Capability> findRecentlyUpdated(int limit);

    /**
     * 删除能力
     *
     * @param id 能力ID
     * @return 是否删除成功
     */
    boolean delete(CapabilityId id);

    /**
     * 删除服务的所有能力
     *
     * @param serviceId 服务ID
     * @return 是否删除成功
     */
    boolean deleteByServiceId(ServiceId serviceId);

    /**
     * 统计能力总数
     *
     * @return 能力总数
     */
    long count();
}
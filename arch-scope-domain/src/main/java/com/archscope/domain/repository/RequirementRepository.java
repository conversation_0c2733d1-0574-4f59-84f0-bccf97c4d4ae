package com.archscope.domain.repository;

import com.archscope.domain.model.servicediscovery.Requirement;
import com.archscope.domain.model.servicediscovery.RequirementPriority;
import com.archscope.domain.valueobject.RequirementId;
import com.archscope.domain.valueobject.ServiceId;

import java.util.List;

/**
 * 需求仓储接口，定义需求实体的持久化操作
 */
public interface RequirementRepository {

    /**
     * 保存需求实体
     *
     * @param requirement 需求实体
     * @return 保存后的需求实体
     */
    Requirement save(Requirement requirement);

    /**
     * 根据ID查找需求
     *
     * @param id 需求ID
     * @return 找到的需求实体，如果不存在则返回null
     */
    Requirement findById(RequirementId id);

    /**
     * 根据优先级查找需求列表
     *
     * @param priority 需求优先级
     * @return 找到的需求实体列表
     */
    List<Requirement> findByPriority(RequirementPriority priority);

    /**
     * 根据所需能力查找需求列表
     *
     * @param capabilityName 能力名称
     * @return 找到的需求实体列表
     */
    List<Requirement> findByRequiredCapability(String capabilityName);

    /**
     * 查找所有需求
     *
     * @return 所有需求实体列表
     */
    List<Requirement> findAll();

    /**
     * 分页查询需求
     *
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 分页的需求实体列表
     */
    List<Requirement> findWithPagination(int offset, int limit);

    /**
     * 删除需求
     *
     * @param id 需求ID
     * @return 是否删除成功
     */
    boolean delete(RequirementId id);

    /**
     * 记录需求匹配反馈
     *
     * @param requirementId 需求ID
     * @param serviceId 服务ID
     * @param satisfied 是否满意
     * @param feedback 反馈内容
     */
    void recordMatchingFeedback(RequirementId requirementId, ServiceId serviceId, boolean satisfied, String feedback);

    /**
     * 统计需求总数
     *
     * @return 需求总数
     */
    long count();
}
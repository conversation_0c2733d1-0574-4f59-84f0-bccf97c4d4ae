package com.archscope.domain.repository;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.TaskStatus;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 任务仓库接口
 */
public interface TaskRepository {
    
    /**
     * 保存任务
     *
     * @param task 任务
     * @return 保存后的任务
     */
    Task save(Task task);
    
    /**
     * 根据ID查找任务
     *
     * @param id 任务ID
     * @return 任务
     */
    Optional<Task> findById(Long id);
    
    /**
     * 查找下一个待执行的任务
     *
     * @return 任务
     */
    Optional<Task> findNextPendingTask();
    
    /**
     * 查找指定类型的下一个待执行任务
     *
     * @param taskType 任务类型
     * @return 任务
     */
    Optional<Task> findNextPendingTaskByType(String taskType);
    
    /**
     * 查找所有指定状态的任务
     *
     * @param status 任务状态
     * @return 任务列表
     */
    List<Task> findAllByStatus(TaskStatus status);
    
    /**
     * 查找所有指定状态和类型的任务
     *
     * @param status 任务状态
     * @param taskType 任务类型
     * @return 任务列表
     */
    List<Task> findAllByStatusAndTaskType(TaskStatus status, String taskType);
    
    /**
     * 查找所有指定状态和项目ID的任务
     *
     * @param status 任务状态
     * @param projectId 项目ID
     * @return 任务列表
     */
    List<Task> findAllByStatusAndProjectId(TaskStatus status, Long projectId);
    
    /**
     * 删除指定日期之前的已完成任务
     *
     * @param date 日期
     * @return 删除的任务数量
     */
    int deleteCompletedTasksOlderThan(LocalDateTime date);
    
    /**
     * 查找所有任务
     *
     * @return 任务列表
     */
    List<Task> findAll();

    Task update(Task task);

    /**
     * 删除任务
     *
     * @param id 任务ID
     */
    void deleteById(Long id);

    List<Task> findByProjectId(Long projectId);

    /**
     * 查找超时的PROCESSING任务
     *
     * @return 超时任务列表
     */
    List<Task> findTimeoutProcessingTasks();

    /**
     * 更新任务状态为PROCESSING并设置超时时间
     *
     * @param taskId 任务ID
     * @param workerId 工作节点ID
     * @param timeoutAt 超时时间
     * @return 更新的行数
     */
    int updateTaskToProcessing(Long taskId, String workerId, LocalDateTime timeoutAt);

    /**
     * 重置超时任务状态为PENDING
     *
     * @param taskId 任务ID
     * @return 更新的行数
     */
    int resetTimeoutTask(Long taskId);

    /**
     * 完成任务并设置结果
     *
     * @param taskId 任务ID
     * @param overallStatus 整体状态
     * @param results 结果JSON
     * @param executionTimeMs 执行时间
     * @return 更新的行数
     */
    int completeTaskWithResult(Long taskId, String overallStatus, String results, Long executionTimeMs);

    /**
     * 标记任务失败
     *
     * @param taskId 任务ID
     * @param errorDetail 错误详情
     * @param executionTimeMs 执行时间
     * @return 更新的行数
     */
    int failTaskWithError(Long taskId, String errorDetail, Long executionTimeMs);

    /**
     * 分页查询任务
     *
     * @param status 任务状态
     * @param taskType 任务类型
     * @param projectId 项目ID
     * @param workerId 工作节点ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param orderBy 排序字段
     * @param offset 偏移量
     * @param limit 限制数量
     * @return 任务列表
     */
    List<Task> findTasksWithPagination(TaskStatus status, String taskType, Long projectId,
                                      String workerId, LocalDateTime startDate, LocalDateTime endDate,
                                      String orderBy, int offset, int limit);

    /**
     * 统计符合条件的任务数量
     *
     * @param status 任务状态
     * @param taskType 任务类型
     * @param projectId 项目ID
     * @param workerId 工作节点ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 任务数量
     */
    long countTasksWithCondition(TaskStatus status, String taskType, Long projectId,
                                String workerId, LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 批量更新任务状态
     *
     * @param taskIds 任务ID列表
     * @param currentStatus 当前状态
     * @param newStatus 新状态
     * @return 更新的行数
     */
    int batchUpdateTaskStatus(List<Long> taskIds, TaskStatus currentStatus, TaskStatus newStatus);

    /**
     * 批量重置超时任务
     *
     * @param taskIds 任务ID列表
     * @return 更新的行数
     */
    int batchResetTimeoutTasks(List<Long> taskIds);

    /**
     * 获取任务统计信息
     *
     * @param projectId 项目ID
     * @param taskType 任务类型
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    List<java.util.Map<String, Object>> getTaskStatistics(Long projectId, String taskType,
                                                          LocalDateTime startDate, LocalDateTime endDate);

    /**
     * 查找长时间运行的任务
     *
     * @param minutes 运行时间阈值（分钟）
     * @param limit 限制数量
     * @return 长时间运行的任务列表
     */
    List<Task> findLongRunningTasks(int minutes, int limit);

    /**
     * 使用CAS操作锁定任务
     *
     * @param taskId 任务ID
     * @param workerId 工作节点ID
     * @param timeoutMinutes 超时时间（分钟）
     * @param expectedVersion 期望的版本号
     * @return 更新的行数
     */
    int lockTaskWithCAS(Long taskId, String workerId, int timeoutMinutes, Integer expectedVersion);

    /**
     * 重试任务
     *
     * @param taskId 任务ID
     * @return 更新的行数
     */
    int retryTask(Long taskId);
}

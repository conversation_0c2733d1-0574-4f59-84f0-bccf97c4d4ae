package com.archscope.domain.task;

import com.archscope.domain.entity.Task;
import com.archscope.domain.service.TaskQueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 任务调度器
 * 负责定期从任务队列中获取任务并分发给任务执行器
 */
@Slf4j
@RequiredArgsConstructor
public class TaskScheduler {

    private final TaskQueueService taskQueueService;
    private final TaskExecutorRegistry taskExecutorRegistry;
    
    private ScheduledExecutorService scheduledExecutorService;
    private boolean running = false;
    
    /**
     * 启动任务调度器
     * 
     * @param initialDelay 初始延迟（秒）
     * @param period 调度周期（秒）
     */
    public void start(long initialDelay, long period) {
        if (running) {
            log.warn("任务调度器已经在运行中");
            return;
        }
        
        log.info("启动任务调度器，初始延迟: {}秒，调度周期: {}秒", initialDelay, period);
        scheduledExecutorService = Executors.newScheduledThreadPool(1);
        scheduledExecutorService.scheduleAtFixedRate(
                this::scheduleTasks, 
                initialDelay, 
                period, 
                TimeUnit.SECONDS
        );
        running = true;
    }
    
    /**
     * 停止任务调度器
     */
    public void stop() {
        if (!running) {
            log.warn("任务调度器未运行");
            return;
        }
        
        log.info("停止任务调度器");
        scheduledExecutorService.shutdown();
        try {
            if (!scheduledExecutorService.awaitTermination(60, TimeUnit.SECONDS)) {
                scheduledExecutorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduledExecutorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        running = false;
    }
    
    /**
     * 调度任务
     */
    private void scheduleTasks() {
        try {
            log.debug("开始调度任务");
            
            // 获取所有待执行的任务
            taskQueueService.getPendingTasks().forEach(this::dispatchTask);
            
        } catch (Exception e) {
            log.error("任务调度异常", e);
        }
    }
    
    /**
     * 分发任务给对应的执行器
     *
     * @param task 任务
     */
    private void dispatchTask(Task task) {
        String taskType = task.getTaskType();
        log.debug("分发任务: {}, 类型: {}", task.getId(), taskType);

        // 跳过LLM专用任务类型，这些任务由外部LLM工作节点处理
        if (isLlmTask(taskType)) {
            log.debug("跳过LLM专用任务: {}, 类型: {}", task.getId(), taskType);
            return;
        }

        // 获取对应类型的任务执行器
        TaskExecutor executor = taskExecutorRegistry.getExecutor(taskType);

        if (executor == null) {
            log.error("未找到任务类型 {} 的执行器", taskType);
            taskQueueService.recordTaskError(task.getId(), "未找到对应的任务执行器: " + taskType);
            return;
        }

        // 提交任务给执行器
        executor.execute(task);
    }

    /**
     * 判断是否为LLM专用任务类型
     *
     * @param taskType 任务类型
     * @return 是否为LLM专用任务
     */
    private boolean isLlmTask(String taskType) {
        return taskType != null && (
            taskType.startsWith("LLM_") ||
            taskType.equals("DOCUMENTATION_GENERATION") ||
            taskType.equals("ARCHITECTURE_REVIEW") ||
            taskType.equals("CODE_ANALYSIS")
        );
    }
}

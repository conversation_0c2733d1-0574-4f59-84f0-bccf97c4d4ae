package com.archscope.domain.task;

import lombok.extern.slf4j.Slf4j;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 任务执行器注册表
 * 负责管理所有任务执行器
 */
@Slf4j
public class TaskExecutorRegistry {
    
    private final Map<String, TaskExecutor> executors = new HashMap<>();
    
    /**
     * 注册任务执行器
     * 
     * @param executor 任务执行器
     */
    public void registerExecutor(TaskExecutor executor) {
        String taskType = executor.getTaskType();
        log.info("注册任务执行器: {}", taskType);
        
        if (executors.containsKey(taskType)) {
            log.warn("任务类型 {} 的执行器已存在，将被覆盖", taskType);
        }
        
        executors.put(taskType, executor);
    }
    
    /**
     * 注销任务执行器
     * 
     * @param taskType 任务类型
     * @return 被注销的执行器，如果不存在则返回null
     */
    public TaskExecutor unregisterExecutor(String taskType) {
        log.info("注销任务执行器: {}", taskType);
        return executors.remove(taskType);
    }
    
    /**
     * 获取任务执行器
     * 
     * @param taskType 任务类型
     * @return 任务执行器，如果不存在则返回null
     */
    public TaskExecutor getExecutor(String taskType) {
        return executors.get(taskType);
    }
    
    /**
     * 获取所有已注册的任务类型
     * 
     * @return 任务类型集合
     */
    public Set<String> getRegisteredTaskTypes() {
        return executors.keySet();
    }
    
    /**
     * 清空所有注册的执行器
     */
    public void clear() {
        log.info("清空所有任务执行器");
        executors.clear();
    }
}

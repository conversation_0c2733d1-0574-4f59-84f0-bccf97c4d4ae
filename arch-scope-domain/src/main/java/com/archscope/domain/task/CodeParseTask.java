package com.archscope.domain.task;

import com.archscope.domain.entity.Task;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Map;

/**
 * 代码解析任务
 * 封装代码解析任务的参数和结果
 */
@Slf4j
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class CodeParseTask {

    // 任务类型常量
    public static final String TASK_TYPE = "CODE_PARSE";

    // 解析类型
    public enum ParseType {
        FULL,       // 全量解析
        INCREMENTAL // 增量解析
    }

    // 任务参数
    private Long repositoryId;    // 代码仓库ID
    private String fromCommit;    // 起始提交ID（增量解析时使用）
    private String toCommit;      // 目标提交ID
    private ParseType parseType;  // 解析类型
    private String[] includePaths; // 包含的路径
    private String[] excludePaths; // 排除的路径

    /**
     * 从Task实体中提取代码解析任务参数
     *
     * @param task 任务实体
     * @return 代码解析任务
     */
    public static CodeParseTask fromTask(Task task) {
        if (!TASK_TYPE.equals(task.getTaskType())) {
            throw new IllegalArgumentException("任务类型不匹配: " + task.getTaskType());
        }

        Map<String, Object> parameters = task.getParameters();
        if (parameters == null || parameters.isEmpty()) {
            throw new IllegalArgumentException("任务参数为空");
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.convertValue(parameters, CodeParseTask.class);
        } catch (Exception e) {
            log.error("解析任务参数失败", e);
            throw new IllegalArgumentException("解析任务参数失败: " + e.getMessage());
        }
    }

    /**
     * 转换为JSON字符串
     *
     * @return JSON字符串
     */
    public String toJson() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(this);
        } catch (IOException e) {
            log.error("序列化任务参数失败", e);
            throw new RuntimeException("序列化任务参数失败: " + e.getMessage());
        }
    }

    /**
     * 创建代码解析任务实体
     *
     * @param projectId 项目ID
     * @return 任务实体
     */
    public Task toTaskEntity(Long projectId) {
        Task task = new Task();
        task.setProjectId(projectId);
        task.setTaskType(TASK_TYPE);
        task.setName("代码解析任务 - 仓库ID: " + repositoryId);
        task.setDescription(parseType == ParseType.FULL ? "全量解析代码仓库" : "增量解析代码仓库");

        ObjectMapper objectMapper = new ObjectMapper();
        task.setParameters(objectMapper.convertValue(this, Map.class));

        return task;
    }
}

package com.archscope.domain.task;

import com.archscope.domain.entity.Task;

/**
 * 任务执行器接口
 * 负责执行特定类型的任务
 */
public interface TaskExecutor {
    
    /**
     * 获取执行器支持的任务类型
     * 
     * @return 任务类型
     */
    String getTaskType();
    
    /**
     * 执行任务
     * 
     * @param task 任务
     */
    void execute(Task task);
    
    /**
     * 取消任务执行
     * 
     * @param taskId 任务ID
     * @return 是否成功取消
     */
    boolean cancel(Long taskId);
}

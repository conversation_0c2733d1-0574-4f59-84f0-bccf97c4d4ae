package com.archscope.domain.task;

import com.archscope.domain.entity.Task;
import com.archscope.domain.service.TaskQueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 项目全量分析任务执行器
 * 负责执行项目全量分析任务，包含代码解析和文档生成的完整流程
 */
@Slf4j
@RequiredArgsConstructor
public class ProjectAnalysisTaskExecutor implements TaskExecutor {

    private final TaskQueueService taskQueueService;
    
    // 用于异步执行任务的线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(2);
    
    // 正在执行的任务ID映射
    private final java.util.Map<Long, CompletableFuture<Void>> runningTasks = new ConcurrentHashMap<>();
    
    @Override
    public String getTaskType() {
        return ProjectAnalysisTask.TASK_TYPE;
    }
    
    @Override
    public void execute(Task task) {
        log.info("开始执行项目全量分析任务: {}", task.getId());
        
        try {
            // 解析任务参数
            ProjectAnalysisTask analysisTask = ProjectAnalysisTask.fromTask(task);
            
            // 验证任务参数
            if (!analysisTask.validate()) {
                log.error("项目全量分析任务参数验证失败: {}", task.getId());
                taskQueueService.recordTaskError(task.getId(), "任务参数验证失败");
                return;
            }
            
            // 更新任务状态为进行中
            taskQueueService.updateTaskStatus(task.getId(), "IN_PROGRESS");
            
            // 异步执行任务
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    executeAnalysisTask(task.getId(), analysisTask);
                } catch (Exception e) {
                    log.error("执行项目全量分析任务异常", e);
                    taskQueueService.recordTaskError(task.getId(), "执行异常: " + e.getMessage());
                } finally {
                    // 从运行中任务列表移除
                    runningTasks.remove(task.getId());
                }
            }, executorService);
            
            // 添加到运行中任务列表
            runningTasks.put(task.getId(), future);
            
        } catch (Exception e) {
            log.error("准备执行项目全量分析任务失败", e);
            taskQueueService.recordTaskError(task.getId(), "准备执行失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean cancel(Long taskId) {
        log.info("取消项目全量分析任务: {}", taskId);
        
        CompletableFuture<Void> future = runningTasks.get(taskId);
        if (future != null) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                runningTasks.remove(taskId);
                taskQueueService.updateTaskStatus(taskId, "CANCELLED");
                log.info("项目全量分析任务已取消: {}", taskId);
            }
            return cancelled;
        }
        
        log.warn("未找到正在执行的项目全量分析任务: {}", taskId);
        return false;
    }
    
    /**
     * 执行项目全量分析任务的核心逻辑
     * 
     * @param taskId 任务ID
     * @param analysisTask 分析任务参数
     */
    private void executeAnalysisTask(Long taskId, ProjectAnalysisTask analysisTask) {
        log.info("开始执行项目全量分析: taskId={}, projectId={}, analysisType={}", 
                taskId, analysisTask.getProjectId(), analysisTask.getAnalysisType());
        
        try {
            // 更新任务进度
            taskQueueService.updateTaskProgress(taskId, 10);
            
            // 由于这是一个全量分析任务，实际的处理逻辑应该由LLM服务来完成
            // 这里只是标记任务为等待LLM处理状态
            log.info("项目全量分析任务已准备就绪，等待LLM服务处理: taskId={}", taskId);
            
            // 更新任务状态为等待LLM处理
            taskQueueService.updateTaskStatus(taskId, "PENDING_LLM");
            taskQueueService.updateTaskProgress(taskId, 20);
            
            // 构建任务结果摘要
            String resultSummary = String.format(
                "项目全量分析任务已准备就绪 - 项目: %s, 仓库: %s, 分支: %s, 分析类型: %s", 
                analysisTask.getProjectName(),
                analysisTask.getRepositoryUrl(),
                analysisTask.getBranch(),
                analysisTask.getAnalysisType()
            );
            
            log.info("项目全量分析任务准备完成: {}, {}", taskId, resultSummary);
            
        } catch (Exception e) {
            log.error("项目全量分析任务执行失败: {}", taskId, e);
            taskQueueService.recordTaskError(taskId, "执行失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取正在执行的任务数量
     * 
     * @return 正在执行的任务数量
     */
    public int getRunningTaskCount() {
        return runningTasks.size();
    }
    
    /**
     * 检查任务是否正在执行
     * 
     * @param taskId 任务ID
     * @return 是否正在执行
     */
    public boolean isTaskRunning(Long taskId) {
        return runningTasks.containsKey(taskId);
    }
    
    /**
     * 关闭执行器，清理资源
     */
    public void shutdown() {
        log.info("关闭项目全量分析任务执行器");
        
        // 取消所有正在执行的任务
        runningTasks.forEach((taskId, future) -> {
            if (!future.isDone()) {
                log.info("取消正在执行的任务: {}", taskId);
                future.cancel(true);
                taskQueueService.updateTaskStatus(taskId, "CANCELLED");
            }
        });
        
        runningTasks.clear();
        
        // 关闭线程池
        executorService.shutdown();
        
        log.info("项目全量分析任务执行器已关闭");
    }
}

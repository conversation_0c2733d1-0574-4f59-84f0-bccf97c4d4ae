package com.archscope.domain.task;

import com.archscope.domain.entity.Task;
import com.archscope.domain.service.DocumentGenerationService;
import com.archscope.domain.service.TaskQueueService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 文档生成任务执行器
 * 负责执行文档生成任务
 */
@Slf4j
@RequiredArgsConstructor
public class DocGenerateTaskExecutor implements TaskExecutor {

    private final DocumentGenerationService documentGenerationService;
    private final TaskQueueService taskQueueService;
    
    // 用于异步执行任务的线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(2);
    
    // 正在执行的任务ID映射
    private final java.util.Map<Long, CompletableFuture<Void>> runningTasks = new java.util.concurrent.ConcurrentHashMap<>();
    
    @Override
    public String getTaskType() {
        return DocGenerateTask.TASK_TYPE;
    }
    
    @Override
    public void execute(Task task) {
        log.info("开始执行文档生成任务: {}", task.getId());
        
        try {
            // 解析任务参数
            DocGenerateTask docTask = DocGenerateTask.fromTask(task);
            
            // 更新任务状态为进行中
            taskQueueService.updateTaskStatus(task.getId(), "IN_PROGRESS");
            
            // 异步执行任务
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    executeDocGenerateTask(task.getId(), docTask);
                } catch (Exception e) {
                    log.error("执行文档生成任务异常", e);
                    taskQueueService.recordTaskError(task.getId(), "执行异常: " + e.getMessage());
                } finally {
                    // 从运行中任务列表移除
                    runningTasks.remove(task.getId());
                }
            }, executorService);
            
            // 添加到运行中任务列表
            runningTasks.put(task.getId(), future);
            
        } catch (Exception e) {
            log.error("准备执行文档生成任务失败", e);
            taskQueueService.recordTaskError(task.getId(), "准备执行失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean cancel(Long taskId) {
        log.info("取消文档生成任务: {}", taskId);
        
        CompletableFuture<Void> future = runningTasks.get(taskId);
        if (future != null && !future.isDone()) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                runningTasks.remove(taskId);
                taskQueueService.cancelTask(taskId);
                log.info("成功取消文档生成任务: {}", taskId);
                return true;
            } else {
                log.warn("无法取消文档生成任务: {}", taskId);
                return false;
            }
        } else {
            log.warn("任务不存在或已完成，无法取消: {}", taskId);
            return false;
        }
    }
    
    /**
     * 执行文档生成任务
     * 
     * @param taskId 任务ID
     * @param docTask 文档生成任务参数
     */
    private void executeDocGenerateTask(Long taskId, DocGenerateTask docTask) {
        log.info("执行文档生成任务: {}, 仓库ID: {}, 提交ID: {}", 
                taskId, docTask.getRepositoryId(), docTask.getCommitId());
        
        try {
            // 更新任务进度
            taskQueueService.updateTaskProgress(taskId, 10);
            
            // 生成文档
            String outputPath = documentGenerationService.generateDocumentation(
                    docTask.getRepositoryId(),
                    docTask.getCommitId(),
                    docTask.getDocTypes(),
                    docTask.getTemplateId(),
                    docTask.getOutputFormat(),
                    docTask.getOutputPath(),
                    docTask.isIncludeArchDiagrams()
            );
            
            // 更新任务进度
            taskQueueService.updateTaskProgress(taskId, 100);
            
            // 完成任务
            String resultSummary = String.format("文档生成完成，输出路径: %s", outputPath);
            taskQueueService.completeTask(taskId, resultSummary);
            
            log.info("文档生成任务完成: {}, {}", taskId, resultSummary);
            
        } catch (Exception e) {
            log.error("文档生成任务执行失败: {}", taskId, e);
            taskQueueService.recordTaskError(taskId, "执行失败: " + e.getMessage());
        }
    }
}

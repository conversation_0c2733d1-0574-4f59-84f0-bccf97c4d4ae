package com.archscope.domain.task;

import com.archscope.domain.entity.Task;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.valueobject.TaskStatus;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * 任务监控器
 * 负责监控任务执行状态，处理超时任务等
 */
@Slf4j
@RequiredArgsConstructor
public class TaskMonitor {
    
    private final TaskQueueService taskQueueService;
    
    private ScheduledExecutorService scheduledExecutorService;
    private boolean running = false;
    
    // 任务超时时间（分钟）
    private long taskTimeoutMinutes = 30;
    
    /**
     * 启动任务监控器
     * 
     * @param initialDelay 初始延迟（秒）
     * @param period 监控周期（秒）
     */
    public void start(long initialDelay, long period) {
        if (running) {
            log.warn("任务监控器已经在运行中");
            return;
        }
        
        log.info("启动任务监控器，初始延迟: {}秒，监控周期: {}秒", initialDelay, period);
        scheduledExecutorService = Executors.newScheduledThreadPool(1);
        scheduledExecutorService.scheduleAtFixedRate(
                this::monitorTasks, 
                initialDelay, 
                period, 
                TimeUnit.SECONDS
        );
        running = true;
    }
    
    /**
     * 停止任务监控器
     */
    public void stop() {
        if (!running) {
            log.warn("任务监控器未运行");
            return;
        }
        
        log.info("停止任务监控器");
        scheduledExecutorService.shutdown();
        try {
            if (!scheduledExecutorService.awaitTermination(60, TimeUnit.SECONDS)) {
                scheduledExecutorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduledExecutorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        running = false;
    }
    
    /**
     * 设置任务超时时间
     * 
     * @param timeoutMinutes 超时时间（分钟）
     */
    public void setTaskTimeout(long timeoutMinutes) {
        this.taskTimeoutMinutes = timeoutMinutes;
        log.info("设置任务超时时间为 {} 分钟", timeoutMinutes);
    }
    
    /**
     * 监控任务
     */
    private void monitorTasks() {
        try {
            log.debug("开始监控任务");
            
            // 检查进行中的任务是否超时
            checkTimeoutTasks();
            
            // 检查失败的任务是否需要重试
            checkFailedTasks();
            
            // 清理过期的已完成任务
            cleanupCompletedTasks();
            
        } catch (Exception e) {
            log.error("任务监控异常", e);
        }
    }
    
    /**
     * 检查超时任务
     */
    private void checkTimeoutTasks() {
        log.debug("检查超时任务");
        
        List<Task> inProgressTasks = taskQueueService.getPendingTasksByType(TaskStatus.PROCESSING.name());
        LocalDateTime now = LocalDateTime.now();
        
        for (Task task : inProgressTasks) {
            LocalDateTime updatedAt = task.getUpdatedAt();
            
            // 如果任务更新时间超过超时时间，则标记为失败
            if (updatedAt != null && Duration.between(updatedAt, now).toMinutes() > taskTimeoutMinutes) {
                log.warn("任务 {} 执行超时", task.getId());
                taskQueueService.recordTaskError(task.getId(), "任务执行超时，超过 " + taskTimeoutMinutes + " 分钟未更新");
            }
        }
    }
    
    /**
     * 检查失败任务
     */
    private void checkFailedTasks() {
        log.debug("检查失败任务");
        
        List<Task> failedTasks = taskQueueService.getPendingTasksByType(TaskStatus.FAILED.name());
        
        for (Task task : failedTasks) {
            // 如果重试次数小于最大重试次数，则重试
            Integer retryCount = task.getRetryCount();
            if (retryCount != null && retryCount < 3) {
                log.info("重试失败任务 {}, 当前重试次数: {}", task.getId(), retryCount);
                
                // 更新重试次数
                task.setRetryCount(retryCount + 1);
                task.setStatus(TaskStatus.PENDING);
                taskQueueService.enqueueTask(task);
            }
        }
    }
    
    /**
     * 清理已完成任务
     */
    private void cleanupCompletedTasks() {
        // 清理30天前的已完成任务
        int cleanedCount = taskQueueService.cleanupExpiredTasks(30);
        if (cleanedCount > 0) {
            log.info("清理了 {} 个过期任务", cleanedCount);
        }
    }
}

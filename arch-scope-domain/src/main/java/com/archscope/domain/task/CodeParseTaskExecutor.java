package com.archscope.domain.task;

import com.archscope.domain.entity.Task;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.entity.CodeRepository;
import com.archscope.domain.service.CodeRepositoryService;
import com.archscope.domain.service.CodeStatisticsService;
import com.archscope.domain.service.IncrementalParseService;
import com.archscope.domain.service.TaskQueueService;
import com.archscope.domain.valueobject.CodeStatistics;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 代码解析任务执行器
 * 负责执行代码解析任务
 */
@Slf4j
@RequiredArgsConstructor
public class CodeParseTaskExecutor implements TaskExecutor {

    private final IncrementalParseService incrementalParseService;
    private final TaskQueueService taskQueueService;
    private final CodeStatisticsService codeStatisticsService;
    private final CodeRepositoryService codeRepositoryService;
    
    // 用于异步执行任务的线程池
    private final ExecutorService executorService = Executors.newFixedThreadPool(2);
    
    // 正在执行的任务ID映射
    private final java.util.Map<Long, CompletableFuture<Void>> runningTasks = new java.util.concurrent.ConcurrentHashMap<>();
    
    @Override
    public String getTaskType() {
        return CodeParseTask.TASK_TYPE;
    }
    
    @Override
    public void execute(Task task) {
        log.info("开始执行代码解析任务: {}", task.getId());
        
        try {
            // 解析任务参数
            CodeParseTask parseTask = CodeParseTask.fromTask(task);
            
            // 更新任务状态为进行中
            taskQueueService.updateTaskStatus(task.getId(), "IN_PROGRESS");
            
            // 异步执行任务
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    executeParseTask(task.getId(), parseTask);
                } catch (Exception e) {
                    log.error("执行代码解析任务异常", e);
                    taskQueueService.recordTaskError(task.getId(), "执行异常: " + e.getMessage());
                } finally {
                    // 从运行中任务列表移除
                    runningTasks.remove(task.getId());
                }
            }, executorService);
            
            // 添加到运行中任务列表
            runningTasks.put(task.getId(), future);
            
        } catch (Exception e) {
            log.error("准备执行代码解析任务失败", e);
            taskQueueService.recordTaskError(task.getId(), "准备执行失败: " + e.getMessage());
        }
    }
    
    @Override
    public boolean cancel(Long taskId) {
        log.info("取消代码解析任务: {}", taskId);
        
        CompletableFuture<Void> future = runningTasks.get(taskId);
        if (future != null && !future.isDone()) {
            boolean cancelled = future.cancel(true);
            if (cancelled) {
                runningTasks.remove(taskId);
                taskQueueService.cancelTask(taskId);
                log.info("成功取消代码解析任务: {}", taskId);
                return true;
            } else {
                log.warn("无法取消代码解析任务: {}", taskId);
                return false;
            }
        } else {
            log.warn("任务不存在或已完成，无法取消: {}", taskId);
            return false;
        }
    }
    
    /**
     * 执行代码解析任务
     * 
     * @param taskId 任务ID
     * @param parseTask 解析任务参数
     */
    private void executeParseTask(Long taskId, CodeParseTask parseTask) {
        log.info("执行代码解析任务: {}, 仓库ID: {}, 解析类型: {}", 
                taskId, parseTask.getRepositoryId(), parseTask.getParseType());
        
        try {
            List<FileParseResult> results;
            
            // 根据解析类型执行不同的解析逻辑
            if (parseTask.getParseType() == CodeParseTask.ParseType.INCREMENTAL) {
                // 增量解析
                log.info("执行增量解析，从提交 {} 到 {}", parseTask.getFromCommit(), parseTask.getToCommit());
                results = incrementalParseService.parseIncrementally(
                        parseTask.getRepositoryId(), 
                        parseTask.getFromCommit(), 
                        parseTask.getToCommit()
                );
            } else {
                // 全量解析，使用空字符串作为起始提交ID
                log.info("执行全量解析，目标提交: {}", parseTask.getToCommit());
                results = incrementalParseService.parseIncrementally(
                        parseTask.getRepositoryId(), 
                        "", 
                        parseTask.getToCommit()
                );
            }
            
            // 计算代码统计信息
            log.info("开始计算代码统计信息，解析结果数量: {}", results.size());
            CodeStatistics statistics = codeStatisticsService.calculateStatistics(results);

            // 获取贡献者数量
            int contributorCount = codeStatisticsService.getContributorCount(parseTask.getRepositoryId());
            statistics = CodeStatistics.builder()
                    .totalLinesOfCode(statistics.getTotalLinesOfCode())
                    .effectiveLinesOfCode(statistics.getEffectiveLinesOfCode())
                    .commentLines(statistics.getCommentLines())
                    .blankLines(statistics.getBlankLines())
                    .totalFileCount(statistics.getTotalFileCount())
                    .fileCountByType(statistics.getFileCountByType())
                    .linesOfCodeByLanguage(statistics.getLinesOfCodeByLanguage())
                    .contributorCount(contributorCount)
                    .build();

            // 更新项目统计信息
            // 注意：这里需要从parseTask中获取项目ID，但当前CodeParseTask可能没有项目ID字段
            // 我们需要通过仓库ID来查找项目ID
            updateProjectStatisticsFromRepository(parseTask.getRepositoryId(), statistics);

            // 更新任务进度
            taskQueueService.updateTaskProgress(taskId, 100);

            // 完成任务
            String resultSummary = String.format("解析完成，共解析 %d 个文件，代码行数: %d，贡献者: %d",
                    results.size(), statistics.getTotalLinesOfCode(), statistics.getContributorCount());
            taskQueueService.completeTask(taskId, resultSummary);

            log.info("代码解析任务完成: {}, {}", taskId, resultSummary);
            
        } catch (Exception e) {
            log.error("代码解析任务执行失败: {}", taskId, e);
            taskQueueService.recordTaskError(taskId, "执行失败: " + e.getMessage());
        }
    }

    /**
     * 通过仓库ID更新项目统计信息
     *
     * @param repositoryId 仓库ID
     * @param statistics 统计信息
     */
    private void updateProjectStatisticsFromRepository(Long repositoryId, CodeStatistics statistics) {
        try {
            // 通过仓库ID查找仓库信息
            CodeRepository repository = codeRepositoryService.findRepositoryById(repositoryId)
                    .orElse(null);

            if (repository != null && repository.getProjectId() != null) {
                // 更新项目统计信息
                codeStatisticsService.updateProjectStatistics(repository.getProjectId(), statistics);
                log.info("已更新项目 {} 的统计信息", repository.getProjectId());
            } else {
                log.warn("无法找到仓库 {} 对应的项目，跳过统计信息更新", repositoryId);
            }
        } catch (Exception e) {
            log.error("更新项目统计信息失败，仓库ID: {}", repositoryId, e);
        }
    }
}

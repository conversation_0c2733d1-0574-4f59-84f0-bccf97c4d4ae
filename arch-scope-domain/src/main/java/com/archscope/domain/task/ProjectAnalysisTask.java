package com.archscope.domain.task;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.DocumentType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 项目全量分析任务
 * 整合代码解析和文档生成的完整分析流程
 */
@Slf4j
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class ProjectAnalysisTask {

    // 任务类型常量
    public static final String TASK_TYPE = "PROJECT_ANALYSIS";

    // 分析类型
    public enum AnalysisType {
        FULL,       // 全量分析（代码解析 + 文档生成）
        CODE_ONLY,  // 仅代码解析
        DOC_ONLY    // 仅文档生成
    }

    // 注意：文档类型使用 com.archscope.domain.valueobject.DocumentType 统一枚举

    /**
     * 项目ID
     */
    private Long projectId;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 项目类型
     */
    private String projectType;

    /**
     * 项目描述
     */
    private String description;

    /**
     * 仓库URL
     */
    private String repositoryUrl;

    /**
     * 分支名称
     */
    private String branch;

    /**
     * 提交ID（可选，用于指定特定版本）
     */
    private String commitId;

    /**
     * 分析类型，默认为全量分析
     */
    private AnalysisType analysisType = AnalysisType.FULL;

    /**
     * 需要生成的文档类型列表
     */
    private List<DocumentType> documentTypes;

    /**
     * 是否包含架构图
     */
    private boolean includeArchDiagrams = true;

    /**
     * 输出格式（markdown, html等）
     */
    private String outputFormat = "markdown";

    /**
     * 额外的分析参数
     */
    private Map<String, Object> analysisParameters;

    /**
     * 从Task实体创建ProjectAnalysisTask
     *
     * @param task 任务实体
     * @return ProjectAnalysisTask实例
     */
    public static ProjectAnalysisTask fromTask(Task task) {
        if (task == null) {
            throw new IllegalArgumentException("Task不能为空");
        }

        if (!TASK_TYPE.equals(task.getTaskType())) {
            throw new IllegalArgumentException("任务类型不匹配，期望: " + TASK_TYPE + ", 实际: " + task.getTaskType());
        }

        try {
            ObjectMapper mapper = new ObjectMapper();
            
            // 解析任务参数
            Map<String, Object> parameters = task.getParameters();
            if (parameters == null || parameters.isEmpty()) {
                throw new IllegalArgumentException("任务参数为空");
            }

            // 从parameters中提取JSON字符串
            String parametersJson = null;
            if (parameters.containsKey("parameters")) {
                parametersJson = (String) parameters.get("parameters");
            } else {
                // 如果parameters直接包含数据，则序列化整个map
                parametersJson = mapper.writeValueAsString(parameters);
            }

            if (parametersJson == null || parametersJson.trim().isEmpty()) {
                throw new IllegalArgumentException("任务参数JSON为空");
            }

            // 解析JSON为ProjectAnalysisTask
            ProjectAnalysisTask analysisTask = mapper.readValue(parametersJson, ProjectAnalysisTask.class);
            
            // 设置默认值
            if (analysisTask.getAnalysisType() == null) {
                analysisTask.setAnalysisType(AnalysisType.FULL);
            }
            
            if (analysisTask.getOutputFormat() == null || analysisTask.getOutputFormat().trim().isEmpty()) {
                analysisTask.setOutputFormat("markdown");
            }

            log.info("成功解析项目分析任务: projectId={}, analysisType={}", 
                    analysisTask.getProjectId(), analysisTask.getAnalysisType());
            
            return analysisTask;

        } catch (IOException e) {
            log.error("解析项目分析任务参数失败: taskId={}", task.getId(), e);
            throw new RuntimeException("解析任务参数失败: " + e.getMessage(), e);
        } catch (IllegalArgumentException e) {
            log.error("创建项目分析任务失败: taskId={}", task.getId(), e);
            throw e; // 重新抛出IllegalArgumentException
        } catch (Exception e) {
            log.error("创建项目分析任务失败: taskId={}", task.getId(), e);
            throw new RuntimeException("创建任务失败: " + e.getMessage(), e);
        }
    }

    /**
     * 转换为JSON字符串
     *
     * @return JSON字符串
     */
    public String toJson() {
        try {
            ObjectMapper mapper = new ObjectMapper();
            return mapper.writeValueAsString(this);
        } catch (Exception e) {
            log.error("序列化项目分析任务失败", e);
            throw new RuntimeException("序列化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 验证任务参数的完整性
     *
     * @return 验证是否通过
     */
    public boolean validate() {
        if (projectId == null || projectId <= 0) {
            log.error("项目ID无效: {}", projectId);
            return false;
        }

        if (repositoryUrl == null || repositoryUrl.trim().isEmpty()) {
            log.error("仓库URL为空");
            return false;
        }

        if (branch == null || branch.trim().isEmpty()) {
            log.error("分支名称为空");
            return false;
        }

        if (projectName == null || projectName.trim().isEmpty()) {
            log.error("项目名称为空");
            return false;
        }

        return true;
    }

    /**
     * 获取任务描述
     *
     * @return 任务描述字符串
     */
    public String getTaskDescription() {
        return String.format("项目全量分析任务 - 项目: %s, 类型: %s, 仓库: %s, 分支: %s", 
                projectName, analysisType, repositoryUrl, branch);
    }

    /**
     * 是否需要代码解析
     *
     * @return true如果需要代码解析
     */
    public boolean needsCodeParsing() {
        return analysisType == AnalysisType.FULL || analysisType == AnalysisType.CODE_ONLY;
    }

    /**
     * 是否需要文档生成
     *
     * @return true如果需要文档生成
     */
    public boolean needsDocumentGeneration() {
        return analysisType == AnalysisType.FULL || analysisType == AnalysisType.DOC_ONLY;
    }
}

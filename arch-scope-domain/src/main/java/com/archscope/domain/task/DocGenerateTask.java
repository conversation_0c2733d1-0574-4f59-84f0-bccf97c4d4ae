package com.archscope.domain.task;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.DocumentType;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 文档生成任务
 * 封装文档生成任务的参数和结果
 */
@Slf4j
@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class DocGenerateTask {

    // 任务类型常量
    public static final String TASK_TYPE = "DOC_GENERATE";

    // 任务参数
    private Long repositoryId;           // 代码仓库ID
    private String commitId;             // 提交ID
    private List<DocumentType> docTypes; // 文档类型列表
    private String templateId;           // 模板ID
    private String outputFormat;         // 输出格式（如 markdown, html）
    private String outputPath;           // 输出路径
    private boolean includeArchDiagrams; // 是否包含架构图

    /**
     * 从Task实体中提取文档生成任务参数
     *
     * @param task 任务实体
     * @return 文档生成任务
     */
    public static DocGenerateTask fromTask(Task task) {
        if (!TASK_TYPE.equals(task.getTaskType())) {
            throw new IllegalArgumentException("任务类型不匹配: " + task.getTaskType());
        }

        Map<String, Object> parameters = task.getParameters();
        if (parameters == null || parameters.isEmpty()) {
            throw new IllegalArgumentException("任务参数为空");
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.convertValue(parameters, DocGenerateTask.class);
        } catch (Exception e) {
            log.error("解析任务参数失败", e);
            throw new IllegalArgumentException("解析任务参数失败: " + e.getMessage());
        }
    }

    /**
     * 转换为JSON字符串
     *
     * @return JSON字符串
     */
    public String toJson() {
        try {
            ObjectMapper objectMapper = new ObjectMapper();
            return objectMapper.writeValueAsString(this);
        } catch (IOException e) {
            log.error("序列化任务参数失败", e);
            throw new RuntimeException("序列化任务参数失败: " + e.getMessage());
        }
    }

    /**
     * 创建文档生成任务实体
     *
     * @param projectId 项目ID
     * @return 任务实体
     */
    public Task toTaskEntity(Long projectId) {
        Task task = new Task();
        task.setProjectId(projectId);
        task.setTaskType(TASK_TYPE);
        task.setName("文档生成任务 - 仓库ID: " + repositoryId);
        task.setDescription("生成项目文档，提交ID: " + commitId);

        ObjectMapper objectMapper = new ObjectMapper();
        task.setParameters(objectMapper.convertValue(this, Map.class));

        return task;
    }
}

package com.archscope.domain.external.llm;

import com.archscope.domain.model.parser.FileParseResult;

/**
 * LLM服务接口 (领域层)
 */
public interface LlmService {

    /**
     * 调用LLM服务解析代码
     *
     * @param filename 文件名
     * @param content 文件内容
     * @param languageType 语言类型
     * @return LLM解析结果
     */
    FileParseResult parseCodeWithLlm(String filename, String content, String languageType);

    /**
     * 调用LLM服务分析依赖关系
     *
     * @param parseResults 文件解析结果列表
     * @return LLM分析的依赖关系列表
     */
    // List<DependencyRelation> analyzeDependenciesWithLlm(List<FileParseResult> parseResults); // 暂不实现，先聚焦基础解析
} 
package com.archscope.domain.config;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Git服务器配置
 * 用于配置不同Git服务器的协议支持情况和URL转换规则
 */
@Slf4j
@Data
@Component
@ConfigurationProperties(prefix = "archscope.git.servers")
public class GitServerConfig {

    /**
     * Git服务器配置映射
     * key: 服务器主机名（如 github.com, gitlab.yeepay.com）
     * value: 服务器配置信息
     */
    private Map<String, ServerConfig> hosts = new HashMap<>();

    /**
     * 默认配置
     */
    private ServerConfig defaultConfig = new ServerConfig();

    /**
     * 服务器配置列表（用于处理包含点号的主机名）
     * 使用列表格式避免Spring Boot配置绑定的点号问题
     */
    @JsonProperty("server-list")
    private List<ServerConfigItem> serverList = new ArrayList<>();

    /**
     * 获取服务器配置列表
     */
    public List<ServerConfigItem> getServerList() {
        return serverList;
    }

    /**
     * 设置服务器配置列表
     */
    public void setServerList(List<ServerConfigItem> serverList) {
        this.serverList = serverList;
    }

    /**
     * 初始化方法，处理配置数据
     */
    @PostConstruct
    public void init() {
        log.debug("GitServerConfig初始化开始");
        log.debug("serverList: {}", serverList);
        log.debug("hosts: {}", hosts);

        // 将serverList转换为hosts映射
        if (serverList != null && !serverList.isEmpty()) {
            log.info("开始转换serverList到hosts映射，serverList大小: {}", serverList.size());
            convertServerListToHosts();
        } else {
            log.warn("serverList为空或null，无法转换为hosts映射。serverList: {}", serverList);
        }

        log.info("GitServerConfig初始化完成，hosts数量: {}", hosts.size());
        log.debug("最终hosts配置: {}", hosts);
    }

    /**
     * 将服务器配置列表转换为hosts映射
     */
    private void convertServerListToHosts() {
        for (ServerConfigItem item : serverList) {
            if (item.getHost() != null && !item.getHost().trim().isEmpty()) {
                ServerConfig serverConfig = new ServerConfig();

                // 复制配置属性
                serverConfig.setSupportsHttps(item.isSupportsHttps());
                serverConfig.setSupportsHttp(item.isSupportsHttp());
                serverConfig.setSuggestHttpsOnSshFailure(item.isSuggestHttpsOnSshFailure());
                serverConfig.setHttpsUrlTemplate(item.getHttpsUrlTemplate());
                serverConfig.setHttpUrlTemplate(item.getHttpUrlTemplate());
                serverConfig.setPersonalAccessToken(item.getPersonalAccessToken());
                serverConfig.setTokenUsername(item.getTokenUsername());

                hosts.put(item.getHost(), serverConfig);
                log.debug("转换主机配置: {} -> {}", item.getHost(), serverConfig);
            }
        }
    }

    /**
     * 服务器配置项（用于列表配置）
     */
    @Data
    public static class ServerConfigItem {
        /**
         * 主机名
         */
        private String host;

        /**
         * 是否支持HTTPS协议
         */
        private boolean supportsHttps = true;

        /**
         * 是否支持HTTP协议
         */
        private boolean supportsHttp = false;

        /**
         * 是否在SSH认证失败时提供HTTPS URL建议
         */
        private boolean suggestHttpsOnSshFailure = true;

        /**
         * 自定义HTTPS URL模板
         * 占位符: {host}, {owner}, {repo}
         */
        private String httpsUrlTemplate = "https://{host}/{owner}/{repo}.git";

        /**
         * 自定义HTTP URL模板
         * 占位符: {host}, {owner}, {repo}
         */
        private String httpUrlTemplate = "http://{host}/{owner}/{repo}.git";

        /**
         * 个人访问令牌 (Personal Access Token)
         * 用于访问私有仓库的认证令牌
         * 例如：GitLab的glpat-xxx格式令牌，GitHub的ghp_xxx格式令牌
         */
        private String personalAccessToken;

        /**
         * 访问令牌对应的用户名
         * 某些Git服务器需要指定用户名，如GitLab通常使用"oauth2"或实际用户名
         * GitHub通常使用token作为用户名，密码为token值
         */
        private String tokenUsername = "oauth2";

        /**
         * 检查是否配置了个人访问令牌
         *
         * @return 如果配置了有效的个人访问令牌则返回true
         */
        public boolean hasPersonalAccessToken() {
            return personalAccessToken != null && !personalAccessToken.trim().isEmpty();
        }
    }

    /**
     * 获取指定主机的配置，如果不存在则返回默认配置
     */
    public ServerConfig getServerConfig(String host) {
        return hosts.getOrDefault(host, defaultConfig);
    }

    /**
     * 服务器配置信息
     */
    @Data
    public static class ServerConfig {
        /**
         * 是否支持HTTPS协议
         */
        private boolean supportsHttps = true;

        /**
         * 是否支持HTTP协议
         */
        private boolean supportsHttp = false;

        /**
         * 是否在SSH认证失败时提供HTTPS URL建议
         */
        private boolean suggestHttpsOnSshFailure = true;

        /**
         * 自定义HTTPS URL模板
         * 占位符: {host}, {owner}, {repo}
         */
        private String httpsUrlTemplate = "https://{host}/{owner}/{repo}.git";

        /**
         * 自定义HTTP URL模板
         * 占位符: {host}, {owner}, {repo}
         */
        private String httpUrlTemplate = "http://{host}/{owner}/{repo}.git";

        /**
         * 个人访问令牌 (Personal Access Token)
         * 用于访问私有仓库的认证令牌
         * 例如：GitLab的glpat-xxx格式令牌，GitHub的ghp_xxx格式令牌
         */
        private String personalAccessToken;

        /**
         * 访问令牌对应的用户名
         * 某些Git服务器需要指定用户名，如GitLab通常使用"oauth2"或实际用户名
         * GitHub通常使用token作为用户名，密码为token值
         */
        private String tokenUsername = "oauth2";

        /**
         * 检查是否配置了个人访问令牌
         *
         * @return 如果配置了有效的个人访问令牌则返回true
         */
        public boolean hasPersonalAccessToken() {
            return personalAccessToken != null && !personalAccessToken.trim().isEmpty();
        }
    }
}

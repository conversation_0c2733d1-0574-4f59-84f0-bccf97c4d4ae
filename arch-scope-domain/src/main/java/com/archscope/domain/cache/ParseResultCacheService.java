package com.archscope.domain.cache;

import com.archscope.domain.model.parser.FileParseResult;

import java.util.List;

/**
 * 解析结果缓存服务接口 (领域层)
 */
public interface ParseResultCacheService {

    /**
     * 从缓存获取指定仓库和提交的解析结果
     *
     * @param repositoryId 仓库ID
     * @param commitId     提交ID
     * @return 解析结果列表，如果缓存不存在则返回空列表
     */
    List<FileParseResult> getParseResults(Long repositoryId, String commitId);

    /**
     * 将解析结果保存到缓存
     *
     * @param repositoryId 仓库ID
     * @param commitId     提交ID
     * @param parseResults 解析结果列表
     */
    void saveParseResults(Long repositoryId, String commitId, List<FileParseResult> parseResults);

    /**
     * 清除指定仓库的所有解析缓存
     *
     * @param repositoryId 仓库ID
     */
    void clearCache(Long repositoryId);
} 
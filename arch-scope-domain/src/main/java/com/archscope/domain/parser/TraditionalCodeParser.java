package com.archscope.domain.parser;

import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.model.parser.LanguageType;

import java.io.File;
import java.io.IOException;

/**
 * 传统代码解析器接口
 */
public interface TraditionalCodeParser {
    
    /**
     * 解析文件内容
     * @param filename 文件名
     * @param content 文件内容
     * @return 解析结果
     */
    FileParseResult parseFile(String filename, String content);
    
    /**
     * 解析文件
     * @param file 文件
     * @return 解析结果
     * @throws IOException IO异常
     */
    FileParseResult parseFile(File file) throws IOException;
    
    /**
     * 获取支持的语言类型
     * @return 语言类型
     */
    LanguageType getSupportedLanguage();
}

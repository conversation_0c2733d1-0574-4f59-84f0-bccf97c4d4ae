package com.archscope.domain.valueobject;

/**
 * 项目类型枚举 - 基于主要技术栈分类
 */
public enum ProjectType {
    JAVA,               // Java项目
    JAVASCRIPT,         // JavaScript/Node.js项目
    PYTHON,             // Python项目
    GO,                 // Go项目
    CSHARP,             // C#项目
    KOTLIN,             // Kotlin项目
    RUST,               // Rust项目
    PHP,                // PHP项目
    TYPESCRIPT,         // TypeScript项目
    OTHER;              // 其他类型

    /**
     * 从字符串转换为ProjectType枚举
     * 支持智能映射，如果字符串无效，返回OTHER类型
     *
     * @param typeString 类型字符串
     * @return ProjectType枚举值
     */
    public static ProjectType fromString(String typeString) {
        if (typeString == null || typeString.trim().isEmpty()) {
            return OTHER;
        }

        String normalizedType = typeString.trim().toUpperCase();

        // 首先尝试直接匹配枚举值
        try {
            return ProjectType.valueOf(normalizedType);
        } catch (IllegalArgumentException e) {
            // 如果直接匹配失败，尝试智能映射
            return mapToProjectType(normalizedType);
        }
    }

    /**
     * 智能映射字符串到项目类型
     * 支持历史数据和常见别名
     */
    private static ProjectType mapToProjectType(String typeString) {
        switch (typeString) {
            case "REACT":
            case "VUE":
            case "ANGULAR":
            case "NODE":
            case "NODEJS":
                return JAVASCRIPT;
            case "ANDROID":
                return KOTLIN;
            case "SPRING":
            case "SPRINGBOOT":
            case "MAVEN":
            case "GRADLE":
                return JAVA;
            case "DJANGO":
            case "FLASK":
                return PYTHON;
            case "DOTNET":
            case ".NET":
                return CSHARP;
            default:
                return OTHER;
        }
    }
}
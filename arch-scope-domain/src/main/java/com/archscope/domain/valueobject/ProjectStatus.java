package com.archscope.domain.valueobject;

/**
 * 项目状态枚举
 * 与数据库约束 chk_project_status 保持一致
 */
public enum ProjectStatus {
    /**
     * 待分析 - 项目刚创建，等待代码分析
     */
    PENDING_ANALYSIS("待分析"),
    
    /**
     * 分析中 - 正在进行代码分析
     */
    ANALYZING("分析中"),
    
    /**
     * 分析完成 - 代码分析已完成，可以生成文档
     */
    ANALYSIS_COMPLETED("分析完成"),
    
    /**
     * 分析失败 - 代码分析过程中出现错误
     */
    ANALYSIS_FAILED("分析失败"),
    
    /**
     * 可用 - 项目可正常访问和使用
     */
    AVAILABLE("可用"),
    
    /**
     * 不可用 - 项目暂时不可访问或使用
     */
    UNAVAILABLE("不可用");

    private final String displayName;

    ProjectStatus(String displayName) {
        this.displayName = displayName;
    }

    public String getDisplayName() {
        return displayName;
    }

    /**
     * 检查是否为终态状态
     * @return true if the status is final (project cannot be processed further)
     */
    public boolean isFinalStatus() {
        return this == ANALYSIS_COMPLETED || this == ANALYSIS_FAILED || this == UNAVAILABLE;
    }

    /**
     * 检查是否可以开始分析
     * @return true if the project can start analysis
     */
    public boolean canStartAnalysis() {
        return this == PENDING_ANALYSIS || this == ANALYSIS_FAILED;
    }

    /**
     * 检查是否可以生成文档
     * @return true if the project can generate documentation
     */
    public boolean canGenerateDocumentation() {
        return this == ANALYSIS_COMPLETED;
    }

    /**
     * 检查是否正在处理中
     * @return true if the project is being processed
     */
    public boolean isProcessing() {
        return this == ANALYZING;
    }

    /**
     * 获取下一个状态（用于状态流转）
     * @return 下一个可能的状态
     */
    public ProjectStatus getNextStatus() {
        switch (this) {
            case PENDING_ANALYSIS:
                return ANALYZING;
            case ANALYZING:
                return ANALYSIS_COMPLETED; // 成功情况下的下一状态
            case ANALYSIS_COMPLETED:
                return AVAILABLE;
            case ANALYSIS_FAILED:
                return PENDING_ANALYSIS; // 重新分析
            case AVAILABLE:
                return UNAVAILABLE; // 可能的状态变更
            case UNAVAILABLE:
                return AVAILABLE; // 恢复可用
            default:
                return this;
        }
    }

    /**
     * 获取失败状态（用于错误处理）
     * @return 对应的失败状态
     */
    public ProjectStatus getFailureStatus() {
        switch (this) {
            case PENDING_ANALYSIS:
            case ANALYZING:
                return ANALYSIS_FAILED;
            case ANALYSIS_COMPLETED:
            case AVAILABLE:
                return UNAVAILABLE;
            default:
                return this;
        }
    }

    /**
     * 从字符串转换为枚举
     * @param status 状态字符串
     * @return 对应的枚举值
     * @throws IllegalArgumentException 如果状态字符串无效
     */
    public static ProjectStatus fromString(String status) {
        if (status == null || status.trim().isEmpty()) {
            return PENDING_ANALYSIS; // 默认状态
        }
        
        try {
            return ProjectStatus.valueOf(status.toUpperCase());
        } catch (IllegalArgumentException e) {
            throw new IllegalArgumentException("无效的项目状态: " + status + 
                ". 有效状态: PENDING_ANALYSIS, ANALYZING, ANALYSIS_COMPLETED, ANALYSIS_FAILED, AVAILABLE, UNAVAILABLE");
        }
    }

    /**
     * 检查状态是否有效
     * @param status 状态字符串
     * @return true if valid
     */
    public static boolean isValidStatus(String status) {
        if (status == null || status.trim().isEmpty()) {
            return false;
        }
        
        try {
            ProjectStatus.valueOf(status.toUpperCase());
            return true;
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    /**
     * 获取所有有效状态的字符串数组
     * @return 状态字符串数组
     */
    public static String[] getAllStatusStrings() {
        ProjectStatus[] values = ProjectStatus.values();
        String[] strings = new String[values.length];
        for (int i = 0; i < values.length; i++) {
            strings[i] = values[i].name();
        }
        return strings;
    }
}

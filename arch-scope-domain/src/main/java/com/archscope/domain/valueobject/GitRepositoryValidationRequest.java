package com.archscope.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Git仓库验证请求值对象
 * 领域层的Git仓库验证请求表示
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitRepositoryValidationRequest {
    
    /**
     * Git仓库URL
     */
    private String repositoryUrl;
    
    /**
     * 是否获取详细信息（包括分支列表、语言信息等）
     * 默认为false，只获取基本信息
     */
    private Boolean fetchDetails = false;
    
    /**
     * 认证用户名（可选，用于私有仓库）
     */
    private String username;
    
    /**
     * 认证密码或Token（可选，用于私有仓库）
     */
    private String password;
}

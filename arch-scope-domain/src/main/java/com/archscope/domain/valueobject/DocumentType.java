package com.archscope.domain.valueobject;

public enum DocumentType {
    PRODUCT_INTRO("产品简介"),
    ARCHITECTURE("架构设计"),
    API("接口文档"),
    USER_MANUAL("用户手册"),
    EXTENSION("扩展能力"),
    LLMS_TXT("LLM生成的原始内容");
    
    private final String displayName;
    
    DocumentType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
} 
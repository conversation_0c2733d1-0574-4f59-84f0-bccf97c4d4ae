package com.archscope.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目配置值对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectConfiguration {
    private String buildTool;           // 构建工具（Maven, Gradle等）
    private String language;            // 主要编程语言
    private String framework;           // 主要框架
    private Boolean enableDocGeneration; // 是否开启文档自动生成
    private Boolean enableCodeAnalysis;  // 是否开启代码分析
    private String accessControl;        // 访问控制策略
} 
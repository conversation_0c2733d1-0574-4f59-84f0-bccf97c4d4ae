package com.archscope.domain.valueobject;

import java.util.Objects;

/**
 * 服务ID值对象，用于唯一标识一个服务
 * 使用String类型保持代码兼容性，在转换层处理与数据库Long类型的转换
 */
public class ServiceId {
    private final String value;

    private ServiceId(String value) {
        this.value = value;
    }

    public static ServiceId of(String value) {
        return new ServiceId(value);
    }

    public static ServiceId of(Long value) {
        if (value == null) {
            return new ServiceId(null);
        }
        return new ServiceId(value.toString());
    }

    /**
     * 创建新的服务ID（用于测试和兼容性）
     */
    public static ServiceId createNew() {
        return new ServiceId(java.util.UUID.randomUUID().toString());
    }

    public String getValue() {
        return value;
    }

    /**
     * 获取Long形式的值（用于数据库操作）
     */
    public Long getLongValue() {
        if (value == null) {
            return null;
        }
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            // 如果不是数字，返回null或抛出异常
            return null;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        ServiceId serviceId = (ServiceId) o;
        return Objects.equals(value, serviceId.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return value != null ? value.toString() : "null";
    }
}
package com.archscope.domain.valueobject;

/**
 * 任务状态枚举
 * 包含基础状态、扩展状态和文档生成相关状态
 */
public enum TaskStatus {
    // 基础状态（与文档定义保持一致）
    QUEUED("已排队"),
    PENDING("等待中"),
    RUNNING("运行中"),
    SUCCESS("成功"),
    FAILED_TRANSIENT("临时失败"),
    FAILED_PERMANENT("永久失败"),
    RETRYING_DELAYED("延迟重试"),
    CANCELLED("已取消"),
    TIMED_OUT("超时"),

    // 扩展状态（向后兼容和特定场景）
    PROCESSING("处理中"),  // 向后兼容，映射到RUNNING
    COMPLETED("已完成"),   // 向后兼容，映射到SUCCESS
    FAILED("失败"),        // 向后兼容，映射到FAILED_PERMANENT
    PARTIAL_SUCCESS("部分成功"),
    WAITING("等待依赖"),
    PAUSED("已暂停"),

    // 文档生成相关状态
    DOC_GEN_IN_PROGRESS("文档生成中"),
    DOC_GEN_FAILED("文档生成失败"),
    DOC_GEN_COMPLETED("文档生成完成");

    private final String displayName;

    TaskStatus(String displayName) {
        this.displayName = displayName;
    }

    /**
     * 检查是否为终态状态
     * @return true if the status is final (task cannot be processed further)
     */
    public boolean isFinalStatus() {
        return this == SUCCESS || this == COMPLETED ||
               this == FAILED_PERMANENT || this == FAILED ||
               this == PARTIAL_SUCCESS || this == CANCELLED ||
               this == DOC_GEN_COMPLETED || this == DOC_GEN_FAILED;
    }

    /**
     * 检查是否为可处理状态
     * @return true if the task can be picked up for processing
     */
    public boolean isProcessable() {
        return this == PENDING || this == QUEUED;
    }

    /**
     * 检查是否为活跃处理状态
     * @return true if the task is currently being processed
     */
    public boolean isActiveProcessing() {
        return this == PROCESSING || this == RUNNING ||
               this == DOC_GEN_IN_PROGRESS;
    }

    /**
     * 检查是否为失败状态
     * @return true if the task has failed
     */
    public boolean isFailedStatus() {
        return this == FAILED || this == FAILED_PERMANENT ||
               this == FAILED_TRANSIENT || this == DOC_GEN_FAILED;
    }

    /**
     * 检查是否为成功状态
     * @return true if the task has completed successfully
     */
    public boolean isSuccessStatus() {
        return this == SUCCESS || this == COMPLETED ||
               this == DOC_GEN_COMPLETED;
    }

    /**
     * 向后兼容方法 - 状态映射
     * @deprecated 使用标准状态替代
     */
    @Deprecated
    public static TaskStatus fromLegacyStatus(String status) {
        switch (status) {
            case "IN_PROGRESS":
                return PROCESSING;
            case "RUNNING":
                return RUNNING;
            case "SUCCESS":
                return SUCCESS;
            case "COMPLETED":
                return COMPLETED;
            case "FAILED":
                return FAILED;
            default:
                return TaskStatus.valueOf(status);
        }
    }

    /**
     * 获取标准化状态（用于API返回）
     * @return 标准化的状态
     */
    public TaskStatus getStandardStatus() {
        switch (this) {
            case PROCESSING:
                return RUNNING;
            case COMPLETED:
                return SUCCESS;
            case FAILED:
                return FAILED_PERMANENT;
            case DOC_GEN_IN_PROGRESS:
                return RUNNING;
            case DOC_GEN_COMPLETED:
                return SUCCESS;
            case DOC_GEN_FAILED:
                return FAILED_PERMANENT;
            default:
                return this;
        }
    }

    /**
     * 获取显示名称
     * @return 状态的中文显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
}
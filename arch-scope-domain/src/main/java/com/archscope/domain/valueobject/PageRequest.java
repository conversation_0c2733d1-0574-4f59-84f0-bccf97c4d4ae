package com.archscope.domain.valueobject;

/**
 * 分页请求值对象
 */
public class PageRequest {
    private final int page;
    private final int size;
    private final String sortBy;
    private final SortDirection sortDirection;

    public PageRequest(int page, int size, String sortBy, SortDirection sortDirection) {
        if (page < 0) {
            throw new IllegalArgumentException("页码不能小于0");
        }
        if (size <= 0) {
            throw new IllegalArgumentException("每页大小必须大于0");
        }
        this.page = page;
        this.size = size;
        this.sortBy = sortBy != null ? sortBy : "registeredAt";
        this.sortDirection = sortDirection != null ? sortDirection : SortDirection.DESC;
    }

    public static PageRequest of(int page, int size) {
        return new PageRequest(page, size, "registeredAt", SortDirection.DESC);
    }

    public static PageRequest of(int page, int size, String sortBy, SortDirection sortDirection) {
        return new PageRequest(page, size, sortBy, sortDirection);
    }

    public int getPage() {
        return page;
    }

    public int getSize() {
        return size;
    }

    public String getSortBy() {
        return sortBy;
    }

    public SortDirection getSortDirection() {
        return sortDirection;
    }

    public int getOffset() {
        return page * size;
    }

    public enum SortDirection {
        ASC, DESC
    }
}
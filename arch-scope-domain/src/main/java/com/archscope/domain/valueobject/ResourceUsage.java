package com.archscope.domain.valueobject;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ResourceUsage {
    private Double cpuUsage;        // CPU使用率（百分比）
    private Long memoryUsageMb;     // 内存使用量（MB）
    private Long diskIOBytes;       // 磁盘IO（字节）
    private Long networkIOBytes;    // 网络IO（字节）
    private Integer threadCount;    // 线程数
    private Long duration;          // 持续时间（毫秒）
    private Long tokenCount;        // LLM Token使用数量（如果适用）
} 
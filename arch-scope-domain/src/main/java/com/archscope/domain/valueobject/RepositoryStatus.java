package com.archscope.domain.valueobject;

/**
 * 代码仓库状态枚举
 */
public enum RepositoryStatus {
    ACTIVE("活跃"),
    INACTIVE("不活跃"),
    SYNCING("同步中"),
    SYNC_FAILED("同步失败"),
    ARCHIVED("已归档");
    
    private final String displayName;
    
    RepositoryStatus(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 根据名称获取仓库状态
     * @param name 状态名称
     * @return 仓库状态枚举值
     */
    public static RepositoryStatus fromString(String name) {
        try {
            return RepositoryStatus.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return INACTIVE; // 默认返回不活跃状态
        }
    }
} 
package com.archscope.domain.valueobject;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * 代码统计信息值对象
 */
@Data
@Builder
public class CodeStatistics {
    
    /**
     * 总代码行数
     */
    private long totalLinesOfCode;
    
    /**
     * 有效代码行数（不包括注释和空行）
     */
    private long effectiveLinesOfCode;
    
    /**
     * 注释行数
     */
    private long commentLines;
    
    /**
     * 空行数
     */
    private long blankLines;
    
    /**
     * 文件总数
     */
    private int totalFileCount;
    
    /**
     * 按文件类型分类的文件数量
     */
    private Map<String, Integer> fileCountByType;
    
    /**
     * 贡献者数量
     */
    private int contributorCount;
    
    /**
     * 按语言分类的代码行数
     */
    private Map<String, Long> linesOfCodeByLanguage;
    
    /**
     * 文件行数统计
     */
    @Data
    @Builder
    public static class FileLineStatistics {
        /**
         * 文件路径
         */
        private String filePath;
        
        /**
         * 总行数
         */
        private int totalLines;
        
        /**
         * 有效代码行数
         */
        private int effectiveLines;
        
        /**
         * 注释行数
         */
        private int commentLines;
        
        /**
         * 空行数
         */
        private int blankLines;
        
        /**
         * 文件大小（字节）
         */
        private long fileSize;
        
        /**
         * 语言类型
         */
        private String languageType;
    }
    
    /**
     * 合并两个统计信息
     * 
     * @param other 另一个统计信息
     * @return 合并后的统计信息
     */
    public CodeStatistics merge(CodeStatistics other) {
        if (other == null) {
            return this;
        }
        
        CodeStatisticsBuilder builder = CodeStatistics.builder()
                .totalLinesOfCode(this.totalLinesOfCode + other.totalLinesOfCode)
                .effectiveLinesOfCode(this.effectiveLinesOfCode + other.effectiveLinesOfCode)
                .commentLines(this.commentLines + other.commentLines)
                .blankLines(this.blankLines + other.blankLines)
                .totalFileCount(this.totalFileCount + other.totalFileCount)
                .contributorCount(Math.max(this.contributorCount, other.contributorCount)); // 贡献者数量取最大值
        
        // 合并文件类型统计
        if (this.fileCountByType != null || other.fileCountByType != null) {
            Map<String, Integer> mergedFileCount = new java.util.HashMap<>();
            if (this.fileCountByType != null) {
                mergedFileCount.putAll(this.fileCountByType);
            }
            if (other.fileCountByType != null) {
                other.fileCountByType.forEach((key, value) -> 
                    mergedFileCount.merge(key, value, Integer::sum));
            }
            builder.fileCountByType(mergedFileCount);
        }
        
        // 合并语言统计
        if (this.linesOfCodeByLanguage != null || other.linesOfCodeByLanguage != null) {
            Map<String, Long> mergedLanguageLines = new java.util.HashMap<>();
            if (this.linesOfCodeByLanguage != null) {
                mergedLanguageLines.putAll(this.linesOfCodeByLanguage);
            }
            if (other.linesOfCodeByLanguage != null) {
                other.linesOfCodeByLanguage.forEach((key, value) -> 
                    mergedLanguageLines.merge(key, value, Long::sum));
            }
            builder.linesOfCodeByLanguage(mergedLanguageLines);
        }
        
        return builder.build();
    }
    
    /**
     * 创建空的统计信息
     * 
     * @return 空的统计信息
     */
    public static CodeStatistics empty() {
        return CodeStatistics.builder()
                .totalLinesOfCode(0)
                .effectiveLinesOfCode(0)
                .commentLines(0)
                .blankLines(0)
                .totalFileCount(0)
                .contributorCount(0)
                .fileCountByType(new java.util.HashMap<>())
                .linesOfCodeByLanguage(new java.util.HashMap<>())
                .build();
    }
}

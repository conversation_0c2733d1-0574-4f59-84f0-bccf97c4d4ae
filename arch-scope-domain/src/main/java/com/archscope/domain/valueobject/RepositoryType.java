package com.archscope.domain.valueobject;

/**
 * 代码仓库类型枚举
 */
public enum RepositoryType {
    GIT("Git"),
    SVN("Subversion"),
    MERCURIAL("Mercurial"),
    PERFORCE("Perforce");
    
    private final String displayName;
    
    RepositoryType(String displayName) {
        this.displayName = displayName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 根据名称获取仓库类型
     * @param name 类型名称
     * @return 仓库类型枚举值
     */
    public static RepositoryType fromString(String name) {
        try {
            return RepositoryType.valueOf(name.toUpperCase());
        } catch (IllegalArgumentException e) {
            return GIT; // 默认返回GIT类型
        }
    }
} 
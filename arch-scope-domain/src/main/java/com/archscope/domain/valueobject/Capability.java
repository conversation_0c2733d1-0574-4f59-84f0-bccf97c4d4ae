package com.archscope.domain.valueobject;

import java.util.Objects;

/**
 * 能力值对象，表示服务提供的一个能力
 */
public class Capability {
    private final String name;
    private final String description;
    private final String version;

    public Capability(String name, String description, String version) {
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("能力名称不能为空");
        }
        this.name = name.trim();
        this.description = description != null ? description.trim() : "";
        this.version = version != null ? version.trim() : "1.0.0";
    }

    public static Capability of(String name, String description, String version) {
        return new Capability(name, description, version);
    }

    public static Capability of(String name, String description) {
        return new Capability(name, description, "1.0.0");
    }

    public static Capability of(String name) {
        return new Capability(name, "", "1.0.0");
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public String getVersion() {
        return version;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Capability that = (Capability) o;
        return Objects.equals(name, that.name) && 
               Objects.equals(version, that.version);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, version);
    }

    @Override
    public String toString() {
        return "Capability{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", version='" + version + '\'' +
                '}';
    }
}
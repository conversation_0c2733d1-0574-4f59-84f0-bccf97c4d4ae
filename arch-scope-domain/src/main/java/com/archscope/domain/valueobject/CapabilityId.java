package com.archscope.domain.valueobject;

import java.util.Objects;

/**
 * 能力ID值对象，用于唯一标识一个服务能力
 * 使用String类型保持代码兼容性，在转换层处理与数据库Long类型的转换
 */
public class CapabilityId {
    private final String value;

    private CapabilityId(String value) {
        this.value = value;
    }

    public static CapabilityId of(String value) {
        return new CapabilityId(value);
    }

    public static CapabilityId of(Long value) {
        if (value == null) {
            return new CapabilityId(null);
        }
        return new CapabilityId(value.toString());
    }

    public static CapabilityId createNew() {
        return new CapabilityId(java.util.UUID.randomUUID().toString());
    }

    public String getValue() {
        return value;
    }

    /**
     * 获取Long形式的值（用于数据库操作）
     */
    public Long getLongValue() {
        if (value == null) {
            return null;
        }
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CapabilityId that = (CapabilityId) o;
        return Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return value.toString();
    }
}
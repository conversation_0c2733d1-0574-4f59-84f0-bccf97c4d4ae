package com.archscope.domain.valueobject;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 元数据值对象，用于存储服务的额外信息
 */
public class Metadata {
    private final Map<String, String> entries;

    private Metadata(Map<String, String> entries) {
        this.entries = new HashMap<>(entries);
    }

    public static Metadata of(Map<String, String> entries) {
        return new Metadata(entries != null ? entries : Collections.emptyMap());
    }

    public static Metadata empty() {
        return new Metadata(Collections.emptyMap());
    }

    public Map<String, String> getEntries() {
        return Collections.unmodifiableMap(entries);
    }

    public String get(String key) {
        return entries.get(key);
    }

    public boolean containsKey(String key) {
        return entries.containsKey(key);
    }

    public int size() {
        return entries.size();
    }

    public boolean isEmpty() {
        return entries.isEmpty();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Metadata metadata = (Metadata) o;
        return Objects.equals(entries, metadata.entries);
    }

    @Override
    public int hashCode() {
        return Objects.hash(entries);
    }

    @Override
    public String toString() {
        return "Metadata{" +
                "entries=" + entries +
                '}';
    }
}
package com.archscope.domain.valueobject;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用户偏好设置值对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserPreference {
    private String theme;           // 用户界面主题
    private String language;        // 界面语言
    private Boolean emailNotification;  // 是否开启邮件通知
    private Boolean systemNotification; // 是否开启系统通知
} 
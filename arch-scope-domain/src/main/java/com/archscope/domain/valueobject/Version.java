package com.archscope.domain.valueobject;

import java.util.Objects;
import java.util.regex.Pattern;

/**
 * 版本值对象，表示服务或能力的版本号
 */
public class Version {
    private static final Pattern VERSION_PATTERN = Pattern.compile("^\\d+(\\.\\d+)*(-[a-zA-Z0-9]+)?$");
    
    private final String value;

    private Version(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Version cannot be null or empty");
        }
        if (!VERSION_PATTERN.matcher(value).matches()) {
            throw new IllegalArgumentException("Invalid version format: " + value);
        }
        this.value = value;
    }

    public static Version of(String value) {
        return new Version(value);
    }

    public String getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Version version = (Version) o;
        return Objects.equals(value, version.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return value;
    }
}
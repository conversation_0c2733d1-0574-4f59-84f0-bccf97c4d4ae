package com.archscope.domain.valueobject;

import java.util.Objects;

/**
 * 需求ID值对象，用于唯一标识一个需求
 * 使用String类型保持代码兼容性，在转换层处理与数据库Long类型的转换
 */
public class RequirementId {
    private final String value;

    private RequirementId(String value) {
        this.value = value;
    }

    public static RequirementId of(String value) {
        return new RequirementId(value);
    }

    public static RequirementId of(Long value) {
        if (value == null) {
            return new RequirementId(null);
        }
        return new RequirementId(value.toString());
    }

    public static RequirementId createNew() {
        return new RequirementId(java.util.UUID.randomUUID().toString());
    }

    public String getValue() {
        return value;
    }

    /**
     * 获取Long形式的值（用于数据库操作）
     */
    public Long getLongValue() {
        if (value == null) {
            return null;
        }
        try {
            return Long.parseLong(value);
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RequirementId that = (RequirementId) o;
        return Objects.equals(value, that.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return value.toString();
    }
}
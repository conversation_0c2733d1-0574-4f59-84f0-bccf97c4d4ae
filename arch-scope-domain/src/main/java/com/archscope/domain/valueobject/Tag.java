package com.archscope.domain.valueobject;

import java.util.Objects;

/**
 * 标签值对象，用于服务和能力的分类和标记
 */
public class Tag {
    private final String value;

    private Tag(String value) {
        if (value == null || value.trim().isEmpty()) {
            throw new IllegalArgumentException("Tag cannot be null or empty");
        }
        this.value = value.trim();
    }

    public static Tag of(String value) {
        return new Tag(value);
    }

    public String getValue() {
        return value;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Tag tag = (Tag) o;
        return Objects.equals(value, tag.value);
    }

    @Override
    public int hashCode() {
        return Objects.hash(value);
    }

    @Override
    public String toString() {
        return value;
    }
}
package com.archscope.domain.entity;

import com.archscope.domain.model.parser.FileParseResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 解析结果缓存实体
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParseResultCache implements Serializable {
    
    private static final long serialVersionUID = 1L;
    
    /**
     * 缓存ID
     */
    private Long id;
    
    /**
     * 代码仓库ID
     */
    private Long repositoryId;
    
    /**
     * 提交ID
     */
    private String commitId;
    
    /**
     * 解析结果列表
     */
    @Builder.Default
    private List<FileParseResult> parseResults = new ArrayList<>();
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
    
    /**
     * 缓存是否有效
     */
    private boolean valid;
    
    /**
     * 缓存大小（字节）
     */
    private long cacheSize;
}

package com.archscope.domain.entity;

import com.archscope.domain.valueobject.DocumentType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentVersion {
    private Long id;
    private Long projectId;              // 关联的项目ID
    private String commitId;             // Git提交ID
    private String contentPath;          // 内容存储路径
    private LocalDateTime timestamp;     // 创建时间戳
    
    // 根据原型扩展的字段
    private DocumentType docType;        // 文档类型，使用枚举替代字符串
    private String versionTag;           // 版本标签（如v1.0, v2.0等）
    private Map<String, Object> compareMetadata; // 版本比较元数据
    private String title;                // 文档标题
    private String description;          // 文档描述
    private String author;               // 文档作者
    private LocalDateTime lastModified;  // 最后修改时间
    private Boolean isPublished;         // 是否已发布
    private String status;               // 文档状态（草稿、已审核、已发布等）
} 
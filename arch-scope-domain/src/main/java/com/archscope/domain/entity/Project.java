package com.archscope.domain.entity;

import com.archscope.domain.valueobject.ProjectConfiguration;
import com.archscope.domain.valueobject.ProjectType;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 项目实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Project {
    private Long id;
    private String name;
    private String description;
    private String repositoryUrl;
    private String normalizedRepositoryUrl;  // 标准化的仓库URL，用于重复检测
    private String branch;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private LocalDateTime lastAnalyzedAt;
    private Long creatorId;
    private String status;          // PENDING, ANALYZING, COMPLETED, FAILED
    private Boolean active;
    private String documentationPath;
    private Integer analysisCount;
    private Integer documentationVersion;
    private List<Long> memberIds;   // 项目成员ID列表
    
    // 根据原型扩展的字段
    private Double rating;                // 项目评分（星级）
    private Long linesOfCode;             // 代码行数统计
    private Integer fileCount;            // 文件数量统计
    private Integer contributorCount;     // 贡献者数量
    private String icon;                  // 项目图标/封面路径
    private ProjectType type;             // 项目类型，使用枚举替代字符串
    private ProjectConfiguration configuration; // 使用值对象替代Map
    private List<Long> documentIds;       // 相关文档ID列表
    private List<Long> taskIds;           // 相关任务ID列表
    private String latestAnalyzedCommitId; // 最新分析的提交ID
}
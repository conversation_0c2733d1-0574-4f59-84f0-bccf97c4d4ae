package com.archscope.domain.entity;

import com.archscope.domain.valueobject.RepositoryStatus;
import com.archscope.domain.valueobject.RepositoryType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * 代码仓库实体类
 * 用于管理项目的代码仓库信息，支持多种认证方式（用户名密码、访问令牌、SSH密钥）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CodeRepository {
    private Long id;                     // 仓库ID
    private String name;                 // 仓库名称
    private String url;                  // 仓库URL
    private String username;             // 访问用户名（HTTP认证）
    private String password;             // 访问密码（HTTP认证）
    private String accessToken;          // 访问令牌（GitHub、GitLab等）
    private RepositoryType type;         // 仓库类型
    private String defaultBranch;        // 默认分支
    private LocalDateTime createdAt;     // 创建时间
    private LocalDateTime lastSyncedAt;  // 最后同步时间
    private RepositoryStatus status;     // 仓库状态
    private boolean sshEnabled;          // 是否启用SSH认证
    private String sshKey;               // SSH私钥
    private String sshKeyPassphrase;     // SSH私钥密码
    private Long projectId;              // 所属项目ID
} 
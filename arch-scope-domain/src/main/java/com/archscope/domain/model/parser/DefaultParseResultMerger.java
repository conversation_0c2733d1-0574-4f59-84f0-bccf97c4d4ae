package com.archscope.domain.model.parser;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 解析结果合并器默认实现
 * 负责将LLM解析结果与传统解析结果进行合并或校验
 */
@Slf4j
@Component
public class DefaultParseResultMerger implements ParseResultMerger {

    @Override
    public FileParseResult merge(FileParseResult llmResult, FileParseResult traditionalResult, MergeStrategy mergeStrategy) {
        log.info("合并解析结果，使用策略: {}", mergeStrategy);
        
        // 如果任一结果为null，返回另一个结果
        if (llmResult == null) {
            log.warn("LLM解析结果为null，直接返回传统解析结果");
            return traditionalResult;
        }
        if (traditionalResult == null) {
            log.warn("传统解析结果为null，直接返回LLM解析结果");
            return llmResult;
        }
        
        // 如果任一结果解析失败，使用另一个成功的结果
        if (!llmResult.isSuccessful() && traditionalResult.isSuccessful()) {
            log.warn("LLM解析失败，使用传统解析结果");
            return traditionalResult;
        }
        if (llmResult.isSuccessful() && !traditionalResult.isSuccessful()) {
            log.warn("传统解析失败，使用LLM解析结果");
            return llmResult;
        }
        if (!llmResult.isSuccessful() && !traditionalResult.isSuccessful()) {
            log.error("LLM解析和传统解析均失败");
            // 创建一个包含两者错误信息的结果
            return FileParseResult.builder()
                    .filename(llmResult.getFilename())
                    .filePath(llmResult.getFilePath())
                    .languageType(llmResult.getLanguageType())
                    .successful(false)
                    .errorMessage("LLM解析错误: " + llmResult.getErrorMessage() + 
                                 "; 传统解析错误: " + traditionalResult.getErrorMessage())
                    .build();
        }
        
        // 根据合并策略选择不同的合并方法
        switch (mergeStrategy) {
            case LLM_PRIORITY:
                return mergeLlmPriority(llmResult, traditionalResult);
            case TRADITIONAL_PRIORITY:
                return mergeTraditionalPriority(llmResult, traditionalResult);
            case SMART_MERGE:
                return smartMerge(llmResult, traditionalResult);
            default:
                log.warn("未知的合并策略: {}，使用智能合并", mergeStrategy);
                return smartMerge(llmResult, traditionalResult);
        }
    }
    
    /**
     * 优先使用LLM结果的合并策略
     */
    private FileParseResult mergeLlmPriority(FileParseResult llmResult, FileParseResult traditionalResult) {
        log.debug("使用LLM优先策略合并结果");
        
        // 创建一个新的结果，基于LLM结果
        FileParseResult.FileParseResultBuilder resultBuilder = FileParseResult.builder()
                .filename(llmResult.getFilename())
                .filePath(llmResult.getFilePath())
                .languageType(llmResult.getLanguageType())
                .packageName(llmResult.getPackageName())
                .fileComment(llmResult.getFileComment())
                .successful(true);
        
        // 导入列表：如果LLM结果为空，使用传统结果
        List<String> imports = llmResult.getImports();
        if (imports == null || imports.isEmpty()) {
            imports = traditionalResult.getImports();
        }
        resultBuilder.imports(imports);
        
        // 类定义：如果LLM结果为空，使用传统结果
        List<ClassDefinition> classDefinitions = llmResult.getClassDefinitions();
        if (classDefinitions == null || classDefinitions.isEmpty()) {
            classDefinitions = traditionalResult.getClassDefinitions();
        }
        resultBuilder.classDefinitions(classDefinitions);
        
        // 依赖关系：如果LLM结果为空，使用传统结果
        List<DependencyRelation> dependencies = llmResult.getDependencies();
        if (dependencies == null || dependencies.isEmpty()) {
            dependencies = traditionalResult.getDependencies();
        }
        resultBuilder.dependencies(dependencies);
        
        return resultBuilder.build();
    }
    
    /**
     * 优先使用传统解析结果的合并策略
     */
    private FileParseResult mergeTraditionalPriority(FileParseResult llmResult, FileParseResult traditionalResult) {
        log.debug("使用传统解析优先策略合并结果");
        
        // 创建一个新的结果，基于传统解析结果
        FileParseResult.FileParseResultBuilder resultBuilder = FileParseResult.builder()
                .filename(traditionalResult.getFilename())
                .filePath(traditionalResult.getFilePath())
                .languageType(traditionalResult.getLanguageType())
                .packageName(traditionalResult.getPackageName())
                .successful(true);
        
        // 文件注释：传统解析通常不提供，使用LLM结果
        resultBuilder.fileComment(llmResult.getFileComment());
        
        // 导入列表：如果传统结果为空，使用LLM结果
        List<String> imports = traditionalResult.getImports();
        if (imports == null || imports.isEmpty()) {
            imports = llmResult.getImports();
        }
        resultBuilder.imports(imports);
        
        // 类定义：如果传统结果为空，使用LLM结果
        List<ClassDefinition> classDefinitions = traditionalResult.getClassDefinitions();
        if (classDefinitions == null || classDefinitions.isEmpty()) {
            classDefinitions = llmResult.getClassDefinitions();
        }
        resultBuilder.classDefinitions(classDefinitions);
        
        // 依赖关系：如果传统结果为空，使用LLM结果
        List<DependencyRelation> dependencies = traditionalResult.getDependencies();
        if (dependencies == null || dependencies.isEmpty()) {
            dependencies = llmResult.getDependencies();
        }
        resultBuilder.dependencies(dependencies);
        
        return resultBuilder.build();
    }
    
    /**
     * 智能合并策略，根据各字段的可靠性选择结果
     */
    private FileParseResult smartMerge(FileParseResult llmResult, FileParseResult traditionalResult) {
        log.debug("使用智能合并策略合并结果");
        
        // 创建一个新的结果
        FileParseResult.FileParseResultBuilder resultBuilder = FileParseResult.builder()
                .filename(llmResult.getFilename())
                .filePath(llmResult.getFilePath())
                .languageType(llmResult.getLanguageType())
                .successful(true);
        
        // 包名：传统解析通常更准确
        resultBuilder.packageName(traditionalResult.getPackageName() != null ? 
                traditionalResult.getPackageName() : llmResult.getPackageName());
        
        // 文件注释：LLM通常更好
        resultBuilder.fileComment(llmResult.getFileComment());
        
        // 导入列表：合并两者的结果，去重
        List<String> mergedImports = Stream.concat(
                llmResult.getImports().stream(),
                traditionalResult.getImports().stream())
                .distinct()
                .collect(Collectors.toList());
        resultBuilder.imports(mergedImports);
        
        // 类定义：智能合并
        List<ClassDefinition> mergedClassDefinitions = mergeClassDefinitions(
                llmResult.getClassDefinitions(), 
                traditionalResult.getClassDefinitions());
        resultBuilder.classDefinitions(mergedClassDefinitions);
        
        // 依赖关系：智能合并
        List<DependencyRelation> mergedDependencies = mergeDependencies(
                llmResult.getDependencies(), 
                traditionalResult.getDependencies());
        resultBuilder.dependencies(mergedDependencies);
        
        return resultBuilder.build();
    }
    
    /**
     * 合并类定义列表
     */
    private List<ClassDefinition> mergeClassDefinitions(List<ClassDefinition> llmClasses, List<ClassDefinition> traditionalClasses) {
        // 创建一个映射，用于快速查找类定义
        Map<String, ClassDefinition> classMap = new HashMap<>();
        
        // 首先添加传统解析的类定义
        for (ClassDefinition classDef : traditionalClasses) {
            classMap.put(classDef.getName(), classDef);
        }
        
        // 然后处理LLM的类定义
        for (ClassDefinition llmClass : llmClasses) {
            String className = llmClass.getName();
            
            if (classMap.containsKey(className)) {
                // 合并类定义
                ClassDefinition traditionalClass = classMap.get(className);
                ClassDefinition mergedClass = mergeClassDefinition(llmClass, traditionalClass);
                classMap.put(className, mergedClass);
            } else {
                // 添加LLM独有的类定义
                classMap.put(className, llmClass);
            }
        }
        
        return new ArrayList<>(classMap.values());
    }
    
    /**
     * 合并单个类定义
     */
    private ClassDefinition mergeClassDefinition(ClassDefinition llmClass, ClassDefinition traditionalClass) {
        // 创建一个新的类定义，基于传统解析结果
        ClassDefinition.ClassDefinitionBuilder builder = ClassDefinition.builder()
                .name(traditionalClass.getName())
                .type(traditionalClass.getType())
                .packageName(traditionalClass.getPackageName())
                .fullyQualifiedName(traditionalClass.getFullyQualifiedName())
                .accessModifier(traditionalClass.getAccessModifier())
                .isAbstract(traditionalClass.isAbstract())
                .isFinal(traditionalClass.isFinal());
        
        // 父类：传统解析通常更准确
        builder.superClass(traditionalClass.getSuperClass() != null ? 
                traditionalClass.getSuperClass() : llmClass.getSuperClass());
        
        // 接口：合并两者的结果，去重
        List<String> mergedInterfaces = Stream.concat(
                llmClass.getInterfaces().stream(),
                traditionalClass.getInterfaces().stream())
                .distinct()
                .collect(Collectors.toList());
        builder.interfaces(mergedInterfaces);
        
        // 字段：LLM可能提供更多信息
        if (llmClass.getFields() != null && !llmClass.getFields().isEmpty()) {
            builder.fields(llmClass.getFields());
        } else {
            builder.fields(traditionalClass.getFields());
        }
        
        // 方法：LLM可能提供更多信息
        if (llmClass.getMethods() != null && !llmClass.getMethods().isEmpty()) {
            builder.methods(llmClass.getMethods());
        } else {
            builder.methods(traditionalClass.getMethods());
        }
        
        return builder.build();
    }
    
    /**
     * 合并依赖关系列表
     */
    private List<DependencyRelation> mergeDependencies(List<DependencyRelation> llmDependencies, List<DependencyRelation> traditionalDependencies) {
        // 创建一个映射，用于快速查找依赖关系
        Map<String, DependencyRelation> dependencyMap = new HashMap<>();
        
        // 首先添加传统解析的依赖关系
        for (DependencyRelation dependency : traditionalDependencies) {
            String key = dependency.getSourceClass() + "->" + dependency.getTargetClass() + ":" + dependency.getType();
            dependencyMap.put(key, dependency);
        }
        
        // 然后处理LLM的依赖关系
        for (DependencyRelation llmDependency : llmDependencies) {
            String key = llmDependency.getSourceClass() + "->" + llmDependency.getTargetClass() + ":" + llmDependency.getType();
            
            if (!dependencyMap.containsKey(key)) {
                // 添加LLM独有的依赖关系
                dependencyMap.put(key, llmDependency);
            }
            // 对于重复的依赖关系，保留传统解析的结果
        }
        
        return new ArrayList<>(dependencyMap.values());
    }
}

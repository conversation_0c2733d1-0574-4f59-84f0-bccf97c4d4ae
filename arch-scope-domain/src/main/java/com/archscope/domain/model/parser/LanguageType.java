package com.archscope.domain.model.parser;

/**
 * 编程语言类型枚举
 */
public enum LanguageType {
    /**
     * Java语言
     */
    JAVA,
    
    /**
     * JavaScript语言
     */
    JAVASCRIPT,
    
    /**
     * TypeScript语言
     */
    TYPESCRIPT,
    
    /**
     * HTML语言
     */
    HTML,
    
    /**
     * CSS语言
     */
    CSS,
    
    /**
     * XML语言
     */
    XML,
    
    /**
     * JSON语言
     */
    JSON,
    
    /**
     * Python语言
     */
    PYTHON,
    
    /**
     * C语言
     */
    C,
    
    /**
     * C++语言
     */
    CPP,
    
    /**
     * C#语言
     */
    CSHARP,
    
    /**
     * Go语言
     */
    GO,
    
    /**
     * Ruby语言
     */
    RUBY,
    
    /**
     * PHP语言
     */
    PHP,
    
    /**
     * Swift语言
     */
    SWIFT,
    
    /**
     * Kotlin语言
     */
    KOTLIN,
    
    /**
     * Rust语言
     */
    RUST,
    
    /**
     * 未知语言类型
     */
    UNKNOWN;
    
    /**
     * 根据文件扩展名判断语言类型
     *
     * @param filename 文件名
     * @return 语言类型
     */
    public static LanguageType fromFilename(String filename) {
        if (filename == null || filename.isEmpty()) {
            return UNKNOWN;
        }
        
        String extension = getFileExtension(filename);
        
        switch (extension.toLowerCase()) {
            case "java":
                return JAVA;
            case "js":
                return JAVASCRIPT;
            case "ts":
                return TYPESCRIPT;
            case "html":
            case "htm":
                return HTML;
            case "css":
                return CSS;
            case "xml":
                return XML;
            case "json":
                return JSON;
            case "py":
            case "pyw":
                return PYTHON;
            case "c":
                return C;
            case "cpp":
            case "cc":
            case "cxx":
            case "c++":
                return CPP;
            case "cs":
                return CSHARP;
            case "go":
                return GO;
            case "rb":
            case "ruby":
                return RUBY;
            case "php":
                return PHP;
            case "swift":
                return SWIFT;
            case "kt":
            case "kts":
                return KOTLIN;
            case "rs":
                return RUST;
            default:
                return UNKNOWN;
        }
    }
    
    /**
     * 获取文件扩展名
     *
     * @param filename 文件名
     * @return 扩展名
     */
    private static String getFileExtension(String filename) {
        int lastDotIndex = filename.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < filename.length() - 1) {
            return filename.substring(lastDotIndex + 1);
        }
        return "";
    }
} 
package com.archscope.domain.model.parser;

import lombok.Builder;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件解析结果模型
 */
@Data
@Builder
public class FileParseResult {
    /**
     * 文件名
     */
    @NotBlank(message = "文件名不能为空")
    private String filename;
    
    /**
     * 文件路径
     */
    @NotBlank(message = "文件路径不能为空")
    private String filePath;
    
    /**
     * 语言类型
     */
    @NotNull(message = "语言类型不能为空")
    private LanguageType languageType;
    
    /**
     * 包名
     */
    private String packageName;
    
    /**
     * 导入列表
     */
    @Builder.Default
    @NotNull(message = "导入列表不能为null")
    @Size(min = 0, message = "导入列表可以为空，但不能为null")
    private List<String> imports = new ArrayList<>();
    
    /**
     * 类定义列表
     */
    @Builder.Default
    @NotNull(message = "类定义列表不能为null")
    @Size(min = 0, message = "类定义列表可以为空，但不能为null")
    private List<ClassDefinition> classDefinitions = new ArrayList<>();
    
    /**
     * 依赖关系列表
     */
    @Builder.Default
    @NotNull(message = "依赖关系列表不能为null")
    @Size(min = 0, message = "依赖关系列表可以为空，但不能为null")
    private List<DependencyRelation> dependencies = new ArrayList<>();
    
    /**
     * 文件注释
     */
    private String fileComment;
    
    /**
     * 解析错误信息（如果有）
     */
    private String errorMessage;
    
    /**
     * 解析是否成功
     */
    @NotNull(message = "解析状态不能为null")
    private boolean successful;
} 

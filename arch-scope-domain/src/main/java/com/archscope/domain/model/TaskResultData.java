package com.archscope.domain.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 任务结果数据模型
 * 用于在domain层内部处理LLM任务结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskResultData {

    /**
     * 任务结果列表
     */
    private List<DocumentResult> results;

    /**
     * 提交ID
     */
    private String commitId;

    /**
     * 文档结果内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DocumentResult {

        /**
         * 文档类型
         */
        private String documentType;

        /**
         * 文档标题
         */
        private String documentTitle;

        /**
         * 文档内容 (Markdown格式)
         */
        private String documentContent;

        /**
         * 文档文件路径
         */
        private String filePath;

        /**
         * 生成状态
         */
        private String status;

        /**
         * 错误信息 (当status为FAILED时)
         */
        private String errorMessage;
    }
}

package com.archscope.domain.model.servicediscovery;

import com.archscope.domain.valueobject.*;

import java.net.URL;
import java.time.Instant;
import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 服务实体，表示一个可被发现的服务
 */
public class Service {
    private final ServiceId id;
    private String name;
    private String description;
    private ServiceType type;
    private Version version;
    private URL endpoint;
    private String groupId;        // Maven/Gradle 坐标 - groupId
    private String artifactId;     // Maven/Gradle 坐标 - artifactId
    private final Set<Tag> tags;
    private final Set<Capability> capabilities;
    private ServiceStatus status;
    private Metadata metadata;
    private final Instant registeredAt;
    private Instant lastUpdatedAt;

    private Service(ServiceId id, String name, String description, ServiceType type, Version version,
                   URL endpoint, String groupId, String artifactId, Set<Tag> tags, Set<Capability> capabilities,
                   ServiceStatus status, Metadata metadata, Instant registeredAt, Instant lastUpdatedAt) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.type = type;
        this.version = version;
        this.endpoint = endpoint;
        this.groupId = groupId;
        this.artifactId = artifactId;
        this.tags = new HashSet<>(tags);
        this.capabilities = new HashSet<>(capabilities);
        this.status = status;
        this.metadata = metadata;
        this.registeredAt = registeredAt;
        this.lastUpdatedAt = lastUpdatedAt;
    }

    public static Service create(String name, String description, ServiceType type, Version version,
                                URL endpoint, String groupId, String artifactId, Set<Tag> tags,
                                ServiceStatus status, Metadata metadata) {
        return new Service(
                ServiceId.createNew(),
                name,
                description,
                type,
                version,
                endpoint,
                groupId,
                artifactId,
                tags != null ? tags : Collections.emptySet(),
                Collections.emptySet(),
                status != null ? status : ServiceStatus.ACTIVE,
                metadata != null ? metadata : Metadata.empty(),
                Instant.now(),
                Instant.now()
        );
    }

    public static Service restore(ServiceId id, String name, String description, ServiceType type, Version version,
                                 URL endpoint, String groupId, String artifactId, Set<Tag> tags, Set<Capability> capabilities,
                                 ServiceStatus status, Metadata metadata, Instant registeredAt, Instant lastUpdatedAt) {
        return new Service(
                id,
                name,
                description,
                type,
                version,
                endpoint,
                groupId,
                artifactId,
                tags != null ? tags : Collections.emptySet(),
                capabilities != null ? capabilities : Collections.emptySet(),
                status != null ? status : ServiceStatus.UNKNOWN,
                metadata != null ? metadata : Metadata.empty(),
                registeredAt,
                lastUpdatedAt
        );
    }

    public void update(String name, String description, ServiceType type, Version version,
                      URL endpoint, String groupId, String artifactId, Set<Tag> tags,
                      ServiceStatus status, Metadata metadata) {
        this.name = name;
        this.description = description;
        this.type = type;
        this.version = version;
        this.endpoint = endpoint;
        this.groupId = groupId;
        this.artifactId = artifactId;
        if (tags != null) {
            this.tags.clear();
            this.tags.addAll(tags);
        }
        this.status = status;
        this.metadata = metadata != null ? metadata : Metadata.empty();
        this.lastUpdatedAt = Instant.now();
    }

    public void addCapability(Capability capability) {
        this.capabilities.add(capability);
        this.lastUpdatedAt = Instant.now();
    }

    public void removeCapability(Capability capability) {
        this.capabilities.remove(capability);
        this.lastUpdatedAt = Instant.now();
    }

    public void updateStatus(ServiceStatus status) {
        this.status = status;
        this.lastUpdatedAt = Instant.now();
    }

    public ServiceId getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public ServiceType getType() {
        return type;
    }

    public Version getVersion() {
        return version;
    }

    public URL getEndpoint() {
        return endpoint;
    }

    public String getGroupId() {
        return groupId;
    }

    public String getArtifactId() {
        return artifactId;
    }

    public Set<Tag> getTags() {
        return Collections.unmodifiableSet(tags);
    }

    public Set<Capability> getCapabilities() {
        return Collections.unmodifiableSet(capabilities);
    }

    public ServiceStatus getStatus() {
        return status;
    }

    public Metadata getMetadata() {
        return metadata;
    }

    public Instant getRegisteredAt() {
        return registeredAt;
    }

    public Instant getLastUpdatedAt() {
        return lastUpdatedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Service service = (Service) o;
        return Objects.equals(id, service.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Service{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", type=" + type +
                ", version=" + version +
                ", groupId='" + groupId + '\'' +
                ", artifactId='" + artifactId + '\'' +
                ", status=" + status +
                '}';
    }
}
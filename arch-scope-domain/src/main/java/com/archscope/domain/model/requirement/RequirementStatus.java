package com.archscope.domain.model.requirement;

/**
 * 需求状态枚举
 */
public enum RequirementStatus {
    NEW("新建"),
    SUBMITTED("已提交"),
    UNDER_REVIEW("审核中"),
    APPROVED("已批准"),
    IN_PROGRESS("进行中"),
    COMPLETED("已完成"),
    REJECTED("已拒绝"),
    CANCELLED("已取消");
    
    private final String description;
    
    RequirementStatus(String description) {
        this.description = description;
    }
    
    public String getDescription() {
        return description;
    }
}

package com.archscope.domain.model.parser;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 类定义模型
 */
@Data
@Builder
public class ClassDefinition {
    /**
     * 类名
     */
    private String name;
    
    /**
     * 全限定类名
     */
    private String fullyQualifiedName;
    
    /**
     * 包名
     */
    private String packageName;
    
    /**
     * 父类
     */
    private String superClass;
    
    /**
     * 实现的接口列表
     */
    @Builder.Default
    private List<String> interfaces = new ArrayList<>();
    
    /**
     * 类型（类、接口、枚举等）
     */
    private ClassType type;
    
    /**
     * 访问修饰符
     */
    private AccessModifier accessModifier;
    
    /**
     * 字段列表
     */
    @Builder.Default
    private List<FieldDefinition> fields = new ArrayList<>();
    
    /**
     * 方法列表
     */
    @Builder.Default
    private List<MethodDefinition> methods = new ArrayList<>();
    
    /**
     * 内部类列表
     */
    @Builder.Default
    private List<ClassDefinition> innerClasses = new ArrayList<>();
    
    /**
     * 注解列表
     */
    @Builder.Default
    private List<String> annotations = new ArrayList<>();
    
    /**
     * 依赖关系列表
     */
    @Builder.Default
    private List<DependencyRelation> dependencies = new ArrayList<>();
    
    /**
     * 是否为抽象类
     */
    private boolean isAbstract;
    
    /**
     * 是否为静态类
     */
    private boolean isStatic;
    
    /**
     * 是否为最终类
     */
    private boolean isFinal;
    
    /**
     * 类的注释
     */
    private String comment;

    public void setModifiers(List<String> modifiersFromJson) {
        if (modifiersFromJson == null) {
            return;
        }
        for (String modifier : modifiersFromJson) {
            if (modifier == null) continue;
            switch (modifier.toLowerCase()) {
                case "public":
                    this.accessModifier = AccessModifier.PUBLIC;
                    break;
                case "private":
                    this.accessModifier = AccessModifier.PRIVATE;
                    break;
                case "protected":
                    this.accessModifier = AccessModifier.PROTECTED;
                    break;
                case "static":
                    this.isStatic = true;
                    break;
                case "final":
                    this.isFinal = true;
                    break;
                case "abstract":
                    this.isAbstract = true;
                    break;
                // PACKAGE_PRIVATE is often the default (no explicit keyword)
                // but if JSON might send it, it could be handled here.
                default:
                    // unknown modifier, maybe log or ignore
                    break;
            }
        }
        // If no access modifier was explicitly set from the list,
        // and it's a top-level class (not inner usually),
        // it might default to public or package-private depending on context,
        // for now, if not set, it remains whatever its default is (null or set by Lombok builder).
        // For typical top-level classes, if no explicit public/private/protected, it's package-private.
        // However, LLM output might always provide one. If not, this.accessModifier could remain null.
        // For this parser, if nothing matches, accessModifier remains as is (e.g. null or default by builder).
        // If no explicit access modifier like public, private, or protected is found,
        // and accessModifier is still null, we might infer PACKAGE_PRIVATE.
        // However, this logic might be too presumptive for a simple setter.
        // Let's assume the JSON will provide enough info, or null is acceptable if not provided.
    }
} 
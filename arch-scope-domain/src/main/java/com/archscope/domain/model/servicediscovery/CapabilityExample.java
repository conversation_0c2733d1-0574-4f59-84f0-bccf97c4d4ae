package com.archscope.domain.model.servicediscovery;

import java.util.Objects;

/**
 * 能力示例值对象，用于展示服务能力的使用示例
 */
public class CapabilityExample {
    private final String name;
    private final String description;
    private final String requestExample;
    private final String responseExample;

    private CapabilityExample(String name, String description, String requestExample, String responseExample) {
        this.name = name;
        this.description = description;
        this.requestExample = requestExample;
        this.responseExample = responseExample;
    }

    public static CapabilityExample of(String name, String description, String requestExample, String responseExample) {
        return new CapabilityExample(name, description, requestExample, responseExample);
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public String getRequestExample() {
        return requestExample;
    }

    public String getResponseExample() {
        return responseExample;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CapabilityExample that = (CapabilityExample) o;
        return Objects.equals(name, that.name) &&
                Objects.equals(description, that.description) &&
                Objects.equals(requestExample, that.requestExample) &&
                Objects.equals(responseExample, that.responseExample);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, description, requestExample, responseExample);
    }

    @Override
    public String toString() {
        return "CapabilityExample{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", requestExample='" + requestExample + '\'' +
                ", responseExample='" + responseExample + '\'' +
                '}';
    }
}
package com.archscope.domain.model.capability;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public class Capability {
    private String capabilityId;
    private String serviceId;
    private String name;
    private String description;
    private String functionSignature;
    private String inputSchema;
    private String outputSchema;
    private List<CapabilityExample> examples;
    private List<String> tags;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 构造函数
    public Capability(String serviceId, String name, String description, String functionSignature,
                     String inputSchema, String outputSchema, List<CapabilityExample> examples, 
                     List<String> tags) {
        this.capabilityId = UUID.randomUUID().toString();
        this.serviceId = serviceId;
        this.name = name;
        this.description = description;
        this.functionSignature = functionSignature;
        this.inputSchema = inputSchema;
        this.outputSchema = outputSchema;
        this.examples = examples;
        this.tags = tags;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Getters and setters
    public String getCapabilityId() {
        return capabilityId;
    }

    public void setCapabilityId(String capabilityId) {
        this.capabilityId = capabilityId;
    }

    public String getServiceId() {
        return serviceId;
    }

    public void setServiceId(String serviceId) {
        this.serviceId = serviceId;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getFunctionSignature() {
        return functionSignature;
    }

    public void setFunctionSignature(String functionSignature) {
        this.functionSignature = functionSignature;
    }

    public String getInputSchema() {
        return inputSchema;
    }

    public void setInputSchema(String inputSchema) {
        this.inputSchema = inputSchema;
    }

    public String getOutputSchema() {
        return outputSchema;
    }

    public void setOutputSchema(String outputSchema) {
        this.outputSchema = outputSchema;
    }

    public List<CapabilityExample> getExamples() {
        return examples;
    }

    public void setExamples(List<CapabilityExample> examples) {
        this.examples = examples;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // 业务方法
    public void update(String name, String description, String functionSignature,
                      String inputSchema, String outputSchema, List<CapabilityExample> examples, 
                      List<String> tags) {
        this.name = name;
        this.description = description;
        this.functionSignature = functionSignature;
        this.inputSchema = inputSchema;
        this.outputSchema = outputSchema;
        this.examples = examples;
        this.tags = tags;
        this.updatedAt = LocalDateTime.now();
    }
}
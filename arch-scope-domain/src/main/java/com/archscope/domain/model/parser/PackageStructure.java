package com.archscope.domain.model.parser;

import lombok.Data;

import java.util.HashSet;
import java.util.Set;
import java.util.TreeSet;

/**
 * 包结构模型
 */
@Data
public class PackageStructure {
    /**
     * 根包名
     */
    private String rootPackage;
    
    /**
     * 所有包的集合
     */
    private Set<String> allPackages = new TreeSet<>();
    
    /**
     * 添加包
     *
     * @param packageName 包名
     */
    public void addPackage(String packageName) {
        allPackages.add(packageName);
        
        // 更新根包（找到共同的前缀）
        if (rootPackage == null) {
            rootPackage = packageName;
        } else {
            rootPackage = findCommonPrefix(rootPackage, packageName);
        }
    }
    
    /**
     * 查找两个包名的共同前缀
     *
     * @param pkg1 包名1
     * @param pkg2 包名2
     * @return 共同前缀
     */
    private String findCommonPrefix(String pkg1, String pkg2) {
        String[] parts1 = pkg1.split("\\.");
        String[] parts2 = pkg2.split("\\.");
        
        StringBuilder prefix = new StringBuilder();
        int i = 0;
        
        while (i < parts1.length && i < parts2.length && parts1[i].equals(parts2[i])) {
            if (prefix.length() > 0) {
                prefix.append(".");
            }
            prefix.append(parts1[i]);
            i++;
        }
        
        return prefix.toString();
    }
    
    /**
     * 获取指定包的子包
     *
     * @param packageName 包名
     * @return 子包集合
     */
    public Set<String> getSubPackages(String packageName) {
        Set<String> subPackages = new HashSet<>();
        
        for (String pkg : allPackages) {
            if (pkg.startsWith(packageName + ".")) {
                subPackages.add(pkg);
            }
        }
        
        return subPackages;
    }
    
    /**
     * 获取指定包的直接子包
     *
     * @param packageName 包名
     * @return 直接子包集合
     */
    public Set<String> getDirectSubPackages(String packageName) {
        Set<String> directSubPackages = new HashSet<>();
        
        for (String pkg : allPackages) {
            if (pkg.startsWith(packageName + ".")) {
                String remaining = pkg.substring(packageName.length() + 1);
                int dotIndex = remaining.indexOf('.');
                
                if (dotIndex == -1) {
                    // 直接子包
                    directSubPackages.add(pkg);
                } else {
                    // 添加第一级子包
                    directSubPackages.add(packageName + "." + remaining.substring(0, dotIndex));
                }
            }
        }
        
        return directSubPackages;
    }
} 
package com.archscope.domain.model.parser;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 传统解析器注册表
 * 管理不同语言的传统解析器
 */
@Slf4j
@Component
public class TraditionalParserRegistry {
    
    private final Map<LanguageType, TraditionalCodeParser> parsers = new HashMap<>();
    
    public TraditionalParserRegistry(List<TraditionalCodeParser> parserList) {
        // 注册所有传统解析器
        for (TraditionalCodeParser parser : parserList) {
            LanguageType supportedLanguage = parser.getSupportedLanguage();
            parsers.put(supportedLanguage, parser);
            log.info("注册传统解析器: {} 用于语言: {}", parser.getClass().getSimpleName(), supportedLanguage);
        }
    }
    
    /**
     * 获取指定语言的传统解析器
     *
     * @param languageType 语言类型
     * @return 传统解析器，如果不存在则返回null
     */
    public TraditionalCodeParser getParser(LanguageType languageType) {
        return parsers.get(languageType);
    }
    
    /**
     * 检查是否支持指定语言
     *
     * @param languageType 语言类型
     * @return 是否支持
     */
    public boolean supportsLanguage(LanguageType languageType) {
        return parsers.containsKey(languageType);
    }
    
    /**
     * 获取所有支持的语言类型
     *
     * @return 支持的语言类型列表
     */
    public List<LanguageType> getSupportedLanguages() {
        return new ArrayList<>(parsers.keySet());
    }
}

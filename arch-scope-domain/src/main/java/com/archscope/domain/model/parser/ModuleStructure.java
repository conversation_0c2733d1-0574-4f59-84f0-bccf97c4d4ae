package com.archscope.domain.model.parser;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 模块结构模型
 */
@Data
public class ModuleStructure {
    /**
     * 模块列表
     */
    private List<Module> modules = new ArrayList<>();
    
    /**
     * 添加模块
     *
     * @param module 模块
     */
    public void addModule(Module module) {
        modules.add(module);
    }
    
    /**
     * 根据名称查找模块
     *
     * @param name 模块名称
     * @return 模块，如果不存在则返回null
     */
    public Module findModuleByName(String name) {
        for (Module module : modules) {
            if (module.getName().equals(name)) {
                return module;
            }
        }
        return null;
    }
    
    /**
     * 根据包名查找模块
     *
     * @param packageName 包名
     * @return 包含该包的模块，如果不存在则返回null
     */
    public Module findModuleByPackage(String packageName) {
        for (Module module : modules) {
            if (module.getPackages().contains(packageName)) {
                return module;
            }
        }
        return null;
    }
    
    /**
     * 分析模块间的依赖关系
     */
    public void analyzeDependencies() {
        for (Module module : modules) {
            for (ClassDefinition classDef : module.getClasses()) {
                // 分析类的依赖关系
                for (DependencyRelation dependency : classDef.getDependencies()) {
                    String targetClass = dependency.getTargetClass();
                    Module targetModule = findModuleContainingClass(targetClass);
                    
                    if (targetModule != null && !targetModule.equals(module)) {
                        module.addDependency(targetModule);
                    }
                }
            }
        }
    }
    
    /**
     * 查找包含指定类的模块
     *
     * @param className 完全限定类名
     * @return 包含该类的模块，如果不存在则返回null
     */
    private Module findModuleContainingClass(String className) {
        for (Module module : modules) {
            for (ClassDefinition classDef : module.getClasses()) {
                if (classDef.getFullyQualifiedName().equals(className)) {
                    return module;
                }
            }
        }
        return null;
    }
}
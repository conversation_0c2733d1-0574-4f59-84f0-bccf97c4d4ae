package com.archscope.domain.model.parser;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 架构层级模型
 */
@Data
@Builder
public class ArchitecturalLayer {
    /**
     * 层级名称
     */
    private String name;
    
    /**
     * 层级描述
     */
    private String description;
    
    /**
     * 层级包含的包
     */
    @Builder.Default
    private List<String> packages = new ArrayList<>();
    
    /**
     * 层级包含的类
     */
    @Builder.Default
    private List<ClassDefinition> classes = new ArrayList<>();
    
    /**
     * 依赖的其他层级
     */
    @Builder.Default
    private List<ArchitecturalLayer> dependencies = new ArrayList<>();
    
    /**
     * 添加包到层级
     *
     * @param packageName 包名
     */
    public void addPackage(String packageName) {
        packages.add(packageName);
    }
    
    /**
     * 添加类到层级
     *
     * @param classDefinition 类定义
     */
    public void addClass(ClassDefinition classDefinition) {
        classes.add(classDefinition);
    }
    
    /**
     * 添加依赖层级
     *
     * @param layer 依赖的层级
     */
    public void addDependency(ArchitecturalLayer layer) {
        if (!dependencies.contains(layer) && !layer.equals(this)) {
            dependencies.add(layer);
        }
    }
}
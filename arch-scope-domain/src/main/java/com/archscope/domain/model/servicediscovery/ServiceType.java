package com.archscope.domain.model.servicediscovery;

/**
 * 服务类型枚举，表示服务的类型
 */
public enum ServiceType {
    /**
     * REST API服务
     */
    REST_API,
    
    /**
     * SOAP Web服务
     */
    SOAP_WEB_SERVICE,
    
    /**
     * gRPC服务
     */
    GRPC,
    
    /**
     * 消息队列服务
     */
    MESSAGE_QUEUE,
    
    /**
     * 数据库服务
     */
    DATABASE,
    
    /**
     * 缓存服务
     */
    CACHE,
    
    /**
     * 存储服务
     */
    STORAGE,
    
    /**
     * 认证授权服务
     */
    AUTH,
    
    /**
     * 其他类型服务
     */
    OTHER;
}
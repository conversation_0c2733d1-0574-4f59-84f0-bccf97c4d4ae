package com.archscope.domain.model.servicediscovery;

import com.archscope.domain.valueobject.RequirementId;

import java.time.Instant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 需求实体，表示对服务能力的需求
 */
public class Requirement {
    private final RequirementId id;
    private String description;
    private final List<String> requiredCapabilities;
    private RequirementPriority priority;
    private final Instant createdAt;
    private Instant lastUpdatedAt;

    private Requirement(RequirementId id, String description, List<String> requiredCapabilities,
                       RequirementPriority priority, Instant createdAt, Instant lastUpdatedAt) {
        this.id = id;
        this.description = description;
        this.requiredCapabilities = new ArrayList<>(requiredCapabilities);
        this.priority = priority;
        this.createdAt = createdAt;
        this.lastUpdatedAt = lastUpdatedAt;
    }

    public static Requirement create(String description, List<String> requiredCapabilities, RequirementPriority priority) {
        return new Requirement(
                RequirementId.createNew(),
                description,
                requiredCapabilities != null ? requiredCapabilities : Collections.emptyList(),
                priority != null ? priority : RequirementPriority.MEDIUM,
                Instant.now(),
                Instant.now()
        );
    }

    public static Requirement restore(RequirementId id, String description, List<String> requiredCapabilities,
                                     RequirementPriority priority, Instant createdAt, Instant lastUpdatedAt) {
        return new Requirement(
                id,
                description,
                requiredCapabilities != null ? requiredCapabilities : Collections.emptyList(),
                priority != null ? priority : RequirementPriority.MEDIUM,
                createdAt,
                lastUpdatedAt
        );
    }

    public void update(String description, List<String> requiredCapabilities, RequirementPriority priority) {
        this.description = description;
        if (requiredCapabilities != null) {
            this.requiredCapabilities.clear();
            this.requiredCapabilities.addAll(requiredCapabilities);
        }
        this.priority = priority;
        this.lastUpdatedAt = Instant.now();
    }

    public void addRequiredCapability(String capability) {
        if (!this.requiredCapabilities.contains(capability)) {
            this.requiredCapabilities.add(capability);
            this.lastUpdatedAt = Instant.now();
        }
    }

    public void removeRequiredCapability(String capability) {
        if (this.requiredCapabilities.remove(capability)) {
            this.lastUpdatedAt = Instant.now();
        }
    }

    public RequirementId getId() {
        return id;
    }

    public String getDescription() {
        return description;
    }

    public List<String> getRequiredCapabilities() {
        return Collections.unmodifiableList(requiredCapabilities);
    }

    public RequirementPriority getPriority() {
        return priority;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public Instant getLastUpdatedAt() {
        return lastUpdatedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Requirement that = (Requirement) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Requirement{" +
                "id=" + id +
                ", description='" + description + '\'' +
                ", requiredCapabilities=" + requiredCapabilities +
                ", priority=" + priority +
                '}';
    }
}
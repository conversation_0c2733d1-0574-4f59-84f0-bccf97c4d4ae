package com.archscope.domain.model.parser;

import java.io.File;
import java.io.IOException;
import java.util.List;

/**
 * 代码解析器接口
 */
public interface CodeParser {
    
    /**
     * 解析文件内容
     *
     * @param filename 文件名
     * @param content 文件内容
     * @return 解析结果
     */
    FileParseResult parseFile(String filename, String content);
    
    /**
     * 解析文件
     *
     * @param file 文件对象
     * @return 解析结果
     * @throws IOException 如果文件读取失败
     */
    FileParseResult parseFile(File file) throws IOException;
    
    /**
     * 解析目录中的所有文件
     *
     * @param directory 目录
     * @return 解析结果列表
     * @throws IOException 如果目录读取失败
     */
    List<FileParseResult> parseDirectory(File directory) throws IOException;
    
    /**
     * 解析目录中的所有文件
     *
     * @param directoryPath 目录路径
     * @return 解析结果列表
     * @throws IOException 如果目录读取失败
     */
    List<FileParseResult> parseDirectory(String directoryPath) throws IOException;
    
    /**
     * 识别文件的语言类型
     *
     * @param filename 文件名
     * @return 语言类型
     */
    LanguageType identifyLanguage(String filename);
    
    /**
     * 分析解析结果中的依赖关系
     *
     * @param parseResults 解析结果列表
     * @return 依赖关系列表
     */
    List<DependencyRelation> analyzeDependencies(List<FileParseResult> parseResults);
} 
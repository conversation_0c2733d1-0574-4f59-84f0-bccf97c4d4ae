package com.archscope.domain.model.parser;

import lombok.Builder;
import lombok.Data;

/**
 * 依赖关系模型
 */
@Data
@Builder
public class DependencyRelation {
    /**
     * 源类
     */
    private String sourceClass;
    
    /**
     * 目标类
     */
    private String targetClass;
    
    /**
     * 依赖类型
     */
    private DependencyType type;
    
    /**
     * 依赖强度（1-10）
     */
    private int strength;
    
    /**
     * 依赖出现的位置（字段、方法参数、返回值等）
     */
    private String location;
} 
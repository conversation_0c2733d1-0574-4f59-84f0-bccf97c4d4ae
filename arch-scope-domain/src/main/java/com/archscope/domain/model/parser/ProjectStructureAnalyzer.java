package com.archscope.domain.model.parser;

import java.util.List;

/**
 * 项目结构分析器接口
 */
public interface ProjectStructureAnalyzer {
    
    /**
     * 分析包结构
     *
     * @param parseResults 文件解析结果列表
     * @return 包结构
     */
    PackageStructure analyzePackageStructure(List<FileParseResult> parseResults);
    
    /**
     * 分析模块结构
     *
     * @param packageStructure 包结构
     * @return 模块结构
     */
    ModuleStructure analyzeModuleStructure(PackageStructure packageStructure);
    
    /**
     * 分析架构层级
     *
     * @param parseResults 文件解析结果列表
     * @return 架构层级
     */
    ArchitecturalLayers analyzeArchitecturalLayers(List<FileParseResult> parseResults);
}
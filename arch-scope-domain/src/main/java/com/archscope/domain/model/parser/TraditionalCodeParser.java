package com.archscope.domain.model.parser;

import java.io.File;
import java.io.IOException;

/**
 * 传统代码解析器接口
 * 定义与CodeParser接口类似的方法，专注于使用传统解析工具解析代码
 */
public interface TraditionalCodeParser {
    
    /**
     * 解析文件内容
     *
     * @param filename 文件名
     * @param content 文件内容
     * @return 解析结果
     */
    FileParseResult parseFile(String filename, String content);
    
    /**
     * 解析文件
     *
     * @param file 文件对象
     * @return 解析结果
     * @throws IOException 如果文件读取失败
     */
    FileParseResult parseFile(File file) throws IOException;
    
    /**
     * 获取支持的语言类型
     *
     * @return 支持的语言类型
     */
    LanguageType getSupportedLanguage();
}
package com.archscope.domain.model.servicediscovery;

import com.archscope.domain.valueobject.CapabilityId;
import com.archscope.domain.valueobject.ServiceId;
import com.archscope.domain.valueobject.Tag;
import com.archscope.domain.valueobject.Version;

import java.time.Instant;
import java.util.Collections;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * 能力实体，表示服务提供的一种能力
 */
public class Capability {
    private final CapabilityId id;
    private final ServiceId serviceId;
    private String name;
    private String description;
    private Version version;
    private final Set<CapabilityExample> examples;
    private final Set<Tag> tags;
    private boolean deprecated;
    private final Instant createdAt;
    private Instant lastUpdatedAt;

    private Capability(CapabilityId id, ServiceId serviceId, String name, String description, 
                      Version version, Set<CapabilityExample> examples, Set<Tag> tags, 
                      boolean deprecated, Instant createdAt, Instant lastUpdatedAt) {
        this.id = id;
        this.serviceId = serviceId;
        this.name = name;
        this.description = description;
        this.version = version;
        this.examples = new HashSet<>(examples);
        this.tags = new HashSet<>(tags);
        this.deprecated = deprecated;
        this.createdAt = createdAt;
        this.lastUpdatedAt = lastUpdatedAt;
    }

    public static Capability create(ServiceId serviceId, String name, String description, 
                                   Version version, Set<CapabilityExample> examples, Set<Tag> tags) {
        return new Capability(
                CapabilityId.createNew(),
                serviceId,
                name,
                description,
                version,
                examples != null ? examples : Collections.emptySet(),
                tags != null ? tags : Collections.emptySet(),
                false,
                Instant.now(),
                Instant.now()
        );
    }

    public static Capability restore(CapabilityId id, ServiceId serviceId, String name, String description, 
                                    Version version, Set<CapabilityExample> examples, Set<Tag> tags, 
                                    boolean deprecated, Instant createdAt, Instant lastUpdatedAt) {
        return new Capability(
                id,
                serviceId,
                name,
                description,
                version,
                examples != null ? examples : Collections.emptySet(),
                tags != null ? tags : Collections.emptySet(),
                deprecated,
                createdAt,
                lastUpdatedAt
        );
    }

    public void update(String name, String description, Version version, Set<Tag> tags) {
        this.name = name;
        this.description = description;
        this.version = version;
        if (tags != null) {
            this.tags.clear();
            this.tags.addAll(tags);
        }
        this.lastUpdatedAt = Instant.now();
    }

    public void addExample(CapabilityExample example) {
        this.examples.add(example);
        this.lastUpdatedAt = Instant.now();
    }

    public void removeExample(CapabilityExample example) {
        this.examples.remove(example);
        this.lastUpdatedAt = Instant.now();
    }

    public void markAsDeprecated() {
        this.deprecated = true;
        this.lastUpdatedAt = Instant.now();
    }

    public void markAsNotDeprecated() {
        this.deprecated = false;
        this.lastUpdatedAt = Instant.now();
    }

    public CapabilityId getId() {
        return id;
    }

    public ServiceId getServiceId() {
        return serviceId;
    }

    public String getName() {
        return name;
    }

    public String getDescription() {
        return description;
    }

    public Version getVersion() {
        return version;
    }

    public Set<CapabilityExample> getExamples() {
        return Collections.unmodifiableSet(examples);
    }

    public Set<Tag> getTags() {
        return Collections.unmodifiableSet(tags);
    }

    public boolean isDeprecated() {
        return deprecated;
    }

    public Instant getCreatedAt() {
        return createdAt;
    }

    public Instant getLastUpdatedAt() {
        return lastUpdatedAt;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Capability that = (Capability) o;
        return Objects.equals(id, that.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    @Override
    public String toString() {
        return "Capability{" +
                "id=" + id +
                ", serviceId=" + serviceId +
                ", name='" + name + '\'' +
                ", version=" + version +
                ", deprecated=" + deprecated +
                '}';
    }
}
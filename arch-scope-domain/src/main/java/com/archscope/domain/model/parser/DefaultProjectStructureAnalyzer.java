package com.archscope.domain.model.parser;

import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 项目结构分析器默认实现
 */
@RequiredArgsConstructor
public class DefaultProjectStructureAnalyzer implements ProjectStructureAnalyzer {
    
    private final CodeParser codeParser;
    
    // 常见的架构层级包名模式
    private static final Map<String, String> LAYER_PATTERNS = new HashMap<>();
    
    static {
        // 控制器层
        LAYER_PATTERNS.put("Controller Layer", ".*\\.(controller|controllers|web|rest|api)(\\..*)?$");
        // 服务层
        LAYER_PATTERNS.put("Service Layer", ".*\\.(service|services)(\\..*)?$");
        // 仓储层
        LAYER_PATTERNS.put("Repository Layer", ".*\\.(repository|repositories|dao|persistence)(\\..*)?$");
        // 模型层
        LAYER_PATTERNS.put("Model Layer", ".*\\.(model|models|domain|entity|entities)(\\..*)?$");
        // 配置层
        LAYER_PATTERNS.put("Config Layer", ".*\\.(config|configuration|configs)(\\..*)?$");
        // 工具层
        LAYER_PATTERNS.put("Utility Layer", ".*\\.(util|utils|utility|utilities|helper|helpers)(\\..*)?$");
    }
    
    @Override
    public PackageStructure analyzePackageStructure(List<FileParseResult> parseResults) {
        PackageStructure packageStructure = new PackageStructure();
        
        for (FileParseResult result : parseResults) {
            String packageName = result.getPackageName();
            if (packageName != null && !packageName.isEmpty()) {
                packageStructure.addPackage(packageName);
            }
        }
        
        return packageStructure;
    }
    
    @Override
    public ModuleStructure analyzeModuleStructure(PackageStructure packageStructure) {
        ModuleStructure moduleStructure = new ModuleStructure();
        
        // 基于包名模式识别模块
        for (String packageName : packageStructure.getAllPackages()) {
            // 提取模块名（通常是包名的第三级）
            String[] parts = packageName.split("\\.");
            if (parts.length >= 3) {
                String moduleName = parts[2];
                
                // 查找或创建模块
                Module module = moduleStructure.findModuleByName(moduleName);
                if (module == null) {
                    module = Module.builder()
                            .name(moduleName)
                            .type(determineModuleType(moduleName))
                            .build();
                    moduleStructure.addModule(module);
                }
                
                // 将包添加到模块
                module.addPackage(packageName);
            }
        }
        
        return moduleStructure;
    }
    
    @Override
    public ArchitecturalLayers analyzeArchitecturalLayers(List<FileParseResult> parseResults) {
        ArchitecturalLayers layers = new ArchitecturalLayers();
        
        // 创建架构层级
        for (Map.Entry<String, String> entry : LAYER_PATTERNS.entrySet()) {
            String layerName = entry.getKey();
            Pattern pattern = Pattern.compile(entry.getValue());
            
            ArchitecturalLayer layer = ArchitecturalLayer.builder()
                    .name(layerName)
                    .build();
            
            // 将匹配的包和类添加到层级
            for (FileParseResult result : parseResults) {
                String packageName = result.getPackageName();
                if (packageName != null && pattern.matcher(packageName).matches()) {
                    layer.addPackage(packageName);
                    
                    // 添加类到层级
                    for (ClassDefinition classDef : result.getClassDefinitions()) {
                        layer.addClass(classDef);
                    }
                }
            }
            
            // 只添加非空的层级
            if (!layer.getPackages().isEmpty()) {
                layers.addLayer(layer);
            }
        }
        
        // 分析层级间的依赖关系
        layers.analyzeDependencies();
        
        return layers;
    }
    
    /**
     * 根据模块名确定模块类型
     *
     * @param moduleName 模块名
     * @return 模块类型
     */
    private ModuleType determineModuleType(String moduleName) {
        if (moduleName.contains("api") || moduleName.contains("controller") || moduleName.contains("rest")) {
            return ModuleType.API;
        } else if (moduleName.contains("service")) {
            return ModuleType.SERVICE;
        } else if (moduleName.contains("domain") || moduleName.contains("model") || moduleName.contains("entity")) {
            return ModuleType.DOMAIN;
        } else if (moduleName.contains("repository") || moduleName.contains("dao") || moduleName.contains("persistence")) {
            return ModuleType.INFRASTRUCTURE;
        } else if (moduleName.contains("util") || moduleName.contains("helper") || moduleName.contains("common")) {
            return ModuleType.UTILITY;
        } else if (moduleName.contains("ui") || moduleName.contains("web") || moduleName.contains("view")) {
            return ModuleType.FRONTEND;
        } else if (moduleName.contains("test")) {
            return ModuleType.TEST;
        } else {
            return ModuleType.UNKNOWN;
        }
    }
}
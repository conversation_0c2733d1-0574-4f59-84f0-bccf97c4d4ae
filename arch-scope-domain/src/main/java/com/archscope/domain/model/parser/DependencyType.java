package com.archscope.domain.model.parser;

/**
 * 依赖类型枚举
 */
public enum DependencyType {
    /**
     * 继承依赖，A extends B
     */
    INHERITANCE,
    
    /**
     * 实现依赖，A implements B
     */
    IMPLEMENTATION,
    
    /**
     * 字段依赖，A has a field of type B
     */
    FIELD,
    
    /**
     * 方法返回依赖，A has a method returning B
     */
    RETURN_TYPE,
    
    /**
     * 参数依赖，A has a method with parameter of type B
     */
    PARAMETER,
    
    /**
     * 局部变量依赖，A has a method with local variable of type B
     */
    LOCAL_VARIABLE,
    
    /**
     * 方法调用依赖，A calls a method of B
     */
    METHOD_CALL,
    
    /**
     * 导入依赖，A imports B
     */
    IMPORT,
    
    /**
     * 注解依赖，A is annotated with B
     */
    ANNOTATION,
    
    /**
     * 泛型依赖，A uses B as generic parameter
     */
    GENERIC
} 
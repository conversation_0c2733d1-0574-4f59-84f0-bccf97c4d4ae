package com.archscope.domain.model.parser;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 参数定义模型
 */
@Data
@Builder
public class ParameterDefinition {
    /**
     * 参数名
     */
    private String name;
    
    /**
     * 参数类型
     */
    private String type;
    
    /**
     * 注解列表
     */
    @Builder.Default
    private List<String> annotations = new ArrayList<>();
    
    /**
     * 是否为最终参数
     */
    private boolean isFinal;
    
    /**
     * 是否为可变参数
     */
    private boolean isVarArgs;
} 
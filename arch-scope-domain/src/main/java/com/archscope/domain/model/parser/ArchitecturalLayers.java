package com.archscope.domain.model.parser;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 架构层级集合模型
 */
@Data
public class ArchitecturalLayers {
    /**
     * 层级列表
     */
    private List<ArchitecturalLayer> layers = new ArrayList<>();
    
    /**
     * 添加层级
     *
     * @param layer 层级
     */
    public void addLayer(ArchitecturalLayer layer) {
        layers.add(layer);
    }
    
    /**
     * 根据名称查找层级
     *
     * @param name 层级名称
     * @return 层级，如果不存在则返回null
     */
    public ArchitecturalLayer findLayerByName(String name) {
        for (ArchitecturalLayer layer : layers) {
            if (layer.getName().equals(name)) {
                return layer;
            }
        }
        return null;
    }
    
    /**
     * 根据包名查找层级
     *
     * @param packageName 包名
     * @return 包含该包的层级，如果不存在则返回null
     */
    public ArchitecturalLayer findLayerByPackage(String packageName) {
        for (ArchitecturalLayer layer : layers) {
            if (layer.getPackages().contains(packageName)) {
                return layer;
            }
        }
        return null;
    }
    
    /**
     * 分析层级间的依赖关系
     */
    public void analyzeDependencies() {
        for (ArchitecturalLayer layer : layers) {
            for (ClassDefinition classDef : layer.getClasses()) {
                // 分析类的依赖关系
                for (DependencyRelation dependency : classDef.getDependencies()) {
                    String targetClass = dependency.getTargetClass();
                    ArchitecturalLayer targetLayer = findLayerContainingClass(targetClass);
                    
                    if (targetLayer != null && !targetLayer.equals(layer)) {
                        layer.addDependency(targetLayer);
                    }
                }
            }
        }
    }
    
    /**
     * 查找包含指定类的层级
     *
     * @param className 完全限定类名
     * @return 包含该类的层级，如果不存在则返回null
     */
    private ArchitecturalLayer findLayerContainingClass(String className) {
        for (ArchitecturalLayer layer : layers) {
            for (ClassDefinition classDef : layer.getClasses()) {
                if (classDef.getFullyQualifiedName().equals(className)) {
                    return layer;
                }
            }
        }
        return null;
    }
}
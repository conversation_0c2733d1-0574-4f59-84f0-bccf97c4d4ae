package com.archscope.domain.model.parser;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 模块模型
 */
@Data
@Builder
public class Module {
    /**
     * 模块名称
     */
    private String name;
    
    /**
     * 模块描述
     */
    private String description;
    
    /**
     * 模块类型
     */
    private ModuleType type;
    
    /**
     * 所属包集合
     */
    @Builder.Default
    private Set<String> packages = new HashSet<>();
    
    /**
     * 模块包含的类定义
     */
    @Builder.Default
    private List<ClassDefinition> classes = new ArrayList<>();
    
    /**
     * 依赖的其他模块
     */
    @Builder.Default
    private List<Module> dependencies = new ArrayList<>();
    
    /**
     * 对该模块有依赖的模块
     */
    @Builder.Default
    private List<Module> dependents = new ArrayList<>();
    
    /**
     * 添加包到模块
     *
     * @param packageName 包名
     */
    public void addPackage(String packageName) {
        packages.add(packageName);
    }
    
    /**
     * 添加类定义到模块
     *
     * @param classDefinition 类定义
     */
    public void addClass(ClassDefinition classDefinition) {
        classes.add(classDefinition);
    }
    
    /**
     * 添加依赖模块
     *
     * @param dependency 依赖模块
     */
    public void addDependency(Module dependency) {
        if (!dependencies.contains(dependency) && !dependency.equals(this)) {
            dependencies.add(dependency);
            dependency.addDependent(this);
        }
    }
    
    /**
     * 添加依赖当前模块的模块
     *
     * @param dependent 依赖当前模块的模块
     */
    public void addDependent(Module dependent) {
        if (!dependents.contains(dependent) && !dependent.equals(this)) {
            dependents.add(dependent);
        }
    }
} 
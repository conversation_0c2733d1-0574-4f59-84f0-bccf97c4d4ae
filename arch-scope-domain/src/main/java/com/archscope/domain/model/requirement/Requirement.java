package com.archscope.domain.model.requirement;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

public class Requirement {
    private String requirementId;
    private String title;
    private String description;
    private String submittedBy;
    private String relatedServiceId;
    private List<String> relatedQueryLogIds;
    private RequirementPriority priority;
    private RequirementStatus status;
    private String feedback;
    private List<String> tags;
    private boolean createdFromSuggestion;
    private String originalSuggestionId;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    
    // 构造函数
    public Requirement(String title, String description, String submittedBy, 
                      String relatedServiceId, RequirementPriority priority, List<String> tags) {
        this.requirementId = UUID.randomUUID().toString();
        this.title = title;
        this.description = description;
        this.submittedBy = submittedBy;
        this.relatedServiceId = relatedServiceId;
        this.priority = priority;
        this.tags = tags;
        this.status = RequirementStatus.NEW;
        this.createdFromSuggestion = false;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // 从建议创建需求的构造函数
    public Requirement(String title, String description, String submittedBy, 
                      String relatedServiceId, RequirementPriority priority, List<String> tags,
                      String originalSuggestionId) {
        this(title, description, submittedBy, relatedServiceId, priority, tags);
        this.createdFromSuggestion = true;
        this.originalSuggestionId = originalSuggestionId;
    }
    
    // Getters and setters
    public String getRequirementId() {
        return requirementId;
    }

    public void setRequirementId(String requirementId) {
        this.requirementId = requirementId;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getSubmittedBy() {
        return submittedBy;
    }

    public void setSubmittedBy(String submittedBy) {
        this.submittedBy = submittedBy;
    }

    public String getRelatedServiceId() {
        return relatedServiceId;
    }

    public void setRelatedServiceId(String relatedServiceId) {
        this.relatedServiceId = relatedServiceId;
    }

    public List<String> getRelatedQueryLogIds() {
        return relatedQueryLogIds;
    }

    public void setRelatedQueryLogIds(List<String> relatedQueryLogIds) {
        this.relatedQueryLogIds = relatedQueryLogIds;
    }

    public RequirementPriority getPriority() {
        return priority;
    }

    public void setPriority(RequirementPriority priority) {
        this.priority = priority;
    }

    public RequirementStatus getStatus() {
        return status;
    }

    public void setStatus(RequirementStatus status) {
        this.status = status;
    }

    public String getFeedback() {
        return feedback;
    }

    public void setFeedback(String feedback) {
        this.feedback = feedback;
    }

    public List<String> getTags() {
        return tags;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public boolean isCreatedFromSuggestion() {
        return createdFromSuggestion;
    }

    public void setCreatedFromSuggestion(boolean createdFromSuggestion) {
        this.createdFromSuggestion = createdFromSuggestion;
    }

    public String getOriginalSuggestionId() {
        return originalSuggestionId;
    }

    public void setOriginalSuggestionId(String originalSuggestionId) {
        this.originalSuggestionId = originalSuggestionId;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // 业务方法
    public void updateStatus(RequirementStatus newStatus, String feedback) {
        this.status = newStatus;
        this.feedback = feedback;
        this.updatedAt = LocalDateTime.now();
    }
    
    public void linkQueryLogs(List<String> queryLogIds) {
        this.relatedQueryLogIds = queryLogIds;
        this.updatedAt = LocalDateTime.now();
    }
}
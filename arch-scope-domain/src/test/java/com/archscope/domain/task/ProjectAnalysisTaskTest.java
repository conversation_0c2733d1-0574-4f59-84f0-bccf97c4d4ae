package com.archscope.domain.task;

import com.archscope.domain.entity.Task;
import com.archscope.domain.valueobject.TaskStatus;
import com.archscope.domain.valueobject.TaskType;
import org.junit.jupiter.api.Test;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ProjectAnalysisTask 单元测试
 */
class ProjectAnalysisTaskTest {

    @Test
    void fromTask_ShouldCreateProjectAnalysisTaskSuccessfully() {
        // Given
        String taskParameters = "{\"projectId\":1,\"projectName\":\"测试项目\",\"projectType\":\"WEB_APPLICATION\",\"description\":\"测试描述\",\"repositoryUrl\":\"https://github.com/test/repo.git\",\"branch\":\"main\",\"analysisType\":\"FULL\",\"includeArchDiagrams\":true,\"outputFormat\":\"markdown\"}";
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("parameters", taskParameters);
        
        Task task = Task.builder()
                .id(1L)
                .projectId(1L)
                .taskType(TaskType.PROJECT_ANALYSIS.name())
                .status(TaskStatus.PENDING)
                .parameters(parameters)
                .createdAt(LocalDateTime.now())
                .build();

        // When
        ProjectAnalysisTask analysisTask = ProjectAnalysisTask.fromTask(task);

        // Then
        assertNotNull(analysisTask);
        assertEquals(1L, analysisTask.getProjectId());
        assertEquals("测试项目", analysisTask.getProjectName());
        assertEquals("WEB_APPLICATION", analysisTask.getProjectType());
        assertEquals("测试描述", analysisTask.getDescription());
        assertEquals("https://github.com/test/repo.git", analysisTask.getRepositoryUrl());
        assertEquals("main", analysisTask.getBranch());
        assertEquals(ProjectAnalysisTask.AnalysisType.FULL, analysisTask.getAnalysisType());
        assertTrue(analysisTask.isIncludeArchDiagrams());
        assertEquals("markdown", analysisTask.getOutputFormat());
    }

    @Test
    void fromTask_WithNullTask_ShouldThrowException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> ProjectAnalysisTask.fromTask(null)
        );
        assertEquals("Task不能为空", exception.getMessage());
    }

    @Test
    void fromTask_WithWrongTaskType_ShouldThrowException() {
        // Given
        Task task = Task.builder()
                .id(1L)
                .taskType("WRONG_TYPE")
                .build();

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> ProjectAnalysisTask.fromTask(task)
        );
        assertTrue(exception.getMessage().contains("任务类型不匹配"));
    }

    @Test
    void fromTask_WithEmptyParameters_ShouldThrowException() {
        // Given
        Task task = Task.builder()
                .id(1L)
                .taskType(TaskType.PROJECT_ANALYSIS.name())
                .parameters(new HashMap<>())
                .build();

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> ProjectAnalysisTask.fromTask(task)
        );
        assertEquals("任务参数为空", exception.getMessage());
    }

    @Test
    void validate_WithValidParameters_ShouldReturnTrue() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setProjectId(1L);
        analysisTask.setProjectName("测试项目");
        analysisTask.setRepositoryUrl("https://github.com/test/repo.git");
        analysisTask.setBranch("main");

        // When
        boolean isValid = analysisTask.validate();

        // Then
        assertTrue(isValid);
    }

    @Test
    void validate_WithInvalidProjectId_ShouldReturnFalse() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setProjectId(null);
        analysisTask.setProjectName("测试项目");
        analysisTask.setRepositoryUrl("https://github.com/test/repo.git");
        analysisTask.setBranch("main");

        // When
        boolean isValid = analysisTask.validate();

        // Then
        assertFalse(isValid);
    }

    @Test
    void validate_WithEmptyRepositoryUrl_ShouldReturnFalse() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setProjectId(1L);
        analysisTask.setProjectName("测试项目");
        analysisTask.setRepositoryUrl("");
        analysisTask.setBranch("main");

        // When
        boolean isValid = analysisTask.validate();

        // Then
        assertFalse(isValid);
    }

    @Test
    void needsCodeParsing_WithFullAnalysis_ShouldReturnTrue() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setAnalysisType(ProjectAnalysisTask.AnalysisType.FULL);

        // When & Then
        assertTrue(analysisTask.needsCodeParsing());
    }

    @Test
    void needsCodeParsing_WithCodeOnlyAnalysis_ShouldReturnTrue() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setAnalysisType(ProjectAnalysisTask.AnalysisType.CODE_ONLY);

        // When & Then
        assertTrue(analysisTask.needsCodeParsing());
    }

    @Test
    void needsCodeParsing_WithDocOnlyAnalysis_ShouldReturnFalse() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setAnalysisType(ProjectAnalysisTask.AnalysisType.DOC_ONLY);

        // When & Then
        assertFalse(analysisTask.needsCodeParsing());
    }

    @Test
    void needsDocumentGeneration_WithFullAnalysis_ShouldReturnTrue() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setAnalysisType(ProjectAnalysisTask.AnalysisType.FULL);

        // When & Then
        assertTrue(analysisTask.needsDocumentGeneration());
    }

    @Test
    void needsDocumentGeneration_WithDocOnlyAnalysis_ShouldReturnTrue() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setAnalysisType(ProjectAnalysisTask.AnalysisType.DOC_ONLY);

        // When & Then
        assertTrue(analysisTask.needsDocumentGeneration());
    }

    @Test
    void needsDocumentGeneration_WithCodeOnlyAnalysis_ShouldReturnFalse() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setAnalysisType(ProjectAnalysisTask.AnalysisType.CODE_ONLY);

        // When & Then
        assertFalse(analysisTask.needsDocumentGeneration());
    }

    @Test
    void toJson_ShouldReturnValidJsonString() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setProjectId(1L);
        analysisTask.setProjectName("测试项目");
        analysisTask.setRepositoryUrl("https://github.com/test/repo.git");
        analysisTask.setBranch("main");
        analysisTask.setAnalysisType(ProjectAnalysisTask.AnalysisType.FULL);

        // When
        String json = analysisTask.toJson();

        // Then
        assertNotNull(json);
        assertTrue(json.contains("\"projectId\":1"));
        assertTrue(json.contains("\"projectName\":\"测试项目\""));
        assertTrue(json.contains("\"repositoryUrl\":\"https://github.com/test/repo.git\""));
        assertTrue(json.contains("\"branch\":\"main\""));
        assertTrue(json.contains("\"analysisType\":\"FULL\""));
    }

    @Test
    void getTaskDescription_ShouldReturnFormattedDescription() {
        // Given
        ProjectAnalysisTask analysisTask = new ProjectAnalysisTask();
        analysisTask.setProjectName("测试项目");
        analysisTask.setAnalysisType(ProjectAnalysisTask.AnalysisType.FULL);
        analysisTask.setRepositoryUrl("https://github.com/test/repo.git");
        analysisTask.setBranch("main");

        // When
        String description = analysisTask.getTaskDescription();

        // Then
        assertNotNull(description);
        assertTrue(description.contains("测试项目"));
        assertTrue(description.contains("FULL"));
        assertTrue(description.contains("https://github.com/test/repo.git"));
        assertTrue(description.contains("main"));
    }

    @Test
    void fromTask_WithDefaultValues_ShouldSetCorrectDefaults() {
        // Given
        String taskParameters = "{\"projectId\":1,\"projectName\":\"测试项目\",\"repositoryUrl\":\"https://github.com/test/repo.git\",\"branch\":\"main\"}";
        
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("parameters", taskParameters);
        
        Task task = Task.builder()
                .id(1L)
                .taskType(TaskType.PROJECT_ANALYSIS.name())
                .parameters(parameters)
                .build();

        // When
        ProjectAnalysisTask analysisTask = ProjectAnalysisTask.fromTask(task);

        // Then
        assertEquals(ProjectAnalysisTask.AnalysisType.FULL, analysisTask.getAnalysisType());
        assertEquals("markdown", analysisTask.getOutputFormat());
    }
}

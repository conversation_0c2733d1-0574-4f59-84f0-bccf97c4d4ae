package com.archscope.domain.service.impl;

import com.archscope.domain.cache.ParseResultCacheService;
import com.archscope.domain.model.parser.ClassDefinition;
import com.archscope.domain.model.parser.CodeChunk;
import com.archscope.domain.model.parser.CodeParser;
import com.archscope.domain.model.parser.DependencyRelation;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.model.parser.LanguageType;
import com.archscope.domain.service.CodeChunkingService;
import com.archscope.domain.service.CodeRepositoryService;
import com.archscope.domain.service.IncrementalParseService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DefaultIncrementalParseServiceTest {
    
    @Mock
    private CodeParser codeParser;
    
    @Mock
    private CodeRepositoryService codeRepositoryService;
    
    @Mock
    private ParseResultCacheService parseResultCacheService;
    
    @Mock
    private CodeChunkingService codeChunkingService;
    
    private IncrementalParseService incrementalParseService;
    
    @TempDir
    Path tempDir;
    
    @BeforeEach
    void setUp() {
        incrementalParseService = new DefaultIncrementalParseService(
                codeParser,
                codeRepositoryService,
                parseResultCacheService,
                codeChunkingService
        );
    }
    
    @Test
    void testParseIncrementally_NoChangedFiles() {
        // 准备测试数据
        Long repositoryId = 1L;
        String fromCommit = "abc123";
        String toCommit = "def456";
        
        // 模拟缓存的解析结果
        List<FileParseResult> cachedResults = new ArrayList<>();
        FileParseResult cachedResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .successful(true)
                .build();
        cachedResults.add(cachedResult);
        
        // 模拟行为
        when(parseResultCacheService.getParseResults(repositoryId, fromCommit)).thenReturn(cachedResults);
        when(codeRepositoryService.getRepositoryChanges(repositoryId, fromCommit, toCommit)).thenReturn(Collections.emptyList());
        
        // 执行测试
        List<FileParseResult> results = incrementalParseService.parseIncrementally(repositoryId, fromCommit, toCommit);
        
        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(cachedResult, results.get(0));
        
        // 验证方法调用
        verify(parseResultCacheService).getParseResults(repositoryId, fromCommit);
        verify(codeRepositoryService).getRepositoryChanges(repositoryId, fromCommit, toCommit);
        verify(parseResultCacheService, never()).saveParseResults(any(), any(), any());
    }
    
    @Test
    void testParseIncrementally_WithChangedFiles() throws IOException {
        // 准备测试数据
        Long repositoryId = 1L;
        String fromCommit = "abc123";
        String toCommit = "def456";
        
        // 创建测试文件
        Path javaFile = tempDir.resolve("Example.java");
        Files.write(javaFile, "public class Example {}".getBytes(StandardCharsets.UTF_8));
        
        // 模拟缓存的解析结果
        List<FileParseResult> cachedResults = new ArrayList<>();
        FileParseResult cachedResult = FileParseResult.builder()
                .filename("OldExample.java")
                .filePath("com/example/OldExample.java")
                .languageType(LanguageType.JAVA)
                .successful(true)
                .build();
        cachedResults.add(cachedResult);
        
        // 模拟变更文件的解析结果
        FileParseResult newResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath(javaFile.toString())
                .languageType(LanguageType.JAVA)
                .successful(true)
                .build();
        
        // 模拟行为
        when(parseResultCacheService.getParseResults(repositoryId, fromCommit)).thenReturn(cachedResults);
        when(codeRepositoryService.getRepositoryChanges(repositoryId, fromCommit, toCommit)).thenReturn(Collections.singletonList(javaFile.toString()));
        when(codeParser.parseFile(any(File.class))).thenReturn(newResult);
        
        // 执行测试
        List<FileParseResult> results = incrementalParseService.parseIncrementally(repositoryId, fromCommit, toCommit);
        
        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        
        // 验证方法调用
        verify(parseResultCacheService).getParseResults(repositoryId, fromCommit);
        verify(codeRepositoryService).getRepositoryChanges(repositoryId, fromCommit, toCommit);
        verify(codeParser).parseFile(any(File.class));
        verify(parseResultCacheService).saveParseResults(eq(repositoryId), eq(toCommit), anyList());
    }
    
    @Test
    void testParseIncrementally_WithLargeChangedFiles() throws IOException {
        // 准备测试数据
        Long repositoryId = 1L;
        String fromCommit = "abc123";
        String toCommit = "def456";
        
        // 创建测试文件
        Path javaFile = tempDir.resolve("LargeExample.java");
        // 创建一个大文件（超过100KB）
        StringBuilder largeContent = new StringBuilder();
        for (int i = 0; i < 10000; i++) {
            largeContent.append("// Line ").append(i).append("\n");
        }
        largeContent.append("public class LargeExample {}\n");
        Files.write(javaFile, largeContent.toString().getBytes(StandardCharsets.UTF_8));
        
        // 模拟缓存的解析结果
        List<FileParseResult> cachedResults = new ArrayList<>();
        
        // 模拟代码块
        List<CodeChunk> chunks = new ArrayList<>();
        CodeChunk chunk = CodeChunk.builder()
                .id("LargeExample.java#LargeExample")
                .filename("LargeExample.java")
                .content("public class LargeExample {}")
                .type(com.archscope.domain.model.parser.ChunkType.CLASS)
                .context("// Context")
                .build();
        chunks.add(chunk);
        
        // 模拟代码块解析结果
        FileParseResult chunkResult = FileParseResult.builder()
                .filename("LargeExample.java")
                .filePath(javaFile.toString())
                .languageType(LanguageType.JAVA)
                .successful(true)
                .build();
        
        // 模拟行为
        when(parseResultCacheService.getParseResults(repositoryId, fromCommit)).thenReturn(cachedResults);
        when(codeRepositoryService.getRepositoryChanges(repositoryId, fromCommit, toCommit)).thenReturn(Collections.singletonList(javaFile.toString()));
        when(codeChunkingService.chunkFile(any(File.class), any(LanguageType.class))).thenReturn(chunks);
        when(codeParser.parseFile(anyString(), anyString())).thenReturn(chunkResult);
        
        // 执行测试
        List<FileParseResult> results = incrementalParseService.parseIncrementally(repositoryId, fromCommit, toCommit);
        
        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        
        // 验证方法调用
        verify(parseResultCacheService).getParseResults(repositoryId, fromCommit);
        verify(codeRepositoryService).getRepositoryChanges(repositoryId, fromCommit, toCommit);
        verify(codeChunkingService).chunkFile(any(File.class), any(LanguageType.class));
        verify(codeParser).parseFile(anyString(), anyString());
        verify(parseResultCacheService).saveParseResults(eq(repositoryId), eq(toCommit), anyList());
    }
    
    @Test
    void testMergeParseResults() {
        // 准备测试数据
        FileParseResult existingResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .imports(Arrays.asList("java.util.List", "java.util.ArrayList"))
                .classDefinitions(Collections.singletonList(
                        ClassDefinition.builder()
                                .name("Example")
                                .build()
                ))
                .dependencies(Collections.singletonList(
                        DependencyRelation.builder()
                                .sourceClass("com.example.Example")
                                .targetClass("java.util.List")
                                .build()
                ))
                .successful(true)
                .build();
        
        FileParseResult newResult = FileParseResult.builder()
                .filename("NewExample.java")
                .filePath("com/example/NewExample.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .imports(Arrays.asList("java.util.Map", "java.util.HashMap"))
                .classDefinitions(Collections.singletonList(
                        ClassDefinition.builder()
                                .name("NewExample")
                                .build()
                ))
                .dependencies(Collections.singletonList(
                        DependencyRelation.builder()
                                .sourceClass("com.example.NewExample")
                                .targetClass("java.util.Map")
                                .build()
                ))
                .successful(true)
                .build();
        
        // 执行测试
        List<FileParseResult> results = incrementalParseService.mergeParseResults(
                Collections.singletonList(existingResult),
                Collections.singletonList(newResult)
        );
        
        // 验证结果
        assertNotNull(results);
        assertEquals(2, results.size());
        assertEquals(existingResult, results.get(0));
        assertEquals(newResult, results.get(1));
    }
    
    @Test
    void testMergeParseResults_WithOverlap() {
        // 准备测试数据
        FileParseResult existingResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .imports(Arrays.asList("java.util.List", "java.util.ArrayList"))
                .classDefinitions(Collections.singletonList(
                        ClassDefinition.builder()
                                .name("Example")
                                .build()
                ))
                .dependencies(Collections.singletonList(
                        DependencyRelation.builder()
                                .sourceClass("com.example.Example")
                                .targetClass("java.util.List")
                                .build()
                ))
                .successful(true)
                .build();
        
        FileParseResult newResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .imports(Arrays.asList("java.util.Map", "java.util.HashMap"))
                .classDefinitions(Collections.singletonList(
                        ClassDefinition.builder()
                                .name("Example")
                                .build()
                ))
                .dependencies(Collections.singletonList(
                        DependencyRelation.builder()
                                .sourceClass("com.example.Example")
                                .targetClass("java.util.Map")
                                .build()
                ))
                .successful(true)
                .build();
        
        // 执行测试
        List<FileParseResult> results = incrementalParseService.mergeParseResults(
                Collections.singletonList(existingResult),
                Collections.singletonList(newResult)
        );
        
        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(newResult, results.get(0));
    }
}

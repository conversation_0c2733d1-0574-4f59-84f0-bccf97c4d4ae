package com.archscope.domain.service.impl;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.valueobject.TaskStatus;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LLM任务服务实现类单元测试
 */
@ExtendWith(MockitoExtension.class)
class LlmTaskServiceImplTest {

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private LlmTaskServiceImpl llmTaskService;

    private Task testTask;

    @BeforeEach
    void setUp() {
        testTask = Task.builder()
                .id(1L)
                .projectId(100L)
                .taskType("CODE_ANALYSIS")
                .status(TaskStatus.PENDING)
                .priority(5)
                .createdAt(LocalDateTime.now())
                .build();
    }

    @Test
    void pullNextTask_Success() {
        // 准备测试数据
        testTask.setTaskVersion(1); // 设置版本号
        when(taskRepository.findNextPendingTask()).thenReturn(Optional.of(testTask));
        when(taskRepository.lockTaskWithCAS(eq(testTask.getId()), anyString(), eq(30), eq(1)))
                .thenReturn(1);

        // 执行测试
        Optional<Task> result = llmTaskService.pullNextTask();

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(testTask.getId(), result.get().getId());
        // 注意：pullNextTask会锁定任务，所以状态可能会变为PROCESSING
        // 这里我们验证返回的是同一个任务即可

        // 验证方法调用
        verify(taskRepository).findNextPendingTask();
        verify(taskRepository).lockTaskWithCAS(eq(testTask.getId()), anyString(), eq(30), eq(1));
    }

    @Test
    void pullNextTask_NoTaskAvailable() {
        // 模拟没有可用任务
        when(taskRepository.findNextPendingTask()).thenReturn(Optional.empty());

        // 执行测试
        Optional<Task> result = llmTaskService.pullNextTask();

        // 验证结果
        assertFalse(result.isPresent());

        // 验证方法调用
        verify(taskRepository).findNextPendingTask();
    }

    @Test
    void lockTaskWithWorker_Success() {
        // 准备测试数据
        LocalDateTime timeoutAt = LocalDateTime.now().plusMinutes(30);
        when(taskRepository.updateTaskToProcessing(eq(1L), eq("worker-1"), any(LocalDateTime.class)))
                .thenReturn(1);

        // 执行测试
        boolean result = llmTaskService.lockTaskWithWorker(1L, "worker-1", 30);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(taskRepository).updateTaskToProcessing(eq(1L), eq("worker-1"), any(LocalDateTime.class));
    }

    @Test
    void lockTaskWithWorker_Failed() {
        // 模拟数据库更新失败（任务已被其他节点锁定）
        when(taskRepository.updateTaskToProcessing(eq(1L), eq("worker-1"), any(LocalDateTime.class)))
                .thenReturn(0);

        // 执行测试
        boolean result = llmTaskService.lockTaskWithWorker(1L, "worker-1", 30);

        // 验证结果
        assertFalse(result);

        // 验证方法调用
        verify(taskRepository).updateTaskToProcessing(eq(1L), eq("worker-1"), any(LocalDateTime.class));
    }

    @Test
    void completeTaskWithResult_Success() {
        // 准备测试数据
        String resultsJson = "{\"documents\":[{\"type\":\"README\",\"content\":\"test\"}]}";
        when(taskRepository.completeTaskWithResult(eq(1L), eq("COMPLETED"), eq(resultsJson), eq(600000L)))
                .thenReturn(1);

        // 执行测试
        boolean result = llmTaskService.completeTaskWithResult(1L, "COMPLETED", resultsJson, 600000L);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(taskRepository).completeTaskWithResult(1L, "COMPLETED", resultsJson, 600000L);
        // 注意：unlockTask是私有方法，这里不能直接验证
    }

    @Test
    void completeTaskWithResult_Failed() {
        // 模拟数据库更新失败
        String resultsJson = "{\"documents\":[{\"type\":\"README\",\"content\":\"test\"}]}";
        when(taskRepository.completeTaskWithResult(eq(1L), eq("COMPLETED"), eq(resultsJson), eq(600000L)))
                .thenReturn(0);

        // 执行测试
        boolean result = llmTaskService.completeTaskWithResult(1L, "COMPLETED", resultsJson, 600000L);

        // 验证结果
        assertFalse(result);

        // 验证方法调用
        verify(taskRepository).completeTaskWithResult(1L, "COMPLETED", resultsJson, 600000L);
        // 注意：unlockTask是私有方法，这里不能直接验证
    }

    @Test
    void failTaskWithError_Success() {
        // 准备测试数据
        String errorDetail = "代码解析失败：无法找到主类";
        when(taskRepository.failTaskWithError(eq(1L), eq(errorDetail), eq(300000L)))
                .thenReturn(1);

        // 执行测试
        boolean result = llmTaskService.failTaskWithError(1L, errorDetail, 300000L);

        // 验证结果
        assertTrue(result);

        // 验证方法调用
        verify(taskRepository).failTaskWithError(1L, errorDetail, 300000L);
        // 注意：unlockTask是私有方法，这里不能直接验证
    }

    @Test
    void failTaskWithError_Failed() {
        // 模拟数据库更新失败
        String errorDetail = "代码解析失败：无法找到主类";
        when(taskRepository.failTaskWithError(eq(1L), eq(errorDetail), eq(300000L)))
                .thenReturn(0);

        // 执行测试
        boolean result = llmTaskService.failTaskWithError(1L, errorDetail, 300000L);

        // 验证结果
        assertFalse(result);

        // 验证方法调用
        verify(taskRepository).failTaskWithError(1L, errorDetail, 300000L);
        // 注意：unlockTask是私有方法，这里不能直接验证
    }

    @Test
    void createLlmTask_Success() {
        // 准备测试数据
        Task savedTask = Task.builder()
                .id(2L)
                .projectId(100L)
                .taskType("CODE_ANALYSIS")
                .status(TaskStatus.PENDING)
                .priority(5)
                .createdAt(LocalDateTime.now())
                .build();

        // 模拟项目存在
        when(projectRepository.findById(100L)).thenReturn(Optional.of(mock(com.archscope.domain.entity.Project.class)));
        when(taskRepository.save(any(Task.class))).thenReturn(savedTask);

        // 模拟ObjectMapper序列化
        try {
            when(objectMapper.writeValueAsString(any())).thenReturn("{\"schemaVersion\":\"1.2\",\"repositoryInfo\":{\"cloneUrl\":\"https://github.com/test/repo.git\"}}");
        } catch (Exception e) {
            // 不应该发生
        }

        // 执行测试
        Task result = llmTaskService.createLlmTask(
                100L,
                "https://github.com/test/repo.git",
                "1234567890abcdef1234567890abcdef12345678",
                "main",
                "CODE_ANALYSIS",
                5
        );

        // 验证结果
        assertNotNull(result);
        assertEquals(2L, result.getId());
        assertEquals(100L, result.getProjectId());
        assertEquals("CODE_ANALYSIS", result.getTaskType());
        assertEquals(TaskStatus.PENDING, result.getStatus());

        // 验证方法调用
        verify(projectRepository).findById(100L);
        verify(taskRepository).save(any(Task.class));
    }

    @Test
    void getTaskInputData_Success() {
        // 准备测试数据
        String inputDataJson = "{\"schemaVersion\":\"1.2\",\"repositoryInfo\":{\"cloneUrl\":\"https://github.com/test/repo.git\"}}";
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("inputData", inputDataJson);
        Task taskWithData = Task.builder()
                .id(1L)
                .parameters(parameters)
                .build();

        when(taskRepository.findById(1L)).thenReturn(Optional.of(taskWithData));

        // 执行测试
        Optional<String> result = llmTaskService.getTaskInputData(1L);

        // 验证结果
        assertTrue(result.isPresent());
        assertEquals(inputDataJson, result.get());

        // 验证方法调用
        verify(taskRepository).findById(1L);
    }

    @Test
    void getTaskInputData_TaskNotFound() {
        // 模拟任务不存在
        when(taskRepository.findById(1L)).thenReturn(Optional.empty());

        // 执行测试
        Optional<String> result = llmTaskService.getTaskInputData(1L);

        // 验证结果
        assertFalse(result.isPresent());

        // 验证方法调用
        verify(taskRepository).findById(1L);
    }

    @Test
    void getTaskInputData_NoInputData() {
        // 准备测试数据（没有inputData参数）
        Map<String, Object> otherParameters = new HashMap<>();
        otherParameters.put("otherParam", "value");
        Task taskWithoutData = Task.builder()
                .id(1L)
                .parameters(otherParameters)
                .build();

        when(taskRepository.findById(1L)).thenReturn(Optional.of(taskWithoutData));

        // 执行测试
        Optional<String> result = llmTaskService.getTaskInputData(1L);

        // 验证结果
        assertFalse(result.isPresent());

        // 验证方法调用
        verify(taskRepository).findById(1L);
    }
}

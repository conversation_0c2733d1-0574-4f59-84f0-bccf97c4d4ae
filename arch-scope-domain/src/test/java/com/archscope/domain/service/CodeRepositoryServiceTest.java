package com.archscope.domain.service;

import com.archscope.domain.entity.CodeRepository;
import com.archscope.domain.repository.CodeRepositoryRepository;
import com.archscope.domain.valueobject.RepositoryStatus;
import com.archscope.domain.valueobject.RepositoryType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * 代码仓库领域服务接口测试
 */
public class CodeRepositoryServiceTest {

    @Mock
    private CodeRepositoryService codeRepositoryService;
    
    @Mock
    private CodeRepositoryRepository codeRepositoryRepository;
    
    private CodeRepository testRepository;
    
    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试数据
        testRepository = CodeRepository.builder()
                .id(1L)
                .name("测试仓库")
                .url("https://github.com/test/repo.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .status(RepositoryStatus.ACTIVE)
                .projectId(101L)
                .createdAt(LocalDateTime.now())
                .build();
                
        // 设置服务模拟数据
        when(codeRepositoryService.createRepository(any(CodeRepository.class))).thenReturn(testRepository);
        when(codeRepositoryService.updateRepository(any(CodeRepository.class))).thenReturn(testRepository);
        when(codeRepositoryService.findRepositoryById(1L)).thenReturn(Optional.of(testRepository));
        when(codeRepositoryService.findRepositoriesByProjectId(101L)).thenReturn(Arrays.asList(testRepository));
        when(codeRepositoryService.syncRepository(1L)).thenReturn(testRepository);
        when(codeRepositoryService.validateRepositoryConnection(any(CodeRepository.class))).thenReturn(true);
        when(codeRepositoryService.getRepositoryBranches(1L)).thenReturn(Arrays.asList("main", "develop", "feature/test"));
    }
    
    @Test
    public void testCreateRepository() {
        // Arrange
        CodeRepository newRepository = CodeRepository.builder()
                .name("新测试仓库")
                .url("https://github.com/test/new-repo.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .projectId(102L)
                .build();
        
        // Act
        CodeRepository result = codeRepositoryService.createRepository(newRepository);
        
        // Assert
        assertNotNull(result);
        assertEquals(1L, result.getId());
        verify(codeRepositoryService, times(1)).createRepository(newRepository);
    }
    
    @Test
    public void testUpdateRepository() {
        // Arrange
        CodeRepository updatedRepository = testRepository;
        updatedRepository.setName("更新后的名称");
        
        // Act
        CodeRepository result = codeRepositoryService.updateRepository(updatedRepository);
        
        // Assert
        assertNotNull(result);
        verify(codeRepositoryService, times(1)).updateRepository(updatedRepository);
    }
    
    @Test
    public void testFindRepositoryById() {
        // Act
        Optional<CodeRepository> result = codeRepositoryService.findRepositoryById(1L);
        
        // Assert
        assertTrue(result.isPresent());
        assertEquals(1L, result.get().getId());
        verify(codeRepositoryService, times(1)).findRepositoryById(1L);
    }
    
    @Test
    public void testFindRepositoriesByProjectId() {
        // Act
        List<CodeRepository> result = codeRepositoryService.findRepositoriesByProjectId(101L);
        
        // Assert
        assertEquals(1, result.size());
        assertEquals(101L, result.get(0).getProjectId());
        verify(codeRepositoryService, times(1)).findRepositoriesByProjectId(101L);
    }
    
    @Test
    public void testSyncRepository() {
        // Arrange - 设置模拟同步后的仓库，更新了lastSyncedAt
        CodeRepository syncedRepository = testRepository;
        LocalDateTime syncTime = LocalDateTime.now();
        syncedRepository.setLastSyncedAt(syncTime);
        
        when(codeRepositoryService.syncRepository(1L)).thenReturn(syncedRepository);
        
        // Act
        CodeRepository result = codeRepositoryService.syncRepository(1L);
        
        // Assert
        assertNotNull(result);
        assertNotNull(result.getLastSyncedAt());
        verify(codeRepositoryService, times(1)).syncRepository(1L);
    }
    
    @Test
    public void testValidateRepositoryConnection() {
        // Act
        boolean result = codeRepositoryService.validateRepositoryConnection(testRepository);
        
        // Assert
        assertTrue(result);
        verify(codeRepositoryService, times(1)).validateRepositoryConnection(testRepository);
    }
    
    @Test
    public void testDeleteRepository() {
        // Act
        codeRepositoryService.deleteRepository(1L);
        
        // Assert
        verify(codeRepositoryService, times(1)).deleteRepository(1L);
    }
    
    @Test
    public void testGetRepositoryBranches() {
        // Act
        List<String> branches = codeRepositoryService.getRepositoryBranches(1L);
        
        // Assert
        assertEquals(3, branches.size());
        assertTrue(branches.contains("main"));
        assertTrue(branches.contains("develop"));
        assertTrue(branches.contains("feature/test"));
        verify(codeRepositoryService, times(1)).getRepositoryBranches(1L);
    }
} 
package com.archscope.domain.service.impl;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.valueobject.TaskStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Optional;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LLM任务拉取算法测试
 * 专门测试任务拉取的核心算法和并发安全性
 */
@ExtendWith(MockitoExtension.class)
class LlmTaskPullAlgorithmTest {

    @Mock
    private TaskRepository taskRepository;

    private LlmTaskServiceImpl llmTaskService;

    @BeforeEach
    void setUp() {
        llmTaskService = new LlmTaskServiceImpl(
                taskRepository,
                null, // projectRepository
                null, // objectMapper
                null, // documentStorageService
                null  // documentVersionService
        );
    }

    @Test
    void testPullNextTask_SingleWorker_Success() {
        // Given
        Task pendingTask = createPendingTask(1L, 5);
        pendingTask.setTaskVersion(1); // 设置版本号

        when(taskRepository.findNextPendingTask()).thenReturn(Optional.of(pendingTask));
        when(taskRepository.lockTaskWithCAS(eq(1L), anyString(), eq(30), eq(1)))
                .thenReturn(1);

        // When
        Optional<Task> result = llmTaskService.pullNextTask();

        // Then
        assertTrue(result.isPresent());
        assertEquals(1L, result.get().getId());
        assertEquals(5, result.get().getPriority());

        verify(taskRepository).findNextPendingTask();
        verify(taskRepository).lockTaskWithCAS(eq(1L), anyString(), eq(30), eq(1));
    }

    @Test
    void testPullNextTask_ConcurrentWorkers_OnlyOneSucceeds() throws InterruptedException {
        // Given
        Task pendingTask = createPendingTask(1L, 5);
        pendingTask.setTaskVersion(1); // 设置版本号
        int workerCount = 5;

        // 在并发场景中，所有线程都会看到同一个任务，然后尝试锁定
        when(taskRepository.findNextPendingTask()).thenReturn(Optional.of(pendingTask));

        // 模拟只有第一个工作节点成功锁定任务
        when(taskRepository.lockTaskWithCAS(eq(1L), anyString(), eq(30), eq(1)))
                .thenReturn(1) // 第一次成功
                .thenReturn(0) // 后续都失败
                .thenReturn(0)
                .thenReturn(0)
                .thenReturn(0);

        // When
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(workerCount);
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failureCount = new AtomicInteger(0);

        ExecutorService executor = Executors.newFixedThreadPool(workerCount);

        for (int i = 0; i < workerCount; i++) {
            executor.submit(() -> {
                try {
                    startLatch.await(); // 等待所有线程准备就绪
                    Optional<Task> result = llmTaskService.pullNextTask();

                    if (result.isPresent()) {
                        successCount.incrementAndGet();
                    } else {
                        failureCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    failureCount.incrementAndGet();
                } finally {
                    finishLatch.countDown();
                }
            });
        }

        startLatch.countDown(); // 启动所有线程
        finishLatch.await(); // 等待所有线程完成

        // Then
        assertEquals(1, successCount.get(), "只有一个工作节点应该成功获取任务");
        assertEquals(workerCount - 1, failureCount.get(), "其他工作节点应该获取失败");

        // 验证所有线程都会调用findNextPendingTask
        verify(taskRepository, times(workerCount)).findNextPendingTask();
        // 在实际的并发场景中，可能只有部分线程会尝试锁定任务
        // 这取决于数据库的锁定机制（如 FOR UPDATE SKIP LOCKED）
        verify(taskRepository, atLeastOnce()).lockTaskWithCAS(eq(1L), anyString(), eq(30), eq(1));
        verify(taskRepository, atMost(workerCount)).lockTaskWithCAS(eq(1L), anyString(), eq(30), eq(1));

        executor.shutdown();
    }

    @Test
    void testPullNextTask_PriorityOrdering() {
        // Given
        
        // 模拟按优先级返回任务（高优先级先返回）
        Task highPriorityTask = createPendingTask(1L, 10);
        highPriorityTask.setTaskVersion(1);
        Task mediumPriorityTask = createPendingTask(2L, 5);
        mediumPriorityTask.setTaskVersion(1);
        Task lowPriorityTask = createPendingTask(3L, 1);
        lowPriorityTask.setTaskVersion(1);

        when(taskRepository.findNextPendingTask())
                .thenReturn(Optional.of(highPriorityTask))
                .thenReturn(Optional.of(mediumPriorityTask))
                .thenReturn(Optional.of(lowPriorityTask));

        when(taskRepository.lockTaskWithCAS(anyLong(), anyString(), eq(30), eq(1)))
                .thenReturn(1);

        // When & Then
        Optional<Task> firstTask = llmTaskService.pullNextTask();
        assertTrue(firstTask.isPresent());
        assertEquals(10, firstTask.get().getPriority(), "应该先返回高优先级任务");

        Optional<Task> secondTask = llmTaskService.pullNextTask();
        assertTrue(secondTask.isPresent());
        assertEquals(5, secondTask.get().getPriority(), "然后返回中优先级任务");

        Optional<Task> thirdTask = llmTaskService.pullNextTask();
        assertTrue(thirdTask.isPresent());
        assertEquals(1, thirdTask.get().getPriority(), "最后返回低优先级任务");

        verify(taskRepository, times(3)).findNextPendingTask();
        verify(taskRepository, times(3)).lockTaskWithCAS(anyLong(), anyString(), eq(30), eq(1));
    }

    @Test
    void testPullNextTask_EmptyQueue() {
        // Given
        when(taskRepository.findNextPendingTask()).thenReturn(Optional.empty());

        // When
        Optional<Task> result = llmTaskService.pullNextTask();

        // Then
        assertFalse(result.isPresent());
        verify(taskRepository).findNextPendingTask();
        verify(taskRepository, never()).lockTaskWithCAS(anyLong(), anyString(), anyInt(), anyInt());
    }

    @Test
    void testPullNextTask_DatabaseException() {
        // Given
        when(taskRepository.findNextPendingTask()).thenThrow(new RuntimeException("Database connection failed"));

        // When
        Optional<Task> result = llmTaskService.pullNextTask();

        // Then - 实际代码会捕获异常并返回empty
        assertFalse(result.isPresent());
        verify(taskRepository).findNextPendingTask();
        verify(taskRepository, never()).lockTaskWithCAS(anyLong(), anyString(), anyInt(), anyInt());
    }

    @Test
    void testLockTaskWithWorker_AtomicOperation() {
        // Given
        Long taskId = 1L;
        String workerId = "worker-001";
        int timeoutMinutes = 30;

        when(taskRepository.updateTaskToProcessing(eq(taskId), eq(workerId), any(LocalDateTime.class)))
                .thenReturn(1);

        // When
        boolean result = llmTaskService.lockTaskWithWorker(taskId, workerId, timeoutMinutes);

        // Then
        assertTrue(result);
        verify(taskRepository).updateTaskToProcessing(eq(taskId), eq(workerId), any(LocalDateTime.class));
    }

    @Test
    void testLockTaskWithWorker_ConcurrentLocking() throws InterruptedException {
        // Given
        Long taskId = 1L;
        int timeoutMinutes = 30;
        int workerCount = 3;

        // 模拟只有第一个工作节点成功锁定
        when(taskRepository.updateTaskToProcessing(eq(taskId), anyString(), any(LocalDateTime.class)))
                .thenReturn(1) // 第一次成功
                .thenReturn(0) // 第二次失败
                .thenReturn(0); // 第三次失败

        // When
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch finishLatch = new CountDownLatch(workerCount);
        AtomicInteger successCount = new AtomicInteger(0);

        ExecutorService executor = Executors.newFixedThreadPool(workerCount);

        for (int i = 0; i < workerCount; i++) {
            final String workerId = "worker-" + String.format("%03d", i + 1);
            executor.submit(() -> {
                try {
                    startLatch.await();
                    boolean result = llmTaskService.lockTaskWithWorker(taskId, workerId, timeoutMinutes);
                    if (result) {
                        successCount.incrementAndGet();
                    }
                } catch (Exception e) {
                    // 忽略异常
                } finally {
                    finishLatch.countDown();
                }
            });
        }

        startLatch.countDown();
        finishLatch.await();

        // Then
        assertEquals(1, successCount.get(), "只有一个工作节点应该成功锁定任务");
        verify(taskRepository, times(workerCount)).updateTaskToProcessing(eq(taskId), anyString(), any(LocalDateTime.class));

        executor.shutdown();
    }

    /**
     * 创建一个待处理的任务
     */
    private Task createPendingTask(Long taskId, Integer priority) {
        return Task.builder()
                .id(taskId)
                .projectId(100L)
                .taskType("CODE_FULL_ANALYSIS_JAVA")
                .status(TaskStatus.PENDING)
                .priority(priority)
                .taskVersion(1)
                .createdAt(LocalDateTime.now())
                .build();
    }
}

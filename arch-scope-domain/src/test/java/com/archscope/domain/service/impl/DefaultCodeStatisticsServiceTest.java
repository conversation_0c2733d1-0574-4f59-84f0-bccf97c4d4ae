package com.archscope.domain.service.impl;

import com.archscope.domain.entity.Project;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.CodeRepositoryService;
import com.archscope.domain.valueobject.CodeStatistics;
import com.archscope.domain.model.parser.LanguageType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DefaultCodeStatisticsServiceTest {

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private CodeRepositoryService codeRepositoryService;

    @InjectMocks
    private DefaultCodeStatisticsService codeStatisticsService;

    @TempDir
    Path tempDir;

    private Project testProject;
    private List<FileParseResult> testParseResults;

    @BeforeEach
    void setUp() {
        testProject = Project.builder()
                .id(1L)
                .name("Test Project")
                .linesOfCode(0L)
                .fileCount(0)
                .contributorCount(0)
                .updatedAt(LocalDateTime.now())
                .build();

        testParseResults = Arrays.asList(
                FileParseResult.builder()
                        .filename("TestClass.java")
                        .filePath("/src/main/java/TestClass.java")
                        .languageType(LanguageType.JAVA)
                        .successful(true)
                        .build(),
                FileParseResult.builder()
                        .filename("test.js")
                        .filePath("/src/main/js/test.js")
                        .languageType(LanguageType.JAVASCRIPT)
                        .successful(true)
                        .build(),
                FileParseResult.builder()
                        .filename("failed.py")
                        .filePath("/src/main/python/failed.py")
                        .languageType(LanguageType.PYTHON)
                        .successful(false)
                        .build()
        );
    }

    @Test
    void testCalculateStatistics() throws IOException {
        // 创建测试文件
        File javaFile = createTestFile("TestClass.java", 
                "package com.test;\n" +
                "\n" +
                "// This is a test class\n" +
                "public class TestClass {\n" +
                "    private String name;\n" +
                "    \n" +
                "    public String getName() {\n" +
                "        return name;\n" +
                "    }\n" +
                "}");

        File jsFile = createTestFile("test.js", 
                "// JavaScript test file\n" +
                "function test() {\n" +
                "    console.log('test');\n" +
                "}");

        // 更新解析结果的文件路径
        testParseResults.get(0).setFilePath(javaFile.getAbsolutePath());
        testParseResults.get(1).setFilePath(jsFile.getAbsolutePath());

        // 执行统计计算
        CodeStatistics statistics = codeStatisticsService.calculateStatistics(testParseResults);

        // 验证结果
        assertNotNull(statistics);
        assertTrue(statistics.getTotalLinesOfCode() > 0);
        assertTrue(statistics.getEffectiveLinesOfCode() > 0);
        assertTrue(statistics.getCommentLines() > 0);
        assertTrue(statistics.getBlankLines() > 0);
        assertEquals(3, statistics.getTotalFileCount()); // 包括失败的文件
        assertNotNull(statistics.getFileCountByType());
        assertNotNull(statistics.getLinesOfCodeByLanguage());
    }

    @Test
    void testCalculateFileLineStatistics() throws IOException {
        // 创建测试文件
        File testFile = createTestFile("TestFile.java",
                "package com.test;\n" +
                "\n" +
                "// This is a comment\n" +
                "/* Multi-line\n" +
                "   comment */\n" +
                "public class TestFile {\n" +
                "    private String field;\n" +
                "    \n" +
                "    public void method() {\n" +
                "        // Another comment\n" +
                "        System.out.println(\"Hello\");\n" +
                "    }\n" +
                "}");

        // 执行统计
        CodeStatistics.FileLineStatistics stats = codeStatisticsService.calculateFileLineStatistics(testFile);

        // 验证结果
        assertNotNull(stats);
        assertEquals(testFile.getPath(), stats.getFilePath());
        assertEquals(13, stats.getTotalLines());
        assertTrue(stats.getEffectiveLines() > 0);
        assertTrue(stats.getCommentLines() > 0);
        assertTrue(stats.getBlankLines() > 0);
        assertEquals(testFile.length(), stats.getFileSize());
        assertEquals("JAVA", stats.getLanguageType());
    }

    @Test
    void testGetContributorCount() {
        Long repositoryId = 1L;
        int expectedCount = 5;

        when(codeRepositoryService.getContributorCount(repositoryId)).thenReturn(expectedCount);

        int actualCount = codeStatisticsService.getContributorCount(repositoryId);

        assertEquals(expectedCount, actualCount);
        verify(codeRepositoryService).getContributorCount(repositoryId);
    }

    @Test
    void testGetContributorCountWithException() {
        Long repositoryId = 1L;

        when(codeRepositoryService.getContributorCount(repositoryId))
                .thenThrow(new RuntimeException("Git error"));

        int actualCount = codeStatisticsService.getContributorCount(repositoryId);

        assertEquals(0, actualCount);
        verify(codeRepositoryService).getContributorCount(repositoryId);
    }

    @Test
    void testUpdateProjectStatistics() {
        Long projectId = 1L;
        CodeStatistics statistics = CodeStatistics.builder()
                .totalLinesOfCode(1000L)
                .effectiveLinesOfCode(800L)
                .commentLines(150L)
                .blankLines(50L)
                .totalFileCount(10)
                .contributorCount(3)
                .build();

        when(projectRepository.findById(projectId)).thenReturn(Optional.of(testProject));
        when(projectRepository.save(any(Project.class))).thenReturn(testProject);

        // 执行更新
        codeStatisticsService.updateProjectStatistics(projectId, statistics);

        // 验证项目被更新
        verify(projectRepository).findById(projectId);
        verify(projectRepository).save(any(Project.class));

        // 验证项目字段被正确设置
        assertEquals(1000L, testProject.getLinesOfCode());
        assertEquals(10, testProject.getFileCount());
        assertEquals(3, testProject.getContributorCount());
    }

    @Test
    void testUpdateProjectStatisticsWithNonExistentProject() {
        Long projectId = 999L;
        CodeStatistics statistics = CodeStatistics.empty();

        when(projectRepository.findById(projectId)).thenReturn(Optional.empty());

        // 执行更新应该抛出异常
        assertThrows(RuntimeException.class, () -> {
            codeStatisticsService.updateProjectStatistics(projectId, statistics);
        });

        verify(projectRepository).findById(projectId);
        verify(projectRepository, never()).save(any(Project.class));
    }

    @Test
    void testCalculateStatisticsFromFiles() throws IOException {
        // 创建测试文件
        File file1 = createTestFile("test1.java", "public class Test1 {\n    // comment\n}");
        File file2 = createTestFile("test2.js", "function test() {\n    return true;\n}");
        File file3 = createTestFile("test3.py", "# Python comment\nprint('hello')");

        List<File> files = Arrays.asList(file1, file2, file3);

        // 执行统计
        CodeStatistics statistics = codeStatisticsService.calculateStatisticsFromFiles(files);

        // 验证结果
        assertNotNull(statistics);
        assertTrue(statistics.getTotalLinesOfCode() > 0);
        assertEquals(3, statistics.getTotalFileCount());
        assertNotNull(statistics.getFileCountByType());
        assertNotNull(statistics.getLinesOfCodeByLanguage());
    }

    private File createTestFile(String filename, String content) throws IOException {
        File file = tempDir.resolve(filename).toFile();
        try (FileWriter writer = new FileWriter(file)) {
            writer.write(content);
        }
        return file;
    }
}

package com.archscope.domain.service;

import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.impl.ProjectServiceImpl;
import com.archscope.domain.valueobject.ProjectType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 项目服务重复注册测试
 * 测试URL标准化和重复检测功能
 */
@DisplayName("项目服务重复注册测试")
class ProjectServiceDuplicateRegistrationTest {

    @Mock
    private ProjectRepository projectRepository;

    private ProjectService projectService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        projectService = new ProjectServiceImpl(projectRepository);
    }

    @Test
    @DisplayName("注册新项目 - 成功注册")
    void registerProject_NewProject_ShouldSucceed() {
        // Given
        String name = "测试项目";
        String description = "测试描述";
        String repositoryUrl = "https://github.com/user/repo.git";
        String branch = "main";

        when(projectRepository.findByNormalizedRepositoryUrl(anyString())).thenReturn(Optional.empty());
        when(projectRepository.save(any(Project.class))).thenAnswer(invocation -> {
            Project project = invocation.getArgument(0);
            // 模拟数据库生成ID
            return Project.builder()
                    .id(1L)
                    .name(project.getName())
                    .description(project.getDescription())
                    .repositoryUrl(project.getRepositoryUrl())
                    .normalizedRepositoryUrl(project.getNormalizedRepositoryUrl())
                    .branch(project.getBranch())
                    .createdAt(project.getCreatedAt())
                    .updatedAt(project.getUpdatedAt())
                    .creatorId(project.getCreatorId())
                    .status(project.getStatus())
                    .active(project.getActive())
                    .analysisCount(project.getAnalysisCount())
                    .documentationVersion(project.getDocumentationVersion())
                    .type(project.getType())
                    .build();
        });

        // When
        Project result = projectService.registerProject(name, description, repositoryUrl, branch);

        // Then
        assertNotNull(result);
        assertEquals(name, result.getName());
        assertEquals(description, result.getDescription());
        assertEquals(repositoryUrl, result.getRepositoryUrl());
        assertEquals("https://github.com/user/repo", result.getNormalizedRepositoryUrl());
        assertEquals(branch, result.getBranch());
        assertEquals("PENDING_ANALYSIS", result.getStatus());
        assertTrue(result.getActive());

        verify(projectRepository).findByNormalizedRepositoryUrl("https://github.com/user/repo");
        verify(projectRepository).save(any(Project.class));
    }

    @ParameterizedTest
    @DisplayName("防止重复注册 - 不同格式的相同仓库URL")
    @ValueSource(strings = {
        "https://gitlab.yeepay.com/yop/yop-center.git",
        "https://gitlab.yeepay.com/yop/yop-center",
        "https://gitlab.yeepay.com/yop/yop-center/",
        "http://gitlab.yeepay.com/yop/yop-center.git",
        "http://gitlab.yeepay.com/yop/yop-center",
        "http://gitlab.yeepay.com/yop/yop-center/"
    })
    void registerProject_DuplicateRepository_ShouldThrowException(String repositoryUrl) {
        // Given
        String name = "新项目";
        String description = "新项目描述";
        String branch = "main";

        // 模拟已存在的项目
        Project existingProject = Project.builder()
                .id(1L)
                .name("现有项目")
                .description("现有项目描述")
                .repositoryUrl("https://gitlab.yeepay.com/yop/yop-center.git")
                .normalizedRepositoryUrl("https://gitlab.yeepay.com/yop/yop-center")
                .branch("main")
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .creatorId(798L)
                .status("PENDING_ANALYSIS")
                .active(true)
                .analysisCount(0)
                .documentationVersion(0)
                .type(ProjectType.OTHER)
                .build();

        when(projectRepository.findByNormalizedRepositoryUrl("https://gitlab.yeepay.com/yop/yop-center"))
                .thenReturn(Optional.of(existingProject));

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> projectService.registerProject(name, description, repositoryUrl, branch)
        );

        assertTrue(exception.getMessage().contains("该仓库已被项目"));
        assertTrue(exception.getMessage().contains("现有项目"));
        assertTrue(exception.getMessage().contains("注册使用"));

        verify(projectRepository).findByNormalizedRepositoryUrl("https://gitlab.yeepay.com/yop/yop-center");
        verify(projectRepository, never()).save(any(Project.class));
    }

    @Test
    @DisplayName("注册项目 - 空仓库URL应抛出异常")
    void registerProject_EmptyRepositoryUrl_ShouldThrowException() {
        // Given
        String name = "测试项目";
        String description = "测试描述";
        String repositoryUrl = "";
        String branch = "main";

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> projectService.registerProject(name, description, repositoryUrl, branch)
        );

        assertEquals("仓库地址不能为空", exception.getMessage());
        verify(projectRepository, never()).findByNormalizedRepositoryUrl(anyString());
        verify(projectRepository, never()).save(any(Project.class));
    }

    @Test
    @DisplayName("注册项目 - 空项目名称应抛出异常")
    void registerProject_EmptyProjectName_ShouldThrowException() {
        // Given
        String name = "";
        String description = "测试描述";
        String repositoryUrl = "https://github.com/user/repo.git";
        String branch = "main";

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> projectService.registerProject(name, description, repositoryUrl, branch)
        );

        assertEquals("项目名称不能为空", exception.getMessage());
        verify(projectRepository, never()).findByNormalizedRepositoryUrl(anyString());
        verify(projectRepository, never()).save(any(Project.class));
    }

    @Test
    @DisplayName("注册项目 - 无效URL格式应抛出异常")
    void registerProject_InvalidUrlFormat_ShouldThrowException() {
        // Given
        String name = "测试项目";
        String description = "测试描述";
        String repositoryUrl = "invalid-url";
        String branch = "main";

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> projectService.registerProject(name, description, repositoryUrl, branch)
        );

        assertEquals("仓库地址格式不正确，请提供有效的Git仓库URL", exception.getMessage());
        verify(projectRepository, never()).findByNormalizedRepositoryUrl(anyString());
        verify(projectRepository, never()).save(any(Project.class));
    }

    @Test
    @DisplayName("注册项目 - SSH URL应正确标准化")
    void registerProject_SshUrl_ShouldNormalizeCorrectly() {
        // Given
        String name = "测试项目";
        String description = "测试描述";
        String repositoryUrl = "**************:user/repo.git";
        String branch = "main";

        when(projectRepository.findByNormalizedRepositoryUrl(anyString())).thenReturn(Optional.empty());
        when(projectRepository.save(any(Project.class))).thenAnswer(invocation -> {
            Project project = invocation.getArgument(0);
            return Project.builder()
                    .id(1L)
                    .name(project.getName())
                    .description(project.getDescription())
                    .repositoryUrl(project.getRepositoryUrl())
                    .normalizedRepositoryUrl(project.getNormalizedRepositoryUrl())
                    .branch(project.getBranch())
                    .createdAt(project.getCreatedAt())
                    .updatedAt(project.getUpdatedAt())
                    .creatorId(project.getCreatorId())
                    .status(project.getStatus())
                    .active(project.getActive())
                    .analysisCount(project.getAnalysisCount())
                    .documentationVersion(project.getDocumentationVersion())
                    .type(project.getType())
                    .build();
        });

        // When
        Project result = projectService.registerProject(name, description, repositoryUrl, branch);

        // Then
        assertNotNull(result);
        assertEquals(repositoryUrl, result.getRepositoryUrl()); // 保持原始URL
        assertEquals("https://github.com/user/repo", result.getNormalizedRepositoryUrl()); // 标准化URL

        verify(projectRepository).findByNormalizedRepositoryUrl("https://github.com/user/repo");
        verify(projectRepository).save(any(Project.class));
    }

    @Test
    @DisplayName("注册项目 - 默认分支应为main")
    void registerProject_NullBranch_ShouldDefaultToMain() {
        // Given
        String name = "测试项目";
        String description = "测试描述";
        String repositoryUrl = "https://github.com/user/repo.git";
        String branch = null;

        when(projectRepository.findByNormalizedRepositoryUrl(anyString())).thenReturn(Optional.empty());
        when(projectRepository.save(any(Project.class))).thenAnswer(invocation -> invocation.getArgument(0));

        // When
        Project result = projectService.registerProject(name, description, repositoryUrl, branch);

        // Then
        assertEquals("main", result.getBranch());
    }
}

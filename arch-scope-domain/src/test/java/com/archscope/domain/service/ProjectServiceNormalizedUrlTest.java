package com.archscope.domain.service;

import com.archscope.domain.entity.Project;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.impl.ProjectServiceImpl;
import com.archscope.domain.util.GitUrlNormalizer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * 项目服务标准化URL测试
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("项目服务标准化URL测试")
class ProjectServiceNormalizedUrlTest {

    @Mock
    private ProjectRepository projectRepository;

    private ProjectService projectService;

    @BeforeEach
    void setUp() {
        projectService = new ProjectServiceImpl(projectRepository);
    }

    @Test
    @DisplayName("注册项目时应该正确设置标准化URL")
    void registerProject_ShouldSetNormalizedUrl() {
        // Given
        String name = "测试项目";
        String description = "测试描述";
        String repositoryUrl = "https://github.com/user/repo.git";
        String branch = "main";
        String expectedNormalizedUrl = "https://github.com/user/repo";

        when(projectRepository.findByNormalizedRepositoryUrl(anyString())).thenReturn(Optional.empty());
        when(projectRepository.save(any(Project.class))).thenAnswer(invocation -> {
            Project project = invocation.getArgument(0);
            project.setId(1L);
            return project;
        });

        // When
        Project result = projectService.registerProject(name, description, repositoryUrl, branch);

        // Then
        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        verify(projectRepository).save(projectCaptor.capture());
        
        Project savedProject = projectCaptor.getValue();
        assertNotNull(savedProject.getNormalizedRepositoryUrl(), "标准化URL不应该为空");
        assertEquals(expectedNormalizedUrl, savedProject.getNormalizedRepositoryUrl(), "标准化URL应该正确");
        assertEquals(repositoryUrl, savedProject.getRepositoryUrl(), "原始URL应该保持不变");
    }

    @Test
    @DisplayName("SSH格式URL应该被标准化为HTTPS格式")
    void registerProject_SshUrl_ShouldBeNormalizedToHttps() {
        // Given
        String name = "测试项目";
        String description = "测试描述";
        String repositoryUrl = "**************:user/repo.git";
        String branch = "main";
        String expectedNormalizedUrl = "https://github.com/user/repo";

        when(projectRepository.findByNormalizedRepositoryUrl(anyString())).thenReturn(Optional.empty());
        when(projectRepository.save(any(Project.class))).thenAnswer(invocation -> {
            Project project = invocation.getArgument(0);
            project.setId(1L);
            return project;
        });

        // When
        Project result = projectService.registerProject(name, description, repositoryUrl, branch);

        // Then
        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        verify(projectRepository).save(projectCaptor.capture());
        
        Project savedProject = projectCaptor.getValue();
        assertEquals(expectedNormalizedUrl, savedProject.getNormalizedRepositoryUrl(), "SSH URL应该被标准化为HTTPS格式");
        assertEquals(repositoryUrl, savedProject.getRepositoryUrl(), "原始SSH URL应该保持不变");
    }

    @Test
    @DisplayName("相同仓库的不同URL格式应该被检测为重复")
    void registerProject_DifferentUrlFormats_ShouldDetectDuplicate() {
        // Given
        String name = "测试项目";
        String description = "测试描述";
        String httpsUrl = "https://github.com/user/repo.git";
        String sshUrl = "**************:user/repo.git";
        String normalizedUrl = "https://github.com/user/repo";

        // 模拟已存在的项目
        Project existingProject = Project.builder()
                .id(1L)
                .name("现有项目")
                .repositoryUrl(httpsUrl)
                .normalizedRepositoryUrl(normalizedUrl)
                .build();

        when(projectRepository.findByNormalizedRepositoryUrl(normalizedUrl))
                .thenReturn(Optional.of(existingProject));

        // When & Then - 尝试用SSH格式注册相同仓库应该失败
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> projectService.registerProject(name, description, sshUrl, "main")
        );

        assertTrue(exception.getMessage().contains("该仓库已被项目"), "应该提示仓库已被注册");
        assertTrue(exception.getMessage().contains("现有项目"), "应该显示现有项目名称");
        
        // 验证没有尝试保存新项目
        verify(projectRepository, never()).save(any(Project.class));
    }

    @Test
    @DisplayName("标准化URL验证应该使用GitUrlNormalizer")
    void registerProject_ShouldUseGitUrlNormalizer() {
        // Given
        String repositoryUrl = "https://github.com/user/repo.git";
        String expectedNormalizedUrl = GitUrlNormalizer.normalize(repositoryUrl);

        when(projectRepository.findByNormalizedRepositoryUrl(anyString())).thenReturn(Optional.empty());
        when(projectRepository.save(any(Project.class))).thenAnswer(invocation -> {
            Project project = invocation.getArgument(0);
            project.setId(1L);
            return project;
        });

        // When
        projectService.registerProject("测试项目", "测试描述", repositoryUrl, "main");

        // Then
        ArgumentCaptor<Project> projectCaptor = ArgumentCaptor.forClass(Project.class);
        verify(projectRepository).save(projectCaptor.capture());
        
        Project savedProject = projectCaptor.getValue();
        assertEquals(expectedNormalizedUrl, savedProject.getNormalizedRepositoryUrl(), 
                "应该使用GitUrlNormalizer的标准化结果");
    }

    @Test
    @DisplayName("无效URL格式应该抛出异常")
    void registerProject_InvalidUrl_ShouldThrowException() {
        // Given
        String invalidUrl = "invalid-url-format";

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> projectService.registerProject("测试项目", "测试描述", invalidUrl, "main")
        );

        assertTrue(exception.getMessage().contains("仓库地址格式不正确"), "应该提示URL格式错误");
        
        // 验证没有进行数据库操作
        verify(projectRepository, never()).findByNormalizedRepositoryUrl(anyString());
        verify(projectRepository, never()).save(any(Project.class));
    }

    @Test
    @DisplayName("标准化失败的URL应该抛出异常")
    void registerProject_NormalizationFails_ShouldThrowException() {
        // Given - 使用一个看起来有效但无法标准化的URL
        String problematicUrl = "https://"; // 格式看起来对但实际无法标准化

        // When & Then
        IllegalArgumentException exception = assertThrows(
                IllegalArgumentException.class,
                () -> projectService.registerProject("测试项目", "测试描述", problematicUrl, "main")
        );

        assertTrue(exception.getMessage().contains("无法解析仓库地址") || 
                  exception.getMessage().contains("仓库地址格式不正确"), 
                  "应该提示URL解析失败");
        
        // 验证没有进行数据库操作
        verify(projectRepository, never()).save(any(Project.class));
    }
}

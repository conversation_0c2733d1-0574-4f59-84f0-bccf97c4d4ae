package com.archscope.domain.service.impl;

import com.archscope.domain.entity.Task;
import com.archscope.domain.repository.TaskRepository;
import com.archscope.domain.service.LlmTaskService;
import com.archscope.domain.valueobject.TaskStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * LLM任务超时处理算法测试
 * 专门测试超时处理的核心算法逻辑
 */
@ExtendWith(MockitoExtension.class)
class LlmTaskTimeoutHandlerTest {

    @Mock
    private TaskRepository taskRepository;

    @Mock
    private LlmTaskService llmTaskService;

    private LlmTaskServiceImpl llmTaskServiceImpl;

    @BeforeEach
    void setUp() {
        llmTaskServiceImpl = new LlmTaskServiceImpl(
                taskRepository,
                null, // projectRepository
                null, // objectMapper
                null, // documentStorageService
                null  // documentVersionService
        );
    }

    @Test
    void testHandleTimeoutTasks_NoTimeoutTasks() {
        // Given
        when(taskRepository.findAllByStatus(TaskStatus.PROCESSING))
                .thenReturn(Collections.emptyList());

        // When
        int result = llmTaskServiceImpl.handleTimeoutTasks();

        // Then
        assertEquals(0, result);
        verify(taskRepository).findAllByStatus(TaskStatus.PROCESSING);
        verify(taskRepository, never()).resetTimeoutTask(anyLong());
    }

    @Test
    void testHandleTimeoutTasks_WithTimeoutTasks() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime timeoutTime = now.minusMinutes(35); // 35分钟前，超过30分钟超时

        Task timeoutTask1 = createTaskWithTimeout(1L, timeoutTime);
        Task timeoutTask2 = createTaskWithTimeout(2L, timeoutTime);
        Task normalTask = createTaskWithTimeout(3L, now.plusMinutes(15)); // 未来15分钟，未超时

        List<Task> processingTasks = Arrays.asList(timeoutTask1, timeoutTask2, normalTask);

        when(taskRepository.findAllByStatus(TaskStatus.PROCESSING))
                .thenReturn(processingTasks);

        // 模拟任务查找
        when(taskRepository.findById(1L)).thenReturn(Optional.of(timeoutTask1));
        when(taskRepository.findById(2L)).thenReturn(Optional.of(timeoutTask2));
        when(taskRepository.findById(3L)).thenReturn(Optional.of(normalTask));

        // When
        int result = llmTaskServiceImpl.handleTimeoutTasks();

        // Then
        assertEquals(2, result);
        verify(taskRepository).findAllByStatus(TaskStatus.PROCESSING);
        verify(taskRepository, atLeast(1)).findById(1L);
        verify(taskRepository, atLeast(1)).findById(2L);
        verify(taskRepository, atLeast(1)).findById(3L);
    }

    @Test
    void testIsTaskTimeout_TaskTimeout() {
        // Given
        Long taskId = 1L;
        LocalDateTime timeoutTime = LocalDateTime.now().minusMinutes(35);
        Task task = createTaskWithTimeout(taskId, timeoutTime);

        when(taskRepository.findById(taskId)).thenReturn(Optional.of(task));

        // When
        boolean result = llmTaskServiceImpl.isTaskTimeout(taskId);

        // Then
        assertTrue(result);
        verify(taskRepository).findById(taskId);
    }

    @Test
    void testIsTaskTimeout_TaskNotTimeout() {
        // Given
        Long taskId = 1L;
        LocalDateTime timeoutTime = LocalDateTime.now().plusMinutes(15); // 未来15分钟
        Task task = createTaskWithTimeout(taskId, timeoutTime);

        when(taskRepository.findById(taskId)).thenReturn(Optional.of(task));

        // When
        boolean result = llmTaskServiceImpl.isTaskTimeout(taskId);

        // Then
        assertFalse(result);
        verify(taskRepository).findById(taskId);
    }

    @Test
    void testIsTaskTimeout_TaskNotFound() {
        // Given
        Long taskId = 1L;
        when(taskRepository.findById(taskId)).thenReturn(Optional.empty());

        // When
        boolean result = llmTaskServiceImpl.isTaskTimeout(taskId);

        // Then
        assertFalse(result);
        verify(taskRepository).findById(taskId);
    }

    @Test
    void testIsTaskTimeout_NoTimeoutInfo() {
        // Given
        Long taskId = 1L;
        Task task = Task.builder()
                .id(taskId)
                .status(TaskStatus.PROCESSING)
                .parameters(Collections.emptyMap()) // 没有超时信息
                .build();

        when(taskRepository.findById(taskId)).thenReturn(Optional.of(task));

        // When
        boolean result = llmTaskServiceImpl.isTaskTimeout(taskId);

        // Then
        assertFalse(result);
        verify(taskRepository).findById(taskId);
    }

    @Test
    void testTaskTimeoutCalculation() {
        // Given
        LocalDateTime now = LocalDateTime.now();
        
        // 测试边界情况
        LocalDateTime exactly30Minutes = now.minusMinutes(30);
        LocalDateTime just29Minutes = now.minusMinutes(29);
        LocalDateTime just31Minutes = now.minusMinutes(31);

        // When & Then
        assertTrue(now.isAfter(exactly30Minutes), "30分钟前应该被认为是超时");
        assertTrue(now.isAfter(just29Minutes), "29分钟前不应该超时");
        assertTrue(now.isAfter(just31Minutes), "31分钟前应该超时");
    }

    /**
     * 创建一个处理中的任务
     */
    private Task createProcessingTask(Long taskId, LocalDateTime processingStartTime) {
        return Task.builder()
                .id(taskId)
                .status(TaskStatus.PROCESSING)
                .processingStartedAt(processingStartTime)
                .timeoutAt(processingStartTime.plusMinutes(30))
                .build();
    }

    /**
     * 创建一个带有超时信息的任务
     */
    private Task createTaskWithTimeout(Long taskId, LocalDateTime timeoutAt) {
        java.util.Map<String, Object> parameters = new java.util.HashMap<>();
        parameters.put("timeoutAt", timeoutAt);

        return Task.builder()
                .id(taskId)
                .status(TaskStatus.PROCESSING)
                .parameters(parameters)
                .build();
    }
}

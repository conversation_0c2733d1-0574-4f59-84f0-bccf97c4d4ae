package com.archscope.domain.service.git;

import com.archscope.domain.config.GitServerConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * GitServiceImpl 测试类
 */
@ExtendWith(MockitoExtension.class)
class GitServiceImplTest {

    @Mock
    private GitServerConfig gitServerConfig;

    @InjectMocks
    private GitServiceImpl gitService;

    private GitServerConfig.ServerConfig serverConfig;

    @BeforeEach
    void setUp() {
        serverConfig = new GitServerConfig.ServerConfig();
    }

    @Test
    void testExtractHostFromHttpsUrl() throws Exception {
        // 使用反射访问私有方法
        Method method = GitServiceImpl.class.getDeclaredMethod("extractHostFromUrl", String.class);
        method.setAccessible(true);

        // 测试HTTPS URL
        String host = (String) method.invoke(gitService, "https://gitlab.yeepay.com/owner/repo.git");
        assertEquals("gitlab.yeepay.com", host);

        // 测试不带.git后缀的URL
        host = (String) method.invoke(gitService, "https://github.com/owner/repo");
        assertEquals("github.com", host);
    }

    @Test
    void testExtractHostFromSshUrl() throws Exception {
        Method method = GitServiceImpl.class.getDeclaredMethod("extractHostFromUrl", String.class);
        method.setAccessible(true);

        // 测试SSH URL
        String host = (String) method.invoke(gitService, "*********************:owner/repo.git");
        assertEquals("gitlab.yeepay.com", host);
    }

    @Test
    void testExtractHostFromInvalidUrl() throws Exception {
        Method method = GitServiceImpl.class.getDeclaredMethod("extractHostFromUrl", String.class);
        method.setAccessible(true);

        // 测试无效URL
        String host = (String) method.invoke(gitService, "invalid-url");
        assertNull(host);

        // 测试null URL
        host = (String) method.invoke(gitService, (String) null);
        assertNull(host);
    }

    @Test
    void testConfigureAuthenticationWithProvidedCredentials() throws Exception {
        // 准备测试数据
        Map<String, String> credentials = new HashMap<>();
        credentials.put("username", "testuser");
        credentials.put("password", "testpass");

        // 模拟TransportCommand（这里只是测试逻辑，实际的JGit命令需要集成测试）
        String repositoryUrl = "https://gitlab.yeepay.com/owner/repo.git";

        // 验证当提供了credentials时，不会查询配置文件
        Method method = GitServiceImpl.class.getDeclaredMethod("configureAuthentication", 
                org.eclipse.jgit.api.TransportCommand.class, String.class, Map.class);
        method.setAccessible(true);

        // 由于我们无法轻易模拟TransportCommand，这里主要测试逻辑
        // 在实际使用中，这个方法会被集成测试覆盖
        assertDoesNotThrow(() -> {
            // 这里主要验证方法不会抛出异常
            // 实际的认证逻辑需要在集成测试中验证
        });
    }

    @Test
    void testServerConfigWithPersonalAccessToken() {
        // 测试ServerConfig的个人访问令牌功能
        GitServerConfig.ServerConfig config = new GitServerConfig.ServerConfig();
        
        // 初始状态没有token
        assertFalse(config.hasPersonalAccessToken());
        
        // 设置token
        config.setPersonalAccessToken("glpat-test-token");
        config.setTokenUsername("oauth2");
        
        assertTrue(config.hasPersonalAccessToken());
        assertEquals("glpat-test-token", config.getPersonalAccessToken());
        assertEquals("oauth2", config.getTokenUsername());
    }

    @Test
    void testGitServerConfigIntegration() {
        // 模拟GitServerConfig的行为
        when(gitServerConfig.getServerConfig("gitlab.yeepay.com")).thenReturn(serverConfig);
        
        // 设置服务器配置
        serverConfig.setPersonalAccessToken("glpat-test-token");
        serverConfig.setTokenUsername("oauth2");
        
        // 验证配置
        GitServerConfig.ServerConfig result = gitServerConfig.getServerConfig("gitlab.yeepay.com");
        assertTrue(result.hasPersonalAccessToken());
        assertEquals("glpat-test-token", result.getPersonalAccessToken());
        assertEquals("oauth2", result.getTokenUsername());
        
        verify(gitServerConfig).getServerConfig("gitlab.yeepay.com");
    }
}

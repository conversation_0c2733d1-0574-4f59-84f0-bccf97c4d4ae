package com.archscope.domain.service;

import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.repository.ServiceRepository;
import com.archscope.domain.service.impl.ServiceRegistrationDomainServiceImpl;
import com.archscope.domain.valueobject.Metadata;
import com.archscope.domain.valueobject.Tag;
import com.archscope.domain.valueobject.Version;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.Collections;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 服务注册领域服务测试
 */
class ServiceRegistrationDomainServiceTest {

    @Mock
    private ServiceRepository serviceRepository;

    private ServiceRegistrationDomainService serviceRegistrationDomainService;

    private String serviceName;
    private String serviceDescription;
    private ServiceType serviceType;
    private Version version;
    private URL endpoint;
    private String groupId;
    private String artifactId;
    private Set<Tag> tags;
    private ServiceStatus status;
    private Metadata metadata;

    @BeforeEach
    void setUp() throws MalformedURLException {
        MockitoAnnotations.openMocks(this);
        serviceRegistrationDomainService = new ServiceRegistrationDomainServiceImpl(serviceRepository);

        serviceName = "测试服务";
        serviceDescription = "这是一个测试服务";
        serviceType = ServiceType.REST_API;
        version = Version.of("1.0.0");
        endpoint = new URL("https://api.example.com/v1");
        groupId = "com.example";
        artifactId = "test-service";
        tags = Collections.singleton(Tag.of("api"));
        status = ServiceStatus.ACTIVE;
        metadata = Metadata.empty();
    }

    @Test
    void testRegisterServiceSuccess() {
        // Arrange
        when(serviceRepository.findByName(serviceName)).thenReturn(null);
        when(serviceRepository.findByEndpoint(endpoint)).thenReturn(null);
        when(serviceRepository.findByMavenCoordinates(groupId, artifactId, version.getValue())).thenReturn(null);

        // Act
        Service result = serviceRegistrationDomainService.registerService(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );

        // Assert
        assertNotNull(result);
        assertEquals(serviceName, result.getName());
        assertEquals(serviceDescription, result.getDescription());
        assertEquals(serviceType, result.getType());
        assertEquals(version, result.getVersion());
        assertEquals(endpoint, result.getEndpoint());
        assertEquals(groupId, result.getGroupId());
        assertEquals(artifactId, result.getArtifactId());
        assertEquals(tags, result.getTags());
        assertEquals(status, result.getStatus());
        assertEquals(metadata, result.getMetadata());
    }

    @Test
    void testRegisterServiceWithoutMavenCoordinates() {
        // Arrange
        when(serviceRepository.findByName(serviceName)).thenReturn(null);
        when(serviceRepository.findByEndpoint(endpoint)).thenReturn(null);

        // Act
        Service result = serviceRegistrationDomainService.registerService(
                serviceName, serviceDescription, serviceType, version,
                endpoint, null, null, tags, status, metadata
        );

        // Assert
        assertNotNull(result);
        assertEquals(serviceName, result.getName());
        assertNull(result.getGroupId());
        assertNull(result.getArtifactId());
    }

    @Test
    void testRegisterServiceWithDuplicateName() {
        // Arrange
        Service existingService = Service.create(serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata);
        when(serviceRepository.findByName(serviceName)).thenReturn(existingService);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.registerService(
                    serviceName, serviceDescription, serviceType, version,
                    endpoint, groupId, artifactId, tags, status, metadata
            );
        });
        assertEquals("Service name already exists: " + serviceName, exception.getMessage());
    }

    @Test
    void testRegisterServiceWithDuplicateEndpoint() {
        // Arrange
        when(serviceRepository.findByName(serviceName)).thenReturn(null);
        Service existingService = Service.create("其他服务", serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata);
        when(serviceRepository.findByEndpoint(endpoint)).thenReturn(existingService);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.registerService(
                    serviceName, serviceDescription, serviceType, version,
                    endpoint, groupId, artifactId, tags, status, metadata
            );
        });
        assertEquals("Service endpoint already exists: " + endpoint, exception.getMessage());
    }

    @Test
    void testRegisterServiceWithDuplicateMavenCoordinates() {
        // Arrange
        when(serviceRepository.findByName(serviceName)).thenReturn(null);
        when(serviceRepository.findByEndpoint(endpoint)).thenReturn(null);
        Service existingService = Service.create("其他服务", serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata);
        when(serviceRepository.findByMavenCoordinates(groupId, artifactId, version.getValue())).thenReturn(existingService);

        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.registerService(
                    serviceName, serviceDescription, serviceType, version,
                    endpoint, groupId, artifactId, tags, status, metadata
            );
        });
        assertEquals("Maven coordinates already exist: " + groupId + ":" + artifactId + ":" + version, exception.getMessage());
    }

    @Test
    void testValidateServiceInfoSuccess() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            serviceRegistrationDomainService.validateServiceInfo(serviceName, serviceType, version, endpoint);
        });
    }

    @Test
    void testValidateServiceInfoWithNullName() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateServiceInfo(null, serviceType, version, endpoint);
        });
        assertEquals("Service name cannot be null or empty", exception.getMessage());
    }

    @Test
    void testValidateServiceInfoWithEmptyName() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateServiceInfo("", serviceType, version, endpoint);
        });
        assertEquals("Service name cannot be null or empty", exception.getMessage());
    }

    @Test
    void testValidateServiceInfoWithNullType() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateServiceInfo(serviceName, null, version, endpoint);
        });
        assertEquals("Service type cannot be null", exception.getMessage());
    }

    @Test
    void testValidateServiceInfoWithNullVersion() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateServiceInfo(serviceName, serviceType, null, endpoint);
        });
        assertEquals("Service version cannot be null", exception.getMessage());
    }

    @Test
    void testValidateServiceInfoWithNullEndpoint() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateServiceInfo(serviceName, serviceType, version, null);
        });
        assertEquals("Service endpoint cannot be null", exception.getMessage());
    }

    @Test
    void testValidateMavenCoordinatesSuccess() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            serviceRegistrationDomainService.validateMavenCoordinates(groupId, artifactId);
        });
    }

    @Test
    void testValidateMavenCoordinatesWithValidFormats() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            serviceRegistrationDomainService.validateMavenCoordinates("com.example.test", "my-artifact");
        });
        assertDoesNotThrow(() -> {
            serviceRegistrationDomainService.validateMavenCoordinates("org.springframework", "spring_core");
        });
        assertDoesNotThrow(() -> {
            serviceRegistrationDomainService.validateMavenCoordinates("io.github.user", "test-lib");
        });
    }

    @Test
    void testValidateMavenCoordinatesWithNullGroupId() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateMavenCoordinates(null, artifactId);
        });
        assertEquals("Group ID cannot be null or empty", exception.getMessage());
    }

    @Test
    void testValidateMavenCoordinatesWithEmptyGroupId() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateMavenCoordinates("", artifactId);
        });
        assertEquals("Group ID cannot be null or empty", exception.getMessage());
    }

    @Test
    void testValidateMavenCoordinatesWithInvalidGroupId() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateMavenCoordinates("com.example-invalid", artifactId);
        });
        assertEquals("Invalid group ID format: com.example-invalid", exception.getMessage());
    }

    @Test
    void testValidateMavenCoordinatesWithNullArtifactId() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateMavenCoordinates(groupId, null);
        });
        assertEquals("Artifact ID cannot be null or empty", exception.getMessage());
    }

    @Test
    void testValidateMavenCoordinatesWithEmptyArtifactId() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateMavenCoordinates(groupId, "");
        });
        assertEquals("Artifact ID cannot be null or empty", exception.getMessage());
    }

    @Test
    void testValidateMavenCoordinatesWithInvalidArtifactId() {
        // Act & Assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            serviceRegistrationDomainService.validateMavenCoordinates(groupId, "invalid.artifact");
        });
        assertEquals("Invalid artifact ID format: invalid.artifact", exception.getMessage());
    }

    @Test
    void testIsServiceNameExists() {
        // Arrange
        Service existingService = Service.create(serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata);
        when(serviceRepository.findByName(serviceName)).thenReturn(existingService);
        when(serviceRepository.findByName("不存在的服务")).thenReturn(null);

        // Act & Assert
        assertTrue(serviceRegistrationDomainService.isServiceNameExists(serviceName));
        assertFalse(serviceRegistrationDomainService.isServiceNameExists("不存在的服务"));
    }

    @Test
    void testIsServiceEndpointExists() throws MalformedURLException {
        // Arrange
        Service existingService = Service.create(serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata);
        when(serviceRepository.findByEndpoint(endpoint)).thenReturn(existingService);

        // 测试不存在的端点
        URL nonExistentEndpoint = new URL("https://api.nonexistent.com/v1");
        when(serviceRepository.findByEndpoint(nonExistentEndpoint)).thenReturn(null);

        // Act & Assert
        assertTrue(serviceRegistrationDomainService.isServiceEndpointExists(endpoint));
        assertFalse(serviceRegistrationDomainService.isServiceEndpointExists(nonExistentEndpoint));
    }

    @Test
    void testIsMavenCoordinatesExists() {
        // Arrange
        Service existingService = Service.create(serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata);
        when(serviceRepository.findByMavenCoordinates(groupId, artifactId, version.getValue())).thenReturn(existingService);

        // 测试不存在的Maven坐标
        String nonExistentGroupId = "com.nonexistent";
        String nonExistentArtifactId = "nonexistent-service";
        Version nonExistentVersion = Version.of("2.0.0");
        when(serviceRepository.findByMavenCoordinates(nonExistentGroupId, nonExistentArtifactId, nonExistentVersion.getValue())).thenReturn(null);

        // Act & Assert
        assertTrue(serviceRegistrationDomainService.isMavenCoordinatesExists(groupId, artifactId, version));
        assertFalse(serviceRegistrationDomainService.isMavenCoordinatesExists(nonExistentGroupId, nonExistentArtifactId, nonExistentVersion));
    }
}
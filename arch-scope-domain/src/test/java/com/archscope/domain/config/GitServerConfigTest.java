package com.archscope.domain.config;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GitServerConfig 配置测试
 */
class GitServerConfigTest {

    private GitServerConfig gitServerConfig;

    @BeforeEach
    void setUp() {
        gitServerConfig = new GitServerConfig();

        // 手动设置测试配置
        Map<String, GitServerConfig.ServerConfig> hosts = new HashMap<>();

        // 配置gitlab.yeepay.com
        GitServerConfig.ServerConfig yeepayConfig = new GitServerConfig.ServerConfig();
        yeepayConfig.setSupportsHttps(true);
        yeepayConfig.setSupportsHttp(false);
        yeepayConfig.setPersonalAccessToken("glpat-test-token");
        yeepayConfig.setTokenUsername("oauth2");
        hosts.put("gitlab.yeepay.com", yeepayConfig);

        gitServerConfig.setHosts(hosts);

        // 设置默认配置
        GitServerConfig.ServerConfig defaultConfig = new GitServerConfig.ServerConfig();
        defaultConfig.setSupportsHttps(true);
        defaultConfig.setSupportsHttp(false);
        defaultConfig.setSuggestHttpsOnSshFailure(true);
        gitServerConfig.setDefaultConfig(defaultConfig);
    }

    @Test
    void testGitLabYeepayConfiguration() {
        // 获取gitlab.yeepay.com的配置
        GitServerConfig.ServerConfig config = gitServerConfig.getServerConfig("gitlab.yeepay.com");

        // 验证基本配置
        assertTrue(config.isSupportsHttps());
        assertFalse(config.isSupportsHttp());

        // 验证个人访问令牌配置
        assertTrue(config.hasPersonalAccessToken());
        assertEquals("glpat-test-token", config.getPersonalAccessToken());
        assertEquals("oauth2", config.getTokenUsername());
    }

    @Test
    void testDefaultConfiguration() {
        // 获取不存在的服务器配置，应该返回默认配置
        GitServerConfig.ServerConfig config = gitServerConfig.getServerConfig("unknown.server.com");

        // 验证默认配置
        assertTrue(config.isSupportsHttps());
        assertFalse(config.isSupportsHttp());
        assertTrue(config.isSuggestHttpsOnSshFailure());

        // 默认配置应该没有个人访问令牌
        assertFalse(config.hasPersonalAccessToken());
    }

    @Test
    void testServerConfigMethods() {
        GitServerConfig.ServerConfig config = new GitServerConfig.ServerConfig();
        
        // 测试没有token的情况
        assertFalse(config.hasPersonalAccessToken());
        
        // 设置空token
        config.setPersonalAccessToken("");
        assertFalse(config.hasPersonalAccessToken());
        
        // 设置空白token
        config.setPersonalAccessToken("   ");
        assertFalse(config.hasPersonalAccessToken());
        
        // 设置有效token
        config.setPersonalAccessToken("glpat-valid-token");
        assertTrue(config.hasPersonalAccessToken());
    }
}

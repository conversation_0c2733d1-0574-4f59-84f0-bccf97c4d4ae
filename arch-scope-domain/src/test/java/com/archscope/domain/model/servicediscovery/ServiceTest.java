package com.archscope.domain.model.servicediscovery;

import com.archscope.domain.valueobject.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.net.MalformedURLException;
import java.net.URL;
import java.time.Instant;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 服务实体测试
 */
class ServiceTest {

    private String serviceName;
    private String serviceDescription;
    private ServiceType serviceType;
    private Version version;
    private URL endpoint;
    private String groupId;
    private String artifactId;
    private Set<Tag> tags;
    private ServiceStatus status;
    private Metadata metadata;

    @BeforeEach
    void setUp() throws MalformedURLException {
        serviceName = "测试服务";
        serviceDescription = "这是一个测试服务";
        serviceType = ServiceType.REST_API;
        version = Version.of("1.0.0");
        endpoint = new URL("https://api.example.com/v1");
        groupId = "com.example";
        artifactId = "test-service";
        tags = new HashSet<>();
        tags.add(Tag.of("api"));
        tags.add(Tag.of("microservice"));
        status = ServiceStatus.ACTIVE;
        metadata = Metadata.empty();
    }

    @Test
    void testCreateService() {
        // Act
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );

        // Assert
        assertNotNull(service);
        assertNotNull(service.getId());
        assertEquals(serviceName, service.getName());
        assertEquals(serviceDescription, service.getDescription());
        assertEquals(serviceType, service.getType());
        assertEquals(version, service.getVersion());
        assertEquals(endpoint, service.getEndpoint());
        assertEquals(groupId, service.getGroupId());
        assertEquals(artifactId, service.getArtifactId());
        assertEquals(tags, service.getTags());
        assertEquals(status, service.getStatus());
        assertEquals(metadata, service.getMetadata());
        assertNotNull(service.getRegisteredAt());
        assertNotNull(service.getLastUpdatedAt());
        assertTrue(service.getCapabilities().isEmpty());
    }

    @Test
    void testCreateServiceWithNullValues() {
        // Act
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, null, null, null
        );

        // Assert
        assertNotNull(service);
        assertEquals(ServiceStatus.ACTIVE, service.getStatus());
        assertEquals(Metadata.empty(), service.getMetadata());
        assertTrue(service.getTags().isEmpty());
    }

    @Test
    void testRestoreService() {
        // Arrange
        ServiceId serviceId = ServiceId.createNew();
        Instant registeredAt = Instant.now().minusSeconds(3600);
        Instant lastUpdatedAt = Instant.now().minusSeconds(1800);
        Set<Capability> capabilities = new HashSet<>();

        // Act
        Service service = Service.restore(
                serviceId, serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, capabilities,
                status, metadata, registeredAt, lastUpdatedAt
        );

        // Assert
        assertNotNull(service);
        assertEquals(serviceId, service.getId());
        assertEquals(serviceName, service.getName());
        assertEquals(serviceDescription, service.getDescription());
        assertEquals(serviceType, service.getType());
        assertEquals(version, service.getVersion());
        assertEquals(endpoint, service.getEndpoint());
        assertEquals(groupId, service.getGroupId());
        assertEquals(artifactId, service.getArtifactId());
        assertEquals(tags, service.getTags());
        assertEquals(capabilities, service.getCapabilities());
        assertEquals(status, service.getStatus());
        assertEquals(metadata, service.getMetadata());
        assertEquals(registeredAt, service.getRegisteredAt());
        assertEquals(lastUpdatedAt, service.getLastUpdatedAt());
    }

    @Test
    void testRestoreServiceWithNullValues() {
        // Arrange
        ServiceId serviceId = ServiceId.createNew();
        Instant registeredAt = Instant.now().minusSeconds(3600);
        Instant lastUpdatedAt = Instant.now().minusSeconds(1800);

        // Act
        Service service = Service.restore(
                serviceId, serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, null, null,
                null, null, registeredAt, lastUpdatedAt
        );

        // Assert
        assertNotNull(service);
        assertEquals(ServiceStatus.UNKNOWN, service.getStatus());
        assertEquals(Metadata.empty(), service.getMetadata());
        assertTrue(service.getTags().isEmpty());
        assertTrue(service.getCapabilities().isEmpty());
    }

    @Test
    void testUpdateService() throws MalformedURLException {
        // Arrange
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );
        Instant originalLastUpdatedAt = service.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String newName = "更新后的服务";
        String newDescription = "更新后的描述";
        ServiceType newType = ServiceType.GRPC;
        Version newVersion = Version.of("2.0.0");
        URL newEndpoint = new URL("https://api.example.com/v2");
        String newGroupId = "com.example.updated";
        String newArtifactId = "updated-service";
        Set<Tag> newTags = Collections.singleton(Tag.of("updated"));
        ServiceStatus newStatus = ServiceStatus.MAINTENANCE;
        Map<String, String> metadataMap = new HashMap<>();
        metadataMap.put("key", "value");
        Metadata newMetadata = Metadata.of(metadataMap);

        // Act
        service.update(newName, newDescription, newType, newVersion,
                newEndpoint, newGroupId, newArtifactId, newTags,
                newStatus, newMetadata);

        // Assert
        assertEquals(newName, service.getName());
        assertEquals(newDescription, service.getDescription());
        assertEquals(newType, service.getType());
        assertEquals(newVersion, service.getVersion());
        assertEquals(newEndpoint, service.getEndpoint());
        assertEquals(newGroupId, service.getGroupId());
        assertEquals(newArtifactId, service.getArtifactId());
        assertEquals(newTags, service.getTags());
        assertEquals(newStatus, service.getStatus());
        assertEquals(newMetadata, service.getMetadata());
        assertTrue(service.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testUpdateServiceWithNullTags() {
        // Arrange
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );
        Set<Tag> originalTags = new HashSet<>(service.getTags());

        // Act
        service.update(serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, null,
                status, metadata);

        // Assert
        assertEquals(originalTags, service.getTags()); // Tags should remain unchanged
    }

    @Test
    void testAddCapability() {
        // Arrange
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );
        Capability capability = Capability.create(
                service.getId(), "测试能力", "测试能力描述",
                Version.of("1.0.0"), Collections.emptySet(), Collections.emptySet()
        );
        Instant originalLastUpdatedAt = service.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Act
        service.addCapability(capability);

        // Assert
        assertTrue(service.getCapabilities().contains(capability));
        assertTrue(service.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testRemoveCapability() {
        // Arrange
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );
        Capability capability = Capability.create(
                service.getId(), "测试能力", "测试能力描述",
                Version.of("1.0.0"), Collections.emptySet(), Collections.emptySet()
        );
        service.addCapability(capability);
        Instant originalLastUpdatedAt = service.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Act
        service.removeCapability(capability);

        // Assert
        assertFalse(service.getCapabilities().contains(capability));
        assertTrue(service.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testUpdateStatus() {
        // Arrange
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );
        Instant originalLastUpdatedAt = service.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Act
        service.updateStatus(ServiceStatus.INACTIVE);

        // Assert
        assertEquals(ServiceStatus.INACTIVE, service.getStatus());
        assertTrue(service.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testGetTagsReturnsUnmodifiableSet() {
        // Arrange
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );

        // Act & Assert
        Set<Tag> returnedTags = service.getTags();
        assertThrows(UnsupportedOperationException.class, () -> {
            returnedTags.add(Tag.of("new-tag"));
        });
    }

    @Test
    void testGetCapabilitiesReturnsUnmodifiableSet() {
        // Arrange
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );

        // Act & Assert
        Set<Capability> returnedCapabilities = service.getCapabilities();
        assertThrows(UnsupportedOperationException.class, () -> {
            returnedCapabilities.add(Capability.create(
                    service.getId(), "测试能力", "测试能力描述",
                    Version.of("1.0.0"), Collections.emptySet(), Collections.emptySet()
            ));
        });
    }

    @Test
    void testEqualsAndHashCode() {
        // Arrange
        ServiceId serviceId = ServiceId.createNew();
        Service service1 = Service.restore(
                serviceId, serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, Collections.emptySet(),
                status, metadata, Instant.now(), Instant.now()
        );
        Map<String, String> metadataMap2 = new HashMap<>();
        metadataMap2.put("key", "value");
        Service service2 = Service.restore(
                serviceId, "不同的名称", "不同的描述", ServiceType.GRPC, Version.of("2.0.0"),
                endpoint, groupId, artifactId, Collections.emptySet(), Collections.emptySet(),
                ServiceStatus.INACTIVE, Metadata.of(metadataMap2), Instant.now(), Instant.now()
        );
        Service service3 = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );

        // Assert
        assertEquals(service1, service2); // Same ID
        assertNotEquals(service1, service3); // Different ID
        assertEquals(service1.hashCode(), service2.hashCode());
        assertNotEquals(service1.hashCode(), service3.hashCode());
    }

    @Test
    void testToString() {
        // Arrange
        Service service = Service.create(
                serviceName, serviceDescription, serviceType, version,
                endpoint, groupId, artifactId, tags, status, metadata
        );

        // Act
        String toString = service.toString();

        // Assert
        assertNotNull(toString);
        assertTrue(toString.contains("Service{"));
        assertTrue(toString.contains("id=" + service.getId()));
        assertTrue(toString.contains("name='" + serviceName + "'"));
        assertTrue(toString.contains("type=" + serviceType));
        assertTrue(toString.contains("version=" + version));
        assertTrue(toString.contains("groupId='" + groupId + "'"));
        assertTrue(toString.contains("artifactId='" + artifactId + "'"));
        assertTrue(toString.contains("status=" + status));
    }
}
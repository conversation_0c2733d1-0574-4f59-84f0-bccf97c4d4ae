package com.archscope.domain.model.parser;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 解析结果合并器测试类
 */
public class ParseResultMergerTest {
    
    private ParseResultMerger merger;
    private FileParseResult llmResult;
    private FileParseResult traditionalResult;
    
    @BeforeEach
    void setUp() {
        merger = new DefaultParseResultMerger();
        
        // 创建LLM解析结果
        llmResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .fileComment("This is a sample class")
                .imports(Arrays.asList("java.util.List", "java.util.Map"))
                .classDefinitions(Arrays.asList(
                        ClassDefinition.builder()
                                .name("Example")
                                .packageName("com.example")
                                .fullyQualifiedName("com.example.Example")
                                .type(ClassType.CLASS)
                                .accessModifier(AccessModifier.PUBLIC)
                                .build()
                ))
                .dependencies(new ArrayList<>())
                .successful(true)
                .build();
        
        // 创建传统解析结果
        traditionalResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .imports(Arrays.asList("java.util.List", "java.util.ArrayList"))
                .classDefinitions(Arrays.asList(
                        ClassDefinition.builder()
                                .name("Example")
                                .packageName("com.example")
                                .fullyQualifiedName("com.example.Example")
                                .type(ClassType.CLASS)
                                .accessModifier(AccessModifier.PUBLIC)
                                .superClass("Object")
                                .build()
                ))
                .dependencies(Arrays.asList(
                        DependencyRelation.builder()
                                .sourceClass("com.example.Example")
                                .targetClass("java.util.List")
                                .type(DependencyType.IMPORT)
                                .strength(1)
                                .location("import")
                                .build()
                ))
                .successful(true)
                .build();
    }
    
    @Test
    @DisplayName("测试解析结果合并器初始化")
    void testMergerInitialization() {
        assertNotNull(merger, "解析结果合并器不应为空");
    }
    
    @Test
    @DisplayName("测试LLM优先策略")
    void testLlmPriorityStrategy() {
        FileParseResult result = merger.merge(llmResult, traditionalResult, ParseResultMerger.MergeStrategy.LLM_PRIORITY);
        
        assertNotNull(result, "合并结果不应为空");
        assertTrue(result.isSuccessful(), "合并结果应该成功");
        
        // 验证LLM优先策略
        assertEquals("This is a sample class", result.getFileComment(), "应使用LLM的文件注释");
        assertEquals(2, result.getImports().size(), "应使用LLM的导入列表");
        assertTrue(result.getImports().contains("java.util.List"), "应包含List导入");
        assertTrue(result.getImports().contains("java.util.Map"), "应包含Map导入");
        assertFalse(result.getImports().contains("java.util.ArrayList"), "不应包含ArrayList导入");
        
        // 验证类定义
        assertEquals(1, result.getClassDefinitions().size(), "应有一个类定义");
        ClassDefinition classDef = result.getClassDefinitions().get(0);
        assertEquals("Example", classDef.getName(), "类名应为Example");
        assertNull(classDef.getSuperClass(), "父类应为null（使用LLM结果）");
    }
    
    @Test
    @DisplayName("测试传统优先策略")
    void testTraditionalPriorityStrategy() {
        FileParseResult result = merger.merge(llmResult, traditionalResult, ParseResultMerger.MergeStrategy.TRADITIONAL_PRIORITY);
        
        assertNotNull(result, "合并结果不应为空");
        assertTrue(result.isSuccessful(), "合并结果应该成功");
        
        // 验证传统优先策略
        assertEquals("This is a sample class", result.getFileComment(), "应使用LLM的文件注释（传统解析通常不提供）");
        assertEquals(2, result.getImports().size(), "应使用传统解析的导入列表");
        assertTrue(result.getImports().contains("java.util.List"), "应包含List导入");
        assertTrue(result.getImports().contains("java.util.ArrayList"), "应包含ArrayList导入");
        assertFalse(result.getImports().contains("java.util.Map"), "不应包含Map导入");
        
        // 验证类定义
        assertEquals(1, result.getClassDefinitions().size(), "应有一个类定义");
        ClassDefinition classDef = result.getClassDefinitions().get(0);
        assertEquals("Example", classDef.getName(), "类名应为Example");
        assertEquals("Object", classDef.getSuperClass(), "父类应为Object（使用传统解析结果）");
        
        // 验证依赖关系
        assertTrue(result.getDependencies().size() >= 1, "应至少有一个依赖关系");
    }
    
    @Test
    @DisplayName("测试智能合并策略")
    void testSmartMergeStrategy() {
        FileParseResult result = merger.merge(llmResult, traditionalResult, ParseResultMerger.MergeStrategy.SMART_MERGE);
        
        assertNotNull(result, "合并结果不应为空");
        assertTrue(result.isSuccessful(), "合并结果应该成功");
        
        // 验证智能合并策略
        assertEquals("This is a sample class", result.getFileComment(), "应使用LLM的文件注释");
        assertTrue(result.getImports().size() >= 3, "应合并导入列表");
        assertTrue(result.getImports().contains("java.util.List"), "应包含List导入");
        assertTrue(result.getImports().contains("java.util.Map"), "应包含Map导入");
        assertTrue(result.getImports().contains("java.util.ArrayList"), "应包含ArrayList导入");
        
        // 验证类定义
        assertEquals(1, result.getClassDefinitions().size(), "应有一个类定义");
        ClassDefinition classDef = result.getClassDefinitions().get(0);
        assertEquals("Example", classDef.getName(), "类名应为Example");
        assertEquals("Object", classDef.getSuperClass(), "父类应为Object（传统解析通常更准确）");
        
        // 验证依赖关系
        assertTrue(result.getDependencies().size() >= 1, "应至少有一个依赖关系");
    }
    
    @Test
    @DisplayName("测试一个结果为null的情况")
    void testOneResultNull() {
        // LLM结果为null
        FileParseResult result1 = merger.merge(null, traditionalResult, ParseResultMerger.MergeStrategy.SMART_MERGE);
        assertNotNull(result1, "合并结果不应为空");
        assertEquals(traditionalResult, result1, "应返回传统解析结果");
        
        // 传统解析结果为null
        FileParseResult result2 = merger.merge(llmResult, null, ParseResultMerger.MergeStrategy.SMART_MERGE);
        assertNotNull(result2, "合并结果不应为空");
        assertEquals(llmResult, result2, "应返回LLM解析结果");
    }
    
    @Test
    @DisplayName("测试两个结果都失败的情况")
    void testBothResultsFailed() {
        // 创建失败的LLM结果
        FileParseResult failedLlmResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .successful(false)
                .errorMessage("LLM解析错误")
                .build();
        
        // 创建失败的传统解析结果
        FileParseResult failedTraditionalResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .successful(false)
                .errorMessage("传统解析错误")
                .build();
        
        FileParseResult result = merger.merge(failedLlmResult, failedTraditionalResult, ParseResultMerger.MergeStrategy.SMART_MERGE);
        
        assertNotNull(result, "合并结果不应为空");
        assertFalse(result.isSuccessful(), "合并结果应该失败");
        assertTrue(result.getErrorMessage().contains("LLM解析错误"), "错误信息应包含LLM错误");
        assertTrue(result.getErrorMessage().contains("传统解析错误"), "错误信息应包含传统解析错误");
    }
    
    @Test
    @DisplayName("测试一个结果成功一个结果失败的情况")
    void testOneResultFailed() {
        // 创建失败的LLM结果
        FileParseResult failedLlmResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .successful(false)
                .errorMessage("LLM解析错误")
                .build();
        
        // LLM失败，传统成功
        FileParseResult result1 = merger.merge(failedLlmResult, traditionalResult, ParseResultMerger.MergeStrategy.SMART_MERGE);
        assertNotNull(result1, "合并结果不应为空");
        assertTrue(result1.isSuccessful(), "合并结果应该成功");
        assertEquals(traditionalResult, result1, "应返回传统解析结果");
        
        // 创建失败的传统解析结果
        FileParseResult failedTraditionalResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .successful(false)
                .errorMessage("传统解析错误")
                .build();
        
        // LLM成功，传统失败
        FileParseResult result2 = merger.merge(llmResult, failedTraditionalResult, ParseResultMerger.MergeStrategy.SMART_MERGE);
        assertNotNull(result2, "合并结果不应为空");
        assertTrue(result2.isSuccessful(), "合并结果应该成功");
        assertEquals(llmResult, result2, "应返回LLM解析结果");
    }
}
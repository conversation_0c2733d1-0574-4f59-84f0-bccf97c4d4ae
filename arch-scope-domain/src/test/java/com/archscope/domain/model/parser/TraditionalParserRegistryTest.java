package com.archscope.domain.model.parser;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

/**
 * 传统解析器注册表测试类
 */
public class TraditionalParserRegistryTest {
    
    @Mock
    private TraditionalCodeParser javaParser;
    
    @Mock
    private TraditionalCodeParser jsParser;
    
    @Mock
    private TraditionalCodeParser pythonParser;
    
    private TraditionalParserRegistry registry;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 配置模拟对象
        when(javaParser.getSupportedLanguage()).thenReturn(LanguageType.JAVA);
        when(jsParser.getSupportedLanguage()).thenReturn(LanguageType.JAVASCRIPT);
        when(pythonParser.getSupportedLanguage()).thenReturn(LanguageType.PYTHON);
        
        // 创建注册表并注册解析器
        List<TraditionalCodeParser> parsers = Arrays.asList(javaParser, jsParser, pythonParser);
        registry = new TraditionalParserRegistry(parsers);
    }
    
    @Test
    @DisplayName("测试传统解析器注册表初始化")
    void testRegistryInitialization() {
        assertNotNull(registry, "传统解析器注册表不应为空");
    }
    
    @Test
    @DisplayName("测试获取指定语言的解析器")
    void testGetParser() {
        TraditionalCodeParser parser = registry.getParser(LanguageType.JAVA);
        assertNotNull(parser, "应返回Java解析器");
        assertEquals(javaParser, parser, "应返回正确的Java解析器");
        
        parser = registry.getParser(LanguageType.JAVASCRIPT);
        assertNotNull(parser, "应返回JavaScript解析器");
        assertEquals(jsParser, parser, "应返回正确的JavaScript解析器");
        
        parser = registry.getParser(LanguageType.PYTHON);
        assertNotNull(parser, "应返回Python解析器");
        assertEquals(pythonParser, parser, "应返回正确的Python解析器");
        
        // 测试获取不存在的解析器
        parser = registry.getParser(LanguageType.CSS);
        assertNull(parser, "不存在的解析器应返回null");
    }
    
    @Test
    @DisplayName("测试检查是否支持指定语言")
    void testSupportsLanguage() {
        assertTrue(registry.supportsLanguage(LanguageType.JAVA), "应支持Java语言");
        assertTrue(registry.supportsLanguage(LanguageType.JAVASCRIPT), "应支持JavaScript语言");
        assertTrue(registry.supportsLanguage(LanguageType.PYTHON), "应支持Python语言");
        assertFalse(registry.supportsLanguage(LanguageType.CSS), "不应支持CSS语言");
        assertFalse(registry.supportsLanguage(LanguageType.UNKNOWN), "不应支持未知语言");
    }
    
    @Test
    @DisplayName("测试获取所有支持的语言类型")
    void testGetSupportedLanguages() {
        List<LanguageType> supportedLanguages = registry.getSupportedLanguages();
        
        assertNotNull(supportedLanguages, "支持的语言列表不应为空");
        assertEquals(3, supportedLanguages.size(), "应支持3种语言");
        assertTrue(supportedLanguages.contains(LanguageType.JAVA), "应包含Java语言");
        assertTrue(supportedLanguages.contains(LanguageType.JAVASCRIPT), "应包含JavaScript语言");
        assertTrue(supportedLanguages.contains(LanguageType.PYTHON), "应包含Python语言");
    }
    
    @Test
    @DisplayName("测试重复注册同一语言的解析器")
    void testRegisterDuplicateLanguage() {
        // 创建另一个Java解析器
        TraditionalCodeParser anotherJavaParser = mock(TraditionalCodeParser.class);
        when(anotherJavaParser.getSupportedLanguage()).thenReturn(LanguageType.JAVA);
        
        // 创建新的注册表，包含重复的Java解析器
        List<TraditionalCodeParser> parsersWithDuplicate = Arrays.asList(javaParser, anotherJavaParser, jsParser);
        TraditionalParserRegistry registryWithDuplicate = new TraditionalParserRegistry(parsersWithDuplicate);
        
        // 验证结果（应该使用最后注册的解析器）
        TraditionalCodeParser parser = registryWithDuplicate.getParser(LanguageType.JAVA);
        assertNotNull(parser, "应返回Java解析器");
        assertEquals(anotherJavaParser, parser, "应返回最后注册的Java解析器");
        
        // 验证支持的语言数量（应该是2，不是3）
        List<LanguageType> supportedLanguages = registryWithDuplicate.getSupportedLanguages();
        assertEquals(2, supportedLanguages.size(), "应支持2种语言（Java和JavaScript）");
    }
}
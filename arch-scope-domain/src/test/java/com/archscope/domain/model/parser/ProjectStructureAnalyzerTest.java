package com.archscope.domain.model.parser;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

// Remove LlmService and other unused imports if they exist
// import com.archscope.domain.external.llm.LlmService;
// import com.archscope.domain.model.parser.TraditionalParserRegistry;
// import com.archscope.domain.model.parser.ParseResultMerger;

/**
 * 项目结构分析器测试类
 */
public class ProjectStructureAnalyzerTest {
    
    // Remove mocks for unused dependencies
    // @Mock
    // private LlmService llmService;
    // @Mock
    // private TraditionalParserRegistry traditionalParserRegistry;
    // @Mock
    // private ParseResultMerger parseResultMerger;
    
    @Mock
    private DefaultCodeParser codeParser; // Mock the dependency directly
    
    @InjectMocks
    private DefaultProjectStructureAnalyzer analyzer; // 修改为具体实现类而不是接口
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }
    
    @Test
    @DisplayName("测试项目结构分析器初始化")
    void testAnalyzerInitialization() {
        assertNotNull(analyzer, "项目结构分析器不应为空");
    }
    
    @Test
    @DisplayName("测试分析项目包结构")
    void testAnalyzePackageStructure() {
        // 创建模拟的文件解析结果
        FileParseResult file1 = FileParseResult.builder()
                .filename("Class1.java")
                .packageName("com.example.service")
                .classDefinitions(singletonList(
                        ClassDefinition.builder()
                                .name("Class1")
                                .packageName("com.example.service")
                                .fullyQualifiedName("com.example.service.Class1")
                                .build()
                ))
                .build();
                
        FileParseResult file2 = FileParseResult.builder()
                .filename("Class2.java")
                .packageName("com.example.repository")
                .classDefinitions(singletonList(
                        ClassDefinition.builder()
                                .name("Class2")
                                .packageName("com.example.repository")
                                .fullyQualifiedName("com.example.repository.Class2")
                                .build()
                ))
                .build();
                
        FileParseResult file3 = FileParseResult.builder()
                .filename("Class3.java")
                .packageName("com.example.controller")
                .classDefinitions(singletonList(
                        ClassDefinition.builder()
                                .name("Class3")
                                .packageName("com.example.controller")
                                .fullyQualifiedName("com.example.controller.Class3")
                                .build()
                ))
                .build();
        
        List<FileParseResult> parseResults = Arrays.asList(file1, file2, file3);
        
        // 分析包结构
        PackageStructure packageStructure = analyzer.analyzePackageStructure(parseResults);
        
        // 验证结果
        assertNotNull(packageStructure, "包结构不应为空");
        assertEquals(3, packageStructure.getAllPackages().size(), "应解析出3个包");
        assertTrue(packageStructure.getAllPackages().contains("com.example.service"), "应包含service包");
        assertTrue(packageStructure.getAllPackages().contains("com.example.repository"), "应包含repository包");
        assertTrue(packageStructure.getAllPackages().contains("com.example.controller"), "应包含controller包");
        
        assertEquals("com.example", packageStructure.getRootPackage(), "根包应为com.example");
    }
    
    @Test
    @DisplayName("测试分析模块结构")
    void testAnalyzeModuleStructure() {
        // 创建模拟的包结构
        PackageStructure packageStructure = new PackageStructure();
        packageStructure.addPackage("com.example.api");
        packageStructure.addPackage("com.example.service");
        packageStructure.addPackage("com.example.service.impl");
        packageStructure.addPackage("com.example.repository");
        packageStructure.addPackage("com.example.controller");
        packageStructure.addPackage("com.example.model");
        packageStructure.addPackage("com.example.config");
        
        // 分析模块结构
        ModuleStructure moduleStructure = analyzer.analyzeModuleStructure(packageStructure);
        
        // 验证结果
        assertNotNull(moduleStructure, "模块结构不应为空");
        assertTrue(moduleStructure.getModules().size() >= 3, "应解析出至少3个模块");
        
        // 验证是否识别出主要模块
        boolean hasServiceModule = false;
        boolean hasRepositoryModule = false;
        boolean hasControllerModule = false;
        
        for (Module module : moduleStructure.getModules()) {
            if (module.getName().contains("service")) {
                hasServiceModule = true;
            } else if (module.getName().contains("repository")) {
                hasRepositoryModule = true;
            } else if (module.getName().contains("controller")) {
                hasControllerModule = true;
            }
        }
        
        assertTrue(hasServiceModule, "应识别出service模块");
        assertTrue(hasRepositoryModule, "应识别出repository模块");
        assertTrue(hasControllerModule, "应识别出controller模块");
    }
    
    @Test
    @DisplayName("测试分析架构层级")
    void testAnalyzeArchitecturalLayers() {
        // 创建模拟的文件解析结果
        FileParseResult controllerFile = FileParseResult.builder()
                .filename("UserController.java")
                .packageName("com.example.controller")
                .classDefinitions(singletonList(
                        ClassDefinition.builder()
                                .name("UserController")
                                .packageName("com.example.controller")
                                .fullyQualifiedName("com.example.controller.UserController")
                                .build()
                ))
                .build();
                
        FileParseResult serviceFile = FileParseResult.builder()
                .filename("UserService.java")
                .packageName("com.example.service")
                .classDefinitions(singletonList(
                        ClassDefinition.builder()
                                .name("UserService")
                                .packageName("com.example.service")
                                .fullyQualifiedName("com.example.service.UserService")
                                .type(ClassType.INTERFACE)
                                .build()
                ))
                .build();
                
        FileParseResult serviceImplFile = FileParseResult.builder()
                .filename("UserServiceImpl.java")
                .packageName("com.example.service.impl")
                .classDefinitions(singletonList(
                        ClassDefinition.builder()
                                .name("UserServiceImpl")
                                .packageName("com.example.service.impl")
                                .fullyQualifiedName("com.example.service.impl.UserServiceImpl")
                                .build()
                ))
                .build();
                
        FileParseResult repositoryFile = FileParseResult.builder()
                .filename("UserRepository.java")
                .packageName("com.example.repository")
                .classDefinitions(singletonList(
                        ClassDefinition.builder()
                                .name("UserRepository")
                                .packageName("com.example.repository")
                                .fullyQualifiedName("com.example.repository.UserRepository")
                                .type(ClassType.INTERFACE)
                                .build()
                ))
                .build();
                
        FileParseResult modelFile = FileParseResult.builder()
                .filename("User.java")
                .packageName("com.example.model")
                .classDefinitions(singletonList(
                        ClassDefinition.builder()
                                .name("User")
                                .packageName("com.example.model")
                                .fullyQualifiedName("com.example.model.User")
                                .build()
                ))
                .build();
        
        List<FileParseResult> parseResults = Arrays.asList(
                controllerFile, serviceFile, serviceImplFile, repositoryFile, modelFile);
        
        // 分析架构层级
        ArchitecturalLayers layers = analyzer.analyzeArchitecturalLayers(parseResults);
        
        // 验证结果
        assertNotNull(layers, "架构层级不应为空");
        assertTrue(layers.getLayers().size() >= 3, "应解析出至少3个架构层级");
        
        // 验证是否识别出主要层级
        boolean hasControllerLayer = false;
        boolean hasServiceLayer = false;
        boolean hasRepositoryLayer = false;
        boolean hasModelLayer = false;
        
        for (ArchitecturalLayer layer : layers.getLayers()) {
            switch (layer.getName()) {
                case "Controller Layer":
                case "Presentation Layer":
                case "Web Layer":
                case "REST Layer":
                    hasControllerLayer = true;
                    break;
                case "Service Layer":
                case "Business Layer":
                    hasServiceLayer = true;
                    break;
                case "Repository Layer":
                case "Data Access Layer":
                case "DAO Layer":
                    hasRepositoryLayer = true;
                    break;
                case "Model Layer":
                case "Domain Layer":
                case "Entity Layer":
                    hasModelLayer = true;
                    break;
            }
        }
        
        assertTrue(hasControllerLayer, "应识别出Controller层");
        assertTrue(hasServiceLayer, "应识别出Service层");
        assertTrue(hasRepositoryLayer, "应识别出Repository层");
        assertTrue(hasModelLayer, "应识别出Model层");
    }

    /**
     * 创建单元素列表的辅助方法，兼容Java 8
     */
    private <T> List<T> singletonList(T element) {
        return Collections.singletonList(element);
    }
}
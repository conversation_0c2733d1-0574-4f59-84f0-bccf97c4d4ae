package com.archscope.domain.model.servicediscovery;

import com.archscope.domain.valueobject.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 能力实体测试
 */
class CapabilityTest {

    private ServiceId serviceId;
    private String capabilityName;
    private String capabilityDescription;
    private Version version;
    private Set<CapabilityExample> examples;
    private Set<Tag> tags;

    @BeforeEach
    void setUp() {
        serviceId = ServiceId.createNew();
        capabilityName = "用户认证";
        capabilityDescription = "提供用户认证功能";
        version = Version.of("1.0.0");
        examples = new HashSet<>();
        examples.add(CapabilityExample.of("登录示例", "用户登录示例", 
                "{\"username\":\"user\",\"password\":\"pass\"}", 
                "{\"token\":\"jwt_token\",\"expires\":3600}"));
        tags = new HashSet<>();
        tags.add(Tag.of("authentication"));
        tags.add(Tag.of("security"));
    }

    @Test
    void testCreateCapability() {
        // Act
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, examples, tags
        );

        // Assert
        assertNotNull(capability);
        assertNotNull(capability.getId());
        assertEquals(serviceId, capability.getServiceId());
        assertEquals(capabilityName, capability.getName());
        assertEquals(capabilityDescription, capability.getDescription());
        assertEquals(version, capability.getVersion());
        assertEquals(examples, capability.getExamples());
        assertEquals(tags, capability.getTags());
        assertFalse(capability.isDeprecated());
        assertNotNull(capability.getCreatedAt());
        assertNotNull(capability.getLastUpdatedAt());
    }

    @Test
    void testCreateCapabilityWithNullValues() {
        // Act
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, null, null
        );

        // Assert
        assertNotNull(capability);
        assertTrue(capability.getExamples().isEmpty());
        assertTrue(capability.getTags().isEmpty());
        assertFalse(capability.isDeprecated());
    }

    @Test
    void testRestoreCapability() {
        // Arrange
        CapabilityId capabilityId = CapabilityId.createNew();
        Instant createdAt = Instant.now().minusSeconds(3600);
        Instant lastUpdatedAt = Instant.now().minusSeconds(1800);
        boolean deprecated = true;

        // Act
        Capability capability = Capability.restore(
                capabilityId, serviceId, capabilityName, capabilityDescription,
                version, examples, tags, deprecated, createdAt, lastUpdatedAt
        );

        // Assert
        assertNotNull(capability);
        assertEquals(capabilityId, capability.getId());
        assertEquals(serviceId, capability.getServiceId());
        assertEquals(capabilityName, capability.getName());
        assertEquals(capabilityDescription, capability.getDescription());
        assertEquals(version, capability.getVersion());
        assertEquals(examples, capability.getExamples());
        assertEquals(tags, capability.getTags());
        assertTrue(capability.isDeprecated());
        assertEquals(createdAt, capability.getCreatedAt());
        assertEquals(lastUpdatedAt, capability.getLastUpdatedAt());
    }

    @Test
    void testRestoreCapabilityWithNullValues() {
        // Arrange
        CapabilityId capabilityId = CapabilityId.createNew();
        Instant createdAt = Instant.now().minusSeconds(3600);
        Instant lastUpdatedAt = Instant.now().minusSeconds(1800);

        // Act
        Capability capability = Capability.restore(
                capabilityId, serviceId, capabilityName, capabilityDescription,
                version, null, null, false, createdAt, lastUpdatedAt
        );

        // Assert
        assertNotNull(capability);
        assertTrue(capability.getExamples().isEmpty());
        assertTrue(capability.getTags().isEmpty());
        assertFalse(capability.isDeprecated());
    }

    @Test
    void testUpdateCapability() {
        // Arrange
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, examples, tags
        );
        Instant originalLastUpdatedAt = capability.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String newName = "更新后的能力";
        String newDescription = "更新后的描述";
        Version newVersion = Version.of("2.0.0");
        Set<Tag> newTags = Collections.singleton(Tag.of("updated"));

        // Act
        capability.update(newName, newDescription, newVersion, newTags);

        // Assert
        assertEquals(newName, capability.getName());
        assertEquals(newDescription, capability.getDescription());
        assertEquals(newVersion, capability.getVersion());
        assertEquals(newTags, capability.getTags());
        assertTrue(capability.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testUpdateCapabilityWithNullTags() {
        // Arrange
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, examples, tags
        );
        Set<Tag> originalTags = new HashSet<>(capability.getTags());

        // Act
        capability.update(capabilityName, capabilityDescription, version, null);

        // Assert
        assertEquals(originalTags, capability.getTags()); // Tags should remain unchanged
    }

    @Test
    void testAddExample() {
        // Arrange
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, Collections.emptySet(), tags
        );
        CapabilityExample newExample = CapabilityExample.of(
                "注册示例", "用户注册示例",
                "{\"username\":\"newuser\",\"email\":\"<EMAIL>\"}",
                "{\"id\":123,\"message\":\"注册成功\"}"
        );
        Instant originalLastUpdatedAt = capability.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Act
        capability.addExample(newExample);

        // Assert
        assertTrue(capability.getExamples().contains(newExample));
        assertTrue(capability.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testRemoveExample() {
        // Arrange
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, examples, tags
        );
        CapabilityExample exampleToRemove = examples.iterator().next();
        Instant originalLastUpdatedAt = capability.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Act
        capability.removeExample(exampleToRemove);

        // Assert
        assertFalse(capability.getExamples().contains(exampleToRemove));
        assertTrue(capability.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testMarkAsDeprecated() {
        // Arrange
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, examples, tags
        );
        assertFalse(capability.isDeprecated());
        Instant originalLastUpdatedAt = capability.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Act
        capability.markAsDeprecated();

        // Assert
        assertTrue(capability.isDeprecated());
        assertTrue(capability.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testMarkAsNotDeprecated() {
        // Arrange
        Capability capability = Capability.restore(
                CapabilityId.createNew(), serviceId, capabilityName, capabilityDescription,
                version, examples, tags, true, Instant.now(), Instant.now()
        );
        assertTrue(capability.isDeprecated());
        Instant originalLastUpdatedAt = capability.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Act
        capability.markAsNotDeprecated();

        // Assert
        assertFalse(capability.isDeprecated());
        assertTrue(capability.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testGetExamplesReturnsUnmodifiableSet() {
        // Arrange
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, examples, tags
        );

        // Act & Assert
        Set<CapabilityExample> returnedExamples = capability.getExamples();
        assertThrows(UnsupportedOperationException.class, () -> {
            returnedExamples.add(CapabilityExample.of("test", "test", "test", "test"));
        });
    }

    @Test
    void testGetTagsReturnsUnmodifiableSet() {
        // Arrange
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, examples, tags
        );

        // Act & Assert
        Set<Tag> returnedTags = capability.getTags();
        assertThrows(UnsupportedOperationException.class, () -> {
            returnedTags.add(Tag.of("new-tag"));
        });
    }

    @Test
    void testEqualsAndHashCode() {
        // Arrange
        CapabilityId capabilityId = CapabilityId.createNew();
        Capability capability1 = Capability.restore(
                capabilityId, serviceId, capabilityName, capabilityDescription,
                version, examples, tags, false, Instant.now(), Instant.now()
        );
        Capability capability2 = Capability.restore(
                capabilityId, ServiceId.createNew(), "不同的名称", "不同的描述",
                Version.of("2.0.0"), Collections.emptySet(), Collections.emptySet(),
                true, Instant.now(), Instant.now()
        );
        Capability capability3 = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, examples, tags
        );

        // Assert
        assertEquals(capability1, capability2); // Same ID
        assertNotEquals(capability1, capability3); // Different ID
        assertEquals(capability1.hashCode(), capability2.hashCode());
        assertNotEquals(capability1.hashCode(), capability3.hashCode());
    }

    @Test
    void testToString() {
        // Arrange
        Capability capability = Capability.create(
                serviceId, capabilityName, capabilityDescription,
                version, examples, tags
        );

        // Act
        String toString = capability.toString();

        // Assert
        assertNotNull(toString);
        assertTrue(toString.contains("Capability{"));
        assertTrue(toString.contains("id=" + capability.getId()));
        assertTrue(toString.contains("serviceId=" + serviceId));
        assertTrue(toString.contains("name='" + capabilityName + "'"));
        assertTrue(toString.contains("version=" + version));
        assertTrue(toString.contains("deprecated=" + capability.isDeprecated()));
    }
}
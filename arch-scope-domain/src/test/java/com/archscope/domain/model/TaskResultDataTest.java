package com.archscope.domain.model;

import org.junit.jupiter.api.Test;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

/**
 * TaskResultData模型测试
 */
class TaskResultDataTest {

    @Test
    void testDocumentResultCreation() {
        // 测试DocumentResult的创建
        TaskResultData.DocumentResult docResult = TaskResultData.DocumentResult.builder()
                .documentType("README")
                .documentTitle("项目说明")
                .documentContent("# 项目说明\n\n这是一个测试项目。")
                .filePath("README.md")
                .status("SUCCESS")
                .errorMessage(null)
                .build();

        assertNotNull(docResult);
        assertEquals("README", docResult.getDocumentType());
        assertEquals("项目说明", docResult.getDocumentTitle());
        assertEquals("# 项目说明\n\n这是一个测试项目。", docResult.getDocumentContent());
        assertEquals("README.md", docResult.getFilePath());
        assertEquals("SUCCESS", docResult.getStatus());
        assertNull(docResult.getErrorMessage());
    }

    @Test
    void testTaskResultDataCreation() {
        // 创建文档结果
        TaskResultData.DocumentResult readmeResult = TaskResultData.DocumentResult.builder()
                .documentType("README")
                .documentTitle("项目说明")
                .documentContent("# 项目说明\n\n这是一个测试项目。")
                .filePath("README.md")
                .status("SUCCESS")
                .build();

        TaskResultData.DocumentResult archResult = TaskResultData.DocumentResult.builder()
                .documentType("ARCHITECTURE")
                .documentTitle("架构设计")
                .documentContent("# 架构设计\n\n系统采用DDD分层架构。")
                .filePath("docs/architecture.md")
                .status("SUCCESS")
                .build();

        // 创建任务结果
        TaskResultData taskResult = TaskResultData.builder()
                .results(Arrays.asList(readmeResult, archResult))
                .commitId("abc123def456")
                .build();

        assertNotNull(taskResult);
        assertNotNull(taskResult.getResults());
        assertEquals(2, taskResult.getResults().size());
        assertEquals("abc123def456", taskResult.getCommitId());

        // 验证第一个文档结果
        TaskResultData.DocumentResult firstResult = taskResult.getResults().get(0);
        assertEquals("README", firstResult.getDocumentType());
        assertEquals("SUCCESS", firstResult.getStatus());

        // 验证第二个文档结果
        TaskResultData.DocumentResult secondResult = taskResult.getResults().get(1);
        assertEquals("ARCHITECTURE", secondResult.getDocumentType());
        assertEquals("SUCCESS", secondResult.getStatus());
    }

    @Test
    void testFailedDocumentResult() {
        // 测试失败的文档结果
        TaskResultData.DocumentResult failedResult = TaskResultData.DocumentResult.builder()
                .documentType("API")
                .documentTitle("API文档")
                .documentContent(null)
                .filePath("docs/api.md")
                .status("FAILED")
                .errorMessage("生成API文档时发生错误")
                .build();

        assertNotNull(failedResult);
        assertEquals("API", failedResult.getDocumentType());
        assertEquals("FAILED", failedResult.getStatus());
        assertEquals("生成API文档时发生错误", failedResult.getErrorMessage());
        assertNull(failedResult.getDocumentContent());
    }

    @Test
    void testEmptyTaskResult() {
        // 测试空的任务结果
        TaskResultData emptyResult = TaskResultData.builder()
                .results(Arrays.asList())
                .commitId("empty123")
                .build();

        assertNotNull(emptyResult);
        assertNotNull(emptyResult.getResults());
        assertTrue(emptyResult.getResults().isEmpty());
        assertEquals("empty123", emptyResult.getCommitId());
    }
}

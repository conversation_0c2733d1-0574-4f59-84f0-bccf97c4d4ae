package com.archscope.domain.model.parser;

import static org.junit.jupiter.api.Assertions.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;

import com.archscope.domain.external.llm.LlmService;
import com.archscope.domain.model.parser.TraditionalParserRegistry;
import com.archscope.domain.model.parser.ParseResultMerger;

import java.io.File;

/**
 * 代码解析器测试类
 */
public class CodeParserTest {

    @Mock
    private LlmService llmService;
    @Mock
    private TraditionalParserRegistry traditionalParserRegistry;
    @Mock
    private ParseResultMerger parseResultMerger;

    @InjectMocks
    private DefaultCodeParser codeParser;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    @DisplayName("测试代码解析器初始化")
    void testCodeParserInitialization() {
        assertNotNull(codeParser, "代码解析器不应为空");
    }

    @Test
    @DisplayName("测试识别Java文件")
    void testIdentifyJavaFile() {
        LanguageType type = codeParser.identifyLanguage("Example.java");
        assertEquals(LanguageType.JAVA, type, "应该正确识别Java文件");
    }

    @Test
    @DisplayName("测试识别JavaScript文件")
    void testIdentifyJavaScriptFile() {
        LanguageType type = codeParser.identifyLanguage("script.js");
        assertEquals(LanguageType.JAVASCRIPT, type, "应该正确识别JavaScript文件");
    }

    @Test
    @DisplayName("测试识别TypeScript文件")
    void testIdentifyTypeScriptFile() {
        LanguageType type = codeParser.identifyLanguage("component.ts");
        assertEquals(LanguageType.TYPESCRIPT, type, "应该正确识别TypeScript文件");
    }

    @Test
    @DisplayName("测试识别未知文件类型")
    void testIdentifyUnknownFileType() {
        LanguageType type = codeParser.identifyLanguage("unknown.xyz");
        assertEquals(LanguageType.UNKNOWN, type, "无法识别的文件类型应返回UNKNOWN");
    }

    @Test
    @DisplayName("测试识别HTML文件")
    void testIdentifyHtmlFile() {
        LanguageType type = codeParser.identifyLanguage("index.html");
        assertEquals(LanguageType.HTML, type, "应该正确识别HTML文件");

        type = codeParser.identifyLanguage("page.htm");
        assertEquals(LanguageType.HTML, type, "应该正确识别HTM文件");
    }

    @Test
    @DisplayName("测试识别CSS文件")
    void testIdentifyCssFile() {
        LanguageType type = codeParser.identifyLanguage("styles.css");
        assertEquals(LanguageType.CSS, type, "应该正确识别CSS文件");
    }

    @Test
    @DisplayName("测试识别XML文件")
    void testIdentifyXmlFile() {
        LanguageType type = codeParser.identifyLanguage("config.xml");
        assertEquals(LanguageType.XML, type, "应该正确识别XML文件");
    }

    @Test
    @DisplayName("测试识别JSON文件")
    void testIdentifyJsonFile() {
        LanguageType type = codeParser.identifyLanguage("data.json");
        assertEquals(LanguageType.JSON, type, "应该正确识别JSON文件");
    }

    @Test
    @DisplayName("测试解析Java文件")
    void testParseJavaFile() {
        String code = "package com.example;\n\npublic class Example {\n    public void method() {}\n}";

        // 模拟LLM服务返回成功结果
        FileParseResult mockResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .classDefinitions(java.util.Collections.singletonList(
                    ClassDefinition.builder()
                        .name("Example")
                        .build()
                ))
                .successful(true)
                .build();

        when(llmService.parseCodeWithLlm("Example.java", code, "JAVA")).thenReturn(mockResult);

        FileParseResult result = codeParser.parseFile("Example.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertEquals("com.example", result.getPackageName(), "应正确解析包名");
        assertEquals(1, result.getClassDefinitions().size(), "应解析出一个类定义");
        assertEquals("Example", result.getClassDefinitions().get(0).getName(), "应正确解析类名");

        verify(llmService).parseCodeWithLlm("Example.java", code, "JAVA");
    }

    @Test
    @DisplayName("测试解析包含导入的Java文件")
    void testParseJavaFileWithImports() {
        String code = "package com.example;\n\nimport java.util.List;\nimport java.util.Map;\n\npublic class Example {\n}";

        // 模拟LLM服务返回成功结果
        FileParseResult mockResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .imports(java.util.Arrays.asList("java.util.List", "java.util.Map"))
                .classDefinitions(java.util.Collections.singletonList(
                    ClassDefinition.builder()
                        .name("Example")
                        .build()
                ))
                .successful(true)
                .build();

        when(llmService.parseCodeWithLlm("Example.java", code, "JAVA")).thenReturn(mockResult);

        FileParseResult result = codeParser.parseFile("Example.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertEquals(2, result.getImports().size(), "应解析出两个导入");
        assertTrue(result.getImports().contains("java.util.List"), "应包含List导入");
        assertTrue(result.getImports().contains("java.util.Map"), "应包含Map导入");

        verify(llmService).parseCodeWithLlm("Example.java", code, "JAVA");
    }

    @Test
    @DisplayName("测试解析Java类的依赖关系")
    void testParseJavaDependencies() {
        String code = "package com.example;\n\nimport com.other.Service;\n\npublic class Example {\n    private Service service;\n    \n    public void useService() {\n        service.doSomething();\n    }\n}";

        // 模拟LLM服务返回成功结果
        DependencyRelation serviceDependency = DependencyRelation.builder()
                .sourceClass("com.example.Example")
                .targetClass("com.other.Service")
                .type(DependencyType.IMPORT)
                .strength(1)
                .location("import")
                .build();

        FileParseResult mockResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .imports(java.util.Collections.singletonList("com.other.Service"))
                .classDefinitions(java.util.Collections.singletonList(
                    ClassDefinition.builder()
                        .name("Example")
                        .build()
                ))
                .dependencies(java.util.Collections.singletonList(serviceDependency))
                .successful(true)
                .build();

        when(llmService.parseCodeWithLlm("Example.java", code, "JAVA")).thenReturn(mockResult);

        FileParseResult result = codeParser.parseFile("Example.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.getDependencies().size() >= 1, "应至少解析出一个依赖");

        // 检查是否包含 Service 类的依赖
        boolean hasServiceDependency = false;
        for (DependencyRelation dependency : result.getDependencies()) {
            if ("com.other.Service".equals(dependency.getTargetClass())) {
                hasServiceDependency = true;
                break;
            }
        }
        assertTrue(hasServiceDependency, "应解析出对Service类的依赖");

        verify(llmService).parseCodeWithLlm("Example.java", code, "JAVA");
    }

    @Test
    @DisplayName("测试解析包含内部类的Java文件")
    void testParseJavaFileWithInnerClass() {
        String code = "package com.example;\n\n" +
                "public class OuterClass {\n" +
                "    private int outerField;\n\n" +
                "    public class InnerClass {\n" +
                "        private int innerField;\n" +
                "        \n" +
                "        public void innerMethod() {}\n" +
                "    }\n" +
                "    \n" +
                "    public void outerMethod() {}\n" +
                "}";

        // 创建内部类
        ClassDefinition innerClass = ClassDefinition.builder()
                .name("InnerClass")
                .type(ClassType.CLASS)
                .accessModifier(AccessModifier.PUBLIC)
                .build();

        // 创建外部类
        ClassDefinition outerClassDef = ClassDefinition.builder()
                .name("OuterClass")
                .packageName("com.example")
                .fullyQualifiedName("com.example.OuterClass")
                .type(ClassType.CLASS)
                .accessModifier(AccessModifier.PUBLIC)
                .innerClasses(java.util.Collections.singletonList(innerClass))
                .build();

        // 模拟LLM服务返回成功结果
        FileParseResult mockResult = FileParseResult.builder()
                .filename("OuterClass.java")
                .filePath("OuterClass.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .classDefinitions(java.util.Collections.singletonList(outerClassDef))
                .successful(true)
                .build();

        when(llmService.parseCodeWithLlm("OuterClass.java", code, "JAVA")).thenReturn(mockResult);

        FileParseResult result = codeParser.parseFile("OuterClass.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertEquals(1, result.getClassDefinitions().size(), "应解析出一个外部类定义");

        ClassDefinition outerClass = result.getClassDefinitions().get(0);
        assertEquals("OuterClass", outerClass.getName(), "应正确解析外部类名");

        assertEquals(1, outerClass.getInnerClasses().size(), "应解析出一个内部类");
        ClassDefinition innerClassResult = outerClass.getInnerClasses().get(0);
        assertEquals("InnerClass", innerClassResult.getName(), "应正确解析内部类名");

        verify(llmService).parseCodeWithLlm("OuterClass.java", code, "JAVA");
    }

    @Test
    @DisplayName("测试解析方法定义")
    void testParseMethodDefinitions() {
        String code = "package com.example;\n\n" +
                "public class MethodTest {\n" +
                "    // 构造方法\n" +
                "    public MethodTest() {}\n\n" +
                "    // 普通方法\n" +
                "    public void doSomething() {}\n\n" +
                "    // 带参数的方法\n" +
                "    public int calculate(int a, int b) { return a + b; }\n\n" +
                "    // 静态方法\n" +
                "    public static String staticMethod() { return \"static\"; }\n\n" +
                "    // 私有方法\n" +
                "    private void privateMethod() {}\n" +
                "}";

        // 创建方法定义
        MethodDefinition constructor = MethodDefinition.builder()
                .name("MethodTest")
                .returnType("")
                .accessModifier(AccessModifier.PUBLIC)
                .isConstructor(true)
                .parameters(java.util.Collections.emptyList())
                .build();

        MethodDefinition doSomething = MethodDefinition.builder()
                .name("doSomething")
                .returnType("void")
                .accessModifier(AccessModifier.PUBLIC)
                .parameters(java.util.Collections.emptyList())
                .build();

        MethodDefinition calculate = MethodDefinition.builder()
                .name("calculate")
                .returnType("int")
                .accessModifier(AccessModifier.PUBLIC)
                .parameters(java.util.Arrays.asList(
                    ParameterDefinition.builder().name("a").type("int").build(),
                    ParameterDefinition.builder().name("b").type("int").build()
                ))
                .build();

        MethodDefinition staticMethod = MethodDefinition.builder()
                .name("staticMethod")
                .returnType("String")
                .accessModifier(AccessModifier.PUBLIC)
                .isStatic(true)
                .parameters(java.util.Collections.emptyList())
                .build();

        MethodDefinition privateMethod = MethodDefinition.builder()
                .name("privateMethod")
                .returnType("void")
                .accessModifier(AccessModifier.PRIVATE)
                .parameters(java.util.Collections.emptyList())
                .build();

        // 创建类定义
        ClassDefinition classDef = ClassDefinition.builder()
                .name("MethodTest")
                .packageName("com.example")
                .fullyQualifiedName("com.example.MethodTest")
                .type(ClassType.CLASS)
                .accessModifier(AccessModifier.PUBLIC)
                .methods(java.util.Arrays.asList(constructor, doSomething, calculate, staticMethod, privateMethod))
                .build();

        // 模拟LLM服务返回成功结果
        FileParseResult mockResult = FileParseResult.builder()
                .filename("MethodTest.java")
                .filePath("MethodTest.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .classDefinitions(java.util.Collections.singletonList(classDef))
                .successful(true)
                .build();

        when(llmService.parseCodeWithLlm("MethodTest.java", code, "JAVA")).thenReturn(mockResult);

        FileParseResult result = codeParser.parseFile("MethodTest.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertEquals(1, result.getClassDefinitions().size(), "应解析出一个类定义");

        ClassDefinition resultClassDef = result.getClassDefinitions().get(0);
        assertEquals(5, resultClassDef.getMethods().size(), "应解析出五个方法");

        // 验证构造方法
        boolean hasConstructor = resultClassDef.getMethods().stream()
                .anyMatch(m -> m.getName().equals("MethodTest") && m.isConstructor());
        assertTrue(hasConstructor, "应解析出构造方法");

        // 验证普通方法
        boolean hasDoSomething = resultClassDef.getMethods().stream()
                .anyMatch(m -> m.getName().equals("doSomething") && m.getParameters().isEmpty());
        assertTrue(hasDoSomething, "应解析出doSomething方法");

        // 验证带参数的方法
        boolean hasCalculate = resultClassDef.getMethods().stream()
                .anyMatch(m -> m.getName().equals("calculate") && m.getParameters().size() == 2);
        assertTrue(hasCalculate, "应解析出calculate方法及其参数");

        // 验证静态方法
        boolean hasStaticMethod = resultClassDef.getMethods().stream()
                .anyMatch(m -> m.getName().equals("staticMethod") && m.isStatic());
        assertTrue(hasStaticMethod, "应解析出静态方法");

        // 验证私有方法
        boolean hasPrivateMethod = resultClassDef.getMethods().stream()
                .anyMatch(m -> m.getName().equals("privateMethod") &&
                         m.getAccessModifier() == AccessModifier.PRIVATE);
        assertTrue(hasPrivateMethod, "应解析出私有方法");

        verify(llmService).parseCodeWithLlm("MethodTest.java", code, "JAVA");
    }

    @Test
    @DisplayName("测试解析字段定义")
    void testParseFieldDefinitions() {
        String code = "package com.example;\n\n" +
                "public class FieldTest {\n" +
                "    // 普通字段\n" +
                "    private int count;\n\n" +
                "    // 静态字段\n" +
                "    private static String name;\n\n" +
                "    // 常量\n" +
                "    public static final double PI = 3.14159;\n\n" +
                "    // 带初始值的字段\n" +
                "    protected boolean active = true;\n" +
                "}";

        // 创建字段定义
        FieldDefinition countField = FieldDefinition.builder()
                .name("count")
                .type("int")
                .accessModifier(AccessModifier.PRIVATE)
                .build();

        FieldDefinition nameField = FieldDefinition.builder()
                .name("name")
                .type("String")
                .accessModifier(AccessModifier.PRIVATE)
                .isStatic(true)
                .build();

        FieldDefinition piField = FieldDefinition.builder()
                .name("PI")
                .type("double")
                .accessModifier(AccessModifier.PUBLIC)
                .isStatic(true)
                .isConstant(true)
                .initialValue("3.14159")
                .build();

        FieldDefinition activeField = FieldDefinition.builder()
                .name("active")
                .type("boolean")
                .accessModifier(AccessModifier.PROTECTED)
                .initialValue("true")
                .build();

        // 创建类定义
        ClassDefinition classDef = ClassDefinition.builder()
                .name("FieldTest")
                .packageName("com.example")
                .fullyQualifiedName("com.example.FieldTest")
                .type(ClassType.CLASS)
                .accessModifier(AccessModifier.PUBLIC)
                .fields(java.util.Arrays.asList(countField, nameField, piField, activeField))
                .build();

        // 模拟LLM服务返回成功结果
        FileParseResult mockResult = FileParseResult.builder()
                .filename("FieldTest.java")
                .filePath("FieldTest.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .classDefinitions(java.util.Collections.singletonList(classDef))
                .successful(true)
                .build();

        when(llmService.parseCodeWithLlm("FieldTest.java", code, "JAVA")).thenReturn(mockResult);

        FileParseResult result = codeParser.parseFile("FieldTest.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertEquals(1, result.getClassDefinitions().size(), "应解析出一个类定义");

        ClassDefinition resultClassDef = result.getClassDefinitions().get(0);
        assertEquals(4, resultClassDef.getFields().size(), "应解析出四个字段");

        // 验证普通字段
        boolean hasCount = resultClassDef.getFields().stream()
                .anyMatch(f -> f.getName().equals("count") && f.getType().equals("int") &&
                         f.getAccessModifier() == AccessModifier.PRIVATE);
        assertTrue(hasCount, "应解析出count字段");

        // 验证静态字段
        boolean hasName = resultClassDef.getFields().stream()
                .anyMatch(f -> f.getName().equals("name") && f.getType().equals("String") &&
                         f.isStatic());
        assertTrue(hasName, "应解析出静态name字段");

        // 验证常量
        boolean hasPI = resultClassDef.getFields().stream()
                .anyMatch(f -> f.getName().equals("PI") && f.getType().equals("double") &&
                         f.isConstant() && f.getInitialValue() != null);
        assertTrue(hasPI, "应解析出PI常量及其初始值");

        // 验证带初始值的字段
        boolean hasActive = resultClassDef.getFields().stream()
                .anyMatch(f -> f.getName().equals("active") && f.getType().equals("boolean") &&
                         f.getAccessModifier() == AccessModifier.PROTECTED &&
                         f.getInitialValue() != null);
        assertTrue(hasActive, "应解析出active字段及其初始值");

        verify(llmService).parseCodeWithLlm("FieldTest.java", code, "JAVA");
    }

    @Test
    @DisplayName("测试解析JavaScript文件")
    void testParseJavaScriptFile() {
        String code = "// 导入\n" +
                "import React from 'react';\n" +
                "import { useState } from 'react';\n\n" +
                "// 类组件\n" +
                "class Counter extends React.Component {\n" +
                "  constructor(props) {\n" +
                "    super(props);\n" +
                "    this.state = { count: 0 };\n" +
                "  }\n\n" +
                "  increment() {\n" +
                "    this.setState({ count: this.state.count + 1 });\n" +
                "  }\n\n" +
                "  render() {\n" +
                "    return <div>{this.state.count}</div>;\n" +
                "  }\n" +
                "}\n\n" +
                "export default Counter;";

        // 创建方法定义
        MethodDefinition constructor = MethodDefinition.builder()
                .name("constructor")
                .parameters(java.util.Collections.singletonList(
                    ParameterDefinition.builder().name("props").build()
                ))
                .build();

        MethodDefinition increment = MethodDefinition.builder()
                .name("increment")
                .parameters(java.util.Collections.emptyList())
                .build();

        MethodDefinition render = MethodDefinition.builder()
                .name("render")
                .parameters(java.util.Collections.emptyList())
                .build();

        // 创建类定义
        ClassDefinition counterClass = ClassDefinition.builder()
                .name("Counter")
                .type(ClassType.CLASS)
                .superClass("React.Component")
                .methods(java.util.Arrays.asList(constructor, increment, render))
                .build();

        // 模拟LLM服务返回成功结果
        FileParseResult mockResult = FileParseResult.builder()
                .filename("Counter.js")
                .filePath("Counter.js")
                .languageType(LanguageType.JAVASCRIPT)
                .imports(java.util.Arrays.asList("react", "react.useState"))
                .classDefinitions(java.util.Collections.singletonList(counterClass))
                .successful(true)
                .build();

        when(llmService.parseCodeWithLlm("Counter.js", code, "JAVASCRIPT")).thenReturn(mockResult);

        FileParseResult result = codeParser.parseFile("Counter.js", code);

        assertNotNull(result, "解析结果不应为空");
        assertEquals(LanguageType.JAVASCRIPT, result.getLanguageType(), "应识别为JavaScript文件");

        // 验证导入解析
        assertTrue(result.getImports().size() >= 1, "应至少解析出一个导入");
        assertTrue(result.getImports().contains("react"), "应解析出react导入");

        // 验证类定义解析
        assertEquals(1, result.getClassDefinitions().size(), "应解析出一个类定义");
        ClassDefinition classDef = result.getClassDefinitions().get(0);
        assertEquals("Counter", classDef.getName(), "应正确解析类名");
        assertEquals("React.Component", classDef.getSuperClass(), "应正确解析父类");

        // 验证方法解析
        assertEquals(3, classDef.getMethods().size(), "应解析出三个方法");
        assertTrue(classDef.getMethods().stream().anyMatch(m -> m.getName().equals("constructor")), "应解析出constructor方法");
        assertTrue(classDef.getMethods().stream().anyMatch(m -> m.getName().equals("increment")), "应解析出increment方法");
        assertTrue(classDef.getMethods().stream().anyMatch(m -> m.getName().equals("render")), "应解析出render方法");

        verify(llmService).parseCodeWithLlm("Counter.js", code, "JAVASCRIPT");
    }
}
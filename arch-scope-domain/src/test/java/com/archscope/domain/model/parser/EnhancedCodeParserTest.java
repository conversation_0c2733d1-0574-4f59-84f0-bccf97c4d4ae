package com.archscope.domain.model.parser;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Disabled;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;

import com.archscope.domain.external.llm.LlmService;
import com.archscope.domain.model.parser.DependencyRelation;
import com.archscope.domain.model.parser.TraditionalCodeParser;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * 增强代码解析器测试类
 * 测试DefaultCodeParser与传统解析器和结果合并器的集成
 */
public class EnhancedCodeParserTest {

    @Mock
    private LlmService llmService;

    @Mock
    private TraditionalCodeParser javaTraditionalParser;

    @InjectMocks
    private DefaultCodeParser codeParser;

    private FileParseResult llmResult;
    private FileParseResult traditionalResult;
    private FileParseResult mergedResult;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        // Update constructor call
        codeParser = new DefaultCodeParser(llmService);

        // 创建测试数据
        llmResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .fileComment("This is a sample class")
                .imports(Arrays.asList("java.util.List", "java.util.Map"))
                .classDefinitions(Arrays.asList(
                        ClassDefinition.builder()
                                .name("Example")
                                .packageName("com.example")
                                .fullyQualifiedName("com.example.Example")
                                .type(ClassType.CLASS)
                                .accessModifier(AccessModifier.PUBLIC)
                                .build()
                ))
                .dependencies(new ArrayList<>())
                .successful(true)
                .build();

        traditionalResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .imports(Arrays.asList("java.util.List", "java.util.ArrayList"))
                .classDefinitions(Arrays.asList(
                        ClassDefinition.builder()
                                .name("Example")
                                .packageName("com.example")
                                .fullyQualifiedName("com.example.Example")
                                .type(ClassType.CLASS)
                                .accessModifier(AccessModifier.PUBLIC)
                                .superClass("Object")
                                .build()
                ))
                .dependencies(Arrays.asList(
                        DependencyRelation.builder()
                                .sourceClass("com.example.Example")
                                .targetClass("java.util.List")
                                .type(DependencyType.IMPORT)
                                .strength(1)
                                .location("import")
                                .build()
                ))
                .successful(true)
                .build();

        mergedResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("com/example/Example.java")
                .languageType(LanguageType.JAVA)
                .packageName("com.example")
                .fileComment("This is a sample class")
                .imports(Arrays.asList("java.util.List", "java.util.Map", "java.util.ArrayList"))
                .classDefinitions(Arrays.asList(
                        ClassDefinition.builder()
                                .name("Example")
                                .packageName("com.example")
                                .fullyQualifiedName("com.example.Example")
                                .type(ClassType.CLASS)
                                .accessModifier(AccessModifier.PUBLIC)
                                .superClass("Object")
                                .build()
                ))
                .dependencies(Arrays.asList(
                        DependencyRelation.builder()
                                .sourceClass("com.example.Example")
                                .targetClass("java.util.List")
                                .type(DependencyType.IMPORT)
                                .strength(1)
                                .location("import")
                                .build()
                ))
                .successful(true)
                .build();
    }

    @Test
    @DisplayName("测试增强代码解析器初始化")
    void testEnhancedCodeParserInitialization() {
        assertNotNull(codeParser, "增强代码解析器不应为空");
    }

    @Test
    @DisplayName("测试同时使用LLM解析和传统解析并合并结果")
    void testParseWithBothAndMerge() {
        // This test's premise is now invalid as traditional parser is not used alongside LLM directly in the simplified logic.
        // We can adapt it to test the LLM path or the Regex fallback path.
        // For now, let's test the LLM success path.

        String code = "package com.example;\n\npublic class Example {\n    public void method() {}\n}";
        when(llmService.parseCodeWithLlm("Example.java", code, "JAVA")).thenReturn(llmResult);
        // Ensure traditional parser mock is not expected to be called in this path
        // when(javaTraditionalParser.parseFile("Example.java", code)).thenReturn(traditionalResult);
        // Ensure merger mock is not expected to be called
        // when(parseResultMerger.merge(llmResult, traditionalResult, ParseResultMerger.MergeStrategy.SMART_MERGE)).thenReturn(mergedResult);

        FileParseResult result = codeParser.parseFile("Example.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertEquals(llmResult, result, "应返回LLM的结果"); // Expecting LLM result now

        verify(llmService).parseCodeWithLlm("Example.java", code, "JAVA");
        verify(javaTraditionalParser, never()).parseFile(anyString(), anyString()); // Verify traditional parser not called
        // Ensure merger is not verified
    }

    @Test
    @DisplayName("测试LLM解析失败时回退到传统解析") // Test name is now inaccurate, should be "回退到Regex解析"
    void testFallbackToRegexWhenLlmFails() { // Renamed test method
        String code = "package com.example;\n\npublic class Example {\n    public void method() {}\n}";
        FileParseResult failedLlmResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("Example.java") // Regex path might use filename as path
                .languageType(LanguageType.JAVA)
                .successful(false)
                .errorMessage("LLM解析错误")
                .build();

        when(llmService.parseCodeWithLlm("Example.java", code, "JAVA")).thenReturn(failedLlmResult);
        // Ensure traditional parser mock is not expected to be called
        // when(javaTraditionalParser.parseFile("Example.java", code)).thenReturn(traditionalResult);
        // Ensure merger mock is not expected to be called
        // when(parseResultMerger.merge(failedLlmResult, traditionalResult, ParseResultMerger.MergeStrategy.SMART_MERGE)).thenReturn(traditionalResult);

        FileParseResult result = codeParser.parseFile("Example.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "回退到Regex解析应该成功"); // Expecting Regex success
        // Add assertions specific to Regex output if needed
        assertEquals("com.example", result.getPackageName());
        assertTrue(result.getClassDefinitions().stream().anyMatch(c -> c.getName().equals("Example")));

        verify(llmService).parseCodeWithLlm("Example.java", code, "JAVA");
        verify(javaTraditionalParser, never()).parseFile(anyString(), anyString()); // Verify traditional parser not called
        // Ensure merger is not verified
    }

    @Test
    @DisplayName("测试传统解析失败时回退到LLM解析") // 修改为测试LLM解析路径
    void testFallbackToLlmWhenTraditionalFails() {
        String code = "package com.example;\n\npublic class Example {\n    public void method() {}\n}";
        // 模拟LLM解析成功
        when(llmService.parseCodeWithLlm("Example.java", code, "JAVA")).thenReturn(llmResult);

        FileParseResult result = codeParser.parseFile("Example.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertEquals(llmResult, result, "应返回LLM的结果");

        verify(llmService).parseCodeWithLlm("Example.java", code, "JAVA");
        verify(javaTraditionalParser, never()).parseFile(anyString(), anyString());
    }

    @Test
    @DisplayName("测试正则表达式解析路径")
    void testFallbackToRegexWhenBothFail() {
        String code = "package com.example;\n\npublic class Example {\n    public void method() {}\n}";
        // 模拟LLM解析失败
        FileParseResult failedLlmResult = FileParseResult.builder()
                .filename("Example.java")
                .filePath("Example.java")
                .languageType(LanguageType.JAVA)
                .successful(false)
                .errorMessage("LLM解析错误")
                .build();

        when(llmService.parseCodeWithLlm("Example.java", code, "JAVA")).thenReturn(failedLlmResult);

        FileParseResult result = codeParser.parseFile("Example.java", code);

        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "正则表达式解析应该成功");
        assertEquals("com.example", result.getPackageName(), "应正确解析包名");
        assertTrue(result.getClassDefinitions().stream().anyMatch(c -> c.getName().equals("Example")), "应正确解析类名");

        verify(llmService).parseCodeWithLlm("Example.java", code, "JAVA");
        verify(javaTraditionalParser, never()).parseFile(anyString(), anyString());
    }

    @Test
    @DisplayName("测试不同语言的解析路径")
    void testDifferentMergeStrategies() {
        // 测试TypeScript文件解析
        String tsCode = "interface User { id: number; name: string; }";
        FileParseResult tsResult = codeParser.parseFile("user.ts", tsCode);

        assertNotNull(tsResult, "TypeScript解析结果不应为空");
        assertTrue(tsResult.isSuccessful(), "TypeScript解析应该成功");
        assertEquals(LanguageType.TYPESCRIPT, tsResult.getLanguageType(), "应识别为TypeScript文件");

        // 测试Python文件解析
        String pyCode = "def hello():\n    print('Hello, World!')";
        FileParseResult pyResult = codeParser.parseFile("hello.py", pyCode);

        assertNotNull(pyResult, "Python解析结果不应为空");
        assertTrue(pyResult.isSuccessful(), "Python解析应该成功");
        assertEquals(LanguageType.PYTHON, pyResult.getLanguageType(), "应识别为Python文件");
    }

    @Test
    @DisplayName("测试不支持LLM解析的语言")
    void testUnsupportedLlmLanguage() {
        String code = "body { color: red; }";
        FileParseResult result = codeParser.parseFile("style.css", code);

        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "解析应该成功");
        assertEquals(LanguageType.CSS, result.getLanguageType(), "应识别为CSS文件");

        verify(llmService, never()).parseCodeWithLlm(anyString(), anyString(), anyString());
        verify(javaTraditionalParser, never()).parseFile(anyString(), anyString());
    }

    @Test
    @DisplayName("测试不支持传统解析的语言") // Test name is now inaccurate, should be "测试JS/TS语言(LLM优先)"
    void testJsTsLanguageLlmFirst() { // Renamed test method
        String code = "function example() { return true; }";
        // Setup a successful LLM result for JS
        FileParseResult jsLlmResult = FileParseResult.builder()
                .filename("example.js")
                .filePath("example.js")
                .languageType(LanguageType.JAVASCRIPT)
                .successful(true)
                // Add relevant details parsed by LLM if needed for assertion
                .build();

        when(llmService.parseCodeWithLlm("example.js", code, "JAVASCRIPT")).thenReturn(jsLlmResult);

        FileParseResult result = codeParser.parseFile("example.js", code);

        assertNotNull(result, "解析结果不应为空");
        assertEquals(jsLlmResult, result, "应返回LLM解析结果");

        verify(llmService).parseCodeWithLlm("example.js", code, "JAVASCRIPT");
        verify(javaTraditionalParser, never()).parseFile(anyString(), anyString());
    }

    // Keep helper method if needed
    private FileParseResult createDummyResult(String source) {
        // ... (keep as is or adapt)
         return FileParseResult.builder()
            .filename("dummy.java")
            .filePath("dummy.java")
            .languageType(LanguageType.JAVA)
            .successful(true)
            .build();
    }
}
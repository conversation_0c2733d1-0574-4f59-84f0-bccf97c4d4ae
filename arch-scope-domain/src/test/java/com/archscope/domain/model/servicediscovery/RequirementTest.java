package com.archscope.domain.model.servicediscovery;

import com.archscope.domain.valueobject.RequirementId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.time.Instant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 需求实体测试
 */
class RequirementTest {

    private String description;
    private List<String> requiredCapabilities;
    private RequirementPriority priority;

    @BeforeEach
    void setUp() {
        description = "需要一个用户管理系统";
        requiredCapabilities = Arrays.asList("用户认证", "用户注册", "密码重置");
        priority = RequirementPriority.HIGH;
    }

    @Test
    void testCreateRequirement() {
        // Act
        Requirement requirement = Requirement.create(description, requiredCapabilities, priority);

        // Assert
        assertNotNull(requirement);
        assertNotNull(requirement.getId());
        assertEquals(description, requirement.getDescription());
        assertEquals(requiredCapabilities, requirement.getRequiredCapabilities());
        assertEquals(priority, requirement.getPriority());
        assertNotNull(requirement.getCreatedAt());
        assertNotNull(requirement.getLastUpdatedAt());
    }

    @Test
    void testCreateRequirementWithNullValues() {
        // Act
        Requirement requirement = Requirement.create(description, null, null);

        // Assert
        assertNotNull(requirement);
        assertEquals(description, requirement.getDescription());
        assertTrue(requirement.getRequiredCapabilities().isEmpty());
        assertEquals(RequirementPriority.MEDIUM, requirement.getPriority());
    }

    @Test
    void testRestoreRequirement() {
        // Arrange
        RequirementId requirementId = RequirementId.createNew();
        Instant createdAt = Instant.now().minusSeconds(3600);
        Instant lastUpdatedAt = Instant.now().minusSeconds(1800);

        // Act
        Requirement requirement = Requirement.restore(
                requirementId, description, requiredCapabilities,
                priority, createdAt, lastUpdatedAt
        );

        // Assert
        assertNotNull(requirement);
        assertEquals(requirementId, requirement.getId());
        assertEquals(description, requirement.getDescription());
        assertEquals(requiredCapabilities, requirement.getRequiredCapabilities());
        assertEquals(priority, requirement.getPriority());
        assertEquals(createdAt, requirement.getCreatedAt());
        assertEquals(lastUpdatedAt, requirement.getLastUpdatedAt());
    }

    @Test
    void testRestoreRequirementWithNullValues() {
        // Arrange
        RequirementId requirementId = RequirementId.createNew();
        Instant createdAt = Instant.now().minusSeconds(3600);
        Instant lastUpdatedAt = Instant.now().minusSeconds(1800);

        // Act
        Requirement requirement = Requirement.restore(
                requirementId, description, null,
                null, createdAt, lastUpdatedAt
        );

        // Assert
        assertNotNull(requirement);
        assertTrue(requirement.getRequiredCapabilities().isEmpty());
        assertEquals(RequirementPriority.MEDIUM, requirement.getPriority());
    }

    @Test
    void testUpdateRequirement() {
        // Arrange
        Requirement requirement = Requirement.create(description, requiredCapabilities, priority);
        Instant originalLastUpdatedAt = requirement.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        String newDescription = "更新后的需求描述";
        List<String> newRequiredCapabilities = Arrays.asList("用户认证", "权限管理");
        RequirementPriority newPriority = RequirementPriority.LOW;

        // Act
        requirement.update(newDescription, newRequiredCapabilities, newPriority);

        // Assert
        assertEquals(newDescription, requirement.getDescription());
        assertEquals(newRequiredCapabilities, requirement.getRequiredCapabilities());
        assertEquals(newPriority, requirement.getPriority());
        assertTrue(requirement.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testUpdateRequirementWithNullCapabilities() {
        // Arrange
        Requirement requirement = Requirement.create(description, requiredCapabilities, priority);
        List<String> originalCapabilities = requirement.getRequiredCapabilities();

        // Act
        requirement.update(description, null, priority);

        // Assert
        assertEquals(originalCapabilities, requirement.getRequiredCapabilities()); // Should remain unchanged
    }

    @Test
    void testAddRequiredCapability() {
        // Arrange
        Requirement requirement = Requirement.create(description, Collections.emptyList(), priority);
        String newCapability = "数据导出";
        Instant originalLastUpdatedAt = requirement.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Act
        requirement.addRequiredCapability(newCapability);

        // Assert
        assertTrue(requirement.getRequiredCapabilities().contains(newCapability));
        assertTrue(requirement.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testAddRequiredCapabilityAlreadyExists() {
        // Arrange
        Requirement requirement = Requirement.create(description, requiredCapabilities, priority);
        String existingCapability = requiredCapabilities.get(0);
        int originalSize = requirement.getRequiredCapabilities().size();
        Instant originalLastUpdatedAt = requirement.getLastUpdatedAt();

        // Act
        requirement.addRequiredCapability(existingCapability);

        // Assert
        assertEquals(originalSize, requirement.getRequiredCapabilities().size());
        assertEquals(originalLastUpdatedAt, requirement.getLastUpdatedAt()); // Should not update timestamp
    }

    @Test
    void testRemoveRequiredCapability() {
        // Arrange
        Requirement requirement = Requirement.create(description, requiredCapabilities, priority);
        String capabilityToRemove = requiredCapabilities.get(0);
        Instant originalLastUpdatedAt = requirement.getLastUpdatedAt();

        // Wait a bit to ensure timestamp difference
        try {
            Thread.sleep(10);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        // Act
        requirement.removeRequiredCapability(capabilityToRemove);

        // Assert
        assertFalse(requirement.getRequiredCapabilities().contains(capabilityToRemove));
        assertTrue(requirement.getLastUpdatedAt().isAfter(originalLastUpdatedAt));
    }

    @Test
    void testRemoveRequiredCapabilityNotExists() {
        // Arrange
        Requirement requirement = Requirement.create(description, requiredCapabilities, priority);
        String nonExistentCapability = "不存在的能力";
        int originalSize = requirement.getRequiredCapabilities().size();
        Instant originalLastUpdatedAt = requirement.getLastUpdatedAt();

        // Act
        requirement.removeRequiredCapability(nonExistentCapability);

        // Assert
        assertEquals(originalSize, requirement.getRequiredCapabilities().size());
        assertEquals(originalLastUpdatedAt, requirement.getLastUpdatedAt()); // Should not update timestamp
    }

    @Test
    void testGetRequiredCapabilitiesReturnsUnmodifiableList() {
        // Arrange
        Requirement requirement = Requirement.create(description, requiredCapabilities, priority);

        // Act & Assert
        List<String> returnedCapabilities = requirement.getRequiredCapabilities();
        assertThrows(UnsupportedOperationException.class, () -> {
            returnedCapabilities.add("新能力");
        });
    }

    @Test
    void testEqualsAndHashCode() {
        // Arrange
        RequirementId requirementId = RequirementId.createNew();
        Requirement requirement1 = Requirement.restore(
                requirementId, description, requiredCapabilities,
                priority, Instant.now(), Instant.now()
        );
        Requirement requirement2 = Requirement.restore(
                requirementId, "不同的描述", Collections.emptyList(),
                RequirementPriority.LOW, Instant.now(), Instant.now()
        );
        Requirement requirement3 = Requirement.create(description, requiredCapabilities, priority);

        // Assert
        assertEquals(requirement1, requirement2); // Same ID
        assertNotEquals(requirement1, requirement3); // Different ID
        assertEquals(requirement1.hashCode(), requirement2.hashCode());
        assertNotEquals(requirement1.hashCode(), requirement3.hashCode());
    }

    @Test
    void testToString() {
        // Arrange
        Requirement requirement = Requirement.create(description, requiredCapabilities, priority);

        // Act
        String toString = requirement.toString();

        // Assert
        assertNotNull(toString);
        assertTrue(toString.contains("Requirement{"));
        assertTrue(toString.contains("id=" + requirement.getId()));
        assertTrue(toString.contains("description='" + description + "'"));
        assertTrue(toString.contains("requiredCapabilities=" + requiredCapabilities));
        assertTrue(toString.contains("priority=" + priority));
    }
}
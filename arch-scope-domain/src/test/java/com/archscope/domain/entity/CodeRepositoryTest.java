package com.archscope.domain.entity;

import com.archscope.domain.valueobject.RepositoryStatus;
import com.archscope.domain.valueobject.RepositoryType;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.time.LocalDateTime;

/**
 * 代码仓库实体类测试
 */
public class CodeRepositoryTest {

    @Test
    public void testCreateBasicCodeRepository() {
        // Arrange
        Long id = 1L;
        String name = "测试仓库";
        String url = "https://github.com/test/repo.git";
        String username = "testuser";
        String password = "password123";
        String accessToken = "ghp_123456789abcdef";
        RepositoryType type = RepositoryType.GIT;
        String defaultBranch = "main";
        LocalDateTime createdAt = LocalDateTime.now();
        LocalDateTime lastSyncedAt = null;
        RepositoryStatus status = RepositoryStatus.ACTIVE;
        boolean sshEnabled = false;
        String sshKey = null;
        String sshKeyPassphrase = null;
        Long projectId = 101L;
        
        // Act
        CodeRepository repository = CodeRepository.builder()
                .id(id)
                .name(name)
                .url(url)
                .username(username)
                .password(password)
                .accessToken(accessToken)
                .type(type)
                .defaultBranch(defaultBranch)
                .createdAt(createdAt)
                .lastSyncedAt(lastSyncedAt)
                .status(status)
                .sshEnabled(sshEnabled)
                .sshKey(sshKey)
                .sshKeyPassphrase(sshKeyPassphrase)
                .projectId(projectId)
                .build();
        
        // Assert
        assertNotNull(repository);
        assertEquals(id, repository.getId());
        assertEquals(name, repository.getName());
        assertEquals(url, repository.getUrl());
        assertEquals(username, repository.getUsername());
        assertEquals(password, repository.getPassword());
        assertEquals(accessToken, repository.getAccessToken());
        assertEquals(type, repository.getType());
        assertEquals(defaultBranch, repository.getDefaultBranch());
        assertEquals(createdAt, repository.getCreatedAt());
        assertNull(repository.getLastSyncedAt());
        assertEquals(status, repository.getStatus());
        assertFalse(repository.isSshEnabled());
        assertNull(repository.getSshKey());
        assertNull(repository.getSshKeyPassphrase());
        assertEquals(projectId, repository.getProjectId());
    }
    
    @Test
    public void testCreateSSHCodeRepository() {
        // Arrange
        String name = "SSH仓库";
        String url = "**************:test/repo.git";
        RepositoryType type = RepositoryType.GIT;
        String defaultBranch = "main";
        boolean sshEnabled = true;
        String sshKey = "-----BEGIN RSA PRIVATE KEY-----\nMIIEpAIBAAKCAQEA...\n-----END RSA PRIVATE KEY-----";
        String sshKeyPassphrase = "passphrase123";
        Long projectId = 102L;
        
        // Act
        CodeRepository repository = CodeRepository.builder()
                .name(name)
                .url(url)
                .type(type)
                .defaultBranch(defaultBranch)
                .sshEnabled(sshEnabled)
                .sshKey(sshKey)
                .sshKeyPassphrase(sshKeyPassphrase)
                .projectId(projectId)
                .build();
        
        // Assert
        assertNotNull(repository);
        assertEquals(name, repository.getName());
        assertEquals(url, repository.getUrl());
        assertNull(repository.getUsername());
        assertNull(repository.getPassword());
        assertNull(repository.getAccessToken());
        assertEquals(type, repository.getType());
        assertEquals(defaultBranch, repository.getDefaultBranch());
        assertTrue(repository.isSshEnabled());
        assertEquals(sshKey, repository.getSshKey());
        assertEquals(sshKeyPassphrase, repository.getSshKeyPassphrase());
        assertEquals(projectId, repository.getProjectId());
    }
    
    @Test
    public void testCreateTokenCodeRepository() {
        // Arrange
        String name = "Token仓库";
        String url = "https://github.com/test/repo.git";
        String accessToken = "ghp_123456789abcdef";
        RepositoryType type = RepositoryType.GIT;
        String defaultBranch = "develop";
        Long projectId = 103L;
        
        // Act
        CodeRepository repository = CodeRepository.builder()
                .name(name)
                .url(url)
                .accessToken(accessToken)
                .type(type)
                .defaultBranch(defaultBranch)
                .projectId(projectId)
                .build();
        
        // Assert
        assertNotNull(repository);
        assertEquals(name, repository.getName());
        assertEquals(url, repository.getUrl());
        assertNull(repository.getUsername());
        assertNull(repository.getPassword());
        assertEquals(accessToken, repository.getAccessToken());
        assertEquals(type, repository.getType());
        assertEquals(defaultBranch, repository.getDefaultBranch());
        assertFalse(repository.isSshEnabled());
        assertEquals(projectId, repository.getProjectId());
    }
    
    @Test
    public void testUpdateRepositoryStatus() {
        // Arrange
        CodeRepository repository = CodeRepository.builder()
                .name("状态更新测试仓库")
                .url("https://github.com/test/repo.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .status(RepositoryStatus.ACTIVE)
                .build();
        
        // Act
        repository.setStatus(RepositoryStatus.INACTIVE);
        
        // Assert
        assertEquals(RepositoryStatus.INACTIVE, repository.getStatus());
    }
    
    @Test
    public void testUpdateLastSyncedAt() {
        // Arrange
        CodeRepository repository = CodeRepository.builder()
                .name("同步时间测试仓库")
                .url("https://github.com/test/repo.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .build();
        
        assertNull(repository.getLastSyncedAt());
        
        // Act
        LocalDateTime syncTime = LocalDateTime.now();
        repository.setLastSyncedAt(syncTime);
        
        // Assert
        assertEquals(syncTime, repository.getLastSyncedAt());
    }
    
    @Test
    public void testRepositoryTypeFromString() {
        // 测试从字符串转换为枚举值
        assertEquals(RepositoryType.GIT, RepositoryType.fromString("GIT"));
        assertEquals(RepositoryType.SVN, RepositoryType.fromString("SVN"));
        assertEquals(RepositoryType.GIT, RepositoryType.fromString("UNKNOWN")); // 默认为GIT
    }
    
    @Test
    public void testRepositoryStatusFromString() {
        // 测试从字符串转换为枚举值
        assertEquals(RepositoryStatus.ACTIVE, RepositoryStatus.fromString("ACTIVE"));
        assertEquals(RepositoryStatus.INACTIVE, RepositoryStatus.fromString("INACTIVE"));
        assertEquals(RepositoryStatus.INACTIVE, RepositoryStatus.fromString("UNKNOWN")); // 默认为INACTIVE
    }
} 
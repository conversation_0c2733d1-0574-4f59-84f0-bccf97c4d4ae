package com.archscope.domain.repository;

import com.archscope.domain.model.servicediscovery.Service;
import com.archscope.domain.model.servicediscovery.ServiceStatus;
import com.archscope.domain.model.servicediscovery.ServiceType;
import com.archscope.domain.valueobject.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 服务仓储接口测试
 */
class ServiceRepositoryTest {

    @Mock
    private ServiceRepository serviceRepository;

    private Service testService;
    private ServiceId testServiceId;

    @BeforeEach
    void setUp() throws MalformedURLException {
        MockitoAnnotations.openMocks(this);

        testServiceId = ServiceId.createNew();
        testService = Service.restore(
                testServiceId,
                "测试服务",
                "这是一个测试服务",
                ServiceType.REST_API,
                Version.of("1.0.0"),
                new URL("https://api.example.com/v1"),
                "com.example",
                "test-service",
                Collections.singleton(Tag.of("api")),
                Collections.emptySet(),
                ServiceStatus.ACTIVE,
                Metadata.empty(),
                java.time.Instant.now(),
                java.time.Instant.now()
        );
    }

    @Test
    void testSave() {
        // Arrange
        when(serviceRepository.save(testService)).thenReturn(testService);

        // Act
        Service result = serviceRepository.save(testService);

        // Assert
        assertNotNull(result);
        assertEquals(testService.getId(), result.getId());
        assertEquals(testService.getName(), result.getName());
        verify(serviceRepository).save(testService);
    }

    @Test
    void testFindById() {
        // Arrange
        when(serviceRepository.findById(testServiceId)).thenReturn(testService);

        // Act
        Service result = serviceRepository.findById(testServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(testServiceId, result.getId());
        assertEquals("测试服务", result.getName());
        verify(serviceRepository).findById(testServiceId);
    }

    @Test
    void testFindByIdString() {
        // Arrange
        String serviceIdString = testServiceId.getValue();
        when(serviceRepository.findById(serviceIdString)).thenReturn(testService);

        // Act
        Service result = serviceRepository.findById(serviceIdString);

        // Assert
        assertNotNull(result);
        assertEquals(testServiceId, result.getId());
        verify(serviceRepository).findById(serviceIdString);
    }

    @Test
    void testFindByIdNotFound() {
        // Arrange
        ServiceId nonExistentId = ServiceId.createNew();
        when(serviceRepository.findById(nonExistentId)).thenReturn(null);

        // Act
        Service result = serviceRepository.findById(nonExistentId);

        // Assert
        assertNull(result);
        verify(serviceRepository).findById(nonExistentId);
    }

    @Test
    void testFindByName() {
        // Arrange
        when(serviceRepository.findByName("测试服务")).thenReturn(testService);

        // Act
        Service result = serviceRepository.findByName("测试服务");

        // Assert
        assertNotNull(result);
        assertEquals("测试服务", result.getName());
        verify(serviceRepository).findByName("测试服务");
    }

    @Test
    void testFindByNameNotFound() {
        // Arrange
        when(serviceRepository.findByName("不存在的服务")).thenReturn(null);

        // Act
        Service result = serviceRepository.findByName("不存在的服务");

        // Assert
        assertNull(result);
        verify(serviceRepository).findByName("不存在的服务");
    }

    @Test
    void testFindByEndpoint() throws MalformedURLException {
        // Arrange
        URL endpoint = new URL("https://api.example.com/v1");
        when(serviceRepository.findByEndpoint(endpoint)).thenReturn(testService);

        // Act
        Service result = serviceRepository.findByEndpoint(endpoint);

        // Assert
        assertNotNull(result);
        assertEquals(endpoint, result.getEndpoint());
        verify(serviceRepository).findByEndpoint(endpoint);
    }

    @Test
    void testFindByMavenCoordinates() {
        // Arrange
        when(serviceRepository.findByMavenCoordinates("com.example", "test-service", "1.0.0"))
                .thenReturn(testService);

        // Act
        Service result = serviceRepository.findByMavenCoordinates("com.example", "test-service", "1.0.0");

        // Assert
        assertNotNull(result);
        assertEquals("com.example", result.getGroupId());
        assertEquals("test-service", result.getArtifactId());
        assertEquals("1.0.0", result.getVersion().getValue());
        verify(serviceRepository).findByMavenCoordinates("com.example", "test-service", "1.0.0");
    }

    @Test
    void testFindByStatus() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByStatus(ServiceStatus.ACTIVE)).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findByStatus(ServiceStatus.ACTIVE);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ServiceStatus.ACTIVE, result.get(0).getStatus());
        verify(serviceRepository).findByStatus(ServiceStatus.ACTIVE);
    }

    @Test
    void testFindByStatusString() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByStatus("ACTIVE")).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findByStatus("ACTIVE");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ServiceStatus.ACTIVE, result.get(0).getStatus());
        verify(serviceRepository).findByStatus("ACTIVE");
    }

    @Test
    void testFindByType() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByType(ServiceType.REST_API)).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findByType(ServiceType.REST_API);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ServiceType.REST_API, result.get(0).getType());
        verify(serviceRepository).findByType(ServiceType.REST_API);
    }

    @Test
    void testFindByTypeString() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByType("REST_API")).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findByType("REST_API");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ServiceType.REST_API, result.get(0).getType());
        verify(serviceRepository).findByType("REST_API");
    }

    @Test
    void testFindByTags() {
        // Arrange
        Set<Tag> tags = Collections.singleton(Tag.of("api"));
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByTags(tags)).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findByTags(tags);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).getTags().contains(Tag.of("api")));
        verify(serviceRepository).findByTags(tags);
    }

    @Test
    void testFindByTagsIn() {
        // Arrange
        List<String> tags = Arrays.asList("api", "microservice");
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByTagsIn(tags)).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findByTagsIn(tags);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(serviceRepository).findByTagsIn(tags);
    }

    @Test
    void testFindByCapabilityName() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByCapabilityName("用户认证")).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findByCapabilityName("用户认证");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(serviceRepository).findByCapabilityName("用户认证");
    }

    @Test
    void testFindByNameContainingIgnoreCase() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByNameContainingIgnoreCase("测试")).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findByNameContainingIgnoreCase("测试");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).getName().contains("测试"));
        verify(serviceRepository).findByNameContainingIgnoreCase("测试");
    }

    @Test
    void testFindByGroupIdAndArtifactId() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findByGroupIdAndArtifactId("com.example", "test-service"))
                .thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findByGroupIdAndArtifactId("com.example", "test-service");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("com.example", result.get(0).getGroupId());
        assertEquals("test-service", result.get(0).getArtifactId());
        verify(serviceRepository).findByGroupIdAndArtifactId("com.example", "test-service");
    }

    @Test
    void testFindByGroupIdAndArtifactIdAndVersion() {
        // Arrange
        when(serviceRepository.findByGroupIdAndArtifactIdAndVersion("com.example", "test-service", "1.0.0"))
                .thenReturn(testService);

        // Act
        Service result = serviceRepository.findByGroupIdAndArtifactIdAndVersion("com.example", "test-service", "1.0.0");

        // Assert
        assertNotNull(result);
        assertEquals("com.example", result.getGroupId());
        assertEquals("test-service", result.getArtifactId());
        assertEquals("1.0.0", result.getVersion().getValue());
        verify(serviceRepository).findByGroupIdAndArtifactIdAndVersion("com.example", "test-service", "1.0.0");
    }

    @Test
    void testFindAll() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findAll()).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findAll();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(serviceRepository).findAll();
    }

    @Test
    void testFindAllWithPageRequest() {
        // Arrange
        PageRequest pageRequest = PageRequest.of(0, 10);
        PageResult<Service> pageResult = new PageResult<>(
                Arrays.asList(testService), 0, 10, 1
        );
        when(serviceRepository.findAll(pageRequest)).thenReturn(pageResult);

        // Act
        PageResult<Service> result = serviceRepository.findAll(pageRequest);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(0, result.getPage());
        assertEquals(10, result.getSize());
        assertEquals(1, result.getTotalElements());
        verify(serviceRepository).findAll(pageRequest);
    }

    @Test
    void testFindByCriteria() {
        // Arrange
        PageRequest pageRequest = PageRequest.of(0, 10);
        PageResult<Service> pageResult = new PageResult<>(
                Arrays.asList(testService), 0, 10, 1
        );
        when(serviceRepository.findByCriteria(
                eq("测试"), eq("REST_API"), eq("ACTIVE"), anyList(),
                eq("com.example"), eq("test-service"), eq("用户认证"), eq(pageRequest)
        )).thenReturn(pageResult);

        // Act
        PageResult<Service> result = serviceRepository.findByCriteria(
                "测试", "REST_API", "ACTIVE", Arrays.asList("api"),
                "com.example", "test-service", "用户认证", pageRequest
        );

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        verify(serviceRepository).findByCriteria(
                eq("测试"), eq("REST_API"), eq("ACTIVE"), anyList(),
                eq("com.example"), eq("test-service"), eq("用户认证"), eq(pageRequest)
        );
    }

    @Test
    void testFindWithPagination() {
        // Arrange
        List<Service> services = Arrays.asList(testService);
        when(serviceRepository.findWithPagination(0, 10)).thenReturn(services);

        // Act
        List<Service> result = serviceRepository.findWithPagination(0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(serviceRepository).findWithPagination(0, 10);
    }

    @Test
    void testDelete() {
        // Arrange
        when(serviceRepository.delete(testServiceId)).thenReturn(true);

        // Act
        boolean result = serviceRepository.delete(testServiceId);

        // Assert
        assertTrue(result);
        verify(serviceRepository).delete(testServiceId);
    }

    @Test
    void testDeleteNotFound() {
        // Arrange
        ServiceId nonExistentId = ServiceId.createNew();
        when(serviceRepository.delete(nonExistentId)).thenReturn(false);

        // Act
        boolean result = serviceRepository.delete(nonExistentId);

        // Assert
        assertFalse(result);
        verify(serviceRepository).delete(nonExistentId);
    }

    @Test
    void testCount() {
        // Arrange
        when(serviceRepository.count()).thenReturn(100L);

        // Act
        long result = serviceRepository.count();

        // Assert
        assertEquals(100L, result);
        verify(serviceRepository).count();
    }

    @Test
    void testCountByStatus() {
        // Arrange
        when(serviceRepository.countByStatus("ACTIVE")).thenReturn(80L);

        // Act
        long result = serviceRepository.countByStatus("ACTIVE");

        // Assert
        assertEquals(80L, result);
        verify(serviceRepository).countByStatus("ACTIVE");
    }

    @Test
    void testCountByStatusGrouped() {
        // Arrange
        Map<String, Long> statusStats = new HashMap<>();
        statusStats.put("ACTIVE", 80L);
        statusStats.put("INACTIVE", 20L);
        when(serviceRepository.countByStatusGrouped()).thenReturn(statusStats);

        // Act
        Map<String, Long> result = serviceRepository.countByStatusGrouped();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(80L, result.get("ACTIVE"));
        assertEquals(20L, result.get("INACTIVE"));
        verify(serviceRepository).countByStatusGrouped();
    }

    @Test
    void testCountByTypeGrouped() {
        // Arrange
        Map<String, Long> typeStats = new HashMap<>();
        typeStats.put("REST_API", 60L);
        typeStats.put("GRAPHQL_API", 40L);
        when(serviceRepository.countByTypeGrouped()).thenReturn(typeStats);

        // Act
        Map<String, Long> result = serviceRepository.countByTypeGrouped();

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(60L, result.get("REST_API"));
        assertEquals(40L, result.get("GRAPHQL_API"));
        verify(serviceRepository).countByTypeGrouped();
    }

    @Test
    void testCountRecentServices() {
        // Arrange
        when(serviceRepository.countRecentServices(7)).thenReturn(15L);

        // Act
        long result = serviceRepository.countRecentServices(7);

        // Assert
        assertEquals(15L, result);
        verify(serviceRepository).countRecentServices(7);
    }
}
package com.archscope.domain.repository;

import com.archscope.domain.model.servicediscovery.Capability;
import com.archscope.domain.model.servicediscovery.CapabilityExample;
import com.archscope.domain.valueobject.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 能力仓储接口测试
 */
class CapabilityRepositoryTest {

    @Mock
    private CapabilityRepository capabilityRepository;

    private Capability testCapability;
    private CapabilityId testCapabilityId;
    private ServiceId testServiceId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        testCapabilityId = CapabilityId.createNew();
        testServiceId = ServiceId.createNew();

        Set<CapabilityExample> examples = new HashSet<>();
        examples.add(CapabilityExample.of("登录示例", "用户登录示例", 
                "{\"username\":\"user\",\"password\":\"pass\"}", 
                "{\"token\":\"jwt_token\",\"expires\":3600}"));

        testCapability = Capability.restore(
                testCapabilityId,
                testServiceId,
                "用户认证",
                "提供用户认证功能",
                Version.of("1.0.0"),
                examples,
                Collections.singleton(Tag.of("authentication")),
                false,
                Instant.now(),
                Instant.now()
        );
    }

    @Test
    void testSave() {
        // Arrange
        when(capabilityRepository.save(testCapability)).thenReturn(testCapability);

        // Act
        Capability result = capabilityRepository.save(testCapability);

        // Assert
        assertNotNull(result);
        assertEquals(testCapability.getId(), result.getId());
        assertEquals(testCapability.getName(), result.getName());
        verify(capabilityRepository).save(testCapability);
    }

    @Test
    void testFindById() {
        // Arrange
        when(capabilityRepository.findById(testCapabilityId)).thenReturn(testCapability);

        // Act
        Capability result = capabilityRepository.findById(testCapabilityId);

        // Assert
        assertNotNull(result);
        assertEquals(testCapabilityId, result.getId());
        assertEquals("用户认证", result.getName());
        verify(capabilityRepository).findById(testCapabilityId);
    }

    @Test
    void testFindByIdNotFound() {
        // Arrange
        CapabilityId nonExistentId = CapabilityId.createNew();
        when(capabilityRepository.findById(nonExistentId)).thenReturn(null);

        // Act
        Capability result = capabilityRepository.findById(nonExistentId);

        // Assert
        assertNull(result);
        verify(capabilityRepository).findById(nonExistentId);
    }

    @Test
    void testFindByServiceIdAndName() {
        // Arrange
        when(capabilityRepository.findByServiceIdAndName(testServiceId, "用户认证"))
                .thenReturn(testCapability);

        // Act
        Capability result = capabilityRepository.findByServiceIdAndName(testServiceId, "用户认证");

        // Assert
        assertNotNull(result);
        assertEquals(testServiceId, result.getServiceId());
        assertEquals("用户认证", result.getName());
        verify(capabilityRepository).findByServiceIdAndName(testServiceId, "用户认证");
    }

    @Test
    void testFindByServiceIdAndNameNotFound() {
        // Arrange
        when(capabilityRepository.findByServiceIdAndName(testServiceId, "不存在的能力"))
                .thenReturn(null);

        // Act
        Capability result = capabilityRepository.findByServiceIdAndName(testServiceId, "不存在的能力");

        // Assert
        assertNull(result);
        verify(capabilityRepository).findByServiceIdAndName(testServiceId, "不存在的能力");
    }

    @Test
    void testFindByServiceId() {
        // Arrange
        List<Capability> capabilities = Arrays.asList(testCapability);
        when(capabilityRepository.findByServiceId(testServiceId)).thenReturn(capabilities);

        // Act
        List<Capability> result = capabilityRepository.findByServiceId(testServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(testServiceId, result.get(0).getServiceId());
        verify(capabilityRepository).findByServiceId(testServiceId);
    }

    @Test
    void testFindByServiceIdEmpty() {
        // Arrange
        ServiceId emptyServiceId = ServiceId.createNew();
        when(capabilityRepository.findByServiceId(emptyServiceId)).thenReturn(Collections.emptyList());

        // Act
        List<Capability> result = capabilityRepository.findByServiceId(emptyServiceId);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(capabilityRepository).findByServiceId(emptyServiceId);
    }

    @Test
    void testFindByName() {
        // Arrange
        List<Capability> capabilities = Arrays.asList(testCapability);
        when(capabilityRepository.findByName("用户认证")).thenReturn(capabilities);

        // Act
        List<Capability> result = capabilityRepository.findByName("用户认证");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("用户认证", result.get(0).getName());
        verify(capabilityRepository).findByName("用户认证");
    }

    @Test
    void testFindByNameEmpty() {
        // Arrange
        when(capabilityRepository.findByName("不存在的能力")).thenReturn(Collections.emptyList());

        // Act
        List<Capability> result = capabilityRepository.findByName("不存在的能力");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(capabilityRepository).findByName("不存在的能力");
    }

    @Test
    void testFindAllCapabilityNames() {
        // Arrange
        List<String> capabilityNames = Arrays.asList("用户认证", "权限管理", "数据导出");
        when(capabilityRepository.findAllCapabilityNames()).thenReturn(capabilityNames);

        // Act
        List<String> result = capabilityRepository.findAllCapabilityNames();

        // Assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertTrue(result.contains("用户认证"));
        assertTrue(result.contains("权限管理"));
        assertTrue(result.contains("数据导出"));
        verify(capabilityRepository).findAllCapabilityNames();
    }

    @Test
    void testFindAllCapabilityNamesEmpty() {
        // Arrange
        when(capabilityRepository.findAllCapabilityNames()).thenReturn(Collections.emptyList());

        // Act
        List<String> result = capabilityRepository.findAllCapabilityNames();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(capabilityRepository).findAllCapabilityNames();
    }

    @Test
    void testFindServiceIdsByCapabilityNames() {
        // Arrange
        List<String> capabilityNames = Arrays.asList("用户认证", "权限管理");
        List<ServiceId> serviceIds = Arrays.asList(testServiceId, ServiceId.createNew());
        when(capabilityRepository.findServiceIdsByCapabilityNames(capabilityNames))
                .thenReturn(serviceIds);

        // Act
        List<ServiceId> result = capabilityRepository.findServiceIdsByCapabilityNames(capabilityNames);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.contains(testServiceId));
        verify(capabilityRepository).findServiceIdsByCapabilityNames(capabilityNames);
    }

    @Test
    void testFindServiceIdsByCapabilityNamesEmpty() {
        // Arrange
        List<String> capabilityNames = Arrays.asList("不存在的能力");
        when(capabilityRepository.findServiceIdsByCapabilityNames(capabilityNames))
                .thenReturn(Collections.emptyList());

        // Act
        List<ServiceId> result = capabilityRepository.findServiceIdsByCapabilityNames(capabilityNames);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(capabilityRepository).findServiceIdsByCapabilityNames(capabilityNames);
    }

    @Test
    void testFindServiceIdsByCapabilityNamesNull() {
        // Arrange
        when(capabilityRepository.findServiceIdsByCapabilityNames(null))
                .thenReturn(Collections.emptyList());

        // Act
        List<ServiceId> result = capabilityRepository.findServiceIdsByCapabilityNames(null);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(capabilityRepository).findServiceIdsByCapabilityNames(null);
    }

    @Test
    void testDelete() {
        // Arrange
        when(capabilityRepository.delete(testCapabilityId)).thenReturn(true);

        // Act
        boolean result = capabilityRepository.delete(testCapabilityId);

        // Assert
        assertTrue(result);
        verify(capabilityRepository).delete(testCapabilityId);
    }

    @Test
    void testDeleteNotFound() {
        // Arrange
        CapabilityId nonExistentId = CapabilityId.createNew();
        when(capabilityRepository.delete(nonExistentId)).thenReturn(false);

        // Act
        boolean result = capabilityRepository.delete(nonExistentId);

        // Assert
        assertFalse(result);
        verify(capabilityRepository).delete(nonExistentId);
    }

    @Test
    void testDeleteByServiceId() {
        // Arrange
        when(capabilityRepository.deleteByServiceId(testServiceId)).thenReturn(true);

        // Act
        boolean result = capabilityRepository.deleteByServiceId(testServiceId);

        // Assert
        assertTrue(result);
        verify(capabilityRepository).deleteByServiceId(testServiceId);
    }

    @Test
    void testDeleteByServiceIdNotFound() {
        // Arrange
        ServiceId nonExistentServiceId = ServiceId.createNew();
        when(capabilityRepository.deleteByServiceId(nonExistentServiceId)).thenReturn(false);

        // Act
        boolean result = capabilityRepository.deleteByServiceId(nonExistentServiceId);

        // Assert
        assertFalse(result);
        verify(capabilityRepository).deleteByServiceId(nonExistentServiceId);
    }

    @Test
    void testCount() {
        // Arrange
        when(capabilityRepository.count()).thenReturn(50L);

        // Act
        long result = capabilityRepository.count();

        // Assert
        assertEquals(50L, result);
        verify(capabilityRepository).count();
    }

    @Test
    void testCountZero() {
        // Arrange
        when(capabilityRepository.count()).thenReturn(0L);

        // Act
        long result = capabilityRepository.count();

        // Assert
        assertEquals(0L, result);
        verify(capabilityRepository).count();
    }

    @Test
    void testFindByServiceIdMultipleCapabilities() {
        // Arrange
        Capability capability2 = Capability.create(
                testServiceId,
                "权限管理",
                "提供权限管理功能",
                Version.of("1.0.0"),
                Collections.emptySet(),
                Collections.singleton(Tag.of("authorization"))
        );
        List<Capability> capabilities = Arrays.asList(testCapability, capability2);
        when(capabilityRepository.findByServiceId(testServiceId)).thenReturn(capabilities);

        // Act
        List<Capability> result = capabilityRepository.findByServiceId(testServiceId);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(testServiceId, result.get(0).getServiceId());
        assertEquals(testServiceId, result.get(1).getServiceId());
        
        Set<String> capabilityNames = new HashSet<>();
        for (Capability capability : result) {
            capabilityNames.add(capability.getName());
        }
        assertTrue(capabilityNames.contains("用户认证"));
        assertTrue(capabilityNames.contains("权限管理"));
        
        verify(capabilityRepository).findByServiceId(testServiceId);
    }

    @Test
    void testFindByNameMultipleServices() {
        // Arrange
        ServiceId serviceId2 = ServiceId.createNew();
        Capability capability2 = Capability.create(
                serviceId2,
                "用户认证",
                "另一个服务的用户认证功能",
                Version.of("2.0.0"),
                Collections.emptySet(),
                Collections.singleton(Tag.of("authentication"))
        );
        List<Capability> capabilities = Arrays.asList(testCapability, capability2);
        when(capabilityRepository.findByName("用户认证")).thenReturn(capabilities);

        // Act
        List<Capability> result = capabilityRepository.findByName("用户认证");

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("用户认证", result.get(0).getName());
        assertEquals("用户认证", result.get(1).getName());
        
        Set<ServiceId> serviceIds = new HashSet<>();
        for (Capability capability : result) {
            serviceIds.add(capability.getServiceId());
        }
        assertTrue(serviceIds.contains(testServiceId));
        assertTrue(serviceIds.contains(serviceId2));
        
        verify(capabilityRepository).findByName("用户认证");
    }
}
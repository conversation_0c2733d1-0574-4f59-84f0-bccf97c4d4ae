package com.archscope.domain.repository;

import com.archscope.domain.model.servicediscovery.Requirement;
import com.archscope.domain.model.servicediscovery.RequirementPriority;
import com.archscope.domain.valueobject.RequirementId;
import com.archscope.domain.valueobject.ServiceId;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.time.Instant;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 需求仓储接口测试
 */
class RequirementRepositoryTest {

    @Mock
    private RequirementRepository requirementRepository;

    private Requirement testRequirement;
    private RequirementId testRequirementId;
    private ServiceId testServiceId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);

        testRequirementId = RequirementId.createNew();
        testServiceId = ServiceId.createNew();

        testRequirement = Requirement.restore(
                testRequirementId,
                "需要一个用户管理系统",
                Arrays.asList("用户认证", "用户注册", "密码重置"),
                RequirementPriority.HIGH,
                Instant.now(),
                Instant.now()
        );
    }

    @Test
    void testSave() {
        // Arrange
        when(requirementRepository.save(testRequirement)).thenReturn(testRequirement);

        // Act
        Requirement result = requirementRepository.save(testRequirement);

        // Assert
        assertNotNull(result);
        assertEquals(testRequirement.getId(), result.getId());
        assertEquals(testRequirement.getDescription(), result.getDescription());
        verify(requirementRepository).save(testRequirement);
    }

    @Test
    void testFindById() {
        // Arrange
        when(requirementRepository.findById(testRequirementId)).thenReturn(testRequirement);

        // Act
        Requirement result = requirementRepository.findById(testRequirementId);

        // Assert
        assertNotNull(result);
        assertEquals(testRequirementId, result.getId());
        assertEquals("需要一个用户管理系统", result.getDescription());
        verify(requirementRepository).findById(testRequirementId);
    }

    @Test
    void testFindByIdNotFound() {
        // Arrange
        RequirementId nonExistentId = RequirementId.createNew();
        when(requirementRepository.findById(nonExistentId)).thenReturn(null);

        // Act
        Requirement result = requirementRepository.findById(nonExistentId);

        // Assert
        assertNull(result);
        verify(requirementRepository).findById(nonExistentId);
    }

    @Test
    void testFindByPriority() {
        // Arrange
        List<Requirement> requirements = Arrays.asList(testRequirement);
        when(requirementRepository.findByPriority(RequirementPriority.HIGH)).thenReturn(requirements);

        // Act
        List<Requirement> result = requirementRepository.findByPriority(RequirementPriority.HIGH);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(RequirementPriority.HIGH, result.get(0).getPriority());
        verify(requirementRepository).findByPriority(RequirementPriority.HIGH);
    }

    @Test
    void testFindByPriorityEmpty() {
        // Arrange
        when(requirementRepository.findByPriority(RequirementPriority.LOW)).thenReturn(Collections.emptyList());

        // Act
        List<Requirement> result = requirementRepository.findByPriority(RequirementPriority.LOW);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(requirementRepository).findByPriority(RequirementPriority.LOW);
    }

    @Test
    void testFindByRequiredCapability() {
        // Arrange
        List<Requirement> requirements = Arrays.asList(testRequirement);
        when(requirementRepository.findByRequiredCapability("用户认证")).thenReturn(requirements);

        // Act
        List<Requirement> result = requirementRepository.findByRequiredCapability("用户认证");

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.get(0).getRequiredCapabilities().contains("用户认证"));
        verify(requirementRepository).findByRequiredCapability("用户认证");
    }

    @Test
    void testFindByRequiredCapabilityEmpty() {
        // Arrange
        when(requirementRepository.findByRequiredCapability("不存在的能力")).thenReturn(Collections.emptyList());

        // Act
        List<Requirement> result = requirementRepository.findByRequiredCapability("不存在的能力");

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(requirementRepository).findByRequiredCapability("不存在的能力");
    }

    @Test
    void testFindAll() {
        // Arrange
        List<Requirement> requirements = Arrays.asList(testRequirement);
        when(requirementRepository.findAll()).thenReturn(requirements);

        // Act
        List<Requirement> result = requirementRepository.findAll();

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(requirementRepository).findAll();
    }

    @Test
    void testFindAllEmpty() {
        // Arrange
        when(requirementRepository.findAll()).thenReturn(Collections.emptyList());

        // Act
        List<Requirement> result = requirementRepository.findAll();

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(requirementRepository).findAll();
    }

    @Test
    void testFindWithPagination() {
        // Arrange
        List<Requirement> requirements = Arrays.asList(testRequirement);
        when(requirementRepository.findWithPagination(0, 10)).thenReturn(requirements);

        // Act
        List<Requirement> result = requirementRepository.findWithPagination(0, 10);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.size());
        verify(requirementRepository).findWithPagination(0, 10);
    }

    @Test
    void testFindWithPaginationEmpty() {
        // Arrange
        when(requirementRepository.findWithPagination(10, 10)).thenReturn(Collections.emptyList());

        // Act
        List<Requirement> result = requirementRepository.findWithPagination(10, 10);

        // Assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(requirementRepository).findWithPagination(10, 10);
    }

    @Test
    void testDelete() {
        // Arrange
        when(requirementRepository.delete(testRequirementId)).thenReturn(true);

        // Act
        boolean result = requirementRepository.delete(testRequirementId);

        // Assert
        assertTrue(result);
        verify(requirementRepository).delete(testRequirementId);
    }

    @Test
    void testDeleteNotFound() {
        // Arrange
        RequirementId nonExistentId = RequirementId.createNew();
        when(requirementRepository.delete(nonExistentId)).thenReturn(false);

        // Act
        boolean result = requirementRepository.delete(nonExistentId);

        // Assert
        assertFalse(result);
        verify(requirementRepository).delete(nonExistentId);
    }

    @Test
    void testRecordMatchingFeedback() {
        // Arrange
        doNothing().when(requirementRepository).recordMatchingFeedback(
                testRequirementId, testServiceId, true, "服务很好用"
        );

        // Act
        requirementRepository.recordMatchingFeedback(testRequirementId, testServiceId, true, "服务很好用");

        // Assert
        verify(requirementRepository).recordMatchingFeedback(testRequirementId, testServiceId, true, "服务很好用");
    }

    @Test
    void testRecordMatchingFeedbackNegative() {
        // Arrange
        doNothing().when(requirementRepository).recordMatchingFeedback(
                testRequirementId, testServiceId, false, "服务不符合需求"
        );

        // Act
        requirementRepository.recordMatchingFeedback(testRequirementId, testServiceId, false, "服务不符合需求");

        // Assert
        verify(requirementRepository).recordMatchingFeedback(testRequirementId, testServiceId, false, "服务不符合需求");
    }

    @Test
    void testRecordMatchingFeedbackWithNullFeedback() {
        // Arrange
        doNothing().when(requirementRepository).recordMatchingFeedback(
                testRequirementId, testServiceId, true, null
        );

        // Act
        requirementRepository.recordMatchingFeedback(testRequirementId, testServiceId, true, null);

        // Assert
        verify(requirementRepository).recordMatchingFeedback(testRequirementId, testServiceId, true, null);
    }

    @Test
    void testRecordMatchingFeedbackWithEmptyFeedback() {
        // Arrange
        doNothing().when(requirementRepository).recordMatchingFeedback(
                testRequirementId, testServiceId, false, ""
        );

        // Act
        requirementRepository.recordMatchingFeedback(testRequirementId, testServiceId, false, "");

        // Assert
        verify(requirementRepository).recordMatchingFeedback(testRequirementId, testServiceId, false, "");
    }

    @Test
    void testCount() {
        // Arrange
        when(requirementRepository.count()).thenReturn(25L);

        // Act
        long result = requirementRepository.count();

        // Assert
        assertEquals(25L, result);
        verify(requirementRepository).count();
    }

    @Test
    void testCountZero() {
        // Arrange
        when(requirementRepository.count()).thenReturn(0L);

        // Act
        long result = requirementRepository.count();

        // Assert
        assertEquals(0L, result);
        verify(requirementRepository).count();
    }

    @Test
    void testFindByPriorityMultipleRequirements() {
        // Arrange
        Requirement requirement2 = Requirement.create(
                "需要一个报表系统",
                Arrays.asList("数据导出", "图表生成"),
                RequirementPriority.HIGH
        );
        List<Requirement> requirements = Arrays.asList(testRequirement, requirement2);
        when(requirementRepository.findByPriority(RequirementPriority.HIGH)).thenReturn(requirements);

        // Act
        List<Requirement> result = requirementRepository.findByPriority(RequirementPriority.HIGH);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(RequirementPriority.HIGH, result.get(0).getPriority());
        assertEquals(RequirementPriority.HIGH, result.get(1).getPriority());
        
        Set<String> descriptions = new HashSet<>();
        for (Requirement requirement : result) {
            descriptions.add(requirement.getDescription());
        }
        assertTrue(descriptions.contains("需要一个用户管理系统"));
        assertTrue(descriptions.contains("需要一个报表系统"));
        
        verify(requirementRepository).findByPriority(RequirementPriority.HIGH);
    }

    @Test
    void testFindByRequiredCapabilityMultipleRequirements() {
        // Arrange
        Requirement requirement2 = Requirement.create(
                "需要一个安全系统",
                Arrays.asList("用户认证", "权限管理", "审计日志"),
                RequirementPriority.MEDIUM
        );
        List<Requirement> requirements = Arrays.asList(testRequirement, requirement2);
        when(requirementRepository.findByRequiredCapability("用户认证")).thenReturn(requirements);

        // Act
        List<Requirement> result = requirementRepository.findByRequiredCapability("用户认证");

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.get(0).getRequiredCapabilities().contains("用户认证"));
        assertTrue(result.get(1).getRequiredCapabilities().contains("用户认证"));
        
        Set<String> descriptions = new HashSet<>();
        for (Requirement requirement : result) {
            descriptions.add(requirement.getDescription());
        }
        assertTrue(descriptions.contains("需要一个用户管理系统"));
        assertTrue(descriptions.contains("需要一个安全系统"));
        
        verify(requirementRepository).findByRequiredCapability("用户认证");
    }

    @Test
    void testFindWithPaginationBoundaryConditions() {
        // Test with offset 0
        List<Requirement> firstPage = Arrays.asList(testRequirement);
        when(requirementRepository.findWithPagination(0, 1)).thenReturn(firstPage);

        List<Requirement> result1 = requirementRepository.findWithPagination(0, 1);
        assertNotNull(result1);
        assertEquals(1, result1.size());

        // Test with large offset
        when(requirementRepository.findWithPagination(1000, 10)).thenReturn(Collections.emptyList());

        List<Requirement> result2 = requirementRepository.findWithPagination(1000, 10);
        assertNotNull(result2);
        assertTrue(result2.isEmpty());

        verify(requirementRepository).findWithPagination(0, 1);
        verify(requirementRepository).findWithPagination(1000, 10);
    }
}
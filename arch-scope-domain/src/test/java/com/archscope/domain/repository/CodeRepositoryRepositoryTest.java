package com.archscope.domain.repository;

import com.archscope.domain.entity.CodeRepository;
import com.archscope.domain.valueobject.RepositoryStatus;
import com.archscope.domain.valueobject.RepositoryType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 代码仓库仓储接口测试
 */
public class CodeRepositoryRepositoryTest {

    @Mock
    private CodeRepositoryRepository codeRepositoryRepository;
    
    private CodeRepository testRepository;
    private List<CodeRepository> testRepositories;
    
    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
        
        // 创建测试数据
        testRepository = CodeRepository.builder()
                .id(1L)
                .name("测试仓库")
                .url("https://github.com/test/repo.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .status(RepositoryStatus.ACTIVE)
                .projectId(101L)
                .createdAt(LocalDateTime.now())
                .build();
                
        // 创建测试列表数据
        CodeRepository repository2 = CodeRepository.builder()
                .id(2L)
                .name("测试仓库2")
                .url("https://github.com/test/repo2.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .status(RepositoryStatus.ACTIVE)
                .projectId(101L)
                .createdAt(LocalDateTime.now())
                .build();
                
        testRepositories = Arrays.asList(testRepository, repository2);
        
        // 设置模拟数据
        when(codeRepositoryRepository.findById(1L)).thenReturn(Optional.of(testRepository));
        when(codeRepositoryRepository.findAll()).thenReturn(testRepositories);
        when(codeRepositoryRepository.findByProjectId(101L)).thenReturn(testRepositories);
        when(codeRepositoryRepository.findByUrl("https://github.com/test/repo.git")).thenReturn(Optional.of(testRepository));
        when(codeRepositoryRepository.save(any(CodeRepository.class))).thenReturn(testRepository);
        when(codeRepositoryRepository.update(any(CodeRepository.class))).thenReturn(testRepository);
    }
    
    @Test
    public void testFindById() {
        // Act
        Optional<CodeRepository> result = codeRepositoryRepository.findById(1L);
        
        // Assert
        assertTrue(result.isPresent());
        assertEquals(1L, result.get().getId());
        assertEquals("测试仓库", result.get().getName());
    }
    
    @Test
    public void testFindByIdNotFound() {
        // Arrange
        when(codeRepositoryRepository.findById(999L)).thenReturn(Optional.empty());
        
        // Act
        Optional<CodeRepository> result = codeRepositoryRepository.findById(999L);
        
        // Assert
        assertFalse(result.isPresent());
    }
    
    @Test
    public void testFindAll() {
        // Act
        List<CodeRepository> result = codeRepositoryRepository.findAll();
        
        // Assert
        assertEquals(2, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals(2L, result.get(1).getId());
    }
    
    @Test
    public void testFindByProjectId() {
        // Act
        List<CodeRepository> result = codeRepositoryRepository.findByProjectId(101L);
        
        // Assert
        assertEquals(2, result.size());
        assertEquals(101L, result.get(0).getProjectId());
        assertEquals(101L, result.get(1).getProjectId());
    }
    
    @Test
    public void testFindByUrl() {
        // Act
        Optional<CodeRepository> result = codeRepositoryRepository.findByUrl("https://github.com/test/repo.git");
        
        // Assert
        assertTrue(result.isPresent());
        assertEquals("https://github.com/test/repo.git", result.get().getUrl());
    }
    
    @Test
    public void testSave() {
        // Arrange
        CodeRepository newRepository = CodeRepository.builder()
                .name("新测试仓库")
                .url("https://github.com/test/new-repo.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .projectId(102L)
                .build();
                
        // 为新仓库设置模拟响应
        CodeRepository savedRepository = CodeRepository.builder()
                .id(3L)
                .name("新测试仓库")
                .url("https://github.com/test/new-repo.git")
                .type(RepositoryType.GIT)
                .defaultBranch("main")
                .projectId(102L)
                .createdAt(LocalDateTime.now())
                .status(RepositoryStatus.ACTIVE)
                .build();
                
        when(codeRepositoryRepository.save(newRepository)).thenReturn(savedRepository);
        
        // Act
        CodeRepository result = codeRepositoryRepository.save(newRepository);
        
        // Assert
        assertNotNull(result);
        assertEquals(savedRepository.getName(), result.getName());
        assertEquals(savedRepository.getUrl(), result.getUrl());
    }
    
    @Test
    public void testUpdate() {
        // Arrange
        CodeRepository updatedRepository = testRepository;
        updatedRepository.setName("更新后的名称");
        updatedRepository.setStatus(RepositoryStatus.INACTIVE);
        
        when(codeRepositoryRepository.update(updatedRepository)).thenReturn(updatedRepository);
        
        // Act
        CodeRepository result = codeRepositoryRepository.update(updatedRepository);
        
        // Assert
        assertEquals("更新后的名称", result.getName());
        assertEquals(RepositoryStatus.INACTIVE, result.getStatus());
    }
    
    @Test
    public void testDelete() {
        // Act & Assert - 确保不抛出异常
        codeRepositoryRepository.delete(1L);
    }
} 
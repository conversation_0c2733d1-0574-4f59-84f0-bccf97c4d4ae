package com.archscope.domain.integration;

import com.archscope.domain.entity.CodeRepository;
import com.archscope.domain.entity.Project;
import com.archscope.domain.model.parser.FileParseResult;
import com.archscope.domain.model.parser.LanguageType;
import com.archscope.domain.repository.ProjectRepository;
import com.archscope.domain.service.CodeRepositoryService;
import com.archscope.domain.service.CodeStatisticsService;
import com.archscope.domain.valueobject.CodeStatistics;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 代码解析和统计功能的集成测试
 */
@ExtendWith(MockitoExtension.class)
class CodeParseStatisticsIntegrationTest {

    @Mock
    private ProjectRepository projectRepository;

    @Mock
    private CodeRepositoryService codeRepositoryService;

    private CodeStatisticsService codeStatisticsService;

    private Project testProject;
    private CodeRepository testRepository;
    private List<FileParseResult> mockParseResults;

    @BeforeEach
    void setUp() {
        // 创建代码统计服务实例
        codeStatisticsService = new com.archscope.domain.service.impl.DefaultCodeStatisticsService(
                projectRepository, codeRepositoryService);

        // 创建测试项目
        testProject = Project.builder()
                .id(1L)
                .name("Test Project")
                .linesOfCode(0L)
                .fileCount(0)
                .contributorCount(0)
                .createdAt(LocalDateTime.now())
                .updatedAt(LocalDateTime.now())
                .build();

        // 创建测试仓库
        testRepository = CodeRepository.builder()
                .id(1L)
                .projectId(1L)
                .name("test-repo")
                .url("https://github.com/test/repo")
                .build();

        // 创建模拟的解析结果
        mockParseResults = Arrays.asList(
                FileParseResult.builder()
                        .filename("UserService.java")
                        .filePath("/src/main/java/com/example/UserService.java")
                        .languageType(LanguageType.JAVA)
                        .successful(true)
                        .build(),
                FileParseResult.builder()
                        .filename("UserController.java")
                        .filePath("/src/main/java/com/example/UserController.java")
                        .languageType(LanguageType.JAVA)
                        .successful(true)
                        .build(),
                FileParseResult.builder()
                        .filename("user.js")
                        .filePath("/src/main/resources/static/js/user.js")
                        .languageType(LanguageType.JAVASCRIPT)
                        .successful(true)
                        .build(),
                FileParseResult.builder()
                        .filename("failed.py")
                        .filePath("/src/test/python/failed.py")
                        .languageType(LanguageType.PYTHON)
                        .successful(false) // 解析失败的文件
                        .build()
        );
    }

    @Test
    void testCodeStatisticsCalculation() {
        // 执行统计计算（注意：由于文件不存在，代码行数会是0，但文件数量会被正确统计）
        CodeStatistics statistics = codeStatisticsService.calculateStatistics(mockParseResults);

        // 验证统计结果
        assertNotNull(statistics);
        assertEquals(4, statistics.getTotalFileCount()); // 包括失败的文件
        assertEquals(0L, statistics.getTotalLinesOfCode()); // 文件不存在，所以行数为0
        assertEquals(0L, statistics.getEffectiveLinesOfCode());
        assertNotNull(statistics.getFileCountByType());
        assertNotNull(statistics.getLinesOfCodeByLanguage());

        // 验证文件类型统计（包括成功和失败的文件）
        assertTrue(statistics.getFileCountByType().containsKey("java"));
        assertTrue(statistics.getFileCountByType().containsKey("js"));
        assertTrue(statistics.getFileCountByType().containsKey("py")); // 失败的文件也会被统计文件类型

        // 验证语言统计（由于文件不存在，语言统计可能为空或包含0值）
        assertNotNull(statistics.getLinesOfCodeByLanguage());
    }

    @Test
    void testProjectStatisticsUpdate() {
        // 准备测试数据
        CodeStatistics statistics = CodeStatistics.builder()
                .totalLinesOfCode(1500L)
                .effectiveLinesOfCode(1200L)
                .commentLines(200L)
                .blankLines(100L)
                .totalFileCount(15)
                .contributorCount(5)
                .build();

        // 模拟项目查找
        when(projectRepository.findById(1L)).thenReturn(Optional.of(testProject));
        when(projectRepository.save(any(Project.class))).thenReturn(testProject);

        // 执行统计信息更新
        codeStatisticsService.updateProjectStatistics(1L, statistics);

        // 验证项目统计信息被正确更新
        verify(projectRepository).findById(1L);
        verify(projectRepository).save(any(Project.class));

        // 验证项目字段被正确设置
        assertEquals(1500L, testProject.getLinesOfCode());
        assertEquals(15, testProject.getFileCount());
        assertEquals(5, testProject.getContributorCount());
        assertNotNull(testProject.getUpdatedAt());
    }

    @Test
    void testCodeStatisticsWithContributorCount() {
        // 模拟贡献者数量获取
        when(codeRepositoryService.getContributorCount(1L)).thenReturn(8);

        // 执行统计计算
        CodeStatistics statistics = codeStatisticsService.calculateStatistics(mockParseResults);

        // 获取贡献者数量
        int contributorCount = codeStatisticsService.getContributorCount(1L);

        // 验证贡献者数量
        assertEquals(8, contributorCount);
        verify(codeRepositoryService).getContributorCount(1L);
    }

    @Test
    void testCodeStatisticsWithFailedParsing() {
        // 创建包含更多失败解析的结果
        List<FileParseResult> mixedResults = Arrays.asList(
                FileParseResult.builder()
                        .filename("success.java")
                        .filePath("/src/main/java/success.java")
                        .languageType(LanguageType.JAVA)
                        .successful(true)
                        .build(),
                FileParseResult.builder()
                        .filename("failed1.py")
                        .filePath("/src/test/failed1.py")
                        .languageType(LanguageType.PYTHON)
                        .successful(false)
                        .build(),
                FileParseResult.builder()
                        .filename("failed2.js")
                        .filePath("/src/test/failed2.js")
                        .languageType(LanguageType.JAVASCRIPT)
                        .successful(false)
                        .build()
        );

        // 执行统计计算
        CodeStatistics statistics = codeStatisticsService.calculateStatistics(mixedResults);

        // 验证统计结果
        assertNotNull(statistics);
        assertEquals(3, statistics.getTotalFileCount()); // 包括失败的文件

        // 由于失败的文件不会被统计代码行数，所以只有成功解析的文件会被计算
        // 这里我们主要验证统计逻辑能正确处理失败的解析结果
        assertEquals(0L, statistics.getTotalLinesOfCode()); // 文件不存在，所以行数为0
        assertEquals(0L, statistics.getEffectiveLinesOfCode());
    }

    @Test
    void testEmptyParseResults() {
        // 测试空的解析结果
        List<FileParseResult> emptyResults = Arrays.asList();

        // 执行统计计算
        CodeStatistics statistics = codeStatisticsService.calculateStatistics(emptyResults);

        // 验证统计结果
        assertNotNull(statistics);
        assertEquals(0, statistics.getTotalFileCount());
        assertEquals(0L, statistics.getTotalLinesOfCode());
        assertEquals(0L, statistics.getEffectiveLinesOfCode());
        assertEquals(0L, statistics.getCommentLines());
        assertEquals(0L, statistics.getBlankLines());
        assertNotNull(statistics.getFileCountByType());
        assertNotNull(statistics.getLinesOfCodeByLanguage());
    }

    @Test
    void testStatisticsMerge() {
        // 创建两个统计对象
        CodeStatistics stats1 = CodeStatistics.builder()
                .totalLinesOfCode(1000L)
                .effectiveLinesOfCode(800L)
                .commentLines(150L)
                .blankLines(50L)
                .totalFileCount(10)
                .contributorCount(3)
                .build();

        CodeStatistics stats2 = CodeStatistics.builder()
                .totalLinesOfCode(500L)
                .effectiveLinesOfCode(400L)
                .commentLines(75L)
                .blankLines(25L)
                .totalFileCount(5)
                .contributorCount(5)
                .build();

        // 执行合并
        CodeStatistics merged = stats1.merge(stats2);

        // 验证合并结果
        assertNotNull(merged);
        assertEquals(1500L, merged.getTotalLinesOfCode());
        assertEquals(1200L, merged.getEffectiveLinesOfCode());
        assertEquals(225L, merged.getCommentLines());
        assertEquals(75L, merged.getBlankLines());
        assertEquals(15, merged.getTotalFileCount());
        assertEquals(5, merged.getContributorCount()); // 取最大值
    }
}

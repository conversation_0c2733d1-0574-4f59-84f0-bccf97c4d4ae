package com.archscope.domain.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 分支过滤工具类测试 - 精简版（只过滤包含"/"的分支）
 */
class BranchFilterUtilTest {

    @Test
    @DisplayName("应该过滤掉包含'/'的分支")
    void shouldFilterBranchesWithSlash() {
        List<String> branches = Arrays.asList(
            "main",
            "develop",
            "release/v1.0.0",
            "release/v2.1.0",
            "releases/hotfix",
            "rel/1.0",
            "release-candidate",  // 不包含'/'，应该保留
            "v1.0.0",            // 不包含'/'，应该保留
            "v2.1.0-beta",       // 不包含'/'，应该保留
            "feature/user-login",
            "feature/payment",
            "feat/dashboard"
        );

        List<String> filtered = BranchFilterUtil.filterBranches(branches);

        System.out.println("=== 包含'/'的分支过滤测试 ===");
        System.out.println("原始分支: " + branches);
        System.out.println("过滤后分支: " + filtered);

        // 验证不包含'/'的分支被保留
        assertTrue(filtered.contains("main"), "main分支应该被保留");
        assertTrue(filtered.contains("develop"), "develop分支应该被保留");
        assertTrue(filtered.contains("release-candidate"), "release-candidate分支应该被保留");
        assertTrue(filtered.contains("v1.0.0"), "v1.0.0分支应该被保留");
        assertTrue(filtered.contains("v2.1.0-beta"), "v2.1.0-beta分支应该被保留");

        // 验证包含'/'的分支被过滤
        assertFalse(filtered.contains("release/v1.0.0"), "release/v1.0.0分支应该被过滤");
        assertFalse(filtered.contains("release/v2.1.0"), "release/v2.1.0分支应该被过滤");
        assertFalse(filtered.contains("releases/hotfix"), "releases/hotfix分支应该被过滤");
        assertFalse(filtered.contains("rel/1.0"), "rel/1.0分支应该被过滤");
        assertFalse(filtered.contains("feature/user-login"), "feature/user-login分支应该被过滤");
        assertFalse(filtered.contains("feature/payment"), "feature/payment分支应该被过滤");
        assertFalse(filtered.contains("feat/dashboard"), "feat/dashboard分支应该被过滤");
    }

    @Test
    @DisplayName("应该保留不包含'/'的分支")
    void shouldKeepBranchesWithoutSlash() {
        List<String> branches = Arrays.asList(
            "main",
            "master",
            "develop",
            "feature-branch",     // 不包含'/'，应该保留
            "hotfix-urgent",      // 不包含'/'，应该保留
            "release-candidate",  // 不包含'/'，应该保留
            "staging",
            "production",
            "test",
            "qa"
        );

        List<String> filtered = BranchFilterUtil.filterBranches(branches);

        System.out.println("=== 不包含'/'的分支保留测试 ===");
        System.out.println("原始分支: " + branches);
        System.out.println("过滤后分支: " + filtered);

        // 验证所有不包含'/'的分支都被保留
        assertEquals(branches.size(), filtered.size(), "所有不包含'/'的分支都应该被保留");
        for (String branch : branches) {
            assertTrue(filtered.contains(branch), branch + "分支应该被保留");
        }
    }

    @Test
    @DisplayName("应该处理空列表和null")
    void shouldHandleEmptyAndNullLists() {
        // 测试null
        assertNull(BranchFilterUtil.filterBranches(null));

        // 测试空列表
        List<String> emptyList = Collections.emptyList();
        List<String> result = BranchFilterUtil.filterBranches(emptyList);
        assertTrue(result.isEmpty());
    }

    @Test
    @DisplayName("应该处理包含null和空字符串的分支")
    void shouldHandleNullAndEmptyBranches() {
        List<String> branches = Arrays.asList(
            "main",
            null,
            "",
            "  ",
            "develop",
            "feature/test"
        );

        List<String> filtered = BranchFilterUtil.filterBranches(branches);

        System.out.println("=== null和空字符串处理测试 ===");
        System.out.println("原始分支: " + branches);
        System.out.println("过滤后分支: " + filtered);

        // 应该保留有效的分支
        assertTrue(filtered.contains("main"));
        assertTrue(filtered.contains("develop"));

        // 应该过滤掉null、空字符串和包含'/'的分支
        assertFalse(filtered.contains(null));
        assertFalse(filtered.contains(""));
        assertFalse(filtered.contains("  "));
        assertFalse(filtered.contains("feature/test"));

        assertEquals(2, filtered.size());
    }

    @Test
    @DisplayName("shouldKeepBranch方法应该正确判断单个分支")
    void shouldKeepBranchMethodTest() {
        // 应该保留的分支（不包含'/'）
        assertTrue(BranchFilterUtil.shouldKeepBranch("main"));
        assertTrue(BranchFilterUtil.shouldKeepBranch("master"));
        assertTrue(BranchFilterUtil.shouldKeepBranch("develop"));
        assertTrue(BranchFilterUtil.shouldKeepBranch("staging"));
        assertTrue(BranchFilterUtil.shouldKeepBranch("production"));
        assertTrue(BranchFilterUtil.shouldKeepBranch("v1.0"));
        assertTrue(BranchFilterUtil.shouldKeepBranch("release-1.0"));
        assertTrue(BranchFilterUtil.shouldKeepBranch("feature-branch"));
        assertTrue(BranchFilterUtil.shouldKeepBranch("hotfix-urgent"));

        // 应该过滤掉的分支（包含'/'）
        assertFalse(BranchFilterUtil.shouldKeepBranch("feature/login"));
        assertFalse(BranchFilterUtil.shouldKeepBranch("release/v1.0"));
        assertFalse(BranchFilterUtil.shouldKeepBranch("hotfix/bug"));
        assertFalse(BranchFilterUtil.shouldKeepBranch("bugfix/issue"));
        assertFalse(BranchFilterUtil.shouldKeepBranch("user/john/feature"));
        assertFalse(BranchFilterUtil.shouldKeepBranch("temp/test"));

        // 应该过滤掉的无效分支
        assertFalse(BranchFilterUtil.shouldKeepBranch(null));
        assertFalse(BranchFilterUtil.shouldKeepBranch(""));
        assertFalse(BranchFilterUtil.shouldKeepBranch("  "));
    }

    @Test
    @DisplayName("应该正确处理各种包含'/'的分支模式")
    void shouldFilterVariousSlashPatterns() {
        List<String> branches = Arrays.asList(
            // 保留的分支（不包含'/'）
            "main",
            "master",
            "develop",
            "MAIN",           // 大小写不同
            "Master",         // 大小写不同
            "DEVELOP",        // 大小写不同

            // 过滤的分支（包含'/'）
            "feature/user-auth",
            "release/v2.0.0",
            "hotfix/critical-fix",
            "bugfix/login-error",
            "experiment/new-ui",
            "personal/john/test",
            "temp/quick-fix",
            "backup/old-code",
            "user/jane/feature",
            "team/frontend/refactor",
            "a/b",
            "x/y/z",
            "Release/V1.0",   // 大小写不同但包含'/'
            "FEATURE/LOGIN",  // 大小写不同但包含'/'
            "Hotfix/Bug"      // 大小写不同但包含'/'
        );

        List<String> filtered = BranchFilterUtil.filterBranches(branches);

        System.out.println("=== 各种包含'/'的分支模式测试 ===");
        System.out.println("原始分支: " + branches);
        System.out.println("过滤后分支: " + filtered);

        // 验证只保留了不包含'/'的分支
        assertEquals(6, filtered.size());
        assertTrue(filtered.contains("main"));
        assertTrue(filtered.contains("master"));
        assertTrue(filtered.contains("develop"));
        assertTrue(filtered.contains("MAIN"));
        assertTrue(filtered.contains("Master"));
        assertTrue(filtered.contains("DEVELOP"));

        // 验证所有包含'/'的分支都被过滤掉了
        filtered.forEach(branch -> assertFalse(branch.contains("/")));
    }

    @Test
    @DisplayName("getFilterDescription方法应该返回正确的描述")
    void getFilterDescriptionTest() {
        String description = BranchFilterUtil.getFilterDescription();

        System.out.println("=== 过滤规则描述 ===");
        System.out.println(description);

        assertNotNull(description);
        assertTrue(description.contains("过滤掉所有包含'/'的分支"));
        assertTrue(description.contains("保留主要分支"));
    }

    @Test
    @DisplayName("应该正确处理边界情况")
    void shouldHandleEdgeCases() {
        List<String> branches = Arrays.asList(
            "/",           // 只有斜杠
            "//",          // 多个斜杠
            "branch/",     // 末尾有斜杠
            "/branch",     // 开头有斜杠
            "a/b/c/d",     // 多个斜杠
            "normal-branch", // 正常分支
            "branch_name",   // 下划线分支
            "branch.name"    // 点号分支
        );

        List<String> filtered = BranchFilterUtil.filterBranches(branches);

        System.out.println("=== 边界情况测试 ===");
        System.out.println("原始分支: " + branches);
        System.out.println("过滤后分支: " + filtered);

        // 只有不包含'/'的分支应该被保留
        assertEquals(3, filtered.size());
        assertTrue(filtered.contains("normal-branch"));
        assertTrue(filtered.contains("branch_name"));
        assertTrue(filtered.contains("branch.name"));

        // 所有包含'/'的分支都应该被过滤掉
        assertFalse(filtered.contains("/"));
        assertFalse(filtered.contains("//"));
        assertFalse(filtered.contains("branch/"));
        assertFalse(filtered.contains("/branch"));
        assertFalse(filtered.contains("a/b/c/d"));
    }
}

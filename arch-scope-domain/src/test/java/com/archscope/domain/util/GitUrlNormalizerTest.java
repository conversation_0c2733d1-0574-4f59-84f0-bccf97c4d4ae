package com.archscope.domain.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.junit.jupiter.params.provider.CsvSource;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GitUrlNormalizer工具类测试
 * 测试URL标准化功能，确保能正确处理各种格式的Git仓库URL
 */
@DisplayName("Git URL标准化工具测试")
class GitUrlNormalizerTest {

    @Test
    @DisplayName("标准化HTTPS URL - 移除.git后缀")
    void normalize_HttpsUrlWithGitSuffix_ShouldRemoveGitSuffix() {
        // Given
        String url = "https://github.com/user/repo.git";
        String expected = "https://github.com/user/repo";

        // When
        String result = GitUrlNormalizer.normalize(url);

        // Then
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("标准化HTTPS URL - 移除尾部斜杠")
    void normalize_HttpsUrlWithTrailingSlash_ShouldRemoveTrailingSlash() {
        // Given
        String url = "https://github.com/user/repo/";
        String expected = "https://github.com/user/repo";

        // When
        String result = GitUrlNormalizer.normalize(url);

        // Then
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("标准化HTTPS URL - 移除.git后缀和尾部斜杠")
    void normalize_HttpsUrlWithGitSuffixAndTrailingSlash_ShouldRemoveBoth() {
        // Given
        String url = "https://github.com/user/repo.git/";
        String expected = "https://github.com/user/repo";

        // When
        String result = GitUrlNormalizer.normalize(url);

        // Then
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("标准化SSH URL - 转换为HTTPS格式")
    void normalize_SshUrl_ShouldConvertToHttps() {
        // Given
        String url = "**************:user/repo.git";
        String expected = "https://github.com/user/repo";

        // When
        String result = GitUrlNormalizer.normalize(url);

        // Then
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("标准化HTTP URL - 转换为HTTPS")
    void normalize_HttpUrl_ShouldConvertToHttps() {
        // Given
        String url = "http://github.com/user/repo";
        String expected = "https://github.com/user/repo";

        // When
        String result = GitUrlNormalizer.normalize(url);

        // Then
        assertEquals(expected, result);
    }

    @ParameterizedTest
    @DisplayName("标准化各种GitLab URL格式")
    @CsvSource({
        "https://gitlab.com/user/repo.git, https://gitlab.com/user/repo",
        "https://gitlab.com/user/repo/, https://gitlab.com/user/repo",
        "https://gitlab.com/user/repo.git/, https://gitlab.com/user/repo",
        "**************:user/repo.git, https://gitlab.com/user/repo",
        "http://gitlab.yeepay.com/yop/yop-center.git, https://gitlab.yeepay.com/yop/yop-center",
        "http://gitlab.yeepay.com/yop/yop-center/, https://gitlab.yeepay.com/yop/yop-center",
        "http://gitlab.yeepay.com/yop/yop-center.git/, https://gitlab.yeepay.com/yop/yop-center"
    })
    void normalize_VariousGitLabUrls_ShouldNormalizeCorrectly(String input, String expected) {
        // When
        String result = GitUrlNormalizer.normalize(input);

        // Then
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("标准化空URL - 应返回null")
    void normalize_EmptyUrl_ShouldReturnNull() {
        // Given
        String url = "";

        // When
        String result = GitUrlNormalizer.normalize(url);

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("标准化null URL - 应返回null")
    void normalize_NullUrl_ShouldReturnNull() {
        // When
        String result = GitUrlNormalizer.normalize(null);

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("标准化无效URL - 应返回null")
    void normalize_InvalidUrl_ShouldReturnNull() {
        // Given
        String url = "invalid-url";

        // When
        String result = GitUrlNormalizer.normalize(url);

        // Then
        assertNull(result);
    }

    @Test
    @DisplayName("检查相同仓库 - 不同格式的相同仓库应返回true")
    void isSameRepository_DifferentFormatsOfSameRepo_ShouldReturnTrue() {
        // Given
        String httpsUrl = "https://github.com/user/repo.git";
        String sshUrl = "**************:user/repo";
        String httpUrl = "http://github.com/user/repo/";

        // When & Then
        assertTrue(GitUrlNormalizer.isSameRepository(httpsUrl, sshUrl));
        assertTrue(GitUrlNormalizer.isSameRepository(httpsUrl, httpUrl));
        assertTrue(GitUrlNormalizer.isSameRepository(sshUrl, httpUrl));
    }

    @Test
    @DisplayName("检查不同仓库 - 不同仓库应返回false")
    void isSameRepository_DifferentRepos_ShouldReturnFalse() {
        // Given
        String url1 = "https://github.com/user1/repo1";
        String url2 = "https://github.com/user2/repo2";

        // When
        boolean result = GitUrlNormalizer.isSameRepository(url1, url2);

        // Then
        assertFalse(result);
    }

    @Test
    @DisplayName("验证有效Git URL")
    void isValidGitUrl_ValidUrls_ShouldReturnTrue() {
        // Given
        String[] validUrls = {
            "https://github.com/user/repo",
            "https://github.com/user/repo.git",
            "http://gitlab.com/user/repo",
            "**************:user/repo.git"
        };

        // When & Then
        for (String url : validUrls) {
            assertTrue(GitUrlNormalizer.isValidGitUrl(url), "URL应该有效: " + url);
        }
    }

    @ParameterizedTest
    @DisplayName("验证无效Git URL")
    @ValueSource(strings = {
        "",
        "invalid-url",
        "ftp://github.com/user/repo",
        "https://github.com",
        "github.com/user/repo"
    })
    void isValidGitUrl_InvalidUrls_ShouldReturnFalse(String url) {
        // When
        boolean result = GitUrlNormalizer.isValidGitUrl(url);

        // Then
        assertFalse(result, "URL应该无效: " + url);
    }

    @Test
    @DisplayName("提取主机名")
    void extractHost_ValidUrls_ShouldExtractCorrectHost() {
        // Given & When & Then
        assertEquals("github.com", GitUrlNormalizer.extractHost("https://github.com/user/repo"));
        assertEquals("gitlab.com", GitUrlNormalizer.extractHost("**************:user/repo"));
        assertEquals("gitlab.yeepay.com", GitUrlNormalizer.extractHost("http://gitlab.yeepay.com/yop/center"));
    }

    @Test
    @DisplayName("提取所有者")
    void extractOwner_ValidUrls_ShouldExtractCorrectOwner() {
        // Given & When & Then
        assertEquals("user", GitUrlNormalizer.extractOwner("https://github.com/user/repo"));
        assertEquals("user", GitUrlNormalizer.extractOwner("**************:user/repo"));
        assertEquals("yop", GitUrlNormalizer.extractOwner("http://gitlab.yeepay.com/yop/center"));
    }

    @Test
    @DisplayName("提取仓库名称")
    void extractRepositoryName_ValidUrls_ShouldExtractCorrectName() {
        // Given & When & Then
        assertEquals("repo", GitUrlNormalizer.extractRepositoryName("https://github.com/user/repo"));
        assertEquals("repo", GitUrlNormalizer.extractRepositoryName("**************:user/repo.git"));
        assertEquals("center", GitUrlNormalizer.extractRepositoryName("http://gitlab.yeepay.com/yop/center/"));
    }

    @Test
    @DisplayName("获取显示URL")
    void getDisplayUrl_ValidUrl_ShouldReturnNormalizedUrl() {
        // Given
        String url = "https://github.com/user/repo.git/";
        String expected = "https://github.com/user/repo";

        // When
        String result = GitUrlNormalizer.getDisplayUrl(url);

        // Then
        assertEquals(expected, result);
    }

    @Test
    @DisplayName("获取显示URL - 无效URL应返回原始URL")
    void getDisplayUrl_InvalidUrl_ShouldReturnOriginalUrl() {
        // Given
        String url = "invalid-url";

        // When
        String result = GitUrlNormalizer.getDisplayUrl(url);

        // Then
        assertEquals(url, result);
    }
}

package com.archscope.domain.valueobject;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * ProjectType枚举测试类
 * 验证类型转换和历史数据兼容性
 */
class ProjectTypeTest {

    @Test
    void fromString_ValidEnumValue_ShouldReturnCorrectType() {
        // Given & When & Then
        assertEquals(ProjectType.JAVA, ProjectType.fromString("JAVA"));
        assertEquals(ProjectType.JAVASCRIPT, ProjectType.fromString("JAVASCRIPT"));
        assertEquals(ProjectType.PYTHON, ProjectType.fromString("PYTHON"));
        assertEquals(ProjectType.GO, ProjectType.fromString("GO"));
        assertEquals(ProjectType.CSHARP, ProjectType.fromString("CSHARP"));
        assertEquals(ProjectType.KOTLIN, ProjectType.fromString("KOTLIN"));
        assertEquals(ProjectType.RUST, ProjectType.fromString("RUST"));
        assertEquals(ProjectType.PHP, ProjectType.fromString("PHP"));
        assertEquals(ProjectType.TYPESCRIPT, ProjectType.fromString("TYPESCRIPT"));
        assertEquals(ProjectType.OTHER, ProjectType.fromString("OTHER"));
    }

    @Test
    void fromString_CaseInsensitive_ShouldWork() {
        // Given & When & Then
        assertEquals(ProjectType.JAVA, ProjectType.fromString("java"));
        assertEquals(ProjectType.JAVASCRIPT, ProjectType.fromString("javascript"));
        assertEquals(ProjectType.PYTHON, ProjectType.fromString("python"));
    }



    @Test
    void fromString_NullOrEmpty_ShouldReturnOther() {
        // Given & When & Then
        assertEquals(ProjectType.OTHER, ProjectType.fromString(null));
        assertEquals(ProjectType.OTHER, ProjectType.fromString(""));
        assertEquals(ProjectType.OTHER, ProjectType.fromString("   "));
    }

    @Test
    void fromString_UnknownType_ShouldReturnOther() {
        // Given & When & Then
        assertEquals(ProjectType.OTHER, ProjectType.fromString("UNKNOWN_TYPE"));
        assertEquals(ProjectType.OTHER, ProjectType.fromString("INVALID"));
        assertEquals(ProjectType.OTHER, ProjectType.fromString("123"));
        assertEquals(ProjectType.OTHER, ProjectType.fromString("@#$%"));
    }

    @Test
    void fromString_HistoricalDataCompatibility_ShouldHandleGracefully() {
        // 测试历史数据中可能存在的类型
        // Given & When & Then
        assertEquals(ProjectType.JAVA, ProjectType.fromString("JAVA"));
        assertEquals(ProjectType.JAVASCRIPT, ProjectType.fromString("REACT"));
        assertEquals(ProjectType.KOTLIN, ProjectType.fromString("ANDROID"));
        assertEquals(ProjectType.OTHER, ProjectType.fromString("REST"));
        assertEquals(ProjectType.OTHER, ProjectType.fromString("LEGACY_TYPE"));
    }

    @Test
    void fromString_EdgeCases_ShouldHandleCorrectly() {
        // Given & When & Then
        assertEquals(ProjectType.OTHER, ProjectType.fromString(""));
        assertEquals(ProjectType.OTHER, ProjectType.fromString(" "));
        assertEquals(ProjectType.OTHER, ProjectType.fromString("\t"));
        assertEquals(ProjectType.OTHER, ProjectType.fromString("\n"));
        assertEquals(ProjectType.JAVA, ProjectType.fromString(" JAVA "));
        assertEquals(ProjectType.OTHER, ProjectType.fromString(" WEB "));
    }

    @Test
    void enumValues_ShouldContainExpectedTypes() {
        // Given
        ProjectType[] expectedTypes = {
            ProjectType.JAVA,
            ProjectType.JAVASCRIPT,
            ProjectType.PYTHON,
            ProjectType.GO,
            ProjectType.CSHARP,
            ProjectType.KOTLIN,
            ProjectType.RUST,
            ProjectType.PHP,
            ProjectType.TYPESCRIPT,
            ProjectType.OTHER
        };

        // When
        ProjectType[] actualTypes = ProjectType.values();

        // Then
        assertEquals(expectedTypes.length, actualTypes.length);
        for (ProjectType expectedType : expectedTypes) {
            boolean found = false;
            for (ProjectType actualType : actualTypes) {
                if (expectedType == actualType) {
                    found = true;
                    break;
                }
            }
            assertTrue(found, "Expected type " + expectedType + " not found in enum values");
        }
    }
}

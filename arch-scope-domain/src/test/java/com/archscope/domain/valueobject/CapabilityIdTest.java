package com.archscope.domain.valueobject;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.Test;

/**
 * 能力ID值对象测试
 */
class CapabilityIdTest {

    @Test
    void testCreateCapabilityIdWithValue() {
        // Arrange
        String value = "test-capability-id";

        // Act
        CapabilityId capabilityId = CapabilityId.of(value);

        // Assert
        assertNotNull(capabilityId);
        assertEquals(value, capabilityId.getValue());
    }

    @Test
    void testCreateNewCapabilityId() {
        // Act
        CapabilityId capabilityId = CapabilityId.createNew();

        // Assert
        assertNotNull(capabilityId);
        assertNotNull(capabilityId.getValue());
        assertFalse(capabilityId.getValue().isEmpty());
        // UUID format validation
        assertTrue(capabilityId.getValue().matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"));
    }

    @Test
    void testCreateNewCapabilityIdGeneratesUniqueValues() {
        // Act
        CapabilityId capabilityId1 = CapabilityId.createNew();
        CapabilityId capabilityId2 = CapabilityId.createNew();

        // Assert
        assertNotEquals(capabilityId1.getValue(), capabilityId2.getValue());
        assertNotEquals(capabilityId1, capabilityId2);
    }

    @Test
    void testEqualsAndHashCode() {
        // Arrange
        String value = "test-capability-id";
        CapabilityId capabilityId1 = CapabilityId.of(value);
        CapabilityId capabilityId2 = CapabilityId.of(value);
        CapabilityId capabilityId3 = CapabilityId.of("different-capability-id");

        // Assert
        assertEquals(capabilityId1, capabilityId2);
        assertNotEquals(capabilityId1, capabilityId3);
        assertEquals(capabilityId1.hashCode(), capabilityId2.hashCode());
        assertNotEquals(capabilityId1.hashCode(), capabilityId3.hashCode());
    }

    @Test
    void testEqualsWithNull() {
        // Arrange
        CapabilityId capabilityId = CapabilityId.of("test-capability-id");

        // Assert
        assertNotEquals(capabilityId, null);
    }

    @Test
    void testEqualsWithDifferentType() {
        // Arrange
        CapabilityId capabilityId = CapabilityId.of("test-capability-id");
        String string = "test-capability-id";

        // Assert
        assertNotEquals(capabilityId, string);
    }

    @Test
    void testEqualsWithSameInstance() {
        // Arrange
        CapabilityId capabilityId = CapabilityId.of("test-capability-id");

        // Assert
        assertEquals(capabilityId, capabilityId);
    }

    @Test
    void testToString() {
        // Arrange
        String value = "test-capability-id";
        CapabilityId capabilityId = CapabilityId.of(value);

        // Act
        String toString = capabilityId.toString();

        // Assert
        assertEquals(value, toString);
    }

    @Test
    void testCreateWithNullValue() {
        // Act & Assert
        assertDoesNotThrow(() -> CapabilityId.of((String)null));
        CapabilityId capabilityId = CapabilityId.of((String)null);
        assertNull(capabilityId.getValue());
    }

    @Test
    void testCreateWithEmptyValue() {
        // Arrange
        String emptyValue = "";

        // Act
        CapabilityId capabilityId = CapabilityId.of(emptyValue);

        // Assert
        assertEquals(emptyValue, capabilityId.getValue());
    }
}
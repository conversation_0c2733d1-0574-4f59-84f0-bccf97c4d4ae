package com.archscope.domain.valueobject;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 项目状态枚举测试
 */
class ProjectStatusTest {

    @Test
    void testProjectStatusValues() {
        // 验证所有状态值
        assertEquals(6, ProjectStatus.values().length);
        
        // 验证状态名称
        assertEquals("PENDING_ANALYSIS", ProjectStatus.PENDING_ANALYSIS.name());
        assertEquals("ANALYZING", ProjectStatus.ANALYZING.name());
        assertEquals("ANALYSIS_COMPLETED", ProjectStatus.ANALYSIS_COMPLETED.name());
        assertEquals("ANALYSIS_FAILED", ProjectStatus.ANALYSIS_FAILED.name());
        assertEquals("AVAILABLE", ProjectStatus.AVAILABLE.name());
        assertEquals("UNAVAILABLE", ProjectStatus.UNAVAILABLE.name());
    }

    @Test
    void testDisplayNames() {
        assertEquals("待分析", ProjectStatus.PENDING_ANALYSIS.getDisplayName());
        assertEquals("分析中", ProjectStatus.ANALYZING.getDisplayName());
        assertEquals("分析完成", ProjectStatus.ANALYSIS_COMPLETED.getDisplayName());
        assertEquals("分析失败", ProjectStatus.ANALYSIS_FAILED.getDisplayName());
        assertEquals("可用", ProjectStatus.AVAILABLE.getDisplayName());
        assertEquals("不可用", ProjectStatus.UNAVAILABLE.getDisplayName());
    }

    @Test
    void testIsFinalStatus() {
        // 终态状态
        assertTrue(ProjectStatus.ANALYSIS_COMPLETED.isFinalStatus());
        assertTrue(ProjectStatus.ANALYSIS_FAILED.isFinalStatus());
        assertTrue(ProjectStatus.UNAVAILABLE.isFinalStatus());
        
        // 非终态状态
        assertFalse(ProjectStatus.PENDING_ANALYSIS.isFinalStatus());
        assertFalse(ProjectStatus.ANALYZING.isFinalStatus());
        assertFalse(ProjectStatus.AVAILABLE.isFinalStatus());
    }

    @Test
    void testCanStartAnalysis() {
        // 可以开始分析的状态
        assertTrue(ProjectStatus.PENDING_ANALYSIS.canStartAnalysis());
        assertTrue(ProjectStatus.ANALYSIS_FAILED.canStartAnalysis());
        
        // 不能开始分析的状态
        assertFalse(ProjectStatus.ANALYZING.canStartAnalysis());
        assertFalse(ProjectStatus.ANALYSIS_COMPLETED.canStartAnalysis());
        assertFalse(ProjectStatus.AVAILABLE.canStartAnalysis());
        assertFalse(ProjectStatus.UNAVAILABLE.canStartAnalysis());
    }

    @Test
    void testCanGenerateDocumentation() {
        // 可以生成文档的状态
        assertTrue(ProjectStatus.ANALYSIS_COMPLETED.canGenerateDocumentation());
        
        // 不能生成文档的状态
        assertFalse(ProjectStatus.PENDING_ANALYSIS.canGenerateDocumentation());
        assertFalse(ProjectStatus.ANALYZING.canGenerateDocumentation());
        assertFalse(ProjectStatus.ANALYSIS_FAILED.canGenerateDocumentation());
        assertFalse(ProjectStatus.AVAILABLE.canGenerateDocumentation());
        assertFalse(ProjectStatus.UNAVAILABLE.canGenerateDocumentation());
    }

    @Test
    void testIsProcessing() {
        // 正在处理的状态
        assertTrue(ProjectStatus.ANALYZING.isProcessing());
        
        // 非处理状态
        assertFalse(ProjectStatus.PENDING_ANALYSIS.isProcessing());
        assertFalse(ProjectStatus.ANALYSIS_COMPLETED.isProcessing());
        assertFalse(ProjectStatus.ANALYSIS_FAILED.isProcessing());
        assertFalse(ProjectStatus.AVAILABLE.isProcessing());
        assertFalse(ProjectStatus.UNAVAILABLE.isProcessing());
    }

    @Test
    void testGetNextStatus() {
        assertEquals(ProjectStatus.ANALYZING, ProjectStatus.PENDING_ANALYSIS.getNextStatus());
        assertEquals(ProjectStatus.ANALYSIS_COMPLETED, ProjectStatus.ANALYZING.getNextStatus());
        assertEquals(ProjectStatus.AVAILABLE, ProjectStatus.ANALYSIS_COMPLETED.getNextStatus());
        assertEquals(ProjectStatus.PENDING_ANALYSIS, ProjectStatus.ANALYSIS_FAILED.getNextStatus());
        assertEquals(ProjectStatus.UNAVAILABLE, ProjectStatus.AVAILABLE.getNextStatus());
        assertEquals(ProjectStatus.AVAILABLE, ProjectStatus.UNAVAILABLE.getNextStatus());
    }

    @Test
    void testGetFailureStatus() {
        assertEquals(ProjectStatus.ANALYSIS_FAILED, ProjectStatus.PENDING_ANALYSIS.getFailureStatus());
        assertEquals(ProjectStatus.ANALYSIS_FAILED, ProjectStatus.ANALYZING.getFailureStatus());
        assertEquals(ProjectStatus.UNAVAILABLE, ProjectStatus.ANALYSIS_COMPLETED.getFailureStatus());
        assertEquals(ProjectStatus.UNAVAILABLE, ProjectStatus.AVAILABLE.getFailureStatus());
        assertEquals(ProjectStatus.ANALYSIS_FAILED, ProjectStatus.ANALYSIS_FAILED.getFailureStatus());
        assertEquals(ProjectStatus.UNAVAILABLE, ProjectStatus.UNAVAILABLE.getFailureStatus());
    }

    @Test
    void testFromString() {
        // 正常情况
        assertEquals(ProjectStatus.PENDING_ANALYSIS, ProjectStatus.fromString("PENDING_ANALYSIS"));
        assertEquals(ProjectStatus.ANALYZING, ProjectStatus.fromString("ANALYZING"));
        assertEquals(ProjectStatus.ANALYSIS_COMPLETED, ProjectStatus.fromString("ANALYSIS_COMPLETED"));
        
        // 大小写不敏感
        assertEquals(ProjectStatus.PENDING_ANALYSIS, ProjectStatus.fromString("pending_analysis"));
        assertEquals(ProjectStatus.ANALYZING, ProjectStatus.fromString("analyzing"));
        
        // 空值默认处理
        assertEquals(ProjectStatus.PENDING_ANALYSIS, ProjectStatus.fromString(null));
        assertEquals(ProjectStatus.PENDING_ANALYSIS, ProjectStatus.fromString(""));
        assertEquals(ProjectStatus.PENDING_ANALYSIS, ProjectStatus.fromString("   "));
        
        // 无效值抛异常
        assertThrows(IllegalArgumentException.class, () -> ProjectStatus.fromString("INVALID_STATUS"));
        assertThrows(IllegalArgumentException.class, () -> ProjectStatus.fromString("PENDING")); // 旧状态值
        assertThrows(IllegalArgumentException.class, () -> ProjectStatus.fromString("COMPLETED")); // 旧状态值
    }

    @Test
    void testIsValidStatus() {
        // 有效状态
        assertTrue(ProjectStatus.isValidStatus("PENDING_ANALYSIS"));
        assertTrue(ProjectStatus.isValidStatus("ANALYZING"));
        assertTrue(ProjectStatus.isValidStatus("ANALYSIS_COMPLETED"));
        assertTrue(ProjectStatus.isValidStatus("ANALYSIS_FAILED"));
        assertTrue(ProjectStatus.isValidStatus("AVAILABLE"));
        assertTrue(ProjectStatus.isValidStatus("UNAVAILABLE"));
        
        // 大小写不敏感
        assertTrue(ProjectStatus.isValidStatus("pending_analysis"));
        assertTrue(ProjectStatus.isValidStatus("analyzing"));
        
        // 无效状态
        assertFalse(ProjectStatus.isValidStatus("INVALID_STATUS"));
        assertFalse(ProjectStatus.isValidStatus("PENDING")); // 旧状态值
        assertFalse(ProjectStatus.isValidStatus("COMPLETED")); // 旧状态值
        assertFalse(ProjectStatus.isValidStatus(null));
        assertFalse(ProjectStatus.isValidStatus(""));
        assertFalse(ProjectStatus.isValidStatus("   "));
    }

    @Test
    void testGetAllStatusStrings() {
        String[] statusStrings = ProjectStatus.getAllStatusStrings();
        
        assertEquals(6, statusStrings.length);
        
        // 验证包含所有状态
        boolean foundPendingAnalysis = false;
        boolean foundAnalyzing = false;
        boolean foundAnalysisCompleted = false;
        boolean foundAnalysisFailed = false;
        boolean foundAvailable = false;
        boolean foundUnavailable = false;
        
        for (String status : statusStrings) {
            switch (status) {
                case "PENDING_ANALYSIS":
                    foundPendingAnalysis = true;
                    break;
                case "ANALYZING":
                    foundAnalyzing = true;
                    break;
                case "ANALYSIS_COMPLETED":
                    foundAnalysisCompleted = true;
                    break;
                case "ANALYSIS_FAILED":
                    foundAnalysisFailed = true;
                    break;
                case "AVAILABLE":
                    foundAvailable = true;
                    break;
                case "UNAVAILABLE":
                    foundUnavailable = true;
                    break;
            }
        }
        
        assertTrue(foundPendingAnalysis, "应该包含PENDING_ANALYSIS状态");
        assertTrue(foundAnalyzing, "应该包含ANALYZING状态");
        assertTrue(foundAnalysisCompleted, "应该包含ANALYSIS_COMPLETED状态");
        assertTrue(foundAnalysisFailed, "应该包含ANALYSIS_FAILED状态");
        assertTrue(foundAvailable, "应该包含AVAILABLE状态");
        assertTrue(foundUnavailable, "应该包含UNAVAILABLE状态");
    }

    @Test
    void testDatabaseConstraintCompatibility() {
        String[] expectedStatuses = {
            "PENDING_ANALYSIS", "ANALYZING", "ANALYSIS_COMPLETED", 
            "ANALYSIS_FAILED", "AVAILABLE", "UNAVAILABLE"
        };
        
        String[] actualStatuses = ProjectStatus.getAllStatusStrings();
        
        assertEquals(expectedStatuses.length, actualStatuses.length, "状态数量应该匹配数据库约束");
        
        for (String expectedStatus : expectedStatuses) {
            boolean found = false;
            for (String actualStatus : actualStatuses) {
                if (expectedStatus.equals(actualStatus)) {
                    found = true;
                    break;
                }
            }
            assertTrue(found, "状态 " + expectedStatus + " 应该存在于枚举中");
        }
    }
}

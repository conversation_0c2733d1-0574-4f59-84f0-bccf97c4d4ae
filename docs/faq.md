# ArchScope 常见问题解答 (FAQ)

## 🚀 产品概述

### Q: ArchScope 是什么？
A: ArchScope 是一个架构观测和守护系统，旨在通过分析代码仓库自动生成项目文档网站，并提供项目健康度评估等功能。MVP阶段主要支持Java项目的分析和文档生成。

### Q: ArchScope 的主要功能有哪些？
A: 主要功能包括：
- 匿名项目提交和分析
- 自动代码解析和AST分析
- 多类型文档自动生成（产品简介、架构设计、API文档等）
- 项目管理和可见性控制
- 任务状态跟踪
- 静态文档网站生成

### Q: 支持哪些编程语言？
A: MVP阶段主要支持Java项目。后续版本将逐步扩展支持JavaScript、Python、Go、C#等主流编程语言。

## 📝 使用指南

### Q: 如何提交项目进行分析？
A: 
1. 访问ArchScope首页
2. 输入Git仓库URL（支持GitHub、GitLab等）
3. 点击"提交分析"按钮
4. 系统会自动判断项目是否已存在，新项目将触发异步分析

### Q: 项目分析需要多长时间？
A: 分析时间取决于项目规模：
- 小型项目（<1万行代码）：5-10分钟
- 中型项目（1-10万行代码）：10-30分钟
- 大型项目（>10万行代码）：30分钟-2小时

### Q: 如何查看分析进度？
A: 提交项目后，系统会返回任务ID。您可以通过任务队列页面实时查看分析进度和日志信息。

### Q: 生成的文档包含哪些内容？
A: 自动生成的文档包括：
- **产品简介**: 项目概述、功能特性、技术栈
- **架构设计**: 系统架构图、模块关系、设计模式
- **API文档**: 接口定义、参数说明、示例代码
- **用户手册**: 安装部署、使用指南、配置说明
- **扩展能力**: 插件机制、二次开发指南
- **LLM原始内容**: AI分析的详细技术报告

## 🔐 权限管理

### Q: 谁可以查看我提交的项目？
A: 项目可见性分为两种：
- **PRIVATE**: 仅管理员可见和管理
- **PUBLIC**: 所有人都可以查看文档（需管理员设置）

### Q: 如何成为管理员？
A: 管理员通过企业SSO系统登录，具体权限由企业IT管理员分配。普通用户无法直接申请管理员权限。

### Q: 管理员可以进行哪些操作？
A: 管理员权限包括：
- 设置项目可见性（PRIVATE/PUBLIC）
- 设置项目状态（AVAILABLE/UNAVAILABLE）
- 查看所有项目（包括PRIVATE项目）
- 管理任务队列
- 系统配置和维护

## 🛠️ 技术问题

### Q: 支持私有Git仓库吗？
A: 目前MVP阶段主要支持公开的Git仓库。私有仓库支持需要配置相应的访问凭证，这个功能在后续版本中会提供。

### Q: 分析失败了怎么办？
A: 分析失败的常见原因：
1. **仓库访问失败**: 检查URL是否正确，仓库是否公开
2. **项目结构不支持**: 确保是标准的Java项目结构
3. **代码编译错误**: 项目需要能够正常编译
4. **网络问题**: 稍后重试或联系管理员

### Q: 可以重新分析已存在的项目吗？
A: 可以。如果项目代码有更新，管理员可以触发重新分析。系统会更新文档内容并保留历史版本。

### Q: 生成的文档可以自定义吗？
A: MVP阶段使用标准模板生成文档。后续版本将支持：
- 自定义文档模板
- 文档内容编辑
- 样式主题定制
- 自定义页面结构

## 🔧 部署运维

### Q: ArchScope 如何部署？
A: ArchScope 设计为在客户私有云Kubernetes环境中部署，包含：
- 后端Spring Boot应用
- 前端Vue.js应用
- MySQL数据库
- Redis缓存
- RocketMQ消息队列

### Q: 系统有哪些依赖？
A: 主要依赖包括：
- **数据库**: MySQL 8.0+
- **缓存**: Redis 6.0+
- **消息队列**: RocketMQ 4.9+
- **容器**: Docker + Kubernetes
- **外部服务**: Git仓库访问、LLM服务（OpenRouter）

### Q: 如何监控系统状态？
A: 系统提供多种监控方式：
- 健康检查端点
- 应用性能监控
- 任务执行状态监控
- 资源使用情况监控
- 错误日志收集

## 🔍 故障排除

### Q: 页面加载缓慢怎么办？
A: 可能的解决方案：
1. 检查网络连接
2. 清除浏览器缓存
3. 检查服务器负载
4. 联系系统管理员

### Q: 文档显示不完整怎么办？
A: 可能的原因：
1. 分析任务尚未完成
2. 项目代码结构特殊
3. 生成过程中出现错误
建议查看任务日志或重新触发分析

### Q: 如何报告Bug或提出建议？
A: 可以通过以下方式：
1. 联系项目管理员
2. 提交Issue到项目仓库
3. 发送邮件给开发团队
4. 参与用户反馈会议

## 📚 更多资源

### Q: 在哪里可以找到更多技术文档？
A: 详细技术文档请参考：
- [架构设计文档](architecture/overview.md)
- [API接口文档](api-design.md)
- [前端开发指南](frontend-development.md)
- [技术问答文档](technical-qa.md)

### Q: 如何参与项目开发？
A: 欢迎参与ArchScope项目开发：
1. 阅读[前端开发指南](frontend-development.md)
2. 了解[编码规范](field-naming-standards.md)
3. 查看[架构决策记录](adrs/)
4. 提交Pull Request

### Q: 项目的发展规划是什么？
A: 项目发展分为四个阶段：
- **Phase 1 (MVP)**: Java项目支持，基础文档生成
- **Phase 2**: 多语言支持，工作流自动化
- **Phase 3**: 健康度评估，用户体验提升
- **Phase 4**: 企业级功能，规模化部署

---

**最后更新**: 2025-08-04  
**如有其他问题，请联系**: ArchScope开发团队

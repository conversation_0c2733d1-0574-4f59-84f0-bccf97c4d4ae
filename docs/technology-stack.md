# ArchScope 技术栈

## 📋 技术栈概览

ArchScope 采用现代化的技术栈，基于微服务架构设计，支持云原生部署。

### 核心技术选型

| 技术领域 | 技术选型 | 版本要求 | 说明 |
|---------|---------|---------|------|
| **后端框架** | Spring Boot | 3.x | 主要应用框架 |
| **前端框架** | Vue.js | 3.x | 用户界面框架 |
| **编程语言** | Java | 17+ | 后端开发语言 |
| **前端语言** | TypeScript | 5.x | 前端开发语言 |
| **数据库** | MySQL | 8.0+ | 主数据库 |
| **缓存** | Redis | 6.0+ | 缓存和会话存储 |
| **消息队列** | RocketMQ | 4.9+ | 异步消息处理 |
| **容器化** | Docker | 20.x+ | 应用容器化 |
| **编排** | Kubernetes | 1.20+ | 容器编排 |

## 🏗️ 后端技术栈

### 核心框架
- **Spring Boot 3.x**: 应用主框架
- **Spring Security**: 安全认证框架
- **Spring Data JPA**: 数据访问层
- **MyBatis-Plus**: ORM增强框架
- **Spring Cloud**: 微服务支持（未来扩展）

### 数据存储
- **MySQL 8.0+**: 关系型数据库
  - 存储项目信息、任务状态、用户数据
  - 支持事务和复杂查询
- **Redis 6.0+**: 内存数据库
  - 缓存热点数据
  - 会话存储
  - 分布式锁

### 消息处理
- **RocketMQ 4.9+**: 消息队列
  - 异步任务处理
  - 事件驱动架构
  - 可靠消息传递

### 代码分析
- **JavaParser**: Java代码AST解析
- **Antlr4**: 通用语法解析器
- **JGit**: Git仓库操作
- **Maven/Gradle**: 项目构建工具集成

### 外部服务集成
- **OpenRouter**: LLM服务集成
- **GitHub API**: Git仓库元数据获取
- **GitLab API**: GitLab仓库支持

## 🎨 前端技术栈

### 核心框架
- **Vue.js 3.x**: 渐进式JavaScript框架
  - Composition API
  - 响应式系统
  - 组件化开发
- **TypeScript 5.x**: 静态类型检查
- **Vite**: 构建工具和开发服务器

### UI框架和样式
- **Tailwind CSS**: 原子化CSS框架
- **FontAwesome 6**: 图标库
- **Element Plus**: Vue 3组件库（可选）
- **Ant Design Vue**: 企业级UI组件库（可选）

### 状态管理和路由
- **Pinia**: 状态管理库
- **Vue Router 4**: 前端路由
- **VueUse**: Vue组合式工具库

### 开发工具
- **ESLint**: 代码质量检查
- **Prettier**: 代码格式化
- **Husky**: Git钩子管理
- **Commitizen**: 提交信息规范

## 🗄️ 数据库设计

### 主数据库 (MySQL)
```sql
-- 核心业务表
projects          -- 项目信息
tasks            -- 任务管理
documents        -- 文档内容
services         -- 服务发现
capabilities     -- 服务能力
requirements     -- 需求管理
```

### 缓存设计 (Redis)
```text
-- 缓存策略
project:{id}           -- 项目详情缓存
task:{id}             -- 任务状态缓存
document:{project_id}  -- 文档内容缓存
session:{user_id}     -- 用户会话
```

## 🔧 开发工具链

### 代码管理
- **Git**: 版本控制
- **GitHub/GitLab**: 代码托管
- **Git Flow**: 分支管理策略

### 构建和部署
- **Maven**: Java项目构建
- **npm/yarn**: 前端包管理
- **Docker**: 容器化
- **Kubernetes**: 容器编排
- **Helm**: Kubernetes包管理

### 监控和日志
- **Prometheus**: 指标监控
- **Grafana**: 监控可视化
- **ELK Stack**: 日志收集和分析
- **Jaeger**: 分布式链路追踪

### 测试框架
- **JUnit 5**: Java单元测试
- **Mockito**: Mock框架
- **TestContainers**: 集成测试
- **Jest**: JavaScript测试框架
- **Cypress**: E2E测试

## 🚀 部署架构

### 容器化部署
```yaml
# Docker Compose 示例
services:
  archscope-backend:
    image: archscope/backend:latest
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
      - rocketmq

  archscope-frontend:
    image: archscope/frontend:latest
    ports:
      - "80:80"

  mysql:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: archscope
      MYSQL_ROOT_PASSWORD: password

  redis:
    image: redis:6.2-alpine

  rocketmq:
    image: apache/rocketmq:4.9.4
```

### Kubernetes部署
- **Deployment**: 应用部署
- **Service**: 服务发现
- **Ingress**: 外部访问
- **ConfigMap**: 配置管理
- **Secret**: 敏感信息管理
- **PVC**: 持久化存储

## 🔐 安全技术

### 认证授权
- **Spring Security**: 安全框架
- **JWT**: 无状态认证
- **OAuth 2.0**: 第三方认证
- **RBAC**: 基于角色的访问控制

### 数据安全
- **HTTPS**: 传输加密
- **数据库加密**: 敏感数据加密
- **API限流**: 防止滥用
- **输入验证**: 防止注入攻击

## 📊 性能优化

### 后端优化
- **连接池**: 数据库连接优化
- **缓存策略**: 多级缓存
- **异步处理**: 非阻塞IO
- **批量操作**: 减少数据库访问

### 前端优化
- **代码分割**: 按需加载
- **资源压缩**: 减少传输大小
- **CDN**: 静态资源加速
- **懒加载**: 图片和组件懒加载

## 🔄 CI/CD流程

### 持续集成
1. **代码提交**: Git推送触发
2. **自动构建**: Maven/npm构建
3. **单元测试**: 自动化测试执行
4. **代码质量**: SonarQube分析
5. **安全扫描**: 依赖漏洞检查

### 持续部署
1. **镜像构建**: Docker镜像打包
2. **镜像推送**: 推送到镜像仓库
3. **环境部署**: Kubernetes部署
4. **健康检查**: 服务可用性验证
5. **回滚机制**: 部署失败自动回滚

## 📈 扩展性考虑

### 水平扩展
- **无状态设计**: 应用层无状态
- **负载均衡**: 多实例部署
- **数据库分片**: 数据水平分割
- **缓存集群**: Redis集群模式

### 垂直扩展
- **资源配置**: CPU/内存调优
- **JVM调优**: 垃圾回收优化
- **数据库优化**: 索引和查询优化
- **网络优化**: 带宽和延迟优化

---

**相关文档**:
- [架构设计文档](architecture/overview.md)
- [部署指南](deployment-guide.md)
- [开发环境搭建](frontend-development.md)

# ArchScope 数据模型详细设计

## 1. 引言
本文档详细定义了ArchScope系统的数据模型，包括关系型数据库(MySQL)的表结构、图数据库(Neo4j)的节点与关系模型，以及缓存(Redis)的使用策略。它是`architecture.md`中数据模型设计的进一步细化，并严格参考了用户提供的技术规范附录（包含数据字典）。

## 2. MySQL关系模型 (DDL)

基于用户提供的数据字典，以下是核心表的DDL语句草稿：

### 2.1 `projects` 表

```sql
CREATE TABLE projects (
  id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '项目唯一标识符 (自增主键)',
  name VARCHAR(255) NOT NULL COMMENT '用户定义的项目名称',
  description TEXT COMMENT '项目的详细描述',
  repository_url VARCHAR(512) NOT NULL COMMENT '原始Git仓库URL',
  normalized_repository_url VARCHAR(512) NOT NULL COMMENT '标准化后的Git仓库URL (唯一键)',
  branch VARCHAR(255) DEFAULT 'main' COMMENT '默认分支名称',
  type VARCHAR(50) DEFAULT 'JAVA' COMMENT '项目类型: JAVA, JAVASCRIPT, PYTHON等',
  status VARCHAR(50) DEFAULT 'PENDING' COMMENT '项目状态: PENDING, ANALYZING, COMPLETED, FAILED',
  active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
  creator_id BIGINT COMMENT '创建者ID',
  created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  last_analyzed_at DATETIME COMMENT '最后分析时间',
  latest_analyzed_commit_id VARCHAR(40) COMMENT '最新分析的提交ID',
  rating DECIMAL(3,2) DEFAULT 0.00 COMMENT '项目评分',
  lines_of_code BIGINT DEFAULT 0 COMMENT '代码行数',
  file_count INT DEFAULT 0 COMMENT '文件数量',
  contributor_count INT DEFAULT 0 COMMENT '贡献者数量',
  icon VARCHAR(255) COMMENT '项目图标',
  UNIQUE INDEX uidx_projects_normalized_repo_url (normalized_repository_url),
  INDEX idx_projects_status (status),
  INDEX idx_projects_created_at (created_at),
  CONSTRAINT chk_projects_repo_type CHECK (repo_type IN ('GITHUB', 'GITLAB')), -- 根据您的数据字典
  CONSTRAINT chk_projects_language CHECK (language IN ('JAVA')), -- MVP Constraint
  CONSTRAINT chk_projects_status CHECK (status IN ('PENDING_ANALYSIS', 'ANALYSIS_IN_PROGRESS', 'ANALYSIS_FAILED', 'DOC_GEN_IN_PROGRESS', 'DOC_GEN_FAILED', 'UNAVAILABLE', 'AVAILABLE')),
  CONSTRAINT chk_projects_visibility CHECK (visibility IN ('PENDING_REVIEW', 'INTERNAL', 'PUBLIC'))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='项目信息表';
````

### 2.2 `tasks` 表

```sql
CREATE TABLE tasks (
   task_id VARCHAR(36) NOT NULL COMMENT '任务唯一标识符 (UUID)',
   project_id VARCHAR(36) COMMENT '关联的项目ID',
   task_type VARCHAR(32) NOT NULL COMMENT '任务类型: CODE_FULL_ANALYSIS_JAVA, DOC_SITE_GENERATION_JAVA',
   status VARCHAR(16) NOT NULL DEFAULT 'QUEUED' COMMENT '任务状态: QUEUED, RUNNING, SUCCESS, FAILED',
   payload TEXT COMMENT 'JSON格式的任务输入参数',
   created_at DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '任务记录创建时间',
   queued_at DATETIME(3) COMMENT '任务实际进入消息队列时间',
   started_at DATETIME(3) COMMENT '任务开始执行时间',
   finished_at DATETIME(3) COMMENT '任务执行完成时间 (成功或失败)',
   retry_count INT NOT NULL DEFAULT 0 COMMENT '当前重试次数',
   last_error_message TEXT COMMENT '最后一次执行失败的错误消息摘要',
   result_summary TEXT COMMENT 'JSON格式的任务执行结果摘要或指针',
   triggered_by_type VARCHAR(32) NOT NULL COMMENT '任务触发类型: WEB_SUBMISSION, ADMIN_SSO_TRIGGER, CHAINED_TASK_TRIGGER',
   triggered_by_id VARCHAR(255) COMMENT '触发源的标识符 (如Admin SSO User ID, 前一个任务的taskId)',
   PRIMARY KEY (task_id),
   INDEX idx_tasks_project_id (project_id),
   INDEX idx_tasks_status (status),
   INDEX idx_tasks_task_type (task_type),
   INDEX idx_tasks_created_at (created_at),
   CONSTRAINT fk_tasks_project_id FOREIGN KEY (project_id) REFERENCES projects(project_id) ON DELETE SET NULL,
   CONSTRAINT chk_tasks_task_type CHECK (task_type IN ('CODE_FULL_ANALYSIS_JAVA', 'DOC_SITE_GENERATION_JAVA')), -- 根据您的数据字典
   CONSTRAINT chk_tasks_status CHECK (status IN ('QUEUED', 'RUNNING', 'SUCCESS', 'FAILED')), -- 根据您的数据字典
   CONSTRAINT chk_tasks_triggered_by_type CHECK (triggered_by_type IN ('WEB_SUBMISSION', 'ADMIN_SSO_TRIGGER', 'SYSTEM_SCHEDULED_TRIGGER', 'CHAINED_TASK_TRIGGER')) -- 根据您的数据字典
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='后台异步任务表';
```

**备注:**

* `ENUM` 类型通过 `CHECK` 约束实现，以保证跨数据库的兼容性（如果需要）。对于MySQL 8.0.16+，也可以直接使用 `ENUM` 类型。
* 时间戳字段使用 `DATETIME(3)` 以支持毫秒精度。
* 明确了外键约束和必要的索引。
* Post-MVP字段（如`healthRating`, `task.priority`）暂时注释掉或移除。

## 3. Neo4j 图模型 (节点与关系属性细化)

基于您在数据字典中提供的信息，对Neo4j图模型进行细化：

* **节点 `:JavaProject`:**

    * `projectId`: String (PK, from MySQL)
    * `name`: String
    * `repoUrlNormalized`: String

* **节点 `:GitCommit`:**

    * `commitId`: String (PK, Git SHA)
    * `timestamp`: Datetime (Neo4j Datetime type)
    * `message`: String

* **节点 `:JavaFile`:**

    * `filePath`: String (PK within commit, e.g., `src/main/java/com/example/MyClass.java`)
    * `fileName`: String
    * `loc`: Integer

* **节点 `:JavaPackage`:**

    * `fqn`: String (PK, e.g., `com.example.service`)
    * `name`: String

* **节点 `:JavaClass` (用于类和接口):**

    * `fqn`: String (PK, e.g., `com.example.service.MyService`)
    * `name`: String
    * `visibility`: String (Enum: `PUBLIC`, `PROTECTED`, `DEFAULT`, `PRIVATE`)
    * `isInterface`: Boolean
    * `isAbstract`: Boolean
    * `isFinal`: Boolean
    * `comment`: String (Nullable)
    * `loc`: Integer

* **节点 `:JavaMethod`:**

    * `signature`: String (PK within class, e.g., `myMethod(java.lang.String,int)`)
    * `name`: String
    * `visibility`: String (Enum: `PUBLIC`, `PROTECTED`, `DEFAULT`, `PRIVATE`)
    * `returnType`: String (FQ Type or primitive)
    * `parameters`: List\<String\> (List of FQ Types or primitives, e.g., `["java.lang.String", "int"]`)
    * `isStatic`: Boolean
    * `isAbstract`: Boolean
    * `isFinal`: Boolean
    * `isSynchronized`: Boolean
    * `cyclomaticComplexity`: Integer (Nullable)
    * `comment`: String (Nullable)
    * `loc`: Integer

* **节点 `:JavaField`:**

    * `name`: String (PK within class)
    * `type`: String (FQ Type or primitive)
    * `visibility`: String (Enum: `PUBLIC`, `PROTECTED`, `DEFAULT`, `PRIVATE`)
    * `isStatic`: Boolean
    * `isFinal`: Boolean
    * `comment`: String (Nullable)

* **关系 (Relationships - 示例):**

    * `(:JavaProject)-[:ANALYZED_FOR_COMMIT {analyzedAt:datetime}]->(:GitCommit)`
    * `(:GitCommit)-[:CONTAINS_FILE]->(:JavaFile)`
    * `(:JavaFile)-[:DEFINES_TYPE]->(:JavaClass | :JavaInterface)`
    * `(:JavaClass)-[:PART_OF_PACKAGE]->(:JavaPackage)` (或 `:JavaInterface`)
    * `(:JavaPackage)-[:HAS_PARENT_PACKAGE]->(:JavaPackage)`
    * `(:JavaClass)-[:DECLARES_METHOD]->(:JavaMethod)`
    * `(:JavaClass)-[:DECLARES_FIELD]->(:JavaField)`
    * `(:JavaInterface)-[:DECLARES_METHOD]->(:JavaMethod)`
    * `(:JavaClass)-[:EXTENDS_CLASS]->(:JavaClass)`
    * `(:JavaClass)-[:IMPLEMENTS_INTERFACE]->(:JavaInterface)`
    * `(:JavaInterface)-[:EXTENDS_INTERFACE]->(:JavaInterface)`
    * `(:JavaMethod)-[:CALLS_METHOD {lineNumber:integer, callType:string}]->(:JavaMethod)` (MVP简化为类级调用或方法签名引用)
    * `(:JavaMethod)-[:ACCESSES_FIELD {accessType:string, lineNumber:integer}]->(:JavaField)`
    * *(其他关系如类型引用等)*

**索引策略 (Neo4j):**

* `:JavaProject(projectId)`
* `:GitCommit(commitId)`
* `:JavaFile(filePath)` (考虑与commitId的复合唯一性)
* `:JavaPackage(fqn)`
* `:JavaClass(fqn)`
* `:JavaInterface(fqn)`
* `:JavaMethod(signature)` (考虑与声明类型的fqn复合唯一性)
* `:JavaField(name)` (考虑与声明类型的fqn复合唯一性)

## 4. Redis 缓存策略 (MVP)

* **缓存键命名规范:** `archscope:{objectType}:{objectId}:{version/qualifier}`
    * 例: `archscope:project_summary_list:public:p0s10sortNameAsc`
    * 例: `archscope:project_detail:a1b2c3d4-e5f6`
    * 例: `archscope:doc_tree:a1b2c3d4-e5f6:abcdef12`
    * 例: `archscope:doc_content:a1b2c3d4-e5f6:abcdef12:/path/to/file.md`
* **缓存内容与TTL (MVP):**
    1.  **公开项目摘要列表 (第一页):**
        * Key: `archscope:project_list:public:default_sort:page_0`
        * Value: `List<ProjectSummaryDTO>` (序列化JSON)
        * TTL: 5 分钟
        * 失效: 定时刷新，或在有项目`visibility`/`status`变更为`PUBLIC`/`AVAILABLE`时（通过事件或管理员操作后）尝试失效。
    2.  **公开文档树结构:**
        * Key: `archscope:doc_tree:{projectId}:{latestPublicCommitId}`
        * Value: `List<DocTreeNodeDTO>` (序列化JSON)
        * TTL: 30 分钟 - 1 小时
        * 失效: 对应项目和commit的文档重新生成成功后。
    3.  **高频访问的公开Markdown文档内容:**
        * Key: `archscope:doc_content:{projectId}:{latestPublicCommitId}:{filePathHash}` (filePath过长可哈希)
        * Value: `MarkdownDocumentDTO.markdownContent` (String)
        * TTL: 1 小时 - 6 小时
        * 失效: 对应项目和commit的文档重新生成成功后。
* **缓存更新/失效策略 (MVP):**
    * 主要依赖TTL。
    * 对于管理员操作（如修改项目`visibility`或`status`为`PUBLIC`/`AVAILABLE`），在操作成功后，应主动尝试删除/更新相关的缓存条目（如公开项目列表缓存）。
    * 文档重新生成任务成功后，应主动删除/更新该项目该commit相关的文档树和文档内容缓存。
* **避免缓存穿透 (MVP简化):** 对于查询结果为空的情况，可以缓存一个特殊的空值标记（带有较短TTL），防止短时间内大量无效查询直接打到数据库。
* **数据序列化:** 使用JSON进行序列化存储 (如Jackson)。

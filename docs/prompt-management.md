# ArchScope 提示词管理系统

## 📋 概述

本项目采用基于文件的提示词管理系统，所有提示词都存储在`prompts/`目录下的YAML文件中，便于随时修改和管理。

## 🗂️ 提示词目录结构

```text
prompts/
├── repository/        # 仓库级别的提示词
│   ├── analysis.yaml  # 仓库分析提示词
│   └── metadata.yaml  # 元数据提取提示词
├── code/              # 代码分析提示词
│   ├── java.yaml      # Java代码分析
│   ├── javascript.yaml # JavaScript代码分析
│   └── common.yaml    # 通用代码分析
├── documentation/     # 文档生成提示词
│   ├── api.yaml       # API文档生成
│   ├── architecture.yaml # 架构文档生成
│   ├── user-manual.yaml # 用户手册生成
│   └── readme.yaml    # README生成
├── integration/       # 集成分析提示词
│   ├── dependency.yaml # 依赖关系分析
│   └── service.yaml   # 服务集成分析
└── version/           # 版本比较提示词
    ├── diff.yaml      # 版本差异分析
    └── changelog.yaml # 变更日志生成
```

## 📝 提示词文件格式

每个YAML文件包含元数据和多个提示词模板：

```yaml
metadata:
  name: "提示词名称"
  description: "提示词描述"
  version: "1.0.0"
  last_updated: "2025-08-04"
  author: "ArchScope团队"
  tags: ["java", "analysis", "code"]

prompts:
  analyze_class_structure:
    description: "分析Java类结构"
    template: |
      请分析以下Java类的结构，包括：
      1. 类的职责和功能
      2. 主要方法和属性
      3. 设计模式的使用
      4. 依赖关系
      
      代码内容：
      ```java
      {code_content}
      ```
      
      请以JSON格式返回分析结果。
    
    parameters:
      - name: "code_content"
        type: "string"
        required: true
        description: "Java类的源代码"
    
    output_format: "json"
    max_tokens: 2000
    temperature: 0.3

  generate_api_doc:
    description: "生成API文档"
    template: |
      基于以下Java接口代码，生成详细的API文档：
      
      接口代码：
      ```java
      {interface_code}
      ```
      
      请包含以下内容：
      1. 接口概述
      2. 方法说明
      3. 参数描述
      4. 返回值说明
      5. 使用示例
      
      输出格式：Markdown
    
    parameters:
      - name: "interface_code"
        type: "string"
        required: true
        description: "Java接口源代码"
    
    output_format: "markdown"
    max_tokens: 3000
    temperature: 0.2
```

## 🔧 提示词管理功能

### 1. 提示词加载
```java
@Service
public class PromptService {
    
    public PromptTemplate loadPrompt(String category, String promptKey) {
        // 从YAML文件加载提示词模板
        String filePath = String.format("prompts/%s.yaml", category);
        return yamlLoader.loadPrompt(filePath, promptKey);
    }
    
    public List<PromptTemplate> loadAllPrompts(String category) {
        // 加载指定分类的所有提示词
        return yamlLoader.loadAllPrompts(category);
    }
}
```

### 2. 提示词渲染
```java
@Service
public class PromptRenderer {
    
    public String renderPrompt(PromptTemplate template, Map<String, Object> parameters) {
        // 使用模板引擎渲染提示词
        return templateEngine.process(template.getTemplate(), parameters);
    }
    
    public String renderWithValidation(PromptTemplate template, Map<String, Object> parameters) {
        // 参数验证后渲染
        validateParameters(template, parameters);
        return renderPrompt(template, parameters);
    }
}
```

### 3. 提示词缓存
```java
@Service
@Cacheable("prompts")
public class PromptCacheService {
    
    @Cacheable(value = "prompt-templates", key = "#category + '_' + #promptKey")
    public PromptTemplate getCachedPrompt(String category, String promptKey) {
        return promptService.loadPrompt(category, promptKey);
    }
    
    @CacheEvict(value = "prompt-templates", allEntries = true)
    public void clearPromptCache() {
        // 清除所有提示词缓存
    }
}
```

## 📊 提示词分类说明

### 1. 仓库级别提示词 (repository/)
- **analysis.yaml**: 整体仓库结构分析
- **metadata.yaml**: 项目元数据提取
- **summary.yaml**: 项目概要生成

### 2. 代码分析提示词 (code/)
- **java.yaml**: Java代码专用分析
- **javascript.yaml**: JavaScript代码分析
- **python.yaml**: Python代码分析
- **common.yaml**: 通用代码分析

### 3. 文档生成提示词 (documentation/)
- **api.yaml**: API接口文档生成
- **architecture.yaml**: 架构设计文档
- **user-manual.yaml**: 用户使用手册
- **readme.yaml**: README文档生成
- **extension.yaml**: 扩展开发指南

### 4. 集成分析提示词 (integration/)
- **dependency.yaml**: 依赖关系分析
- **service.yaml**: 服务集成分析
- **database.yaml**: 数据库集成分析

### 5. 版本比较提示词 (version/)
- **diff.yaml**: 代码差异分析
- **changelog.yaml**: 变更日志生成
- **migration.yaml**: 迁移指南生成

## 🎯 提示词设计原则

### 1. 清晰性
- 明确的指令和期望
- 具体的输出格式要求
- 详细的示例说明

### 2. 一致性
- 统一的模板格式
- 标准化的参数命名
- 一致的输出结构

### 3. 可维护性
- 模块化的提示词设计
- 版本控制和变更记录
- 详细的文档说明

### 4. 可扩展性
- 支持新语言的提示词
- 可配置的参数系统
- 灵活的模板引擎

## 🔄 提示词版本管理

### 版本控制策略
- 使用语义化版本号 (Semantic Versioning)
- 重大变更需要版本升级
- 保持向后兼容性

### 变更管理
```yaml
# 版本变更记录示例
changelog:
  - version: "1.2.0"
    date: "2025-08-04"
    changes:
      - "新增Python代码分析提示词"
      - "优化API文档生成模板"
      - "修复Java类分析的参数验证"
  
  - version: "1.1.0"
    date: "2025-07-15"
    changes:
      - "增强架构文档生成功能"
      - "添加数据库集成分析提示词"
```

## 🧪 提示词测试

### 单元测试
```java
@Test
public void testPromptRendering() {
    PromptTemplate template = promptService.loadPrompt("code", "analyze_class_structure");
    Map<String, Object> params = Map.of("code_content", sampleJavaCode);
    
    String rendered = promptRenderer.renderPrompt(template, params);
    
    assertThat(rendered).contains("请分析以下Java类的结构");
    assertThat(rendered).contains(sampleJavaCode);
}
```

### 集成测试
```java
@Test
public void testLLMIntegration() {
    String prompt = promptRenderer.renderWithValidation(template, parameters);
    String response = llmService.generateResponse(prompt);
    
    assertThat(response).isNotEmpty();
    assertThat(JsonUtils.isValidJson(response)).isTrue();
}
```

## 📈 性能优化

### 缓存策略
- 提示词模板缓存
- 渲染结果缓存
- 参数验证缓存

### 加载优化
- 懒加载机制
- 批量加载支持
- 异步加载处理

## 🔧 配置管理

### 应用配置
```yaml
# application.yml
archscope:
  prompts:
    base-path: "prompts"
    cache-enabled: true
    cache-ttl: 3600
    template-engine: "thymeleaf"
    validation-enabled: true
```

### 环境配置
- 开发环境：启用详细日志
- 测试环境：使用测试提示词
- 生产环境：启用缓存和优化

---

**相关文档**:
- [LLM集成指南](llm-task-integration-guide.md)
- [开发指南](frontend-development.md)
- [API设计规范](api-design.md)

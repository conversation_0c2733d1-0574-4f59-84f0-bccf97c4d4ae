# 开发指南

本目录包含ArchScope项目的开发相关文档和指南。

## 📋 文档列表

### 开发指南
- [前端开发指南](../frontend-development.md) - 前端开发最佳实践
- [Git集成配置](../git-personal-access-token-config.md) - Git个人访问令牌配置
- [LLM任务集成指南](../llm-task-integration-guide.md) - LLM任务集成开发指南
- [提示词管理](../prompt-management.md) - 提示词管理系统

### 测试指南
- [测试策略](testing-strategy.md) - 项目测试策略和最佳实践（如果存在）

## 🎯 使用指南

### 新手开发者
1. 阅读 [前端开发指南](../frontend-development.md) 了解前端开发规范
2. 配置 [Git集成](../git-personal-access-token-config.md) 进行代码管理
3. 学习 [LLM任务集成](../llm-task-integration-guide.md) 进行AI功能开发

### 高级开发者
- 参考 [提示词管理](../prompt-management.md) 优化AI交互体验

## 📝 维护说明

- 开发指南应与实际开发流程保持同步
- 新的开发实践需要及时更新到文档中
- 重要变更需要在CHANGELOG中记录
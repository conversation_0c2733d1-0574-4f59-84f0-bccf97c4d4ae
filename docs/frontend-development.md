# ArchScope 前端开发指南

## 📋 开发环境搭建

### 系统要求
- Node.js 18.x 或更高版本
- npm 9.x 或 yarn 1.22.x
- Git 2.x

### 环境配置
```bash
# 克隆项目
git clone https://github.com/your-org/arch-scope.git
cd arch-scope/arch-scope-frontend

# 安装依赖
npm install
# 或使用 yarn
yarn install

# 启动开发服务器
npm run dev
# 或使用 yarn
yarn dev
```

## 🏗️ 项目结构

```text
arch-scope-frontend/
├── public/                 # 静态资源
│   ├── favicon.ico
│   └── index.html
├── src/
│   ├── api/               # API接口封装
│   ├── assets/            # 静态资源
│   ├── components/        # 可复用组件
│   ├── layouts/           # 页面布局
│   ├── router/            # 路由配置
│   ├── stores/            # Pinia状态管理
│   ├── types/             # TypeScript类型定义
│   ├── utils/             # 工具函数
│   ├── views/             # 页面组件
│   ├── App.vue            # 根组件
│   └── main.ts            # 应用入口
├── package.json
├── vite.config.ts         # Vite配置
├── tailwind.config.js     # Tailwind配置
└── tsconfig.json          # TypeScript配置
```

## 🎨 开发规范

### 组件开发
- 使用Vue 3 Composition API
- 遵循单一职责原则
- 组件名使用PascalCase
- 文件名使用kebab-case

### 样式规范
- 优先使用Tailwind CSS原子类
- 避免内联样式
- 组件样式使用scoped

### TypeScript规范
- 严格类型检查
- 定义清晰的接口
- 使用类型断言时要谨慎

## 🔧 开发工具

### 推荐IDE配置
- VS Code + Volar插件
- ESLint + Prettier
- TypeScript支持

### 调试工具
- Vue DevTools
- 浏览器开发者工具
- Network面板监控API调用

## 📦 构建部署

### 开发构建
```bash
npm run build:dev
```

### 生产构建
```bash
npm run build
```

### 预览构建结果
```bash
npm run preview
```

## 🧪 测试

### 单元测试
```bash
npm run test:unit
```

### E2E测试
```bash
npm run test:e2e
```

---

**相关文档**:
- [前端架构设计](frontend-architecture.md)
- [API设计规范](api-design.md)
- [界面原型](prototype/README.md)

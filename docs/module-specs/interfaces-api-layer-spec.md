# ArchScope 模块内部设计规范: Interfaces API Layer (`archscope-facade`)

## 1. 引言
本模块负责暴露系统的RESTful API接口，处理HTTP请求和响应，进行DTO转换和输入验证。它依赖`archscope-app`模块来执行业务逻辑。对于需要管理员权限的API，它将依赖外部URL过滤器注入的权限上下文进行授权判断（通过Spring Security注解或编程式检查）。

## 2. REST Controllers

## 2.1 `PublicProjectController` (`com.archscope.interfaces.api.project.PublicProjectController`)
* **路径前缀:** `/projects`
* **方法 (对应`api-spec.yaml`中的`operationId`):**
    * `@PostMapping`: `submitNewProject(@Valid @RequestBody ProjectCreateRequestDTO dto)`
        * 调用 `projectAppService.handleSubmitNewProject(commandMapper.toCommand(dto))`.
        * 根据返回的 `ProjectOperationResponseDTO` 构建 `ResponseEntity<ApiResponse<ProjectOperationResponseDTO>>` (HTTP 200 or 202).
    * `@GetMapping("/{projectId}")`: `getPublicProjectDetails(@PathVariable String projectId)`
        * 调用 `projectAppService.getPublicProjectDetails(projectId)`.
        * 返回 `ResponseEntity<ApiResponse<ProjectDetailDTO>>`.
    * `@GetMapping`: `listPubliclyAvailableProjects(@Valid PageParams pageParams, @RequestParam(required=false) String search)`
        * 调用 `projectAppService.listPublicProjects(queryMapper.toQuery(pageParams, search))`.
        * 返回 `ResponseEntity<ApiResponse<PagedResult<ProjectSummaryDTO>>>`.

## 2.2 `PublicDocumentationController` (`com.archscope.interfaces.api.doc.PublicDocumentationController`)
* **路径前缀:** `/projects/{projectId}/documentation/latest`
* **方法:**
    * `@GetMapping("/tree")`: `getPublicLatestDocumentationTree(@PathVariable String projectId)`
        * 调用 `documentationAppService.getPublicDocumentTree(projectId)`.
        * 返回 `ResponseEntity<ApiResponse<List<DocTreeNodeDTO>>>`.
    * `@GetMapping("/content")`: `getPublicLatestDocumentContent(@PathVariable String projectId, @RequestParam String filePath)`
        * 调用 `documentationAppService.getPublicDocumentContent(projectId, filePath)`.
        * 返回 `ResponseEntity<ApiResponse<MarkdownDocumentDTO>>`.

## 2.3 `PublicTaskController` (`com.archscope.interfaces.api.task.PublicTaskController`)
* **路径前缀:** `/tasks`
* **方法:**
    * `@GetMapping`: `listAllTasksPublicView(@Valid PageParams pageParams, PublicTaskQueryDTO filters)`
        * 调用 `taskAppService.listTasksPublic(queryMapper.toQuery(pageParams, filters))`.
        * 返回 `ResponseEntity<ApiResponse<PagedResult<TaskSummaryDTO>>>`.
    * `@GetMapping("/{taskId}")`: `getTaskByIdPublicView(@PathVariable String taskId)`
        * 调用 `taskAppService.getTaskDetailsPublic(taskId)`.
        * 返回 `ResponseEntity<ApiResponse<TaskDetailDTO>>`.

## 2.4 `SystemInfoController` (`com.archscope.interfaces.api.system.SystemInfoController`)
* **路径前缀:** `/system`
* **方法:**
    * `@GetMapping("/error-codes")`: `getSystemErrorCodes()`
        * 调用 `systemInfoAppService.getAllErrorCodes()`.
        * 返回 `ResponseEntity<ApiResponse<List<ErrorCodeDTO>>>`.

## 2.5 `AdminProjectManagementController` (`com.archscope.interfaces.api.project.AdminProjectManagementController`)
* **路径前缀:** `/projects-management` (或 `/projects` 配合HTTP方法和外部过滤器区分)
* **安全:** 所有方法需要`ssoAdminAuth`，并依赖外部拦截器提供的`ARCHSCOPE_ADMIN`角色。
* **方法 (示例):**
    * `@GetMapping`: `@PreAuthorize("hasRole('ARCHSCOPE_ADMIN')") listAllProjectsAdmin(@Valid PageParams pageParams, AdminProjectQueryDTO filters)`
        * 获取`AuthenticatedAdminContext` (通过`SecurityContextHolder`或方法参数注入)。
        * 调用`adminProjectAppService.listAllProjects(query, adminContext)`.
    * `@PutMapping("/{projectId}")`: `@PreAuthorize("hasRole('ARCHSCOPE_ADMIN')") updateProjectAdmin(@PathVariable String projectId, @Valid @RequestBody AdminProjectUpdateRequestDTO dto)`
        * 调用`adminProjectAppService.updateProjectByAdmin(command, adminContext)`.

## 3. DTOs (Data Transfer Objects)
* 定义在 `com.archscope.interfaces.api.dto.<feature>` 包下。
* 严格与`api-spec.yaml`中的`schemas`对应。
* 使用JSR 303/380 Bean Validation注解进行输入验证。
* 考虑使用MapStruct或类似库进行DTO与领域对象之间的映射，映射逻辑可以放在`facade`层或`app`层的边界。

## 4. 全局异常处理 (`com.archscope.interfaces.api.exception.GlobalApiExceptionHandler`)
* 使用`@ControllerAdvice`。
* 捕获来自应用层或领域层的特定业务异常 (如`ProjectNotFoundException`, `TaskUpdateForbiddenException`) 并映射到`api-spec.yaml`定义的`ErrorResponse`和HTTP状态码。
* 捕获`MethodArgumentNotValidException` (Bean Validation失败) 返回HTTP 400及详细字段错误。
* 捕获通用`AccessDeniedException` (来自Spring Security的方法级授权) 返回HTTP 403。
* 捕获其他未处理异常返回HTTP 500，记录详细日志。

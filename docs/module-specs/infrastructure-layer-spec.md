# ArchScope 模块内部设计规范: Infrastructure Layer (`archscope-infrastructure`)

### 1. 引言
本模块负责所有与外部系统和技术细节相关的实现，包括数据持久化、消息传递、外部服务客户端、以及领域层定义的端口（接口）的具体适配。它依赖`archscope-domain`的接口。

### 2. 持久化适配器 (Persistence Adapters)

#### 2.1 MySQL Adapters (`com.archscope.infrastructure.persistence.mysql`)
* **`MySqlProjectRepositoryImpl implements ProjectRepository`:**
    * 使用Spring Data JPA: 定义`ProjectJpaEntity` (POJO类，带JPA注解，字段与`projects`表严格对应) 和 `SpringDataProjectJpaRepository extends JpaRepository<ProjectJpaEntity, String>`。
    * `MySqlProjectRepositoryImpl`注入`SpringDataProjectJpaRepository`和`ProjectDomainMapper` (用于JPA实体与领域实体`Project`的转换)。
    * 实现`findById`, `findByNormalizedRepoUrl`, `save`, `findAllByVisibilityAndStatus`, `findAllForAdmin`等方法，内部调用JPA Repository的方法，并进行领域对象转换。
    * 例如，`save(Project project)`: `ProjectJpaEntity entity = mapper.toJpaEntity(project); jpaRepo.save(entity);`
* **`MySqlTaskRepositoryImpl implements TaskRepository`:**
    * 类似`MySqlProjectRepositoryImpl`，使用`TaskJpaEntity`和`SpringDataTaskJpaRepository`.

#### 2.2 Neo4j Adapter (`com.archscope.infrastructure.graph.neo4j`)
* **`Neo4jCodeGraphAdapterImpl implements CodeGraphWriterPort, CodeGraphReaderPort`:**
    * 使用Spring Data Neo4j: 定义节点实体 (如`@Node JavaClassNode`) 和关系实体 (`@RelationshipProperties `)，以及`Neo4jRepository`接口。
    * 或者使用Neo4j Java Driver直接执行Cypher查询。
    * **`storeAnalyzedCodeStructure`方法:**
        * 接收`ProjectId`, `commitId`, 和领域层传递的`ParsedCodeData` (包含类、方法、字段、关系的列表)。
        * 将`ParsedCodeData`转换为Neo4j的节点和关系创建/合并语句 (Cypher)。
        * 考虑使用批处理和事务来高效写入大量图数据。
        * 确保节点（如`JavaClass`）的唯一性（基于`fqn`和`commitId`）。
    * **`getDocumentTreeStructure`方法:**
        * 执行Cypher查询，从与`projectId`和`commitId`关联的`:JavaFile`和`:JavaPackage`节点构建树形结构。
    * **`getDataForMarkdownTemplate`方法:**
        * 根据模板类型或文档路径，执行特定的Cypher查询（例如，获取一个类的所有方法和字段，获取一个包下的所有类）以组装模板所需的数据。

#### 2.3 Redis Adapter (`com.archscope.infrastructure.cache.redis`)
* **`RedisCacheServiceImpl implements CacheServicePort`** (假设`CacheServicePort`在`app`或`domain`定义)
    * 注入`StringRedisTemplate`或`RedisTemplate<String, Object>`.
    * 实现`get`, `put`, `evict`等缓存操作。
    * 使用Jackson进行对象到JSON字符串的序列化/反序列化。
    * 实现`data-model.md`中定义的缓存键命名规范和TTL策略。

### 3. 消息适配器 (Messaging Adapters - RocketMQ)

#### 3.1 `RocketMQTaskProducerImpl` (`com.archscope.infrastructure.messaging.rocketmq.producer`)
* **Implements `TaskMessageProducerPort`** (接口来自`app`层)
* **依赖:** `RocketMQTemplate` (来自`rocketmq-spring-boot-starter`)
* **方法:**
    * `void sendAnalysisTask(Task taskToQueue)`:
        * 将`Task`对象（或其核心payload）序列化为JSON字符串。
        * 使用`rocketMQTemplate.asyncSendOrderly(...)`或`syncSend(...)`发送到`ARCHSCOPE_CODE_ANALYSIS_TOPIC` (Topic名称在配置中定义)。使用`Task.projectId`或`Task.taskId`作为sharding key（如果需要顺序处理某个项目的任务）。
    * `void sendDocGenTask(Task taskToQueue)`: 类似地发送到`ARCHSCOPE_DOC_GENERATION_TOPIC`.

#### 3.2 `RocketMQ Task Consumers` (`com.archscope.infrastructure.messaging.rocketmq.consumer`)
* **`CodeAnalysisTaskConsumer`:**
    * `@Service`
    * `@RocketMQMessageListener(topic = "${archscope.mq.topics.code-analysis}", consumerGroup = "archscope-analysis-consumer-group")`
    * `public void onMessage(String messageJson)`:
        1.  反序列化`messageJson`为`TaskPayloadDTO`或`TaskId`.
        2.  调用`codeAnalysisExecutionService.executeAnalysisTask(...)` (来自`app`层).
        3.  处理ACK/NACK逻辑 (通常由Spring RocketMQ自动处理，除非需要手动控制)。
        4.  错误处理：捕获`executeAnalysisTask`抛出的异常。如果可重试，则让消息重新入队（Spring RocketMQ支持）；如果不可重试或达到最大重试次数，则发送到DLQ或记录严重错误。
* **`DocSiteGenerationTaskConsumer`:** 类似地处理文档生成任务。

### 4. 外部服务客户端/适配器

#### 4.1 `GitServiceAdapterImpl` (`com.archscope.infrastructure.vcs.git`)
* **Implements `GitServiceAdapterPort`**
* **依赖:** JGit库 (或配置为执行`git`命令行)。
* **方法:**
    * `ClonedRepo checkoutRepository(RepositoryInfo repoInfo, String specificCommitOrBranch)`:
        * 处理PAT或OAuth Token认证（从安全存储中获取，或动态获取）。
        * 克隆或更新本地代码副本到临时工作目录。
        * Checkout到指定的commit或branch。
        * 返回本地路径。
    * `WorkspaceedRepoMetadata fetchRemoteRepositoryMetadata(String repoUrl)`:
        * 根据`repoUrl`判断Git提供商 (GitHub/GitLab)。
        * 使用系统级PAT或OAuth App凭证调用相应的Git Provider API (e.g., GitHub `GET /repos/{owner}/{repo}`).
        * 解析API响应，提取项目名称、描述、默认分支、主要语言等。
        * 填充并返回`WorkspaceedRepoMetadata` VO。
        * 处理API调用错误（404, 403, 401,速率限制等）。
* **凭证管理:** 需要与安全存储（如K8s Secrets，或Vault）集成来获取Git访问凭证。

#### 4.2 `OpenRouterLlmClientAdapterImpl` (`com.archscope.infrastructure.llm.openrouter`) - (MVP后)
* **Implements `LlmServicePort`**
* **依赖:** Spring `RestTemplate`或`WebClient`.
* **方法:**
    * `LlmAnalysisResponse analyzeCodeSnippet(String codeSnippet, LlmPrompt prompt, LlmModelConfig config)`:
        1.  从安全存储获取OpenRouter API Key。
        2.  根据`prompt`和`codeSnippet`以及`config`（指定模型、参数如temperature, max_tokens）构造OpenRouter API请求体。
        3.  调用OpenRouter API (`POST /api/v1/chat/completions` 或类似端点)。
        4.  处理响应，提取分析结果。
        5.  处理错误（API限流、认证失败、模型错误等）。
* **配置:** OpenRouter API endpoint, API Key, default model。

#### 4.3 `SsoClientAdapterImpl` (`com.archscope.infrastructure.sso`)
* **职责:** 与Spring Security的SSO集成协作，主要用于从`SecurityContextHolder`或已验证的`Authentication`对象中提取管理员用户的标准化信息。
* **方法:**
    * `Optional<AuthenticatedAdminContext> getCurrentAdminContext()`:
        1.  从`SecurityContextHolder.getContext().getAuthentication()`获取`Authentication`对象。
        2.  检查是否已认证且角色包含`ARCHSCOPE_ADMIN`（角色信息由外部拦截器注入）。
        3.  提取SSO用户ID、用户名等信息，包装成`AuthenticatedAdminContext` VO。
* **注意:** 实际的OIDC/SAML协议处理（如重定向、回调、断言验证）由Spring Security的SSO客户端过滤器（在`archscope-main`中配置）完成。此适配器更多是应用层获取已认证用户上下文的便捷方式。

#### 4.4 `ThymeleafMarkdownGeneratorImpl` & `FileSystemStaticSitePublisherImpl`
(如`architecture.md`中所述，分别负责Markdown生成和静态站点文件发布到K8s PV)

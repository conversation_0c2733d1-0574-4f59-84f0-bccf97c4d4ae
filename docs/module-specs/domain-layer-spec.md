# ArchScope 模块内部设计规范: Domain Layer (`archscope-domain`)

**版本:** 1.1 (基于最终API和数据模型)
**最后更新日期:** 2025-05-12
**关联架构文档版本:** 1.2
**关联数据模型版本:** 1.4
**关联API规范版本:** (最终版)
**参考用户规范附录版本:** (`tech_specs_filled_final.md`)

## 1. 引言
本文档详细描述 `archscope-domain` 模块的内部设计。该模块是ArchScope系统的核心，包含所有业务实体、值对象、领域事件、领域服务接口和仓储接口。它封装了核心业务规则和逻辑，不依赖任何具体的技术实现框架。

## 2. 核心领域实体 (Entities)

## 2.1 `Project` 聚合根
* **包路径:** `com.archscope.domain.entity.Project`
* **核心属性:** (与实际数据库表结构保持一致)
    * `id`: `Long` - 聚合根ID (自增主键)
    * `name`: `String` (len:255, NotNull)
    * `description`: `String` (Text, Nullable)
    * `repositoryUrl`: `String` (len:512, NotNull) - 原始仓库URL
    * `normalizedRepositoryUrl`: `String` (len:512, NotNull, Unique) - 标准化仓库URL
    * `branch`: `String` (len:255, Default: 'main') - 默认分支
    * `type`: `String` (len:50, Default: 'JAVA') - 项目类型
    * `status`: `String` (len:50, Default: 'PENDING') - 项目状态
    * `active`: `Boolean` (Default: true) - 是否激活
    * `creatorId`: `Long` (Nullable) - 创建者ID
    * `createdAt`: `LocalDateTime` (NotNull)
    * `updatedAt`: `LocalDateTime` (NotNull)
    * `lastAnalyzedAt`: `LocalDateTime` (Nullable)
    * `latestAnalyzedCommitId`: `String` (len:40, Nullable)
    * `rating`: `Double` (Nullable) - 项目评分
    * `linesOfCode`: `Long` (Default: 0) - 代码行数
    * `fileCount`: `Integer` (Default: 0) - 文件数量
    * `contributorCount`: `Integer` (Default: 0) - 贡献者数量
    * `icon`: `String` (len:255, Nullable) - 项目图标
* **值对象 `RepositoryInfo`:**
    * `url`: `String` (原始用户输入URL)
    * `type`: `ProjectRepoTypeEnum` (Enum: `GITHUB`, `GITLAB`)
    * `normalizedUrl`: `String` (由原始URL标准化生成，用于唯一性判断)
* **枚举 (位于 `com.archscope.domain.project.model.enums` 或类似包):**
    * `ProjectLanguage`: `JAVA`
    * `ProjectStatus`: `PENDING_ANALYSIS`, `ANALYSIS_IN_PROGRESS`, `ANALYSIS_FAILED`, `DOC_GEN_IN_PROGRESS`, `DOC_GEN_FAILED`, `UNAVAILABLE`, `AVAILABLE` (与 `tech_specs_filled_final.md` 一致)
    * `ProjectVisibility`: `PENDING_REVIEW`, `INTERNAL`, `PUBLIC` (与 `tech_specs_filled_final.md` 一致)
    * `ProjectRepoTypeEnum`: `GITHUB`, `GITLAB` (与 `tech_specs_filled_final.md` 一致)
* **核心业务方法 (确保与业务规则BR-PRJ-* 和 BR-TASK-* 对齐):**
    * `public static Project createForAnonymousSubmission(String nameFromGit, String descriptionFromGit, RepositoryInfo repoInfo, ProjectLanguage language, String defaultBranchFromGit)`:
        * 业务规则 BR-PRJ-003: 初始化 `visibility=PENDING_REVIEW`, `status=PENDING_ANALYSIS`.
        * 填充从Git API获取的字段，若无法获取则使用预设默认值（例如，name可基于repoUrl生成，description可为空）。
        * 触发 `ProjectSubmittedEvent`.
    * `public void initiateAnalysis(String commitIdToAnalyze)`:
        * 更新 `status` 为 `ANALYSIS_IN_PROGRESS`.
        * 记录 `commitIdToAnalyze` (可能作为临时的 `currentAnalyzingCommitId`).
        * 触发 `ProjectAnalysisInitiatedEvent`.
    * `public void recordAnalysisOutcome(String analyzedCommitId, boolean success, String Optional<String> errorMessage, AnalysisSummary summary)`:
        * 若 `success`：更新 `latestAnalyzedCommitId`，`status` 转为 `DOC_GEN_IN_PROGRESS` (或 `UNAVAILABLE` 如果文档生成是可选步骤或需额外触发)。触发 `ProjectAnalysisSucceededEvent`.
        * 若 `!success`：`status` 转为 `ANALYSIS_FAILED`，记录 `errorMessage`。触发 `ProjectAnalysisFailedEvent`.
    * `public void initiateDocumentationGeneration()`:
        * 更新 `status` 为 `DOC_GEN_IN_PROGRESS`.
        * 触发 `ProjectDocGenerationInitiatedEvent`.
    * `public void recordDocumentationOutcome(String forCommitId, String generatedSitePath, boolean success, Optional<String> errorMessage)`:
        * 若 `success`：
            * 如果 `this.visibility == PUBLIC`, 则更新 `latestPublicCommitId = forCommitId` 和 `publicDocSitePath = generatedSitePath`。
            * `status` 转为 `UNAVAILABLE` (等待管理员审核后设为 `AVAILABLE`)。
            * 触发 `ProjectDocGenerationSucceededEvent`.
        * 若 `!success`：`status` 转为 `DOC_GEN_FAILED`，记录 `errorMessage`。触发 `ProjectDocGenerationFailedEvent`.
    * `public void updateVisibilityByAdmin(ProjectVisibility newVisibility, AuthenticatedAdminContext adminContext)`:
        * (AuthenticatedAdminContext包含管理员SSO ID和角色，由外部拦截器提供)
        * 更新 `visibility`。记录 `adminNotes`。触发 `ProjectVisibilityChangedByAdminEvent`.
    * `public void updateStatusByAdmin(ProjectStatus newStatus, AuthenticatedAdminContext adminContext)`:
        * 更新 `status`。记录 `adminNotes`。触发 `ProjectStatusChangedByAdminEvent`.
    * `public boolean isPubliclyViewable()`: `return this.visibility == ProjectVisibility.PUBLIC && this.status == ProjectStatus.AVAILABLE;` (BR-DOC-001)

## 2.2 `Task` 聚合根
* **包路径:** `com.archscope.domain.task.Task`
* **核心属性:** (严格参照 `data-model.md` 1.4 版 `tasks` 表定义及 `tech_specs_filled_final.md` 数据字典)
    * `taskId`: `TaskId` (VO)
    * `projectId`: `ProjectId` (VO, Nullable)
    * `taskType`: `TaskTypeEnum`
    * `status`: `TaskStatusEnum` (Default: `QUEUED`)
    * `payload`: `String` (JSON)
    * `createdAt`, `queuedAt`, `startedAt`, `finishedAt`: `Instant` (Nullable as appropriate)
    * `retryCount`: `int`
    * `lastErrorMessage`: `String` (Nullable)
    * `resultSummary`: `String` (JSON, Nullable, e.g., `{"filesParsed": 150, "classesFound": 30}`)
    * `triggeredByType`: `TaskTriggeredByTypeEnum`
    * `triggeredById`: `String` (Nullable)
* **值对象 `TaskId`:** `id`: `String` (UUID)
* **枚举 (位于 `com.archscope.domain.task.model.enums` 或类似包):**
    * `TaskTypeEnum`: `CODE_FULL_ANALYSIS_JAVA`, `DOC_SITE_GENERATION_JAVA` (来自 `tech_specs_filled_final.md`)
    * `TaskStatusEnum`: `QUEUED`, `RUNNING`, `SUCCESS`, `FAILED` (来自 `tech_specs_filled_final.md`)
    * `TaskTriggeredByTypeEnum`: `WEB_SUBMISSION`, `ADMIN_SSO_TRIGGER`, `CHAINED_TASK_TRIGGER` (来自 `tech_specs_filled_final.md`, `SYSTEM_SCHEDULED_TRIGGER` 是Post-MVP)
* **核心业务方法:**
    * `public static Task create(TaskType type, ProjectId projectId, String payloadJson, TaskTriggeredByType triggeredBy, String triggerId)`: 创建新任务，初始状态`QUEUED`。触发 `TaskCreatedEvent`.
    * `public void markAsRunning()`: 更新`status`为`RUNNING`, 设置`startedAt`. 触发 `TaskStatusChangedEvent`.
    * `public void markAsSuccessful(String resultSummaryJson)`: 更新`status`为`SUCCESS`, 设置`finishedAt`, `resultSummary`. 触发 `TaskStatusChangedEvent`.
    * `public void markAsFailed(String errorMessage)`: 更新`status`为`FAILED`, 设置`finishedAt`, `lastErrorMessage`, 增加`retryCount`. 触发 `TaskStatusChangedEvent`.

## 3. 仓储接口 (Repository Interfaces)
定义在 `archscope-domain` 的 `com.archscope.domain.<subdomain>.port` 包下。

## 3.1 `ProjectRepository`
```java
package com.archscope.domain.project.port;
// ... imports for Project, ProjectId, Page, Pageable, Optional, List, Enums ...
public interface ProjectRepository {
    void save(Project project);
    Optional<Project> findById(ProjectId projectId);
    Optional<Project> findByNormalizedRepoUrl(String normalizedRepoUrl); // Key for BR-PRJ-001 & BR-PRJ-004
    // For project_list.html (public view)
    Page<Project> findAllByVisibilityAndStatus(ProjectVisibility visibility, ProjectStatus status, Pageable pageable);
    // For admin view
    Page<Project> findAllForAdmin(AdminProjectQueryCriteria criteria, Pageable pageable); // Criteria includes name search, status, visibility filters
}
```
* `AdminProjectQueryCriteria` (VO): 包含 `searchTerm`, `statusFilter`, `visibilityFilter`.

## 3.2 `TaskRepository`
```java
package com.archscope.domain.task.port;
// ... imports for Task, TaskId, Page, Pageable, Optional, List, Enums ...
public interface TaskRepository {
    void save(Task task);
    Optional<Task> findById(TaskId taskId);
    // For task_queue.html (public view)
    Page<Task> findAllPublicView(PublicTaskQueryCriteria criteria, Pageable pageable);
    // For admin task view (Post-MVP for advanced filtering/management)
    // Page<Task> findAllForAdmin(AdminTaskQueryCriteria criteria, Pageable pageable);
}
```
* `PublicTaskQueryCriteria` (VO): 包含 `projectIdFilter`, `statusFilter`, `taskTypeFilter`.

## 3.3 `CodeGraphWriterPort` (用于解耦Neo4j写入)
```java
package com.archscope.domain.analysis.port;
// ... imports for ProjectId, GitCommitId, ParsedCodeData (represents AST result) ...
public interface CodeGraphWriterPort {
    void storeAnalyzedCodeStructure(ProjectId projectId, String commitId, ParsedCodeData parsedCodeData);
}
```
* `ParsedCodeData` (VO/DTO): 包含从AST解析出的类、方法、字段、关系的结构化数据。

## 3.4 `CodeGraphReaderPort` (用于解耦Neo4j读取)
```java
package com.archscope.domain.analysis.port;
// ... imports for ProjectId, GitCommitId, DocTreeNode (from domain), etc. ...
public interface CodeGraphReaderPort {
    List<DocTreeNode> getDocumentTreeStructure(ProjectId projectId, String commitId);
    Map<String, Object> getDataForMarkdownTemplate(ProjectId projectId, String commitId, String templateTypeOrDocPath);
    // Other specific query methods needed for documentation or analysis
}
```

## 4. 领域服务接口 (Domain Service Interfaces - 可选)
* `RepositoryMetadataFetcher` (`com.archscope.domain.project.service`):
    * `Optional<FetchedRepoMetadata> fetchMetadata(String repoUrl, RepositoryType type)`: 抽象从Git提供商获取项目名称、描述、默认分支等信息的行为。`WorkspaceedRepoMetadata` 是一个VO。
* `ProjectSubmittedPolicy` (`com.archscope.domain.project.service`):
    * `ProjectSubmissionOutcome evaluateSubmission(String submittedRepoUrl, FetchedRepoMetadata fetchedMetadata)`: 根据BR-PRJ-004判断是新项目还是已存在，并返回相应的结果（如 `NEW_PROJECT_ACCEPTED`, `EXISTING_PUBLIC_REDIRECT`, `EXISTING_INTERNAL_INFO`）。

## 5. 领域事件 (Domain Events)
(与上一版 `architecture.md` 中的定义一致)
* `ProjectSubmittedEvent(projectId, repoUrl, autoFilledName, occurredOn)`
* `ProjectAnalysisTaskCreatedEvent(taskId, projectId, commitId, occurredOn)`
* `ProjectAnalysisCompletedEvent(taskId, projectId, commitId, success, errorMessage, occurredOn)`
* `DocGenerationTaskCreatedEvent(taskId, projectId, commitId, occurredOn)`
* `DocGenerationCompletedEvent(taskId, projectId, commitId, success, sitePath, errorMessage, occurredOn)`
* `ProjectVisibilityUpdatedByAdminEvent(projectId, newVisibility, adminSsoUserId, occurredOn)`
* `ProjectStatusUpdatedByAdminEvent(projectId, newStatus, adminSsoUserId, occurredOn)`

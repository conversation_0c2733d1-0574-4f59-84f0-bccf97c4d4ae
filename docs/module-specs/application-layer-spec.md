# ArchScope 模块内部设计规范: Application Layer (`archscope-app`)

## 1. 引言
本模块负责编排领域对象和基础设施服务来完成业务用例，处理事务边界，并作为`facade`层的主要入口。它不包含业务规则（业务规则在`domain`层）。

## 2. 应用服务 (Application Services)

## 2.1 `ProjectAppService` (`com.archscope.application.project.ProjectAppService`)
* **依赖:** `ProjectRepository`, `TaskRepository`, `TaskMessageProducerPort` (Infra port), `RepositoryMetadataFetcherPort` (Domain/Infra port), `DomainEventPublisherPort`.
* **核心方法 (对应 User Stories & API `operationId`):**
    * `ProjectOperationResponseDTO handleSubmitNewProject(SubmitProjectCommand command)`:
        1.  调用 `RepositoryMetadataFetcherPort.fetchMetadata(command.getRepoUrl())`. 若失败，返回错误。
        2.  标准化URL，调用 `ProjectRepository.findByNormalizedRepoUrl()`.
        3.  **If new:** 调用 `Project.createForAnonymousSubmission(...)` 创建实体，`ProjectRepository.save(project)`. 创建 `Task` (`CODE_FULL_ANALYSIS_JAVA`), `TaskRepository.save(task)`. 调用 `TaskMessageProducerPort.sendAnalysisTask(task)` (BR-TASK-001). 发布 `ProjectSubmittedEvent`, `ProjectAnalysisTaskCreatedEvent`. 返回 `NewProjectAcceptedDTO`部分。
        4.  **If existing:** 检查`project.isPubliclyViewable()`. 返回 `ExistingProjectInfoDTO`部分。
        5.  **事务性:** 确保新项目创建和初始任务创建/发送的原子性。
    * `ProjectDetailDTO getPublicProjectDetails(String projectId)`:
        1.  `project = projectRepository.findById(...)`.
        2.  If `!project.isPubliclyViewable()`, throw `ForbiddenException`.
        3.  (MVP) 获取最新公开commitId下的文档入口点（可能来自`DocumentSet`或`Project`自身的字段）。
        4.  (MVP) 获取与此项目相关的最新几个`TaskSummaryDTO`（公开可见的状态）。
        5.  组装并返回 `ProjectDetailDTO`.
    * `PagedResult<ProjectSummaryDTO> listPubliclyAvailableProjects(Pageable pageable, String searchTerm)`:
        1.  调用 `projectRepository.findAllByVisibilityAndStatus(PUBLIC, AVAILABLE, pageable, searchTerm)`。
        2.  转换为 `ProjectSummaryDTO`。
* **Admin 相关方法 (在 `AdminProjectAppService` 或本服务内通过 `AuthenticatedAdminContext` 进行权限检查):**
    * `ProjectDetailDTO getProjectDetailsForAdmin(String projectId, AuthenticatedAdminContext adminContext)`
    * `void updateProjectByAdmin(String projectId, AdminProjectUpdateCommand command, AuthenticatedAdminContext adminContext)`:
        1.  获取`Project`实体。
        2.  调用`project.updateVisibilityByAdmin()`, `project.updateStatusByAdmin()`, 或其他更新方法。
        3.  `projectRepository.save(project)`.
        4.  发布相应领域事件。
    * `TaskInfoDTO triggerAnalysisByAdmin(String projectId, TriggerAnalysisCommand command, AuthenticatedAdminContext adminContext)`:
        1.  获取`Project`实体。
        2.  创建`Task` (`CODE_FULL_ANALYSIS_JAVA`), `TaskRepository.save()`.
        3.  `taskMessageProducer.sendAnalysisTask()`. 发布事件。

## 2.2 `TaskAppService` (`com.archscope.application.task.TaskAppService`)
* **依赖:** `TaskRepository`, `ProjectRepository`.
* **核心方法:**
    * `TaskDetailDTO getTaskDetailsPublic(String taskId)`: (公开API)
        1.  `task = taskRepository.findById(taskId)`. 若无，抛出`TaskNotFoundException`.
        2.  组装`TaskDetailDTO` (payload和result为摘要)。
    * `PagedResult<TaskSummaryDTO> listTasksPublic(PublicTaskQueryCriteria criteria, Pageable pageable)`: (公开API)
        1.  调用 `taskRepository.findAllPublicView(criteria, pageable)`.
        2.  转换为`TaskSummaryDTO`.
    * `(Admin) TaskDetailDTO getTaskDetailsForAdmin(String taskId, AuthenticatedAdminContext adminContext)`
    * `(Admin) PagedResult<TaskSummaryDTO> listTasksForAdmin(AdminTaskQueryCriteria criteria, Pageable pageable, AuthenticatedAdminContext adminContext)`
    * `(Admin, Post-MVP) void retryTask(String taskId, AuthenticatedAdminContext adminContext)`
    * `(Admin, Post-MVP) void cancelTask(String taskId, AuthenticatedAdminContext adminContext)`

## 2.3 `CodeAnalysisExecutionService` (`com.archscope.application.analysis.CodeAnalysisExecutionService`)
* **(被`RocketMQAnalysisTaskConsumer`调用)**
* **依赖:** `TaskRepository`, `ProjectRepository`, `GitServiceAdapterPort`, `JavaAstParserAdapterPort`, `CodeGraphWriterPort`, `TaskMessageProducerPort`.
* **核心方法:**
    * `@Transactional void executeAnalysisTask(String taskId, AnalysisTaskPayload payload)`:
        1.  `taskRepository.updateStatus(taskId, RUNNING)`.
        2.  `project = projectRepository.findById(payload.getProjectId())`.
        3.  `project.initiateAnalysis(payload.getCommitToAnalyze())`. `projectRepository.save(project)`.
        4.  `codePath = gitService.cloneOrFetch(project.getRepositoryInfo(), payload.getCommitToAnalyze())`.
        5.  `parsedData = javaAstParser.parse(codePath)`.
        6.  `codeGraphWriter.storeAnalyzedCodeStructure(project.getProjectId(), payload.getCommitToAnalyze(), parsedData)`.
        7.  `project.recordAnalysisOutcome(payload.getCommitToAnalyze(), true, null, summary)`.
        8.  `projectRepository.save(project)`.
        9.  `taskRepository.updateStatus(taskId, SUCCESS, analysisResultSummary)`.
        10. 创建并发送`DOC_SITE_GENERATION_JAVA`任务消息。
        11. 异常处理: `project.failAnalysis()`, `taskRepository.updateStatus(taskId, FAILED)`.

## 2.4 `DocumentationGenerationService` (`com.archscope.application.docgen.DocumentationGenerationService`)
* **(被`RocketMQDocGenTaskConsumer`调用)**
* **依赖:** `TaskRepository`, `ProjectRepository`, `CodeGraphReaderPort`, `MarkdownGeneratorAdapterPort`, `StaticSitePublisherAdapterPort`.
* **核心方法:**
    * `@Transactional void executeDocSiteGenerationTask(String taskId, DocGenTaskPayload payload)`:
        1.  `taskRepository.updateStatus(taskId, RUNNING)`.
        2.  `project = projectRepository.findById(payload.getProjectId())`.
        3.  `project.initiateDocumentationGeneration()`. `projectRepository.save(project)`.
        4.  `graphData = codeGraphReader.getDataForMarkdownTemplate(payload.getProjectId(), payload.getCommitId(), "default_java_mvp_template")`.
        5.  `markdownFiles = markdownGenerator.generate(graphData, "default_java_mvp_template")`.
        6.  `sitePathIdentifier = staticSitePublisher.publishSite(payload.getProjectId(), payload.getCommitId(), markdownFiles)`.
        7.  `project.recordDocumentationOutcome(payload.getCommitId(), sitePathIdentifier, true, null)`.
        8.  `projectRepository.save(project)`.
        9.  `taskRepository.updateStatus(taskId, SUCCESS, docGenResultSummary)`.
        10. 异常处理.

## 2.5 `SystemInfoAppService` (`com.archscope.application.system.SystemInfoAppService`)
* **依赖:** (可能是一个静态的错误码配置文件或服务).
* **核心方法:**
    * `List<ErrorCodeDTO> getAllErrorCodes()`: 读取并返回错误码列表。

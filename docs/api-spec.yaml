openapi: 3.0.3
info:
  title: ArchScope API
  version: v1.0.0
  description: |
    API for ArchScope - Architecture Observation and Governance System.
    MVP Focus: Java projects.
    - Anonymous users can submit a project via Git URL; the system then auto-fetches project metadata from the Git provider.
    - The system generates a basic documentation website for successfully analyzed projects.
    - Viewing publicly marked and available project documentation, and viewing task statuses, is open to everyone.
    - Administrative functions (like marking projects public/available, manually triggering analysis, or task operations) are protected and require SSO Admin authentication.
    - SSO integration is part of MVP for admin functions. Permission handling relies on a customer-provided URL filter/interceptor that injects role context. ArchScope API paths for admin functions do NOT have a specific '/admin' prefix.
  contact:
    name: ArchScope Technical Team
    # url: (您的项目/公司网址)
    # email: (您的联系邮箱)
servers:
  - url: /api/v1
    description: ArchScope API Server V1

# ------------------------------------------------------------
# Security Schemes
# ------------------------------------------------------------
components:
  securitySchemes:
    ssoAdminAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
      description: "Authentication token (JWT) for Admin users, obtained via SSO and validated by an external URL filter/interceptor which injects user's role context."

  # ------------------------------------------------------------
  # Reusable Schemas (DTOs) - Reflecting user's tech_specs_filled_final.md
  # ------------------------------------------------------------
  schemas:
    # --- Standard API Response Wrappers ---
    ApiResponse:
      type: object
      properties:
        success:
          type: boolean
          description: "Indicates if the request was successful."
          example: true
        code:
          type: string
          description: "Internal status code (e.g., '200' for success, or a specific error code like 'PRJ_001_REPO_URL_INVALID')."
          example: "200"
        message:
          type: string
          description: "A human-readable message."
          example: "操作成功"
        data:
          type: object
          nullable: true
          description: "The actual response data."
    ErrorResponse:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            success: { type: boolean, example: false }
            code: { type: string, example: "PRJ_001_REPO_URL_INVALID" }
            message: { type: string, example: "仓库URL格式无效或无法访问。" }
            data: { nullable: true, example: null }

    # --- Pagination DTOs ---
    PageParams:
      type: object
      properties:
        page: { type: integer, format: int32, description: "Page number (0-indexed).", default: 0, minimum: 0 }
        size: { type: integer, format: int32, description: "Number of items per page.", default: 10, minimum: 1, maximum: 100 }
        sort: { type: string, nullable: true, description: "Sorting criteria. Format: 'fieldName,(asc|desc)'.", example: "createdAt,desc" }
    PagedResult_ProjectSummaryDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                content: { type: array, items: { $ref: '#/components/schemas/ProjectSummaryDTO' } }
                pageNumber: { type: integer, format: int32 }
                pageSize: { type: integer, format: int32 }
                totalElements: { type: integer, format: int64 }
                totalPages: { type: integer, format: int32 }
                first: { type: boolean }
                last: { type: boolean }
                numberOfElements: { type: integer, format: int32 }
                empty: { type: boolean }
    PagedResult_TaskSummaryDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data:
              type: object
              properties:
                content: { type: array, items: { $ref: '#/components/schemas/TaskSummaryDTO' } }
                pageNumber: { type: integer, format: int32 }
                pageSize: { type: integer, format: int32 }
                totalElements: { type: integer, format: int64 }
                totalPages: { type: integer, format: int32 }
                first: { type: boolean }
                last: { type: boolean }
                numberOfElements: { type: integer, format: int32 }
                empty: { type: boolean }

    # --- Project DTOs ---
    ProjectCreateRequestDTO:
      type: object
      description: "Data to submit a new project. Only repoUrl is required."
      required: [repoUrl]
      properties:
        repoUrl:
          type: string
          format: url
          description: "Git repository URL. System auto-fetches other metadata."
          minLength: 1 # from @NotBlank
          pattern: '^(https?|git)(:\/\/|@)([^:\/\s]+)[\/\:]?(([^:@\/\s]*[:|@])?([^\/\s]+))?\/([^\/\s]+\/[^\/\s]+)(\.git)?(\/)?$' # A common Git URL pattern
          example: "https://gitlab.yeepay.com/awesome/awesome-components"
    ProjectOperationResponseDTO:
      type: object
      properties:
        isNewProject: { type: boolean }
        message: { type: string }
        projectId: { type: string, format: uuid }
        taskInfo: { $ref: '#/components/schemas/TaskInfoDTO', nullable: true }
        existingProjectInfo: {
          type: object
            nullable: true
            properties:
              visibility: { $ref: '#/components/schemas/ProjectVisibilityEnum' }
              status: { $ref: '#/components/schemas/ProjectStatusEnum' }
        }
    ProjectSummaryDTO: # For project_list.html
      type: object
      properties:
        projectId: { type: string, format: "uuid" }
        name: { type: string, maxLength: 64 } # From tech_specs_filled_final.md
        repoUrl: { type: string, format: "url", description: "Original submitted URL."}
        repoType: { $ref: '#/components/schemas/ProjectRepoTypeEnum' }
        language: { $ref: '#/components/schemas/ProjectLanguageEnum' }
        lastDocGeneratedAt: { type: string, format: "date-time", nullable: true, description: "Timestamp of last successful public documentation generation." }
        status: { $ref: '#/components/schemas/ProjectStatusEnum' }
        visibility: { $ref: '#/components/schemas/ProjectVisibilityEnum' }
    ProjectDetailDTO: # For project_detail.html (public view parts)
      allOf:
        - $ref: '#/components/schemas/ProjectSummaryDTO'
      type: object
      properties:
        description: { type: string, maxLength: 1024, nullable: true } # From tech_specs_filled_final.md
        defaultBranch: { type: string, maxLength: 32, nullable: true } # From tech_specs_filled_final.md
        latestAnalyzedCommitId: { type: string, maxLength: 40, nullable: true } # From tech_specs_filled_final.md
        latestPublicCommitId: { type: string, maxLength: 40, nullable: true } # From tech_specs_filled_final.md
        publicDocSitePath: { type: string, maxLength: 255, nullable: true } # From tech_specs_filled_final.md
        createdAt: { type: string, format: "date-time" }
        updatedAt: { type: string, format: "date-time" }
        documentationLinks:
          type: array
          items:
            type: object
            properties:
              name: { type: string }
              targetPath: { type: string }
        recentTasks:
          type: array
          items:
            $ref: '#/components/schemas/TaskSummaryDTO'
    AdminProjectDetailDTO: # For Admin view, inherits public ProjectDetailDTO and adds admin-specific fields
      allOf:
        - $ref: '#/components/schemas/ProjectDetailDTO'
      type: object
      properties:
        adminNotes: { type: string, nullable: true } # From tech_specs_filled_final.md (TEXT maps to string)
        repoUrlNormalized: { type: string, maxLength: 128, readOnly: true, description: "Normalized repo URL used for uniqueness."} # From tech_specs_filled_final.md
    AdminProjectUpdateRequestDTO:
      type: object
      description: "Fields an admin can update for a project."
      properties:
        name: { type: string, maxLength: 64, nullable: true, description: "Admin can override auto-fetched name. Validation: @Size(min=3, max=100)" } # from tech_specs_filled_final.md validation rules
        description: { type: string, maxLength: 1024, nullable: true, description: "Admin can override auto-fetched description. Validation: @Size(max=500)" } # from tech_specs_filled_final.md validation rules
        defaultBranch: { type: string, maxLength: 32, nullable: true, description: "Admin can set/override default branch. Validation: @Size(max=100)" } # from tech_specs_filled_final.md validation rules
        visibility: { $ref: '#/components/schemas/ProjectVisibilityEnum', nullable: true, description: "Validation: @NotNull" } # from tech_specs_filled_final.md validation rules
        status: { $ref: '#/components/schemas/ProjectStatusEnum', nullable: true, description: "Validation: @NotNull" } # from tech_specs_filled_final.md validation rules
        adminNotes: { type: string, nullable: true }
    ProjectLanguageEnum:
      type: string
      enum: [JAVA] # From tech_specs_filled_final.md
    ProjectRepoTypeEnum:
      type: string
      enum: [GITHUB, GITLAB] # From tech_specs_filled_final.md
    ProjectStatusEnum: # From tech_specs_filled_final.md
      type: string
      enum: [PENDING_ANALYSIS, ANALYSIS_IN_PROGRESS, ANALYSIS_FAILED, DOC_GEN_IN_PROGRESS, DOC_GEN_FAILED, UNAVAILABLE, AVAILABLE]
    ProjectVisibilityEnum: # From tech_specs_filled_final.md
      type: string
      enum: [PENDING_REVIEW, INTERNAL, PUBLIC]

    # --- Task DTOs ---
    TaskInfoDTO:
      type: object
      properties:
        taskId: { type: string, format: "uuid" }
        status: { $ref: '#/components/schemas/TaskStatusEnum', example: "QUEUED" }
        message: { type: string, example: "代码分析任务已成功加入队列。" }

    # --- LLM Task Pull/Delivery DTOs ---
    LLMTaskPullRequestDTO:
      type: object
      description: "LLM工作节点任务拉取请求"
      required: [workerId]
      properties:
        workerId:
          type: string
          description: "工作节点唯一标识"
          example: "llm-worker-node-01"
        workerVersion:
          type: string
          description: "工作节点版本"
          example: "1.0.0"
          nullable: true
        supportedTaskTypes:
          type: array
          items:
            $ref: '#/components/schemas/TaskTypeEnum'
          description: "支持的任务类型列表"
          nullable: true
        maxConcurrentTasks:
          type: integer
          format: int32
          description: "最大并发任务数"
          minimum: 1
          maximum: 10
          default: 1
          nullable: true

    LLMTaskPullResponseDTO:
      type: object
      properties:
        hasTask:
          type: boolean
          description: "是否有可用任务"
        taskId:
          type: string
          format: "uuid"
          description: "任务ID"
          nullable: true
        projectId:
          type: string
          format: "uuid"
          description: "项目ID"
          nullable: true
        taskType:
          $ref: '#/components/schemas/TaskTypeEnum'
          nullable: true
        priority:
          type: integer
          format: int32
          description: "任务优先级 (1-10, 数字越大优先级越高)"
          minimum: 1
          maximum: 10
          default: 5
          nullable: true
        createdAt:
          type: string
          format: "date-time"
          description: "任务创建时间"
          nullable: true
        timeoutAt:
          type: string
          format: "date-time"
          description: "任务超时时间 (30分钟后)"
          nullable: true
        inputData:
          type: object
          description: "任务输入数据"
          nullable: true
          properties:
            schemaVersion:
              type: string
              description: "输入数据格式版本"
              example: "1.2"
            repositoryInfo:
              type: object
              properties:
                cloneUrl:
                  type: string
                  format: url
                  description: "Git仓库克隆URL"
                commitId:
                  type: string
                  pattern: "^[0-9a-f]{40}$"
                  description: "Git提交ID (40位十六进制)"
                branchName:
                  type: string
                  description: "分支名称"
                  default: "main"
        parameters:
          type: object
          description: "任务参数"
          nullable: true
          additionalProperties: true
        message:
          type: string
          description: "响应消息"
          example: "No pending tasks available"
          nullable: true

    LLMTaskDeliveryRequestDTO:
      type: object
      description: "LLM任务交付请求"
      required: [overallStatus]
      properties:
        overallStatus:
          type: string
          enum: [COMPLETED, FAILED, PARTIAL_SUCCESS]
          description: "任务整体状态"
        commitId:
          type: string
          pattern: "^[0-9a-f]{40}$"
          description: "关联的Git提交ID (40位十六进制)"
          nullable: true
        results:
          type: array
          items:
            $ref: '#/components/schemas/LLMTaskResultDTO'
          description: "任务结果数组"
          nullable: true
        startTime:
          type: string
          format: "date-time"
          description: "任务开始时间"
          nullable: true
        endTime:
          type: string
          format: "date-time"
          description: "任务结束时间"
          nullable: true
        executionTimeMs:
          type: integer
          format: int64
          description: "执行时间(毫秒)"
          minimum: 0
          nullable: true
        errorMessage:
          type: string
          description: "错误信息 (status为FAILED时必填)"
          nullable: true
        errorDetail:
          type: string
          description: "错误详情"
          nullable: true
        workerInfo:
          $ref: '#/components/schemas/LLMWorkerInfoDTO'
          nullable: true

    LLMTaskResultDTO:
      type: object
      description: "LLM任务单个结果"
      required: [documentType, status]
      properties:
        documentType:
          type: string
          enum: [README, ARCHITECTURE, API, USER_MANUAL, EXTENSION, LLMS_TXT]
          description: "文档类型"
        documentTitle:
          type: string
          description: "文档标题"
          nullable: true
        documentContent:
          type: string
          description: "文档内容 (Markdown格式)"
          nullable: true
        filePath:
          type: string
          description: "文档文件路径"
          nullable: true
        status:
          type: string
          enum: [SUCCESS, FAILED, SKIPPED]
          description: "生成状态"
        errorMessage:
          type: string
          description: "错误信息 (status为FAILED时)"
          nullable: true

    LLMWorkerInfoDTO:
      type: object
      description: "LLM工作节点信息"
      properties:
        workerId:
          type: string
          description: "工作节点ID"
        workerVersion:
          type: string
          description: "工作节点版本"
          nullable: true
        processedTasksCount:
          type: integer
          format: int64
          description: "已处理任务数量"
          minimum: 0
          nullable: true

    LLMTaskDeliveryResponseDTO:
      type: object
      properties:
        taskId:
          type: string
          format: "uuid"
          description: "任务ID"
        status:
          type: string
          description: "任务状态"
        processedAt:
          type: string
          format: "date-time"
          description: "处理时间"
    TaskSummaryDTO:
      type: object
      properties:
        taskId: { type: string, format: "uuid" }
        projectId: { type: string, format: "uuid", nullable: true }
        projectName: { type: string, maxLength: 64, nullable: true }
        taskType: { $ref: '#/components/schemas/TaskTypeEnum' }
        status: { $ref: '#/components/schemas/TaskStatusEnum' }
        createdAt: { type: string, format: "date-time" }
        startedAt: { type: string, format: "date-time", nullable: true }
        finishedAt: { type: string, format: "date-time", nullable: true }
    TaskDetailDTO:
      allOf:
        - $ref: '#/components/schemas/TaskSummaryDTO'
      type: object
      properties:
        payloadSummary: { type: string, nullable: true, description: "Summary of task input parameters." }
        resultSummary: { type: string, nullable: true, description: "Summary of task execution results (e.g., '{\"filesParsed\": 150, \"classesFound\": 30}')" } # From tech_specs_filled_final.md
        lastErrorMessage: { type: string, format: "text", nullable: true } # From tech_specs_filled_final.md
        logsPreview: { type: array, items: { type: string }, nullable: true, example: ["[INFO] Task started..."] }
        triggeredByType: { $ref: '#/components/schemas/TaskTriggeredByTypeEnum' }
        triggeredById: { type: string, maxLength: 255, nullable: true }
        retryCount: {type: integer, format: int32, default: 0}
    TaskTypeEnum: # From tech_specs_filled_final.md
      type: string
      enum: [CODE_FULL_ANALYSIS_JAVA, DOC_SITE_GENERATION_JAVA]
    TaskStatusEnum: # From tech_specs_filled_final.md + LLM task integration
      type: string
      enum: [PENDING, QUEUED, PROCESSING, RUNNING, COMPLETED, SUCCESS, FAILED, PARTIAL_SUCCESS, CANCELLED, PAUSED]
      # Note: PENDING/PROCESSING/COMPLETED for LLM tasks; QUEUED/RUNNING/SUCCESS/FAILED for internal tasks
    TaskTriggeredByTypeEnum: # From tech_specs_filled_final.md
      type: string
      enum: [WEB_SUBMISSION, ADMIN_SSO_TRIGGER, CHAINED_TASK_TRIGGER]
    AdminTaskActionRequestDTO:
      type: object
      required: [action]
      properties:
        action: { type: string, enum: [RETRY, CANCEL] } # Post-MVP

    # --- Documentation DTOs ---
    DocTreeNodeDTO:
      type: object
      properties:
        id: { type: string }
        name: { type: string }
        path: { type: string }
        type: { type: string, enum: [FILE, DIRECTORY] }
        children: { type: array, items: { $ref: '#/components/schemas/DocTreeNodeDTO' }, nullable: true }
    MarkdownDocumentDTO:
      type: object
      properties:
        title: { type: string, nullable: true }
        markdownContent: { type: string }

    # --- System DTOs ---
    ErrorCodeDTO: # For /system/error-codes, from tech_specs_filled_final.md
      type: object
      properties:
        code: { type: string }
        title: { type: string } # Added for better display
        message: { type: string }
        description: { type: string }
        possibleCauses: { type: array, items: { type: string }, nullable: true }
        suggestedSolutions: { type: array, items: { type: string }, nullable: true }
    ApiResponse_ErrorCodeDTO_List:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { type: array, items: { $ref: '#/components/schemas/ErrorCodeDTO' } }
    # Other ApiResponse_xxx wrappers will follow this pattern...
    ApiResponse_ProjectOperationResponseDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { $ref: '#/components/schemas/ProjectOperationResponseDTO' }
    ApiResponse_ProjectDetailDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { $ref: '#/components/schemas/ProjectDetailDTO' }
    ApiResponse_AdminProjectDetailDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { $ref: '#/components/schemas/AdminProjectDetailDTO' }
    ApiResponse_TaskInfoDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { $ref: '#/components/schemas/TaskInfoDTO' }
    ApiResponse_TaskDetailDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { $ref: '#/components/schemas/TaskDetailDTO' }
    ApiResponse_DocTreeNode_List:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { type: array, items: { $ref: '#/components/schemas/DocTreeNodeDTO' } }
    ApiResponse_MarkdownDocumentDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { $ref: '#/components/schemas/MarkdownDocumentDTO' }

    # --- LLM Task API Response Wrappers ---
    ApiResponse_LLMTaskPullResponseDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { $ref: '#/components/schemas/LLMTaskPullResponseDTO' }
    ApiResponse_LLMTaskDeliveryResponseDTO:
      allOf:
        - $ref: '#/components/schemas/ApiResponse'
        - type: object
          properties:
            data: { $ref: '#/components/schemas/LLMTaskDeliveryResponseDTO' }


  # --- Reusable Error Responses from tech_specs_filled_final.md ---
  responses:
    BadRequestError:
      description: "请求参数无效。 (Code: SYS_002_REQUEST_VALIDATION_FAILED or specific like PRJ_001_REPO_URL_INVALID, ANLS_003_UNSUPPORTED_LANGUAGE_FOR_OPERATION)"
      content:
        application/json: { schema: { $ref: '#/components/schemas/ErrorResponse' } }
    UnauthorizedError:
      description: "用户未认证。 (Code: AUTH_001_UNAUTHENTICATED)"
      content:
        application/json: { schema: { $ref: '#/components/schemas/ErrorResponse' } }
    ForbiddenError:
      description: "无权访问。 (Code: AUTH_002_ACCESS_DENIED or PRJ_004_PROJECT_NOT_PUBLICLY_AVAILABLE, PRJ_007_UPDATE_FORBIDDEN)"
      content:
        application/json: { schema: { $ref: '#/components/schemas/ErrorResponse' } }
    NotFoundError:
      description: "资源未找到。 (Code: PRJ_003_PROJECT_NOT_FOUND, TASK_001_NOT_FOUND, DOC_003_FILE_NOT_FOUND_IN_DOCS)"
      content:
        application/json: { schema: { $ref: '#/components/schemas/ErrorResponse' } }
    ConflictError:
      description: "操作与当前资源状态冲突。 (Code: PRJ_005_ALREADY_EXISTS)"
      content:
        application/json: { schema: { $ref: '#/components/schemas/ErrorResponse' } }
    InternalServerError:
      description: "系统内部错误。 (Code: SYS_001_INTERNAL_SERVER_ERROR or specific like PRJ_002_REPO_CLONE_FAILED, ANLS_001_JAVA_PARSE_FAILED, ANLS_002_LLM_REQUEST_FAILED, DOC_001_GENERATION_FAILED, DOC_002_TEMPLATE_NOT_FOUND)"
      content:
        application/json: { schema: { $ref: '#/components/schemas/ErrorResponse' } }

  # ------------------------------------------------------------
  # Reusable Parameters
  # ------------------------------------------------------------
  parameters:
    PageParam:
      name: page
      in: query
      description: "Page number (0-indexed) for pagination."
      required: false
      schema: { type: integer, format: int32, default: 0, minimum: 0 }
    SizeParam:
      name: size
      in: query
      description: "Number of items per page."
      required: false
      schema: { type: integer, format: int32, default: 10, minimum: 1, maximum: 100 }
    SortParam: # Generic sort param
      name: sort
      in: query
      description: "Sort order. Format: 'fieldName,(asc|desc)'."
      required: false
      schema: { type: string }
    SearchParam: # Generic search param
      name: search
      in: query
      description: "Search term for filtering results by name or description."
      required: false
      schema: { type: string }
    ProjectIdPathParam:
      name: projectId
      in: path
      required: true
      description: "The UUID of the project."
      schema: { type: string, format: uuid }
    TaskIdPathParam:
      name: taskId
      in: path
      required: true
      description: "The UUID of the task."
      schema: { type: string, format: uuid }
    FilePathQueryParam:
      name: filePath
      in: query
      required: true
      description: "Path to the document file relative to project's doc root (e.g., README.md, architecture/overview.md)."
      schema: { type: string }
    ProjectVisibilityQueryParam:
      name: visibility
      in: query
      description: "Filter projects by visibility."
      required: false
      schema: { $ref: '#/components/schemas/ProjectVisibilityEnum' }
    ProjectStatusQueryParam:
      name: status
      in: query
      description: "Filter projects by status."
      required: false
      schema: { $ref: '#/components/schemas/ProjectStatusEnum' }
    TaskStatusQueryParam:
      name: status
      in: query
      description: "Filter tasks by status."
      required: false
      schema: { $ref: '#/components/schemas/TaskStatusEnum' }
    TaskTypeQueryParam:
      name: taskType
      in: query
      description: "Filter tasks by type."
      required: false
      schema: { $ref: '#/components/schemas/TaskTypeEnum' }

# ------------------------------------------------------------
# API Paths (Admin paths do not have '/admin' prefix)
# ------------------------------------------------------------
paths:
  # --- Authentication (Post-SSO session info) ---
  /auth/session:
    get:
      tags: [Authentication]
      summary: "获取当前通过SSO认证的用户会话信息 (主要供管理员角色使用)"
      operationId: getCurrentSsoSessionInfo
      # This endpoint implies a valid SSO session is already established by the external filter
      # If called by an admin, the ssoAdminAuth scheme below would be relevant if this endpoint
      # itself requires specific role checks beyond just being authenticated.
      # For now, let's assume the external filter gatekeeps this, and if called, user is authenticated.
      # security:
      #   - ssoAdminAuth: [] # Only if this endpoint itself has further role checks
      responses:
        '200':
          description: "当前SSO用户信息 (角色等由外部拦截器处理并注入上下文)"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  # Define a AuthenticatedUserDetailsDTO or similar if more than just success message
                  - type: object
                    properties:
                      data: { type: object, description: "User details from SSO context, e.g., {ssoUserId: 'id', username: 'name', roles: ['ADMIN']}" }
        '401': { $ref: '#/components/responses/UnauthorizedError' } # If external filter deems session invalid

  # --- Public Project Submission, Listing & Doc Viewing ---
  /projects:
    post:
      tags: [Projects (Public Actions)]
      summary: "匿名提交新项目仓库URL进行分析 (MVP: Java only)"
      operationId: submitNewProject
      description: "用户提供Git仓库URL。系统将尝试从该URL获取项目元数据。如果是新项目，则异步触发代码分析任务。如果项目已存在，则返回现有项目信息。"
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/ProjectCreateRequestDTO' }
            examples:
              submitYeepayProject:
                value:
                  repoUrl: "https://gitlab.yeepay.com/awesome/awesome-components"
      responses:
        '200': # Project already exists
          description: "项目已存在。根据项目状态和可见性，提供相应信息。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_ProjectOperationResponseDTO' } }
        '202': # New project accepted
          description: "新项目提交成功，已加入分析队列。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_ProjectOperationResponseDTO' } }
        '400': { $ref: '#/components/responses/BadRequestError' }
        '409': { $ref: '#/components/responses/ConflictError' }
        '500': { $ref: '#/components/responses/InternalServerError' }
    get: # project_list.html (public view of available projects)
      tags: [Projects (Public Actions)]
      summary: "公开列出状态为AVAILABLE且可见性为PUBLIC的项目 (MVP: Java项目)"
      operationId: listPubliclyAvailableProjects
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/SizeParam'
        - $ref: '#/components/parameters/SortParam' # Specific example: lastDocGeneratedAt,desc
        - $ref: '#/components/parameters/SearchParam'
      responses:
        '200':
          description: "成功获取公开且可用的项目列表。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/PagedResult_ProjectSummaryDTO' } }

  /projects/{projectId}: # Public view of a specific project's details
    get:
      tags: [Projects (Public Actions)]
      summary: "获取特定公开可用项目的详情 (不含管理信息)"
      operationId: getPublicProjectDetails
      parameters:
        - $ref: '#/components/parameters/ProjectIdPathParam'
      responses:
        '200':
          description: "成功获取项目详情。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_ProjectDetailDTO' } }
        '403': { $ref: '#/components/responses/ForbiddenError' }
        '404': { $ref: '#/components/responses/NotFoundError' }

  /projects/{projectId}/documentation/latest/tree:
    get:
      tags: [Documentation (Public)]
      summary: "获取项目最新公开可用版本的文档树结构 (MVP)"
      operationId: getPublicLatestDocumentationTree
      parameters:
        - $ref: '#/components/parameters/ProjectIdPathParam'
      responses:
        '200':
          description: "成功获取文档树结构。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_DocTreeNode_List' } }
        '403': { $ref: '#/components/responses/ForbiddenError' }
        '404': { $ref: '#/components/responses/NotFoundError' }

  /projects/{projectId}/documentation/latest/content:
    get:
      tags: [Documentation (Public)]
      summary: "获取项目最新公开可用版本中特定文档文件的内容 (MVP)"
      operationId: getPublicLatestDocumentContent
      parameters:
        - $ref: '#/components/parameters/ProjectIdPathParam'
        - $ref: '#/components/parameters/FilePathQueryParam'
      responses:
        '200':
          description: "成功获取Markdown文档内容。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_MarkdownDocumentDTO' } }
        '403': { $ref: '#/components/responses/ForbiddenError' }
        '404': { $ref: '#/components/responses/NotFoundError' }

  # --- Public Task Viewing ---
  /tasks: # task_queue.html (public view)
    get:
      tags: [Tasks (Public View)]
      summary: "公开列出所有后台任务 (用于监控)"
      operationId: listAllTasksPublicView
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/SizeParam'
        - name: projectId # From task_queue.html prototype, can filter by projectId
          in: query
          required: false
          schema: { type: string, format: uuid }
        - $ref: '#/components/parameters/TaskStatusQueryParam' # From task_queue.html prototype
        - $ref: '#/components/parameters/TaskTypeQueryParam' # From task_queue.html prototype (if filter exists)
        - $ref: '#/components/parameters/SortParam' # Example: createdAt,desc
      responses:
        '200':
          description: "成功获取任务列表。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_PagedResult_TaskSummaryDTO' } }

  /tasks/{taskId}: # task_detail.html (public view)
    get:
      tags: [Tasks (Public View)]
      summary: "公开获取特定后台任务的详情 (用于监控)"
      operationId: getTaskByIdPublicView
      parameters:
        - $ref: '#/components/parameters/TaskIdPathParam'
      responses:
        '200':
          description: "成功获取任务详情。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_TaskDetailDTO' } }
        '404': { $ref: '#/components/responses/NotFoundError' }

  # --- LLM Task Pull/Delivery Endpoints (External LLM Service Integration) ---
  /api/v1/llm-tasks/pull:
    post:
      tags: [LLM Tasks (External Service)]
      summary: "LLM工作节点拉取待处理任务"
      operationId: pullLLMTask
      description: "外部LLM服务周期性调用此接口获取待处理任务。系统会将PENDING状态的任务分配给工作节点，并将任务状态更新为PROCESSING。任务有30分钟超时机制。"
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/LLMTaskPullRequestDTO' }
            examples:
              pullTaskRequest:
                value:
                  workerId: "llm-worker-node-01"
                  workerVersion: "1.0.0"
                  supportedTaskTypes: ["CODE_FULL_ANALYSIS_JAVA", "DOC_SITE_GENERATION_JAVA"]
                  maxConcurrentTasks: 3
      responses:
        '200':
          description: "任务拉取成功。如果hasTask为true，则返回任务详情；如果为false，则表示当前无可用任务。"
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ApiResponse_LLMTaskPullResponseDTO' }
              examples:
                hasTask:
                  summary: "有可用任务"
                  value:
                    success: true
                    code: "200"
                    message: "任务拉取成功"
                    data:
                      hasTask: true
                      taskId: "12345678-1234-1234-1234-123456789012"
                      projectId: "87654321-4321-4321-4321-210987654321"
                      taskType: "CODE_FULL_ANALYSIS_JAVA"
                      priority: 5
                      createdAt: "2025-06-23T10:30:00Z"
                      timeoutAt: "2025-06-23T11:00:00Z"
                      inputData:
                        schemaVersion: "1.2"
                        repositoryInfo:
                          cloneUrl: "https://github.com/example/project.git"
                          commitId: "a1b2c3d4e5f6789012345678901234567890abcd"
                          branchName: "main"
                      parameters:
                        additionalConfig: "value"
                noTask:
                  summary: "无可用任务"
                  value:
                    success: true
                    code: "200"
                    message: "当前无可用任务"
                    data:
                      hasTask: false
                      message: "No pending tasks available"
        '400': { $ref: '#/components/responses/BadRequestError' }
        '500': { $ref: '#/components/responses/InternalServerError' }

  /api/v1/llm-tasks/{taskId}/callback:
    post:
      tags: [LLM Tasks (External Service)]
      summary: "LLM服务提交任务处理结果"
      operationId: deliverLLMTaskResult
      description: "外部LLM服务完成任务处理后调用此接口提交结果。支持完全成功(COMPLETED)、完全失败(FAILED)和部分成功(PARTIAL_SUCCESS)状态。接口支持幂等调用。"
      parameters:
        - name: taskId
          in: path
          required: true
          description: "任务ID"
          schema: { type: string, format: uuid }
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/LLMTaskDeliveryRequestDTO' }
            examples:
              completedTask:
                summary: "任务完成"
                value:
                  overallStatus: "COMPLETED"
                  commitId: "a1b2c3d4e5f6789012345678901234567890abcd"
                  results:
                    - documentType: "README"
                      documentTitle: "项目说明文档"
                      documentContent: "# 项目说明\n\n这是一个示例项目..."
                      filePath: "README.md"
                      status: "SUCCESS"
                    - documentType: "ARCHITECTURE"
                      documentTitle: "架构设计文档"
                      documentContent: "# 架构设计\n\n## 系统架构..."
                      filePath: "docs/architecture.md"
                      status: "SUCCESS"
                  startTime: "2025-06-23T10:30:00Z"
                  endTime: "2025-06-23T10:45:00Z"
                  executionTimeMs: 900000
                  workerInfo:
                    workerId: "llm-worker-node-01"
                    workerVersion: "1.0.0"
                    processedTasksCount: 42
              failedTask:
                summary: "任务失败"
                value:
                  overallStatus: "FAILED"
                  errorMessage: "代码仓库克隆失败"
                  errorDetail: "Git clone failed: repository not found or access denied"
                  startTime: "2025-06-23T10:30:00Z"
                  endTime: "2025-06-23T10:32:00Z"
                  executionTimeMs: 120000
                  workerInfo:
                    workerId: "llm-worker-node-01"
                    workerVersion: "1.0.0"
      responses:
        '200':
          description: "任务结果处理成功"
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ApiResponse_LLMTaskDeliveryResponseDTO' }
              examples:
                success:
                  value:
                    success: true
                    code: "200"
                    message: "任务结果处理成功"
                    data:
                      taskId: "12345678-1234-1234-1234-123456789012"
                      status: "COMPLETED"
                      processedAt: "2025-06-23T10:45:00Z"
        '400': { $ref: '#/components/responses/BadRequestError' }
        '404': { $ref: '#/components/responses/NotFoundError' }
        '500': { $ref: '#/components/responses/InternalServerError' }

  /llm-tasks/{taskId}/status:
    get:
      tags: [LLM Tasks (External Service)]
      summary: "查询LLM任务状态 (辅助接口)"
      operationId: getLLMTaskStatus
      description: "外部LLM服务查询任务状态的辅助接口。建议使用标准任务API (/api/v1/tasks/{taskId}) 获取详细状态信息。"
      parameters:
        - name: taskId
          in: path
          required: true
          description: "任务ID"
          schema: { type: string, format: uuid }
      responses:
        '200':
          description: "任务状态查询成功"
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        type: object
                        properties:
                          taskId: { type: string, format: uuid }
                          message: { type: string, example: "请使用标准任务API查询详细状态" }
                          statusEndpoint: { type: string, example: "/api/v1/tasks/12345678-1234-1234-1234-123456789012" }
        '404': { $ref: '#/components/responses/NotFoundError' }
        '500': { $ref: '#/components/responses/InternalServerError' }

  # --- System Endpoints ---
  /system/error-codes:
    get:
      tags: [System]
      summary: "获取系统错误码列表及其描述"
      operationId: getSystemErrorCodes
      responses:
        '200':
          description: "成功获取错误码列表。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_ErrorCodeDTO_List' } }

  # --- Administration Endpoints (Protected by external URL filter + ssoAdminAuth) ---
  # Note: Paths do NOT start with /admin per user request
  /projects-management: # Admin listing of all projects
    get:
      tags: [Administration (Projects)]
      summary: "(Admin) 列出所有项目，包括非公开、待审核等"
      operationId: listAllProjectsAdmin
      security:
        - ssoAdminAuth: []
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/SizeParam'
        - $ref: '#/components/parameters/SortParam' # Example: createdAt,desc
        - $ref: '#/components/parameters/SearchParam'
        - $ref: '#/components/parameters/ProjectVisibilityQueryParam'
        - $ref: '#/components/parameters/ProjectStatusQueryParam'
      responses:
        '200':
          description: "成功获取所有项目列表 (管理员视图)。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_PagedResult_ProjectSummaryDTO' } }
        '401': { $ref: '#/components/responses/UnauthorizedError' }
        '403': { $ref: '#/components/responses/ForbiddenError' }

  /projects-management/{projectId}: # Admin GET, PUT, DELETE for a specific project
    get:
      tags: [Administration (Projects)]
      summary: "(Admin) 获取任何项目的完整详情，包括管理备注"
      operationId: getProjectByIdAdmin
      security:
        - ssoAdminAuth: []
      parameters:
        - $ref: '#/components/parameters/ProjectIdPathParam'
      responses:
        '200':
          description: "成功获取项目完整详情 (管理员视图)。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_AdminProjectDetailDTO' } }
        '401': { $ref: '#/components/responses/UnauthorizedError' }
        '403': { $ref: '#/components/responses/ForbiddenError' }
        '404': { $ref: '#/components/responses/NotFoundError' }
    put:
      tags: [Administration (Projects)]
      summary: "(Admin) 更新项目详情、可见性、状态或管理备注"
      operationId: updateProjectAdmin
      security:
        - ssoAdminAuth: []
      parameters:
        - $ref: '#/components/parameters/ProjectIdPathParam'
      requestBody:
        required: true
        content:
          application/json:
            schema: { $ref: '#/components/schemas/AdminProjectUpdateRequestDTO' }
      responses:
        '200':
          description: "项目信息更新成功。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_AdminProjectDetailDTO' } }
        '400': { $ref: '#/components/responses/BadRequestError' }
        '401': { $ref: '#/components/responses/UnauthorizedError' }
        '403': { $ref: '#/components/responses/ForbiddenError' }
        '404': { $ref: '#/components/responses/NotFoundError' }
    delete: # Post-MVP for project deletion
      tags: [Administration (Projects)]
      summary: "(Admin) 删除一个项目 (Post-MVP)"
      operationId: deleteProjectAdmin
      security:
        - ssoAdminAuth: []
      parameters:
        - $ref: '#/components/parameters/ProjectIdPathParam'
      responses:
        '204': { description: "项目删除成功。" }
        '401': { $ref: '#/components/responses/UnauthorizedError' }
        '403': { $ref: '#/components/responses/ForbiddenError' }
        '404': { $ref: '#/components/responses/NotFoundError' }

  /projects-management/{projectId}/trigger-analysis: # Admin trigger analysis
    post:
      tags: [Administration (Projects)]
      summary: "(Admin) 手动触发指定项目的代码分析"
      operationId: triggerProjectAnalysisAdmin
      security:
        - ssoAdminAuth: []
      parameters:
        - $ref: '#/components/parameters/ProjectIdPathParam'
      requestBody:
        description: "分析任务的可选参数。"
        content:
          application/json:
            schema:
              type: object
              properties:
                commitId:
                  type: string
                  maxLength: 40
                  pattern: "^[0-9a-f]{7,40}$" # From tech_specs_filled_final.md validation
                  nullable: true
                  description: "要分析的特定Commit SHA。如果为空，则使用项目的默认分支的最新Commit。"
                forceReanalysis:
                  type: boolean
                  default: false
                  description: "强制重新分析。"
      responses:
        '202':
          description: "分析任务已接受并加入队列。"
          content:
            application/json: { schema: { $ref: '#/components/schemas/ApiResponse_TaskInfoDTO' } }
        '400': { $ref: '#/components/responses/BadRequestError' }
        '401': { $ref: '#/components/responses/UnauthorizedError' }
        '403': { $ref: '#/components/responses/ForbiddenError' }
        '404': { $ref: '#/components/responses/NotFoundError' }

  # Admin Task Operations (Post-MVP, e.g., retry, cancel. GET /tasks/{taskId} is public)
  # Example for Post-MVP admin task action:
  # /tasks-management/{taskId}/action:
  #   post:
  #     tags: [Administration (Tasks)]
  #     summary: "(Admin) 对特定任务执行操作 (如重试、取消)"
  #     operationId: performAdminTaskAction
  #     security:
  #       - ssoAdminAuth: []
  #     parameters:
  #       - $ref: '#/components/parameters/TaskIdPathParam'
  #     requestBody:
  #       required: true
  #       content:
  #         application/json:
  #           schema: { $ref: '#/components/schemas/AdminTaskActionRequestDTO' }
  #     responses:
  #       '200':
  #         description: "操作已成功执行或已入队。"
  #         content:
  #           application/json: { schema: { $ref: '#/components/schemas/ApiResponse_TaskDetailDTO' } } # Return updated task
  #       '400': { $ref: '#/components/responses/BadRequestError' }
  #       '401': { $ref: '#/components/responses/UnauthorizedError' }
  #       '403': { $ref: '#/components/responses/ForbiddenError' }
  #       '404': { $ref: '#/components/responses/NotFoundError' }

tags:
  - name: Authentication
    description: "用户认证会话管理 (主要与SSO集成后获取会话信息相关)。"
  - name: Projects (Public Actions)
    description: "公开的项目提交和基本信息查看接口。"
  - name: Documentation (Public)
    description: "公开的项目文档内容访问接口。"
  - name: Tasks (Public View)
    description: "公开的任务状态和详情查看接口，用于通用监控。"
  - name: LLM Tasks (External Service)
    description: "外部LLM服务集成接口，用于任务拉取和结果交付。支持30分钟超时机制和PENDING->PROCESSING->COMPLETED/FAILED状态流转。"
  - name: System
    description: "系统级通用信息接口 (如错误码列表)。"
  - name: Administration (Projects)
    description: "管理员专属的项目管理接口 (需SSO认证及管理员角色，由外部URL过滤器保护)。"
  - name: Administration (Tasks)
    description: "管理员专属的任务管理接口 (查看已通过公开API提供，操作类接口需SSO认证，多为Post-MVP)。"
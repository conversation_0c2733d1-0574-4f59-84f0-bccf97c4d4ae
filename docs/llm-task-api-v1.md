# LLM任务API接口文档 V1

## 概述

本文档描述了ArchScope系统与外部LLM服务交互的API接口规范。这些接口实现了任务拉取和交付的异步处理模式，支持30分钟超时机制和多种任务状态流转。

## 接口规范

### 基础信息

- **API版本**: v1
- **基础路径**: `/api/v1/llm-tasks`
- **内容类型**: `application/json`
- **字符编码**: UTF-8

### 任务状态流转

```
PENDING → PROCESSING → COMPLETED/FAILED/PARTIAL_SUCCESS
    ↓         ↓
CANCELLED  PAUSED
    ↓         ↓
PENDING   PROCESSING
```

- **PENDING**: 等待处理
- **PROCESSING**: 正在处理（30分钟超时）
- **COMPLETED**: 完全成功
- **FAILED**: 完全失败
- **PARTIAL_SUCCESS**: 部分成功

## API接口

### 1. 任务拉取接口

**接口地址**: `POST /api/v1/llm-tasks/pull`

**功能描述**: LLM工作节点周期性调用此接口获取待处理任务

#### 请求参数

```json
{
  "workerId": "llm-worker-node-01",
  "workerVersion": "1.0.0",
  "supportedTaskTypes": ["CODE_ANALYSIS", "DOC_GENERATION"],
  "maxConcurrentTasks": 3
}
```

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| workerId | string | 是 | 工作节点唯一标识 |
| workerVersion | string | 否 | 工作节点版本 |
| supportedTaskTypes | array | 否 | 支持的任务类型列表 |
| maxConcurrentTasks | integer | 否 | 最大并发任务数 |

#### 响应格式

**有任务时**:
```json
{
  "hasTask": true,
  "taskId": 12345,
  "projectId": 67890,
  "taskType": "CODE_ANALYSIS",
  "priority": 5,
  "createdAt": "2025-06-23T10:30:00",
  "timeoutAt": "2025-06-23T11:00:00",
  "inputData": {
    "schemaVersion": "1.2",
    "repositoryInfo": {
      "cloneUrl": "https://github.com/example/project.git",
      "commitId": "a1b2c3d4e5f6789012345678901234567890abcd",
      "branchName": "main"
    }
  },
  "parameters": {
    "additionalConfig": "value"
  }
}
```

**无任务时**:
```json
{
  "hasTask": false,
  "message": "No pending tasks available"
}
```

### 2. 任务交付接口

**接口地址**: `POST /api/v1/llm-tasks/{taskId}/callback`

**功能描述**: LLM服务完成处理后回调此接口提交结果

#### 路径参数

| 参数 | 类型 | 说明 |
|------|------|------|
| taskId | long | 任务ID |

#### 请求参数

```json
{
  "overallStatus": "COMPLETED",
  "commitId": "a1b2c3d4e5f6789012345678901234567890abcd",
  "results": [
    {
      "documentType": "README",
      "documentTitle": "项目说明文档",
      "documentContent": "# 项目说明\n\n这是一个示例项目...",
      "filePath": "README.md",
      "status": "SUCCESS"
    },
    {
      "documentType": "ARCHITECTURE",
      "documentTitle": "架构设计文档",
      "documentContent": "# 架构设计\n\n## 系统架构...",
      "filePath": "docs/architecture.md",
      "status": "SUCCESS"
    }
  ],
  "startTime": "2025-06-23T10:30:00",
  "endTime": "2025-06-23T10:45:00",
  "executionTimeMs": 900000,
  "workerInfo": {
    "workerId": "llm-worker-node-01",
    "workerVersion": "1.0.0",
    "processedTasksCount": 42
  }
}
```

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| overallStatus | string | 是 | 任务整体状态 (COMPLETED/FAILED/PARTIAL_SUCCESS) |
| commitId | string | 否 | 关联的Git提交ID (40位十六进制) |
| results | array | 否 | 任务结果数组 |
| startTime | datetime | 否 | 任务开始时间 |
| endTime | datetime | 否 | 任务结束时间 |
| executionTimeMs | long | 否 | 执行时间(毫秒) |
| errorMessage | string | 否 | 错误信息 (status为FAILED时必填) |
| errorDetail | string | 否 | 错误详情 |
| workerInfo | object | 否 | 工作节点信息 |

#### 结果对象格式

| 字段 | 类型 | 必填 | 说明 |
|------|------|------|------|
| documentType | string | 是 | 文档类型 (README/ARCHITECTURE/API等) |
| documentTitle | string | 否 | 文档标题 |
| documentContent | string | 否 | 文档内容 (Markdown格式) |
| filePath | string | 否 | 文档文件路径 |
| status | string | 是 | 生成状态 (SUCCESS/FAILED/SKIPPED) |
| errorMessage | string | 否 | 错误信息 (status为FAILED时) |

#### 响应格式

**成功时**:
```json
{
  "success": true,
  "message": "任务结果处理成功",
  "data": {
    "taskId": 12345,
    "status": "COMPLETED",
    "processedAt": "2025-06-23T10:45:00"
  }
}
```

**失败时**:
```json
{
  "success": false,
  "message": "任务结果处理失败，请检查任务状态"
}
```

### 3. 任务状态查询接口

**接口地址**: `GET /api/v1/llm-tasks/{taskId}/status`

**功能描述**: 查询任务状态信息（辅助接口）

#### 响应格式

```json
{
  "success": true,
  "message": "任务状态查询",
  "data": {
    "taskId": 12345,
    "message": "请使用标准任务API查询详细状态",
    "statusEndpoint": "/api/tasks/12345"
  }
}
```

## 错误处理

### HTTP状态码

- `200 OK`: 请求成功
- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 任务不存在
- `500 Internal Server Error`: 服务器内部错误

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述信息"
}
```

### 常见错误

1. **任务锁定失败**: 任务已被其他工作节点锁定
2. **参数验证失败**: 请求参数格式不正确
3. **任务状态错误**: 任务当前状态不允许此操作
4. **超时处理**: 任务超过30分钟自动重置为PENDING状态

## 超时机制

- **超时时间**: 30分钟
- **检查频率**: 每5分钟检查一次
- **超时处理**: 自动将PROCESSING状态任务重置为PENDING状态
- **重试机制**: 支持任务重试，最大重试次数可配置

## 使用示例

### Python客户端示例

```python
import requests
import json
from datetime import datetime

class LLMWorkerClient:
    def __init__(self, archscope_url, worker_id, worker_type="CODE_ANALYSIS"):
        self.archscope_url = archscope_url
        self.worker_id = worker_id
        self.worker_type = worker_type
    
    def pull_task(self):
        """拉取任务"""
        url = f"{self.archscope_url}/api/v1/llm-tasks/pull"
        payload = {
            "workerId": self.worker_id,
            "workerVersion": "1.0.0"
        }
        
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()
    
    def deliver_result(self, task_id, status, results=None, error_message=None):
        """提交结果"""
        url = f"{self.archscope_url}/api/v1/llm-tasks/{task_id}/callback"
        payload = {
            "overallStatus": status,
            "startTime": datetime.now().isoformat(),
            "endTime": datetime.now().isoformat(),
            "executionTimeMs": 900000,
            "workerInfo": {
                "workerId": self.worker_id,
                "workerVersion": "1.0.0"
            }
        }
        
        if results:
            payload["results"] = results
        if error_message:
            payload["errorMessage"] = error_message
            
        response = requests.post(url, json=payload)
        response.raise_for_status()
        return response.json()

# 使用示例
client = LLMWorkerClient("http://localhost:8080", "worker-001")

# 拉取任务
task = client.pull_task()
if task.get("hasTask"):
    task_id = task["taskId"]
    
    # 处理任务...
    
    # 提交成功结果
    results = [
        {
            "documentType": "README",
            "documentTitle": "项目说明",
            "documentContent": "# 项目说明\n\n...",
            "status": "SUCCESS"
        }
    ]
    
    response = client.deliver_result(task_id, "COMPLETED", results=results)
    print(f"任务提交成功: {response}")
```

## 配置说明

### 应用配置

```yaml
# application.yml
archscope:
  scheduler:
    llm-timeout:
      enabled: true  # 启用超时检查
      
app:
  scheduler:
    llm-task-timeout:
      enabled: true  # 启用任务超时处理
```

### 数据库配置

确保已执行数据库迁移脚本 `V1_2__extend_tasks_for_llm.sql` 以支持新的字段和表结构。

## 监控和日志

- 任务拉取和交付操作会记录详细日志
- 超时任务处理会记录警告日志
- 建议监控以下指标：
  - 任务拉取频率
  - 任务完成率
  - 平均处理时间
  - 超时任务数量

## 注意事项

1. **幂等性**: 任务交付接口支持重复调用
2. **并发安全**: 使用数据库锁和Redis分布式锁确保并发安全
3. **错误恢复**: 超时任务会自动重置，支持重新处理
4. **资源清理**: 定期清理已完成的旧任务记录
5. **版本兼容**: 输入数据格式使用schema版本控制

## 模块化单体架构 - 事件驱动的解耦任务管道之详细设计

### 1. C4模型展示

为了清晰地展现方案B的架构，我们将分层描述其C4模型。

#### 1.1 Level 1: 系统上下文图 (System Context)

此图已在阶段2中基本形成，再次确认ArchScope系统的核心交互边界：

* **用户 (End User)**：通过Web浏览器与ArchScope前端应用交互，查看项目、文档，触发分析任务。
* **Git仓库 (Git Repository - 如GitHub, GitLab)**：ArchScope从此拉取项目源代码进行分析。通过HTTPS和Token认证。
* **OpenRouter API (LLM Service)**：ArchScope调用此服务，利用大语言模型分析代码结构（结合AST结果）并生成文档内容。通过API Key认证。
* **配置文件系统 (Configuration System - 作为部署包一部分)**：为ArchScope提供运行配置，包括数据库连接信息、加密的Git和OpenRouter凭证、预设的LLM提示词等。
* **文件系统/文档存储 (File System - HTML Output Directory)**：ArchScope后端将最终生成的HTML文档网站输出到此指定目录，供Web服务器访问。

```mermaid
graph LR
    subgraph ExternalSystems [外部系统/参与者]
        User[(用户)]
        GitRepo[Git仓库<br>GitHub/GitLab]
        OpenRouter[OpenRouter API<br>LLM服务]
        ConfigFile[配置文件<br>部署包内]
        FileSystem[文件系统<br>HTML输出目录]
    end

    subgraph ArchScopeSystem [ArchScope 系统]
        ArchScopeBackend[模块化后端应用<br>Spring Boot]
    end

    User -- HTTPS --> ArchScopeBackend
    ArchScopeBackend -- HTTPS/Git --> GitRepo
    ArchScopeBackend -- HTTPS --> OpenRouter
    ConfigFile -- 读取 --> ArchScopeBackend
    ArchScopeBackend -- 写入 --> FileSystem

    %% 以下为前端部分，虽然在后端设计中不是主要容器，但为了上下文完整性可提及
    %% FrontendApp[前端应用 (Vue.js SPA)<br>@用户浏览器]
    %% User -- interacts via --> FrontendApp
    %% FrontendApp -- HTTP API --> ArchScopeBackend
    %% WebServer[Web服务器<br>(Nginx/Embedded)]
    %% WebServer -- serves --> FrontendApp
    %% WebServer -- serves static --> FileSystem
```
*(注：上述Mermaid图例为简化版系统上下文，实际 `architecture.md` 中会提供更规范的图示)*

#### 1.2 Level 2: 容器图 (Containers)

在ArchScope系统内部，MVP阶段我们可以定义以下主要容器（大部分将通过Docker Compose进行本地部署和管理）：

1.  **`Frontend Application (Vue.js SPA)`**:
    * 描述：单页应用程序，运行在用户的浏览器中。提供用户界面，用于项目注册、手动触发分析、查看任务状态、浏览生成的文档网站。
    * 技术：Vue.js 3, TypeScript, Tailwind CSS。
    * 职责：用户交互、API请求发送、前端展现。

2.  **`Backend API Application (Spring Boot - 模块化单体)`**:
    * 描述：系统的核心，处理所有业务逻辑、API请求、编排分析任务流程，并与所有其他数据和消息容器交互。采用模块化设计。
    * 技术：Java 8+, Spring Boot 3.x, Maven。
    * 职责：API服务、业务逻辑处理、任务编排与调度、与外部服务（Git, OpenRouter）集成、文档内容生成（Markdown到HTML）、HTML文件输出。
    * 部署：Docker容器。

3.  **`Database (MySQL)`**:
    * 描述：关系型数据库，存储项目元数据（名称、仓库地址、配置等）、分析任务信息（ID、状态、各阶段状态、时间戳、关联的项目ID等）、文档版本元数据（Commit ID、生成时间、HTML路径等）。MVP阶段不包含用户账户信息。
    * 技术：MySQL 8.0+。
    * 职责：持久化核心业务数据。
    * 部署：Docker容器。

4.  **`Cache (Redis)`**:
    * 描述：内存数据存储，用于缓存常用数据（如项目简要信息、任务状态）、分布式锁（确保某些任务阶段的原子性，如适用）、会话管理（MVP阶段无用户会话，未来可能使用）。
    * 技术：Redis 7.x。
    * 职责：提升性能、管理临时状态。
    * 部署：Docker容器。

5.  **`Graph Database (Neo4j)`**:
    * 描述：图数据库，用于存储从源代码AST解析出的代码结构信息（如类、方法、它们之间的调用关系、继承关系等）。这些结构化数据将作为LLM分析的重要上下文。
    * 技术：Neo4j 5.x。
    * 职责：存储和查询代码的结构化知识图谱。
    * 部署：Docker容器。

6.  **`Message Queue (Apache RocketMQ)`**:
    * 描述：分布式消息队列，用于解耦和驱动“代码分析与文档生成”流程中的各个独立子任务。实现任务的异步处理、失败重试（针对单个阶段）、削峰填谷。
    * 技术：Apache RocketMQ 5.x。
    * 职责：异步任务调度、事件驱动的流程编排。
    * 部署：Docker容器。

7.  **`Generated Documentation Storage (File System Volume)`**:
    * 描述：一个持久化的文件系统目录（例如，通过Docker Volume映射）。`Backend API Application`将生成的HTML文档网站（按项目、版本组织）输出到此目录。
    * 技术：标准文件系统。
    * 职责：存储最终用户可访问的静态文档网站文件。

8.  **`Web Server (Nginx - 推荐或嵌入式备选)`**:
    * 描述：用于向用户提供`Frontend Application (Vue.js SPA)`的静态资源，并作为反向代理将API请求路由到`Backend API Application`。同时，它也负责提供对`Generated Documentation Storage`中静态HTML文档网站的访问。
    * 技术：Nginx (推荐) 或 Spring Boot内嵌Web服务器（如Tomcat/Jetty，可同时服务API和静态内容）。
    * 职责：静态资源服务、API反向代理、负载均衡（未来）。
    * 部署：Docker容器 (若为独立Nginx)。

*(注：在MVP的Docker Compose环境中，Spring Boot应用自身也可以配置为服务其前端静态资源和生成的文档，以简化初始部署。但从长远和生产部署角度看，独立的Nginx更佳。)*

```mermaid
graph TD
    UserInterface[用户浏览器]

    subgraph ArchScopeSystemBoundary [ArchScope 系统 - 运行于服务器]
        FrontendAppContainer[Frontend Application<br>Vue.js SPA @ 浏览器]
        BackendAppContainer[Backend API Application<br>Spring Boot Modular Monolith]
        MySQLContainer[Database<br>MySQL]
        RedisContainer[Cache<br>Redis]
        Neo4jContainer[Graph Database<br>Neo4j]
        RocketMQContainer[Message Queue<br>Apache RocketMQ]
        DocStorageContainer[Generated Documentation Storage<br>File System Volume]
        WebServerContainer[Web Server<br>Embedded]

        UserInterface -- HTTP/S --> WebServerContainer
        WebServerContainer -- Serves UI & Docs --> UserInterface
        WebServerContainer -- Proxies API Calls --> BackendAppContainer
        BackendAppContainer -- Reads/Writes --> MySQLContainer
        BackendAppContainer -- Reads/Writes --> RedisContainer
        BackendAppContainer -- Reads/Writes --> Neo4jContainer
        BackendAppContainer -- Publishes/Consumes --> RocketMQContainer
        BackendAppContainer -- Writes HTML --> DocStorageContainer
        WebServerContainer -- Reads HTML --> DocStorageContainer
    end

    %% External Interactions (already shown in context, but can be re-emphasized)
    ExtGit[Git Repositories]
    ExtOpenRouter[OpenRouter API]
    ExtConfigFiles[Configuration Files]

    BackendAppContainer -- Clones/Pulls Code --> ExtGit
    BackendAppContainer -- LLM Analysis --> ExtOpenRouter
    BackendAppContainer -- Reads Config --> ExtConfigFiles

    %% Link UserInterface to FrontendAppContainer if shown as separate from boundary
    %% UserInterface --> FrontendAppContainer

```
*(注：此Mermaid图简化了前端在浏览器中运行的事实，并将其与Web服务器关联以表示服务关系。)*

#### 1.3 Level 3: 组件图 (Components within `Backend API Application`)

`Backend API Application (Spring Boot Modular Monolith)` 内部将按照DDD分层思想和功能内聚性划分为以下主要组件/模块：

1.  **`1. Presentation Layer (接口层 - Facade/Controllers)`**:
    * **`ProjectController`**: 处理项目注册、列表查询、详情获取、触发分析等HTTP请求。
    * **`TaskStatusController`**: 提供查询任务状态的API端点。
    * **`DocViewController` (可选)**: 如果文档不是完全由Web服务器通过路径直接提供，而是需要后端做一些逻辑（如版本重定向、权限检查（未来）），则可能需要此组件。MVP阶段，鉴于HTML已生成到目录，此组件可能较轻或不存在，主要由Web服务器负责。
    * **职责**: 暴露RESTful API (遵循 `api-spec.yaml`)，接收前端请求，进行初步校验，将请求委派给应用服务层。MVP阶段无用户认证逻辑。

2.  **`2. Application Layer (应用服务层)`**:
    * **`ProjectApplicationService`**: 编排项目创建、查询、更新（MVP只涉及基础信息）等用例。与`ProjectRepository`交互。在收到分析请求时，发布一个如`CodeAnalysisInitiatedEvent`到RocketMQ。
    * **`TaskQueryService`**: 提供查询分析任务状态和历史的服务。与`AnalysisTaskRepository`和可能的`TaskStatusCache`交互。
    * **`DocGenerationOrchestrationService` (逻辑概念)**: 虽然具体步骤由MQ消费者解耦，但应用层可能需要一个服务来发起整个文档生成流程的第一个消息，或对整个流程的状态进行高级别跟踪。
    * **职责**: 实现具体业务用例，协调领域对象和基础设施服务，管理事务边界（如果跨多个领域对象的操作需要）。

3.  **`3. Domain Layer (领域层)`**:
    * **`Project` (Aggregate Root)**: 包含项目ID、名称、Git仓库URL、默认分支、关联的文档版本等核心信息和业务规则。
    * **`AnalysisTask` (Aggregate Root)**: 表示一次完整的代码分析和文档生成任务。包含任务ID、项目ID、当前状态（如 `PENDING`, `CLONING`, `AST_PARSING`, `LLM_ANALYZING`, `HTML_RENDERING`, `COMPLETED`, `FAILED`）、各阶段子任务状态、错误信息、时间戳等。
    * **`CodeStructureSnapshot` (Entity/Value Object)**: 代表特定commit ID的代码AST解析结果的快照或其在Neo4j中的引用。
    * **`GeneratedDocumentContent` (Entity/Value Object)**: 代表LLM生成的Markdown内容或其引用。
    * **`DocumentVersion` (Entity)**: 关联项目、Commit ID、生成的HTML文档的存储路径、生成时间等。
    * **Domain Services**: 如 `ProjectDomainService` (处理复杂项目相关的业务规则，MVP阶段可能较简单)。
    * **Domain Events**: 例如 `ProjectRegisteredEvent`, `CodeAnalysisFailedEvent`, `HtmlRenderedSuccessfullyEvent`。这些事件可以被应用层捕获或直接发布到消息队列。
    * **职责**: 包含核心业务逻辑、实体、值对象、聚合、领域服务和仓储接口定义。

4.  **`4. Infrastructure Layer (基础设施层)`**:
    * **`a. Git Adapter`**:
        * **`GitServiceImpl`**: 实现从Git仓库克隆和拉取代码的逻辑。使用`CredentialService`获取解密的Git凭证。
    * **`b. Code Analysis Pipeline Components (RocketMQ Consumers/Producers)`**:
        * **`CodeCloneRequestConsumer`**: 监听 "请求代码分析" 消息，调用`GitServiceImpl`克隆代码，成功后发布 `CodeClonedEvent`（含代码本地路径和任务ID）。
        * **`ASTParsingRequestConsumer`**: 监听 `CodeClonedEvent`，针对Java代码调用JavaParser进行AST分析，将结果（或其摘要/指针）存入Neo4j（通过`CodeStructureRepository`），成功后发布 `ASTParsedEvent`（含任务ID和AST数据引用）。
        * **`LLMAnalysisRequestConsumer`**: 监听 `ASTParsedEvent`，从Neo4j或消息中获取AST信息，结合从`PromptService`获取的预设提示词，调用`OpenRouterIntegrationService`与OpenRouter API交互，获取LLM生成的Markdown文档内容，成功后发布 `MarkdownGeneratedEvent`（含任务ID和Markdown内容或其引用）。
        * **`HTMLRenderingRequestConsumer`**: 监听 `MarkdownGeneratedEvent`，获取Markdown内容，使用Thymeleaf模板引擎将其渲染为HTML，并将HTML文件保存到配置的输出目录（通过`FileSystemAdapter`），成功后发布 `HTMLPublishedEvent`（含任务ID、HTML文件路径、Commit ID等）。
        * **`TaskStatusUpdaterConsumers`**: 监听各个阶段成功或失败的事件，更新`AnalysisTask`在MySQL中的状态。
    * **`c. LLM Integration`**:
        * **`OpenRouterIntegrationService`**: 封装与OpenRouter API的交互细节，包括构建请求、处理响应、API Key管理（通过`CredentialService`）、错误处理、重试（按最佳实践，如指数退避）、超时控制。
        * **`PromptService`**: 从配置文件加载和管理预设的LLM提示词。
    * **`d. Persistence Adapters (Repositories)`**:
        * **`ProjectRepositoryImpl (JPA/MyBatis)`**: 实现`Project`聚合的数据库操作。
        * **`AnalysisTaskRepositoryImpl (JPA/MyBatis)`**: 实现`AnalysisTask`聚合的数据库操作。
        * **`CodeStructureRepositoryImpl (Neo4j Client)`**: 实现代码结构图谱在Neo4j中的存储和查询。
        * **`DocumentVersionRepositoryImpl (JPA/MyBatis)`**: 实现文档版本元数据的数据库操作。
    * **`e. Message Queue Adapters (RocketMQ)`**:
        * **`RocketMQEventPublisher`**: 统一的事件/消息发布服务。
        * 上述各Pipeline Consumers。
    * **`f. File System Adapter`**:
        * **`LocalFileSystemAdapter`**: 负责将生成的HTML文件写入本地（或挂载的Docker Volume）文件系统的指定目录。
    * **`g. Security Utilities`**:
        * **`CredentialServiceImpl`**: 负责从配置文件加载并AES解密Git Token和OpenRouter API Key。密钥管理需考虑安全（例如，主密钥不硬编码，通过环境变量等注入）。
    * **`h. Cache Adapters (Redis)`**:
        * **`RedisCacheManager`**: 提供通用的缓存读写能力。
    * **职责**: 实现领域层定义的接口，与外部系统（数据库、消息队列、外部API、文件系统）交互，提供技术支持能力。

5.  **`5. Cross-Cutting Concerns (横切关注点)`**:
    * **`Logging`**: 使用SLF4j + Logback/Log4j2进行结构化日志记录。
    * **`Exception Handling`**: 全局异常处理器，统一API错误响应格式。
    * **`Configuration Management`**: Spring Boot的配置管理机制（`application.properties/.yml`）。
    * **`Metrics & Monitoring (Future)`**: 集成Micrometer等库，暴露应用指标（MVP阶段可能仅依赖日志）。

此C4模型（特别是组件层面）清晰地展示了方案B如何通过事件驱动和消息队列实现核心流程的解耦，每个消费者组件专注于其特定阶段的处理，并通过发布新事件来驱动流程的下一步。

### 2. 技术适配性、优势、风险、扩展性、运维、安全 (后续详细展开)

基于上述C4模型，我们可以进一步详细分析方案B在以下方面的特性：

* **技术适配性与领域匹配度**：Spring Boot的模块化能力、RocketMQ的异步解耦、Neo4j对代码结构的表达、LLM在内容生成方面的潜力，都与ArchScope的需求高度契合。
* **关键技术优势**：
    * **解耦与弹性**：各分析阶段（克隆、AST、LLM、渲染）独立，单个阶段失败可独立重试，不影响其他已完成或待处理阶段，提高了整体流程的健壮性。这正是您所看重的优点。
    * **可维护性**：各阶段逻辑分离，代码更易于理解、修改和测试。
    * **可观测性（潜力）**：可以针对每个消息队列和消费者进行独立的监控和告警。
* **潜在技术风险与实施挑战**：
    * **消息一致性与顺序**：虽然RocketMQ支持顺序消息，但需仔细设计以确保复杂依赖下的处理顺序，或设计幂等消费者处理乱序/重复消息。
    * **分布式事务（若需要）**：跨多个消息和数据库操作的强一致性事务难以实现，需接受最终一致性或设计补偿事务。MVP阶段应尽量避免此类复杂性。
    * **监控与调试复杂度**：分布式异步流程的端到端追踪和问题定位比同步流程更复杂，需要良好的日志和（未来）分布式追踪支持。
    * **OpenRouter API依赖**：外部API的稳定性、性能、费用、模型效果变化是主要风险。健壮的错误处理、重试、超时、熔断（未来）和可能的降级策略（如LLM不可用时仅用AST生成基础文档）是必要的。
* **扩展能力评估**：
    * **水平扩展**：可以通过增加RocketMQ消费者实例来扩展特定处理阶段的吞吐能力（如LLM分析阶段可能较慢，可增加其消费者数量）。Backend API应用本身也可以水平扩展。
    * **垂直扩展**：可以为数据库、消息队列等基础设施分配更多资源。
* **运维复杂度与故障恢复**：
    * **运维**：需要维护Spring Boot应用、MySQL、Redis、Neo4j、RocketMQ等多个组件。Docker Compose简化了MVP的本地部署。
    * **故障恢复**：RocketMQ的重试队列和死信队列机制为任务阶段的自动和手动恢复提供了支持。数据库备份和恢复是标准操作。
* **安全架构**：
    * **凭证管理**：AES加密存储Git和OpenRouter凭证，密钥管理是关键。
    * **API安全 (ArchScope自身API)**：MVP阶段无用户认证，但API应设计为未来可扩展支持认证授权。应防止常见Web漏洞（OWASP Top 10）。
    * **数据保护**：用户代码在处理过程中需妥善管理，避免泄露。LLM交互可能涉及代码片段传输，需关注OpenRouter的数据隐私政策。生成的文档存储也需考虑访问控制（MVP阶段可能较简单，依赖Web服务器配置）。

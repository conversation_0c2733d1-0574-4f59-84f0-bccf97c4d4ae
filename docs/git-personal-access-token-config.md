# Git个人访问令牌配置指南

## 概述

ArchScope支持为不同的Git服务器配置个人访问令牌(Personal Access Token, PAT)，以便访问私有仓库。系统会自动使用配置的令牌进行认证，无需在每次操作时手动输入凭据。

## 支持的Git服务器

- **GitHub** (github.com)
- **GitLab** (gitlab.com, 私有GitLab实例)
- **Gitee** (gitee.com)
- **Bitbucket** (bitbucket.org)
- **其他标准Git服务器**

## 配置方式

### 1. 在application.yml中配置

```yaml
archscope:
  git:
    servers:
      hosts:
        # GitHub配置示例
        github.com:
          supportsHttps: true
          supportsHttp: true
          suggestHttpsOnSshFailure: true
          personalAccessToken: "ghp_xxxxxxxxxxxxxxxxxxxx"  # GitHub个人访问令牌
          tokenUsername: "token"  # GitHub使用token作为用户名

        # GitLab.com配置示例
        gitlab.com:
          supportsHttps: true
          supportsHttp: true
          suggestHttpsOnSshFailure: true
          personalAccessToken: "glpat-xxxxxxxxxxxxxxxxxxxx"  # GitLab个人访问令牌
          tokenUsername: "oauth2"  # GitLab推荐使用oauth2作为用户名

        # 私有GitLab实例配置示例
        gitlab.yeepay.com:
          supportsHttps: true
          supportsHttp: false
          suggestHttpsOnSshFailure: false
          personalAccessToken: "**************************"  # 个人访问令牌
          tokenUsername: "oauth2"  # GitLab推荐使用oauth2作为用户名
```

### 2. 环境变量配置

也可以通过环境变量进行配置：

```bash
# GitHub
export ARCHSCOPE_GIT_SERVERS_HOSTS_GITHUB_COM_PERSONALACCESSTOKEN=ghp_xxxxxxxxxxxxxxxxxxxx
export ARCHSCOPE_GIT_SERVERS_HOSTS_GITHUB_COM_TOKENUSERNAME=token

# GitLab
export ARCHSCOPE_GIT_SERVERS_HOSTS_GITLAB_COM_PERSONALACCESSTOKEN=glpat-xxxxxxxxxxxxxxxxxxxx
export ARCHSCOPE_GIT_SERVERS_HOSTS_GITLAB_COM_TOKENUSERNAME=oauth2

# 私有GitLab实例（注意点号需要转换为下划线）
export ARCHSCOPE_GIT_SERVERS_HOSTS_GITLAB_YEEPAY_COM_PERSONALACCESSTOKEN=**************************
export ARCHSCOPE_GIT_SERVERS_HOSTS_GITLAB_YEEPAY_COM_TOKENUSERNAME=oauth2
```

## 获取个人访问令牌

### GitHub

1. 登录GitHub，进入 Settings > Developer settings > Personal access tokens
2. 点击 "Generate new token"
3. 选择所需的权限范围（通常需要 `repo` 权限）
4. 生成令牌，格式为 `ghp_xxxxxxxxxxxxxxxxxxxx`

### GitLab

1. 登录GitLab，进入 User Settings > Access Tokens
2. 创建新的访问令牌
3. 选择所需的权限范围（通常需要 `read_repository` 或 `api` 权限）
4. 生成令牌，格式为 `glpat-xxxxxxxxxxxxxxxxxxxx`

### Gitee

1. 登录Gitee，进入 设置 > 私人令牌
2. 生成新令牌
3. 选择所需的权限范围

## 认证优先级

系统按以下优先级使用认证信息：

1. **API请求中提供的用户名密码** - 最高优先级
2. **配置文件中的个人访问令牌** - 中等优先级
3. **匿名访问** - 最低优先级（仅适用于公开仓库）

## 配置参数说明

| 参数 | 说明 | 示例值 |
|------|------|--------|
| `personalAccessToken` | 个人访问令牌 | `**************************` |
| `tokenUsername` | 令牌对应的用户名 | `oauth2` (GitLab), `token` (GitHub) |
| `supportsHttps` | 是否支持HTTPS协议 | `true` |
| `supportsHttp` | 是否支持HTTP协议 | `false` |
| `suggestHttpsOnSshFailure` | SSH失败时是否建议HTTPS | `true` |

## 安全注意事项

1. **不要在代码中硬编码令牌** - 使用配置文件或环境变量
2. **定期轮换令牌** - 建议定期更新个人访问令牌
3. **最小权限原则** - 只授予必要的权限
4. **保护配置文件** - 确保包含令牌的配置文件不被泄露

## 故障排除

### 常见问题

1. **认证失败**
   - 检查令牌是否正确
   - 确认令牌权限是否足够
   - 验证令牌是否已过期

2. **配置不生效**
   - 检查配置文件语法是否正确
   - 确认服务器主机名是否匹配
   - 重启应用以加载新配置

3. **SSH URL无法访问**
   - 系统优先支持HTTPS认证
   - 建议使用HTTPS URL替代SSH URL

### 日志查看

系统会在日志中记录认证相关信息：

```
INFO  - 使用配置的个人访问令牌进行认证: host=gitlab.yeepay.com, tokenUsername=oauth2
INFO  - 使用请求中提供的认证信息: username=testuser
INFO  - 未找到认证信息，尝试匿名访问: https://github.com/public/repo.git
```

## 示例用法

配置完成后，系统会自动使用配置的令牌访问私有仓库：

```java
// API调用示例
GitRepositoryValidationRequest request = GitRepositoryValidationRequest.builder()
    .repositoryUrl("https://gitlab.yeepay.com/private/repo.git")
    .fetchDetails(true)
    .build();

// 系统会自动使用配置的个人访问令牌进行认证
GitRepositoryInfo result = gitRepositoryMetadataService.validateAndFetchRepositoryInfo(request);
```

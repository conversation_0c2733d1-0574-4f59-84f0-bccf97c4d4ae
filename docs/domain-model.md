# ArchScope 领域模型 (Domain Model)

## 1. 引言

本文档描述了ArchScope系统的核心领域模型。领域模型是系统业务逻辑和数据结构的抽象表示，旨在捕获关键业务概念、它们之间的关系以及它们所遵循的规则。本模型将作为后续数据库设计、服务设计和代码实现的重要依据。

我们将采用领域驱动设计 (DDD) 的一些基本概念，如聚合（Aggregate）、实体（Entity）、值对象（Value Object）和领域服务（Domain Service）。

## 2. 核心领域与子域

ArchScope的核心领域围绕 **项目架构的观测与守护** 展开。可以划分为以下主要子域：

* **项目管理 (Project Management Subdomain):** 负责项目的注册、配置和生命周期管理。
* **代码分析 (Code Analysis Subdomain):** 负责代码仓库的获取、解析（AST、LLM）、结构与依赖提取。
* **文档管理 (Documentation Management Subdomain):** 负责从分析结果生成文档、文档版本控制、模板管理和文档网站生成。
* **任务调度 (Task Scheduling Subdomain):** 负责管理和执行所有后台异步任务。
* **用户与认证 (User & Auth Subdomain):** 负责用户身份识别和访问控制（依赖SSO）。
* **健康度评估 (Health Assessment Subdomain):** (MVP后) 负责项目质量指标计算和评估。
* **系统配置 (System Configuration Subdomain):** (MVP后 Admin功能) 负责系统级设置。

## 3. 核心领域对象（聚合、实体、值对象）

以下是各子域中的核心领域对象：

### 3.1 项目管理子域 (Project Management Subdomain)

#### 3.1.1 Project (聚合根 - Aggregate Root, 实体 - Entity)
代表用户在ArchScope中注册和管理的一个代码项目。

* **属性 (Attributes):**
    * `id`: Long (数字ID，唯一标识符，聚合根ID，自增主键)
    * `name`: String (项目名称，用户定义)
    * `description`: String (可选，项目描述)
    * `repositoryUrl`: String (Git仓库URL)
    * `normalizedRepositoryUrl`: String (标准化的仓库URL，用于重复检测)
    * `branch`: String (代码仓库的默认分支)
    * `creatorId`: Long (项目创建者ID)
    * `status`: String (项目状态：PENDING, ANALYZING, COMPLETED, FAILED)
    * `active`: Boolean (是否激活)
    * `type`: String (项目类型：JAVA, JAVASCRIPT, PYTHON等)
    * `createdAt`: LocalDateTime
    * `updatedAt`: LocalDateTime
    * `lastAnalyzedAt`: LocalDateTime (可选, 上次分析的时间)

* **行为 (Behaviors - 部分示例):**
    * `registerNewProject(name, repoInfo, ownerUserId)`: 创建项目。
    * `updateDetails(name, description)`: 更新项目基本信息。
    * `updateRepositoryInfo(repoInfo)`: 更新仓库信息。
    * `updateSettings(newSettings)`: 更新项目配置。
    * `archiveProject()`: 归档项目。
    * `triggerAnalysis(commitId, analysisType)`: 触发一次新的代码分析。
    * `canBeAccessedBy(ssoUserInfo)`: 判断用户是否有权限访问。

* **关联 (Relationships):**
    * `HAS_MANY` `AnalysisRun` (实体，记录历次分析运行)
    * `HAS_MANY` `DocumentSet` (实体，代表某个commit的文档集)
    * `HAS_MANY` `ProjectSsoMemberMapping` (实体，如果需要独立于SSO管理项目级额外角色或权限) - **MVP阶段可能简化，主要依赖SSO组**

#### 3.1.2 RepositoryInfo (值对象 - Value Object)
描述代码仓库的详细信息。

* **属性:**
    * `url`: String (仓库的克隆URL, e.g., "https://github.com/org/repo.git")
    * `type`: Enum (GITHUB, GITLAB, BITBUCKET, OTHER_GIT)
    * `credentialsId`: String (可选, 指向安全存储的访问凭证ID，如Git Token)

#### 3.1.3 ProjectSettings (值对象 - Value Object)
项目的特定配置。

* **属性:**
    * `supportedLanguage`: Enum (JAVA - **MVP仅Java**, PYTHON, JAVASCRIPT, etc.)
    * `analysisSchedule`: Enum (MANUAL, ON_COMMIT_WEBHOOK, DAILY, WEEKLY) - **MVP为MANUAL**
    * `llmConfiguration`: LLMConfiguration (值对象，LLM相关配置) - **MVP简化**
    * `notificationPreferences`: NotificationPreferences (值对象) - **MVP简化**
    * `excludedPaths`: List<String> (解析时排除的路径)

### 3.2 代码分析子域 (Code Analysis Subdomain)

#### 3.2.1 AnalysisRun (实体 - Entity)
代表一次完整的代码分析执行过程，通常与一个`Task`关联。

* **属性:**
    * `analysisRunId`: String (UUID, 唯一标识符)
    * `projectId`: String (关联的Project ID)
    * `commitId`: String (本次分析针对的代码提交SHA)
    * `analysisType`: Enum (FULL, INCREMENTAL)
    * `status`: Enum (PENDING, RUNNING, COMPLETED_SUCCESS, COMPLETED_WITH_ERRORS, FAILED)
    * `triggeredAt`: DateTime
    * `startedAt`: DateTime (可选)
    * `finishedAt`: DateTime (可选)
    * `errorMessage`: String (可选, 如果失败)
    * `analysisResultPointer`: String (指向代码知识图谱中本次分析结果的入口或标识)
    * `llmUsageInfo`: LLMUsageInfo (值对象, LLM调用统计) - **MVP简化**

* **关联:**
    * `BELONGS_TO` `Project`
    * `GENERATES` `CodeKnowledgeGraphSnapshot` (概念上的，实际存储在图数据库中，这里是指向该快照的引用)

#### 3.2.2 CodeKnowledgeGraph (概念模型 - 存储于图数据库)
这是代码分析的核心产出，以图的形式存储。

* **节点类型 (Node Types - 示例，MVP针对Java):**
    * `KG_Project`: (projectId, name)
    * `KG_Commit`: (commitId, timestamp, author)
    * `KG_File`: (filePath, language, commitId)
    * `KG_Package` (Java): (packageName, filePath)
    * `KG_Class` (Java): (fullyQualifiedName, name, visibility, isInterface, isAbstract, filePath, startLine, endLine)
    * `KG_Method` (Java): (signature, name, visibility, returnType, parameters, classId, startLine, endLine, cyclomaticComplexity)
    * `KG_Field` (Java): (name, type, visibility, classId)
    * `KG_Annotation` (Java): (fullyQualifiedName, targetElementId)
    * `(后续)` `KG_Dependency`, `KG_DesignPatternInstance`, `KG_CodeSmell`

* **边类型 (Edge Types - 示例，MVP针对Java):**
    * `PROJECT_HAS_COMMIT`
    * `COMMIT_CONTAINS_FILE`
    * `FILE_IN_PACKAGE` (Java)
    * `FILE_DEFINES_CLASS` (Java)
    * `PACKAGE_CONTAINS_CLASS` (Java)
    * `CLASS_HAS_METHOD` (Java)
    * `CLASS_HAS_FIELD` (Java)
    * `CLASS_EXTENDS_CLASS` (Java)
    * `CLASS_IMPLEMENTS_INTERFACE` (Java)
    * `METHOD_CALLS_METHOD` (Java)
    * `METHOD_ACCESSES_FIELD` (Java)
    * `FIELD_HAS_TYPE` (指向类型对应的`KG_Class`或原始类型)
    * `ELEMENT_IS_ANNOTATED_BY`
    * `(后续)` `MODULE_DEPENDS_ON_MODULE`, `CLASS_USES_CLASS` (更泛化的依赖)

#### 3.2.3 LLMConfiguration (值对象 - Value Object) - MVP简化
LLM分析相关的配置。

* **属性:**
    * `provider`: Enum (OPENAI, AZURE_OPENAI, LOCAL_MODEL_X) - **MVP不强调**
    * `modelName`: String - **MVP不强调**
    * `promptSetId`: String (指向一组Prompt模板的ID) - **MVP不强调**

### 3.3 文档管理子域 (Documentation Management Subdomain)

#### 3.3.1 DocumentSet (聚合根 - Aggregate Root, 实体 - Entity)
代表一个特定代码版本（Commit）下生成的一整套文档。

* **属性:**
    * `documentSetId`: String (UUID, 唯一标识符)
    * `projectId`: String (关联的Project ID)
    * `commitId`: String (关联的代码提交SHA)
    * `generatedAt`: DateTime
    * `status`: Enum (GENERATING, AVAILABLE, SUPERSEDED, FAILED_TO_GENERATE)
    * `manifest`: DocumentManifest (值对象, 描述文档树和文件位置)
    * `templateSetUsed`: String (生成此文档集所使用的模板集ID)
    * `docSiteUrlPath`: String (此版本文档在文档网站上的相对URL路径)

* **行为:**
    * `generateFromAnalysis(analysisRun, templateSet)`: 根据分析结果和模板生成文档。
    * `publishToSite()`: 将文档发布到可访问的网站路径。
    * `compareWith(anotherDocumentSet)`: (行为可能在领域服务中) 比较两个文档集的差异。

* **关联:**
    * `BELONGS_TO` `Project`
    * `BASED_ON` `AnalysisRun` (或其结果)

#### 3.3.2 DocumentManifest (值对象 - Value Object)
描述一个`DocumentSet`中包含的文档结构和文件。

* **属性:**
    * `rootPath`: String (文档在存储中的根路径)
    * `treeStructure`: List<DocTreeNode> (文档的树形结构，包含文件名、路径、标题等)
    * `errorLogPath`: String (可选, 生成过程中的错误日志路径)

#### 3.3.3 DocTreeNode (值对象 - Value Object)
文档树中的一个节点，代表一个文件或目录。

* **属性:**
    * `name`: String (文件名或目录名)
    * `path`: String (相对于文档集根目录的路径)
    * `type`: Enum (FILE, DIRECTORY)
    * `title`: String (可选, 从Markdown H1或frontmatter提取的标题)
    * `children`: List<DocTreeNode> (如果是目录，其子节点)

#### 3.3.4 DocTemplate (实体 - Entity) - MVP后
用户可定义的文档模板。

* **属性:**
    * `templateId`: String (UUID)
    * `name`: String (模板名称)
    * `description`: String (可选)
    * `content`: String (Thymeleaf模板内容)
    * `type`: Enum (PROJECT_OVERVIEW, C4_CONTEXT, MODULE_API_DOCS, etc.)
    * `languageScope`: Enum (JAVA, PYTHON, ALL) - **MVP主要考虑Java**
    * `isDefault`: Boolean

#### 3.3.5 SystemErrorCodeGuide (概念实体 - MVP为静态内容)
错误码和解决方案指南。

* **属性 (如果动态管理):**
    * `errorCode`: String (如 "ANALYSIS_GIT_AUTH_FAILED")
    * `title`: String (简短描述)
    * `description`: String (详细说明)
    * `possibleCauses`: List<String>
    * `suggestedSolutions`: List<String> (步骤化)
    * `relatedDocumentationLink`: String (可选)
* **MVP阶段:** 这部分内容可能是预置在系统中的静态Markdown文件，通过`FR-DOCSITE-008`提供查看页面。

### 3.4 任务调度子域 (Task Scheduling Subdomain)

#### 3.4.1 Task (聚合根 - Aggregate Root, 实体 - Entity)
代表一个需要在后台异步执行的工作单元。

* **属性 (参照 `prd.md` 和 `api-spec.yaml` 中的 `TaskDetailDTO`):**
    * `taskId`: String (UUID, 唯一标识符)
    * `projectId`: String (可选, 关联的项目ID)
    * `taskType`: Enum (FULL_CODE_PARSE, INCREMENTAL_CODE_PARSE, DOC_MARKDOWN_GEN, DOC_SITE_GEN, HEALTH_ASSESSMENT, etc.)
    * `status`: Enum (QUEUED, PENDING, RUNNING, SUCCESS, FAILED_TRANSIENT, FAILED_PERMANENT, RETRYING_DELAYED, CANCELLED, TIMED_OUT)
    * `priority`: Integer (应用层优先级) - **MVP简化**
    * `payload`: JSON String (任务输入参数)
    * `rocketMQMessageId`: String (可选)
    * `createdAt`: DateTime
    * `queuedAt`: DateTime (可选)
    * `startedAt`: DateTime (可选)
    * `finishedAt`: DateTime (可选)
    * `retryCount`: Integer
    * `maxRetries`: Integer
    * `lastErrorMessage`: String (可选)
    * `resultSummary`: JSON String (可选, 任务结果摘要或指针)
    * `triggeredBy`: TriggerInfo (值对象)

* **行为:**
    * `queue()`: 将任务放入消息队列。
    * `startExecution()`: 标记任务开始执行。
    * `completeSuccessfully(resultSummary)`: 标记任务成功。
    * `fail(errorMessage, isRetryable)`: 标记任务失败。
    * `scheduleRetry(delay)`: 安排重试。
    * `cancel()`: 请求取消任务。

#### 3.4.2 TriggerInfo (值对象 - Value Object)
任务的触发信息。

* **属性:**
    * `type`: Enum (MANUAL_UI, WEBHOOK_GIT, SCHEDULED_SYSTEM, CHAINED_TASK)
    * `ssoUserId`: String (可选, 如果是用户手动触发)
    * `sourceEventId`: String (可选, 如Webhook事件ID)

### 3.5 用户与认证子域 (User & Auth Subdomain)

#### 3.5.1 AuthenticatedUser (概念实体 - 信息来自SSO)
代表通过SSO成功认证的用户在ArchScope上下文中的信息。

* **属性:**
    * `ssoUserId`: String (来自SSO的用户唯一标识)
    * `username`: String (来自SSO)
    * `email`: String (来自SSO)
    * `ssoGroupsOrRoles`: List<String> (用户在SSO系统中的组或角色列表)
    * `archScopeRole`: Enum (ADMIN, USER, GUEST - 根据SSO信息映射得到)

* **行为 (可能在领域服务中实现):**
    * `hasPermissionForProject(projectId, requiredPermissionLevel)`
    * `isAdmin()`

---
## 4. 领域服务 (Domain Services) - 部分示例

* **ProjectRegistrationService:** 处理新项目的注册逻辑，包括创建`Project`实体，初始化`ProjectSettings`，触发首次`AnalysisRun`任务。
* **CodeAnalysisOrchestrationService:** 协调代码克隆、AST解析、LLM分析（如果启用）的流程，管理`AnalysisRun`状态，并将结果存入代码知识图谱。
* **DocumentationPipelineService:** 负责从`AnalysisRun`结果和`DocTemplate`生成`DocumentSet`，并触发`DocSiteGenTask`。
* **TaskExecutionService (抽象概念，具体由各Worker实现):** 负责实际执行特定类型的`Task`，如`JavaAstParsingService`。
* **AccessControlService:** (依赖SSO) 检查`AuthenticatedUser`是否有权执行特定操作或访问特定资源。

## 5. 领域事件 (Domain Events) - 部分示例

* `ProjectRegisteredEvent`: (projectId, ownerUserId, repoUrl)
* `CodeAnalysisCompletedEvent`: (analysisRunId, projectId, commitId, status, resultPointer)
* `DocumentSetGeneratedEvent`: (documentSetId, projectId, commitId, docSiteUrlPath)
* `TaskStatusChangedEvent`: (taskId, newStatus, oldStatus)
* `ProjectHealthCalculatedEvent`: (projectId, reportId, overallRating) (MVP后)

## 6. 实体关系图 (ERD - Mermaid 语法)

这是一个简化的、高层次的实体关系图，主要展示核心实体及其关系。代码知识图谱 (KG) 的内部结构更为复杂，适合用图数据库原生表示。

```mermaid
erDiagram
    User {
        string ssoUserId PK "SSO User ID"
        string username
        string email
        string archScopeRole "Mapped Role (ADMIN, USER)"
        string ssoGroups "List of SSO Groups/Roles"
    }

    Project {
        bigint id PK "Project ID (Auto Increment)"
        string name
        string description
        string repositoryUrl
        string normalizedRepositoryUrl
        string branch "Default Branch"
        bigint creatorId FK "Creator User ID"
        string status "Enum (PENDING, ANALYZING, COMPLETED, FAILED)"
        boolean active
        string type "Enum (JAVA, JAVASCRIPT, PYTHON, etc.)"
        datetime createdAt
        datetime updatedAt
        datetime lastAnalyzedAt
    }

    AnalysisRun {
        string analysisRunId PK "Analysis Run ID (UUID)"
        string projectId FK "Project ID"
        string commitId "Git Commit SHA"
        string analysisType "Enum (FULL, INCREMENTAL)"
        string status "Enum (PENDING, RUNNING, COMPLETED_SUCCESS, FAILED)"
        datetime triggeredAt
        datetime finishedAt
        string resultPointer "Ref to Graph DB data"
    }

    DocumentSet {
        string documentSetId PK "Document Set ID (UUID)"
        string projectId FK "Project ID"
        string commitId "Git Commit SHA"
        datetime generatedAt
        string status "Enum (GENERATING, AVAILABLE)"
        json manifest "DocumentManifest VO"
        string docSiteUrlPath
    }

    Task {
        string taskId PK "Task ID (UUID)"
        string projectId FK "Optional Project ID"
        string taskType "Enum (FULL_CODE_PARSE, DOC_MARKDOWN_GEN, etc.)"
        string status "Enum (QUEUED, RUNNING, SUCCESS, FAILED)"
        json payload "Task Input Parameters"
        datetime createdAt
        datetime finishedAt
        string resultSummary "JSON summary or pointer"
    }

    DocTemplate {
        string templateId PK "Template ID (UUID)"
        string name
        string type "Enum (PROJECT_OVERVIEW, C4_CONTEXT)"
        string content "Thymeleaf template text"
        string languageScope "Enum (JAVA, ALL)"
    }

    Project ||--o{ AnalysisRun : "has many"
    Project ||--o{ DocumentSet : "generates for commits of"
    AnalysisRun }o--|| Task : "is executed as a"
    DocumentSet }o--|| AnalysisRun : "is based on"
    DocumentSet ||--o{ DocTemplate : "uses" (conceptually, templateId stored)

    %% User-Project relationship is more about access control via SSO,
    %% direct ERD link might be via a ProjectMemberMapping if explicit ArchScope roles are needed beyond SSO.
    %% For MVP, Project.ownerUserId links to the creator.
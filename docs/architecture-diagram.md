# ArchScope 系统架构图

## DDD六边形架构图

```mermaid
graph TB
    %% 外部系统
    User[👤 用户]
    Admin[👨‍💼 管理员]
    Git[🔗 Git仓库<br/>GitHub/GitLab]
    OpenRouter[🤖 OpenRouter<br/>LLM服务]
    SSO[🔐 SSO身份提供商]

    %% 前端
    Frontend[🌐 前端SPA<br/>Vue 3 + TypeScript]

    %% 主应用容器
    subgraph "ArchScope Backend Application"
        %% 接口层
        subgraph "arch-scope-facade (接口层)"
            WebAPI[🔌 Web API<br/>REST接口]
            DTO[📋 DTO对象]
        end

        %% 应用服务层
        subgraph "arch-scope-app (应用服务层)"
            ProjectApp[📁 项目应用服务]
            TaskApp[⚙️ 任务应用服务]
            AdminApp[👨‍💼 管理应用服务]
            Controller[🎮 控制器层]
        end

        %% 领域模型层 (核心)
        subgraph "arch-scope-domain (领域模型层) - 核心"
            Project[📦 Project聚合]
            Task[⚡ Task聚合]
            Service[🔧 Service聚合]
            DomainService[🏗️ 领域服务]
            Repository[📚 仓储接口]
        end

        %% 基础设施层
        subgraph "arch-scope-infrastructure (基础设施层)"
            %% 持久化适配器
            subgraph "持久化适配器"
                ProjectRepo[📁 项目仓储实现]
                TaskRepo[⚙️ 任务仓储实现]
                ServiceRepo[🔧 服务仓储实现]
            end

            %% 外部服务适配器
            subgraph "外部服务适配器"
                GitAdapter[🔗 Git适配器]
                LLMAdapter[🤖 LLM适配器]
                SSOAdapter[🔐 SSO适配器]
            end

            %% 消息处理
            subgraph "消息处理"
                MQProducer[📤 消息生产者]
                MQConsumer[📥 消息消费者]
                EventHandler[🎯 事件处理器]
            end
        end

        %% 主应用模块
        subgraph "arch-scope-main (启动模块)"
            MainApp[🚀 应用启动器]
            Config[⚙️ 全局配置]
        end
    end

    %% 数据存储
    subgraph "数据存储层"
        MySQL[(🗄️ MySQL<br/>关系数据)]
        Neo4j[(🕸️ Neo4j<br/>图数据库)]
        Redis[(⚡ Redis<br/>缓存)]
    end

    %% 消息队列
    RocketMQ[📨 RocketMQ<br/>消息队列]

    %% 静态文档服务
    DocSite[📄 静态文档站点<br/>Nginx]

    %% 连接关系
    User --> Frontend
    Admin --> Frontend
    Frontend --> WebAPI

    WebAPI --> Controller
    Controller --> ProjectApp
    Controller --> TaskApp
    Controller --> AdminApp

    ProjectApp --> Project
    TaskApp --> Task
    AdminApp --> Project

    Project --> DomainService
    Task --> DomainService
    Service --> DomainService

    DomainService --> Repository

    Repository -.-> ProjectRepo
    Repository -.-> TaskRepo
    Repository -.-> ServiceRepo

    ProjectRepo --> MySQL
    TaskRepo --> MySQL
    ServiceRepo --> Neo4j

    GitAdapter --> Git
    LLMAdapter --> OpenRouter
    SSOAdapter --> SSO

    MQProducer --> RocketMQ
    RocketMQ --> MQConsumer
    MQConsumer --> EventHandler

    EventHandler --> Task

    %% 缓存连接
    ProjectRepo -.-> Redis
    TaskRepo -.-> Redis

    %% 文档生成
    EventHandler -.-> DocSite

    %% 样式定义
    classDef external fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef frontend fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef facade fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef app fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef domain fill:#fff8e1,stroke:#f57f17,stroke-width:3px
    classDef infrastructure fill:#fce4ec,stroke:#880e4f,stroke-width:2px
    classDef storage fill:#f1f8e9,stroke:#33691e,stroke-width:2px
    classDef main fill:#e3f2fd,stroke:#0d47a1,stroke-width:2px

    class User,Admin,Git,OpenRouter,SSO external
    class Frontend frontend
    class WebAPI,DTO facade
    class ProjectApp,TaskApp,AdminApp,Controller app
    class Project,Task,Service,DomainService,Repository domain
    class ProjectRepo,TaskRepo,ServiceRepo,GitAdapter,LLMAdapter,SSOAdapter,MQProducer,MQConsumer,EventHandler infrastructure
    class MySQL,Neo4j,Redis,RocketMQ,DocSite storage
    class MainApp,Config main
```

## 架构层次说明

### 🎯 核心原则
- **DDD领域驱动设计**：以业务领域为核心进行建模
- **六边形架构**：应用核心与外部依赖通过端口解耦
- **依赖倒置**：高层模块不依赖底层模块，都依赖抽象

### 📦 模块职责

#### 1. arch-scope-facade (接口层)
- 定义与外部系统交互的接口和DTO
- 不包含业务逻辑，仅负责数据传输

#### 2. arch-scope-app (应用服务层)
- 编排领域对象完成业务用例
- 处理事务管理和DTO转换
- 适配外部HTTP请求

#### 3. arch-scope-domain (领域模型层) - 核心
- 包含实体、值对象、领域服务接口
- 封装核心业务逻辑和规则
- 定义仓储接口

#### 4. arch-scope-infrastructure (基础设施层)
- 实现仓储接口（MySQL, Neo4j, Redis）
- 集成外部服务（Git, OpenRouter, RocketMQ）
- 处理SSO认证信息

#### 5. arch-scope-main (主应用模块)
- 应用启动入口和全局配置
- 依赖注入配置

### 🔄 数据流向
1. **用户请求** → 前端SPA → Web API → 控制器
2. **业务处理** → 应用服务 → 领域模型 → 领域服务
3. **数据持久化** → 仓储接口 → 仓储实现 → 数据库
4. **异步处理** → 消息队列 → 事件处理器 → 业务逻辑

### 🏗️ 技术栈
- **前端**: Vue 3 + TypeScript + Tailwind CSS
- **后端**: Java + Spring Boot + DDD架构
- **数据库**: MySQL (关系数据) + Neo4j (图数据) + Redis (缓存)
- **消息队列**: RocketMQ
- **部署**: Kubernetes + Docker
# ArchScope 文档目录整改计划

## 📋 当前问题分析

### 1. 文件命名不一致
- **下划线命名**: `api_design.md`, `frontend_architecture.md`, `core_modules.md`
- **连字符命名**: `api-path-mapping.md`, `data-model.md`, `domain-model.md`
- **中文命名**: `提示词管理.md`
- **混合命名**: `ArchScope项目技术实施方案.md`

### 2. 文档分类不清晰
- **架构文档散落**: `architecture.md`, `frontend_architecture.md`, `architecture/`目录
- **API文档重复**: `api_design.md`, `api-spec.yaml`, `api-path-mapping.md`, `llm-integration-api.md`
- **规范文档混乱**: 部分在`module-specs/`目录，部分在根目录

### 3. 文档内容重复或过时
- **架构文档重复**: `architecture.md`与`frontend_architecture.md`有重叠
- **API设计重复**: `api_design.md`与`api-path-mapping.md`内容有重叠
- **计划文档过时**: `plan.md`可能已过时，需要更新或归档

### 4. 目录结构不够清晰
- 缺少明确的分类目录
- 技术文档、业务文档、规范文档混在一起
- 没有文档索引和导航

## 🎯 整改建议

### 1. 统一文件命名规范
**采用连字符命名（kebab-case）**，符合Web标准和Markdown最佳实践：

```
api_design.md → api-design.md
frontend_architecture.md → frontend-architecture.md
core_modules.md → core-modules.md
提示词管理.md → prompt-management.md
ArchScope项目技术实施方案.md → archscope-technical-implementation.md
```

### 2. 重新组织目录结构
建议的新目录结构：

```
docs/
├── README.md                           # 文档导航索引
├── architecture/                       # 架构设计文档
│   ├── overview.md                     # 架构总览（整合现有architecture.md）
│   ├── backend-architecture.md         # 后端架构
│   ├── frontend-architecture.md        # 前端架构
│   ├── data-model.md                   # 数据模型
│   ├── domain-model.md                 # 领域模型
│   └── component-interaction.md        # 组件交互
├── api/                                # API文档
│   ├── api-design.md                   # API设计规范
│   ├── api-spec.yaml                   # OpenAPI规范
│   ├── api-path-mapping.md             # API路径映射
│   └── llm-integration-api.md          # LLM集成API
├── specifications/                     # 规范文档
│   ├── field-naming-standards.md       # 字段命名规范
│   ├── enum-definitions-audit.md       # 枚举定义规范
│   ├── coding-standards.md             # 编码规范
│   └── module-specs/                   # 模块规范
│       ├── application-layer-spec.md
│       ├── domain-layer-spec.md
│       ├── infrastructure-layer-spec.md
│       └── interfaces-api-layer-spec.md
├── development/                        # 开发文档
│   ├── frontend-development.md         # 前端开发指南
│   ├── git-integration-guide.md        # Git集成指南
│   ├── llm-task-integration-guide.md   # LLM任务集成指南
│   └── prompt-management.md            # 提示词管理
├── deployment/                         # 部署文档
│   ├── technology-stack.md             # 技术栈
│   └── deployment-guide.md             # 部署指南
├── business/                           # 业务文档
│   ├── user-story.md                   # 用户故事
│   ├── qa.md                           # 问答文档
│   └── risk-assessment.md              # 风险评估
├── research/                           # 研究报告
│   ├── deep-research-report.md         # 深度研究报告
│   └── project-analysis-optimization.md # 项目分析优化
├── adrs/                               # 架构决策记录
│   ├── adr-template.md
│   ├── adr-001-backend-architecture-choice.md
│   └── ...
├── prototype/                          # 界面原型
│   ├── README.md
│   ├── images/
│   └── *.html
├── audit/                              # 审查报告
│   ├── consistency-audit-report.md     # 一致性审查报告
│   └── security-audit-report.md        # 安全审查报告（如需要）
└── archive/                            # 归档文档
    ├── plan.md                         # 项目计划（已过时）
    └── legacy-docs/                    # 其他过时文档
```

### 3. 文档内容整合建议

#### 3.1 架构文档整合
- **合并**: 将`architecture.md`和`frontend_architecture.md`整合为`architecture/`目录下的专门文档
- **拆分**: 将大型架构文档按模块拆分为更易维护的小文档

#### 3.2 API文档整合
- **保留**: `api-spec.yaml`作为标准OpenAPI规范
- **整合**: 将`api_design.md`和`api-path-mapping.md`的内容整合，避免重复
- **专门化**: LLM相关API文档独立维护

#### 3.3 规范文档集中
- **集中**: 将所有编码规范、命名规范、模块规范集中到`specifications/`目录
- **标准化**: 统一规范文档的格式和结构

### 4. 创建文档导航
创建`docs/README.md`作为文档导航索引，包含：
- 文档分类说明
- 快速导航链接
- 文档维护说明
- 贡献指南

## 📅 实施计划

### 阶段1: 目录结构调整（1-2天）
1. 创建新的目录结构
2. 移动现有文档到对应目录
3. 重命名文件以符合命名规范

### 阶段2: 文档内容整合（3-5天）
1. 整合重复的架构文档
2. 整合重复的API文档
3. 更新文档内部链接

### 阶段3: 文档完善（2-3天）
1. 创建文档导航索引
2. 更新过时内容
3. 统一文档格式

### 阶段4: 验证和优化（1天）
1. 检查所有链接是否正确
2. 验证文档结构的合理性
3. 收集反馈并优化

## ✅ 预期效果

1. **提高可维护性**: 清晰的目录结构便于文档维护
2. **减少重复**: 消除重复内容，避免信息不一致
3. **提升可读性**: 统一的命名和格式提高阅读体验
4. **便于导航**: 清晰的分类和索引便于快速找到所需文档
5. **支持协作**: 标准化的结构便于团队协作维护

## 🚨 注意事项

1. **保持向后兼容**: 在移动文档时，考虑现有链接的影响
2. **逐步迁移**: 避免一次性大规模变更，采用渐进式迁移
3. **更新引用**: 及时更新代码中对文档的引用路径
4. **团队沟通**: 确保团队成员了解新的文档结构

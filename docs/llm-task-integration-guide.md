# ArchScope LLM任务集成指南

## 概述

本文档描述了如何与ArchScope系统集成，实现LLM服务的任务拉取和结果提交功能。ArchScope采用异步任务处理模式，LLM服务通过标准化API接口获取待处理任务并提交处理结果。

## 核心概念

### 任务生命周期
- **PENDING**: 任务已创建，等待LLM服务拉取
- **PROCESSING**: 任务已被LLM服务拉取，正在处理中
- **COMPLETED**: 任务处理完成，结果已提交
- **FAILED**: 任务处理失败
- **TIMEOUT**: 任务处理超时（30分钟后自动重置为PENDING）

### 工作节点类型
- `CODE_ANALYSIS`: 代码分析任务
- `DOC_GENERATION`: 文档生成任务
- `CUSTOM`: 自定义任务类型

## API接口规范

### 1. 任务拉取接口

**接口地址**: `POST /api/v1/llm-tasks/pull`

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
  "workerId": "llm-worker-node-01",
  "workerVersion": "1.0.0",
  "supportedTaskTypes": ["CODE_FULL_ANALYSIS_JAVA", "DOC_SITE_GENERATION_JAVA"],
  "maxConcurrentTasks": 3
}
```

**请求参数说明**:
- `workerId` (必填): 工作节点唯一标识符
- `workerVersion` (可选): 工作节点版本号
- `supportedTaskTypes` (可选): 支持的任务类型列表
- `maxConcurrentTasks` (可选): 最大并发任务数

**成功响应** (有任务时):
```json
{
  "hasTask": true,
  "taskId": "12345",
  "projectId": "67890",
  "taskType": "CODE_FULL_ANALYSIS_JAVA",
  "priority": 5,
  "createdAt": "2025-06-21T10:30:00",
  "timeoutAt": "2025-06-21T11:00:00",
  "inputData": {
    "schemaVersion": "1.2",
    "repositoryInfo": {
      "cloneUrl": "https://github.com/example/project.git",
      "commitId": "a1b2c3d4e5f6789012345678901234567890abcd",
      "branchName": "main"
    }
  },
  "parameters": {
    "additionalConfig": "value"
  }
}
```

**成功响应** (无任务时):
```json
{
  "hasTask": false,
  "message": "No pending tasks available"
}
```

### 2. 任务结果提交接口

**接口地址**: `POST /api/v1/llm-tasks/{taskId}/callback`

**请求头**:
```
Content-Type: application/json
```

**请求体**:
```json
{
  "overallStatus": "COMPLETED",
  "commitId": "a1b2c3d4e5f6789012345678901234567890abcd",
  "results": [
    {
      "documentType": "README",
      "documentTitle": "项目说明",
      "documentContent": "# 项目说明\n\n这是一个示例项目...",
      "filePath": "docs/README.md",
      "status": "SUCCESS"
    },
    {
      "documentType": "API_DOC",
      "documentTitle": "API文档",
      "documentContent": "# API文档\n\n## 接口列表...",
      "filePath": "docs/api.md",
      "status": "SUCCESS"
    }
  ],
  "startTime": "2025-06-21T10:30:00",
  "endTime": "2025-06-21T10:45:00",
  "executionTimeMs": 900000,
  "workerInfo": {
    "workerId": "llm-worker-node-01",
    "workerVersion": "1.0.0"
  }
}
```

**请求参数说明**:
- `overallStatus` (必填): 任务整体状态，可选值：`COMPLETED`、`FAILED`、`PARTIAL_SUCCESS`
- `commitId` (可选): 关联的Git提交ID，必须为40位十六进制字符
- `results` (可选): 任务结果数组，支持多文档类型
- `startTime` (可选): 任务开始时间
- `endTime` (可选): 任务结束时间
- `executionTimeMs` (可选): 任务执行时间（毫秒）
- `errorMessage` (可选): 错误信息，当overallStatus为FAILED时建议填写
- `errorDetail` (可选): 详细错误信息
- `workerInfo` (可选): 工作节点信息

**结果对象说明**:
- `documentType` (必填): 文档类型，如README、API_DOC、ARCHITECTURE等
- `documentTitle` (可选): 文档标题
- `documentContent` (可选): 文档内容（Markdown格式）
- `filePath` (可选): 文档文件路径
- `status` (必填): 生成状态，可选值：`SUCCESS`、`FAILED`、`SKIPPED`
- `errorMessage` (可选): 错误信息，当status为FAILED时填写

**成功响应**:
```json
{
  "success": true,
  "message": "任务结果处理成功",
  "data": {
    "taskId": "12345",
    "status": "COMPLETED"
  }
}
```

## Python客户端示例

```python
import requests
import json
from datetime import datetime
from typing import Optional, Dict, List, Any

class ArchScopeLLMClient:
    def __init__(self, base_url: str, worker_id: str, worker_type: str = "CODE_ANALYSIS"):
        """
        初始化ArchScope LLM客户端
        
        Args:
            base_url: ArchScope服务器地址，如 "http://localhost:8080"
            worker_id: 工作节点唯一标识符
            worker_type: 工作节点类型
        """
        self.base_url = base_url.rstrip('/')
        self.worker_id = worker_id
        self.worker_type = worker_type
        self.worker_version = "1.0.0"
    
    def pull_task(self) -> Optional[Dict[str, Any]]:
        """
        拉取待处理任务
        
        Returns:
            任务信息字典，如果没有任务则返回None
        """
        url = f"{self.base_url}/api/v1/llm-tasks/pull"
        payload = {
            "workerId": self.worker_id,
            "workerVersion": self.worker_version,
            "supportedTaskTypes": ["CODE_FULL_ANALYSIS_JAVA", "DOC_SITE_GENERATION_JAVA"],
            "maxConcurrentTasks": 3
        }
        
        try:
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            if result.get("hasTask"):
                return result
            else:
                return None
                
        except requests.RequestException as e:
            print(f"拉取任务失败: {e}")
            return None
    
    def submit_result(self, task_id: str, status: str, results: List[Dict] = None, 
                     error_message: str = None, execution_time_ms: int = None) -> bool:
        """
        提交任务处理结果
        
        Args:
            task_id: 任务ID
            status: 任务状态 (COMPLETED/FAILED/PARTIAL_SUCCESS)
            results: 结果列表
            error_message: 错误信息
            execution_time_ms: 执行时间（毫秒）
            
        Returns:
            提交是否成功
        """
        url = f"{self.base_url}/api/v1/llm-tasks/{task_id}/callback"
        
        payload = {
            "overallStatus": status,
            "startTime": datetime.now().isoformat(),
            "endTime": datetime.now().isoformat(),
            "workerInfo": {
                "workerId": self.worker_id,
                "workerVersion": self.worker_version
            }
        }
        
        if results:
            payload["results"] = results
        if error_message:
            payload["errorMessage"] = error_message
        if execution_time_ms:
            payload["executionTimeMs"] = execution_time_ms
            
        try:
            response = requests.post(url, json=payload, timeout=30)
            response.raise_for_status()
            
            result = response.json()
            return result.get("success", False)
            
        except requests.RequestException as e:
            print(f"提交结果失败: {e}")
            return False

# 使用示例
def main():
    # 初始化客户端
    client = ArchScopeLLMClient(
        base_url="http://localhost:8080",
        worker_id="llm-worker-001",
        worker_type="CODE_ANALYSIS"
    )
    
    # 拉取任务
    task = client.pull_task()
    if not task:
        print("当前没有待处理任务")
        return
    
    task_id = task["taskId"]
    print(f"获取到任务: {task_id}")
    
    try:
        # 处理任务逻辑
        # ... 这里实现具体的代码分析和文档生成逻辑 ...
        
        # 构造结果
        results = [
            {
                "documentType": "README",
                "documentTitle": "项目说明",
                "documentContent": "# 项目说明\n\n这是通过LLM生成的项目说明文档...",
                "status": "SUCCESS"
            },
            {
                "documentType": "ARCHITECTURE",
                "documentTitle": "架构设计",
                "documentContent": "# 架构设计\n\n## 系统架构\n\n...",
                "status": "SUCCESS"
            }
        ]
        
        # 提交成功结果
        success = client.submit_result(
            task_id=task_id,
            status="COMPLETED",
            results=results,
            execution_time_ms=900000
        )
        
        if success:
            print("任务处理完成并提交成功")
        else:
            print("结果提交失败")
            
    except Exception as e:
        # 提交失败结果
        client.submit_result(
            task_id=task_id,
            status="FAILED",
            error_message=str(e)
        )
        print(f"任务处理失败: {e}")

if __name__ == "__main__":
    main()
```

## 最佳实践

### 1. 错误处理
- 始终使用try-catch包装API调用
- 网络超时设置合理的超时时间（建议30秒）
- 任务处理失败时及时提交FAILED状态

### 2. 任务超时管理
- 任务有30分钟超时限制，超时后会自动重置为PENDING状态
- 建议在处理长时间任务时定期检查任务状态
- 合理估算任务处理时间，避免超时

### 3. 结果提交规范
- 文档内容使用标准Markdown格式
- 合理设置documentType，便于系统分类管理
- 提供准确的执行时间统计

### 4. 轮询策略
- 建议使用指数退避算法进行任务拉取
- 无任务时适当延长轮询间隔
- 避免过于频繁的API调用

### 5. 日志记录
- 记录任务拉取和提交的详细日志
- 包含任务ID、处理时间、结果状态等关键信息
- 便于问题排查和性能监控

## 故障排除

### 常见错误码
- `400 Bad Request`: 请求参数格式错误
- `404 Not Found`: 任务ID不存在
- `409 Conflict`: 任务状态冲突
- `500 Internal Server Error`: 服务器内部错误

### 调试建议
1. 检查API端点URL是否正确
2. 验证请求体JSON格式
3. 确认任务ID格式正确
4. 检查网络连接和防火墙设置
5. 查看ArchScope服务器日志

## 监控指标

建议监控以下关键指标：
- 任务拉取频率和成功率
- 任务处理时间分布
- 任务完成率和失败率
- 超时任务数量
- API响应时间

通过这些指标可以及时发现和解决性能问题，确保LLM服务的稳定运行。

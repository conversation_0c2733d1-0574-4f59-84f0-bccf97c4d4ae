# 规范文档

本目录包含ArchScope项目的所有技术规范和标准文档。

## 📋 文档列表

### 命名规范
- [字段命名规范](field-naming-standards.md) - 统一字段命名标准和约定
- [枚举定义规范](enum-definitions-audit.md) - 枚举类型定义规范和审查

### 模块规范
- [模块规范](../module-specs/) - 各层模块设计规范
  - [应用层规范](../module-specs/application-layer-spec.md)
  - [领域层规范](../module-specs/domain-layer-spec.md)
  - [基础设施层规范](../module-specs/infrastructure-layer-spec.md)
  - [接口层规范](../module-specs/interfaces-api-layer-spec.md)

## 🎯 使用指南

### 开发者
1. 遵循 [字段命名规范](field-naming-standards.md) 进行数据库和API设计
2. 参考 [枚举定义规范](enum-definitions-audit.md) 定义枚举类型
3. 按照模块规范进行代码组织

### 架构师
- 使用这些规范确保系统设计的一致性和可维护性

## 📝 维护说明

- 规范文档应定期审查和更新
- 新增规范需要团队讨论和确认
- 规范变更需要在CHANGELOG中记录
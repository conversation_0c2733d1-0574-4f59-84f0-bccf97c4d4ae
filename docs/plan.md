# `plan.md` (ArchScope 项目实施计划)

## 1. 项目概述与目标

ArchScope是一个架构观测与守护系统。MVP阶段目标是实现针对Java项目的匿名提交、后端自动获取元数据、AST解析、基础Markdown文档和静态网站生成、管理员通过SSO进行项目管理（设置可见性/状态）、以及任务状态的公开查看。后续阶段将逐步增强自动化、解析深度、多语言支持、健康度评估和用户体验。

## 2. 总体分期规划 (Roadmap Summary)

* **Phase 1 (MVP): 核心基础与手动流程验证 (Java Focus)**
* **Phase 2: 自动化工作流与文档站增强**
* **Phase 3: 功能增强与用户体验提升**
* **Phase 4: 成熟化与规模化**

## 3. Phase 1 (MVP): 核心基础与手动流程验证 (Java Focus)

**目标：** 用户通过SSO登录（管理员），匿名用户可提交Java项目URL，系统自动获取元数据，手动触发Java AST解析，生成基础Markdown文档和可访问的静态文档网站（含基础导航、代码高亮、错误码指南页）。任务状态可公开查看。管理员可将项目设为公开可用。

**关键里程碑 (MVP Milestones):**
* **M1.1:** 核心后端框架与SSO集成原型完成。
* **M1.2:** 匿名项目提交与元数据自动获取流程完成。
* **M1.3:** Java AST解析与图数据库存储核心逻辑完成。
* **M1.4:** 基础Markdown文档生成与静态网站托管流程跑通。
* **M1.5:** 公开任务查看API与错误码指南页面完成。
* **M1.6:** 管理员项目管理核心API（设置可见性/状态）完成。
* **M1.7:** MVP功能集成测试通过，可进行内部演示。

**风险控制点 (MVP):** (已在 `architecture.md` 和ADRs中详述)
* SSO集成技术细节。
* Git API元数据获取的稳定性与覆盖面。
* Java AST解析的准确性。
* 自建DB/MQ在K8s的初始部署与配置。

**MVP 详细任务清单:**
*(复杂度评分: 1=非常简单, 10=非常复杂。 依赖关系中的 "S" 代表Setup阶段任务，"M1.x.y" 代表MVP阶段其他任务)*

| 任务ID   | 任务名称                                       | 描述                                                                                                                              | 依赖 (ID)                                  | 复杂度 | 模块涉及                     |
| :------- | :--------------------------------------------- | :-------------------------------------------------------------------------------------------------------------------------------- |:-----------------------------------------| :----- | :--------------------------- |
| **M2: 项目提交与元数据获取 (Milestone M1.2)** |                                                |                                                                                                                                   |                                          |        |                              |
| M2.1     | `archscope-domain`: `Project`实体与VO定义      | 实现`Project`, `ProjectId`, `RepositoryInfo`, `ProjectStatus`, `ProjectVisibility`, `ProjectLanguage`, `ProjectRepoTypeEnum`。          |                                          | 6      | Backend (Domain)             |
| M2.2     | `archscope-domain`: `ProjectRepository`接口    | 定义项目相关的持久化操作接口。                                                                                                          | M2.1                                     | 3      | Backend (Domain)             |
| M2.3     | `archscope-infrastructure`: `MySqlProjectRepositoryImpl` | 实现`ProjectRepository`，使用Spring Data JPA/MyBatisPlus与MySQL交互。创建`ProjectJpaEntity`及Mapper。                               | M2.2,                                    | 7      | Backend (Infra, Domain)    |
| M2.4     | `archscope-domain`: `RepositoryMetadataFetcherPort`接口 | 定义获取Git仓库元数据的接口。                                                                                                         |                                          | 3      | Backend (Domain)             |
| M2.5     | `archscope-infrastructure`: `GitServiceAdapterImpl` (元数据获取) | 实现`RepositoryMetadataFetcherPort`。调用GitHub/GitLab API获取项目名、描述、默认分支等。处理系统级Git凭证。                               | M2.4                                     | 7      | Backend (Infra)              |
| M2.6     | `archscope-app`: `ProjectApplicationService.submitNewProject` | 实现匿名项目提交流程：接收URL，调用Git API获取元数据，判断新旧，创建新`Project`，保存，触发分析任务。                                          | M2.1, M2.3, M2.5, M3.3 (Task Producer)   | 9      | Backend (App, Domain, Infra) |
| M2.7     | `archscope-facade`: `PublicProjectController.submitNewProject` | 实现`POST /projects` API端点。                                                                                       | M2.6                                     | 5      | Backend (API, App)           |
| M2.8     | 前端: 项目注册页面实现 (`register_project.html`) | UI允许输入`repoUrl`，调用`POST /projects` API，处理成功（新/旧项目指引）和错误响应。                                                              | M2.7,                                    | 5      | Frontend                     |
| **M3: 任务系统与Java AST代码解析 (Milestone M1.3)** |                                                |                                                                                                                                   |                                          |        |                              |
| M3.1     | `archscope-domain`: `Task`实体与VO定义         | 实现`Task`, `TaskId`, `TaskTypeEnum`, `TaskStatusEnum`, `TaskTriggeredByTypeEnum`.                                                       |                                          | 5      | Backend (Domain)             |
| M3.2     | `archscope-domain`: `TaskRepository`接口与MySQL实现 | 定义并实现`TaskRepository` (`MySqlTaskRepositoryImpl`)。                                                                               | M3.1,                                    | 5      | Backend (Domain, Infra)    |
| M3.3     | `archscope-app`/`infra`: `TaskMessageProducerPort`与RocketMQ实现 | 定义发送任务消息接口，并用RocketMQ实现。                                                                                                | M3.1,                                    | 6      | Backend (App, Infra)         |
| M3.4     | `archscope-domain`: `CodeGraphPersistencePort`接口 | 定义存储/查询代码图谱数据的接口。                                                                                                        |                                          | 4      | Backend (Domain)             |
| M3.5     | `archscope-infrastructure`: `Neo4jCodeGraphAdapterImpl` (Schema &基础写入) | 实现`CodeGraphPersistencePort`部分。定义Neo4j Schema (Java节点/关系)。实现将`ParsedCodeData`写入Neo4j的基础逻辑。                          | M3.4,                                    | 8      | Backend (Infra, Domain)    |
| M3.6     | `archscope-domain`/`infra`: `JavaAstParserAdapterPort`与JavaParser实现 | 定义Java AST解析接口，使用JavaParser实现，提取包、类、方法、字段，填充`ParsedCodeData` VO。                                                |                                          | 7      | Backend (Domain, Infra)    |
| M3.7     | `archscope-infrastructure`: `GitServiceAdapterImpl` (代码克隆/拉取) | 实现代码克隆和拉取到指定commit的逻辑，支持PAT和OAuth App（MVP优先PAT）。                                                                | M2.5                                     | 7      | Backend (Infra)              |
| M3.8     | `archscope-app`: `CodeAnalysisExecutionAppService` | 实现`executeAnalysisTask`核心逻辑，编排Git拉取、AST解析、图数据存储、任务状态更新、触发后续文档生成任务。                                        | M2.3, M3.2, M3.3, M3.5, M3.6, M3.7       | 9      | Backend (App, Domain, Infra) |
| M3.9     | `archscope-infrastructure`: `RocketMQAnalysisTaskConsumer` | 消费代码分析任务消息，调用`CodeAnalysisExecutionAppService`。处理消息ACK和基本重试。                                                            | M3.8,                                    | 6      | Backend (Infra, App)         |
| **M4: 文档生成与网站托管 (Milestone M1.4)** |                                                |                                                                                                                                   |                                          |        |                              |
| M4.1     | `archscope-domain`/`infra`: `MarkdownGeneratorPort`与Thymeleaf实现 | 定义Markdown生成接口，使用Thymeleaf和1-2个固定Java项目模板（如项目结构概览、包/类列表）生成Markdown内容字符串。                                  | M3.5                                     | 7      | Backend (Domain, Infra)    |
| M4.2     | `archscope-domain`/`infra`: `StaticSitePublisherPort`与文件系统/Nginx实现 | 定义静态站点发布接口。将生成的Markdown渲染为HTML（使用CommonMark-Java），连同CSS/JS资源写入K8s PV或Nginx可访问目录。返回站点相对路径。             | M4.1                                     | 7      | Backend (Domain, Infra)    |
| M4.3     | `archscope-app`: `DocumentationGenerationAppService` | 实现`executeDocSiteGenerationTask`核心逻辑，编排从Neo4j获取数据、生成Markdown、发布站点、更新项目(docSitePath, status)和任务状态。          | M3.5, M4.1, M4.2                         | 8      | Backend (App, Domain, Infra) |
| M4.4     | `archscope-infrastructure`: `RocketMQDocGenTaskConsumer` | 消费文档生成任务消息，调用`DocumentationGenerationAppService`。                                                                       | M4.3,                                    | 6      | Backend (Infra, App)         |
| M4.5     | K8s Nginx配置 (或后端App直接服务静态内容)        | 配置K8s Ingress和Nginx Service将项目文档的`publicDocSitePath`映射到实际存储的静态文件，使其可通过URL公开访问。                                  | M4.2                                     | 5      | DevOps, Backend              |
| M4.6     | 前端: 文档查看基础页面框架                       | 实现文档网站的SPA骨架，包含导航区域（左侧树）和内容显示区域。                                                                                     |                                          | 6      | Frontend                     |
| M4.7     | `archscope-app`/`facade`: 文档树API (`getPublicDocumentTree`) | 实现API以获取指定项目最新公开可用版本的文档树结构。                                                                                       | M4.3 (需要知道doc结构)                         | 6      | Backend (App, API, Infra)|
| M4.8     | 前端: 文档树渲染与导航                         | 调用文档树API，在前端渲染可交互的导航树。                                                                                                      | M4.6, M4.7                               | 6      | Frontend                     |
| M4.9     | `archscope-app`/`facade`: 文档内容API (`getPublicDocumentContent`) | 实现API以获取指定项目最新公开可用版本中特定Markdown文件的内容。                                                                                | M4.3 (需要知道doc内容)                         | 6      | Backend (App, API, Infra)|
| M4.10    | 前端: Markdown内容渲染与代码高亮               | 调用文档内容API，在前端使用Markdown渲染库（如marked.js或markdown-it）渲染内容，并集成代码高亮（如highlight.js或PrismJS）。                   | M4.8, M4.9                               | 7      | Frontend                     |
| **M5: 公开任务查看与错误码指南 (Milestone M1.5)** |                                                |                                                                                                                                   |                                          |        |                              |
| M5.1     | `archscope-app`/`facade`: 公开任务列表与详情API | 实现`GET /tasks`和`GET /tasks/{id}` API (`listTasksPublic`, `getTaskDetailsPublic`)，返回摘要信息。                                           | M3.2                                     | 5      | Backend (App, API, Infra)|
| M5.2     | 前端: 任务队列页面 (`task_queue.html`)         | UI调用`GET /tasks`，展示任务列表，支持基础过滤（按项目ID、状态、类型 - 基于HTML原型推断）。                                                              | M5.1,                                    | 6      | Frontend                     |
| M5.3     | 前端: 任务详情页面 (`task_detail.html`)        | UI调用`GET /tasks/{id}`，展示任务摘要信息和日志预览。                                                                                          | M5.1,                                    | 5      | Frontend                     |
| M5.4     | `archscope-app`/`facade`: 错误码指南API | 实现`GET /system/error-codes` API，返回`ErrorCodeDTO`列表 (内容可硬编码或从配置文件加载)。                                                       |                                          | 3      | Backend (App, API)           |
| M5.5     | 前端: 错误码指南页面 (`error_codes_guide.html`)| UI调用错误码API，展示错误码、描述和解决方案。                                                                                                 | M5.4,                                    | 4      | Frontend                     |
| **M6: 管理员项目管理核心 (Milestone M1.6)** |                                                |                                                                                                                                   |                                          |        |                              |
| M6.1     | `archscope-app`: `AdminProjectAppService`核心方法 | 实现`listAllProjectsForAuthorizedUser`, `getProjectDetailsForAuthorizedUser`, `updateProjectByAuthorizedUser` (含visibility/status更新)。     | M2.3                                     | 7      | Backend (App, Domain)        |
| M6.2     | `archscope-facade`: Admin项目管理API   | 实现`/projects-management`下的GET和PUT端点，受SSO Admin角色保护。                                                                             | M6.1                                     | 6      | Backend (API, App)           |
| M6.3     | `archscope-app`/`facade`: Admin手动触发解析API | 实现`/projects-management/{id}/trigger-analysis` POST端点，受SSO Admin角色保护。                                                              | M2.6 (可复用部分逻辑)                           | 5      | Backend (API, App)           |
| M6.4     | 前端: 管理员项目管理界面 (MVP极简)             | (如果时间允许) 提供基础UI供SSO管理员查看所有项目，修改`visibility`和`status`，手动触发解析。                                                      | M6.2, M6.3,                              | 7      | Frontend                     |
| **M7: 集成与测试 (Milestone M1.7)** |                                                |                                                                                                                                   |                                          |        |                              |
| M7.1     | 端到端流程测试 (匿名提交->管理员审核->公开文档)  | 覆盖核心用户旅程。                                                                                                                            | M2,M3,M4,M6                              | 7      | QA, Backend, Frontend        |
| M7.2     | API集成测试                                  | 使用RestAssured或类似工具测试核心API。                                                                                                         | M2.7, M4.7, M4.9, M5.1, M5.4, M6.2, M6.3 | 6 | Backend, QA                  |
| M7.3     | 单元测试覆盖率 (核心模块)                      | 确保`domain`和`app`层的核心逻辑有足够的单元测试。                                                                                               | All code                                 | 5      | Backend                      |
| M7.4     | Docker Compose 本地环境验证                  | 验证所有服务可以通过`docker-compose.yml` (或K8s minikube/kind) 正常启动和交互。                                                                    |                                          | 4      | DevOps, All                  |
| M7.5     | MVP功能验收文档与演示准备                    |                                                                                                                                              | All MVP                                  | 3      | PM, QA, Lead                 |

*(这是一个非常详细的MVP任务列表。实际执行中，部分任务的粒度可能需要进一步调整，或者某些子任务可以合并。)*

---
**接下来，我将为您提供 Phase 2, Phase 3, 和 Phase 4 的任务分解。这些阶段的任务粒度会比MVP略粗，因为它们的具体实现会更依赖于MVP的成果和后续的优先级调整。**

## 4. Phase 2: 自动化工作流与文档站增强 (Post-MVP)

**目标：** 实现自动化触发和基础的文档版本管理，增强文档网站用户体验。

**关键里程碑 (Phase 2):**
* **M2.1:** Git Webhook成功接收事件并自动触发项目分析任务。
* **M2.2:** 初步的Java增量解析逻辑（基于文件变更）可用。
* **M2.3:** 文档网站支持按Commit ID选择和查看不同版本的文档。
* **M2.4:** 文档网站集成基础的前端内容搜索功能。
* **M2.5:** 能够生成并展示基础的PlantUML C4组件图（基于Java包结构）。

**风险控制点 (Phase 2):**
* 增量解析的准确性和上下文维护复杂度。
* 多版本文档存储方案对存储和查询性能的影响。
* Webhook安全性和可靠性。

**Phase 2 详细任务清单 (概要):**

| 任务ID   | 任务名称                                         | 描述                                                                                                   | 依赖 (ID)         | 复杂度 | 模块涉及                     |
| :------- | :----------------------------------------------- | :----------------------------------------------------------------------------------------------------- | :---------------- | :----- | :--------------------------- |
| P2-GHW-1 | Git Webhook接收器开发 (`infra`, `app`)           | 实现Webhook端点（GitHub, GitLab），验证签名，解析payload，创建并发送分析任务到MQ。                             | M1.7, M3.3        | 7      | Backend (Infra, App)         |
| P2-GHW-2 | 项目配置Webhook (UI/API - Admin)               | 管理员界面允许为项目配置Webhook URL和Secret。                                                               | P2-GHW-1, M6.4    | 6      | Backend (API,App), Frontend|
| P2-SCAN-1| 定时扫描Git仓库变更 (`infra`, `app`)             | (备用机制) 实现定时任务轮询检查项目Git仓库是否有新Commit，若有则触发分析。                                          | M3.7, M3.3        | 6      | Backend (Infra, App)         |
| P2-IPAR-1| Java增量解析核心逻辑 (`domain`, `app`, `infra`)  | 识别变更文件集。仅对变更文件及其直接影响文件进行AST解析。更新Neo4j图数据（合并/替换节点和关系）。                     | M3.8              | 9      | Backend (All)                |
| P2-IPAR-2| 代码分块与上下文关联 (Java)                      | 实现将大型Java文件分块，并在解析块时维护必要的上下文（如imports, 类成员）以确保准确性。                               | P2-IPAR-1         | 8      | Backend (Infra, Domain)    |
| P2-DVER-1| `DocumentSet`实体与版本化存储 (`domain`, `infra`)| 完整实现`DocumentSet`实体，每次文档生成都创建新版本记录，关联到Commit ID。旧版本存档。                               | M4.3              | 7      | Backend (Domain, Infra)    |
| P2-DVER-2| 文档版本查询API (`app`, `api`)                   | 提供API，允许按Commit ID列表获取项目所有可用的文档版本信息（commit SHA, time, message）。                               | P2-DVER-1         | 5      | Backend (App, API)           |
| P2-DVER-3| 前端文档版本选择器UI                           | 在文档网站的导航栏或特定区域添加版本选择下拉框，调用API获取版本列表，并能切换加载不同版本的文档树和内容。                 | P2-DVER-2, M4.10  | 6      | Frontend                     |
| P2-DSCH-1| 前端文档内容搜索 (Lunr.js或类似)               | 在静态文档网站生成时，为所有Markdown内容创建前端搜索索引。实现前端搜索框和结果展示。                                     | M4.5, M4.10       | 6      | Frontend, Backend (DocGen) |
| P2-DIAG-1| PlantUML C4组件图 (Java包) 生成 (`infra`, `app`) | 从Neo4j中提取Java项目的包结构和主要类，生成PlantUML文本描述C4 Component图。                                         | M3.5              | 7      | Backend (Infra, App)         |
| P2-DIAG-2| 文档网站嵌入渲染PlantUML图                     | 将生成的PlantUML文本（或预渲染的SVG）嵌入到Markdown文档中，并在前端文档网站正确显示。                                   | P2-DIAG-1, M4.10  | 5      | Backend (DocGen), Frontend |
| P2-TASK-1| 任务管理增强 (重试/DLQ配置)                      | 细化RocketMQ消费者的重试策略（次数、间隔），配置DLQ，并建立DLQ消息的基础告警或查看机制。                                | M3.9, M4.4        | 6      | Backend (Infra), DevOps      |
| P2-K8S-1 | K8s初步部署与运维文档                          | 编写基础的K8s部署描述文件 (Deployments, StatefulSets, Services, Ingress) 和初步的运维指南。                          | M7.4              | 7      | DevOps, Backend Lead         |

## 5. Phase 3: 功能增强与用户体验提升 (Post-MVP) - 任务概要

**目标：** 丰富解析能力（深度LLM，初步多语言），完善文档功能（对比、模板），引入基础健康度评估，优化UI/UX。

**关键里程碑 (Phase 3):**
* **M3.1:** LLM辅助的代码摘要和代码异味识别（Java）功能上线。
* **M3.2:** 成功支持第二门编程语言（如Python）的AST解析和基础文档生成。
* **M3.3:** 项目健康度模块上线，展示基础的健康度指标和评分。
* **M3.4:** 文档版本对比功能可用。
* **M3.5:** 管理员可对系统级文档模板进行初步管理。

**风险控制点 (Phase 3):**
* LLM分析的准确性、成本和性能。
* 新语言解析器的开发和维护成本。
* 健康度指标的有效性和用户接受度。

**Phase 3 详细任务清单 (概要):**

| 任务ID   | 任务名称                                         | 描述                                                                                                | 依赖 (ID)         | 复杂度 | 模块涉及                        |
| :------- | :----------------------------------------------- | :-------------------------------------------------------------------------------------------------- | :---------------- | :----- | :------------------------------ |
| P3-LLM-1 | LLM代码摘要功能 (Java)                           | 集成OpenRouter，设计Prompt，对Java类/方法生成摘要，并集成到文档中。                                          | M1.1.3 (SSO for Key if needed), P2-IPAR, ADR-008 | 8 | Backend (Infra, App, Domain)|
| P3-LLM-2 | LLM代码异味识别 (Java - 初步)                    | 设计Prompt识别几种常见的Java代码异味 (如Long Method, Large Class)，结果可用于健康度评估。                        | P3-LLM-1          | 8      | Backend (Infra, App, Domain)|
| P3-LANG-1| Python AST解析器集成                             | 引入Python AST解析库 (如Python `ast`模块的Java调用封装，或Antlr Python grammar)，提取结构信息。                 | M3.6 (Java Parser as ref) | 9 | Backend (Infra, Domain)       |
| P3-LANG-2| Python项目基础文档生成                           | 为Python项目调整或创建新的文档模板，生成基础结构文档。                                                          | P3-LANG-1, M4.1   | 7      | Backend (Infra, App)          |
| P3-HLTH-1| 健康度指标定义与数据收集 (Java)                  | 定义MVP后第一批健康度指标（如圈复杂度、依赖数量、测试覆盖率（需外部输入）、代码异味数）。从Neo4j和MySQL收集数据。      | M3.5, P3-LLM-2    | 7      | Backend (Domain, App, Infra)|
| P3-HLTH-2| 健康度评分算法与报告DTO                          | 设计简单的加权评分算法，生成`HealthReportDTO`。                                                              | P3-HLTH-1         | 6      | Backend (Domain, App)         |
| P3-HLTH-3| 项目详情页展示健康度报告                         | 在`ProjectDetailDTO`中加入健康度摘要，前端`project_detail.html`展示。                                            | P3-HLTH-2         | 5      | Backend (API, App), Frontend|
| P3-DCOMP-1| 文档内容Diff API                               | 后端API，接收两个commitId和一个文件路径，返回该文件在两个版本间的diff结果（如 unified diff格式或HTML diff）。     | P2-DVER-1         | 7      | Backend (App, Infra, Domain)|
| P3-DCOMP-2| 前端文档版本对比UI (`project_doc_compare.html`)| 实现UI，允许用户选择两个版本和文件，调用API获取并展示diff结果。                                                  | P3-DCOMP-1, P2-DVER-3 | 8 | Frontend                      |
| P3-DTMPL-1| 文档模板管理 (Admin - 系统级)                    | (Admin) 提供界面管理系统预设的Thymeleaf文档模板（增删改查基础模板内容）。                                         | M4.1, M6.4        | 7      | Backend (All), Frontend       |
| P3-TASKUI-1| 任务管理Admin UI (队列查看、任务详情)            | 实现`task_queue.html`和`task_detail.html`的完整管理员功能，包括查看所有任务、详细日志、payload、result等。          | P2-TASK-1, M5.2, M5.3 | 7 | Frontend, Backend (API,App)|
| P3-UX-1  | 前端导航与主题优化                               | 根据用户反馈改进文档网站和主应用的导航体验，提供基础的主题定制选项（如暗色模式）。                                   | M4.10, P2-DVER-3  | 6      | Frontend                      |

## 6. Phase 4: 成熟化与规模化 (Post-MVP) - 任务概要

**目标：** 系统功能完善，性能优化，安全性加固，支持更大规模应用和扩展。

**关键里程碑 (Phase 4):**
* **M4.1:** 高级健康度评估（含自定义规则）和报告功能完善。
* **M4.2:** 统计分析功能上线。
* **M4.3:** 支持本地LLM部署选项并完成集成。
* **M4.4:** 系统完成大规模性能和压力测试，关键瓶颈得到优化。
* **M4.5:** 完成一轮深度安全加固和审计。
* **M4.6:** 提供稳定的外部集成API。

**风险控制点 (Phase 4):**
* 大规模数据处理和查询的性能瓶颈。
* 本地LLM部署的资源需求和运维复杂度。
* 自定义规则引擎的设计和实现复杂度。

**Phase 4 详细任务清单 (概要):**

| 任务ID   | 任务名称                                         | 描述                                                                                                 | 依赖 (ID)         | 复杂度 | 模块涉及                        |
| :------- | :----------------------------------------------- | :--------------------------------------------------------------------------------------------------- | :---------------- | :----- | :------------------------------ |
| P4-HLTH-1| 健康度自定义规则引擎                             | 允许管理员或高级用户通过UI定义新的健康度指标计算规则、权重和阈值。                                             | P3-HLTH-2         | 9      | Backend (All), Frontend       |
| P4-HLTH-2| 健康度趋势分析与报告导出                         | 记录健康度历史数据，生成趋势图表，支持报告导出 (PDF/CSV)。                                                  | P3-HLTH-2         | 7      | Backend (App, Infra), Frontend|
| P4-STATS-1| 访问统计数据收集与存储                           | 设计并实现用户访问项目文档网站的行为日志收集（页面、时长、来源等），聚合存储到MySQL或专用分析库。                     | M4.5              | 7      | Backend (Infra, App), Frontend|
| P4-STATS-2| 统计分析仪表盘                                   | 提供Admin仪表盘，展示项目活跃度、热门文档、用户访问趋势等统计图表。                                             | P4-STATS-1        | 8      | Backend (API,App), Frontend |
| P4-LLMLCL-1| 本地LLM部署方案调研与实现                        | 调研在K8s环境中部署开源LLM（如Ollama+CodeLlama）的方案，包括GPU资源管理、模型服务封装。                             | P3-LLM-1, ADR-008 | 10     | DevOps, Backend (Infra)       |
| P4-LLMLCL-2| `LLMAdapter`支持本地模型API                    | 扩展`LLMAdapter`以支持调用内部部署的本地LLM服务API。                                                         | P4-LLMLCL-1       | 6      | Backend (Infra)               |
| P4-PERF-1| 大规模性能压力测试                               | 针对高并发用户访问、大量项目、大型代码仓库解析等场景进行全面的性能和压力测试。                                     | P2-K8S-1          | 8      | QA, DevOps, Backend           |
| P4-PERF-2| 关键性能瓶颈分析与优化                           | 根据压力测试结果，识别并优化瓶颈（DB查询、图查询、API响应、任务处理等）。                                          | P4-PERF-1         | 9      | Backend (All)                 |
| P4-SEC-1 | LLM交互安全审计与加固                            | 完善Prompt注入防护，敏感数据在送给LLM前脱敏，对LLM输出进行安全审查。                                           | P3-LLM-1          | 7      | Backend (Infra, App)        |
| P4-SEC-2 | PII过滤与代码脱敏规则增强                        | (如果系统处理用户上传的文档或敏感代码) 优化PII和敏感信息的自动识别与过滤/脱敏机制。                               | P4-SEC-1          | 8      | Backend (Infra, Domain)     |
| P4-SEC-3 | 定期安全审计与渗透测试                           | 引入第三方或内部安全团队进行全面的安全评估。                                                                   | All Prev Phases   | 7      | Security Team, DevOps       |
| P4-APIEXT-1| 外部集成API设计与发布 (v1)                     | 设计并发布一套供第三方系统集成的稳定API（如触发项目分析、获取分析结果、查询项目信息等）。                               | All Prev API tasks| 8      | Backend (API, App, Domain)  |
| P4-LANG-1| 多语言支持扩展 (JS/TS等)                       | 按照P3-LANG的模式，增加对JavaScript/TypeScript等更多语言的AST解析和文档生成支持。                               | P3-LANG-2         | 9      | Backend (Infra, Domain, App)|
| P4-K8S-ADV| K8s高级运维 (HPA, 资源优化, GitOps)            | 实现基于Metrics的Horizontal Pod Autoscaling。优化K8s资源请求与限制。采用GitOps进行部署管理。                    | P2-K8S-1          | 8      | DevOps                      |

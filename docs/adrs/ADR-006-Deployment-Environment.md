### ADR-006: 代码知识图谱存储方案 (Neo4j)

**状态:** 已接受 (Accepted)

**上下文 (Context):**
ArchScope的核心功能之一是分析代码仓库，提取架构元素（如类、方法、模块）及其之间的复杂依赖关系（如调用、继承、实现、引用）。这些高度连接的数据天然适合用图结构来表示和查询，形成代码知识图谱。我们需要选择一个合适的图数据库技术来存储和管理这些图数据，以支持后续的文档生成、依赖分析、架构可视化和（未来）健康度评估。

**决策驱动因素 (Decision Drivers):**
* **数据模型匹配度:** 所选技术必须能高效地存储和查询图结构数据（节点、带属性的边）。
* **查询能力:** 需要支持复杂的图遍历查询（如查找所有间接依赖、识别循环依赖、路径分析、模式匹配）。
* **性能:** 对图谱的读写性能，特别是查询性能。
* **可扩展性:** 应对代码库规模增长（节点和关系数量可能达到亿级）和并发查询增加的能力。
* **生态与工具:** 成熟的驱动程序、查询语言、可视化工具、社区支持。
* **NFR-PERF, NFR-SCALE:** 满足系统对代码分析结果查询的性能和扩展性要求。
* **部署环境:** 私有云K8s，自建数据库。
* **团队熟悉度 (次要，可学习):** 团队对图数据库技术的掌握程度。

**考虑的方案 (Considered Options):**

* **方案1: 原生图数据库 - Neo4j**
    * 一个成熟的、高性能的、原生的属性图数据库，使用Cypher作为声明式查询语言。拥有广泛的社区支持和工具生态。
    * **优点:**
        * **为图而生:** 数据模型与代码知识图谱高度匹配。
        * **强大的Cypher查询语言:** 非常适合复杂的图遍历和模式匹配。
        * **高性能:** 对于图相关的查询通常优于其他类型数据库。
        * **成熟的生态:** 完善的Java驱动、Spring Data Neo4j集成、可视化工具（Neo4j Browser, Bloom）、APOC扩展库。
        * **ACID事务支持。**
        * **可扩展性:** Enterprise Edition支持Causal Clustering实现高可用和读写分离。Community Edition单实例，可通过K8s StatefulSet保证基础可靠性。
    * **缺点:**
        * Community Edition的扩展性和高可用性有限。
        * 与关系型数据库相比，运维和数据建模需要新的技能。
        * 对于非图结构的查询（如大量属性过滤后聚合），可能不如关系型数据库或文档数据库。

* **方案2: 关系型数据库模拟图 (如MySQL)**
    * 使用邻接表、路径枚举、嵌套集等技术在MySQL中存储图数据。
    * **优点:** 无需引入新的数据库技术，运维统一。
    * **缺点:** 查询复杂且性能低下，尤其是对于多跳查询和路径分析。SQL不适合表达图查询。数据冗余和一致性维护困难。

* **方案3: 其他图数据库 (如 JanusGraph, ArangoDB, NebulaGraph)**
    * **JanusGraph:** 开源分布式图数据库，支持多种后端存储（Cassandra, HBase, Elasticsearch），可扩展性强。使用Gremlin查询语言。学习曲线较陡，运维复杂。
    * **ArangoDB:** 多模型数据库（文档、图、键值），使用AQL查询语言。功能全面，但可能不如专注的图数据库在特定图查询上极致。
    * **NebulaGraph:** 开源分布式图数据库，专为大规模图设计，性能高。使用nGQL查询语言。相对较新，生态可能不如Neo4j成熟。
    * **优点:** 这些数据库各有特点，在分布式、多模等方面有优势。
    * **缺点:** 团队可能不熟悉，生态、工具链、社区支持可能不如Neo4j广泛。学习和集成成本可能较高。

* **方案4: NoSQL文档数据库 (如MongoDB) 存储图边列表**
    * 用文档存储节点，节点内嵌边列表或引用其他节点。
    * **优点:** 对节点属性的灵活扩展性好。
    * **缺点:** 图遍历仍需在应用层实现或通过聚合框架模拟，性能不如原生图数据库。

**决策 (Decision):**

我们决定采用 **方案1: 原生图数据库 - Neo4j (5.x Community Edition for MVP)** 作为ArchScope代码知识图谱的存储方案。

理由:
1.  **数据模型高度匹配:** 代码的结构和依赖关系天然是图，Neo4j的属性图模型能直接、高效地表示这些数据。
2.  **强大的图查询能力:** Cypher查询语言非常适合ArchScope需要的复杂依赖分析、路径查找、模式识别等功能，这是生成有价值的架构洞察和文档的基础。
3.  **成熟的生态和Java集成:** Spring Data Neo4j提供了与Spring Boot的无缝集成，简化了开发。Neo4j Browser等工具便于开发和调试。
4.  **性能优势:** 对于图相关的查询，Neo4j通常比用其他类型数据库模拟图的方式性能更好。
5.  **ACID事务:** 保证数据写入的完整性。
6.  **MVP阶段可行性:** Neo4j Community Edition可以在K8s上通过StatefulSet自建单实例部署，满足MVP阶段的需求。
7.  **未来可扩展路径清晰:** 如果未来需要更高的可用性和扩展性，可以迁移到Neo4j Enterprise Edition的集群方案或考虑Neo4j AuraDB（云托管，如果客户策略允许）。

**后果 (Consequences):**

* **积极的:**
    * 能够高效存储和查询复杂的代码结构和依赖关系，为ArchScope的核心功能提供强大数据支持。
    * 便于实现高级的代码分析功能，如影响分析、架构规则校验。
    * Spring Data Neo4j简化了与Java后端的数据访问层集成。
* **消极的/风险/需要注意的:**
    * **运维Neo4j Community Edition:** 需要在K8s上正确配置StatefulSet、PV/PVC、备份策略（定期dump）、监控（通过JMX Exporter或APOC）。其高可用性依赖于K8s的Pod自愈和存储的可靠性。
    * **团队技能建设:** 开发团队需要学习Cypher查询语言和Neo4j的数据建模最佳实践。
    * **数据建模重要性:** 需要精心设计图模型（节点标签、属性、关系类型和方向）以优化查询性能和数据表达能力 (已在`architecture.md`中初步设计)。
    * **数据一致性 (与MySQL):** 代码知识图谱的数据通常源于MySQL中记录的项目和分析任务。需要确保在分析完成后，相关图数据被正确生成和关联。
    * **Community Edition限制:** 单实例，无内置集群高可用和水平扩展。性能瓶颈时主要靠垂直扩展。
* **需要进一步的工作/决策:**
    * 详细设计Neo4j的图模型Schema，包括节点属性、关系属性、索引策略 (已在`architecture.md`中初步完成)。
    * 制定Neo4j Community Edition在K8s上的详细部署、备份、恢复和监控方案。
    * 编写`archscope-infrastructure`中的`Neo4jCodeGraphAdapterImpl`，实现图数据的写入和查询逻辑。
    * 评估MVP阶段的图数据量和查询复杂度，确保Community Edition性能满足要求。

**其他相关信息 (Optional):**
* 参考 `architecture.md` 中数据模型设计关于Neo4j图模型的详细描述。

**最后更新日期:** 2025-05-12
**决策者:** ArchScope项目架构团队

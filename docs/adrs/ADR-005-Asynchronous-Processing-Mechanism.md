### ADR-005: 异步处理机制 (RocketMQ任务队列)

**状态:** 已接受 (Accepted)

**上下文 (Context):**
ArchScope系统中包含多个耗时操作，如代码仓库的克隆/拉取、代码解析（AST及未来LLM）、Markdown文档生成、静态文档网站打包等。如果这些操作在用户请求的同步线程中执行，会导致API响应时间过长，用户体验差，甚至可能引发请求超时。因此，需要一个健壮的异步处理机制来将这些操作后台化。

**决策驱动因素 (Decision Drivers):**
* **NFR-PERF-001 (API响应时间):** 要求核心API响应时间小于1秒，启动异步任务的API响应小于200毫秒。
* **NFR-PERF-003 (代码仓库处理能力):** 代码解析（尤其是大型仓库）本身就是耗时操作。
* **NFR-AVAIL-004 (Fault Tolerance):** 异步任务处理失败时应有重试机制，不应影响主系统可用性。
* **NFR-SCALE-003 & 004 (解析能力与服务扩展):** 异步任务处理单元应能独立于API服务进行扩展。
* **解耦:** 将任务的提交与任务的执行解耦，提高系统的灵活性和弹性。
* **可靠性:** 保证任务在提交后最终会被处理，即使发生临时故障。
* **技术栈偏好 (来自`tasks.json`):** 项目已在环境搭建阶段设置了RocketMQ。

**考虑的方案 (Considered Options):**

* **方案1: Java内置线程池 (`ExecutorService`)**
    * 在Spring Boot应用内部使用`@Async`注解或直接管理`ExecutorService`来执行后台任务。
    * **优点:** 实现简单，无需引入外部中间件，适合非常轻量级的异步操作。
    * **缺点:**
        * **无持久化:** 应用重启会导致队列中未处理的任务丢失（除非自行实现持久化逻辑）。
        * **单体限制:** 难以跨多个应用实例进行任务分发和负载均衡。
        * **监控与管理弱:** 缺乏专业的任务队列管理、监控、死信处理等功能。
        * **可靠性不足:** 错误处理和重试逻辑需要完全自行实现。
        * **不适合长时间运行或资源密集型任务，** 可能耗尽应用服务器资源。

* **方案2: 专用消息队列 (Message Queue - MQ) - 如Apache RocketMQ**
    * 将耗时任务作为消息发送到MQ，由独立的消费者应用（或同一应用内的专门消费者线程池）从MQ拉取并处理。
    * **优点:**
        * **解耦:** 生产者和消费者完全解耦。
        * **持久化与可靠性:** MQ通常提供消息持久化，保证消息不丢失。支持事务消息、确认机制。
        * **削峰填谷:** 能够应对突发任务请求，平滑处理负载。
        * **可扩展性:** 生产者和消费者都可以独立扩展。
        * **丰富的特性:** 支持优先级队列（或通过多队列模拟）、延迟消息、死信队列、消息追溯、监控管理界面等。
        * **错误处理与重试:** MQ消费者框架通常提供重试机制。
    * **缺点:**
        * **引入外部依赖:** 需要部署和运维MQ集群（尽管本项目是自建在K8s）。
        * **开发复杂度略高:** 需要处理消息的序列化/反序列化，设计消息格式。

* **方案3: 分布式任务调度框架 (如Quartz, XXL-Job, PowerJob)**
    * 使用专门的任务调度框架来管理和执行定时任务或一次性后台任务。
    * **优点:** 提供更丰富的任务调度策略（CRON表达式、依赖触发、分片执行等）、任务管理界面、执行器管理。
    * **缺点:** 主要面向调度而非纯粹的异步解耦。对于ArchScope主要由API或事件触发的后台任务，MQ的简单生产者-消费者模式可能更直接。引入另一个框架会增加系统复杂度。

**决策 (Decision):**

我们决定采用 **方案2: 专用消息队列，具体选择 Apache RocketMQ 5.x (自建在K8s)** 作为ArchScope系统的核心异步处理机制。

理由:
1.  **满足核心需求:** RocketMQ能够很好地满足API响应时间、耗时操作后台化、任务解耦、可靠执行和可扩展性的需求。
2.  **技术栈一致性:** `tasks.json`表明项目已在环境搭建阶段引入并设置了RocketMQ，团队对其应有一定熟悉度。
3.  **特性丰富且成熟:** RocketMQ是阿里巴巴开源的分布式消息中间件，经过大规模生产环境验证，功能完善，包括：
    * **高可用:** 支持多Master多Slave部署。
    * **消息持久化:** 保证消息不丢失。
    * **多种消息类型:** 支持普通消息、顺序消息（按需使用）、延迟消息（可用于任务重试间隔控制）、事务消息（用于关键操作的最终一致性）。
    * **削峰填谷能力。**
    * **消费者负载均衡和故障转移。**
    * **死信队列 (DLQ):** 用于处理无法成功消费的消息。
    * **监控与管理:** 提供Console或API进行监控。
4.  **与Spring Boot集成良好:** 有成熟的`rocketmq-spring-boot-starter`简化集成开发。
5.  **性能优异:** RocketMQ以其高吞吐量和低延迟著称。
6.  **私有云K8s自建:** RocketMQ可以容器化并部署在K8s上，与ArchScope的部署环境一致。可以使用Operator或Helm chart进行管理。

**后果 (Consequences):**

* **积极的:**
    * 系统响应能力和用户体验将得到显著提升，耗时操作不再阻塞用户请求。
    * 系统整体的吞吐量和弹性得到增强。
    * 核心业务逻辑与后台任务处理实现解耦，提高了系统的可维护性和可扩展性。
    * 任务处理的可靠性得到保障。
* **消极的/风险/需要注意的:**
    * **运维成本:** 需要在K8s环境中运维一套自建的RocketMQ集群（包括NameServer和Broker节点），确保其高可用、性能和数据持久化。
    * **消息设计与管理:** 需要精心设计消息的Topic、Tag、Key以及消息体结构，确保消息的正确路由和处理。
    * **消费者幂等性:** 消费者在处理消息时需要保证幂等性，以应对MQ可能的消息重投。
    * **分布式事务考量 (Post-MVP):** 如果业务操作（如DB更新）与消息发送需要严格原子性，需要考虑使用RocketMQ的事务消息或实现本地事件表/Outbox模式。
    * **监控与告警:** 需要将RocketMQ集群的健康状况和关键指标（队列积压、消费速率等）纳入统一监控告警平台。
* **需要进一步的工作/决策:**
    * 详细设计各类异步任务的消息格式和Topic/Tag规划 (已在`architecture.md`中初步规划)。
    * 实现`archscope-infrastructure`中的`TaskMessageProducer`和各类`TaskConsumer`。
    * 制定RocketMQ在K8s上的详细部署、配置、备份和监控方案。
    * 明确各任务的重试策略和DLQ处理流程。

**其他相关信息 (Optional):**
* 参考 `architecture.md` 中关于任务管理与调度功能、RocketMQ集成以及相关流程图的描述。

**最后更新日期:** 2025-05-12
**决策者:** ArchScope项目架构团队

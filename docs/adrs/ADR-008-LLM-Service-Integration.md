### ADR-008: LLM服务集成策略 (OpenRouter首选)

**状态:** 已接受 (Accepted)

**上下文 (Context):**
ArchScope计划利用大语言模型 (LLM) 来增强代码分析能力（多为MVP后功能，MVP阶段可能仅有极简应用）。我们需要一种灵活、可扩展且成本可控的LLM服务集成策略。客户已指明外部LLM API首选OpenRouter。

**决策驱动因素 (Decision Drivers):**
* **模型多样性与灵活性:** 希望能访问多种LLM模型。
* **API统一性:** 简化与不同LLM后端的交互。
* **成本控制:** 能够比较和选择不同模型的成本。
* **集成复杂度:** 开发工作量。
* **未来支持本地模型:** 架构应易于扩展。
* **客户偏好:** OpenRouter为首选。
* **NFR-SEC-004 (LLM交互安全):** Prompt注入、数据隐私。

**考虑的方案 (Considered Options):**

* **方案1: 直接集成特定LLM提供商API (如 OpenAI, Anthropic)**
    * 为每个提供商编写独立客户端。
    * **优点:** 可用特定提供商独有特性。
    * **缺点:** 高耦合，切换成本高，重复开发，模型选择受限，成本管理分散。

* **方案2: 集成LLM API网关 - OpenRouter**
    * OpenRouter提供统一API访问多种模型。
    * **优点:** 模型选择灵活，API统一，潜在成本优化，简化API Key管理。
    * **缺点:** 依赖第三方网关，可能无法用最新或独有API特性，数据隐私路径增加。

* **方案3: 自建LLM推理服务并集成 (针对本地模型选项)**
    * 在私有云K8s部署开源LLM。
    * **优点:** 数据私有可控，无外部API调用成本。
    * **缺点:** 部署运维复杂，需GPU，模型管理挑战，性能调优复杂。

**决策 (Decision):**

我们决定采用 **方案2: 集成LLM API网关 - OpenRouter** 作为ArchScope与外部LLM服务交互的首选和主要方式。
同时，在`archscope-infrastructure`中设计一个**LLM服务适配器层 (LLMAdapter)**，该适配器层初期主要实现对OpenRouter API的调用，但其接口设计应具备一定的通用性，以便未来可以：
a.  直接集成其他特定LLM提供商的API。
b.  集成内部自建的LLM推理服务API（实现“本地模型选项”）。

理由:
1.  **满足客户偏好:** OpenRouter已被指定为首选。
2.  **模型灵活性最大化:** OpenRouter提供了访问大量不同模型的便捷途径。
3.  **简化初期集成:** ArchScope只需对接一个相对统一的API。
4.  **成本透明与控制:** 用户使用其OpenRouter账户，可以自行管理其LLM使用成本和模型选择。
5.  **适配器层支持未来扩展:** 方便平滑增加对其他LLM源的支持。

**后果 (Consequences):**

* **积极的:**
    * 增强了ArchScope在LLM模型选择上的灵活性和未来适应性。
    * 简化了多LLM提供商的直接集成工作。
    * 用户可以利用OpenRouter的生态和可能的成本优势。
* **消极的/风险/需要注意的:**
    * **依赖OpenRouter:** ArchScope的LLM功能将依赖OpenRouter服务的可用性、性能和政策。需要有监控和可能的降级策略。
    * **数据隐私:** 数据流经第三方服务，需在隐私政策中向用户说明。本地模型选项是最终解决此问题的途径。
    * **OpenRouter API特性集:** ArchScope能使用的LLM特性受限于OpenRouter API提供的功能。
    * **API Key管理:** 需要安全地管理ArchScope系统自身的OpenRouter API Key，或设计机制让用户安全地提供他们自己的Key。
* **需要进一步的工作/决策:**
    * 详细研究OpenRouter API的认证方式、请求/响应格式、错误处理、支持的模型列表及特性。
    * 设计`LLMAdapter`的通用接口。
    * 实现`OpenRouterLlmClientAdapterImpl`。
    * 制定LLM交互的安全策略（Prompt净化、输出过滤）在`LLMAdapter`或调用方中的实现。
    * MVP阶段的LLM集成将非常简化。

**其他相关信息 (Optional):**
* ArchScope的LLM Prompt设计将是另一个关键工作。

**最后更新日期:** 2025-05-12
**决策者:** ArchScope项目架构团队 (客户反馈整合)

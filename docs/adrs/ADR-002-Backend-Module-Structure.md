### ADR-002: 后端模块结构 (DDD六边形Maven模块)

**状态:** 已接受 (Accepted)

**上下文 (Context):**
在选择了模块化单体作为ArchScope的后端架构模式后 (见 ADR-001)，我们需要定义清晰的内部模块结构，以保证代码的组织性、可维护性、可测试性，并支持领域驱动设计 (DDD) 和六边形架构（端口与适配器）的原则。一个良好定义的模块结构是实现高内聚、低耦合的关键，并为未来的系统演进提供便利。

**决策驱动因素 (Decision Drivers):**
* **ADR-001:** 已决定采用模块化单体架构。
* **NFR-MAINTAIN-006 (DDD分层):** 需求明确要求后端采用领域驱动设计原则。
* **架构原则:** 高内聚、低耦合、清晰的职责边界、依赖倒置。
* **可测试性:** 核心业务逻辑应能独立于外部依赖进行测试。
* **可理解性:** 模块结构应直观反映系统的逻辑组成。
* **可演进性:** 模块边界应为未来可能的重构或微服务拆分提供便利。
* **团队开发效率:** 清晰的模块划分有助于并行开发和减少冲突。

**考虑的方案 (Considered Options):**

* **方案1: 基于技术分层的传统模块结构 (e.g., `controller`, `service`, `dao`, `model`)**
    * 按照技术职责划分模块或包。例如，所有Controller放在一个模块/包，所有Service放在另一个，以此类推。
    * **优点:** 简单直观，许多开发者熟悉。
    * **缺点:** 容易导致业务逻辑分散在不同技术层中，形成所谓的“贫血领域模型”。模块间的依赖容易混乱，难以真正实现高内聚、低耦合。业务功能的修改可能需要跨多个技术模块进行，可维护性差。不完全符合DDD和六边形架构的核心思想。

* **方案2: 基于DDD和六边形架构的Maven模块结构**
    * 按照DDD的层级和六边形架构的端口与适配器思想划分独立的Maven模块。具体划分为：
        * `archscope-domain`: 领域模型层 (实体、值对象、领域服务接口、仓储接口)。
        * `archscope-app`: 应用服务层 (编排领域逻辑、处理用例、事务管理)。
        * `archscope-facade`: 接口层/门面层 (REST API Controllers, DTOs)。
        * `archscope-infrastructure`: 基础设施层 (仓储实现、外部服务适配器)。
        * `archscope-main`: 应用启动与配置。
    * **优点:**
        * **清晰的职责分离：** 业务核心（domain, app）与技术实现（infrastructure, facade）完全解耦。
        * **强化的领域模型：** `domain`模块成为系统的核心，封装所有业务规则。
        * **依赖倒置：** `infrastructure`和`app`都依赖于`domain`中定义的抽象（接口），符合依赖倒置原则。
        * **高可测试性：** `domain`和`app`层可以独立于数据库、消息队列等基础设施进行单元测试和集成测试。
        * **明确的依赖方向：** `facade` -> `app` -> `domain` <- `infrastructure`。有助于控制耦合。
        * **支持演进：** 清晰的适配器边界使得替换基础设施实现或将某个适配器连同其驱动的应用服务拆分为微服务更为容易。
    * **缺点:**
        * 对于非常简单的CRUD应用可能显得过于复杂。
        * 需要团队对DDD和六边形架构有较好的理解。
        * 模块数量比传统分层略多。

* **方案3: 单一模块，内部按包结构划分 (类似方案1，但未使用Maven多模块)**
    * 所有代码在一个Maven模块中，通过内部包结构（如`com.archscope.api`, `com.archscope.application`, `com.archscope.domain`, `com.archscope.infrastructure`）来组织。
    * **优点:** 项目结构最简单，没有多模块管理的开销。
    * **缺点:** 模块边界是逻辑上的，依赖管理和编译时隔离不如Maven多模块严格，更容易被破坏。长期来看，可维护性不如方案2。

**决策 (Decision):**

我们决定采用 **方案2: 基于DDD和六边形架构的Maven模块结构**。具体模块如下：
* `archscope-domain`
* `archscope-app`
* `archscope-facade`
* `archscope-infrastructure`
* `archscope-main`

理由:
1.  **最佳实践对齐:** 此结构完美契合了PRD中对DDD分层架构的要求 (NFR-MAINTAIN-006)，并且是实现六边形架构的推荐方式。
2.  **核心业务逻辑保护:** `archscope-domain`作为核心，不依赖任何具体技术实现，保证了业务逻辑的纯粹性和可移植性。
3.  **高度可测试性:** 能够轻松地对`domain`和`app`层进行单元测试和模拟外部依赖的集成测试。
4.  **清晰的依赖管理:** Maven多模块提供了编译时的依赖隔离和方向控制，有助于维持架构的健康。
5.  **促进关注点分离:** 每个模块都有非常明确的职责，使得开发者可以更专注于特定领域的问题。
6.  **可演进性强:** `archscope-infrastructure`中的适配器模式使得未来更换数据库、消息队列或集成新的外部服务更为容易。同时，如果未来需要将某个功能拆分为微服务，围绕特定应用服务及其依赖的领域逻辑和适配器进行剥离也更清晰。
7.  **符合团队提升目标:** 虽然需要团队对DDD和六边形架构有一定理解，但这也是提升团队架构设计能力的良好实践。

**后果 (Consequences):**

* **积极的:**
    * 系统将拥有一个非常清晰、健壮且可维护的内部结构。
    * 核心业务逻辑得到良好保护，不易受技术细节变化的影响。
    * 单元测试和组件测试的覆盖率和有效性将更容易得到保障。
    * 新成员更容易理解系统的不同部分及其职责。
    * 为未来的扩展和重构奠定了坚实基础。
* **消极的/风险/需要注意的:**
    * **初期学习曲线:** 团队成员可能需要一些时间来完全适应这种严格的模块划分和依赖规则。
    * **模块间通信开销 (轻微):** 虽然都是进程内调用，但通过接口和DTO转换会比直接访问内部实现略有性能开销，但在模块化单体中通常可忽略不计。
    * **避免过度设计:** 对于某些非常简单的CRUD操作，严格遵循所有分层可能显得繁琐。需要找到实用主义和架构原则之间的平衡，例如简单的查询可以直接从`facade`通过一个轻量级查询服务到`infrastructure`（如果绕过`app`和`domain`被允许的话，但这通常不推荐，除非是纯粹的数据读取且不含业务逻辑）。
* **需要进一步的工作/决策:**
    * 详细定义每个模块内部的包结构和代码组织规范。
    * 明确各模块间核心接口的定义 (已在`architecture.md`中初步规划)。
    * 建立CI/CD流程，支持Maven多模块项目的构建和测试。

**其他相关信息 (Optional):**
* 详细的模块职责和交互已在 `architecture.md` 的C4组件图和模块设计章节中描述。

**最后更新日期:** 2025-05-12
**决策者:** ArchScope项目架构团队

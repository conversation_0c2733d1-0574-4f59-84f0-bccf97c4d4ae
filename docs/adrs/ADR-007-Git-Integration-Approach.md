**ADR-007: Git集成方式 (PAT与OAuth App支持)**

**状态:** 已接受 (Accepted)

**上下文 (Context):**
ArchScope系统需要与外部Git代码仓库（如GitHub, GitLab）进行交互，以克隆或拉取项目源代码进行分析。这涉及到对Git仓库的认证和授权。我们需要选择一种或多种安全且用户友好的方式来让ArchScope访问用户的代码仓库。

**决策驱动因素 (Decision Drivers):**
* **安全性:** 保护用户Git仓库的访问凭证，遵循最小权限原则。
* **用户体验:** 用户配置Git仓库访问方式的便捷性。
* **功能需求:** ArchScope需要足够的权限来克隆/拉取代码，以及（未来）可能需要的Webhook管理权限。
* **可维护性:** ArchScope后端处理不同认证方式的复杂性。
* **Git提供商支持:** GitHub, GitLab等主流平台对PAT和OAuth App的支持程度。

**考虑的方案 (Considered Options):**

* **方案1: 仅支持用户提供Personal Access Token (PAT)**
    * 用户为其在ArchScope中注册的每个项目提供一个针对该仓库的PAT。ArchScope存储并使用此PAT进行Git操作。
    * **优点:**
        * 实现相对简单：ArchScope只需安全存储和使用PAT即可。
        * 用户对其提供的PAT有完全控制权，可以设置权限范围和有效期。
    * **缺点:**
        * **安全性风险较高:** PAT通常权限较大（除非用户精确配置了fine-grained PAT）。如果ArchScope的PAT存储被攻破，可能导致用户仓库受损。
        * **用户管理负担:** 用户需要手动创建、管理、轮换PAT，并确保其具有正确的权限。PAT泄露风险在用户端。
        * **权限范围可能过大或不足:** 用户可能不清楚如何配置最小权限的PAT。

* **方案2: 仅支持系统级OAuth App集成**
    * ArchScope在每个支持的Git提供商平台（如GitHub, GitLab）注册为一个OAuth App。用户在ArchScope中添加项目时，通过标准的OAuth 2.0流程授权ArchScope访问其仓库。ArchScope获取access token和refresh token。
    * **优点:**
        * **更安全:** 遵循OAuth 2.0标准，权限由用户明确授予，令牌由系统管理和刷新，通常有时效性。可以请求更细粒度的权限范围（scopes）。
        * **用户体验更好:** 用户只需通过熟悉的OAuth授权流程点击授权，无需手动生成和复制粘贴Token。
        * **集中管理:** ArchScope可以更好地管理对Git提供商API的访问（如统一处理速率限制）。
    * **缺点:**
        * **ArchScope初始设置复杂:** 需要在各个Git平台注册和配置OAuth App。
        * **后端实现复杂:** 需要完整实现OAuth 2.0客户端逻辑（授权码流程、令牌刷新、状态管理、回调处理）。
        * **用户可能不希望授予第三方App权限。**

* **方案3: 同时支持PAT和OAuth App两种方式**
    * 为用户提供选择，既可以使用便捷但可能风险稍高的PAT方式，也可以使用更安全但配置略复杂的OAuth App授权方式。
    * **优点:** 灵活性最高，可以满足不同用户或场景的需求。例如，对于私有部署的GitLab实例，OAuth App配置可能更复杂，用户可能倾向于使用PAT。对于公有GitHub，OAuth App体验更好。
    * **缺点:** ArchScope后端需要同时实现和维护两种认证逻辑，增加了代码复杂性。用户界面也需要引导用户选择和配置。

* **方案4: SSH密钥认证**
    * 用户为ArchScope生成一个专用的SSH密钥对，并将公钥添加到其Git仓库的部署密钥中。ArchScope使用私钥进行Git操作。
    * **优点:** 较安全，权限可以限制到特定仓库。
    * **缺点:** 用户配置相对复杂（生成密钥、添加公钥）。ArchScope需要安全存储和管理私钥。不便于通过API进行某些元数据操作（SSH主要用于Git协议本身）。

**决策 (Decision):**

我们决定采用 **方案3: 同时支持用户提供Personal Access Token (PAT) 和 系统级OAuth App集成 两种方式** 来与Git仓库进行交互。

* **MVP阶段，可以优先实现PAT方式**，因为它对ArchScope后端的实现复杂度较低，可以快速支持核心的代码拉取功能。
* **OAuth App方式作为后续增强或并行开发**，以提供更优的安全性和用户体验，特别是针对公有云Git服务提供商。

理由:
1.  **灵活性与用户选择:** 提供了多种选项以适应不同的用户偏好、技术环境（如内部GitLab vs 公有GitHub）和安全需求。
2.  **安全性平衡:** OAuth App是更推荐的安全实践，但PAT的便捷性对某些用户或场景仍有价值。通过明确告知用户各种方式的利弊，让用户做出选择。
3.  **功能覆盖:** 两种方式都能满足ArchScope核心的代码克隆/拉取需求。OAuth App在未来管理Webhook等高级集成时更有优势。
4.  **逐步实现:** 可以在MVP阶段先实现一种（如PAT），后续迭代中加入另一种，平滑演进。

**后果 (Consequences):**

* **积极的:**
    * 提高了ArchScope对不同Git环境和用户习惯的适应性。
    * 用户可以选择最适合其场景的认证方式。
    * 为未来更深入的Git集成（如Webhook自动配置）通过OAuth App打下基础。
* **消极的/风险/需要注意的:**
    * **后端复杂度增加:** `archscope-infrastructure`中的`GitServiceAdapter`需要支持处理两种不同的认证机制。
    * **用户界面复杂度增加:** 项目注册和配置界面需要清晰地引导用户选择和设置PAT或完成OAuth授权。
    * **PAT安全存储是关键:** 如果用户选择PAT，ArchScope必须使用强加密机制（如K8s Secrets + 应用层加密）来存储这些PAT。
    * **OAuth App管理:** 需要在目标Git平台上创建和维护ArchScope的OAuth App配置（client ID, client secret, redirect URIs）。Client secret同样需要安全存储。
* **需要进一步的工作/决策:**
    * 详细设计`GitServiceAdapter`的接口，使其能够处理不同类型的认证凭证。
    * 确定MVP阶段优先实现的认证方式（已建议先PAT）。
    * 设计用户界面，清晰地引导用户配置Git仓库访问。
    * 制定PAT和OAuth App凭证（如client secret, refresh tokens）的安全存储和管理策略。
    * 为每种支持的Git提供商（GitHub, GitLab等）明确所需的OAuth scopes。

**其他相关信息 (Optional):**
* ArchScope的Git操作将使用HTTPS协议，不直接使用SSH协议。

**最后更新日期:** 2025-05-12
**决策者:** ArchScope项目架构团队 (客户反馈整合)

---

**ADR-008: LLM服务集成策略 (OpenRouter首选)**

**状态:** 已接受 (Accepted)

**上下文 (Context):**
ArchScope计划利用大语言模型 (LLM) 来增强代码分析能力，例如进行语义理解、代码摘要、识别代码异味、辅助设计模式识别等（这些多为MVP后功能，但MVP阶段可能需要极简的LLM调用，如获取文件摘要）。我们需要选择一种灵活、可扩展且（在可能的情况下）成本可控的LLM服务集成策略。客户已指明外部LLM API首选OpenRouter。

**决策驱动因素 (Decision Drivers):**
* **模型多样性与灵活性:** 希望能够访问多种不同的LLM模型，并根据任务需求和成本效益选择最合适的模型。
* **API统一性:** 最好通过一个相对统一的API接口与不同的LLM后端进行交互，降低集成和切换成本。
* **成本控制:** 能够比较不同模型的成本，并可能利用开源模型或更经济的商业模型。
* **集成复杂度:** 集成LLM服务的开发工作量。
* **未来支持本地模型:** 架构应能方便地扩展以支持本地部署的LLM。
* **客户偏好:** 已明确OpenRouter为首选外部API。
* **NFR-SEC-004 (LLM交互安全):** 需要考虑Prompt注入、数据隐私等问题。

**考虑的方案 (Considered Options):**

* **方案1: 直接集成特定LLM提供商API (如 OpenAI, Anthropic, Google Vertex AI)**
    * 为每个希望支持的LLM提供商分别编写API客户端和适配逻辑。
    * **优点:** 可以充分利用特定提供商的独有特性和优化。
    * **缺点:**
        * **高耦合:** 与特定提供商API绑定，切换成本高。
        * **重复开发:** 每个提供商都需要独立的集成工作。
        * **模型选择受限:** 只能使用已集成的提供商的模型。
        * **成本管理分散:** 需要管理多个提供商的账户和计费。

* **方案2: 集成LLM API网关 - OpenRouter**
    * OpenRouter提供一个统一的API接口，可以访问来自不同提供商的多种LLM模型（包括开源模型如Llama系列、Mistral系列，以及商业模型如GPT系列、Claude系列）。用户使用自己的OpenRouter API Key付费。
    * **优点:**
        * **模型选择极其灵活:** 通过一个API即可访问大量模型，方便实验和切换。
        * **API统一性:** ArchScope只需对接OpenRouter的API规范。
        * **潜在成本优化:** OpenRouter本身可能提供一些模型的成本优势，或者方便用户比较和选择性价比高的模型。
        * **简化API Key管理:** ArchScope只需配置OpenRouter的API Key（或者让用户提供其自己的OpenRouter Key）。
    * **缺点:**
        * **依赖第三方网关:** 增加了一个中间层，OpenRouter自身的可用性和性能会影响ArchScope。
        * **可能无法使用最新或特定提供商的独有API特性:** 网关API通常是标准化的，可能无法立即支持某个模型提供商的最新Beta功能。
        * **数据隐私路径增加:** 数据会经过OpenRouter，需要信任其数据处理策略（尽管OpenRouter声明其不存储请求数据）。

* **方案3: 自建LLM推理服务并集成 (针对本地模型选项)**
    * 在私有云K8s环境中部署开源LLM（如使用Ollama, vLLM, TGI等框架），并提供内部API供ArchScope调用。
    * **优点:** 数据完全私有可控，无外部API调用成本（但有硬件和运维成本）。
    * **缺点:** 部署和运维复杂，需要GPU等昂贵硬件资源。模型选择和更新管理有挑战。性能调优复杂。这是“本地模型选项”的实现方式，但作为唯一的LLM集成策略则过于沉重。

**决策 (Decision):**

我们决定采用 **方案2: 集成LLM API网关 - OpenRouter** 作为ArchScope与外部LLM服务交互的首选和主要方式。
同时，在`archscope-infrastructure`中设计一个**LLM服务适配器层 (LLMAdapter)**，该适配器层初期主要实现对OpenRouter API的调用，但其接口设计应具备一定的通用性，以便未来可以：
a.  直接集成其他特定LLM提供商的API（如果OpenRouter无法满足某些需求）。
b.  集成内部自建的LLM推理服务API（实现“本地模型选项”）。

理由:
1.  **满足客户偏好:** OpenRouter已被指定为首选。
2.  **模型灵活性最大化:** OpenRouter提供了访问大量不同模型的便捷途径，非常适合ArchScope需要根据不同代码分析任务（如摘要、结构化提取、异味识别）选择不同特长或成本效益模型的场景。
3.  **简化初期集成:** ArchScope只需关注对接一个相对统一的API (OpenRouter的API)，而不是多个不同LLM提供商的API。
4.  **成本透明与控制:** 用户使用其OpenRouter账户，可以自行管理其LLM使用成本和模型选择。
5.  **适配器层支持未来扩展:** 通过`LLMAdapter`的设计，可以在不修改核心业务逻辑（`archscope-app`, `archscope-domain`）的情况下，平滑地增加对其他LLM源（包括本地模型）的支持。

**后果 (Consequences):**

* **积极的:**
    * 大大增强了ArchScope在LLM模型选择上的灵活性和未来适应性。
    * 简化了多LLM提供商的直接集成工作。
    * 用户可以利用OpenRouter的生态和可能的成本优势。
* **消极的/风险/需要注意的:**
    * **依赖OpenRouter:** ArchScope的LLM功能将依赖OpenRouter服务的可用性、性能和政策。需要有监控和可能的降级策略（例如，如果OpenRouter不可用，暂时禁用LLM增强功能或切换到备用方案）。
    * **数据隐私:** 虽然OpenRouter通常声明不存储数据，但数据流经第三方服务的事实需要在隐私政策中向用户说明。本地模型选项是最终解决此问题的途径。
    * **OpenRouter API特性集:** ArchScope能使用的LLM特性受限于OpenRouter API提供的功能。
    * **API Key管理:** 需要安全地管理ArchScope系统自身的OpenRouter API Key（如果系统统一使用一个Key），或者设计机制让用户安全地提供和管理他们自己的OpenRouter Key。
* **需要进一步的工作/决策:**
    * 详细研究OpenRouter API的认证方式、请求/响应格式、错误处理、支持的模型列表及特性（如流式输出、JSON模式等）。
    * 设计`LLMAdapter`的通用接口，使其能够抽象不同LLM的调用细节。
    * 实现`OpenRouterLlmClientAdapterImpl`。
    * 制定LLM交互的安全策略（Prompt净化、输出过滤）在`LLMAdapter`或调用方（如`CodeAnalysisApplicationService`）中的实现。
    * MVP阶段的LLM集成将非常简化（可能仅用于非常基础的文本处理），复杂的LLM分析功能将在后续阶段基于此适配器逐步实现。

**其他相关信息 (Optional):**
* ArchScope的LLM Prompt设计将是另一个关键工作，需要针对不同分析任务和目标模型进行优化。

**最后更新日期:** 2025-05-12
**决策者:** ArchScope项目架构团队 (客户反馈整合)

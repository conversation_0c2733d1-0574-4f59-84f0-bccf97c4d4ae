### ADR-004: 数据持久化策略 (MySQL + Neo4j + Redis自建方案)

**状态:** 已接受 (Accepted)

**上下文 (Context):**
ArchScope系统需要持久化存储多种类型的数据，包括项目元数据、用户和任务信息（关系型）、代码知识图谱（图结构）、以及可能的缓存数据。我们需要选择合适的数据库技术来满足这些不同的存储需求，并考虑在客户私有云Kubernetes环境中自建这些数据库的运维和管理。

**决策驱动因素 (Decision Drivers):**
* **数据类型与查询需求:**
    * 项目、任务、配置等具有清晰结构和固定关系的数据，适合关系型数据库。
    * 代码元素（类、方法、文件等）及其复杂的依赖关系（调用、继承、实现等）天然适合图数据库。
    * 需要快速访问的热点数据或计算成本高的查询结果适合缓存。
* **NFR-PERF (性能):** 数据库读写性能，特别是图数据库在复杂查询下的性能。
* **NFR-SCALE (可扩展性):** 数据库应对数据量增长和并发访问增加的能力。
* **NFR-AVAIL (可用性):** 数据库的高可用和数据备份恢复能力。
* **NFR-MAINTAIN (可维护性):** 数据库的运维复杂度，社区支持，团队熟悉程度。
* **技术栈偏好 (来自`tasks.json`):** 已有使用MySQL和Redis的经验。
* **部署环境:** 私有云K8s，自建数据库。
* **成本:** 开源数据库方案优先。

**考虑的方案 (Considered Options):**

* **方案1: 仅使用关系型数据库 (如MySQL)**
    * 所有数据（包括图结构数据通过邻接表或嵌套集等方式模拟）都存储在MySQL中。
    * **优点:** 技术栈统一，运维相对简单，团队熟悉。
    * **缺点:** 使用关系型数据库模拟图结构，对于复杂的图查询（如多跳依赖、路径查找、模式匹配）性能低下且查询语句复杂，难以满足代码知识图谱的分析需求。

* **方案2: 关系型数据库 (MySQL) + 图数据库 (Neo4j) + 缓存 (Redis) - Polyglot Persistence**
    * **MySQL:** 存储项目元数据、任务信息、用户配置等结构化数据。
    * **Neo4j:** 专门存储代码解析后形成的代码知识图谱（节点如类、方法；关系如调用、继承）。
    * **Redis:** 作为缓存层，存储热点数据、会话信息（如果需要）、分布式锁等。
    * **优点:** 每种数据类型都由最适合的数据库技术来处理，性能和功能最优。Neo4j对图查询有原生高效支持。Redis提供高性能缓存。
    * **缺点:** 引入多种数据库技术，增加了系统的复杂性和运维成本（尽管在K8s统一管理下有所缓解）。需要处理跨不同数据存储的数据一致性问题（通常是最终一致性）。

* **方案3: 仅使用图数据库 (如Neo4j)**
    * 尝试将所有数据都模型化并存储在Neo4j中，包括传统的关系型数据。
    * **优点:** 数据模型统一在图结构中。
    * **缺点:** Neo4j并非为所有类型的关系型数据查询和事务处理优化。对于简单的CRUD和报表类查询，关系型数据库通常更高效和方便。运维和团队技能要求较高。

* **方案4: 关系型数据库 (MySQL) + 文档数据库 (MongoDB) + 缓存 (Redis)**
    * 文档数据库存储半结构化的解析结果或文档内容。
    * **优点:** MongoDB对半结构化数据友好。
    * **缺点:** 代码依赖关系本质上是图结构，MongoDB的图处理能力不如原生图数据库Neo4j。

**决策 (Decision):**

我们决定采用 **方案2: 关系型数据库 (MySQL) + 图数据库 (Neo4j) + 缓存 (Redis) - Polyglot Persistence**。

* **MySQL (8.0.x):** 用于存储项目的元数据 (`projects`表)、异步任务的状态和详情 (`tasks`表)、系统配置、以及未来可能的用户偏好设置等具有明确结构和关系的数据。
* **Neo4j (5.x Community Edition for MVP):** 专门用于存储和查询代码分析产生的代码知识图谱。节点代表代码元素（包、类、接口、方法、字段等），关系代表它们之间的依赖（继承、实现、调用、引用等）。
* **Redis (7.x):** 作为高速缓存层，用于缓存常用的查询结果（如项目列表摘要、公开文档树结构、热门文档内容片段），以及未来可能的分布式锁或计数器等。

理由:
1.  **最佳技术匹配:** 为不同类型的数据选择最适合的存储技术，能够最大化发挥各自优势。MySQL擅长处理结构化事务数据，Neo4j是处理高度连接的图数据的行业领导者，Redis提供高性能的键值缓存。
2.  **满足核心需求:** 代码知识图谱是ArchScope的核心价值之一，使用原生图数据库Neo4j能够高效支持复杂的依赖分析、结构可视化和未来可能的架构规则校验。
3.  **性能考量:** 将图数据从关系型数据库中分离出来，避免了在MySQL中进行低效的递归查询或复杂的JOIN操作。Redis缓存可以显著提升高频读操作的性能。
4.  **团队已有经验:** `tasks.json`表明团队对MySQL和Redis已有使用经验。Neo4j的学习曲线对于图数据建模是必要的投入。
5.  **K8s自建可行性:** MySQL, Neo4j (Community Edition), 和 Redis 都有成熟的在Kubernetes上自建部署的方案 (如使用Operator或Helm charts)，可以与企业的统一备份、监控、日志平台集成。

**后果 (Consequences):**

* **积极的:**
    * 代码分析和图谱查询的性能和功能将得到有力保障。
    * 关系型数据的管理保持高效和成熟。
    * 系统整体性能可以通过缓存得到优化。
    * 为未来更高级的代码分析功能（如路径分析、影响分析）奠定了良好的数据基础。
* **消极的/风险/需要注意的:**
    * **运维复杂度增加:** 需要在K8s环境中运维和管理三种不同的数据存储服务。这要求团队具备相应的技能，或依赖良好的K8s Operator和自动化脚本。
    * **数据一致性:** 需要处理MySQL和Neo4j之间数据的一致性问题。例如，项目在MySQL中创建后，其对应的图数据在Neo4j中创建。这通常通过应用层的逻辑或异步事件来保证最终一致性。MVP阶段，可以简化为先在一个存储中成功写入，再写入另一个。
    * **开发人员技能要求:** 开发团队需要同时掌握关系型数据库和图数据库的建模与查询技术（SQL 和 Cypher）。
    * **资源消耗:** 在K8s集群中运行多个数据库实例会消耗更多的计算和存储资源。
* **需要进一步的工作/决策:**
    * 详细设计MySQL的表结构和索引 (已在`architecture.md`中初步完成)。
    * 详细设计Neo4j的图模型（节点标签、属性、关系类型）和索引 (已在`architecture.md`中初步完成)。
    * 明确Redis的具体缓存策略（缓存哪些数据、缓存失效机制、缓存键设计）。
    * 制定自建MySQL, Neo4j, Redis在K8s上的详细部署、备份、监控和高可用方案。
    * 设计跨数据存储的数据同步或一致性保障机制（如果需要强一致性，MVP阶段可能采用应用层保证）。

**其他相关信息 (Optional):**
* 参考 `architecture.md` 中数据模型设计的详细描述。

**最后更新日期:** 2025-05-12
**决策者:** ArchScope项目架构团队
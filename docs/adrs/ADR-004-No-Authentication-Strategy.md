# ADR-004: 无认证策略实施

**状态:** 已接受 (Accepted)

**日期:** 2025-08-05

## 上下文 (Context)

根据项目实际需求和用户偏好，ArchScope系统需要采用完全开放的访问模式，移除所有认证和授权要求。这与之前的ADR-003中定义的SSO认证策略不同，现在需要实现一个完全无认证的系统架构。

## 决策驱动因素 (Decision Drivers)

* **用户偏好**: 明确要求移除所有认证相关的接口、依赖和处理
* **简化部署**: 无认证系统更容易部署和维护
* **开发效率**: 避免复杂的认证集成，专注于核心业务功能
* **访问便利性**: 用户可以直接访问所有功能，无需登录流程

## 决策 (Decision)

我们决定采用**完全无认证策略**：

### 1. 系统访问模式
- **所有API端点**均可匿名访问，无需任何认证
- **项目管理功能**（创建、修改、删除）开放给所有用户
- **任务管理功能**（查看、操作、监控）开放给所有用户
- **文档访问功能**完全开放

### 2. 安全配置
- Spring Security配置为`permitAll()`模式
- 禁用CSRF、会话管理、HTTP Basic等认证机制
- 移除所有认证相关的过滤器和拦截器

### 3. API设计
- 移除所有认证相关的请求头要求
- API文档中不包含认证说明
- 所有接口均标记为公开访问

## 实施细节 (Implementation)

### 1. Spring Security配置
```java
@Configuration
@EnableWebSecurity
public class SecurityConfig {
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        return http
            .csrf().disable()
            .authorizeHttpRequests(authz -> authz.anyRequest().permitAll())
            .sessionManagement().disable()
            .httpBasic().disable()
            .formLogin().disable()
            .logout().disable()
            .build();
    }
}
```

### 2. API文档更新
- 移除OpenAPI配置中的认证方案定义
- 更新API文档说明为"开放访问模式"
- 移除所有认证相关的示例和说明

### 3. 前端适配
- 移除所有登录/登出相关的UI组件
- 移除认证状态管理逻辑
- 简化API调用，无需携带认证信息

## 后果 (Consequences)

### 积极影响
* **部署简化**: 无需配置SSO、用户管理等复杂组件
* **开发效率**: 专注于业务功能开发，无需处理认证逻辑
* **用户体验**: 用户可以直接使用所有功能，无需注册登录
* **维护成本**: 降低系统复杂度和维护成本

### 需要注意的风险
* **数据安全**: 所有数据和功能完全开放，需要在网络层面进行访问控制
* **操作审计**: 无法追踪具体的操作用户，需要通过其他方式记录操作日志
* **恶意使用**: 需要在应用层面实现防滥用机制（如限流、监控）

### 缓解措施
1. **网络安全**: 通过防火墙、VPN等网络层面控制访问
2. **操作日志**: 记录IP地址、操作时间等信息用于审计
3. **监控告警**: 实施异常操作检测和告警机制
4. **数据备份**: 定期备份重要数据，防止意外删除或修改

## 相关文档

* 本ADR替代了ADR-003中的认证策略
* 参考`SecurityConfig.java`的具体实现
* 参考更新后的API文档说明

## 最后更新

**日期**: 2025-08-05
**决策者**: ArchScope项目团队
**状态**: 已实施

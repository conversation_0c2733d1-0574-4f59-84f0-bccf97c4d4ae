### ADR-001: 后端架构模式选择 (模块化单体)

**状态:** 已接受 (Accepted)

**上下文 (Context):**
ArchScope系统需要一个稳定、可维护且能支持MVP阶段快速迭代的后端架构。系统核心功能包括项目管理、代码解析（Java MVP）、文档生成与网站托管、任务管理，并计划部署在客户的私有云Kubernetes环境中，使用自建的数据库和消息队列。项目初期规模相对较小，开发团队紧密协作。我们需要选择一种合适的架构模式来平衡开发效率、系统性能、可扩展性和长期可维护性。

**决策驱动因素 (Decision Drivers):**
* **FR (综合):** 满足MVP阶段的核心功能需求，并为后续阶段的功能扩展（如多语言支持、高级LLM分析、健康度评估）提供基础。
* **NFR-MAINTAIN-006 (DDD分层):** 需求明确要求后端采用领域驱动设计原则。
* **NFR-DEPLOY-001, 003 (容器化, 本地开发):** 易于容器化部署和本地开发环境搭建。
* **项目目标 (快速交付MVP):** MVP阶段需要快速迭代和验证核心价值。
* **团队规模与协作:** 初期团队规模较小，紧密协作。
* **运维复杂度:** 考虑到在私有云K8s自建核心组件，架构本身的运维复杂度不宜过高。
* **未来演进:** 架构应具备未来向更细粒度服务（如微服务）演进的潜力。

**考虑的方案 (Considered Options):**

* **方案1: 微服务架构 (Microservices Architecture)**
    * 将系统拆分为多个独立部署的小型服务（如项目服务、解析服务、文档服务、任务服务等），每个服务有自己的数据存储（或共享但隔离）。服务间通过API或消息队列通信。
    * **优点:** 高度解耦，独立扩展，技术栈灵活，故障隔离性好，适合大型复杂系统和大规模团队。
    * **缺点:** 开发和运维复杂度高（分布式事务、服务发现、API网关、监控链路），进程间通信开销，不适合小型团队和项目初期快速迭代，可能导致过度拆分。

* **方案2: 模块化单体架构 (Modular Monolith)**
    * 将系统构建为单个部署单元，但内部通过清晰的模块边界（如Maven模块）划分职责，遵循高内聚、低耦合原则。模块间主要通过进程内方法调用进行交互。
    * **优点:** 开发效率高，部署简单，测试相对容易，事务管理简单，进程内调用性能好。良好的模块化可以为未来拆分微服务打下基础。
    * **缺点:** 整体扩展性不如微服务，单个模块的性能瓶颈可能影响整个应用，技术栈通常较为统一，大型项目后期可能变得臃肿。

* **方案3: 分层单体架构 (Layered Monolith - 非严格模块化)**
    * 也是单个部署单元，按技术分层（如展现层、业务逻辑层、数据访问层），但模块化边界可能不清晰，模块间耦合度可能较高。
    * **优点:** 相对简单，易于理解。
    * **缺点:** 长期可维护性差，容易形成“大泥球”，不利于独立测试和未来演进。

**决策 (Decision):**

我们决定采用 **方案2: 模块化单体架构 (Modular Monolith)**，并结合领域驱动设计 (DDD) 和六边形架构思想来组织内部模块。

理由:
1.  **满足MVP快速迭代需求:** 模块化单体开发效率高，部署简单，能够支持MVP阶段的快速功能交付和验证。
2.  **符合团队和项目规模:** 对于初期的小规模系统和紧密协作的团队，单体架构更易于管理和协调。
3.  **降低初期运维复杂度:** 相比微服务，单体应用在私有云K8s环境中的部署和运维相对简单，尤其是在核心依赖组件（DB, MQ）也采用自建方案的情况下。
4.  **支持DDD和六边形架构:** 模块化设计允许我们在单体内部严格实践DDD和六边形架构，实现高内聚、低耦合，保证代码质量和可维护性（满足NFR-MAINTAIN-006）。
5.  **为未来演进奠定基础:** 清晰的模块边界和定义的接口，使得未来当系统规模增长或特定模块成为瓶颈时，更容易将其平滑地拆分为独立的微服务。
6.  **性能优势:** 模块间进程内调用，避免了微服务架构中网络通信带来的延迟和复杂性，对API响应时间有利。异步耗时任务已通过RocketMQ解耦。

**后果 (Consequences):**

* **积极的:**
    * 开发启动速度快，MVP交付周期有望缩短。
    * 初期部署和运维成本较低。
    * 团队成员更容易理解整个系统。
    * 事务管理简单。
    * 代码更容易在模块间进行重构（相比跨微服务重构）。
* **消极的/风险/需要注意的:**
    * **模块边界纪律：** 团队必须严格遵守模块化设计原则，通过接口进行模块间交互，避免形成隐性耦合。需要代码审查和规范来保障。
    * **整体扩展性受限：** 虽然可以通过K8s水平复制整个单体实例，但无法像微服务那样针对性地扩展某个高负载模块。需通过异步化和缓存等手段优化性能瓶颈点。
    * **单一故障点：** 单体应用中一个模块的严重错误可能影响整个应用的可用性（尽管K8s的自愈能力可以缓解）。
    * **技术栈统一：** 整个应用将共享主要的技术栈（Java, Spring Boot）。
* **需要进一步的工作/决策:**
    * 详细定义各模块的职责边界和接口（已在`architecture.md`中初步完成）。
    * 建立严格的代码规范和审查流程，确保模块化设计的实施质量。
    * 设计有效的异步处理机制，将耗时和资源密集型操作（如代码解析）与主应用流程解耦。

**其他相关信息 (Optional):**
* 参考 `architecture.md` 中关于模块化单体和DDD六边形架构的详细阐述。

**最后更新日期:** 2025-05-12
**决策者:** ArchScope项目架构团队 (由AI架构师主导，客户确认)
# ArchScope UI 实现指南

本文档为ArchScope前端开发提供指导，说明如何基于`docs/prototype`目录中的界面原型实现实际UI。

## 前期准备

1. **了解原型**
   - 阅读 [界面原型分析文档](ui-prototype-analysis.md)
   - 查看 [原型目录README](../prototype/README.md)
   - 在浏览器中打开各原型HTML文件，了解整体交互流程

2. **技术栈确认**
   - Vue 3 
   - TypeScript
   - Tailwind CSS
   - 字体图标库（FontAwesome或类似方案）

## 实现路径

按照以下优先级逐步实现UI：

### 1. 基础架构搭建 (第1周)

- 创建Vue 3项目架构
- 集成Tailwind CSS
- 设置TypeScript
- 创建基础布局组件
- 实现全局样式变量（颜色、间距等）

### 2. 核心组件库 (第2周)

实现以下可复用组件：

- 导航栏组件
- 侧边栏组件
- 卡片组件（带悬停效果）
- 按钮组件（含各种状态）
- 表单元素组件
- 表格组件
- 加载状态组件
- 通知/提示组件

### 3. 页面实现 (第3-5周)

按优先级依次实现：

1. **项目列表页面** (`project_list.html`)
   - 实现项目卡片列表
   - 实现搜索和过滤功能
   - 实现分页

2. **项目注册页面** (`register_project.html`)
   - 实现表单及验证
   - 实现高级选项折叠面板
   - 实现表单提交与响应

3. **项目详情页面** (`project_detail.html`)
   - 实现项目信息展示区
   - 实现统计数据可视化
   - 实现标签与操作按钮

4. **基础文档页面** (以`project_doc_home.html`为模板)
   - 实现文档侧边栏导航
   - 实现文档内容区域
   - 实现版本选择功能

5. **任务队列页面** (`task_queue.html`)
   - 实现任务列表
   - 实现任务状态展示
   - 实现任务操作按钮

### 4. 动画与交互 (持续优化)

在基础功能实现后添加：

- 页面过渡动画
- 元素悬停效果
- 按钮点击涟漪效果
- 滚动加载效果
- 表单交互反馈

## 关键样式规范

请严格遵循以下设计规范：

### 颜色系统

```css
:root {
  /* 主要颜色 */
  --primary: #4F46E5;        /* Indigo-600 */
  --primary-hover: #4338CA;  /* Indigo-700 */
  
  /* 侧边栏颜色 */
  --sidebar-bg: #1E293B;     /* Slate-800 */
  --sidebar-header-bg: #0F172A; /* Slate-900 */
  
  /* 文本颜色 */
  --text-dark: #1E293B;      /* Slate-800 */
  --text-medium: #64748B;    /* Slate-500 */
  --text-light: #E2E8F0;     /* Slate-200 */
  
  /* 背景颜色 */
  --bg-light: #F8FAFC;       /* Slate-50 */
  --bg-medium: #F1F5F9;      /* Slate-100 */
  
  /* 状态颜色 */
  --success: #10B981;        /* Emerald-500 */
  --warning: #F59E0B;        /* Amber-500 */
  --error: #EF4444;          /* Red-500 */
  --info: #3B82F6;           /* Blue-500 */
  
  /* 评分颜色 */
  --star-filled: #FBBF24;    /* Amber-400 */
  --star-empty: #E5E7EB;     /* Gray-200 */
}
```

### 排版

- 标题层级遵循原型中的字体大小和粗细
- 主体文本使用 16px（基础）和 14px（次要内容）
- 标签和提示使用 12px

### 间距系统

- 遵循Tailwind的间距系统
- 页面外边距：1.5rem (24px)
- 卡片内边距：1rem (16px)
- 元素间距：0.75rem (12px)
- 区块间距：2rem (32px)

### 圆角

- 按钮和表单：0.375rem (6px)
- 卡片：0.5rem (8px)
- 小型UI元素：0.25rem (4px)

### 阴影

- 卡片正常状态：`0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)`
- 卡片悬停状态：`0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)`
- 下拉菜单：`0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)`

## 响应式设计

实现以下断点的响应式布局：

- 手机：< 640px
- 平板：640px - 1024px
- 桌面：> 1024px

针对移动设备的特殊处理：
- 侧边栏转为可折叠菜单
- 表格转为卡片列表
- 减少每行显示的元素数量
- 调整页面标题和导航位置

## 测试与验证

对每个实现的页面进行以下测试：

1. 功能测试：确保所有交互元素功能正常
2. 响应式测试：在不同屏幕尺寸下检查布局
3. 视觉对比：与原型进行视觉对比，确保一致性
4. 可访问性测试：检查颜色对比度、键盘导航等
5. 性能测试：检查页面加载速度和动画性能

## 与后端集成

- 所有API调用应通过统一的服务层
- 使用Pinia进行状态管理
- 实现数据加载和错误状态的处理
- 提供模拟数据用于开发和测试

## 资源链接

- [Vue 3 文档](https://v3.cn.vuejs.org/)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [TypeScript 文档](https://www.typescriptlang.org/docs/)
- [项目原型文件](../prototype/) 
# ArchScope 用户故事 (User Stories)

## 1. 引言

本文档包含ArchScope系统的用户故事，旨在从最终用户的角度描述系统功能和价值。这些用户故事将指导开发团队的功能实现和验收测试。用户故事主要围绕已定义的MVP（最小可行产品）范围以及后续阶段的核心功能进行阐述。

## 2. 用户画像回顾

* **技术团队管理者 (Tech Lead / Engineering Manager - TLM):** 关注效率、健康度、风险。
* **开发人员 (Developer - DEV):** 关注实现细节、依赖、API、问题排查。
* **架构师 (Architect - ARCH):** 关注架构一致性、技术选型、风险点。
* **新成员 (New Joiner - NJ):** 关注快速上手、项目概览。
* **系统管理员 (Admin - ADM):** 关注系统运维、用户管理、配置。
* **外部协作者/使用方 (External Party - EXT):** 关注集成、API文档。

*(注：由于MVP阶段简化了用户角色管理并依赖SSO，部分故事中的“角色”更多指其在团队中的职责而非系统内的精确权限角色)*

## 3. MVP 用户故事

### 3.1 认证与访问 (MVP)

* **US-MVP-AUTH-001 (通过SSO登录):**
    * 作为一名 **开发者 (DEV)**，
    * 我想要 **通过公司的单点登录 (SSO) 系统无缝登录到ArchScope**，
    * 以便 **我不需要记住额外的用户名和密码就能访问系统**。
    * *验收标准:*
        * 用户访问ArchScope时，若无会话，则被重定向到SSO提供商登录页面。
        * 成功通过SSO认证后，用户被重定向回ArchScope并成功登录。
        * ArchScope能够正确解析SSO断言并识别用户身份。

* **US-MVP-AUTH-002 (访问授权内容):**
    * 作为一名 **开发者 (DEV)**，
    * 我想要 **在通过SSO登录后，能够访问我被授权查看的项目和文档**，
    * 以便 **我能获取工作所需的信息，同时系统的访问控制得到保障**。
    * *验收标准:*
        * 用户只能看到SSO用户组/角色映射到ArchScope权限所允许的项目列表。
        * 尝试访问未授权的项目或功能时，应收到明确的权限不足提示。

### 3.2 项目管理 (MVP)

* **US-MVP-PM-001 (注册Java项目):**
    * 作为一名 **开发者 (DEV)** 或 **技术团队管理者 (TLM)** (拥有项目创建权限)，
    * 我想要 **通过提供项目名称和Java项目的Git仓库URL来注册一个新项目** 到ArchScope中，
    * 以便 **系统能够开始分析该Java项目并为其生成初始文档**。
    * *验收标准:*
        * 用户可以在"注册项目"页面 (`register_project.html` 概念) 输入项目名称、Git URL (仅Java项目)。
        * 提交后，项目成功在ArchScope中创建。
        * 系统后台自动为该项目创建一个初始代码解析任务。
        * 用户可以在项目列表 (`project_list.html` 概念) 中看到新注册的项目。

* **US-MVP-PM-002 (查看项目列表):**
    * 作为一名 **开发者 (DEV)**，
    * 我想要 **查看一个包含我所有可访问项目的列表页面**，列表中应显示项目名称、仓库类型（如GitHub/GitLab）和最后活动/分析时间，
    * 以便 **我能快速找到并导航到我需要关注的项目**。
    * *验收标准:*
        * `project_list.html` (概念) 正确显示用户有权访问的项目。
        * 每个项目条目至少显示名称、仓库类型图标、最后分析日期。
        * 列表支持基本的分页（如果项目数量多）。

* **US-MVP-PM-003 (触发Java项目解析):**
    * 作为一名 **开发者 (DEV)** (拥有项目操作权限)，
    * 我想要 **在特定Java项目的详情页面手动触发一次对该项目最新代码的全量解析**，
    * 以便 **在我知道代码有重要更新后，能确保ArchScope尽快基于最新代码生成文档**。
    * *验收标准:*
        * 在项目详情页 (`project_detail.html` 概念) 有一个"触发解析"按钮。
        * 点击后，后端成功创建一个针对该Java项目的全量代码解析任务。
        * 前端应显示任务已提交的反馈，并提供任务ID或链接供追踪。

### 3.3 代码解析与文档生成 (MVP - 针对Java)

* **US-MVP-CODE-001 (Java项目AST解析):**
    * 作为 **ArchScope系统 (SYSTEM)**，
    * 当一个Java项目的解析任务被触发时，我需要 **能够克隆/拉取Java代码，并使用AST技术分析其核心结构** (如包、类、接口、方法、字段)，
    * 以便 **为后续的文档生成和（未来）更深入的分析提供基础数据**。
    * *验收标准:*
        * 系统能成功拉取指定的Java项目代码。
        * AST解析能正确识别Java项目的包结构、主要的类/接口定义、方法签名。
        * 解析出的结构信息被初步存储到图数据库中。

* **US-MVP-DOCGEN-001 (生成基础Java项目文档):**
    * 作为 **ArchScope系统 (SYSTEM)**，
    * 当一个Java项目的代码解析成功完成后，我需要 **基于解析出的结构信息和预设的Java项目模板（Thymeleaf），自动生成基础的Markdown格式文档** (如项目结构概览、主要模块/包列表)，
    * 以便 **用户可以获得该Java项目的初步文档化成果**。
    * *验收标准:*
        * 系统能根据AST解析结果和固定模板生成Markdown文件。
        * 生成的Markdown文件包含项目名称、代码库中的主要包列表。
        * 每个包下可以列出主要的类/接口名称。

### 3.4 文档网站与查看 (MVP - 核心特性)

* **US-MVP-DOCSITE-001 (生成可访问的文档网站):**
    * 作为 **ArchScope系统 (SYSTEM)**，
    * 当一个项目的Markdown文档生成完成后，我需要 **将这些Markdown文件渲染成一个包含基础导航的静态HTML网站**，并使其可以在线访问，
    * 以便 **用户可以通过浏览器方便地阅读和浏览项目文档**。
    * *验收标准:*
        * 系统能将生成的Markdown文件集合转换为一组相互链接的HTML页面。
        * 生成的网站有一个唯一的访问URL（或路径）。
        * 用户可以通过该URL访问到文档网站首页。

* **US-MVP-DOCSITE-002 (浏览文档内容与导航):**
    * 作为一名 **开发者 (DEV)** 或 **新成员 (NJ)**，
    * 我想要 **在生成的文档网站上，通过左侧的导航树（基于项目的目录结构或文档结构）来浏览不同的文档章节和页面** (例如查看 `project_doc_home.html`, `project_doc_architecture.html` 概念页面)，
    * 以便 **我能系统地、有条理地阅读和理解项目文档**。
    * *验收标准:*
        * 文档网站左侧提供一个可展开/折叠的导航菜单。
        * 导航菜单的结构反映了文档的组织结构。
        * 点击导航菜单项能正确加载并显示对应的文档内容。
        * Java代码片段在文档中能正确高亮显示。

* **US-MVP-DOCSITE-003 (查看错误码指南):**
    * 作为一名 **开发者 (DEV)**，
    * 当我在使用ArchScope系统或其生成的文档遇到问题时，我想要 **能够访问一个错误码和解决方案的查看页面** (`error_codes_guide.html` 概念)，
    * 以便 **我能根据错误码快速找到问题的原因和可能的解决办法**。
    * *验收标准:*
        * 系统中有一个可访问的页面，列出常见的ArchScope系统错误码。
        * 每个错误码包含简明扼要的描述和建议的排查步骤或解决方案。
        * 此页面内容在MVP阶段可以由ArchScope团队手动维护更新。

### 3.5 任务管理 (MVP)

* **US-MVP-TASK-001 (查看异步任务状态):**
    * 作为一名 **开发者 (DEV)**，
    * 当我触发了一个耗时操作（如代码解析、文档生成）后，我想要 **能够通过一个任务ID轮询API来查看该任务的当前状态** (如排队中、运行中、成功、失败)，
    * 以便 **我能了解操作的进展情况，并在完成后得到通知或查看结果**。
    * *验收标准:*
        * 触发异步操作的API响应中包含一个唯一的任务ID。
        * 提供一个 `GET /tasks/{taskId}` API端点。
        * 调用该API能返回任务的ID、类型、当前状态、创建时间、开始/结束时间（如果适用）。
        * 前端可以通过轮询此API来更新任务状态显示。

* **US-MVP-TASK-002 (任务队列基本处理):**
    * 作为 **ArchScope系统 (SYSTEM)**，
    * 我需要 **使用RocketMQ消息队列来接收和调度后台任务**（如代码解析、文档生成），
    * 以便 **实现任务的异步处理，并为后续的优先级、重试等高级功能打下基础**。
    * *验收标准:*
        * 任务请求能成功发送到RocketMQ。
        * 消费者能从RocketMQ获取任务并执行。
        * `Task`表能记录任务的基本流转状态。
        * MVP阶段支持简单的失败记录和有限次数的自动重试（基于RocketMQ消费者机制）。

## 4. 后续阶段用户故事 (示例 - 非MVP)

### 4.1 项目管理 (后续)

* **US-POSTMVP-PM-001 (查看项目详情页完整信息):**
    * 作为一名 **架构师 (ARCH)**，
    * 我想要 **在项目详情页看到更全面的信息**，包括项目健康度星级、主要技术栈标签、代码统计（如LoC、文件数）、最近的提交活动、以及已生成的各类文档（如C4图、API文档）的快速入口，
    * 以便 **我对项目的整体状况有一个快速而完整的把握**。
    * *(对应 `project_detail.html` 的完整功能)*

* **US-POSTMVP-PM-002 (配置项目解析参数):**
    * 作为一名 **技术团队管理者 (TLM)**，
    * 我想要 **在项目设置中配置代码解析的一些参数**，例如选择特定的LLM模型（如果支持本地部署或多种云服务）、调整分析深度、或排除某些不想被分析的目录，
    * 以便 **根据项目特点和团队需求优化分析效果和成本**。
    * *(对应 `settings.html` 的部分功能)*

### 4.2 代码解析 (后续)

* **US-POSTMVP-CODE-001 (LLM辅助依赖识别):**
    * 作为 **ArchScope系统 (SYSTEM)**，
    * 在进行代码解析时，我需要 **利用LLM辅助识别AST难以捕捉的动态依赖或通过特定框架（如Spring）注入的依赖关系**，
    * 以便 **生成更准确、更全面的项目依赖图**。

* **US-POSTMVP-CODE-002 (设计模式识别):**
    * 作为一名 **架构师 (ARCH)**，
    * 我想要 **ArchScope能够自动识别我的代码中应用的关键设计模式** (如工厂模式、单例模式、策略模式等)，并在文档或分析报告中进行标注，
    * 以便 **我能快速了解代码的架构风格和复用策略，并检查其应用是否得当**。

* **US-POSTMVP-CODE-003 (增量解析与上下文维护):**
    * 作为 **ArchScope系统 (SYSTEM)**，
    * 当项目代码发生少量变更时，我需要 **只对变更及其直接影响的部分进行增量解析，并有效维护和利用已有的解析上下文信息**，
    * 以便 **大幅提高代码分析的效率，并快速更新相关文档，同时保证分析结果的准确性**。

### 4.3 文档生成与网站 (后续)

* **US-POSTMVP-DOCGEN-001 (生成C4架构图):**
    * 作为一名 **架构师 (ARCH)**，
    * 我想要 **ArchScope能根据代码分析结果和（可选的）我的补充配置，自动生成项目的C4模型图** (Context, Container, Component, Code)，并嵌入到文档网站中，
    * 以便 **我能清晰地向团队内外展示不同层级的系统架构**。
    * *(对应 `project_doc_architecture.html` 中图表的完整功能)*

* **US-POSTMVP-DOCSITE-001 (文档版本切换):**
    * 作为一名 **开发者 (DEV)**，
    * 我想要 **在文档网站上方便地切换和查看项目在不同代码提交（版本）时对应的文档内容**，
    * 以便 **我能追溯历史文档，理解架构的演变过程**。
    * *(对应 `project_doc_home.html` 等页面侧边栏的版本选择功能)*

* **US-POSTMVP-DOCSITE-002 (文档版本对比):**
    * 作为一名 **开发者 (DEV)**，
    * 我想要 **选择文档的两个不同版本进行内容对比，系统能高亮显示两个版本之间的文本差异（增、删、改）**，
    * 以便 **我能快速识别出文档在不同版本间具体发生了哪些变化**。
    * *(对应 `project_doc_compare.html` 的核心功能)*

* **US-POSTMVP-DOCSITE-003 (文档模板自定义):**
    * 作为一名 **架构师 (ARCH)** 或 **技术团队管理者 (TLM)**，
    * 我想要 **能够为我的项目自定义或选择不同的文档模板**，以控制生成文档的结构、章节和展现风格，
    * 以便 **产出更符合团队规范和阅读习惯的架构文档**。

### 4.4 健康度评估 (后续)

* **US-POSTMVP-HEALTH-001 (查看项目健康度报告):**
    * 作为一名 **技术团队管理者 (TLM)**，
    * 我想要 **定期查看项目的综合健康度报告**，其中包含一个总体星级评分、各项关键指标（如代码复杂度、测试覆盖率、依赖风险）的明细、以及潜在的风险点和改进建议，
    * 以便 **我能及时了解项目的技术健康状况并采取相应措施**。

* **US-POSTMVP-HEALTH-002 (自定义健康度规则):**
    * 作为一名 **架构师 (ARCH)**，
    * 我想要 **能够根据我们团队的标准和项目的特定关注点，自定义健康度评估的指标权重和阈值**，
    * 以便 **让健康度报告更贴合我们的实际需求和质量标准**。

### 4.5 自动化与集成 (后续)

* **US-POSTMVP-CHANGE-001 (Webhook自动更新):**
    * 作为一名 **开发者 (DEV)**，
    * 我期望 **当我向代码仓库推送代码后，ArchScope能通过Webhook自动感知到变更，并触发相应的代码解析和文档更新任务**，
    * 以便 **我总是能在文档网站上看到最新的信息，而无需手动操作**。

---

这份用户故事文档将随着项目的进展和需求的变化而持续更新。
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArchScope - 项目列表</title>
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for layout */
        :root {
            --primary-color: #4F46E5; /* Indigo-600 */
            --primary-hover: #4338CA; /* Indigo-700 */
            --sidebar-bg: #1E293B; /* Slate-800 */
            --sidebar-header-bg: #0F172A; /* Slate-900 */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #f9fafb; /* Gray-50 */
        }

        .content-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Table styles */
        .table-header {
            background-color: #F8FAFC; /* Slate-50 */
            font-weight: 600;
            text-transform: uppercase;
            font-size: 0.75rem;
            letter-spacing: 0.05em;
            color: #64748B; /* Slate-500 */
        }

        .table-row {
            transition: all 0.2s ease;
        }

        .table-row:hover {
            background-color: #F1F5F9; /* Slate-100 */
        }

        /* Star rating styles */
        .star-rating {
            display: inline-flex;
            align-items: center;
        }

        .star-filled {
            color: #FBBF24; /* Amber-400 */
        }

        .star-empty {
            color: #E5E7EB; /* Gray-200 */
        }

        /* Custom select styles */
        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .form-select {
            display: block;
            width: 100%;
            padding: 0.375rem 2.25rem 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #1F2937;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #D1D5DB;
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-select:focus {
            border-color: #4F46E5;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
        }

        /* Custom input styles */
        .form-input {
            display: block;
            width: 100%;
            height: calc(1.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #1F2937;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #D1D5DB;
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-input:focus {
            border-color: #4F46E5;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
        }

        /* Input with icon styles */
        .input-with-icon {
            position: relative;
        }

        .input-with-icon .form-input {
            padding-left: 2.5rem;
        }

        .input-with-icon .input-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
            pointer-events: none;
            z-index: 10;
        }

        /* Animation effects */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        .animate-slide-up {
            animation: slideUp 0.5s ease-out;
        }

        .animate-slide-down {
            animation: slideDown 0.3s ease-out;
        }

        .animate-scale {
            transition: transform 0.3s ease;
        }

        .animate-scale:hover {
            transform: scale(1.02);
        }

        .animate-button {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .animate-button:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .animate-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .animate-button:active:after {
            animation: ripple 1s ease-out;
        }

        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes slideUp {
            0% { transform: translateY(20px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        @keyframes slideDown {
            0% { transform: translateY(-20px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 1;
            }
            20% {
                transform: scale(25, 25);
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">

    <nav class="bg-gray-800 p-4">
        <div class="container mx-auto flex justify-between items-center">
            <a href="project_list.html" class="text-white flex items-center">
                <div class="bg-gray-800 rounded-full p-1 flex items-center justify-center mr-2">
                    <img src="images/logo.png" alt="ArchScope" class="h-8 w-8">
                </div>
                <span class="text-2xl font-bold">ArchScope</span>
            </a>
            <div class="flex space-x-4">
                <a href="project_list.html" class="bg-indigo-600 text-white px-3 py-2 rounded-md text-sm font-medium">项目列表</a>
                <a href="task_queue.html" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">任务队列</a>
                <a href="register_project.html" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">注册项目</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto p-6">
        <div class="flex items-center justify-between mb-6 animate-fade-in">
            <h1 class="text-3xl font-bold text-gray-800">项目列表</h1>
            <a href="register_project.html" class="btn-primary bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center animate-button">
                <i class="fas fa-plus-circle mr-2"></i> 注册新项目
            </a>
        </div>

        <div class="content-card bg-white shadow-md rounded-lg overflow-hidden mb-6 animate-slide-up">
            <div class="p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-700">所有项目</h2>
                <div class="flex items-center">
                    <div class="input-with-icon mr-4">
                        <i class="fas fa-search input-icon"></i>
                        <input type="text" placeholder="搜索项目..." class="form-input border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white">
                    </div>
                    <select class="form-select">
                        <option>所有项目</option>
                        <option>最近更新</option>
                        <option>星级排序</option>
                    </select>
                </div>
            </div>
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="table-header">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            项目名称
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            仓库地址
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            星级
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            最后更新
                        </th>
                        <th scope="col" class="relative px-6 py-3">
                            <span class="sr-only">操作</span>
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <!-- Project Row 1 -->
                    <tr class="table-row animate-scale">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center">
                                    <img class="h-10 w-10 rounded-full object-cover" src="https://images.unsplash.com/photo-1531297484001-80022131f5a1?auto=format&fit=crop&w=100&q=80" alt="Project Icon">
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        Awesome Components
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        组件库项目
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-code-branch text-gray-400 mr-2"></i>
                                <span>gitlab.yeepay.com/awesome/awesome-components</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="star-rating text-sm">
                                <i class="fas fa-star star-filled"></i>
                                <i class="fas fa-star star-filled"></i>
                                <i class="fas fa-star star-filled"></i>
                                <i class="fas fa-star star-filled"></i>
                                <i class="far fa-star star-empty"></i>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="far fa-clock text-gray-400 mr-2"></i>
                                <span>2023-10-26 10:00</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="project_detail.html" class="text-indigo-600 hover:text-indigo-900 mr-4 flex items-center inline-flex">
                                <i class="fas fa-info-circle mr-1"></i> 详情
                            </a>
                            <a href="project_doc_home.html" class="text-green-600 hover:text-green-900 flex items-center inline-flex">
                                <i class="fas fa-external-link-alt mr-1"></i> 访问
                            </a>
                        </td>
                    </tr>

                    <!-- Project Row 2 -->
                    <tr class="table-row animate-scale">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10 bg-blue-100 rounded-full flex items-center justify-center">
                                    <img class="h-10 w-10 rounded-full object-cover" src="https://images.unsplash.com/photo-1496171367470-9ed9a91c9398?auto=format&fit=crop&w=100&q=80" alt="Project Icon">
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-gray-900">
                                        Payment Gateway
                                    </div>
                                    <div class="text-sm text-gray-500">
                                        支付核心服务
                                    </div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-code-branch text-gray-400 mr-2"></i>
                                <span>gitlab.yeepay.com/payment/gateway</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="star-rating text-sm">
                                <i class="fas fa-star star-filled"></i>
                                <i class="fas fa-star star-filled"></i>
                                <i class="fas fa-star star-filled"></i>
                                <i class="fas fa-star star-filled"></i>
                                <i class="fas fa-star star-filled"></i>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="far fa-clock text-gray-400 mr-2"></i>
                                <span>2023-10-25 15:30</span>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="project_detail.html" class="text-indigo-600 hover:text-indigo-900 mr-4 flex items-center inline-flex">
                                <i class="fas fa-info-circle mr-1"></i> 详情
                            </a>
                            <a href="project_doc_home.html" class="text-green-600 hover:text-green-900 flex items-center inline-flex">
                                <i class="fas fa-external-link-alt mr-1"></i> 访问
                            </a>
                        </td>
                    </tr>

                    <!-- Add more project rows as needed -->

                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="flex items-center justify-between animate-fade-in">
            <div class="text-sm text-gray-700">
                显示 <span class="font-medium">1</span> 到 <span class="font-medium">2</span> 共 <span class="font-medium">2</span> 个项目
            </div>
            <div class="flex items-center space-x-2">
                <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button" disabled>
                    上一页
                </button>
                <button class="px-3 py-1 rounded-md bg-indigo-600 text-white hover:bg-indigo-700 animate-button">
                    1
                </button>
                <button class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button" disabled>
                    下一页
                </button>
            </div>
        </div>
    </div>

</body>
</html>
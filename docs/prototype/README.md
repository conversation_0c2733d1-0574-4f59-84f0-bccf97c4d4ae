# ArchScope 界面原型

本目录包含ArchScope（架构鹰眼）系统的界面原型设计文件。这些HTML原型展示了系统的视觉设计和功能布局，为前端开发提供了明确的实现参考。

## 原型文件列表

### 核心页面
- `project_list.html` - 项目列表页面
- `project_detail.html` - 项目详情页面
- `register_project.html` - 项目注册页面
- `task_queue.html` - 任务队列页面

### 文档页面
- `project_doc_home.html` - 文档首页（产品简介）
- `project_doc_architecture.html` - 架构设计文档
- `project_doc_extension.html` - 扩展能力文档
- `project_doc_user_manual.html` - 用户手册文档
- `project_doc_api.html` - API接口文档
- `project_doc_llms_txt.html` - LLM生成的原始内容
- `project_doc_compare.html` - 文档版本比较页面

### 管理页面
- `settings.html` - 系统设置页面
- `user_management.html` - 用户管理页面
- `task_detail.html` - 任务详情页面

## 设计规范

原型使用以下主要技术和设计规范：

- **CSS框架**: Tailwind CSS
- **图标库**: FontAwesome
- **主色调**: 靛蓝色 (#4F46E5, Indigo-600)
- **次要色**: 深蓝色 (#1E293B, Slate-800)
- **交互设计**: 包含悬停效果、过渡动画和涟漪效果
- **响应式设计**: 适配桌面和移动设备

## 如何查看原型

1. 直接在浏览器中打开HTML文件
2. 通过点击页面内的导航链接可以在不同页面间切换
3. 大部分交互效果（如悬停、点击）在浏览器中可直接体验

## 前端开发参考

在开发前端时，应参考这些原型实现以下方面：

1. 视觉一致性：保持与原型相同的颜色、间距和排版
2. 组件结构：遵循原型中的组件层次和布局
3. 交互行为：实现原型中展示的各种交互效果
4. 响应式布局：确保在不同屏幕尺寸下都能正常显示

## 相关分析文档

更详细的原型分析参见: [界面原型分析文档](../architecture/ui-prototype-analysis.md) 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目文档 - 架构概览</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for layout */
        :root {
            --sidebar-width: 18rem;
            --primary-color: #4F46E5; /* Indigo-600 */
            --primary-hover: #4338CA; /* Indigo-700 */
            --sidebar-bg: #1E293B; /* Slate-800 */
            --sidebar-header-bg: #0F172A; /* Slate-900 */
            --sidebar-item-hover: #334155; /* Slate-700 */
            --sidebar-active: #3B82F6; /* Blue-500 */
            --sidebar-text: #E2E8F0; /* Slate-200 */
            --sidebar-text-muted: #94A3B8; /* Slate-400 */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .sidebar-header {
            background-color: var(--sidebar-header-bg);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content {
            /* Use padding-left on the main content div instead of margin-left here */
        }

        .active-link {
            color: #ffffff;
            background-color: var(--sidebar-active);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .nav-link {
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: var(--sidebar-item-hover);
            border-left: 3px solid var(--primary-color);
        }

        .active-link {
            border-left: 3px solid var(--primary-color);
        }

        /* Tooltip styles */
        [title]:hover::after {
            content: attr(title);
            position: absolute;
            background-color: var(--sidebar-header-bg);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            margin-top: 1.5rem;
            z-index: 10;
            white-space: nowrap;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Custom styles for heading hierarchy */
        .prose h1 {
            font-size: 2.25rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 700;
            color: #1E293B; /* Slate-800 */
        }

        .prose h2 {
            font-size: 1.75rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #334155; /* Slate-700 */
            padding-bottom: 0.25rem;
            border-bottom: 1px solid #E2E8F0; /* Slate-200 */
        }

        .prose h3 {
            font-size: 1.5rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #475569; /* Slate-600 */
        }

        .prose h4 {
            font-size: 1.25rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #64748B; /* Slate-500 */
        }

        .prose ul {
            list-style: disc;
            margin-left: 1.5em;
        }

        .prose li {
            margin-bottom: 0.5em;
        }

        .prose p {
            line-height: 1.7;
            margin-bottom: 1.25em;
        }

        .prose code {
            background-color: #F1F5F9; /* Slate-100 */
            padding: 0.2em 0.4em;
            border-radius: 0.25rem;
            font-size: 0.875em;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Custom select styles */
        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        /* Content card styles */
        .content-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans flex">

    <!-- Left Sidebar Navigation -->
    <div class="sidebar fixed h-screen bg-gray-900 text-gray-300 flex flex-col shadow-lg">
        <!-- Compact Project Navigation Bar -->
        <div class="px-4 py-3 border-b border-gray-700 bg-gray-800">
            <div class="flex items-center justify-between">
                <a href="project_detail.html" class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center" title="返回项目主页">
                    <i class="fas fa-arrow-circle-left"></i>
                </a>
                <div class="relative inline-block text-left flex-grow mx-2">
                    <select id="project-select-sidebar" class="form-select block w-full py-1 px-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                        <option value="project1">[项目名称]</option>
                        <option value="project2">其他项目 A</option>
                        <option value="project3">其他项目 B</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="p-4 border-b border-gray-700">
            <h1 class="text-xl font-bold text-white truncate">[项目名称] 文档</h1>
        </div>
        <nav class="flex-grow p-6 overflow-y-auto">

            <ul class="space-y-2"> <!-- Increased space-y for better separation -->
                <li>
                    <a href="project_doc_home.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-home w-5 mr-3"></i> 产品简介</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_architecture.html" class="flex items-center justify-between text-white bg-gray-700 px-4 py-2 rounded-md transition duration-200 active-link">
                         <span class="flex items-center"><i class="fas fa-sitemap w-5 mr-3"></i> 架构设计</span>
                    </a>
                    <ul class="ml-4 mt-2 space-y-1 border-l border-gray-700 pl-4">
                        <li><a href="project_doc_architecture.html#架构概览-architecture-overview" class="flex items-center justify-between block text-gray-400 hover:text-white text-sm py-1 rounded-md transition duration-200">
                            <span>架构概览</span>
                        </a></li>
                        <li><a href="project_doc_architecture.html#系统分层-ddd" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">系统分层 (DDD)</a></li>
                        <li><a href="project_doc_architecture.html#关键技术栈" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">关键技术栈</a></li>
                        <li><a href="project_doc_architecture.html#部署视图" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">部署视图</a></li>
                        <li><a href="project_doc_architecture.html#数据架构-data-architecture" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">数据架构 Data Architecture</a></li>
                        <li><a href="project_doc_architecture.html#依赖管理-dependency-management" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">依赖管理 Dependency Management</a></li>
                        <li><a href="project_doc_architecture.html#构建部署-build-system" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">构建部署 Build System</a></li>
                        <li><a href="project_doc_architecture.html#核心服务和组件" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">核心服务和组件</a></li>
                    </ul>
                </li>
                 <li>
                    <a href="project_doc_extension.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-puzzle-piece w-5 mr-3"></i> 扩展能力</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_user_manual.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-book-open w-5 mr-3"></i> 用户手册</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_api.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-code w-5 mr-3"></i> 接口文档</span>
                    </a>
                </li>
                 <li>
                    <a href="project_doc_llms_txt.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-alt w-5 mr-3"></i> llms.txt</span>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- Version Selection and Comparison -->
        <div class="p-4 border-t border-gray-700 mt-auto">
            <div class="flex items-center mb-3">
                <label for="version-select-sidebar" class="text-gray-400 text-sm font-medium whitespace-nowrap mr-2">版本:</label>
                <select id="version-select-sidebar" class="form-select flex-grow px-2 py-1 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                    <option value="latest">最新版本 (1.0.0)</option>
                    <option value="v0.9">版本 0.9</option>
                    <option value="v0.8">版本 0.8</option>
                </select>
            </div>
            <button class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 border border-gray-600 rounded shadow transition duration-200 text-sm" onclick="window.location.href='project_doc_compare.html'">
                <i class="fas fa-code-branch mr-2"></i> 版本对比
            </button>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="content flex-grow py-8 px-8 ml-72 max-w-7xl mx-auto"> <!-- Added max-width and centered -->
        <div class="content-card bg-white shadow-xl rounded-lg p-8 prose max-w-none">
            <h1 class="text-3xl font-bold text-gray-800 mb-6 pb-2 border-b border-gray-200">架构概览 Architecture Overview</h1>

            <!-- Rendered Markdown content for Architecture Overview -->
            <p class="text-lg"><code class="bg-indigo-50 text-indigo-600 px-2 py-1 rounded">架构鹰眼 ArchScope</code> 系统采用模块化的前后端分离架构，遵循领域驱动设计 (DDD) 原则，并通过六边形架构模式实现业务逻辑与技术细节的解耦。</p>

            <h2 id="系统分层-ddd" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">系统分层 (DDD)</h2>
            <p class="mb-4">系统后端按照 DDD 划分为以下核心层：</p>
            <div class="bg-gray-50 rounded-lg p-5 border border-gray-200 mb-6">
                <ul class="space-y-4">
                    <li class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-md bg-blue-100 flex items-center justify-center mr-4">
                            <i class="fas fa-layer-group text-blue-600"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 mb-1">应用服务层 (Application)</h4>
                            <p class="text-gray-600 mb-0">协调领域对象完成业务用例，对外提供服务接口。</p>
                        </div>
                    </li>
                    <li class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-md bg-indigo-100 flex items-center justify-center mr-4">
                            <i class="fas fa-cube text-indigo-600"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 mb-1">领域模型层 (Domain)</h4>
                            <p class="text-gray-600 mb-0">包含实体、值对象、聚合根、领域服务和仓储接口，是业务逻辑的核心。</p>
                        </div>
                    </li>
                    <li class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-md bg-green-100 flex items-center justify-center mr-4">
                            <i class="fas fa-database text-green-600"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 mb-1">基础设施层 (Infrastructure)</h4>
                            <p class="text-gray-600 mb-0">实现仓储接口，处理数据持久化、外部服务调用等技术细节。</p>
                        </div>
                    </li>
                    <li class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10 rounded-md bg-purple-100 flex items-center justify-center mr-4">
                            <i class="fas fa-plug text-purple-600"></i>
                        </div>
                        <div>
                            <h4 class="text-lg font-medium text-gray-900 mb-1">接口层 (Interfaces/Facade)</h4>
                            <p class="text-gray-600 mb-0">负责外部请求的接收和响应，如 RESTful API。</p>
                        </div>
                    </li>
                </ul>
            </div>

            <h2 id="关键技术栈" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">关键技术栈</h2>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <div class="flex items-center mb-3">
                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <i class="fas fa-server text-blue-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">后端</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-blue-500 mr-2"></span>
                            <span>Java 8+</span>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-blue-500 mr-2"></span>
                            <span>Spring Boot</span>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-blue-500 mr-2"></span>
                            <span>MyBatis Plus</span>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-blue-500 mr-2"></span>
                            <span>Redis</span>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-blue-500 mr-2"></span>
                            <span>Maven</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <div class="flex items-center mb-3">
                        <div class="h-10 w-10 rounded-full bg-indigo-100 flex items-center justify-center mr-3">
                            <i class="fas fa-desktop text-indigo-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">前端</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>
                            <span>TypeScript</span>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>
                            <span>Vue 3.x</span>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>
                            <span>Tailwind CSS</span>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>
                            <span>FontAwesome</span>
                        </div>
                    </div>
                </div>

                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <div class="flex items-center mb-3">
                        <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                            <i class="fas fa-database text-green-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">数据存储</h3>
                    </div>
                    <div class="space-y-2">
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                            <span>MySQL 8.0+</span>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                            <span>Redis (缓存)</span>
                        </div>
                        <div class="flex items-center">
                            <span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>
                            <span>RocketMQ (队列)</span>
                        </div>
                    </div>
                </div>
            </div>

            <h2 id="部署视图">部署视图</h2>
            <p>系统设计为可容器化部署，后端服务可水平扩展。前端应用通过 Nginx 等 Web 服务器托管。MySQL 数据库、Redis 缓存和 RocketMQ 消息队列作为独立服务部署。</p>

            <h3 id="部署环境">部署环境</h3>
            <ul class="space-y-2">
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-blue-500 mr-2"></span>开发环境</li>
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-blue-500 mr-2"></span>测试环境</li>
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-blue-500 mr-2"></span>生产环境</li>
            </ul>

            <h3 id="部署拓扑">部署拓扑</h3>
            <p>（此处可插入部署图，例如使用 Mermaid 语法）</p>
            <pre><code class="language-mermaid">graph TD
                A[用户] --> B(前端应用);
                B --> C(Nginx);
                C --> D(后端服务集群);
                D --> E(MySQL);
                D --> F(Redis);
                D --> G(RocketMQ);
            </code></pre>

            <h4>容器化部署</h4>
            <p>系统各服务均打包为 Docker 镜像，方便在容器编排平台（如 Kubernetes）上进行部署和管理。</p>

            <h2 id="数据架构-data-architecture">数据架构 Data Architecture</h2>
            <p>系统的数据架构主要围绕项目、文档版本、任务、用户等核心实体进行设计.</p>

            <h3 id="核心数据实体">核心数据实体</h3>
            <ul class="space-y-2">
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>项目 (Project)</li>
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>文档版本 (DocumentVersion)</li>
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>解析任务 (ParseTask)</li>
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>用户 (User)</li>
            </ul>

            <h4>实体关系</h4>
            <p>（此处可插入实体关系图，例如使用 Mermaid 语法）</p>
            <pre><code class="language-mermaid">graph LR
                Project -- has --> DocumentVersion;
                Project -- generates --> ParseTask;
                User -- manages --> Project;
            </code></pre>

            <h3>数据流</h3>
            <p>描述数据在系统各组件之间的流转过程.</p>

            <h4>文档解析数据流</h4>
            <p>代码仓库变更 -> Webhook -> 任务调度服务 -> 文档解析服务 -> 数据存储.</p>

            <h2 id="依赖管理-dependency-management">依赖管理 Dependency Management</h2>
            <p>项目依赖通过 Maven 进行管理.</p>

            <h3>主要依赖</h3>
            <ul class="space-y-2">
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>Spring Boot Starter</li>
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>MyBatis Plus</li>
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>Redis Client</li>
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>RocketMQ Client</li>
                <li class="flex items-center"><span class="inline-block h-2 w-2 rounded-full bg-green-500 mr-2"></span>...</li>
            </ul>

            <h4>依赖版本控制</h4>
            <p>使用 Maven BOM (Bill of Materials) 管理依赖版本，确保一致性.</p>

            <h2 id="构建部署-build-system">构建部署 Build System</h2>
            <p>项目使用 Maven 进行构建，生成可执行 JAR 包.</p>

            <h3>构建流程</h3>
            <p>描述 CI/CD 构建流程.</p>

            <h4>自动化构建</h4>
            <p>集成 Jenkins 或 GitLab CI 进行自动化构建和镜像打包.</p>

            <h2 id="核心服务和组件">核心服务和组件</h2>
            <p>系统包含以下核心服务和组件：</p>

            <h3>文档解析服务</h3>
            <p>负责从代码仓库中解析文档和代码结构.</p>

            <h4>解析器模块</h4>
            <p>针对不同类型的文档和代码（Markdown, Java, etc.）设计独立的解析器模块.</p>

            <h3>版本管理服务</h3>
            <p>负责存储和管理文档的不同版本.</p>

            <h4>版本存储</h4>
            <p>文档版本存储在对象存储或文件系统中，元数据存储在数据库.</p>

            <h3>任务调度服务</h3>
            <p>负责处理文档更新和解析任务队列.</p>

            <h4>任务优先级</h4>
            <p>根据项目星级和任务类型设置不同的处理优先级.</p>

            <h3>API Gateway</h3>
            <p>（如果需要）</p>

            <h4>API 设计</h4>
            <p>提供 RESTful API 供前端和其他服务调用.</p>
        </div>
    </div>

    <!-- Mermaid JS -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                flowchart: { useMaxWidth: true, htmlLabels: true }
            });

            // 将 pre > code.language-mermaid 转换为 div.mermaid
            document.querySelectorAll('pre > code.language-mermaid').forEach(function(codeBlock) {
                const content = codeBlock.textContent;
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = content;

                const preElement = codeBlock.parentElement;
                preElement.parentElement.replaceChild(mermaidDiv, preElement);
            });

            // 重新初始化 Mermaid
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        });
    </script>
</body>
</html>
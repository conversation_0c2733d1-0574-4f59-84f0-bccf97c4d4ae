<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目文档 - 版本对比</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for layout */
        :root {
            --sidebar-width: 18rem;
            --primary-color: #4F46E5; /* Indigo-600 */
            --primary-hover: #4338CA; /* Indigo-700 */
            --sidebar-bg: #1E293B; /* Slate-800 */
            --sidebar-header-bg: #0F172A; /* Slate-900 */
            --sidebar-item-hover: #334155; /* Slate-700 */
            --sidebar-active: #3B82F6; /* Blue-500 */
            --sidebar-text: #E2E8F0; /* Slate-200 */
            --sidebar-text-muted: #94A3B8; /* Slate-400 */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .sidebar-header {
            background-color: var(--sidebar-header-bg);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content {
            /* Use padding-left on the main content div instead of margin-left here */
        }

        .active-link {
            color: #ffffff;
            background-color: var(--sidebar-active);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .nav-link {
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: var(--sidebar-item-hover);
            border-left: 3px solid var(--primary-color);
        }

        .active-link {
            border-left: 3px solid var(--primary-color);
        }

        /* Tooltip styles */
        [title]:hover::after {
            content: attr(title);
            position: absolute;
            background-color: var(--sidebar-header-bg);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            margin-top: 1.5rem;
            z-index: 10;
            white-space: nowrap;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Custom styles for heading hierarchy */
        .prose h1 {
            font-size: 2.25rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 700;
            color: #1E293B; /* Slate-800 */
        }

        .prose h2 {
            font-size: 1.75rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #334155; /* Slate-700 */
            padding-bottom: 0.25rem;
            border-bottom: 1px solid #E2E8F0; /* Slate-200 */
        }

        .prose h3 {
            font-size: 1.5rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #475569; /* Slate-600 */
        }

        .prose h4 {
            font-size: 1.25rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #64748B; /* Slate-500 */
        }

        .prose ul {
            list-style: disc;
            margin-left: 1.5em;
        }

        .prose li {
            margin-bottom: 0.5em;
        }

        .prose p {
            line-height: 1.7;
            margin-bottom: 1.25em;
        }

        .prose code {
            background-color: #F1F5F9; /* Slate-100 */
            padding: 0.2em 0.4em;
            border-radius: 0.25rem;
            font-size: 0.875em;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Custom select styles */
        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        /* Content card styles */
        .content-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Custom styles for diff view */
        .diff-added {
            background-color: #e6ffed; /* Light green */
        }
        .diff-removed {
            background-color: #ffebeb; /* Light red */
        }
        .diff-line {
            padding: 2px 0;
        }
        .diff-line pre {
            margin: 0;
            padding: 0;
            white-space: pre-wrap; /* Wrap long lines */
            word-break: break-all; /* Break words if necessary */
        }
        .line-number {
            display: inline-block;
            width: 40px; /* Fixed width for line numbers */
            text-align: right;
            margin-right: 10px;
            color: #999;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans flex">

    <!-- Left Sidebar Navigation for Comparison -->
    <div class="sidebar fixed h-screen bg-gray-900 text-gray-300 flex flex-col shadow-lg">
        <!-- Compact Project Navigation Bar -->
        <div class="px-4 py-3 border-b border-gray-700 bg-gray-800">
            <div class="flex items-center justify-between">
                <a href="project_detail.html" class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center" title="返回项目主页">
                    <i class="fas fa-arrow-circle-left"></i>
                </a>
                <div class="relative inline-block text-left flex-grow mx-2">
                    <select id="project-select-sidebar" class="form-select block w-full py-1 px-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                        <option value="project1">[项目名称]</option>
                        <option value="project2">其他项目 A</option>
                        <option value="project3">其他项目 B</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="p-4 border-b border-gray-700">
            <h1 class="text-xl font-bold text-white truncate">版本对比</h1>
            <p class="text-sm text-gray-400 mt-1">版本 0.9 vs 1.0.0</p>
        </div>
        <nav class="flex-grow p-6 overflow-y-auto">
            <ul class="space-y-2"> <!-- Increased space-y for better separation -->
                <li>
                    <a href="#" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-home w-5 mr-3"></i> 产品简介</span>
                        <!-- Difference indicator placeholder -->
                        <span class="w-2 h-2 bg-red-500 rounded-full ml-2"></span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                         <span class="flex items-center"><i class="fas fa-sitemap w-5 mr-3"></i> 架构设计</span>
                          <!-- Difference indicator placeholder -->
                        <span class="w-2 h-2 bg-red-500 rounded-full ml-2"></span>
                    </a>
                    <ul class="ml-8 mt-2 space-y-1 border-l border-gray-700 pl-4"> <!-- Increased mt and space-y -->
                        <li><a href="#" class="nav-link flex items-center justify-between block text-gray-400 hover:text-white text-sm py-1 transition duration-200">
                            <span>架构概览</span>
                            <!-- Difference indicator placeholder -->
                            <span class="w-1.5 h-1.5 bg-red-500 rounded-full ml-2"></span>
                        </a></li>
                        <li><a href="#" class="nav-link flex items-center justify-between block text-gray-400 hover:text-white text-sm py-1 transition duration-200">
                            <span>流程架构</span>
                            <!-- Difference indicator placeholder -->
                            <!-- <span class="w-1.5 h-1.5 bg-red-500 rounded-full ml-2"></span> -->
                        </a></li>
                        <li><a href="#" class="nav-link flex items-center justify-between block text-gray-400 hover:text-white text-sm py-1 transition duration-200">
                            <span>数据架构</span>
                            <!-- Difference indicator placeholder -->
                            <span class="w-1.5 h-1.5 bg-red-500 rounded-full ml-2"></span>
                        </a></li>
                        <li><a href="#" class="nav-link flex items-center justify-between block text-gray-400 hover:text-white text-sm py-1 transition duration-200">
                            <span>依赖管理</span>
                            <!-- Difference indicator placeholder -->
                            <!-- <span class="w-1.5 h-1.5 bg-red-500 rounded-full ml-2"></span> -->
                        </a></li>
                        <li><a href="#" class="nav-link flex items-center justify-between block text-gray-400 hover:text-white text-sm py-1 transition duration-200">
                            <span>构建部署</span>
                            <!-- Difference indicator placeholder -->
                            <span class="w-1.5 h-1.5 bg-red-500 rounded-full ml-2"></span>
                        </a></li>
                        <li><a href="#" class="nav-link flex items-center justify-between block text-gray-400 hover:text-white text-sm py-1 transition duration-200">
                            <span>核心服务和组件</span>
                            <!-- Difference indicator placeholder -->
                            <!-- <span class="w-1.5 h-1.5 bg-red-500 rounded-full ml-2"></span> -->
                        </a></li>
                    </ul>
                </li>
                 <li>
                    <a href="#" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-puzzle-piece w-5 mr-3"></i> 扩展能力</span>
                        <!-- Difference indicator placeholder -->
                        <!-- <span class="w-2 h-2 bg-red-500 rounded-full ml-2"></span> -->
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-book-open w-5 mr-3"></i> 用户手册</span>
                        <!-- Difference indicator placeholder -->
                        <span class="w-2 h-2 bg-red-500 rounded-full ml-2"></span>
                    </a>
                </li>
                <li>
                    <a href="#" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-code w-5 mr-3"></i> 接口文档</span>
                        <!-- Difference indicator placeholder -->
                        <!-- <span class="w-2 h-2 bg-red-500 rounded-full ml-2"></span> -->
                    </a>
                </li>
                 <li>
                    <a href="#" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-alt w-5 mr-3"></i> llms.txt</span>
                        <!-- Difference indicator placeholder -->
                        <span class="w-2 h-2 bg-red-500 rounded-full ml-2"></span>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- Return to Document Link -->
        <div class="p-4 border-t border-gray-700 mt-auto">
             <a href="project_doc_architecture.html" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md flex items-center transition duration-200 text-sm shadow-sm">
                <i class="fas fa-arrow-left mr-2"></i> 返回文档
            </a>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="content flex-grow py-8 px-8 ml-72 max-w-7xl mx-auto"> <!-- Added flex-grow for consistent layout -->
        <div class="content-card bg-white shadow-xl rounded-lg p-8 mb-6 prose max-w-none">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-semibold text-gray-800 border-b-0 pb-0">
                    文档页面: <span class="font-normal text-gray-600">产品简介</span>
                </h2>
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <span class="text-sm text-gray-600 mr-2">版本对比:</span>
                        <div class="flex items-center bg-gray-100 rounded-md px-3 py-1">
                            <span class="text-sm font-medium text-gray-800">0.9</span>
                            <i class="fas fa-arrow-right mx-2 text-gray-400"></i>
                            <span class="text-sm font-medium text-gray-800">1.0.0</span>
                        </div>
                    </div>
                    <div class="flex space-x-2">
                        <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-md text-sm transition duration-200">
                            <i class="fas fa-expand-arrows-alt mr-1"></i> 全部展开
                        </button>
                        <button class="bg-indigo-100 hover:bg-indigo-200 text-indigo-700 px-3 py-1 rounded-md text-sm transition duration-200">
                            <i class="fas fa-download mr-1"></i> 导出差异
                        </button>
                    </div>
                </div>
            </div>

            <div class="border-t border-b border-gray-200 py-3 mb-4 flex items-center justify-between text-sm">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center">
                        <span class="w-3 h-3 bg-red-500 opacity-70 rounded-sm mr-1"></span>
                        <span class="text-gray-600">删除</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-3 h-3 bg-green-500 opacity-70 rounded-sm mr-1"></span>
                        <span class="text-gray-600">新增</span>
                    </div>
                    <div class="flex items-center">
                        <span class="w-3 h-3 bg-yellow-500 opacity-70 rounded-sm mr-1"></span>
                        <span class="text-gray-600">修改</span>
                    </div>
                </div>
                <div class="flex items-center">
                    <span class="text-gray-600 mr-2">变更总数:</span>
                    <span class="font-medium text-gray-800">12</span>
                </div>
            </div>

            <div class="font-mono text-sm bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                <div class="bg-gray-100 px-4 py-2 border-b border-gray-200 flex justify-between items-center">
                    <span class="font-medium text-gray-700">产品简介.md</span>
                    <span class="text-xs text-gray-500">最后更新: 2023-10-25</span>
                </div>
                <!-- Simulated Diff Content for a document page -->
                <div class="p-4 space-y-1">
                    <div class="diff-line diff-removed bg-red-50 rounded px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">5</span>
                        <pre class="whitespace-pre-wrap">- 系统可以基于需求文档、设计文档、代码仓库为开发者提供项目的全局视角。</pre>
                    </div>
                    <div class="diff-line diff-added bg-green-50 rounded px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">5</span>
                        <pre class="whitespace-pre-wrap">+ ArchScope 系统可以基于需求文档、设计文档、代码仓库等多种来源为开发者提供项目的全局视角。</pre>
                    </div>
                    <div class="diff-line px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">6</span>
                        <pre class="whitespace-pre-wrap">方便开发者快速理解项目、治理项目，方便使用方快速对接。</pre>
                    </div>
                    <div class="diff-line px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">7</span>
                        <pre class="whitespace-pre-wrap"></pre>
                    </div>
                    <div class="diff-line px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">8</span>
                        <pre class="whitespace-pre-wrap">## 核心能力</pre>
                    </div>
                    <div class="diff-line px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">9</span>
                        <pre class="whitespace-pre-wrap"></pre>
                    </div>
                    <div class="diff-line diff-removed bg-red-50 rounded px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">10</span>
                        <pre class="whitespace-pre-wrap">- 自动感知项目的变更周期，并定期更新相关文档。</pre>
                    </div>
                    <div class="diff-line diff-added bg-green-50 rounded px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">10</span>
                        <pre class="whitespace-pre-wrap">+ 系统自动感知项目的代码提交和文档更新，并触发定期的文档生成和更新任务。</pre>
                    </div>
                    <div class="diff-line px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">11</span>
                        <pre class="whitespace-pre-wrap">统计各项目的访问量，并给出展示页面。</pre>
                    </div>
                    <div class="diff-line px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">12</span>
                        <pre class="whitespace-pre-wrap">智能任务排队处理，优化资源分配。</pre>
                    </div>
                    <div class="diff-line px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">13</span>
                        <pre class="whitespace-pre-wrap"></pre>
                    </div>
                    <div class="diff-line px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">14</span>
                        <pre class="whitespace-pre-wrap">## 主要特性</pre>
                    </div>
                    <div class="diff-line diff-added bg-green-50 rounded px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">15</span>
                        <pre class="whitespace-pre-wrap">+ 新增对 Mermaid 图谱的支持，可以在文档中直接渲染架构图和流程图。</pre>
                    </div>
                    <div class="diff-line px-2 py-1 flex">
                        <span class="line-number text-gray-500 mr-4 select-none w-8 text-right">16</span>
                        <pre class="whitespace-pre-wrap">网站是由多个markdown文件渲染而成，整个网站对外呈现的内容如下：</pre>
                    </div>
                </div>
            </div>

            <div class="mt-6 flex justify-between">
                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm transition duration-200 flex items-center">
                    <i class="fas fa-chevron-left mr-2"></i> 上一个文件
                </button>
                <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-2 rounded-md text-sm transition duration-200 flex items-center">
                    下一个文件 <i class="fas fa-chevron-right ml-2"></i>
                </button>
            </div>
        </div>

        <div class="content-card bg-white shadow-xl rounded-lg p-6 mb-6 prose max-w-none">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">变更摘要</h3>
            <div class="space-y-3">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-5 w-5 rounded-full bg-green-100 flex items-center justify-center mr-3">
                        <i class="fas fa-plus text-green-600 text-xs"></i>
                    </div>
                    <p class="text-gray-700 m-0">新增了 Mermaid 图谱支持，可以在文档中直接渲染架构图和流程图</p>
                </div>
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-5 w-5 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                        <i class="fas fa-pencil-alt text-yellow-600 text-xs"></i>
                    </div>
                    <p class="text-gray-700 m-0">改进了自动文档更新的描述，明确了触发机制</p>
                </div>
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-5 w-5 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <i class="fas fa-info text-blue-600 text-xs"></i>
                    </div>
                    <p class="text-gray-700 m-0">更新了产品名称，统一使用 "ArchScope" 作为系统名称</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Mermaid JS -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                flowchart: { useMaxWidth: true, htmlLabels: true }
            });

            // 将 pre > code.language-mermaid 转换为 div.mermaid
            document.querySelectorAll('pre > code.language-mermaid').forEach(function(codeBlock) {
                const content = codeBlock.textContent;
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = content;

                const preElement = codeBlock.parentElement;
                preElement.parentElement.replaceChild(mermaidDiv, preElement);
            });

            // 重新初始化 Mermaid
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        });
    </script>
</body>
</html>
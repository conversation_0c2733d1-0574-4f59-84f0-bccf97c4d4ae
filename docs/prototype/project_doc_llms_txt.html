<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目文档 - llms.txt</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for layout */
        :root {
            --sidebar-width: 18rem;
            --primary-color: #4F46E5; /* Indigo-600 */
            --primary-hover: #4338CA; /* Indigo-700 */
            --sidebar-bg: #1E293B; /* Slate-800 */
            --sidebar-header-bg: #0F172A; /* Slate-900 */
            --sidebar-item-hover: #334155; /* Slate-700 */
            --sidebar-active: #3B82F6; /* Blue-500 */
            --sidebar-text: #E2E8F0; /* Slate-200 */
            --sidebar-text-muted: #94A3B8; /* Slate-400 */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .sidebar-header {
            background-color: var(--sidebar-header-bg);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content {
            /* Use padding-left on the main content div instead of margin-left here */
        }

        .active-link {
            color: #ffffff;
            background-color: var(--sidebar-active);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .nav-link {
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: var(--sidebar-item-hover);
            border-left: 3px solid var(--primary-color);
        }

        .active-link {
            border-left: 3px solid var(--primary-color);
        }

        /* Tooltip styles */
        [title]:hover::after {
            content: attr(title);
            position: absolute;
            background-color: var(--sidebar-header-bg);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            margin-top: 1.5rem;
            z-index: 10;
            white-space: nowrap;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Custom styles for heading hierarchy */
        .prose h1 {
            font-size: 2.25rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 700;
            color: #1E293B; /* Slate-800 */
        }

        .prose h2 {
            font-size: 1.75rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #334155; /* Slate-700 */
            padding-bottom: 0.25rem;
            border-bottom: 1px solid #E2E8F0; /* Slate-200 */
        }

        .prose h3 {
            font-size: 1.5rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #475569; /* Slate-600 */
        }

        .prose h4 {
            font-size: 1.25rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #64748B; /* Slate-500 */
        }

        .prose ul {
            list-style: disc;
            margin-left: 1.5em;
        }

        .prose li {
            margin-bottom: 0.5em;
        }

        .prose p {
            line-height: 1.7;
            margin-bottom: 1.25em;
        }

        .prose code {
            background-color: #F1F5F9; /* Slate-100 */
            padding: 0.2em 0.4em;
            border-radius: 0.25rem;
            font-size: 0.875em;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Custom select styles */
        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        /* Content card styles */
        .content-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans flex">

    <!-- Left Sidebar Navigation -->
    <div class="sidebar fixed h-screen bg-gray-900 text-gray-300 flex flex-col shadow-lg">
        <!-- Compact Project Navigation Bar -->
        <div class="px-4 py-3 border-b border-gray-700 bg-gray-800">
            <div class="flex items-center justify-between">
                <a href="project_detail.html" class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center" title="返回项目主页">
                    <i class="fas fa-arrow-circle-left"></i>
                </a>
                <div class="relative inline-block text-left flex-grow mx-2">
                    <select id="project-select-sidebar" class="form-select block w-full py-1 px-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                        <option value="project1">[项目名称]</option>
                        <option value="project2">其他项目 A</option>
                        <option value="project3">其他项目 B</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="p-4 border-b border-gray-700">
            <h1 class="text-xl font-bold text-white truncate">[项目名称] 文档</h1>
        </div>
        <nav class="flex-grow p-6 overflow-y-auto">

            <ul class="space-y-2"> <!-- Increased space-y for better separation -->
                <li>
                    <a href="project_doc_home.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-home w-5 mr-3"></i> 产品简介</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_architecture.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                         <span class="flex items-center"><i class="fas fa-sitemap w-5 mr-3"></i> 架构设计</span>
                    </a>
                </li>
                 <li>
                    <a href="project_doc_extension.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-puzzle-piece w-5 mr-3"></i> 扩展能力</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_user_manual.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-book-open w-5 mr-3"></i> 用户手册</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_api.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-code w-5 mr-3"></i> 接口文档</span>
                    </a>
                </li>
                 <li>
                    <a href="project_doc_llms_txt.html" class="nav-link flex items-center justify-between text-white bg-blue-600 px-4 py-2 rounded-md transition duration-200 active-link">
                        <span class="flex items-center"><i class="fas fa-file-alt w-5 mr-3"></i> llms.txt</span>
                    </a>
                     <!-- No H2s listed under llms.txt in the requirements -->
                    <ul class="ml-4 mt-2 space-y-1 border-l border-gray-700 pl-4">
                        <!-- H2 links for llms.txt will go here if defined -->
                    </ul>
                </li>
            </ul>
        </nav>
        <!-- Version Selection and Comparison -->
        <div class="p-4 border-t border-gray-700 mt-auto">
            <div class="flex items-center mb-3">
                <label for="version-select-sidebar" class="text-gray-400 text-sm font-medium whitespace-nowrap mr-2">版本:</label>
                <select id="version-select-sidebar" class="form-select flex-grow px-2 py-1 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                    <option value="latest">最新版本 (1.0.0)</option>
                    <option value="v0.9">版本 0.9</option>
                    <option value="v0.8">版本 0.8</option>
                </select>
            </div>
            <button class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 border border-gray-600 rounded shadow transition duration-200 text-sm" onclick="window.location.href='project_doc_compare.html'">
                <i class="fas fa-code-branch mr-2"></i> 版本对比
            </button>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="content flex-grow py-8 px-8 ml-72 max-w-7xl mx-auto"> <!-- Added max-width and centered -->
        <div class="content-card bg-white shadow-xl rounded-lg p-8 prose max-w-none">
            <h1 class="text-3xl font-bold text-gray-800 mb-6 pb-2 border-b border-gray-200">llms.txt</h1>

            <!-- llms.txt Content -->
            <p class="text-lg"><code class="bg-indigo-50 text-indigo-600 px-2 py-1 rounded">llms.txt</code> 是一个标准化的文件，用于指导大型语言模型（LLMs）如何与您的项目进行交互。</p>

            <div class="bg-purple-50 p-4 rounded-md border-l-4 border-purple-500 mb-6">
                <p class="text-purple-800"><strong>提示：</strong> llms.txt 文件类似于 robots.txt，但专为 AI 助手和大型语言模型设计，帮助它们更好地理解和处理您的项目。</p>
            </div>

            <h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">llms.txt 文件的作用</h2>
            <p class="mb-4">llms.txt 文件允许项目所有者定义：</p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <div class="flex items-center mb-3">
                        <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                            <i class="fas fa-map-signs text-blue-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">项目导航指南</h3>
                    </div>
                    <p class="text-gray-600">指导 AI 如何浏览和理解项目结构</p>
                </div>
                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <div class="flex items-center mb-3">
                        <div class="h-10 w-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                            <i class="fas fa-shield-alt text-green-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">访问控制</h3>
                    </div>
                    <p class="text-gray-600">指定哪些内容可以被 AI 访问和引用</p>
                </div>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <div class="flex items-center mb-3">
                        <div class="h-10 w-10 rounded-full bg-yellow-100 flex items-center justify-center mr-3">
                            <i class="fas fa-lightbulb text-yellow-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">上下文提示</h3>
                    </div>
                    <p class="text-gray-600">提供项目背景和关键信息</p>
                </div>
                <div class="bg-white p-5 rounded-lg shadow-sm border border-gray-100">
                    <div class="flex items-center mb-3">
                        <div class="h-10 w-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                            <i class="fas fa-exclamation-triangle text-red-600"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-800">注意事项</h3>
                    </div>
                    <p class="text-gray-600">指出常见误解和需要避免的错误</p>
                </div>
            </div>

            <h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">文件格式说明</h2>
            <p class="mb-4">llms.txt 使用简单的文本格式，包含以下几个主要部分：</p>

            <div class="bg-gray-50 p-5 rounded-lg border border-gray-200 mb-6 font-mono text-sm overflow-x-auto">
                <pre># 项目名称: ArchScope
# 版本: 1.0.0
# 最后更新: 2023-10-26

[项目描述]
ArchScope 是一个面向开发者的架构观测和守护系统。

[导航指南]
- docs/ - 项目文档目录
- src/ - 源代码目录
- api/ - API 定义和实现

[访问控制]
允许:
- 公开文档
- API 规范
- 示例代码

禁止:
- 私有配置文件
- 测试数据

[上下文提示]
- 项目使用 DDD 架构
- 主要编程语言: Java, TypeScript
- 文档使用 Markdown 格式

[注意事项]
- API 版本号在路径中指定
- 配置文件使用 YAML 格式</pre>
            </div>

            <h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">如何配置和使用</h2>
            <p class="mb-4">要为您的项目创建和使用 llms.txt 文件，请按照以下步骤操作：</p>

            <ol class="space-y-4 list-decimal pl-5 mb-6">
                <li class="text-gray-700">
                    <span class="font-medium text-gray-900">创建文件</span> - 在项目根目录创建名为 <code>llms.txt</code> 的文本文件
                </li>
                <li class="text-gray-700">
                    <span class="font-medium text-gray-900">添加内容</span> - 按照上述格式添加相关内容
                </li>
                <li class="text-gray-700">
                    <span class="font-medium text-gray-900">提交到仓库</span> - 确保文件与项目一起提交到代码仓库
                </li>
                <li class="text-gray-700">
                    <span class="font-medium text-gray-900">定期更新</span> - 随着项目的发展，及时更新 llms.txt 文件
                </li>
            </ol>

            <div class="bg-blue-50 p-4 rounded-md border-l-4 border-blue-500 mb-6">
                <p class="text-blue-800"><strong>提示：</strong> ArchScope 系统会自动为您的项目生成 llms.txt 文件的初始版本，您可以根据需要进行修改和完善。</p>
            </div>
        </div>
    </div>

    <!-- Mermaid JS -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                flowchart: { useMaxWidth: true, htmlLabels: true }
            });

            // 将 pre > code.language-mermaid 转换为 div.mermaid
            document.querySelectorAll('pre > code.language-mermaid').forEach(function(codeBlock) {
                const content = codeBlock.textContent;
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = content;

                const preElement = codeBlock.parentElement;
                preElement.parentElement.replaceChild(mermaidDiv, preElement);
            });

            // 重新初始化 Mermaid
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        });
    </script>
</body>
</html>
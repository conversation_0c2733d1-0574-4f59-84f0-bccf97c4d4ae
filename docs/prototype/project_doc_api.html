<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目文档 - 接口文档</title>
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for layout */
        :root {
            --sidebar-width: 18rem;
            --primary-color: #4F46E5; /* Indigo-600 */
            --primary-hover: #4338CA; /* Indigo-700 */
            --sidebar-bg: #1E293B; /* Slate-800 */
            --sidebar-header-bg: #0F172A; /* Slate-900 */
            --sidebar-item-hover: #334155; /* Slate-700 */
            --sidebar-active: #3B82F6; /* Blue-500 */
            --sidebar-text: #E2E8F0; /* Slate-200 */
            --sidebar-text-muted: #94A3B8; /* Slate-400 */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .sidebar-header {
            background-color: var(--sidebar-header-bg);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content {
            /* Use padding-left on the main content div instead of margin-left here */
        }

        .active-link {
            color: #ffffff;
            background-color: var(--sidebar-active);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .nav-link {
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: var(--sidebar-item-hover);
            border-left: 3px solid var(--primary-color);
        }

        .active-link {
            border-left: 3px solid var(--primary-color);
        }

        /* Tooltip styles */
        [title]:hover::after {
            content: attr(title);
            position: absolute;
            background-color: var(--sidebar-header-bg);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            margin-top: 1.5rem;
            z-index: 10;
            white-space: nowrap;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Custom styles for heading hierarchy */
        .prose h1 {
            font-size: 2.25rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 700;
            color: #1E293B; /* Slate-800 */
        }

        .prose h2 {
            font-size: 1.75rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #334155; /* Slate-700 */
            padding-bottom: 0.25rem;
            border-bottom: 1px solid #E2E8F0; /* Slate-200 */
        }

        .prose h3 {
            font-size: 1.5rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #475569; /* Slate-600 */
        }

        .prose h4 {
            font-size: 1.25rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #64748B; /* Slate-500 */
        }

        .prose ul {
            list-style: disc;
            margin-left: 1.5em;
        }

        .prose li {
            margin-bottom: 0.5em;
        }

        .prose p {
            line-height: 1.7;
            margin-bottom: 1.25em;
        }

        .prose code {
            background-color: #F1F5F9; /* Slate-100 */
            padding: 0.2em 0.4em;
            border-radius: 0.25rem;
            font-size: 0.875em;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Custom select styles */
        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        /* Content card styles */
        .content-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans flex">

    <!-- Left Sidebar Navigation -->
    <div class="sidebar fixed h-screen bg-gray-900 text-gray-300 flex flex-col shadow-lg">
        <!-- Compact Project Navigation Bar -->
        <div class="px-4 py-3 border-b border-gray-700 bg-gray-800">
            <div class="flex items-center justify-between">
                <a href="project_detail.html" class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center" title="返回项目主页">
                    <i class="fas fa-arrow-circle-left"></i>
                </a>
                <div class="relative inline-block text-left flex-grow mx-2">
                    <select id="project-select-sidebar" class="form-select block w-full py-1 px-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                        <option value="project1">[项目名称]</option>
                        <option value="project2">其他项目 A</option>
                        <option value="project3">其他项目 B</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="p-4 border-b border-gray-700">
            <h1 class="text-xl font-bold text-white truncate">[项目名称] 文档</h1>
        </div>
        <nav class="flex-grow p-6 overflow-y-auto">

            <ul class="space-y-2"> <!-- Increased space-y for better separation -->
                <li>
                    <a href="project_doc_home.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-home w-5 mr-3"></i> 产品简介</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_architecture.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                         <span class="flex items-center"><i class="fas fa-sitemap w-5 mr-3"></i> 架构设计</span>
                    </a>
                </li>
                 <li>
                    <a href="project_doc_extension.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-puzzle-piece w-5 mr-3"></i> 扩展能力</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_user_manual.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-book-open w-5 mr-3"></i> 用户手册</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_api.html" class="flex items-center justify-between text-white bg-gray-700 px-4 py-2 rounded-md transition duration-200 active-link">
                        <span class="flex items-center"><i class="fas fa-file-code w-5 mr-3"></i> 接口文档</span>
                    </a>
                     <!-- No H2s listed under 接口文档 in the requirements -->
                    <ul class="ml-4 mt-2 space-y-1 border-l border-gray-700 pl-4">
                        <!-- H2 links for 接口文档 will go here if defined -->
                    </ul>
                </li>
                 <li>
                    <a href="project_doc_llms_txt.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-alt w-5 mr-3"></i> llms.txt</span>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- Version Selection and Comparison -->
        <div class="p-4 border-t border-gray-700 mt-auto">
            <div class="flex items-center mb-3">
                <label for="version-select-sidebar" class="text-gray-400 text-sm font-medium whitespace-nowrap mr-2">版本:</label>
                <select id="version-select-sidebar" class="form-select flex-grow px-2 py-1 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                    <option value="latest">最新版本 (1.0.0)</option>
                    <option value="v0.9">版本 0.9</option>
                    <option value="v0.8">版本 0.8</option>
                </select>
            </div>
            <button class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 border border-gray-600 rounded shadow transition duration-200 text-sm" onclick="window.location.href='project_doc_compare.html'">
                <i class="fas fa-code-branch mr-2"></i> 版本对比
            </button>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="content flex-grow py-8 px-8 ml-72 max-w-7xl mx-auto"> <!-- Added max-width and centered -->
        <div class="content-card bg-white shadow-xl rounded-lg p-8 prose max-w-none">
            <h1 class="text-3xl font-bold text-gray-800 mb-6 pb-2 border-b border-gray-200">接口文档</h1>

            <!-- API Documentation Content -->
            <p class="text-lg"><code class="bg-indigo-50 text-indigo-600 px-2 py-1 rounded">ArchScope</code> 系统提供了一系列 RESTful API，用于与系统进行交互和集成。</p>

            <div class="bg-gray-50 rounded-lg p-6 my-6 border border-gray-200">
                <h2 class="text-xl font-semibold text-gray-800 mb-4">API 概览</h2>
                <p class="mb-4">ArchScope API 使用 RESTful 风格设计，支持标准的 HTTP 方法，并使用 JSON 作为数据交换格式。</p>

                <!-- Swagger Access Button -->
                <div class="mb-6 flex flex-col sm:flex-row items-center justify-between gap-4 p-4 bg-indigo-50 rounded-lg border border-indigo-100 animate-scale">
                    <div class="flex items-center">
                        <div class="h-12 w-12 rounded-full bg-indigo-100 flex items-center justify-center mr-4">
                            <i class="fas fa-file-code text-indigo-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-medium text-lg text-gray-800">Swagger API 文档</h3>
                            <p class="text-gray-600">访问交互式 API 文档，在线测试 API 端点</p>
                        </div>
                    </div>
                    <div class="flex gap-3">
                        <a href="#" class="btn-primary bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center animate-button">
                            <i class="fas fa-eye mr-2"></i> 在线查看
                        </a>
                        <a href="#" class="bg-white text-indigo-600 border border-indigo-300 hover:bg-indigo-50 px-4 py-2 rounded-lg flex items-center animate-button">
                            <i class="fas fa-download mr-2"></i> 下载 Swagger 文件
                        </a>
                    </div>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex items-center mb-2">
                            <div class="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center mr-2">
                                <i class="fas fa-lock text-green-600"></i>
                            </div>
                            <h3 class="font-medium">认证</h3>
                        </div>
                        <p class="text-sm text-gray-600">API 使用 JWT 令牌进行认证，通过 Authorization 头传递</p>
                    </div>
                    <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-100">
                        <div class="flex items-center mb-2">
                            <div class="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center mr-2">
                                <i class="fas fa-exchange-alt text-blue-600"></i>
                            </div>
                            <h3 class="font-medium">数据格式</h3>
                        </div>
                        <p class="text-sm text-gray-600">请求和响应均使用 JSON 格式，UTF-8 编码</p>
                    </div>
                </div>
            </div>

            <h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">API 端点</h2>

            <div class="overflow-x-auto">
                <table class="min-w-full bg-white border border-gray-200 rounded-lg mb-6">
                    <thead>
                        <tr class="bg-gray-50">
                            <th class="py-2 px-4 border-b text-left">端点</th>
                            <th class="py-2 px-4 border-b text-left">方法</th>
                            <th class="py-2 px-4 border-b text-left">描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td class="py-2 px-4 border-b"><code>/api/v1/projects</code></td>
                            <td class="py-2 px-4 border-b"><span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">GET</span></td>
                            <td class="py-2 px-4 border-b">获取项目列表</td>
                        </tr>
                        <tr>
                            <td class="py-2 px-4 border-b"><code>/api/v1/projects/{id}</code></td>
                            <td class="py-2 px-4 border-b"><span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">GET</span></td>
                            <td class="py-2 px-4 border-b">获取特定项目详情</td>
                        </tr>
                        <tr>
                            <td class="py-2 px-4 border-b"><code>/api/v1/projects</code></td>
                            <td class="py-2 px-4 border-b"><span class="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs font-medium">POST</span></td>
                            <td class="py-2 px-4 border-b">创建新项目</td>
                        </tr>
                        <tr>
                            <td class="py-2 px-4 border-b"><code>/api/v1/tasks</code></td>
                            <td class="py-2 px-4 border-b"><span class="bg-green-100 text-green-800 px-2 py-1 rounded text-xs font-medium">GET</span></td>
                            <td class="py-2 px-4 border-b">获取任务列表</td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">请求和响应示例</h2>

            <h3 class="text-xl font-semibold text-gray-700 mt-6 mb-3">获取项目列表</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                <div>
                    <h4 class="text-lg font-medium text-gray-700 mb-2">请求</h4>
                    <div class="bg-gray-800 text-gray-200 p-4 rounded-md font-mono text-sm overflow-x-auto">
                        GET /api/v1/projects<br>
                        Authorization: Bearer {token}
                    </div>
                </div>
                <div>
                    <h4 class="text-lg font-medium text-gray-700 mb-2">响应</h4>
                    <div class="bg-gray-800 text-gray-200 p-4 rounded-md font-mono text-sm overflow-x-auto">
                        {<br>
                        &nbsp;&nbsp;"status": "success",<br>
                        &nbsp;&nbsp;"data": [<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;{<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"id": "123",<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"name": "示例项目",<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;"created_at": "2023-10-25T15:30:00Z"<br>
                        &nbsp;&nbsp;&nbsp;&nbsp;}<br>
                        &nbsp;&nbsp;]<br>
                        }
                    </div>
                </div>
            </div>

            <div class="bg-blue-50 p-4 rounded-md border-l-4 border-blue-500 mb-6">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-600 text-xl mr-3"></i>
                    <div>
                        <p class="text-blue-800"><strong>注意：</strong> 完整的 API 文档可通过上方的 Swagger 文档入口获取，包括所有端点、参数和响应格式的详细说明。</p>
                        <p class="text-blue-700 mt-2">使用 Swagger UI 可以在线测试 API 端点，也可以下载 Swagger 文件进行本地集成。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mermaid JS -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                flowchart: { useMaxWidth: true, htmlLabels: true }
            });

            // 将 pre > code.language-mermaid 转换为 div.mermaid
            document.querySelectorAll('pre > code.language-mermaid').forEach(function(codeBlock) {
                const content = codeBlock.textContent;
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = content;

                const preElement = codeBlock.parentElement;
                preElement.parentElement.replaceChild(mermaidDiv, preElement);
            });

            // 重新初始化 Mermaid
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        });
    </script>
</body>
</html>
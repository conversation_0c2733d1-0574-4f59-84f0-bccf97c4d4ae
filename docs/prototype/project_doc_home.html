<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目文档 - [项目名称]</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for layout */
        :root {
            --sidebar-width: 18rem;
            --primary-color: #4F46E5; /* Indigo-600 */
            --primary-hover: #4338CA; /* Indigo-700 */
            --sidebar-bg: #1E293B; /* Slate-800 */
            --sidebar-header-bg: #0F172A; /* Slate-900 */
            --sidebar-item-hover: #334155; /* Slate-700 */
            --sidebar-active: #3B82F6; /* Blue-500 */
            --sidebar-text: #E2E8F0; /* Slate-200 */
            --sidebar-text-muted: #94A3B8; /* Slate-400 */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .sidebar-header {
            background-color: var(--sidebar-header-bg);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content {
            /* Use padding-left on the main content div instead of margin-left here */
        }

        .active-link {
            color: #ffffff;
            background-color: var(--sidebar-active);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .nav-link {
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: var(--sidebar-item-hover);
            border-left: 3px solid var(--primary-color);
        }

        .active-link {
            border-left: 3px solid var(--primary-color);
        }

        /* Tooltip styles */
        [title]:hover::after {
            content: attr(title);
            position: absolute;
            background-color: var(--sidebar-header-bg);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            margin-top: 1.5rem;
            z-index: 10;
            white-space: nowrap;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Custom styles for heading hierarchy */
        .prose h1 {
            font-size: 2.25rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 700;
            color: #1E293B; /* Slate-800 */
        }

        .prose h2 {
            font-size: 1.75rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #334155; /* Slate-700 */
            padding-bottom: 0.25rem;
            border-bottom: 1px solid #E2E8F0; /* Slate-200 */
        }

        .prose h3 {
            font-size: 1.5rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #475569; /* Slate-600 */
        }

        .prose h4 {
            font-size: 1.25rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #64748B; /* Slate-500 */
        }

        .prose ul {
            list-style: disc;
            margin-left: 1.5em;
        }

        .prose li {
            margin-bottom: 0.5em;
        }

        .prose p {
            line-height: 1.7;
            margin-bottom: 1.25em;
        }

        .prose code {
            background-color: #F1F5F9; /* Slate-100 */
            padding: 0.2em 0.4em;
            border-radius: 0.25rem;
            font-size: 0.875em;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Custom select styles */
        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        /* Content card styles */
        .content-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans flex">

    <!-- Left Sidebar Navigation -->
    <div class="sidebar fixed h-screen bg-gray-900 text-gray-300 flex flex-col shadow-lg">
        <!-- Compact Project Navigation Bar -->
        <div class="px-4 py-3 border-b border-gray-700 bg-gray-800">
            <div class="flex items-center justify-between">
                <a href="project_detail.html" class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center" title="返回项目主页">
                    <i class="fas fa-arrow-circle-left"></i>
                </a>
                <div class="relative inline-block text-left flex-grow mx-2">
                    <select id="project-select-sidebar" class="form-select block w-full py-1 px-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                        <option value="project1">[项目名称]</option>
                        <option value="project2">其他项目 A</option>
                        <option value="project3">其他项目 B</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="p-4 border-b border-gray-700">
            <h1 class="text-xl font-bold text-white truncate">[项目名称] 文档</h1>
        </div>
        <nav class="flex-grow p-6 overflow-y-auto">

            <ul class="space-y-2"> <!-- Increased space-y for better separation -->
                <li>
                    <a href="project_doc_home.html" class="flex items-center justify-between text-white bg-gray-700 px-4 py-2 rounded-md transition duration-200 active-link">
                        <span class="flex items-center"><i class="fas fa-home w-5 mr-3"></i> 产品简介</span>
                    </a>
                    <ul class="ml-4 mt-1 space-y-1 border-l border-gray-700 pl-4">
                        <li><a href="#核心能力" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">核心能力</a></li>
                        <li><a href="#主要特性" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">主要特性</a></li>
                        <li><a href="#未来规划" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">未来规划</a></li>
                        <li><a href="#目标用户" class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200">目标用户</a></li>
                    </ul>
                </li>
                <li>
                    <a href="project_doc_architecture.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                         <span class="flex items-center"><i class="fas fa-sitemap w-5 mr-3"></i> 架构设计</span>
                    </a>
                </li>
                 <li>
                    <a href="project_doc_extension.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-puzzle-piece w-5 mr-3"></i> 扩展能力</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_user_manual.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-book-open w-5 mr-3"></i> 用户手册</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_api.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-code w-5 mr-3"></i> 接口文档</span>
                    </a>
                </li>
                 <li>
                    <a href="project_doc_llms_txt.html" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-alt w-5 mr-3"></i> llms.txt</span>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- Version Selection and Comparison -->
        <div class="p-4 border-t border-gray-700 mt-auto">
            <div class="flex items-center mb-3">
                <label for="version-select-sidebar" class="text-gray-400 text-sm font-medium whitespace-nowrap mr-2">版本:</label>
                <select id="version-select-sidebar" class="form-select flex-grow px-2 py-1 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                    <option value="latest">最新版本 (1.0.0)</option>
                    <option value="v0.9">版本 0.9</option>
                    <option value="v0.8">版本 0.8</option>
                </select>
            </div>
            <button class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 border border-gray-600 rounded shadow transition duration-200 text-sm" onclick="window.location.href='project_doc_compare.html'">
                <i class="fas fa-code-branch mr-2"></i> 版本对比
            </button>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="content flex-grow py-8 px-8 ml-72 max-w-7xl mx-auto"> <!-- Added max-width and centered -->
        <div class="content-card bg-white shadow-xl rounded-lg p-8 prose max-w-none">
            <h1 class="text-3xl font-bold text-gray-800 mb-6 pb-2 border-b border-gray-200">产品简介</h1>

            <!-- Rendered Markdown content for Product Introduction -->
            <p class="text-lg"><code class="bg-indigo-50 text-indigo-600 px-2 py-1 rounded">架构鹰眼 ArchScope</code> 系统是一个面向开发者的架构观测和守护系统。</p>
            <p>它旨在通过自动化分析需求文档、设计文档和代码仓库，为开发者提供项目的全局视角，从而帮助开发者快速理解和治理项目，同时也方便使用方快速对接。</p>

            <h2 id="核心能力" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">核心能力</h2>
            <ul class="space-y-2 my-6">
                <li class="flex items-center">
                    <span class="inline-flex items-center justify-center rounded-full bg-indigo-100 text-indigo-600 h-5 w-5 mr-3"><i class="fas fa-check text-xs"></i></span>
                    <span>基于仓库地址自动生成专属项目文档网站。</span>
                </li>
                <li class="flex items-center">
                    <span class="inline-flex items-center justify-center rounded-full bg-indigo-100 text-indigo-600 h-5 w-5 mr-3"><i class="fas fa-check text-xs"></i></span>
                    <span>自动感知项目变更周期，定期更新文档。</span>
                </li>
                <li class="flex items-center">
                    <span class="inline-flex items-center justify-center rounded-full bg-indigo-100 text-indigo-600 h-5 w-5 mr-3"><i class="fas fa-check text-xs"></i></span>
                    <span>统计项目访问量，进行分级（1-5星）。</span>
                </li>
                <li class="flex items-center">
                    <span class="inline-flex items-center justify-center rounded-full bg-indigo-100 text-indigo-600 h-5 w-5 mr-3"><i class="fas fa-check text-xs"></i></span>
                    <span>智能任务排队处理，优化资源分配。</span>
                </li>
            </ul>

            <h3 id="主要特性" class="text-xl font-semibold text-gray-800 mt-8 mb-4">主要特性</h3>
            <p>ArchScope 提供以下主要特性：</p>
            <ul class="space-y-3 my-6">
                <li class="flex items-center">
                    <span class="inline-flex items-center justify-center rounded-md bg-blue-100 text-blue-600 h-6 w-6 mr-3"><i class="fas fa-file-alt text-xs"></i></span>
                    <span><strong class="text-gray-800">自动化文档生成</strong>: 从代码和现有文档中提取信息。</span>
                </li>
                <li class="flex items-center">
                    <span class="inline-flex items-center justify-center rounded-md bg-green-100 text-green-600 h-6 w-6 mr-3"><i class="fas fa-code-branch text-xs"></i></span>
                    <span><strong class="text-gray-800">版本管理与对比</strong>: 追踪文档变更，方便回溯和比较。</span>
                </li>
                <li class="flex items-center">
                    <span class="inline-flex items-center justify-center rounded-md bg-yellow-100 text-yellow-600 h-6 w-6 mr-3"><i class="fas fa-chart-line text-xs"></i></span>
                    <span><strong class="text-gray-800">项目健康度评估</strong>: 基于访问量和更新频率等指标。</span>
                </li>
                <li class="flex items-center">
                    <span class="inline-flex items-center justify-center rounded-md bg-purple-100 text-purple-600 h-6 w-6 mr-3"><i class="fas fa-puzzle-piece text-xs"></i></span>
                    <span><strong class="text-gray-800">可扩展的解析器</strong>: 支持集成更多文档和代码类型。</span>
                </li>
            </ul>

            <h4 id="自动化文档生成细节" class="text-lg font-semibold text-gray-700 mt-6 mb-3">自动化文档生成细节</h4>
            <p class="mb-4 text-gray-600">系统通过解析代码注释、特定标记和项目结构，自动提取关键信息，生成结构化的文档内容。</p>

            <h4 class="text-lg font-semibold text-gray-700 mt-6 mb-3">版本管理流程</h4>
            <p class="mb-4 text-gray-600">每次检测到重要变更时，系统会自动创建新的文档版本快照，并记录变更日志。</p>

            <h3 id="未来规划" class="text-xl font-semibold text-gray-800 mt-8 mb-4">未来规划</h3>
            <p class="mb-4">我们计划在未来版本中增加以下功能：</p>
            <ul class="space-y-2 my-6 pl-5 list-disc">
                <li class="text-gray-700">深度代码结构分析。</li>
                <li class="text-gray-700">与 CI/CD 流程集成。</li>
                <li class="text-gray-700">更丰富的可视化图表。</li>
            </ul>

            <h4 id="集成路线图" class="text-lg font-semibold text-gray-700 mt-6 mb-3">集成路线图</h4>
            <p class="mb-4 text-gray-600">计划逐步支持主流 CI/CD 平台和更多的代码仓库类型。</p>

            <div class="bg-gray-50 p-6 rounded-lg border border-gray-200 my-8">
                <h2 id="目标用户" class="text-2xl font-semibold text-gray-800 mb-4">目标用户</h2>
                <p class="mb-4">本系统主要服务于需要快速理解、维护和对接项目的开发者和使用方。</p>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-6">
                    <div class="bg-white p-5 rounded-lg shadow-sm">
                        <h3 id="开发者" class="text-xl font-semibold text-gray-800 mb-3 flex items-center">
                            <i class="fas fa-code mr-2 text-indigo-500"></i>开发者
                        </h3>
                        <p class="mb-4 text-gray-600">帮助开发者快速熟悉新项目，理解现有架构，进行代码治理。</p>

                        <h4 class="text-lg font-semibold text-gray-700 mt-4 mb-2">新项目入职</h4>
                        <p class="mb-3 text-gray-600">新成员可以通过 ArchScope 快速获取项目全貌和核心文档。</p>

                        <h4 class="text-lg font-semibold text-gray-700 mt-4 mb-2">日常开发</h4>
                        <p class="mb-3 text-gray-600">方便开发者查找接口、依赖和架构细节。</p>
                    </div>

                    <div class="bg-white p-5 rounded-lg shadow-sm">
                        <h3 id="使用方" class="text-xl font-semibold text-gray-800 mb-3 flex items-center">
                            <i class="fas fa-users mr-2 text-green-500"></i>使用方
                        </h3>
                        <p class="mb-4 text-gray-600">提供清晰的接口文档和用户手册，方便快速集成和使用项目。</p>

                        <h4 class="text-lg font-semibold text-gray-700 mt-4 mb-2">快速对接</h4>
                        <p class="mb-3 text-gray-600">使用方可以直接查阅接口文档，了解如何调用项目服务。</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Mermaid JS -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                flowchart: { useMaxWidth: true, htmlLabels: true }
            });

            // 将 pre > code.language-mermaid 转换为 div.mermaid
            document.querySelectorAll('pre > code.language-mermaid').forEach(function(codeBlock) {
                const content = codeBlock.textContent;
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = content;

                const preElement = codeBlock.parentElement;
                preElement.parentElement.replaceChild(mermaidDiv, preElement);
            });

            // 重新初始化 Mermaid
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArchScope - 任务详情</title>
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for layout */
        :root {
            --primary-color: #4F46E5; /* Indigo-600 */
            --primary-hover: #4338CA; /* Indigo-700 */
            --sidebar-bg: #1E293B; /* Slate-800 */
            --sidebar-header-bg: #0F172A; /* Slate-900 */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #f9fafb; /* Gray-50 */
        }

        .content-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Status badge styles */
        .status-badge {
            display: inline-flex;
            align-items: center;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 600;
        }

        .status-processing {
            background-color: #FEF3C7; /* Amber-100 */
            color: #92400E; /* Amber-800 */
        }

        .status-success {
            background-color: #DCFCE7; /* Green-100 */
            color: #166534; /* Green-800 */
        }

        .status-waiting {
            background-color: #DBEAFE; /* Blue-100 */
            color: #1E40AF; /* Blue-800 */
        }

        .status-failed {
            background-color: #FEE2E2; /* Red-100 */
            color: #991B1B; /* Red-800 */
        }

        /* Stat card styles */
        .stat-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1.5rem;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Log terminal styles */
        .log-terminal {
            background-color: #1E293B; /* Slate-800 */
            color: #E2E8F0; /* Slate-200 */
            border-radius: 0.5rem;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
            font-size: 0.875rem;
            line-height: 1.5;
            overflow-x: auto;
            max-height: 24rem;
        }

        .log-info {
            color: #93C5FD; /* Blue-300 */
        }

        .log-warn {
            color: #FCD34D; /* Amber-300 */
        }

        .log-error {
            color: #FCA5A5; /* Red-300 */
        }

        /* Custom select styles */
        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .form-select {
            display: block;
            width: 100%;
            padding: 0.375rem 2.25rem 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #1F2937;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #D1D5DB;
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-select:focus {
            border-color: #4F46E5;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
        }

        /* Custom input styles */
        .form-input {
            display: block;
            width: 100%;
            height: calc(1.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #1F2937;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #D1D5DB;
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-input:focus {
            border-color: #4F46E5;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
        }

        /* Input with icon styles */
        .input-with-icon {
            position: relative;
        }

        .input-with-icon .form-input {
            padding-left: 2.5rem;
        }

        .input-with-icon .input-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
            pointer-events: none;
            z-index: 10;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">

    <nav class="bg-gray-800 p-4">
        <div class="container mx-auto flex justify-between items-center">
            <a href="project_list.html" class="text-white flex items-center">
                <div class="bg-gray-800 rounded-full p-1 flex items-center justify-center mr-2">
                    <img src="images/logo.png" alt="ArchScope" class="h-8 w-8">
                </div>
                <span class="text-2xl font-bold">ArchScope</span>
            </a>
            <div class="flex space-x-4">
                <a href="project_list.html" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">项目列表</a>
                <a href="task_queue.html" class="bg-indigo-600 text-white px-3 py-2 rounded-md text-sm font-medium">任务队列</a>
                <a href="register_project.html" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">注册项目</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto p-6">
        <div class="flex items-center justify-between mb-6 animate-fade-in">
            <h1 class="text-3xl font-bold text-gray-800">任务详情</h1>
            <a href="task_queue.html" class="text-indigo-600 hover:text-indigo-900 flex items-center transition duration-200 animate-button">
                <i class="fas fa-arrow-left mr-2"></i> 返回任务队列
            </a>
        </div>

        <!-- Task Summary Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6 animate-slide-up">
            <div class="stat-card animate-scale">
                <div class="flex items-center justify-center h-12 w-12 rounded-full bg-indigo-100 mb-3">
                    <i class="fas fa-tasks text-indigo-600"></i>
                </div>
                <div class="text-gray-600 text-sm font-semibold">总任务数</div>
                <div class="text-3xl font-bold text-gray-800 mt-1">150</div>
            </div>
            <div class="stat-card animate-scale">
                <div class="flex items-center justify-center h-12 w-12 rounded-full bg-yellow-100 mb-3">
                    <i class="fas fa-sync-alt text-yellow-600"></i>
                </div>
                <div class="text-gray-600 text-sm font-semibold">处理中</div>
                <div class="text-3xl font-bold text-yellow-600 mt-1">5</div>
            </div>
            <div class="stat-card animate-scale">
                <div class="flex items-center justify-center h-12 w-12 rounded-full bg-green-100 mb-3">
                    <i class="fas fa-check-circle text-green-600"></i>
                </div>
                <div class="text-gray-600 text-sm font-semibold">成功</div>
                <div class="text-3xl font-bold text-green-600 mt-1">130</div>
            </div>
            <div class="stat-card animate-scale">
                <div class="flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-3">
                    <i class="fas fa-times-circle text-red-600"></i>
                </div>
                <div class="text-gray-600 text-sm font-semibold">失败</div>
                <div class="text-3xl font-bold text-red-600 mt-1">15</div>
            </div>
        </div>

        <!-- Task Details -->
        <div class="content-card mb-6 overflow-hidden animate-slide-up" style="animation-delay: 0.2s;">
            <div class="p-4 bg-gray-50 border-b border-gray-200">
                <h2 class="text-lg font-semibold text-gray-700 flex items-center">
                    <i class="fas fa-info-circle text-indigo-500 mr-2"></i>
                    任务信息
                </h2>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700">
                    <div>
                        <div class="flex items-center mb-3">
                            <div class="w-32 text-gray-500 font-medium">任务ID:</div>
                            <div class="flex items-center">
                                <i class="fas fa-tasks text-gray-400 mr-2"></i>
                                <span class="font-medium">task-12345</span>
                            </div>
                        </div>
                        <div class="flex items-center mb-3">
                            <div class="w-32 text-gray-500 font-medium">关联项目:</div>
                            <div class="flex items-center">
                                <span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>
                                <span>Awesome Components</span>
                            </div>
                        </div>
                        <div class="flex items-center mb-3">
                            <div class="w-32 text-gray-500 font-medium">任务类型:</div>
                            <div class="flex items-center">
                                <i class="fas fa-file-alt text-gray-400 mr-2"></i>
                                <span>文档更新 (UPDATE)</span>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 text-gray-500 font-medium">状态:</div>
                            <span class="status-badge status-processing flex items-center">
                                <i class="fas fa-sync-alt mr-2"></i> 处理中
                            </span>
                        </div>
                    </div>
                    <div>
                        <div class="flex items-center mb-3">
                            <div class="w-32 text-gray-500 font-medium">创建时间:</div>
                            <div class="flex items-center">
                                <i class="far fa-clock text-gray-400 mr-2"></i>
                                <span>2023-10-26 10:05</span>
                            </div>
                        </div>
                        <div class="flex items-center mb-3">
                            <div class="w-32 text-gray-500 font-medium">开始时间:</div>
                            <div class="flex items-center">
                                <i class="far fa-clock text-gray-400 mr-2"></i>
                                <span>2023-10-26 10:06</span>
                            </div>
                        </div>
                        <div class="flex items-center mb-3">
                            <div class="w-32 text-gray-500 font-medium">结束时间:</div>
                            <div class="flex items-center">
                                <span>-</span>
                            </div>
                        </div>
                        <div class="flex items-center">
                            <div class="w-32 text-gray-500 font-medium">耗时:</div>
                            <div class="flex items-center">
                                <span>-</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-6 pt-4 border-t border-gray-200">
                    <div class="flex items-center">
                        <div class="w-32 text-gray-500 font-medium">错误信息:</div>
                        <div class="flex items-center">
                            <span>无</span>
                        </div>
                    </div>
                    <!-- Display error message if status is FAILED -->
                    <!--
                    <div class="flex items-center">
                        <div class="w-32 text-gray-500 font-medium">错误信息:</div>
                        <div class="flex items-center text-red-600">
                            <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                            <span>仓库克隆失败，权限不足。</span>
                        </div>
                    </div>
                    -->
                </div>
            </div>
        </div>

        <!-- Task Logs -->
        <div class="content-card overflow-hidden animate-slide-up" style="animation-delay: 0.4s;">
            <div class="p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between">
                <h2 class="text-lg font-semibold text-gray-700 flex items-center">
                    <i class="fas fa-terminal text-indigo-500 mr-2"></i>
                    任务日志
                </h2>
                <button class="btn-primary bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-1 rounded-lg flex items-center text-sm animate-button">
                    <i class="fas fa-download mr-1"></i> 下载日志
                </button>
            </div>
            <div class="log-terminal p-4">
                <pre class="whitespace-pre-wrap break-words">
<span class="log-info">[INFO] 2023-10-26 10:06:15 - Task task-12345 started for project Awesome Components.</span>
<span class="log-info">[INFO] 2023-10-26 10:06:20 - Cloning repository https://gitlab.yeepay.com/awesome/awesome-components...</span>
<span class="log-info">[INFO] 2023-10-26 10:07:05 - Repository cloned successfully.</span>
<span class="log-info">[INFO] 2023-10-26 10:07:10 - Starting document parsing and generation...</span>
<span class="log-warn">[WARN] 2023-10-26 10:08:30 - Could not find llms.txt in repository root. Skipping.</span>
<span class="log-info">[INFO] 2023-10-26 10:10:00 - Document generation complete.</span>
<span class="log-info">[INFO] 2023-10-26 10:10:10 - Updating website content...</span>
<span class="log-info">[INFO] 2023-10-26 10:10:25 - Website updated successfully.</span>
<span class="log-info">[INFO] 2023-10-26 10:10:30 - Task task-12345 finished successfully.</span>
                </pre>
                <!-- Example of error log -->
                <!--
<span class="log-info">[INFO] 2023-10-15 14:00:00 - Task task-12345 started for project Awesome Components.</span>
<span class="log-info">[INFO] 2023-10-15 14:00:05 - Cloning repository https://gitlab.yeepay.com/awesome/awesome-components...</span>
<span class="log-error">[ERROR] 2023-10-15 14:00:15 - Failed to clone repository: Authentication failed.</span>
<span class="log-error">[ERROR] 2023-10-15 14:00:15 - Task task-12345 failed.</span>
                -->
            </div>
        </div>
    </div>

    <!-- Mermaid JS -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                flowchart: { useMaxWidth: true, htmlLabels: true }
            });

            // 将 pre > code.language-mermaid 转换为 div.mermaid
            document.querySelectorAll('pre > code.language-mermaid').forEach(function(codeBlock) {
                const content = codeBlock.textContent;
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = content;

                const preElement = codeBlock.parentElement;
                preElement.parentElement.replaceChild(mermaidDiv, preElement);
            });

            // 重新初始化 Mermaid
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        });
    </script>
</body>
</html>
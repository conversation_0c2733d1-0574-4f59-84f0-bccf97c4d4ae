<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目文档 - 用户手册</title>
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for layout */
        :root {
            --sidebar-width: 18rem;
            --primary-color: #4F46E5; /* Indigo-600 */
            --primary-hover: #4338CA; /* Indigo-700 */
            --sidebar-bg: #1E293B; /* Slate-800 */
            --sidebar-header-bg: #0F172A; /* Slate-900 */
            --sidebar-item-hover: #334155; /* Slate-700 */
            --sidebar-active: #3B82F6; /* Blue-500 */
            --sidebar-text: #E2E8F0; /* Slate-200 */
            --sidebar-text-muted: #94A3B8; /* Slate-400 */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
        }

        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--sidebar-bg);
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .sidebar-header {
            background-color: var(--sidebar-header-bg);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .content {
            /* Use padding-left on the main content div instead of margin-left here */
        }

        .active-link {
            color: #ffffff;
            background-color: var(--sidebar-active);
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
        }

        .nav-link {
            transition: all 0.2s ease;
            border-left: 3px solid transparent;
        }

        .nav-link:hover {
            background-color: var(--sidebar-item-hover);
            border-left: 3px solid var(--primary-color);
        }

        .active-link {
            border-left: 3px solid var(--primary-color);
        }

        /* Tooltip styles */
        [title]:hover::after {
            content: attr(title);
            position: absolute;
            background-color: var(--sidebar-header-bg);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            font-size: 0.75rem;
            margin-top: 1.5rem;
            z-index: 10;
            white-space: nowrap;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Custom styles for heading hierarchy */
        .prose h1 {
            font-size: 2.25rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 700;
            color: #1E293B; /* Slate-800 */
        }

        .prose h2 {
            font-size: 1.75rem;
            margin-top: 1.5em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #334155; /* Slate-700 */
            padding-bottom: 0.25rem;
            border-bottom: 1px solid #E2E8F0; /* Slate-200 */
        }

        .prose h3 {
            font-size: 1.5rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #475569; /* Slate-600 */
        }

        .prose h4 {
            font-size: 1.25rem;
            margin-top: 1.75em;
            margin-bottom: 0.5em;
            font-weight: 600;
            color: #64748B; /* Slate-500 */
        }

        .prose ul {
            list-style: disc;
            margin-left: 1.5em;
        }

        .prose li {
            margin-bottom: 0.5em;
        }

        .prose p {
            line-height: 1.7;
            margin-bottom: 1.25em;
        }

        .prose code {
            background-color: #F1F5F9; /* Slate-100 */
            padding: 0.2em 0.4em;
            border-radius: 0.25rem;
            font-size: 0.875em;
            font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Custom select styles */
        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        /* Content card styles */
        .content-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
    </style>
</head>
<body class="bg-gray-50 font-sans flex">

    <!-- Left Sidebar Navigation -->
    <div class="sidebar fixed h-screen bg-gray-900 text-gray-300 flex flex-col shadow-lg">
        <!-- Compact Project Navigation Bar -->
        <div class="px-4 py-3 border-b border-gray-700 bg-gray-800">
            <div class="flex items-center justify-between">
                <a href="project_detail.html" class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center" title="返回项目主页">
                    <i class="fas fa-arrow-circle-left"></i>
                </a>
                <div class="relative inline-block text-left flex-grow mx-2">
                    <select id="project-select-sidebar" class="form-select block w-full py-1 px-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                        <option value="project1">[项目名称]</option>
                        <option value="project2">其他项目 A</option>
                        <option value="project3">其他项目 B</option>
                    </select>
                </div>
            </div>
        </div>
        <div class="p-4 border-b border-gray-700">
            <h1 class="text-xl font-bold text-white truncate">[项目名称] 文档</h1>
        </div>
        <nav class="flex-grow p-6 overflow-y-auto">

            <ul class="space-y-2"> <!-- Increased space-y for better separation -->
                <li>
                    <a href="project_doc_home.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-home w-5 mr-3"></i> 产品简介</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_architecture.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                         <span class="flex items-center"><i class="fas fa-sitemap w-5 mr-3"></i> 架构设计</span>
                    </a>
                </li>
                 <li>
                    <a href="project_doc_extension.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-puzzle-piece w-5 mr-3"></i> 扩展能力</span>
                    </a>
                </li>
                <li>
                    <a href="project_doc_user_manual.html" class="nav-link flex items-center justify-between text-white bg-blue-600 px-4 py-2 rounded-md transition duration-200 active-link">
                        <span class="flex items-center"><i class="fas fa-book-open w-5 mr-3"></i> 用户手册</span>
                    </a>
                    <!-- No H2s listed under 用户手册 in the requirements -->
                    <ul class="ml-4 mt-2 space-y-1 border-l border-gray-700 pl-4">
                        <!-- H2 links for 用户手册 will go here if defined -->
                    </ul>
                </li>
                <li>
                    <a href="project_doc_api.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-code w-5 mr-3"></i> 接口文档</span>
                    </a>
                </li>
                 <li>
                    <a href="project_doc_llms_txt.html" class="nav-link flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                        <span class="flex items-center"><i class="fas fa-file-alt w-5 mr-3"></i> llms.txt</span>
                    </a>
                </li>
            </ul>
        </nav>
        <!-- Version Selection and Comparison -->
        <div class="p-4 border-t border-gray-700 mt-auto">
            <div class="flex items-center mb-3">
                <label for="version-select-sidebar" class="text-gray-400 text-sm font-medium whitespace-nowrap mr-2">版本:</label>
                <select id="version-select-sidebar" class="form-select flex-grow px-2 py-1 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                    <option value="latest">最新版本 (1.0.0)</option>
                    <option value="v0.9">版本 0.9</option>
                    <option value="v0.8">版本 0.8</option>
                </select>
            </div>
            <button class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 border border-gray-600 rounded shadow transition duration-200 text-sm" onclick="window.location.href='project_doc_compare.html'">
                <i class="fas fa-code-branch mr-2"></i> 版本对比
            </button>
        </div>
    </div>

    <!-- Main Content Area -->
    <div class="content flex-grow py-8 px-8 ml-72 max-w-7xl mx-auto"> <!-- Added max-width and centered -->
        <div class="content-card bg-white shadow-xl rounded-lg p-8 prose max-w-none">
            <h1 class="text-3xl font-bold text-gray-800 mb-6 pb-2 border-b border-gray-200">用户手册</h1>

            <!-- User Manual Content -->
            <p class="text-lg">欢迎使用 <code class="bg-indigo-50 text-indigo-600 px-2 py-1 rounded">ArchScope</code> 系统。本手册将指导您如何有效地使用系统的各项功能。</p>

            <div class="bg-blue-50 p-4 rounded-md border-l-4 border-blue-500 mb-6">
                <p class="text-blue-800"><strong>提示：</strong> 如需获取更详细的技术文档，请参阅<a href="project_doc_api.html" class="text-blue-600 hover:underline">接口文档</a>和<a href="project_doc_architecture.html" class="text-blue-600 hover:underline">架构设计</a>章节。</p>
            </div>

            <div class="flex justify-center mb-8">
                <div class="bg-gray-50 rounded-lg p-4 inline-flex space-x-4">
                    <a href="#registration" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-200">注册与登录</a>
                    <a href="#project-management" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-200">项目管理</a>
                    <a href="#documentation" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-200">文档查看</a>
                    <a href="#version-comparison" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-200">版本对比</a>
                </div>
            </div>

            <h2 id="registration" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">如何注册和登录</h2>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div>
                    <h3 class="text-xl font-semibold text-gray-700 mb-3">注册新账户</h3>
                    <ol class="space-y-2 list-decimal pl-5">
                        <li class="text-gray-700">访问 ArchScope 首页，点击右上角的"注册"按钮</li>
                        <li class="text-gray-700">填写必要的信息，包括用户名、邮箱和密码</li>
                        <li class="text-gray-700">点击"创建账户"按钮完成注册</li>
                        <li class="text-gray-700">验证您的邮箱地址（通过点击发送到您邮箱的链接）</li>
                    </ol>
                    <div class="mt-4 p-3 bg-yellow-50 rounded-md border border-yellow-200">
                        <p class="text-sm text-yellow-800"><i class="fas fa-info-circle mr-1"></i> 企业用户可以使用单点登录 (SSO) 功能，请联系您的系统管理员获取详情。</p>
                    </div>
                </div>
                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200">
                    <h3 class="text-xl font-semibold text-gray-700 mb-3">登录系统</h3>
                    <ol class="space-y-2 list-decimal pl-5">
                        <li class="text-gray-700">访问 ArchScope 首页，点击右上角的"登录"按钮</li>
                        <li class="text-gray-700">输入您的用户名/邮箱和密码</li>
                        <li class="text-gray-700">点击"登录"按钮</li>
                        <li class="text-gray-700">如果启用了两因素认证，请按照提示完成验证</li>
                    </ol>
                    <div class="mt-4 flex items-center p-3 bg-green-50 rounded-md border border-green-200">
                        <i class="fas fa-lightbulb text-green-600 mr-2"></i>
                        <p class="text-sm text-green-800">建议启用两因素认证以提高账户安全性。</p>
                    </div>
                </div>
            </div>

            <h2 id="project-management" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">如何添加和管理项目</h2>

            <div class="mb-6">
                <h3 class="text-xl font-semibold text-gray-700 mb-3">添加新项目</h3>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4">
                    <div class="flex items-center">
                        <div class="bg-indigo-100 text-indigo-800 rounded-full h-6 w-6 flex items-center justify-center mr-3">1</div>
                        <div>
                            <h4 class="font-medium text-gray-900">导航到项目页面</h4>
                            <p class="text-gray-600 mb-0">登录后，点击导航栏中的"项目"选项，然后点击"添加新项目"按钮。</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4">
                    <div class="flex items-center">
                        <div class="bg-indigo-100 text-indigo-800 rounded-full h-6 w-6 flex items-center justify-center mr-3">2</div>
                        <div>
                            <h4 class="font-medium text-gray-900">填写项目信息</h4>
                            <p class="text-gray-600 mb-0">输入项目名称、描述，并选择项目类型。如果是代码仓库，需要提供仓库地址。</p>
                        </div>
                    </div>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center">
                        <div class="bg-indigo-100 text-indigo-800 rounded-full h-6 w-6 flex items-center justify-center mr-3">3</div>
                        <div>
                            <h4 class="font-medium text-gray-900">配置项目设置</h4>
                            <p class="text-gray-600 mb-0">设置访问权限、文档生成频率等选项，然后点击"创建项目"按钮。</p>
                        </div>
                    </div>
                </div>
            </div>

            <h2 id="documentation" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">如何查看项目文档</h2>
            <p class="mb-4">ArchScope 提供了多种方式查看和浏览项目文档：</p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-list-ul text-indigo-600 mr-2"></i>
                        <h3 class="font-medium text-gray-900">目录浏览</h3>
                    </div>
                    <p class="text-sm text-gray-600">通过左侧导航栏浏览文档的不同章节和部分。</p>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-search text-indigo-600 mr-2"></i>
                        <h3 class="font-medium text-gray-900">搜索功能</h3>
                    </div>
                    <p class="text-sm text-gray-600">使用顶部的搜索框查找特定内容或关键词。</p>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                    <div class="flex items-center mb-2">
                        <i class="fas fa-bookmark text-indigo-600 mr-2"></i>
                        <h3 class="font-medium text-gray-900">书签功能</h3>
                    </div>
                    <p class="text-sm text-gray-600">为常用文档添加书签，方便快速访问。</p>
                </div>
            </div>

            <h2 id="version-comparison" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">如何使用版本对比功能</h2>
            <p class="mb-4">版本对比功能允许您比较项目文档的不同版本，查看变更内容：</p>

            <div class="bg-gray-50 p-5 rounded-lg border border-gray-200 mb-6">
                <ol class="space-y-3 list-decimal pl-5">
                    <li class="text-gray-700">
                        <span class="font-medium text-gray-900">选择基准版本</span> - 在侧边栏底部的版本选择器中选择一个版本作为基准
                    </li>
                    <li class="text-gray-700">
                        <span class="font-medium text-gray-900">点击版本对比按钮</span> - 点击侧边栏底部的"版本对比"按钮
                    </li>
                    <li class="text-gray-700">
                        <span class="font-medium text-gray-900">选择比较版本</span> - 在对比页面中选择要与基准版本进行比较的另一个版本
                    </li>
                    <li class="text-gray-700">
                        <span class="font-medium text-gray-900">查看差异</span> - 系统将显示两个版本之间的差异，添加的内容以绿色标记，删除的内容以红色标记
                    </li>
                </ol>
            </div>

            <div class="bg-indigo-50 p-4 rounded-md border-l-4 border-indigo-500 mb-6">
                <p class="text-indigo-800"><strong>小贴士：</strong> 您可以通过点击文档中的特定部分，查看该部分在不同版本间的详细变更历史。</p>
            </div>

            <h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">常见问题解答</h2>

            <div class="space-y-4 mb-6">
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                    <h3 class="font-medium text-gray-900 mb-2">如何导出文档？</h3>
                    <p class="text-gray-600">点击文档页面右上角的"导出"按钮，选择所需的格式（PDF、HTML 或 Markdown）进行导出。</p>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                    <h3 class="font-medium text-gray-900 mb-2">如何共享文档链接？</h3>
                    <p class="text-gray-600">点击文档页面右上角的"分享"按钮，复制生成的链接或直接通过邮件分享。</p>
                </div>
                <div class="bg-white p-4 rounded-lg shadow-sm border border-gray-200">
                    <h3 class="font-medium text-gray-900 mb-2">如何提交文档反馈？</h3>
                    <p class="text-gray-600">在文档页面底部点击"提交反馈"按钮，填写反馈表单并提交。</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Mermaid JS -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                flowchart: { useMaxWidth: true, htmlLabels: true }
            });

            // 将 pre > code.language-mermaid 转换为 div.mermaid
            document.querySelectorAll('pre > code.language-mermaid').forEach(function(codeBlock) {
                const content = codeBlock.textContent;
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = content;

                const preElement = codeBlock.parentElement;
                preElement.parentElement.replaceChild(mermaidDiv, preElement);
            });

            // 重新初始化 Mermaid
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        });
    </script>
</body>
</html>
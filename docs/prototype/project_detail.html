<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ArchScope - 项目详情</title>
    <link rel="icon" href="images/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="images/favicon.ico" type="image/x-icon">
    <!-- Tailwind CSS CDN -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <!-- FontAwesome CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* Custom styles for layout */
        :root {
            --primary-color: #4F46E5; /* Indigo-600 */
            --primary-hover: #4338CA; /* Indigo-700 */
            --sidebar-bg: #1E293B; /* Slate-800 */
            --sidebar-header-bg: #0F172A; /* Slate-900 */
        }

        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: #f9fafb; /* Gray-50 */
        }

        .content-card {
            background-color: white;
            border-radius: 0.5rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
            transition: all 0.3s ease;
        }

        .content-card:hover {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        /* Custom button styles */
        .btn-primary {
            background-color: var(--primary-color);
            color: white;
            transition: all 0.2s ease;
        }

        .btn-primary:hover {
            background-color: var(--primary-hover);
        }

        /* Custom select styles */
        select {
            background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
            background-position: right 0.5rem center;
            background-repeat: no-repeat;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
        }

        .form-select {
            display: block;
            width: 100%;
            padding: 0.375rem 2.25rem 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #1F2937;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #D1D5DB;
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-select:focus {
            border-color: #4F46E5;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
        }

        /* Custom input styles */
        .form-input {
            display: block;
            width: 100%;
            height: calc(1.5em + 0.75rem + 2px);
            padding: 0.375rem 0.75rem;
            font-size: 1rem;
            font-weight: 400;
            line-height: 1.5;
            color: #1F2937;
            background-color: #fff;
            background-clip: padding-box;
            border: 1px solid #D1D5DB;
            border-radius: 0.375rem;
            transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
        }

        .form-input:focus {
            border-color: #4F46E5;
            outline: 0;
            box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
        }

        /* Input with icon styles */
        .input-with-icon {
            position: relative;
        }

        .input-with-icon .form-input {
            padding-left: 2.5rem;
        }

        .input-with-icon .input-icon {
            position: absolute;
            left: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            color: #9CA3AF;
            pointer-events: none;
            z-index: 10;
        }

        /* Animation effects */
        .animate-fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        .animate-slide-up {
            animation: slideUp 0.5s ease-out;
        }

        .animate-slide-down {
            animation: slideDown 0.3s ease-out;
        }

        .animate-scale {
            transition: transform 0.3s ease;
        }

        .animate-scale:hover {
            transform: scale(1.02);
        }

        .animate-button {
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .animate-button:after {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            width: 5px;
            height: 5px;
            background: rgba(255, 255, 255, 0.5);
            opacity: 0;
            border-radius: 100%;
            transform: scale(1, 1) translate(-50%);
            transform-origin: 50% 50%;
        }

        .animate-button:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
        }

        .animate-button:active:after {
            animation: ripple 1s ease-out;
        }

        @keyframes fadeIn {
            0% { opacity: 0; }
            100% { opacity: 1; }
        }

        @keyframes slideUp {
            0% { transform: translateY(20px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        @keyframes slideDown {
            0% { transform: translateY(-20px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        @keyframes ripple {
            0% {
                transform: scale(0, 0);
                opacity: 1;
            }
            20% {
                transform: scale(25, 25);
                opacity: 1;
            }
            100% {
                opacity: 0;
                transform: scale(40, 40);
            }
        }
    </style>
</head>
<body class="bg-gray-100 font-sans">

    <nav class="bg-gray-800 p-4">
        <div class="container mx-auto flex justify-between items-center">
            <a href="project_list.html" class="text-white flex items-center">
                <div class="bg-gray-800 rounded-full p-1 flex items-center justify-center mr-2">
                    <img src="images/logo.png" alt="ArchScope" class="h-8 w-8">
                </div>
                <span class="text-2xl font-bold">ArchScope</span>
            </a>
            <div class="flex space-x-4">
                <a href="project_list.html" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">项目列表</a>
                <a href="task_queue.html" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">任务队列</a>
                <a href="register_project.html" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium">注册项目</a>
            </div>
        </div>
    </nav>

    <div class="container mx-auto p-6">
        <div class="flex items-center justify-between mb-6 animate-fade-in">
            <h1 class="text-3xl font-bold text-gray-800">项目详情</h1>
            <a href="project_list.html" class="text-indigo-600 hover:text-indigo-900 flex items-center animate-button">
                <i class="fas fa-arrow-left mr-2"></i> 返回项目列表
            </a>
        </div>

        <div class="content-card bg-white shadow-md rounded-lg p-6 mb-6 animate-slide-up">
            <div class="flex items-center mb-4">
                <div class="flex-shrink-0 h-16 w-16">
                    <!-- Example Image from Unsplash -->
                    <img class="h-16 w-16 rounded-full" src="https://images.unsplash.com/photo-1531297484001-80022131f5a1?auto=format&fit=crop&w=150&q=80" alt="Project Icon">
                </div>
                <div class="ml-4">
                    <h2 class="text-2xl font-semibold text-gray-900">架构鹰眼 ArchScope</h2>
                    <p class="text-gray-600">仓库地址: [ArchScope 仓库地址]</p> <!-- Placeholder for ArchScope repo -->
                    <div class="text-lg text-yellow-500 mt-1">
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i>
                        <i class="fas fa-star"></i> <!-- Assuming ArchScope is a 5-star project -->
                        <span class="text-gray-600 text-sm ml-2">(5/5 星)</span>
                    </div>
                </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div class="animate-fade-in" style="animation-delay: 0.2s;">
                    <h3 class="text-xl font-semibold text-gray-800 mb-3">文档网站</h3>
                    <ul class="space-y-2">
                        <li><a href="project_doc_home.html" class="text-indigo-600 hover:text-indigo-900 animate-button"><i class="fas fa-file-alt mr-2"></i> 产品简介</a></li>
                        <li><a href="project_doc_architecture.html" class="text-indigo-600 hover:text-indigo-900 animate-button"><i class="fas fa-sitemap mr-2"></i> 架构设计</a></li>
                        <li><a href="#" class="text-indigo-600 hover:text-indigo-900 animate-button"><i class="fas fa-book-open mr-2"></i> 用户手册</a></li>
                        <li><a href="#" class="text-indigo-600 hover:text-indigo-900 animate-button"><i class="fas fa-file-code mr-2"></i> 接口文档</a></li>
                        <li><a href="#" class="text-indigo-600 hover:text-indigo-900 animate-button"><i class="fas fa-file-alt mr-2"></i> llms.txt</a></li>
                        <li><a href="project_doc_home.html" class="text-green-600 hover:text-green-900 font-semibold animate-button"><i class="fas fa-globe mr-2"></i> 访问生成的网站</a></li>
                    </ul>
                </div>
                <div class="animate-fade-in" style="animation-delay: 0.4s;">
                    <h3 class="text-xl font-semibold text-gray-800 mb-3">统计信息</h3>
                    <div class="bg-gray-50 rounded-lg p-4 animate-scale">
                        <div class="flex items-center justify-between mb-2">
                            <span class="text-gray-600">总访问量:</span>
                            <span class="text-gray-900 font-semibold">5,678</span> <!-- Updated visit count -->
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-gray-600">最后更新时间:</span>
                            <span class="text-gray-900 font-semibold">2024-04-27 14:00</span> <!-- Updated last update time -->
                        </div>
                        <!-- Add more stats here if needed -->
                    </div>

                    <h3 class="text-xl font-semibold text-gray-800 mt-6 mb-3">最近任务</h3>
                     <ul class="space-y-2 text-sm text-gray-700">
                        <li class="animate-scale"><i class="fas fa-check-circle text-green-500 mr-2"></i> 2024-04-27 14:00 - 文档更新 (成功)</li> <!-- Updated task time -->
                        <li class="animate-scale"><i class="fas fa-check-circle text-green-500 mr-2"></i> 2024-04-26 11:00 - 文档更新 (成功)</li> <!-- Updated task time -->
                        <li class="animate-scale"><i class="fas fa-exclamation-circle text-red-500 mr-2"></i> 2024-04-25 16:30 - 文档更新 (失败)</li> <!-- Updated task time -->
                        <!-- Add more recent tasks -->
                    </ul>
                </div>
            </div>
        </div>

        <!-- Could add sections for tasks, settings, etc. -->

    </div>

    <!-- Mermaid JS -->
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            mermaid.initialize({
                startOnLoad: true,
                theme: 'default',
                securityLevel: 'loose',
                flowchart: { useMaxWidth: true, htmlLabels: true }
            });

            // 将 pre > code.language-mermaid 转换为 div.mermaid
            document.querySelectorAll('pre > code.language-mermaid').forEach(function(codeBlock) {
                const content = codeBlock.textContent;
                const mermaidDiv = document.createElement('div');
                mermaidDiv.className = 'mermaid';
                mermaidDiv.textContent = content;

                const preElement = codeBlock.parentElement;
                preElement.parentElement.replaceChild(mermaidDiv, preElement);
            });

            // 重新初始化 Mermaid
            mermaid.init(undefined, document.querySelectorAll('.mermaid'));
        });
    </script>
</body>
</html>
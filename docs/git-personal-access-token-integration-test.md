# Git个人访问令牌集成测试指南

## 测试目标

验证ArchScope系统能够正确使用配置的个人访问令牌访问私有Git仓库。

## 测试环境准备

### 1. 配置测试令牌

在 `arch-scope-main/src/main/resources/application.yml` 中配置测试令牌：

```yaml
archscope:
  git:
    servers:
      hosts:
        gitlab.yeepay.com:
          supportsHttps: true
          supportsHttp: false
          suggestHttpsOnSshFailure: false
          personalAccessToken: "**************************"  # 替换为实际令牌
          tokenUsername: "oauth2"
```

### 2. 启动应用

```bash
cd arch-scope-main
mvn spring-boot:run
```

## 测试用例

### 测试用例1：验证配置加载

**目标**: 确认系统正确加载了个人访问令牌配置

**步骤**:
1. 启动应用
2. 查看日志输出

**期望结果**:
```
INFO - gitlab.yeepay.com 配置: suggestHttpsOnSshFailure=false, supportsHttps=true, hasPersonalAccessToken=true
```

### 测试用例2：API调用测试

**目标**: 通过API验证私有仓库访问

**步骤**:
1. 使用curl或Postman调用验证API：

```bash
curl -X POST "http://localhost:8080/api/git-repository/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "repositoryUrl": "https://gitlab.yeepay.com/private/repo.git",
    "fetchDetails": true
  }'
```

**期望结果**:
- 返回状态码: 200
- 响应包含仓库信息
- 日志显示使用配置的令牌进行认证

### 测试用例3：认证优先级测试

**目标**: 验证认证信息的优先级顺序

**步骤**:
1. 在请求中提供用户名密码：

```bash
curl -X POST "http://localhost:8080/api/git-repository/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "repositoryUrl": "https://gitlab.yeepay.com/private/repo.git",
    "fetchDetails": true,
    "username": "testuser",
    "password": "testpass"
  }'
```

**期望结果**:
- 系统优先使用请求中的认证信息
- 日志显示: "使用请求中提供的认证信息: username=testuser"

### 测试用例4：公开仓库访问测试

**目标**: 验证公开仓库的匿名访问

**步骤**:
1. 测试访问公开仓库：

```bash
curl -X POST "http://localhost:8080/api/git-repository/validate" \
  -H "Content-Type: application/json" \
  -d '{
    "repositoryUrl": "https://github.com/spring-projects/spring-boot.git",
    "fetchDetails": true
  }'
```

**期望结果**:
- 成功访问公开仓库
- 日志显示: "未找到认证信息，尝试匿名访问"

## 错误场景测试

### 错误场景1：无效令牌

**步骤**:
1. 配置无效的个人访问令牌
2. 尝试访问私有仓库

**期望结果**:
- 返回认证失败错误
- 提供有用的错误信息

### 错误场景2：令牌权限不足

**步骤**:
1. 配置权限不足的令牌
2. 尝试访问需要更高权限的仓库

**期望结果**:
- 返回权限不足错误
- 建议检查令牌权限

## 性能测试

### 测试目标
验证使用个人访问令牌不会显著影响系统性能

### 测试方法
1. 并发调用API 100次
2. 记录响应时间
3. 对比使用令牌前后的性能差异

### 期望结果
- 平均响应时间增加不超过10%
- 无内存泄漏或连接池耗尽

## 安全测试

### 测试目标
确保个人访问令牌不会在日志或响应中泄露

### 测试方法
1. 检查所有日志输出
2. 检查API响应内容
3. 检查错误信息

### 期望结果
- 日志中不包含完整的令牌值
- API响应中不包含令牌信息
- 错误信息不泄露敏感信息

## 测试检查清单

- [ ] 配置正确加载
- [ ] 私有仓库访问成功
- [ ] 认证优先级正确
- [ ] 公开仓库匿名访问
- [ ] 无效令牌错误处理
- [ ] 权限不足错误处理
- [ ] 性能无显著下降
- [ ] 令牌信息不泄露
- [ ] 日志信息完整且安全

## 故障排除

### 常见问题

1. **配置不生效**
   - 检查YAML语法
   - 确认服务器主机名匹配
   - 重启应用

2. **认证失败**
   - 验证令牌有效性
   - 检查令牌权限
   - 确认令牌未过期

3. **日志级别**
   - 设置日志级别为DEBUG以获取详细信息：
   ```yaml
   logging:
     level:
       com.archscope.domain.service: DEBUG
   ```

## 自动化测试脚本

```bash
#!/bin/bash
# git-token-integration-test.sh

BASE_URL="http://localhost:8080/api/git-repository"

echo "=== Git个人访问令牌集成测试 ==="

# 测试1: 私有仓库访问
echo "测试1: 私有仓库访问"
curl -s -X POST "$BASE_URL/validate" \
  -H "Content-Type: application/json" \
  -d '{"repositoryUrl": "https://gitlab.yeepay.com/private/repo.git", "fetchDetails": true}' \
  | jq '.success'

# 测试2: 公开仓库访问
echo "测试2: 公开仓库访问"
curl -s -X POST "$BASE_URL/validate" \
  -H "Content-Type: application/json" \
  -d '{"repositoryUrl": "https://github.com/spring-projects/spring-boot.git", "fetchDetails": true}' \
  | jq '.success'

echo "=== 测试完成 ==="
```

使用方法：
```bash
chmod +x git-token-integration-test.sh
./git-token-integration-test.sh
```

# API文档

本目录包含ArchScope项目的所有API相关文档。

## 📋 文档列表

### 核心API文档
- [API设计规范](api-design.md) - RESTful API设计标准和最佳实践
- [API路径映射](api-path-mapping.md) - API路径规范和映射关系
- [LLM集成API](llm-integration-api.md) - LLM服务集成接口文档

### API规范
- [OpenAPI规范](../api-spec.yaml) - 标准API规范文件

## 🎯 使用指南

### 开发者
1. 首先阅读 [API设计规范](api-design.md) 了解设计原则
2. 查看 [API路径映射](api-path-mapping.md) 了解路径规范
3. 参考 [OpenAPI规范](../api-spec.yaml) 进行具体实现

### 集成开发
- 查看 [LLM集成API](llm-integration-api.md) 了解LLM服务集成方式

## 📝 维护说明

- API文档应与代码实现保持同步
- 新增API时需要更新相应的文档
- 重要变更需要在CHANGELOG中记录
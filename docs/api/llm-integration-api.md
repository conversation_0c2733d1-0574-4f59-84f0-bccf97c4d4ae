# ArchScope LLM服务集成API文档

## 概述

本文档描述了ArchScope与外部LLM服务之间的API接口规范，用于实现代码分析任务的分布式处理。

## 工作流程

```mermaid
sequenceDiagram
    participant AS as ArchScope
    participant LLM as LLM服务
    participant Worker as LL<PERSON> Worker

    Note over AS,Worker: 1. 任务创建阶段
    AS->>AS: 用户触发项目分析
    AS->>AS: 创建LLM任务(PENDING状态)
    AS->>AS: 存储任务到数据库

    Note over AS,Worker: 2. 任务拉取阶段
    Worker->>AS: POST /api/v1/llm-tasks/pull
    AS->>AS: 查找PENDING任务
    AS->>AS: 锁定任务(PROCESSING状态)
    AS->>Worker: 返回任务数据

    Note over AS,Worker: 3. 任务处理阶段
    Worker->>Worker: 克隆代码仓库
    Worker->>Worker: 分析代码并生成文档
    Worker->>AS: POST /api/v1/llm-tasks/{taskId}/callback

    Note over AS,Worker: 4. 结果处理阶段
    AS->>AS: 更新任务状态(COMPLETED/FAILED)
    AS->>AS: 存储生成的文档
    AS->>AS: 触发后续流程
```

## API接口规范

### 1. 任务拉取接口

**接口地址**: `POST /api/v1/llm-tasks/pull`

**请求参数**:
```json
{
  "workerId": "llm-worker-node-01",
  "workerVersion": "1.0.0",
  "supportedTaskTypes": ["CODE_ANALYSIS", "DOC_GENERATION"],
  "maxConcurrentTasks": 3
}
```

**响应格式**:

```json
{
  "hasTask": true,
  "taskId": 12345,
  "projectId": 67890,
  "taskType": "CODE_PARSE",
  "priority": 5,
  "createdAt": "2025-06-21T10:30:00",
  "timeoutAt": "2025-06-21T11:00:00",
  "inputData": {
    "schemaVersion": "1.2",
    "repositoryInfo": {
      "cloneUrl": "https://github.com/example/project.git",
      "commitId": "a1b2c3d4e5f6789012345678901234567890abcd",
      "branchName": "main"
    }
  },
  "parameters": {
    "additionalConfig": "value"
  }
}
```

**无任务时的响应**:
```json
{
  "hasTask": false,
  "message": "No pending tasks available"
}
```

### 2. 任务交付接口

**接口地址**: `POST /api/v1/llm-tasks/{taskId}/callback`

**请求格式**:

```json
{
  "taskId": 12345,
  "status": "SUCCESS",
  "documentContent": "# 项目架构文档\n\n## 概述\n...",
  "errorMessage": null,
  "errorDetails": null,
  "startTime": "2025-06-21T10:30:00",
  "endTime": "2025-06-21T10:45:00",
  "executionTimeMs": 900000,
  "processedFileCount": 150,
  "documentType": "ARCHITECTURE_OVERVIEW",
  "metadata": {
    "classCount": 45,
    "methodCount": 320,
    "packageCount": 12
  }
}
```

**响应格式**:

```json
{
  "success": true,
  "message": "任务结果已成功处理",
  "taskId": 12345
}
```

**失败时的响应**:
```json
{
  "success": false,
  "message": "任务结果处理失败",
  "taskId": 12345
}
```

### 3. 任务创建接口 (内部使用)

**接口地址**: `POST /api/tasks` (使用标准任务创建接口)

**请求格式**:

```json
{
  "projectId": 67890,
  "repositoryUrl": "https://github.com/example/project.git",
  "commitId": "a1b2c3d4e5f6789012345678901234567890abcd",
  "branchName": "main",
  "taskType": "CODE_PARSE",
  "priority": 5
}
```

### 4. 超时任务清理接口 (系统内部)

**说明**: 超时任务由系统任务调度器自动处理，无需外部调用

**响应格式**:

```json
{
  "success": true,
  "message": "超时任务清理完成",
  "timeoutTaskCount": 3
}
```

## 数据模型

### 任务输入数据 (LlmTaskInputDto)

```json
{
  "schemaVersion": "1.2",
  "repositoryInfo": {
    "cloneUrl": "string (required)",
    "commitId": "string (required, 40位十六进制)",
    "branchName": "string (optional)"
  }
}
```

### 任务状态枚举

- `PENDING`: 等待处理
- `PROCESSING`: 正在处理 (已被工作节点锁定)
- `COMPLETED`: 处理完成
- `FAILED`: 处理失败
- `TIMEOUT`: 处理超时

## 错误处理

### 常见错误码

- `400 Bad Request`: 请求参数错误
- `404 Not Found`: 任务不存在
- `409 Conflict`: 任务状态冲突
- `500 Internal Server Error`: 服务器内部错误

### 错误响应格式

```json
{
  "success": false,
  "message": "错误描述",
  "errorCode": "ERROR_CODE",
  "details": "详细错误信息"
}
```

## 安全考虑

1. **认证**: 建议在生产环境中添加API密钥认证
2. **授权**: 限制只有授权的LLM工作节点可以访问接口
3. **速率限制**: 防止恶意请求导致系统过载
4. **数据验证**: 严格验证输入数据格式和内容

## 监控和日志

### 关键指标

- 任务创建速率
- 任务完成速率
- 任务超时率
- 平均处理时间
- 错误率

### 日志记录

- 任务生命周期事件
- API调用记录
- 错误和异常信息
- 性能指标

## 部署配置

### 环境变量

```properties
# 任务超时时间 (分钟)
archscope.llm.task.timeout=30

# 超时检查间隔 (毫秒)
archscope.scheduler.llm-timeout.interval=300000

# 是否启用超时检查
archscope.scheduler.llm-timeout.enabled=true
```

### 数据库配置

确保Task表包含以下字段用于LLM任务管理:
- `parameters`: JSON字段，存储任务输入数据和超时信息
- `result`: TEXT字段，存储生成的文档内容
- `error_log`: TEXT字段，存储错误信息

## 示例代码

### LLM工作节点示例 (Python)

```python
import requests
import time
import json

class LLMWorker:
    def __init__(self, archscope_url, worker_type="default"):
        self.archscope_url = archscope_url
        self.worker_type = worker_type
    
    def pull_task(self):
        """拉取任务"""
        response = requests.post(
            f"{self.archscope_url}/api/v1/llm-tasks/pull",
            json={
                "workerId": self.worker_type,
                "workerVersion": "1.0.0",
                "maxConcurrentTasks": 1
            }
        )
        return response.json()
    
    def deliver_result(self, task_id, status, content=None, error=None):
        """提交结果"""
        payload = {
            "taskId": task_id,
            "status": status,
            "documentContent": content,
            "errorMessage": error,
            "startTime": "2025-06-21T10:30:00",
            "endTime": "2025-06-21T10:45:00",
            "executionTimeMs": 900000
        }
        
        response = requests.post(
            f"{self.archscope_url}/api/v1/llm-tasks/{task_id}/callback",
            json=payload
        )
        return response.json()
    
    def process_task(self, task_data):
        """处理任务"""
        try:
            # 克隆仓库并分析代码
            repo_info = task_data["inputData"]["repositoryInfo"]
            clone_url = repo_info["cloneUrl"]
            commit_id = repo_info["commitId"]
            
            # TODO: 实现实际的代码分析逻辑
            document_content = self.analyze_code(clone_url, commit_id)
            
            return "SUCCESS", document_content, None
            
        except Exception as e:
            return "FAILED", None, str(e)
    
    def run(self):
        """主循环"""
        while True:
            try:
                # 拉取任务
                task_response = self.pull_task()
                
                if not task_response.get("hasTask"):
                    time.sleep(10)  # 等待10秒后重试
                    continue
                
                task_id = task_response["taskId"]
                print(f"处理任务: {task_id}")
                
                # 处理任务
                status, content, error = self.process_task(task_response)
                
                # 提交结果
                result = self.deliver_result(task_id, status, content, error)
                print(f"任务完成: {task_id}, 结果: {result}")
                
            except Exception as e:
                print(f"工作节点错误: {e}")
                time.sleep(30)  # 错误后等待30秒

# 使用示例
worker = LLMWorker("http://localhost:8080")
worker.run()
```

## 版本历史

- **v1.2**: 当前版本，支持基本的任务拉取和交付功能
- **v1.1**: 初始版本，基础API设计
- **v1.0**: 概念设计阶段

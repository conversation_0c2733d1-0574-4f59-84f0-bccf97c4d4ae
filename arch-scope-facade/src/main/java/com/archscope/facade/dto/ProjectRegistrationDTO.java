package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 项目注册数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProjectRegistrationDTO {
    
    @NotBlank(message = "项目名称不能为空")
    @Size(min = 2, max = 100, message = "项目名称长度必须在2-100个字符之间")
    private String name;
    
    @Size(max = 500, message = "项目描述长度不能超过500个字符")
    private String description;
    
    @NotBlank(message = "仓库URL不能为空")
    @Pattern(regexp = "^(https?://.*|git@.*:.*)", message = "仓库URL格式不正确，支持HTTPS和SSH格式")
    private String repositoryUrl;
    
    private String branch;
    
    private String type;
    
    private String icon;
    
    private Long creatorId;
} 
package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;

/**
 * 项目概要数据传输对象（用于列表显示）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "项目概要数据传输对象，用于列表显示")
public class ProjectSummaryDTO {
    @Schema(description = "项目唯一标识符")
    private Long id;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "项目简要描述")
    private String description;

    @Schema(description = "代码仓库URL地址")
    private String repositoryUrl;

    @Schema(description = "代码分支名称")
    private String branch;

    @Schema(description = "项目状态")
    private String status;

    @Schema(description = "项目评分")
    private Double rating;

    @Schema(description = "项目图标URL")
    private String icon;

    @Schema(description = "项目类型")
    private String type;

    @Schema(description = "项目创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "项目最后更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "项目是否激活")
    private Boolean active;
}
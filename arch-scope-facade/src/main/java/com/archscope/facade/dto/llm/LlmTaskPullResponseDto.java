package com.archscope.facade.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * LLM任务拉取响应数据传输对象
 * 用于向LLM服务返回待处理的任务信息
 * 符合API规范中的LLMTaskPullResponseDTO定义
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LlmTaskPullResponseDto {

    /**
     * 任务ID (UUID格式)
     */
    @JsonProperty("taskId")
    private String taskId;

    /**
     * 项目ID (UUID格式)
     */
    @JsonProperty("projectId")
    private String projectId;

    /**
     * 任务类型
     */
    @JsonProperty("taskType")
    private String taskType;

    /**
     * 任务优先级
     */
    @JsonProperty("priority")
    private Integer priority;

    /**
     * 任务输入数据 (符合LlmTaskInputDto格式)
     */
    @JsonProperty("inputData")
    private LlmTaskInputDto inputData;

    /**
     * 任务创建时间
     */
    @JsonProperty("createdAt")
    private LocalDateTime createdAt;

    /**
     * 任务超时时间
     */
    @JsonProperty("timeoutAt")
    private LocalDateTime timeoutAt;

    /**
     * 任务参数 (额外的配置信息)
     */
    @JsonProperty("parameters")
    private Map<String, Object> parameters;

    /**
     * 是否有任务可用
     */
    @JsonProperty("hasTask")
    private Boolean hasTask;

    /**
     * 消息 (当没有任务时的说明)
     */
    @JsonProperty("message")
    private String message;

    /**
     * 创建空响应 (无任务可用)
     */
    public static LlmTaskPullResponseDto noTaskAvailable() {
        return LlmTaskPullResponseDto.builder()
                .hasTask(false)
                .message("No pending tasks available")
                .build();
    }

    /**
     * 创建任务响应 (使用String类型ID)
     */
    public static LlmTaskPullResponseDto withTask(String taskId, String projectId, String taskType,
                                                  Integer priority, LlmTaskInputDto inputData,
                                                  LocalDateTime createdAt, LocalDateTime timeoutAt,
                                                  Map<String, Object> parameters) {
        return LlmTaskPullResponseDto.builder()
                .hasTask(true)
                .taskId(taskId)
                .projectId(projectId)
                .taskType(taskType)
                .priority(priority != null ? priority : 5) // 默认优先级为5
                .inputData(inputData)
                .createdAt(createdAt)
                .timeoutAt(timeoutAt)
                .parameters(parameters)
                .build();
    }

    /**
     * 创建任务响应 (兼容Long类型ID)
     */
    public static LlmTaskPullResponseDto withTask(Long taskId, Long projectId, String taskType,
                                                  Integer priority, LlmTaskInputDto inputData,
                                                  LocalDateTime createdAt, LocalDateTime timeoutAt,
                                                  Map<String, Object> parameters) {
        return withTask(
            taskId != null ? taskId.toString() : null,
            projectId != null ? projectId.toString() : null,
            taskType, priority, inputData, createdAt, timeoutAt, parameters
        );
    }

    /**
     * 兼容旧版本的Object参数类型
     */
    public static LlmTaskPullResponseDto withTask(Long taskId, Long projectId, String taskType,
                                                  Integer priority, LlmTaskInputDto inputData,
                                                  LocalDateTime createdAt, LocalDateTime timeoutAt,
                                                  Object parameters) {
        Map<String, Object> paramMap = null;
        if (parameters instanceof Map) {
            paramMap = (Map<String, Object>) parameters;
        }
        return withTask(taskId, projectId, taskType, priority, inputData, createdAt, timeoutAt, paramMap);
    }
}

package com.archscope.facade.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

/**
 * LLM任务交付响应数据传输对象
 * 用于返回任务交付处理结果
 * 符合API规范中的LLMTaskDeliveryResponseDTO定义
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LlmTaskDeliveryResponseDto {

    /**
     * 任务ID (UUID格式)
     */
    @JsonProperty("taskId")
    private String taskId;

    /**
     * 任务状态
     */
    @JsonProperty("status")
    private String status;

    /**
     * 处理时间
     */
    @JsonProperty("processedAt")
    private LocalDateTime processedAt;

    /**
     * 创建成功响应
     */
    public static LlmTaskDeliveryResponseDto success(String taskId, String status) {
        return LlmTaskDeliveryResponseDto.builder()
                .taskId(taskId)
                .status(status)
                .processedAt(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功响应 (兼容Long类型ID)
     */
    public static LlmTaskDeliveryResponseDto success(Long taskId, String status) {
        return success(taskId != null ? taskId.toString() : null, status);
    }
}

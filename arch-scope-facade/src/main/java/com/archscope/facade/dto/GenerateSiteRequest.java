package com.archscope.facade.dto;

import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 生成站点请求DTO
 */
@Data
public class GenerateSiteRequest {
    
    /**
     * 文档版本ID列表
     */
    @NotEmpty(message = "文档版本ID列表不能为空")
    private List<Long> documentVersionIds;
    
    /**
     * 输出路径
     */
    @NotNull(message = "输出路径不能为空")
    private String outputPath;
}

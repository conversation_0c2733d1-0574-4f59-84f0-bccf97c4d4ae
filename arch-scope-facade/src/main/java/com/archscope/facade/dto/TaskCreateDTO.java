package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import javax.validation.constraints.Min;
import javax.validation.constraints.Max;
import java.util.Map;

/**
 * 任务创建数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskCreateDTO {
    
    /**
     * 项目ID
     */
    @NotNull(message = "项目ID不能为空")
    private Long projectId;
    
    /**
     * 任务名称
     */
    @NotBlank(message = "任务名称不能为空")
    @Size(min = 2, max = 100, message = "任务名称长度必须在2-100个字符之间")
    private String name;
    
    /**
     * 任务描述
     */
    @Size(max = 500, message = "任务描述长度不能超过500个字符")
    private String description;
    
    /**
     * 任务类型
     */
    @NotBlank(message = "任务类型不能为空")
    private String type;
    
    /**
     * 任务优先级（1-10，数字越大优先级越高）
     */
    @Min(value = 1, message = "优先级最小值为1")
    @Max(value = 10, message = "优先级最大值为10")
    private Integer priority;
    
    /**
     * 任务参数
     */
    private Map<String, Object> parameters;
    
    /**
     * 分配用户ID
     */
    private Long assigneeId;
    
    /**
     * 关联的Git提交ID
     */
    private String commitId;
}

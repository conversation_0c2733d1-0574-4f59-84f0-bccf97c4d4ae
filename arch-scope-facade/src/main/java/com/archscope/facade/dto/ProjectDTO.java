package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 项目数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "项目数据传输对象")
public class ProjectDTO {
    @Schema(description = "项目唯一标识符")
    private Long id;

    @Schema(description = "项目名称")
    private String name;

    @Schema(description = "项目描述信息")
    private String description;

    @Schema(description = "代码仓库URL地址")
    private String repositoryUrl;

    @Schema(description = "代码分支名称")
    private String branch;

    @Schema(description = "项目状态，如ACTIVE、INACTIVE等")
    private String status;

    @Schema(description = "项目评分，范围0-5")
    private Double rating;

    @Schema(description = "代码行数统计")
    private Long linesOfCode;

    @Schema(description = "文件数量统计")
    private Integer fileCount;

    @Schema(description = "贡献者数量")
    private Integer contributorCount;

    @Schema(description = "项目图标URL")
    private String icon;

    @Schema(description = "项目类型，如Java、Python等")
    private String type;

    @Schema(description = "项目成员ID列表")
    private List<Long> memberIds;

    @Schema(description = "项目文档ID列表")
    private List<Long> documentIds;

    @Schema(description = "项目任务ID列表")
    private List<Long> taskIds;

    @Schema(description = "项目创建者ID")
    private Long creatorId;

    @Schema(description = "项目是否激活状态")
    private Boolean active;

    @Schema(description = "项目创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "项目最后更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "项目最后分析时间")
    private LocalDateTime lastAnalyzedAt;
}
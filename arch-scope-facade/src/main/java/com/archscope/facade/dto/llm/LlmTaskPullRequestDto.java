package com.archscope.facade.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

/**
 * LLM任务拉取请求数据传输对象
 * 用于接收LLM工作节点的任务拉取请求
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LlmTaskPullRequestDto {

    /**
     * 工作节点ID
     */
    @JsonProperty("workerId")
    @NotBlank(message = "工作节点ID不能为空")
    private String workerId;



    /**
     * 工作节点版本 (可选)
     */
    @JsonProperty("workerVersion")
    private String workerVersion;

    /**
     * 支持的任务类型列表 (可选)
     */
    @JsonProperty("supportedTaskTypes")
    private String[] supportedTaskTypes;

    /**
     * 最大并发任务数 (可选)
     */
    @JsonProperty("maxConcurrentTasks")
    private Integer maxConcurrentTasks;
}

package com.archscope.facade.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

/**
 * LLM任务响应数据传输对象
 * 用于接收LLM服务返回的任务结果
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LlmTaskResponseDto {

    /**
     * 任务ID
     */
    @JsonProperty("taskId")
    @NotNull(message = "任务ID不能为空")
    private Long taskId;

    /**
     * 任务执行状态
     */
    @JsonProperty("status")
    @NotBlank(message = "任务状态不能为空")
    private String status; // SUCCESS, FAILED

    /**
     * 生成的文档内容 (Markdown格式)
     */
    @JsonProperty("documentContent")
    private String documentContent;

    /**
     * 错误信息 (当status为FAILED时)
     */
    @JsonProperty("errorMessage")
    private String errorMessage;

    /**
     * 错误详情 (当status为FAILED时)
     */
    @JsonProperty("errorDetails")
    private String errorDetails;

    /**
     * 任务执行开始时间
     */
    @JsonProperty("startTime")
    private LocalDateTime startTime;

    /**
     * 任务执行完成时间
     */
    @JsonProperty("endTime")
    private LocalDateTime endTime;

    /**
     * 执行耗时 (毫秒)
     */
    @JsonProperty("executionTimeMs")
    private Long executionTimeMs;

    /**
     * 处理的文件数量
     */
    @JsonProperty("processedFileCount")
    private Integer processedFileCount;

    /**
     * 生成的文档类型
     */
    @JsonProperty("documentType")
    private String documentType; // ARCHITECTURE_OVERVIEW, API_DOCUMENTATION, etc.

    /**
     * 额外的元数据
     */
    @JsonProperty("metadata")
    private Object metadata;
}

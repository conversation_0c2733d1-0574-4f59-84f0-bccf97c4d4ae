package com.archscope.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.archscope.facade.dto.ResultCode;

/**
 * API响应DTO
 *
 * @param <T> 响应数据类型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse<T> {

    /**
     * 是否成功
     */
    private boolean success;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 创建成功响应
     *
     * @param message 响应消息
     * @param data 响应数据
     * @param <T> 响应数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(String message, T data) {
        return new ApiResponse<>(true, message, data);
    }

    /**
     * 创建成功响应
     *
     * @param data 响应数据
     * @param <T> 响应数据类型
     * @return 成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return success("操作成功", data);
    }

    /**
     * 创建错误响应
     *
     * @param message 错误消息
     * @param <T> 响应数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(false, message, null);
    }

    /**
     * 创建错误响应
     *
     * @param message 错误消息
     * @param data 错误数据
     * @param <T> 响应数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(String message, T data) {
        return new ApiResponse<>(false, message, data);
    }

    /**
     * 创建错误响应
     *
     * @param resultCode 结果码
     * @param message 错误消息
     * @param <T> 响应数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(ResultCode resultCode, String message) {
        return new ApiResponse<>(false, message, null);
    }

    /**
     * 创建错误响应
     *
     * @param resultCode 结果码
     * @param <T> 响应数据类型
     * @return 错误响应
     */
    public static <T> ApiResponse<T> error(ResultCode resultCode) {
        return new ApiResponse<>(false, resultCode.getMessage(), null);
    }

    /**
     * 创建带错误详情的错误响应
     *
     * @param resultCode 结果码
     * @param errorDetail 错误详情
     * @return 错误响应
     */
    public static ApiResponse<ErrorDetail> errorWithDetail(ResultCode resultCode, ErrorDetail errorDetail) {
        return new ApiResponse<>(false, resultCode.getMessage(), errorDetail);
    }

    /**
     * 创建带错误详情的错误响应
     *
     * @param message 错误消息
     * @param errorDetail 错误详情
     * @return 错误响应
     */
    public static ApiResponse<ErrorDetail> errorWithDetail(String message, ErrorDetail errorDetail) {
        return new ApiResponse<>(false, message, errorDetail);
    }
}

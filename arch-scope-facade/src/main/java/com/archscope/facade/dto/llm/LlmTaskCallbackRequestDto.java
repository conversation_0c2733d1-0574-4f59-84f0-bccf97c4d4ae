package com.archscope.facade.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.time.LocalDateTime;
import java.util.List;

/**
 * LLM任务交付回调请求数据传输对象
 * 用于接收LLM服务完成任务后的结果提交
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LlmTaskCallbackRequestDto {

    /**
     * 任务整体状态
     */
    @JsonProperty("overallStatus")
    @NotBlank(message = "任务整体状态不能为空")
    @Pattern(regexp = "^(COMPLETED|FAILED|PARTIAL_SUCCESS)$", 
             message = "任务整体状态必须为COMPLETED、FAILED或PARTIAL_SUCCESS")
    private String overallStatus;

    /**
     * 关联的Git提交ID
     */
    @JsonProperty("commitId")
    @Pattern(regexp = "^[0-9a-f]{40}$", message = "提交ID必须为40位十六进制字符")
    private String commitId;

    /**
     * 任务结果数组，支持多文档类型
     */
    @JsonProperty("results")
    @Valid
    private List<TaskResult> results;

    /**
     * 任务开始时间
     */
    @JsonProperty("startTime")
    private LocalDateTime startTime;

    /**
     * 任务结束时间
     */
    @JsonProperty("endTime")
    private LocalDateTime endTime;

    /**
     * 任务执行时间(毫秒)
     */
    @JsonProperty("executionTimeMs")
    private Long executionTimeMs;

    /**
     * 错误信息 (当overallStatus为FAILED时必填)
     */
    @JsonProperty("errorMessage")
    private String errorMessage;

    /**
     * 错误详情 (可选)
     */
    @JsonProperty("errorDetail")
    private String errorDetail;

    /**
     * 工作节点信息 (可选)
     */
    @JsonProperty("workerInfo")
    private LlmWorkerInfoDto workerInfo;

    /**
     * 任务结果内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class TaskResult {

        /**
         * 文档类型
         */
        @JsonProperty("documentType")
        @NotBlank(message = "文档类型不能为空")
        private String documentType;

        /**
         * 文档标题
         */
        @JsonProperty("documentTitle")
        private String documentTitle;

        /**
         * 文档内容 (Markdown格式)
         */
        @JsonProperty("documentContent")
        private String documentContent;

        /**
         * 文档文件路径
         */
        @JsonProperty("filePath")
        private String filePath;

        /**
         * 生成状态
         */
        @JsonProperty("status")
        @NotBlank(message = "生成状态不能为空")
        @Pattern(regexp = "^(SUCCESS|FAILED|SKIPPED)$", 
                 message = "生成状态必须为SUCCESS、FAILED或SKIPPED")
        private String status;

        /**
         * 错误信息 (当status为FAILED时)
         */
        @JsonProperty("errorMessage")
        private String errorMessage;
    }


}

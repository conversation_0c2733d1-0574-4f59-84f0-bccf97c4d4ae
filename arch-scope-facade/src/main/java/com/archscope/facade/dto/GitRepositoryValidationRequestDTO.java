package com.archscope.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * Git仓库验证请求数据传输对象
 * 用于验证Git仓库URL并获取基本信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GitRepositoryValidationRequestDTO {

    /**
     * Git仓库URL
     */
    @NotBlank(message = "仓库URL不能为空")
    @Pattern(
        regexp = "^(https?://|git@)[\\w\\.-]+[:/][\\w\\.-]+/[\\w\\.-]+(\\.git)?/?$",
        message = "仓库URL格式不正确，支持HTTPS和SSH格式"
    )
    private String repositoryUrl;

    /**
     * 是否获取详细信息（包括分支列表、语言信息等）
     * 默认为false，只获取基本信息
     */
    private Boolean fetchDetails = false;

    /**
     * 认证用户名（可选，用于私有仓库）
     */
    private String username;

    /**
     * 认证密码或Token（可选，用于私有仓库）
     */
    private String password;
}

package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 任务概要数据传输对象（用于列表显示）
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskSummaryDTO {
    
    /**
     * 任务ID
     */
    private Long id;
    
    /**
     * 项目ID
     */
    private Long projectId;
    
    /**
     * 项目名称
     */
    private String projectName;
    
    /**
     * 任务名称
     */
    private String name;
    
    /**
     * 任务类型
     */
    private String type;
    
    /**
     * 任务状态
     */
    private String status;
    
    /**
     * 详细状态描述
     */
    private String detailedStatus;
    
    /**
     * 任务优先级
     */
    private Integer priority;
    
    /**
     * 进度百分比（0-100）
     */
    private Integer progress;
    
    /**
     * 创建时间
     */
    private LocalDateTime createdAt;
    
    /**
     * 开始处理时间
     */
    private LocalDateTime processingStartedAt;
    
    /**
     * 执行时间（毫秒）
     */
    private Long executionTime;
    
    /**
     * 重试次数
     */
    private Integer retryCount;
    
    /**
     * 任务整体状态（COMPLETED/FAILED/PARTIAL_SUCCESS）
     */
    private String overallStatus;
    
    /**
     * 关联的Git提交ID
     */
    private String commitId;
}

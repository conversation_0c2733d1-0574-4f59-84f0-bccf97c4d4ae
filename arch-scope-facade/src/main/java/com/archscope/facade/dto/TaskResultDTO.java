package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务结果数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "任务结果数据传输对象")
public class TaskResultDTO {
    
    /**
     * 任务ID
     */
    @Schema(description = "任务唯一标识符")
    private Long taskId;

    /**
     * 项目ID
     */
    @Schema(description = "关联的项目ID")
    private Long projectId;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态，如PENDING、PROCESSING、COMPLETED、FAILED等")
    private String status;

    /**
     * 任务整体状态（COMPLETED/FAILED/PARTIAL_SUCCESS）
     */
    @Schema(description = "任务整体状态，如COMPLETED、FAILED、PARTIAL_SUCCESS等")
    private String overallStatus;

    /**
     * 任务结果
     */
    @Schema(description = "任务执行结果")
    private String result;

    /**
     * 任务结果JSON字符串（支持多文档类型）
     */
    @Schema(description = "任务结果JSON字符串，支持多文档类型的结果")
    private String results;

    /**
     * 错误信息
     */
    @Schema(description = "任务执行过程中的错误信息")
    private String errorMessage;

    /**
     * 错误详情
     */
    @Schema(description = "详细的错误信息和堆栈跟踪")
    private String errorDetails;

    /**
     * 执行时间（毫秒）
     */
    @Schema(description = "任务实际执行时间，单位毫秒")
    private Long executionTime;

    /**
     * 开始时间
     */
    @Schema(description = "任务开始执行的时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @Schema(description = "任务结束执行的时间")
    private LocalDateTime endTime;

    /**
     * 处理的文件数量
     */
    @Schema(description = "任务处理的文件数量")
    private Integer processedFileCount;

    /**
     * 生成的文档类型
     */
    @Schema(description = "生成的文档类型，如OVERVIEW、API_DOC等")
    private String documentType;
    
    /**
     * 额外的元数据
     */
    private Map<String, Object> metadata;
    
    /**
     * 关联的Git提交ID
     */
    private String commitId;
}

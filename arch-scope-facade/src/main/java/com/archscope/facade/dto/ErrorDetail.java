package com.archscope.facade.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.Instant;
import java.util.List;
import java.util.Map;

/**
 * 错误详情DTO
 * 提供更详细的错误信息，特别适用于服务发现相关的错误
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErrorDetail {

    /**
     * 错误代码
     */
    private String errorCode;

    /**
     * 错误消息
     */
    private String message;

    /**
     * 详细描述
     */
    private String details;

    /**
     * 错误发生时间
     */
    private Instant timestamp;

    /**
     * 错误路径
     */
    private String path;

    /**
     * 字段级别的错误信息
     */
    private List<FieldError> fieldErrors;

    /**
     * 额外的上下文信息
     */
    private Map<String, Object> context;

    /**
     * 字段错误信息
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class FieldError {
        /**
         * 字段名
         */
        private String field;

        /**
         * 错误消息
         */
        private String message;

        /**
         * 被拒绝的值
         */
        private Object rejectedValue;
    }

    /**
     * 创建简单错误详情
     */
    public static ErrorDetail simple(String errorCode, String message) {
        ErrorDetail errorDetail = new ErrorDetail();
        errorDetail.setErrorCode(errorCode);
        errorDetail.setMessage(message);
        errorDetail.setTimestamp(Instant.now());
        return errorDetail;
    }

    /**
     * 创建带路径的错误详情
     */
    public static ErrorDetail withPath(String errorCode, String message, String path) {
        ErrorDetail errorDetail = simple(errorCode, message);
        errorDetail.setPath(path);
        return errorDetail;
    }

    /**
     * 创建带详细描述的错误详情
     */
    public static ErrorDetail withDetails(String errorCode, String message, String details) {
        ErrorDetail errorDetail = simple(errorCode, message);
        errorDetail.setDetails(details);
        return errorDetail;
    }
}
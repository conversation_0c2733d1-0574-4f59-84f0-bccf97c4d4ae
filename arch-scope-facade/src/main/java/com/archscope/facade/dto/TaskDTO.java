package com.archscope.facade.dto;

import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import io.swagger.v3.oas.annotations.media.Schema;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 任务数据传输对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "任务数据传输对象")
public class TaskDTO {
    
    /**
     * 任务ID
     */
    @Schema(description = "任务唯一标识符")
    private Long id;

    /**
     * 项目ID
     */
    @Schema(description = "关联的项目ID")
    private Long projectId;

    /**
     * 任务名称
     */
    @Schema(description = "任务名称")
    private String name;

    /**
     * 任务描述
     */
    @Schema(description = "任务详细描述")
    private String description;

    /**
     * 任务类型
     */
    @Schema(description = "任务类型，如PROJECT_ANALYSIS、DOCUMENTATION_GENERATION等")
    private String type;

    /**
     * 任务状态
     */
    @Schema(description = "任务状态，如PENDING、PROCESSING、COMPLETED、FAILED等")
    private String status;

    /**
     * 详细状态描述
     */
    @Schema(description = "详细状态描述，提供更具体的状态信息")
    private String detailedStatus;

    /**
     * 任务优先级
     */
    @Schema(description = "任务优先级，数值越高优先级越高")
    private Integer priority;

    /**
     * 进度百分比（0-100）
     */
    @Schema(description = "任务执行进度百分比，范围0-100")
    private Integer progress;
    
    /**
     * 创建时间
     */
    @Schema(description = "任务创建时间")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @Schema(description = "任务最后更新时间")
    private LocalDateTime updatedAt;

    /**
     * 开始处理时间
     */
    @Schema(description = "任务开始处理的时间")
    private LocalDateTime processingStartedAt;

    /**
     * 超时时间
     */
    @Schema(description = "任务超时时间，超过此时间任务将被标记为超时")
    private LocalDateTime timeoutAt;

    /**
     * 执行时间（毫秒）
     */
    @Schema(description = "任务实际执行时间，单位毫秒")
    private Long executionTime;

    /**
     * 重试次数
     */
    @Schema(description = "任务已重试次数")
    private Integer retryCount;

    /**
     * 最大重试次数
     */
    @Schema(description = "任务最大重试次数")
    private Integer maxRetries;

    /**
     * 错误日志
     */
    @Schema(description = "任务执行过程中的错误日志")
    private String errorLog;

    /**
     * 最后一次错误详情
     */
    @Schema(description = "最后一次错误的详细信息")
    private String lastErrorDetail;
    
    /**
     * 任务结果
     */
    @Schema(description = "任务执行结果")
    private String result;

    /**
     * 任务结果JSON字符串（支持多文档类型）
     */
    @Schema(description = "任务结果JSON字符串，支持多文档类型的结果")
    private String results;

    /**
     * 任务参数
     */
    @Schema(description = "任务执行参数，键值对形式存储")
    private Map<String, Object> parameters;

    /**
     * 创建用户ID
     */
    @Schema(description = "创建任务的用户ID")
    private Long userId;

    /**
     * 分配用户ID
     */
    @Schema(description = "任务分配给的用户ID")
    private Long assigneeId;

    /**
     * LLM工作节点ID
     */
    @Schema(description = "处理任务的LLM工作节点ID")
    private String workerId;

    /**
     * 任务整体状态（COMPLETED/FAILED/PARTIAL_SUCCESS）
     */
    @Schema(description = "任务整体状态，如COMPLETED、FAILED、PARTIAL_SUCCESS等")
    private String overallStatus;

    /**
     * 关联的Git提交ID
     */
    @Schema(description = "关联的Git提交ID")
    private String commitId;

    /**
     * 任务版本号
     */
    @Schema(description = "任务版本号")
    private String taskVersion;
}

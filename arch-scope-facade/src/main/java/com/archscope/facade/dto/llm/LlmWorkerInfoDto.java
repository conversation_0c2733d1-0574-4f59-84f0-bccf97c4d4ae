package com.archscope.facade.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * LLM工作节点信息数据传输对象
 * 符合API规范中的LLMWorkerInfoDTO定义
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LlmWorkerInfoDto {

    /**
     * 工作节点ID
     */
    @JsonProperty("workerId")
    private String workerId;

    /**
     * 工作节点版本
     */
    @JsonProperty("workerVersion")
    private String workerVersion;

    /**
     * 已处理任务数量
     */
    @JsonProperty("processedTasksCount")
    private Long processedTasksCount;
}

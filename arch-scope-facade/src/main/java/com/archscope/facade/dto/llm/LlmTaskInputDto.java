package com.archscope.facade.dto.llm;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * LLM任务输入数据传输对象
 * 用于向LLM服务发送任务数据
 * 
 * Schema Version: 1.2
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LlmTaskInputDto {

    /**
     * Schema版本，固定为1.2
     */
    @JsonProperty("schemaVersion")
    @NotBlank(message = "Schema版本不能为空")
    @Pattern(regexp = "^1\\.2$", message = "Schema版本必须为1.2")
    private String schemaVersion = "1.2";

    /**
     * 仓库信息
     */
    @JsonProperty("repositoryInfo")
    @NotNull(message = "仓库信息不能为空")
    @Valid
    private RepositoryInfo repositoryInfo;

    /**
     * 仓库信息内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class RepositoryInfo {

        /**
         * 仓库克隆URL (HTTPS或SSH格式)
         */
        @JsonProperty("cloneUrl")
        @NotBlank(message = "克隆URL不能为空")
        private String cloneUrl;

        /**
         * 特定的提交哈希值 (40位十六进制)
         */
        @JsonProperty("commitId")
        @NotBlank(message = "提交ID不能为空")
        @Pattern(regexp = "^[0-9a-f]{40}$", message = "提交ID必须为40位十六进制字符")
        private String commitId;

        /**
         * 分支名称 (用于上下文)
         */
        @JsonProperty("branchName")
        private String branchName;
    }
}

package com.archscope.facade.dto;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import javax.validation.ConstraintViolation;
import javax.validation.Validation;
import javax.validation.Validator;
import javax.validation.ValidatorFactory;
import java.util.Set;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ProjectRegistrationDTO 验证测试
 */
@DisplayName("项目注册DTO验证测试")
class ProjectRegistrationDTOValidationTest {

    private Validator validator;

    @BeforeEach
    void setUp() {
        ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
        validator = factory.getValidator();
    }

    @Test
    @DisplayName("有效的项目注册数据应该通过验证")
    void validProjectRegistrationData_ShouldPassValidation() {
        // Given
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("测试项目")
                .description("这是一个测试项目")
                .repositoryUrl("https://github.com/user/repo.git")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertTrue(violations.isEmpty(), "有效数据应该通过验证");
    }

    @Test
    @DisplayName("SSH格式的仓库URL应该通过验证")
    void sshRepositoryUrl_ShouldPassValidation() {
        // Given
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("测试项目")
                .repositoryUrl("**************:user/repo.git")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertTrue(violations.isEmpty(), "SSH格式URL应该通过验证");
    }

    @Test
    @DisplayName("HTTP协议的仓库URL应该通过验证")
    void httpRepositoryUrl_ShouldPassValidation() {
        // Given
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("测试项目")
                .repositoryUrl("http://gitlab.yeepay.com/user/repo.git")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertTrue(violations.isEmpty(), "HTTP协议URL应该通过验证");
    }

    @Test
    @DisplayName("空项目名称应该验证失败")
    void emptyProjectName_ShouldFailValidation() {
        // Given
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("")
                .repositoryUrl("https://github.com/user/repo.git")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream()
                .anyMatch(v -> v.getMessage().contains("项目名称不能为空")));
    }

    @Test
    @DisplayName("项目名称过短应该验证失败")
    void shortProjectName_ShouldFailValidation() {
        // Given
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("a")
                .repositoryUrl("https://github.com/user/repo.git")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream()
                .anyMatch(v -> v.getMessage().contains("项目名称长度必须在2-100个字符之间")));
    }

    @Test
    @DisplayName("项目名称过长应该验证失败")
    void longProjectName_ShouldFailValidation() {
        // Given
        String longName = String.join("", Collections.nCopies(101, "a"));
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name(longName)
                .repositoryUrl("https://github.com/user/repo.git")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream()
                .anyMatch(v -> v.getMessage().contains("项目名称长度必须在2-100个字符之间")));
    }

    @Test
    @DisplayName("空仓库URL应该验证失败")
    void emptyRepositoryUrl_ShouldFailValidation() {
        // Given
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("测试项目")
                .repositoryUrl("")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream()
                .anyMatch(v -> v.getMessage().contains("仓库URL不能为空")));
    }

    @Test
    @DisplayName("无效格式的仓库URL应该验证失败")
    void invalidRepositoryUrl_ShouldFailValidation() {
        // Given
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("测试项目")
                .repositoryUrl("invalid-url")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream()
                .anyMatch(v -> v.getMessage().contains("仓库URL格式不正确")));
    }

    @Test
    @DisplayName("项目描述过长应该验证失败")
    void longDescription_ShouldFailValidation() {
        // Given
        String longDescription = String.join("", Collections.nCopies(501, "a"));
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("测试项目")
                .description(longDescription)
                .repositoryUrl("https://github.com/user/repo.git")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertFalse(violations.isEmpty());
        assertTrue(violations.stream()
                .anyMatch(v -> v.getMessage().contains("项目描述长度不能超过500个字符")));
    }

    @Test
    @DisplayName("空项目描述应该通过验证")
    void emptyDescription_ShouldPassValidation() {
        // Given
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("测试项目")
                .description("")
                .repositoryUrl("https://github.com/user/repo.git")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertTrue(violations.isEmpty(), "空描述应该通过验证");
    }

    @Test
    @DisplayName("null项目描述应该通过验证")
    void nullDescription_ShouldPassValidation() {
        // Given
        ProjectRegistrationDTO dto = ProjectRegistrationDTO.builder()
                .name("测试项目")
                .description(null)
                .repositoryUrl("https://github.com/user/repo.git")
                .branch("main")
                .build();

        // When
        Set<ConstraintViolation<ProjectRegistrationDTO>> violations = validator.validate(dto);

        // Then
        assertTrue(violations.isEmpty(), "null描述应该通过验证");
    }
}

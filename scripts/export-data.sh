#!/bin/bash

# ArchScope 数据导出脚本
# 用途：导出ArchScope数据库中的所有数据，以便在其他环境导入使用
# 作者：ArchScope Team
# 日期：$(date +%Y-%m-%d)

set -e

# 配置参数
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_USER=${DB_USER:-root}
DB_PASSWORD=${DB_PASSWORD:-root}
DB_NAME=${DB_NAME:-archscope}
EXPORT_DIR=${EXPORT_DIR:-./data-export}
TIMESTAMP=$(date +%Y%m%d_%H%M%S)

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查依赖
check_dependencies() {
    log_info "检查依赖工具..."
    
    if ! command -v mysql &> /dev/null; then
        log_error "MySQL客户端未安装，请先安装MySQL客户端"
        exit 1
    fi
    
    if ! command -v mysqldump &> /dev/null; then
        log_error "mysqldump工具未安装，请先安装MySQL客户端工具包"
        exit 1
    fi
    
    log_success "依赖检查完成"
}

# 测试数据库连接
test_connection() {
    log_info "测试数据库连接..."
    
    if mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "USE $DB_NAME; SELECT 1;" &> /dev/null; then
        log_success "数据库连接成功"
    else
        log_error "数据库连接失败，请检查连接参数"
        exit 1
    fi
}

# 创建导出目录
create_export_dir() {
    log_info "创建导出目录..."
    
    EXPORT_PATH="$EXPORT_DIR/archscope_export_$TIMESTAMP"
    mkdir -p "$EXPORT_PATH"
    
    log_success "导出目录创建完成: $EXPORT_PATH"
}

# 导出数据库结构
export_schema() {
    log_info "导出数据库结构..."
    
    mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
        --no-data \
        --routines \
        --triggers \
        --single-transaction \
        --lock-tables=false \
        --add-drop-table \
        --add-drop-database \
        --create-options \
        "$DB_NAME" > "$EXPORT_PATH/01_schema.sql"
    
    log_success "数据库结构导出完成"
}

# 导出业务数据
export_data() {
    log_info "导出业务数据..."
    
    # 导出所有表数据（排除flyway_schema_history）
    mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
        --no-create-info \
        --single-transaction \
        --lock-tables=false \
        --complete-insert \
        --extended-insert=false \
        --ignore-table="$DB_NAME.flyway_schema_history" \
        "$DB_NAME" > "$EXPORT_PATH/02_data.sql"
    
    log_success "业务数据导出完成"
}

# 导出单个表数据（用于调试）
export_table_data() {
    local table_name=$1
    log_info "导出表 $table_name 数据..."
    
    mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
        --no-create-info \
        --single-transaction \
        --lock-tables=false \
        --complete-insert \
        --extended-insert=false \
        "$DB_NAME" "$table_name" > "$EXPORT_PATH/table_${table_name}.sql"
    
    log_success "表 $table_name 数据导出完成"
}

# 生成数据统计报告
generate_report() {
    log_info "生成数据统计报告..."
    
    cat > "$EXPORT_PATH/export_report.txt" << EOF
ArchScope 数据导出报告
===================

导出时间: $(date)
数据库: $DB_NAME
主机: $DB_HOST:$DB_PORT

表数据统计:
EOF

    # 获取各表数据量
    mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "
        USE $DB_NAME;
        SELECT 'project' as table_name, COUNT(*) as count FROM project
        UNION ALL SELECT 'tasks', COUNT(*) FROM tasks
        UNION ALL SELECT 'document_version', COUNT(*) FROM document_version
        UNION ALL SELECT 'llm_task_results', COUNT(*) FROM llm_task_results
        UNION ALL SELECT 'llm_worker_nodes', COUNT(*) FROM llm_worker_nodes;
    " >> "$EXPORT_PATH/export_report.txt"
    
    log_success "数据统计报告生成完成"
}

# 创建导入脚本
create_import_script() {
    log_info "创建导入脚本..."
    
    cat > "$EXPORT_PATH/import.sh" << 'EOF'
#!/bin/bash

# ArchScope 数据导入脚本
# 用途：将导出的数据导入到新的数据库环境

set -e

# 配置参数
DB_HOST=${DB_HOST:-localhost}
DB_PORT=${DB_PORT:-3306}
DB_USER=${DB_USER:-root}
DB_PASSWORD=${DB_PASSWORD:-root}
DB_NAME=${DB_NAME:-archscope}

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查文件是否存在
if [ ! -f "01_schema.sql" ] || [ ! -f "02_data.sql" ]; then
    log_error "导入文件不存在，请确保在正确的目录下运行此脚本"
    exit 1
fi

# 测试数据库连接
log_info "测试数据库连接..."
if ! mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "SELECT 1;" &> /dev/null; then
    log_error "数据库连接失败，请检查连接参数"
    exit 1
fi

# 创建数据库（如果不存在）
log_info "创建数据库..."
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入数据库结构
log_info "导入数据库结构..."
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < 01_schema.sql

# 导入业务数据
log_info "导入业务数据..."
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" "$DB_NAME" < 02_data.sql

log_success "数据导入完成！"

# 验证导入结果
log_info "验证导入结果..."
mysql -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" -e "
    USE $DB_NAME;
    SELECT 'project' as table_name, COUNT(*) as count FROM project
    UNION ALL SELECT 'tasks', COUNT(*) FROM tasks
    UNION ALL SELECT 'document_version', COUNT(*) FROM document_version
    UNION ALL SELECT 'llm_task_results', COUNT(*) FROM llm_task_results
    UNION ALL SELECT 'llm_worker_nodes', COUNT(*) FROM llm_worker_nodes;
"
EOF

    chmod +x "$EXPORT_PATH/import.sh"
    log_success "导入脚本创建完成"
}

# 主函数
main() {
    log_info "开始ArchScope数据导出..."
    
    check_dependencies
    test_connection
    create_export_dir
    export_schema
    export_data
    
    # 导出各表数据（用于调试）
    export_table_data "project"
    export_table_data "tasks"
    export_table_data "document_version"
    export_table_data "llm_worker_nodes"
    
    generate_report
    create_import_script
    
    log_success "数据导出完成！"
    log_info "导出路径: $EXPORT_PATH"
    log_info "使用方法: cd $EXPORT_PATH && ./import.sh"
}

# 显示帮助信息
show_help() {
    cat << EOF
ArchScope 数据导出脚本

用法: $0 [选项]

选项:
    -h, --help          显示帮助信息
    --host HOST         数据库主机 (默认: localhost)
    --port PORT         数据库端口 (默认: 3306)
    --user USER         数据库用户 (默认: root)
    --password PASS     数据库密码 (默认: root)
    --database DB       数据库名称 (默认: archscope)
    --export-dir DIR    导出目录 (默认: ./data-export)

环境变量:
    DB_HOST, DB_PORT, DB_USER, DB_PASSWORD, DB_NAME, EXPORT_DIR

示例:
    $0
    $0 --host ************* --user admin --password secret
    DB_HOST=************* DB_USER=admin DB_PASSWORD=secret $0
EOF
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --host)
            DB_HOST="$2"
            shift 2
            ;;
        --port)
            DB_PORT="$2"
            shift 2
            ;;
        --user)
            DB_USER="$2"
            shift 2
            ;;
        --password)
            DB_PASSWORD="$2"
            shift 2
            ;;
        --database)
            DB_NAME="$2"
            shift 2
            ;;
        --export-dir)
            EXPORT_DIR="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 执行主函数
main

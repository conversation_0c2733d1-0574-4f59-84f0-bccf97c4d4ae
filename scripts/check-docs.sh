#!/bin/bash

# ArchScope 文档质量检查脚本
# 用于检查文档的格式、链接有效性等

set -e

echo "🔍 开始ArchScope文档质量检查..."

# 检查是否安装了必要的工具
check_tool() {
    if ! command -v $1 &> /dev/null; then
        echo "❌ 错误: $1 未安装"
        echo "请运行: npm install -g $2"
        exit 1
    fi
}

# 检查工具安装
echo "📋 检查必要工具..."
check_tool "markdownlint" "markdownlint-cli"
check_tool "markdown-link-check" "markdown-link-check"

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 计数器
total_files=0
format_errors=0
link_errors=0

echo ""
echo "🎨 检查Markdown格式..."

# 检查Markdown格式
if markdownlint docs/**/*.md; then
    echo -e "${GREEN}✅ Markdown格式检查通过${NC}"
else
    echo -e "${RED}❌ Markdown格式检查发现问题${NC}"
    format_errors=1
fi

echo ""
echo "🔗 检查链接有效性..."

# 检查链接有效性
link_check_failed=0
for file in $(find docs -name "*.md"); do
    total_files=$((total_files + 1))
    echo "检查文件: $file"

    if ! markdown-link-check "$file" --quiet; then
        echo -e "${RED}❌ 链接检查失败: $file${NC}"
        link_check_failed=1
    fi
done

if [ $link_check_failed -eq 0 ]; then
    echo -e "${GREEN}✅ 所有链接检查通过${NC}"
else
    echo -e "${RED}❌ 发现无效链接${NC}"
    link_errors=1
fi

echo ""
echo "📊 检查结果汇总:"
echo "- 检查文件总数: $total_files"
echo "- 格式错误: $format_errors"
echo "- 链接错误: $link_errors"

# 总结
if [ $format_errors -eq 0 ] && [ $link_errors -eq 0 ]; then
    echo -e "${GREEN}🎉 所有检查通过！文档质量良好。${NC}"
    exit 0
else
    echo -e "${RED}⚠️ 发现问题，请修复后重新检查。${NC}"
    exit 1
fi
#!/bin/bash

# ArchScope 部署包创建脚本
# 用途：创建包含数据、配置和部署说明的完整部署包

set -e

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
PACKAGE_NAME="archscope_deployment_package_$TIMESTAMP"
PACKAGE_DIR="./$PACKAGE_NAME"

echo "=== ArchScope 部署包创建 ==="
echo "包名称: $PACKAGE_NAME"
echo "开始时间: $(date)"
echo

# 创建包目录结构
mkdir -p "$PACKAGE_DIR"/{data,config,scripts,docs}

echo "✅ 创建目录结构完成"

# 1. 复制最新的数据导出
if [ -d "data-export" ]; then
    LATEST_DATA_EXPORT=$(ls -t data-export/ | head -1)
    if [ -n "$LATEST_DATA_EXPORT" ]; then
        cp -r "data-export/$LATEST_DATA_EXPORT"/* "$PACKAGE_DIR/data/"
        echo "✅ 复制数据导出: $LATEST_DATA_EXPORT"
    fi
fi

# 如果没有数据导出，创建一个
if [ ! -f "$PACKAGE_DIR/data/01_schema.sql" ]; then
    echo "📦 未找到数据导出，正在创建..."
    ./scripts/quick-export.sh
    
    # 复制新创建的导出文件
    BACKUP_FILE=$(ls -t archscope_backup_*.sql | head -1)
    IMPORT_FILE=$(ls -t import_*.sh | head -1)
    
    if [ -n "$BACKUP_FILE" ]; then
        cp "$BACKUP_FILE" "$PACKAGE_DIR/data/complete_backup.sql"
        echo "✅ 复制完整备份: $BACKUP_FILE"
    fi
    
    if [ -n "$IMPORT_FILE" ]; then
        cp "$IMPORT_FILE" "$PACKAGE_DIR/data/quick_import.sh"
        chmod +x "$PACKAGE_DIR/data/quick_import.sh"
        echo "✅ 复制快速导入脚本: $IMPORT_FILE"
    fi
fi

# 2. 复制最新的配置导出
if [ -d "config_export_"* ]; then
    LATEST_CONFIG_EXPORT=$(ls -td config_export_* | head -1)
    if [ -n "$LATEST_CONFIG_EXPORT" ]; then
        cp -r "$LATEST_CONFIG_EXPORT"/* "$PACKAGE_DIR/config/"
        echo "✅ 复制配置导出: $LATEST_CONFIG_EXPORT"
    fi
fi

# 如果没有配置导出，创建一个
if [ ! -f "$PACKAGE_DIR/config/application.yml" ]; then
    echo "📦 未找到配置导出，正在创建..."
    ./scripts/export-config.sh
    
    # 复制新创建的配置
    LATEST_CONFIG_EXPORT=$(ls -td config_export_* | head -1)
    if [ -n "$LATEST_CONFIG_EXPORT" ]; then
        cp -r "$LATEST_CONFIG_EXPORT"/* "$PACKAGE_DIR/config/"
        echo "✅ 复制新配置导出: $LATEST_CONFIG_EXPORT"
    fi
fi

# 3. 复制部署脚本
cp scripts/*.sh "$PACKAGE_DIR/scripts/"
echo "✅ 复制部署脚本"

# 4. 复制重要文档
if [ -f "README.md" ]; then
    cp "README.md" "$PACKAGE_DIR/docs/"
fi

if [ -d "docs" ]; then
    cp -r docs/* "$PACKAGE_DIR/docs/" 2>/dev/null || true
fi

echo "✅ 复制文档"

# 5. 创建部署指南
cat > "$PACKAGE_DIR/DEPLOYMENT_GUIDE.md" << 'EOF'
# ArchScope 部署指南

本部署包包含了ArchScope系统的完整部署资源，包括数据库数据、配置文件和部署脚本。

## 📁 目录结构

```
archscope_deployment_package/
├── data/                    # 数据库数据
│   ├── 01_schema.sql       # 数据库结构
│   ├── 02_data.sql         # 业务数据
│   ├── complete_backup.sql # 完整备份
│   ├── import.sh           # 导入脚本
│   └── table_*.sql         # 单表数据
├── config/                  # 配置文件
│   ├── application.yml     # 主配置文件
│   ├── docker-compose.yml  # Docker配置
│   ├── environment-template.yml # 环境模板
│   └── db/migration/       # 数据库迁移文件
├── scripts/                # 部署脚本
│   ├── export-data.sh      # 数据导出脚本
│   ├── export-config.sh    # 配置导出脚本
│   └── quick-export.sh     # 快速导出脚本
├── docs/                   # 文档
└── DEPLOYMENT_GUIDE.md     # 本指南
```

## 🚀 快速部署

### 方式一：使用快速导入脚本

```bash
cd data/
chmod +x quick_import.sh
./quick_import.sh
```

### 方式二：手动导入

```bash
# 1. 创建数据库
mysql -u root -p -e "CREATE DATABASE archscope CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 2. 导入数据
mysql -u root -p archscope < data/complete_backup.sql
```

### 方式三：使用Docker

```bash
# 1. 修改配置
cp config/docker-compose-template.yml docker-compose.yml
# 编辑 docker-compose.yml 中的配置

# 2. 启动服务
docker-compose up -d

# 3. 导入数据
docker exec -i archscope-mysql mysql -u root -proot archscope < data/complete_backup.sql
```

## ⚙️ 环境配置

### 1. 数据库配置

修改 `config/application.yml` 中的数据库连接信息：

```yaml
spring:
  datasource:
    url: *************************************
    username: YOUR_USERNAME
    password: YOUR_PASSWORD
```

### 2. Redis配置

```yaml
spring:
  redis:
    host: YOUR_REDIS_HOST
    port: 6379
    password: YOUR_REDIS_PASSWORD
```

### 3. Git服务器配置

在 `archscope.git.servers.server-list` 中配置Git服务器访问令牌。

### 4. OpenAI配置

```yaml
openai:
  api:
    key: YOUR_OPENAI_API_KEY
    model: gpt-4
```

## 🔧 应用部署

### 后端部署

```bash
# 1. 编译项目
mvn clean package -DskipTests

# 2. 启动应用
java -jar arch-scope-main/target/arch-scope-main-*.jar \
  --spring.profiles.active=prod \
  --spring.config.location=config/application.yml
```

### 前端部署

```bash
# 1. 安装依赖
cd arch-scope-frontend
npm install

# 2. 构建
npm run build

# 3. 部署到Web服务器
# 将 dist 目录内容部署到 Nginx 或其他Web服务器
```

## 📊 数据验证

部署完成后，可以通过以下方式验证数据：

```sql
-- 检查表数据量
SELECT 'project' as table_name, COUNT(*) as count FROM project
UNION ALL SELECT 'tasks', COUNT(*) FROM tasks
UNION ALL SELECT 'document_version', COUNT(*) FROM document_version;

-- 检查项目状态
SELECT name, status, created_at FROM project ORDER BY created_at DESC;
```

## 🔍 故障排查

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否启动
   - 验证连接参数是否正确
   - 确认防火墙设置

2. **Redis连接失败**
   - 检查Redis服务状态
   - 验证密码配置
   - 检查网络连通性

3. **Git仓库访问失败**
   - 验证访问令牌是否有效
   - 检查网络连接
   - 确认仓库权限

### 日志查看

```bash
# 查看应用日志
tail -f logs/archscope.log

# 查看数据库日志
tail -f /var/log/mysql/error.log
```

## 📞 技术支持

如需帮助，请参考：
- 项目文档：docs/ 目录
- 配置模板：config/ 目录
- 部署脚本：scripts/ 目录

---

祝您部署顺利！🎉
EOF

# 6. 创建版本信息文件
cat > "$PACKAGE_DIR/VERSION_INFO.txt" << EOF
ArchScope 部署包版本信息
========================

包创建时间: $(date)
包版本: $TIMESTAMP

数据统计:
EOF

# 添加数据统计信息
if [ -f "$PACKAGE_DIR/data/export_report.txt" ]; then
    cat "$PACKAGE_DIR/data/export_report.txt" >> "$PACKAGE_DIR/VERSION_INFO.txt"
else
    echo "未找到数据统计报告" >> "$PACKAGE_DIR/VERSION_INFO.txt"
fi

echo "✅ 创建版本信息"

# 7. 创建校验文件
echo "创建文件校验信息..."
find "$PACKAGE_DIR" -type f -exec md5sum {} \; > "$PACKAGE_DIR/CHECKSUMS.md5"
echo "✅ 创建校验文件"

# 8. 显示包内容
echo
echo "=== 部署包内容 ==="
du -sh "$PACKAGE_DIR"
echo
echo "目录结构:"
tree "$PACKAGE_DIR" 2>/dev/null || find "$PACKAGE_DIR" -type d | sed 's/[^/]*\//  /g'

echo
echo "=== 文件清单 ==="
find "$PACKAGE_DIR" -type f | sort

echo
echo "✅ 部署包创建完成！"
echo "📦 包位置: $PACKAGE_DIR"
echo "📖 部署指南: $PACKAGE_DIR/DEPLOYMENT_GUIDE.md"
echo
echo "=== 使用方法 ==="
echo "1. 将整个 $PACKAGE_NAME 目录复制到目标服务器"
echo "2. 阅读 DEPLOYMENT_GUIDE.md 了解部署步骤"
echo "3. 根据环境修改配置文件"
echo "4. 执行数据导入和应用部署"
echo
echo "完成时间: $(date)"

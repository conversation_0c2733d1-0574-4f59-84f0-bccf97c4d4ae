#!/usr/bin/env node

/**
 * 字段命名一致性验证脚本
 * 验证前端接口定义与后端API的字段命名一致性
 */

const fs = require('fs');
const path = require('path');

// 定义标准字段映射
const STANDARD_FIELD_MAPPINGS = {
  // 项目相关字段
  'linesOfCode': {
    database: 'lines_of_code',
    java: 'linesOfCode',
    api: 'linesOfCode',
    frontend: 'linesOfCode'
  },
  'fileCount': {
    database: 'file_count',
    java: 'fileCount',
    api: 'fileCount',
    frontend: 'fileCount'
  },
  'contributorCount': {
    database: 'contributor_count',
    java: 'contributorCount',
    api: 'contributorCount',
    frontend: 'contributorCount'
  },
  'repositoryUrl': {
    database: 'repository_url',
    java: 'repositoryUrl',
    api: 'repositoryUrl',
    frontend: 'repositoryUrl'
  },
  'createdAt': {
    database: 'created_at',
    java: 'createdAt',
    api: 'createdAt',
    frontend: 'createdAt'
  },
  'updatedAt': {
    database: 'updated_at',
    java: 'updatedAt',
    api: 'updatedAt',
    frontend: 'updatedAt'
  },
  'lastAnalyzedAt': {
    database: 'last_analyzed_at',
    java: 'lastAnalyzedAt',
    api: 'lastAnalyzedAt',
    frontend: 'lastAnalyzedAt'
  },
  // 服务相关字段
  'serviceId': {
    database: 'service_id',
    java: 'serviceId',
    api: 'id', // API层统一使用id
    frontend: 'id'
  },
  'registeredAt': {
    database: 'registered_at',
    java: 'registeredAt',
    api: 'registeredAt',
    frontend: 'registeredAt'
  },
  'lastUpdatedAt': {
    database: 'last_updated_at',
    java: 'lastUpdatedAt',
    api: 'lastUpdatedAt',
    frontend: 'lastUpdatedAt'
  }
};

// 需要检查的文件路径
const FILES_TO_CHECK = {
  frontend: [
    'arch-scope-frontend/src/stores/project.ts',
    'arch-scope-frontend/src/utils/api.ts'
  ],
  backend: [
    'arch-scope-facade/src/main/java/com/archscope/facade/dto/ProjectDTO.java',
    'arch-scope-app/src/main/java/com/archscope/app/dto/ServiceDTO.java'
  ]
};

// 不一致字段的别名（需要清理的）
const DEPRECATED_ALIASES = [
  'lineCount',      // 应使用 linesOfCode
  'contributors',   // 应使用 contributorCount
  'repoUrl',        // 应使用 repositoryUrl
  'serviceId'       // API层应使用 id
];

/**
 * 检查前端文件中的字段命名
 */
function checkFrontendFieldNaming() {
  console.log('🔍 检查前端字段命名...');
  const issues = [];

  FILES_TO_CHECK.frontend.forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查是否存在不推荐的别名
    DEPRECATED_ALIASES.forEach(alias => {
      const regex = new RegExp(`\\b${alias}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        issues.push({
          file: filePath,
          issue: `发现不推荐的字段别名: ${alias}`,
          count: matches.length,
          type: 'deprecated_alias'
        });
      }
    });

    // 检查标准字段是否存在
    Object.keys(STANDARD_FIELD_MAPPINGS).forEach(standardField => {
      const expectedName = STANDARD_FIELD_MAPPINGS[standardField].frontend;
      const regex = new RegExp(`\\b${expectedName}\\b`, 'g');
      const matches = content.match(regex);
      if (matches) {
        console.log(`✅ ${filePath}: 找到标准字段 ${expectedName} (${matches.length}次)`);
      }
    });
  });

  return issues;
}

/**
 * 检查后端文件中的字段命名
 */
function checkBackendFieldNaming() {
  console.log('\n🔍 检查后端字段命名...');
  const issues = [];

  FILES_TO_CHECK.backend.forEach(filePath => {
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  文件不存在: ${filePath}`);
      return;
    }

    const content = fs.readFileSync(filePath, 'utf8');
    
    // 检查ServiceDTO中是否同时存在id和serviceId
    if (filePath.includes('ServiceDTO.java')) {
      const hasId = /private\s+String\s+id;/.test(content);
      const hasServiceId = /private\s+String\s+serviceId;/.test(content);
      
      if (hasId && hasServiceId) {
        issues.push({
          file: filePath,
          issue: '同时存在id和serviceId字段，应统一使用id',
          type: 'duplicate_field'
        });
      } else if (hasId) {
        console.log(`✅ ${filePath}: 正确使用id字段`);
      }
    }

    // 检查标准字段命名
    Object.keys(STANDARD_FIELD_MAPPINGS).forEach(standardField => {
      const expectedName = STANDARD_FIELD_MAPPINGS[standardField].java;
      const regex = new RegExp(`private\\s+\\w+\\s+${expectedName};`, 'g');
      const matches = content.match(regex);
      if (matches) {
        console.log(`✅ ${filePath}: 找到标准字段 ${expectedName}`);
      }
    });
  });

  return issues;
}

/**
 * 生成修复建议
 */
function generateFixSuggestions(issues) {
  if (issues.length === 0) {
    console.log('\n🎉 所有字段命名检查通过！');
    return;
  }

  console.log('\n🛠️  修复建议:');
  
  const groupedIssues = issues.reduce((acc, issue) => {
    if (!acc[issue.type]) acc[issue.type] = [];
    acc[issue.type].push(issue);
    return acc;
  }, {});

  Object.keys(groupedIssues).forEach(type => {
    console.log(`\n📋 ${type === 'deprecated_alias' ? '清理不推荐的别名' : '修复重复字段'}:`);
    
    groupedIssues[type].forEach(issue => {
      console.log(`   - ${issue.file}`);
      console.log(`     问题: ${issue.issue}`);
      
      if (issue.type === 'deprecated_alias') {
        console.log(`     建议: 将所有 ${issue.issue.split(': ')[1]} 替换为对应的标准字段名`);
      } else {
        console.log(`     建议: 移除重复的字段定义，统一使用标准命名`);
      }
    });
  });
}

/**
 * 主函数
 */
function main() {
  console.log('🚀 开始字段命名一致性验证...\n');
  
  const frontendIssues = checkFrontendFieldNaming();
  const backendIssues = checkBackendFieldNaming();
  
  const allIssues = [...frontendIssues, ...backendIssues];
  
  console.log(`\n📊 检查结果: 发现 ${allIssues.length} 个问题`);
  
  generateFixSuggestions(allIssues);
  
  // 输出字段命名规范摘要
  console.log('\n📖 字段命名规范摘要:');
  console.log('   - 数据库层: snake_case (created_at, lines_of_code)');
  console.log('   - Java代码层: camelCase (createdAt, linesOfCode)');
  console.log('   - API接口层: camelCase (createdAt, linesOfCode)');
  console.log('   - 前端代码层: camelCase (createdAt, linesOfCode)');
  console.log('   - 服务ID字段: API层统一使用 "id"');
  
  process.exit(allIssues.length > 0 ? 1 : 0);
}

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  checkFrontendFieldNaming,
  checkBackendFieldNaming,
  STANDARD_FIELD_MAPPINGS,
  DEPRECATED_ALIASES
};

#!/bin/bash

# ArchScope 快速数据导出脚本
# 简化版本，适用于快速导出和导入

set -e

# 默认配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=root
DB_NAME=archscope
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
EXPORT_FILE="archscope_backup_$TIMESTAMP.sql"

echo "=== ArchScope 快速数据导出 ==="
echo "导出文件: $EXPORT_FILE"
echo "开始时间: $(date)"
echo

# 导出完整数据库（结构+数据）
echo "正在导出数据库..."
mysqldump -h "$DB_HOST" -P "$DB_PORT" -u "$DB_USER" -p"$DB_PASSWORD" \
    --single-transaction \
    --routines \
    --triggers \
    --lock-tables=false \
    --add-drop-table \
    --complete-insert \
    --ignore-table="$DB_NAME.flyway_schema_history" \
    "$DB_NAME" > "$EXPORT_FILE"

echo "✅ 数据导出完成: $EXPORT_FILE"

# 生成导入命令
cat > "import_${TIMESTAMP}.sh" << EOF
#!/bin/bash
# ArchScope 数据导入脚本
# 生成时间: $(date)

# 配置参数（请根据目标环境修改）
TARGET_DB_HOST=localhost
TARGET_DB_PORT=3306
TARGET_DB_USER=root
TARGET_DB_PASSWORD=root
TARGET_DB_NAME=archscope

echo "=== ArchScope 数据导入 ==="
echo "目标数据库: \$TARGET_DB_NAME"
echo "开始时间: \$(date)"
echo

# 创建数据库（如果不存在）
echo "创建数据库..."
mysql -h "\$TARGET_DB_HOST" -P "\$TARGET_DB_PORT" -u "\$TARGET_DB_USER" -p"\$TARGET_DB_PASSWORD" \\
    -e "CREATE DATABASE IF NOT EXISTS \$TARGET_DB_NAME CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入数据
echo "导入数据..."
mysql -h "\$TARGET_DB_HOST" -P "\$TARGET_DB_PORT" -u "\$TARGET_DB_USER" -p"\$TARGET_DB_PASSWORD" \\
    "\$TARGET_DB_NAME" < "$EXPORT_FILE"

echo "✅ 数据导入完成！"

# 验证导入结果
echo
echo "=== 数据验证 ==="
mysql -h "\$TARGET_DB_HOST" -P "\$TARGET_DB_PORT" -u "\$TARGET_DB_USER" -p"\$TARGET_DB_PASSWORD" -e "
    USE \$TARGET_DB_NAME;
    SELECT 'project' as table_name, COUNT(*) as count FROM project
    UNION ALL SELECT 'tasks', COUNT(*) FROM tasks
    UNION ALL SELECT 'document_version', COUNT(*) FROM document_version
    UNION ALL SELECT 'llm_task_results', COUNT(*) FROM llm_task_results
    UNION ALL SELECT 'llm_worker_nodes', COUNT(*) FROM llm_worker_nodes;
"
EOF

chmod +x "import_${TIMESTAMP}.sh"

echo "✅ 导入脚本生成完成: import_${TIMESTAMP}.sh"
echo
echo "=== 使用说明 ==="
echo "1. 将以下文件复制到目标环境："
echo "   - $EXPORT_FILE"
echo "   - import_${TIMESTAMP}.sh"
echo
echo "2. 在目标环境执行："
echo "   chmod +x import_${TIMESTAMP}.sh"
echo "   ./import_${TIMESTAMP}.sh"
echo
echo "3. 或者直接使用 mysql 命令导入："
echo "   mysql -h HOST -u USER -pPASSWORD DATABASE_NAME < $EXPORT_FILE"
echo
echo "完成时间: $(date)"

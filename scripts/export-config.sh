#!/bin/bash

# ArchScope 配置文件导出脚本
# 用途：导出应用配置文件，以便在其他环境使用

set -e

TIMESTAMP=$(date +%Y%m%d_%H%M%S)
CONFIG_EXPORT_DIR="config_export_$TIMESTAMP"

echo "=== ArchScope 配置导出 ==="
echo "导出目录: $CONFIG_EXPORT_DIR"
echo "开始时间: $(date)"
echo

# 创建导出目录
mkdir -p "$CONFIG_EXPORT_DIR"

# 导出主要配置文件
echo "导出应用配置文件..."

# 复制主配置文件
if [ -f "arch-scope-main/src/main/resources/application.yml" ]; then
    cp "arch-scope-main/src/main/resources/application.yml" "$CONFIG_EXPORT_DIR/application.yml"
    echo "✅ application.yml"
fi

# 复制测试配置文件
if [ -f "arch-scope-main/src/test/resources/application-test.yml" ]; then
    cp "arch-scope-main/src/test/resources/application-test.yml" "$CONFIG_EXPORT_DIR/application-test.yml"
    echo "✅ application-test.yml"
fi

# 复制Docker配置
if [ -f "docker-compose.yml" ]; then
    cp "docker-compose.yml" "$CONFIG_EXPORT_DIR/docker-compose.yml"
    echo "✅ docker-compose.yml"
fi

# 复制Maven配置
if [ -f "pom.xml" ]; then
    cp "pom.xml" "$CONFIG_EXPORT_DIR/pom.xml"
    echo "✅ pom.xml"
fi

# 复制数据库迁移文件
if [ -d "arch-scope-infrastructure/src/main/resources/db/migration" ]; then
    mkdir -p "$CONFIG_EXPORT_DIR/db/migration"
    cp -r arch-scope-infrastructure/src/main/resources/db/migration/* "$CONFIG_EXPORT_DIR/db/migration/"
    echo "✅ 数据库迁移文件"
fi

# 复制前端配置
if [ -f "arch-scope-frontend/package.json" ]; then
    mkdir -p "$CONFIG_EXPORT_DIR/frontend"
    cp "arch-scope-frontend/package.json" "$CONFIG_EXPORT_DIR/frontend/"
    echo "✅ frontend/package.json"
fi

if [ -f "arch-scope-frontend/vite.config.ts" ]; then
    cp "arch-scope-frontend/vite.config.ts" "$CONFIG_EXPORT_DIR/frontend/"
    echo "✅ frontend/vite.config.ts"
fi

# 创建环境配置模板
cat > "$CONFIG_EXPORT_DIR/environment-template.yml" << 'EOF'
# ArchScope 环境配置模板
# 请根据目标环境修改以下配置

server:
  port: 8080

spring:
  application:
    name: arch-scope
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************************
    username: root
    password: root
    type: com.alibaba.druid.pool.DruidDataSource
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10s

# Git服务器配置
archscope:
  git:
    servers:
      server-list:
        - host: "github.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true
          # personal-access-token: "your-github-token"
          # token-username: "token"

        - host: "gitlab.com"
          supports-https: true
          supports-http: true
          suggest-https-on-ssh-failure: true
          # personal-access-token: "your-gitlab-token"
          # token-username: "oauth2"

# OpenAI配置
openai:
  api:
    key: ${OPENAI_API_KEY:your-api-key}
    model: gpt-4

# JWT配置
app:
  jwt:
    secret: your-jwt-secret-key
    expirationMs: 3600000
    refreshExpirationMs: 86400000
EOF

# 创建Docker环境配置
cat > "$CONFIG_EXPORT_DIR/docker-compose-template.yml" << 'EOF'
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: archscope-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root
      MYSQL_DATABASE: archscope
      MYSQL_USER: archscope
      MYSQL_PASSWORD: archscope
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    container_name: archscope-redis
    command: redis-server --requirepass redis123
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  archscope:
    image: archscope:latest
    container_name: archscope-app
    depends_on:
      - mysql
      - redis
    ports:
      - "8080:8080"
    environment:
      - SPRING_DATASOURCE_URL=*****************************************************************************************************************************************************
      - SPRING_DATASOURCE_USERNAME=archscope
      - SPRING_DATASOURCE_PASSWORD=archscope
      - SPRING_REDIS_HOST=redis
      - SPRING_REDIS_PASSWORD=redis123
    restart: unless-stopped

volumes:
  mysql_data:
  redis_data:
EOF

# 创建部署说明文档
cat > "$CONFIG_EXPORT_DIR/README.md" << 'EOF'
# ArchScope 配置文件说明

## 文件清单

- `application.yml` - 主应用配置文件
- `application-test.yml` - 测试环境配置
- `docker-compose.yml` - 原始Docker配置
- `docker-compose-template.yml` - Docker部署模板
- `environment-template.yml` - 环境配置模板
- `pom.xml` - Maven项目配置
- `db/migration/` - 数据库迁移文件
- `frontend/` - 前端配置文件

## 部署步骤

### 1. 环境准备

- JDK 8+
- MySQL 8.0+
- Redis 7+
- Node.js 16+ (前端)

### 2. 数据库初始化

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE archscope CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 执行迁移文件（按版本顺序）
mysql -u root -p archscope < db/migration/V1_0__create_project_table.sql
mysql -u root -p archscope < db/migration/V1_1__create_tasks_table.sql
# ... 其他迁移文件
```

### 3. 配置修改

1. 复制 `environment-template.yml` 为 `application-prod.yml`
2. 修改数据库连接信息
3. 配置Redis连接信息
4. 设置Git服务器访问令牌
5. 配置OpenAI API密钥

### 4. 应用部署

#### 方式一：Docker部署

```bash
# 修改 docker-compose-template.yml 中的配置
docker-compose -f docker-compose-template.yml up -d
```

#### 方式二：传统部署

```bash
# 后端
mvn clean package -DskipTests
java -jar arch-scope-main/target/arch-scope-main-*.jar --spring.profiles.active=prod

# 前端
cd arch-scope-frontend
npm install
npm run build
# 部署 dist 目录到 Web 服务器
```

## 注意事项

1. 确保数据库字符集为 utf8mb4
2. Redis 建议设置密码
3. 生产环境请修改默认的JWT密钥
4. Git访问令牌需要有仓库读取权限
5. OpenAI API密钥用于文档生成功能

## 环境变量

可以通过环境变量覆盖配置：

- `DB_HOST` - 数据库主机
- `DB_PORT` - 数据库端口
- `DB_USER` - 数据库用户名
- `DB_PASSWORD` - 数据库密码
- `REDIS_HOST` - Redis主机
- `REDIS_PASSWORD` - Redis密码
- `OPENAI_API_KEY` - OpenAI API密钥
EOF

echo
echo "✅ 配置文件导出完成！"
echo "导出目录: $CONFIG_EXPORT_DIR"
echo
echo "=== 导出内容 ==="
ls -la "$CONFIG_EXPORT_DIR"
echo
echo "请查看 $CONFIG_EXPORT_DIR/README.md 了解部署说明"
echo "完成时间: $(date)"

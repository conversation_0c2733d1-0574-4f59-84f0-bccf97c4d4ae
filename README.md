# ArchScope - 架构鹰眼

![Project Logo](/docs/prototype/images/logo.png)

[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
![Build Status](https://img.shields.io/badge/build-pending-yellow)
![Version](https://img.shields.io/badge/version-0.0.1-blue)
[![ko-fi](https://ko-fi.com/img/githubbutton_sm.svg)](https://ko-fi.com/W7W71IFTGX)

## 🎯 项目简介

ArchScope是一个面向开发者的架构观测和守护系统，旨在通过自动化分析设计文档和代码仓库，为开发者提供项目的全局视角，帮助开发者快速理解和治理项目。

![ArchScope Dashboard](/docs/dashboard.png)

## ✨ 核心特性

- 🔍 **智能代码分析**：基于LLM+提示词的代码仓库解析
- 📝 **自动文档生成**：自动生成项目架构文档
- 🔄 **版本变更追踪**：版本变更感知和对比
- 📊 **健康度评估**：项目健康度评估和星级评定
- 🌐 **文档站点**：文档网站自动生成和托管

## 🛠 技术架构

### 技术栈

- **前端**
  - TypeScript
  - Vue 3.x
  - Tailwind CSS
- **后端**
  - Java
  - Spring Boot
  - DDD架构
- **存储**
  - MySQL 8.0+
  - Redis
- **中间件**
  - RocketMQ

### 系统架构图

![ArchScope DDD六边形架构图](docs/architecture-diagram.md)

> 📋 **架构说明**: ArchScope采用DDD六边形架构，以领域模型为核心，通过端口与适配器模式实现与外部系统的解耦。详细架构说明请参见 [架构文档](docs/architecture.md)。

## 🚀 快速开始

### 环境要求

- Java 8
- Node.js 16+
- MySQL 8.0+
- Redis 6+
- RocketMQ 4+ (可选，系统有容错机制)

### 安装步骤

1. **克隆仓库**
```bash
git clone https://github.com/im47cn/arch-scope.git
cd arch-scope
```

2. **启动基础服务**

使用Docker Compose启动MySQL和Redis：
```bash
# 启动Docker (如果使用colima)
colima start

# 启动数据库服务
docker-compose up -d mysql redis
```

或者手动安装并启动MySQL和Redis服务。

3. **后端启动**

```bash
# 设置Java环境 (如果系统有多个Java版本)
export JAVA_HOME=/Library/Java/JavaVirtualMachines/jdk1.8.0_301.jdk/Contents/Home

# 编译整个项目
mvn clean install -DskipTests

# 启动后端服务
cd arch-scope-main
mvn spring-boot:run
```

后端服务将在 http://localhost:8080 启动

4. **前端启动**

```bash
# 进入前端目录
cd arch-scope-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

前端应用将在 http://localhost:3000 启动

5. **验证安装**

- 后端健康检查：
```bash
curl http://localhost:8080/actuator/health
```

- 项目API测试：
```bash
curl http://localhost:8080/api/projects
```

- 前端访问：打开浏览器访问 http://localhost:3000

### 故障排除

#### 常见问题

1. **磁盘空间不足**
```bash
# 清理Maven缓存
rm -rf ~/.m2/repository

# 清理编译产物
find . -name "target" -type d -exec rm -rf {} + 2>/dev/null || true

# 清理前端依赖
find . -name "node_modules" -type d -exec rm -rf {} + 2>/dev/null || true
```

2. **Java版本问题**
```bash
# 检查Java版本
java -version

# 设置正确的JAVA_HOME (macOS示例)
export JAVA_HOME=/Library/Java/JavaVirtualMachines/jdk1.8.0_301.jdk/Contents/Home
```

3. **数据库连接问题**
```bash
# 检查MySQL是否运行
mysql -h localhost -P 3306 -u root -proot -e "SHOW DATABASES;"

# 检查Docker服务状态
docker-compose ps
```

4. **数据类型错误**

如果遇到 `NumberFormatException: For input string: "demo-v1.0.0"` 错误：
```sql
-- 修复数据库中的错误数据
UPDATE project SET documentation_version = 1 WHERE documentation_version = 'demo-v1.0.0';
```

5. **端口占用**
```bash
# 检查端口占用
lsof -i :8080  # 后端端口
lsof -i :3000  # 前端端口

# 杀死占用进程
kill -9 <PID>
```

## 📂 项目结构

项目采用DDD六边形架构，详细约定参考 [README-DDD.md](/README-DDD.md)

### 整体结构

```
/
├── docs/
│   └── prototype/              # 界面原型
├── arch-scope-app/             # 应用服务层
├── arch-scope-domain/          # 领域模型层
├── arch-scope-facade/          # 接口层
├── arch-scope-infrastructure/  # 基础设施层
├── arch-scope-main/            # 主应用模块
├── arch-scope-frontend/        # 前端应用
└── pom.xml                     # 父级POM
```

### 前端项目结构

```
/arch-scope-frontend/
├── .github/workflows/
│   └── e2e-tests.yml                  # CI/CD工作流
├── public/                            # 静态资源
├── cypress/                           # E2E测试框架
│   ├── e2e/
│   │   ├── task-management.cy.ts      # 任务列表页面测试
│   │   ├── task-detail.cy.ts          # 任务详情页面测试
│   │   └── task-workflow.cy.ts        # 完整工作流程测试
│   ├── support/
│   │   ├── e2e.ts                     # 全局配置
│   │   └── commands.ts                # 自定义命令
│   ├── fixtures/                      # 测试数据
│   │   ├── tasks.json                 # 测试数据
│   │   ├── task-processing.json       # 处理中任务数据
│   │   ├── task-completed.json        # 已完成任务数据
│   │   └── task-failed.json           # 失败任务数据
│   └── README.md                      # 测试文档
├── src/
│   ├── assets/                        # 静态文件 (图片、字体等)
│   ├── components/                    # 可复用组件
│   ├── layouts/                       # 页面布局组件
│   ├── router/                        # Vue Router 配置
│   ├── stores/                        # Pinia 状态管理
│   ├── views/                         # 页面组件
│   │   ├── projects/                  # 项目相关页面
│   │   ├── tasks/                     # 任务相关页面
│   │   └── ...
│   ├── App.vue                        # 根组件
│   ├── main.ts                        # 入口文件
│   └── styles/                        # 全局样式
├── cypress.config.ts                  # Cypress配置
├── run-e2e-tests.sh                   # 测试运行脚本
├── E2E_TESTING_SUMMARY.md             # 实现总结
├── .gitignore
├── package.json
├── tailwind.config.js                 # Tailwind CSS 配置文件
├── tsconfig.json                      # TypeScript 配置文件
└── vite.config.ts                     # Vite 配置文件
```

## 🧪 测试验收

### 交互式运行（推荐）
```bash
npm run test:e2e:dev:open
```

### 无头模式运行
```bash
npm run test:e2e:dev
```

### 使用脚本运行
```bash
./run-e2e-tests.sh
./run-e2e-tests.sh --open
```

## 🤝 贡献指南

欢迎贡献代码和提示词！请遵循以下步骤：

1. Fork 仓库
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建 Pull Request

详细贡献规范请参考 [CONTRIBUTING.md](CONTRIBUTING.md)

## 📃 许可证

本项目采用 MIT 许可证。详情请参考 [LICENSE](LICENSE) 文件。

---
如有问题，请联系: [dreambt](https://github.com/im47cn)

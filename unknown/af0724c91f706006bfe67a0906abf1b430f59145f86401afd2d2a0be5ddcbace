package com.archscope.domain.model.parser;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 代码块模型
 * 用于将大型文件分割为可管理的块
 */
@Data
@Builder
public class CodeChunk {
    /**
     * 块ID
     */
    private String id;
    
    /**
     * 文件名
     */
    private String filename;
    
    /**
     * 块内容
     */
    private String content;
    
    /**
     * 块类型（类、方法、函数等）
     */
    private ChunkType type;
    
    /**
     * 块在文件中的起始行
     */
    private int startLine;
    
    /**
     * 块在文件中的结束行
     */
    private int endLine;
    
    /**
     * 上下文信息（如导入语句、包声明等）
     */
    private String context;
    
    /**
     * 块的依赖关系
     */
    @Builder.Default
    private List<String> dependencies = new ArrayList<>();
}

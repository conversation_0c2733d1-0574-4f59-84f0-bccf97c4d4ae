package com.archscope.domain.model.parser;

/**
 * 解析结果合并器接口
 * 负责将LLM解析结果与传统解析结果进行合并或校验
 */
public interface ParseResultMerger {

    /**
     * 合并LLM解析结果与传统解析结果
     *
     * @param llmResult LLM解析结果
     * @param traditionalResult 传统解析结果
     * @param mergeStrategy 合并策略
     * @return 合并后的解析结果
     */
    FileParseResult merge(FileParseResult llmResult, FileParseResult traditionalResult, MergeStrategy mergeStrategy);

    /**
     * 合并策略枚举
     */
    enum MergeStrategy {
        /**
         * 优先使用LLM结果
         */
        LLM_PRIORITY,
        
        /**
         * 优先使用传统解析结果
         */
        TRADITIONAL_PRIORITY,
        
        /**
         * 智能合并，根据各字段的可靠性选择结果
         */
        SMART_MERGE
    }
}

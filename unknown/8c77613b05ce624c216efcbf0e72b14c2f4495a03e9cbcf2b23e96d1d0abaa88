metadata:
  name: "Python代码分析提示词"
  description: "用于分析Python代码并生成符合FileParseResult格式的结构化输出"
  version: "1.1.0"
  last_updated: "2025-05-04"
  author: "ArchScope团队"

prompts:
  code_structure:
    description: "分析Python代码结构并生成标准化输出"
    model: "claude-3"
    parameters:
      temperature: 0.1
      max_tokens: 4000
    template: |
      分析以下Python代码文件，提取其结构信息并以严格的JSON格式返回，确保完全符合FileParseResult模型规范。

      文件路径: {{file_path}}
      
      ```python
      {{code}}
      ```
      
      请按照以下精确的JSON结构返回分析结果：

      ```json
      {
        "filename": "文件名（不含路径）",
        "filePath": "完整的文件路径",
        "languageType": "PYTHON",
        "packageName": "模块名/包名",
        "imports": [
          "完整的导入语句列表"
        ],
        "classDefinitions": [
          {
            "name": "类名",
            "fullyQualifiedName": "全限定名称",
            "packageName": "模块名/包名",
            "superClass": "父类名（如果有）",
            "interfaces": [],
            "type": "CLASS",
            "accessModifier": "PUBLIC",
            "fields": [
              {
                "name": "属性名",
                "type": "属性类型（如果有类型注解）",
                "accessModifier": "PUBLIC/PRIVATE",
                "isStatic": false,
                "isFinal": false,
                "annotations": [],
                "comment": "属性注释"
              }
            ],
            "methods": [
              {
                "name": "方法名",
                "returnType": "返回类型（如果有类型注解）",
                "accessModifier": "PUBLIC/PRIVATE",
                "parameters": [
                  {
                    "name": "参数名",
                    "type": "参数类型（如果有类型注解）",
                    "annotations": []
                  }
                ],
                "annotations": [],
                "isStatic": false,
                "isFinal": false,
                "isAbstract": false,
                "comment": "方法注释"
              }
            ],
            "innerClasses": [],
            "annotations": [],
            "dependencies": [
              {
                "sourceClass": "当前类名",
                "targetClass": "依赖的类名",
                "type": "INHERITANCE/COMPOSITION/DEPENDENCY",
                "strength": 1,
                "location": "依赖位置描述"
              }
            ],
            "isAbstract": false,
            "isStatic": false,
            "isFinal": false,
            "comment": "类注释"
          }
        ],
        "dependencies": [
          {
            "sourceClass": "源类名/函数名",
            "targetClass": "目标类名/函数名",
            "type": "依赖类型",
            "strength": 1,
            "location": "依赖位置"
          }
        ],
        "fileComment": "文件级注释",
        "errorMessage": null,
        "successful": true
      }
      ```

      重要提示：
      1. 所有字段必须严格遵循上述结构
      2. 确保JSON格式完全正确，不得有任何语法错误
      3. 所有必填字段不能为null（除非显式允许）
      4. 列表类型字段至少返回空数组[]
      5. 布尔值必须是true/false，不能使用字符串
      6. 对于Python特有的概念（如装饰器、模块级函数等），适当映射到最接近的结构
      7. 对于没有显式类型注解的字段，可以使用"unknown"或根据上下文推断

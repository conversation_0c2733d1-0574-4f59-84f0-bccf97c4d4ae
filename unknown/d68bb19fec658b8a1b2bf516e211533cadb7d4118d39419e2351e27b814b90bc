package com.archscope.domain.model.parser;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.InjectMocks;

import com.archscope.domain.external.llm.LlmService;
import com.archscope.domain.model.parser.DependencyRelation;
import com.archscope.domain.model.parser.ParseResultMerger;

import java.util.List;

/**
 * 多语言代码解析器测试类
 * 测试代码解析器对不同编程语言的支持
 */
public class MultiLanguageCodeParserTest {
    
    @Mock
    private LlmService llmService;
    
    private DefaultCodeParser codeParser;
    
    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        
        codeParser = new DefaultCodeParser(llmService);
    }
    
    @Test
    @DisplayName("测试Java代码解析")
    void testJavaCodeParsing() {
        String code = "package com.example;\n\n" +
                "import java.util.List;\n" +
                "import java.util.ArrayList;\n\n" +
                "public class Example {\n" +
                "    private List<String> items = new ArrayList<>();\n\n" +
                "    public void addItem(String item) {\n" +
                "        items.add(item);\n" +
                "    }\n\n" +
                "    public List<String> getItems() {\n" +
                "        return items;\n" +
                "    }\n" +
                "}";
        
        FileParseResult result = codeParser.parseFile("Example.java", code);
        
        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "解析应该成功");
        assertEquals(LanguageType.JAVA, result.getLanguageType(), "应识别为Java文件");
        assertEquals("com.example", result.getPackageName(), "应正确解析包名");
        assertTrue(result.getImports().contains("java.util.List"), "应包含List导入");
        assertTrue(result.getImports().contains("java.util.ArrayList"), "应包含ArrayList导入");
        assertEquals(1, result.getClassDefinitions().size(), "应解析出一个类定义");
        assertEquals("Example", result.getClassDefinitions().get(0).getName(), "应正确解析类名");
    }
    
    @Test
    @DisplayName("测试JavaScript代码解析")
    void testJavaScriptCodeParsing() {
        String code = "import React from 'react';\n" +
                "import { useState } from 'react';\n\n" +
                "class Counter extends React.Component {\n" +
                "  constructor(props) {\n" +
                "    super(props);\n" +
                "    this.state = { count: 0 };\n" +
                "  }\n\n" +
                "  increment() {\n" +
                "    this.setState({ count: this.state.count + 1 });\n" +
                "  }\n\n" +
                "  render() {\n" +
                "    return <div>{this.state.count}</div>;\n" +
                "  }\n" +
                "}\n\n" +
                "export default Counter;";
        
        FileParseResult result = codeParser.parseFile("Counter.js", code);
        
        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "解析应该成功");
        assertEquals(LanguageType.JAVASCRIPT, result.getLanguageType(), "应识别为JavaScript文件");
        assertTrue(result.getImports().size() >= 1, "应至少解析出一个导入");
        assertTrue(result.getClassDefinitions().size() >= 1, "应至少解析出一个类定义");
        
        if (result.getClassDefinitions().size() >= 1) {
            ClassDefinition classDef = result.getClassDefinitions().get(0);
            assertEquals("Counter", classDef.getName(), "应正确解析类名");
            assertEquals("React.Component", classDef.getSuperClass(), "应正确解析父类");
        }
    }
    
    @Test
    @DisplayName("测试TypeScript代码解析")
    void testTypeScriptCodeParsing() {
        String code = "import React, { FC, useState } from 'react';\n\n" +
                "interface CounterProps {\n" +
                "  initialCount: number;\n" +
                "}\n\n" +
                "const Counter: FC<CounterProps> = ({ initialCount }) => {\n" +
                "  const [count, setCount] = useState(initialCount);\n\n" +
                "  const increment = () => {\n" +
                "    setCount(count + 1);\n" +
                "  };\n\n" +
                "  return (\n" +
                "    <div>\n" +
                "      <p>{count}</p>\n" +
                "      <button onClick={increment}>Increment</button>\n" +
                "    </div>\n" +
                "  );\n" +
                "};\n\n" +
                "export default Counter;";
        
        FileParseResult result = codeParser.parseFile("Counter.ts", code);
        
        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "解析应该成功");
        assertEquals(LanguageType.TYPESCRIPT, result.getLanguageType(), "应识别为TypeScript文件");
        assertTrue(result.getImports().size() >= 1, "应至少解析出一个导入");
        
        // 验证接口定义解析（如果实现支持）
        if (result.getClassDefinitions().stream().anyMatch(c -> c.getType() == ClassType.INTERFACE)) {
            ClassDefinition interfaceDef = result.getClassDefinitions().stream()
                    .filter(c -> c.getType() == ClassType.INTERFACE)
                    .findFirst()
                    .orElse(null);
            assertNotNull(interfaceDef, "应解析出接口定义");
            assertEquals("CounterProps", interfaceDef.getName(), "应正确解析接口名");
        }
    }
    
    @Test
    @DisplayName("测试HTML代码解析")
    void testHtmlCodeParsing() {
        String code = "<!DOCTYPE html>\n" +
                "<html lang=\"en\">\n" +
                "<head>\n" +
                "    <meta charset=\"UTF-8\">\n" +
                "    <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n" +
                "    <title>Example</title>\n" +
                "    <link rel=\"stylesheet\" href=\"styles.css\">\n" +
                "</head>\n" +
                "<body>\n" +
                "    <div class=\"container\">\n" +
                "        <h1>Hello, World!</h1>\n" +
                "        <p>This is an example HTML page.</p>\n" +
                "    </div>\n" +
                "    <script src=\"script.js\"></script>\n" +
                "</body>\n" +
                "</html>";
        
        FileParseResult result = codeParser.parseFile("index.html", code);
        
        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "解析应该成功");
        assertEquals(LanguageType.HTML, result.getLanguageType(), "应识别为HTML文件");
    }
    
    @Test
    @DisplayName("测试CSS代码解析")
    void testCssCodeParsing() {
        String code = "body {\n" +
                "    font-family: Arial, sans-serif;\n" +
                "    margin: 0;\n" +
                "    padding: 0;\n" +
                "    background-color: #f0f0f0;\n" +
                "}\n\n" +
                ".container {\n" +
                "    max-width: 1200px;\n" +
                "    margin: 0 auto;\n" +
                "    padding: 20px;\n" +
                "}\n\n" +
                "h1 {\n" +
                "    color: #333;\n" +
                "    font-size: 24px;\n" +
                "}\n\n" +
                "p {\n" +
                "    color: #666;\n" +
                "    line-height: 1.5;\n" +
                "}";
        
        FileParseResult result = codeParser.parseFile("styles.css", code);
        
        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "解析应该成功");
        assertEquals(LanguageType.CSS, result.getLanguageType(), "应识别为CSS文件");
    }
    
    @Test
    @DisplayName("测试Python代码解析")
    void testPythonCodeParsing() {
        String code = "import os\n" +
                "import sys\n" +
                "from typing import List, Dict\n\n" +
                "class Example:\n" +
                "    def __init__(self):\n" +
                "        self.items = []\n\n" +
                "    def add_item(self, item: str) -> None:\n" +
                "        self.items.append(item)\n\n" +
                "    def get_items(self) -> List[str]:\n" +
                "        return self.items\n\n" +
                "if __name__ == '__main__':\n" +
                "    example = Example()\n" +
                "    example.add_item('test')\n" +
                "    print(example.get_items())";
        
        FileParseResult result = codeParser.parseFile("example.py", code);
        
        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "解析应该成功");
        assertEquals(LanguageType.PYTHON, result.getLanguageType(), "应识别为Python文件");
    }
    
    @Test
    @DisplayName("测试C++代码解析")
    void testCppCodeParsing() {
        String code = "#include <iostream>\n" +
                "#include <vector>\n" +
                "#include <string>\n\n" +
                "class Example {\n" +
                "private:\n" +
                "    std::vector<std::string> items;\n\n" +
                "public:\n" +
                "    void addItem(const std::string& item) {\n" +
                "        items.push_back(item);\n" +
                "    }\n\n" +
                "    std::vector<std::string> getItems() const {\n" +
                "        return items;\n" +
                "    }\n" +
                "};\n\n" +
                "int main() {\n" +
                "    Example example;\n" +
                "    example.addItem(\"test\");\n" +
                "    \n" +
                "    auto items = example.getItems();\n" +
                "    for (const auto& item : items) {\n" +
                "        std::cout << item << std::endl;\n" +
                "    }\n" +
                "    \n" +
                "    return 0;\n" +
                "}";
        
        FileParseResult result = codeParser.parseFile("example.cpp", code);
        
        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "解析应该成功");
        assertEquals(LanguageType.CPP, result.getLanguageType(), "应识别为C++文件");
    }
    
    @Test
    @DisplayName("测试混合语言项目的解析")
    void testMixedLanguageProjectParsing() {
        // Java文件
        String javaCode = "package com.example;\n\npublic class Example {\n    public void method() {}\n}";
        
        // JavaScript文件
        String jsCode = "import React from 'react';\n\nclass Component extends React.Component {\n  render() {\n    return <div>Hello</div>;\n  }\n}";
        
        // HTML文件
        String htmlCode = "<!DOCTYPE html>\n<html>\n<head>\n    <title>Example</title>\n</head>\n<body>\n    <h1>Hello</h1>\n</body>\n</html>";
        
        // CSS文件
        String cssCode = "body { margin: 0; padding: 0; }";
        
        // 解析所有文件
        FileParseResult javaResult = codeParser.parseFile("Example.java", javaCode);
        FileParseResult jsResult = codeParser.parseFile("Component.js", jsCode);
        FileParseResult htmlResult = codeParser.parseFile("index.html", htmlCode);
        FileParseResult cssResult = codeParser.parseFile("styles.css", cssCode);
        
        // 验证结果
        assertNotNull(javaResult, "Java解析结果不应为空");
        assertNotNull(jsResult, "JavaScript解析结果不应为空");
        assertNotNull(htmlResult, "HTML解析结果不应为空");
        assertNotNull(cssResult, "CSS解析结果不应为空");
        
        assertTrue(javaResult.isSuccessful(), "Java解析应该成功");
        assertTrue(jsResult.isSuccessful(), "JavaScript解析应该成功");
        assertTrue(htmlResult.isSuccessful(), "HTML解析应该成功");
        assertTrue(cssResult.isSuccessful(), "CSS解析应该成功");
        
        assertEquals(LanguageType.JAVA, javaResult.getLanguageType(), "应识别为Java文件");
        assertEquals(LanguageType.JAVASCRIPT, jsResult.getLanguageType(), "应识别为JavaScript文件");
        assertEquals(LanguageType.HTML, htmlResult.getLanguageType(), "应识别为HTML文件");
        assertEquals(LanguageType.CSS, cssResult.getLanguageType(), "应识别为CSS文件");
    }
    
    @Test
    @DisplayName("测试未知语言的解析")
    void testUnknownLanguageParsing() {
        String code = "This is some unknown code format.";
        
        FileParseResult result = codeParser.parseFile("unknown.xyz", code);
        
        assertNotNull(result, "解析结果不应为空");
        assertTrue(result.isSuccessful(), "解析应该成功，但可能没有提取到有用信息");
        assertEquals(LanguageType.UNKNOWN, result.getLanguageType(), "应识别为未知语言类型");
    }
}
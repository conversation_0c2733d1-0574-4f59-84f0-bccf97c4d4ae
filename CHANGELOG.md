# 更新日志

本文件记录了ArchScope项目的所有重要变更。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布] - 2025-08-03

### 🔧 修复 (Fix)

#### 枚举定义统一修复

- **问题**: 项目中存在枚举重复定义问题，影响代码一致性
- **修复**:
  - 消除DocumentType枚举重复定义，统一使用域对象中的定义
  - 消除ServiceStatus枚举重复定义，统一使用完整版本
  - 更新相关引用和导入语句
- **影响**: 提高枚举定义一致性，消除歧义
- **详细文档**: 参见 `docs/enum-definitions-audit.md`

#### 字段命名规范统一修复

- **问题**: 项目中存在驼峰命名与下划线命名混用问题
- **修复**:
  - 制定统一的字段命名规范文档
  - 修复前端字段命名不一致问题（移除别名）
  - 优化后端API字段命名（统一主键字段）
  - 创建自动化验证工具
- **影响**: 提高数据模型一致性，减少开发混淆
- **详细文档**: 参见 `docs/field-naming-standards.md`

#### 主键类型不一致问题修复 - 第3阶段完成

- **问题描述**: 项目中存在主键类型不一致问题，项目相关表使用BIGINT AUTO_INCREMENT，而服务发现相关表使用VARCHAR(36) UUID
- **解决方案**: 统一使用BIGINT AUTO_INCREMENT主键策略
- **影响范围**: 服务发现模块的所有数据表和相关代码
- **当前状态**: ✅ 数据库迁移脚本已完成，✅ DO类已更新，🔄 Mapper接口更新中

#### 具体修改内容

##### 1. 数据库迁移脚本 ✅ 已完成
- 新增 `V1_12__unify_primary_key_types.sql`
- 重新创建所有服务发现相关表，统一使用BIGINT主键：
  - `services` 表：`service_id` (VARCHAR) → `id` (BIGINT AUTO_INCREMENT)
  - `capabilities` 表：`capability_id` (VARCHAR) → `id` (BIGINT AUTO_INCREMENT)
  - `requirements` 表：`requirement_id` (VARCHAR) → `id` (BIGINT AUTO_INCREMENT)
  - `query_logs` 表：`query_log_id` (VARCHAR) → `id` (BIGINT AUTO_INCREMENT)
  - `requirement_suggestions` 表：`suggestion_id` (VARCHAR) → `id` (BIGINT AUTO_INCREMENT)

##### 2. DO类更新 ✅ 已完成
- `ServiceDO.java`: `String serviceId` → `Long id`
- `CapabilityDO.java`: `String capabilityId` → `Long id`, `String serviceId` → `Long serviceId`
- `RequirementDO.java`: `String requirementId` → `Long id`, `String relatedServiceId` → `Long relatedServiceId`
- `QueryLogDO.java`: `String queryLogId` → `Long id`, `String relatedRequirementId` → `Long relatedRequirementId`
- `RequirementSuggestionDO.java`: `String suggestionId` → `Long id`, `String convertedRequirementId` → `Long convertedRequirementId`
- 所有DO类添加了正确的MyBatis-Plus注解：`@TableId(value = "id", type = IdType.AUTO)`

##### 3. Mapper接口更新 ✅ 已完成
- ServiceMapper: `findByServiceId(String)` → `findById(Long)` ✅ 已完成
- CapabilityMapper: 所有方法的参数类型和SQL语句已更新 ✅ 已完成
- RequirementMapper: 所有方法的参数类型和SQL语句已更新 ✅ 已完成
- QueryLogMapper: 所有方法的参数类型和SQL语句已更新 ✅ 已完成
- RequirementSuggestionMapper: 所有方法的参数类型和SQL语句已更新 ✅ 已完成

#### 技术影响
- **兼容性**: 这是一个破坏性变更，需要重新初始化数据库
- **性能**: BIGINT主键相比UUID在单体应用中性能更好，占用空间更小
- **一致性**: 统一了整个项目的主键策略，提高了代码一致性

#### 下一步计划
1. 完成所有Mapper接口的SQL语句更新
2. 更新相关的服务层和控制器层代码
3. 更新前端TypeScript接口定义
4. 编写和执行完整的测试用例

## [未发布]

### 修复
- 修复了Spring Boot应用启动时的数据库字段类型错误
  - 问题：Project实体的documentationVersion字段期望Integer类型，但数据库中存储了字符串"demo-v1.0.0"
  - 解决：将数据库中的错误数据修正为数字1
  - 影响：解决了`NumberFormatException: For input string: "demo-v1.0.0"`错误

### 改进
- 更新了README.md文档，添加了完整的前端和后端运行指南
  - 添加了详细的环境要求和安装步骤
  - 包含了Docker Compose启动数据库服务的说明
  - 添加了故障排除部分，记录常见问题和解决方案
  - 提供了验证安装的测试命令

### 技术细节
- 成功解决了磁盘空间不足导致的编译失败问题
- 确认了应用程序的容错机制正常工作（RocketMQ不可用时的优雅降级）
- 验证了MySQL、Redis连接正常
- 确认了前后端通信正常

## [0.0.1] - 2025-07-17

### 新增
- 初始项目结构
- DDD六边形架构实现
- 前端Vue3 + TypeScript应用
- 后端Spring Boot应用
- 数据库迁移脚本
- Docker Compose配置

/**
 * 注册项目页面交互演示脚本
 * 在浏览器控制台中运行此脚本来演示交互功能
 */

// 演示函数：模拟用户交互
function demoRegisterProjectInteraction() {
  console.log('🚀 开始演示注册项目页面交互功能...');
  
  // 检查页面是否正确加载
  const repoInput = document.getElementById('repo_url');
  const submitButton = document.querySelector('button[type="submit"]');
  
  if (!repoInput || !submitButton) {
    console.error('❌ 页面元素未找到，请确保在注册项目页面运行此脚本');
    return;
  }
  
  console.log('✅ 页面元素检查通过');
  
  // 步骤1：检查初始状态
  console.log('\n📋 步骤1：检查初始状态');
  console.log('- 仓库地址输入框:', repoInput ? '✅ 存在' : '❌ 不存在');
  console.log('- 提交按钮状态:', submitButton.disabled ? '✅ 已禁用' : '❌ 未禁用');
  
  // 检查详细字段是否隐藏
  const nameInput = document.getElementById('name');
  const descInput = document.getElementById('description');
  const branchSelect = document.getElementById('branch');
  
  const detailFieldsVisible = nameInput && nameInput.offsetParent !== null;
  console.log('- 详细字段显示状态:', detailFieldsVisible ? '❌ 可见（应该隐藏）' : '✅ 隐藏');
  
  // 步骤2：模拟输入仓库地址
  console.log('\n📝 步骤2：模拟输入仓库地址');
  const testUrl = 'https://github.com/vuejs/vue';
  
  // 设置输入值
  repoInput.value = testUrl;
  repoInput.dispatchEvent(new Event('input', { bubbles: true }));
  console.log(`- 输入测试URL: ${testUrl}`);
  
  // 步骤3：模拟失去焦点事件
  console.log('\n🎯 步骤3：模拟失去焦点事件');
  setTimeout(() => {
    repoInput.dispatchEvent(new Event('blur', { bubbles: true }));
    console.log('- 触发blur事件');
    
    // 等待一段时间检查结果
    setTimeout(() => {
      console.log('\n📊 步骤4：检查解析结果');
      
      // 检查详细字段是否显示
      const nameInputAfter = document.getElementById('name');
      const detailFieldsVisibleAfter = nameInputAfter && nameInputAfter.offsetParent !== null;
      console.log('- 详细字段显示状态:', detailFieldsVisibleAfter ? '✅ 可见' : '❌ 仍隐藏');
      
      // 检查自动填充
      if (nameInputAfter) {
        console.log('- 项目名称自动填充:', nameInputAfter.value || '❌ 未填充');
      }
      
      const descInputAfter = document.getElementById('description');
      if (descInputAfter) {
        console.log('- 项目描述自动填充:', descInputAfter.value || '❌ 未填充');
      }
      
      const branchSelectAfter = document.getElementById('branch');
      if (branchSelectAfter) {
        console.log('- 分支选择自动填充:', branchSelectAfter.value || '❌ 未填充');
      }
      
      // 检查提交按钮状态
      console.log('- 提交按钮状态:', submitButton.disabled ? '❌ 仍禁用' : '✅ 已启用');
      
      console.log('\n🎉 基础演示完成！');

      // 步骤5：测试修改URL时的行为
      console.log('\n🔄 步骤5：测试修改仓库地址');
      setTimeout(() => {
        const newTestUrl = 'https://github.com/facebook/react';
        repoInput.value = newTestUrl;
        repoInput.dispatchEvent(new Event('input', { bubbles: true }));
        console.log(`- 修改URL为: ${newTestUrl}`);

        // 立即检查详细字段是否隐藏
        setTimeout(() => {
          const nameInputAfterChange = document.getElementById('name');
          const detailFieldsVisibleAfterChange = nameInputAfterChange && nameInputAfterChange.offsetParent !== null;
          console.log('- 修改URL后详细字段状态:', detailFieldsVisibleAfterChange ? '❌ 仍可见（应该隐藏）' : '✅ 已隐藏');

          console.log('\n🎉 完整演示完成！');

        }, 100); // 立即检查

      }, 2000); // 等待2秒后测试修改URL

      // 提供清理函数
      window.cleanupDemo = () => {
        repoInput.value = '';
        repoInput.dispatchEvent(new Event('input', { bubbles: true }));
        console.log('🧹 演示状态已清理');
      };

      console.log('💡 提示：运行 cleanupDemo() 来清理演示状态');
      
    }, 3000); // 等待3秒检查结果
    
  }, 1000); // 等待1秒后触发blur
}

// 检查当前页面是否为注册页面
function checkCurrentPage() {
  const isRegisterPage = window.location.pathname.includes('/projects/register') || 
                        window.location.hash.includes('/projects/register');
  
  if (!isRegisterPage) {
    console.warn('⚠️  当前不在注册项目页面，请导航到注册页面后再运行演示');
    console.log('🔗 注册页面地址: http://localhost:3001/projects/register');
    return false;
  }
  
  return true;
}

// 主演示函数
function runDemo() {
  console.clear();
  console.log('🎭 ArchScope 注册项目页面交互演示');
  console.log('=====================================');
  
  if (!checkCurrentPage()) {
    return;
  }
  
  // 等待页面完全加载
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', demoRegisterProjectInteraction);
  } else {
    demoRegisterProjectInteraction();
  }
}

// 导出演示函数
if (typeof window !== 'undefined') {
  window.runRegisterDemo = runDemo;
  console.log('💡 在浏览器控制台中运行 runRegisterDemo() 开始演示');
} else {
  // Node.js环境
  module.exports = { runDemo, demoRegisterProjectInteraction };
}

// 自动运行演示（如果在浏览器环境中直接加载）
if (typeof window !== 'undefined' && window.location) {
  console.log('🎯 注册项目交互演示脚本已加载');
  console.log('📖 使用说明:');
  console.log('   1. 确保在注册项目页面 (/projects/register)');
  console.log('   2. 在控制台运行: runRegisterDemo()');
  console.log('   3. 观察交互效果');
  console.log('   4. 运行 cleanupDemo() 清理状态');
}

# ArchScope - 架构鹰眼系统 robots.txt
# 允许所有搜索引擎爬虫访问

User-agent: *
Allow: /

# 允许访问的主要页面
Allow: /projects
Allow: /tasks
Allow: /projects/*/documents
Allow: /projects/*/docs

# 禁止访问的路径
Disallow: /api/
Disallow: /test/
Disallow: /_nuxt/
Disallow: /assets/
Disallow: /node_modules/

# 站点地图位置
Sitemap: https://archscope.example.com/sitemap.xml

# 爬取延迟（毫秒）
Crawl-delay: 1

# 针对特定搜索引擎的配置
User-agent: Googlebot
Allow: /
Crawl-delay: 0

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Baiduspider
Allow: /
Crawl-delay: 2

/**
 * 项目列表功能验证脚本
 * 在浏览器控制台中运行，验证项目列表页面的所有功能
 */

// 验证项目列表功能
function verifyProjectListFunctionality() {
  console.log('🔍 开始验证项目列表功能...');
  console.log('=====================================');

  const results = {
    pageLoad: false,
    dataDisplay: false,
    searchFunction: false,
    sortFunction: false,
    paginationFunction: false,
    errorHandling: false,
    debugTools: false
  };

  // 1. 检查页面是否正确加载
  console.log('1. 检查页面加载状态');
  const isProjectListPage = window.location.pathname.includes('/projects') || 
                           window.location.hash.includes('/projects');
  
  if (isProjectListPage) {
    console.log('✅ 项目列表页面已加载');
    results.pageLoad = true;
  } else {
    console.log('❌ 当前不在项目列表页面');
    console.log('🔗 请导航到: http://localhost:3000/projects');
    return results;
  }

  // 2. 检查项目数据显示
  console.log('\n2. 检查项目数据显示');
  const projectRows = document.querySelectorAll('tbody tr');
  const emptyState = document.querySelector('[class*="text-center"]');
  
  if (projectRows.length > 0) {
    console.log(`✅ 项目数据显示正常，共 ${projectRows.length} 个项目`);
    results.dataDisplay = true;
    
    // 显示前3个项目的信息
    Array.from(projectRows).slice(0, 3).forEach((row, index) => {
      const nameCell = row.querySelector('td:first-child .text-sm.font-medium');
      const descCell = row.querySelector('td:first-child .text-sm.text-gray-500');
      if (nameCell && descCell) {
        console.log(`   ${index + 1}. ${nameCell.textContent.trim()}`);
        console.log(`      ${descCell.textContent.trim()}`);
      }
    });
  } else if (emptyState && emptyState.textContent.includes('暂无项目')) {
    console.log('ℹ️ 显示空状态（暂无项目）');
  } else {
    console.log('❌ 项目数据显示异常');
  }

  // 3. 检查搜索功能
  console.log('\n3. 检查搜索功能');
  const searchInput = document.querySelector('input[placeholder*="搜索"]');
  if (searchInput) {
    console.log('✅ 搜索输入框存在');
    results.searchFunction = true;
    
    // 测试搜索功能
    const originalValue = searchInput.value;
    searchInput.value = 'ArchScope';
    searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    
    setTimeout(() => {
      const filteredRows = document.querySelectorAll('tbody tr');
      console.log(`   搜索 "ArchScope" 后显示 ${filteredRows.length} 个结果`);
      
      // 恢复原始值
      searchInput.value = originalValue;
      searchInput.dispatchEvent(new Event('input', { bubbles: true }));
    }, 100);
  } else {
    console.log('❌ 搜索输入框不存在');
  }

  // 4. 检查排序功能
  console.log('\n4. 检查排序功能');
  const sortSelect = document.querySelector('select');
  if (sortSelect) {
    console.log('✅ 排序选择器存在');
    console.log(`   当前排序选项: ${sortSelect.value}`);
    console.log(`   可用选项: ${Array.from(sortSelect.options).map(o => o.value).join(', ')}`);
    results.sortFunction = true;
  } else {
    console.log('❌ 排序选择器不存在');
  }

  // 5. 检查分页功能
  console.log('\n5. 检查分页功能');
  const paginationButtons = document.querySelectorAll('button[class*="px-3 py-1"]');
  const paginationInfo = document.querySelector('[class*="text-sm text-gray-700"]');
  
  if (paginationButtons.length > 0) {
    console.log(`✅ 分页按钮存在，共 ${paginationButtons.length} 个按钮`);
    results.paginationFunction = true;
  }
  
  if (paginationInfo) {
    console.log(`✅ 分页信息显示: ${paginationInfo.textContent.trim()}`);
  }

  // 6. 检查错误处理
  console.log('\n6. 检查错误处理功能');
  const errorState = document.querySelector('[class*="text-red"]');
  const retryButton = document.querySelector('button[class*="bg-indigo-600"]');
  const mockDataButton = document.querySelector('button[class*="bg-gray-600"]');
  
  if (errorState) {
    console.log('ℹ️ 检测到错误状态显示');
    results.errorHandling = true;
  }
  
  if (retryButton) {
    console.log('✅ 重试按钮存在');
  }
  
  if (mockDataButton) {
    console.log('✅ 使用演示数据按钮存在');
  }

  // 7. 检查调试工具
  console.log('\n7. 检查调试工具');
  if (typeof window.projectsDebug !== 'undefined') {
    console.log('✅ 调试工具已加载');
    console.log('   可用命令: testAPI, setMockData, getMockData, clearMockData');
    results.debugTools = true;
  } else {
    console.log('❌ 调试工具未加载');
  }

  // 8. 生成验证报告
  console.log('\n📊 验证结果汇总');
  console.log('=====================================');
  
  const passedTests = Object.values(results).filter(Boolean).length;
  const totalTests = Object.keys(results).length;
  const successRate = Math.round((passedTests / totalTests) * 100);
  
  console.log(`✅ 通过测试: ${passedTests}/${totalTests} (${successRate}%)`);
  
  Object.entries(results).forEach(([test, passed]) => {
    const status = passed ? '✅' : '❌';
    const testName = test.replace(/([A-Z])/g, ' $1').toLowerCase();
    console.log(`${status} ${testName}`);
  });

  if (successRate >= 80) {
    console.log('\n🎉 项目列表功能验证通过！');
    console.log('💡 所有主要功能都正常工作');
  } else if (successRate >= 60) {
    console.log('\n⚠️ 项目列表功能基本正常，但有一些问题需要修复');
  } else {
    console.log('\n❌ 项目列表功能存在严重问题，需要进一步调试');
  }

  // 9. 提供调试建议
  console.log('\n💡 调试建议:');
  if (!results.dataDisplay) {
    console.log('   - 检查后端服务是否启动: npm run mock-server');
    console.log('   - 检查API代理配置是否正确');
    console.log('   - 查看浏览器网络面板的API请求');
  }
  
  if (!results.debugTools) {
    console.log('   - 刷新页面重新加载调试工具');
    console.log('   - 检查控制台是否有JavaScript错误');
  }

  console.log('\n🔧 快速修复命令:');
  console.log('   projectsDebug.testAPI() - 测试API连接');
  console.log('   projectsDebug.setMockData() - 设置模拟数据');
  console.log('   location.reload() - 刷新页面');

  return results;
}

// 自动运行验证（如果在浏览器环境中）
if (typeof window !== 'undefined') {
  // 导出到全局作用域
  window.verifyProjectList = verifyProjectListFunctionality;
  
  console.log('🎯 项目列表功能验证工具已加载');
  console.log('📖 使用说明:');
  console.log('   1. 确保在项目列表页面 (/projects)');
  console.log('   2. 在控制台运行: verifyProjectList()');
  console.log('   3. 查看验证结果和建议');
  
  // 如果当前在项目列表页面，提示用户运行验证
  const isProjectListPage = window.location.pathname.includes('/projects') || 
                           window.location.hash.includes('/projects');
  if (isProjectListPage) {
    console.log('\n✨ 检测到当前在项目列表页面，可以立即运行验证');
    console.log('🚀 运行命令: verifyProjectList()');
  }
} else {
  // Node.js环境
  module.exports = { verifyProjectListFunctionality };
}

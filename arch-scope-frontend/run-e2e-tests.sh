#!/bin/bash

# 任务管理页面 E2E 测试运行脚本

echo "🚀 开始运行任务管理页面 E2E 测试..."

# 检查前端开发服务器是否运行
echo "📡 检查前端开发服务器状态..."
if curl -s http://localhost:3000 > /dev/null; then
    echo "✅ 前端开发服务器正在运行"
    SERVER_RUNNING=true
else
    echo "❌ 前端开发服务器未运行，正在启动..."
    SERVER_RUNNING=false
fi

# 如果服务器未运行，启动它
if [ "$SERVER_RUNNING" = false ]; then
    echo "🔧 启动前端开发服务器..."
    npm run dev &
    DEV_SERVER_PID=$!
    
    # 等待服务器启动
    echo "⏳ 等待服务器启动..."
    for i in {1..30}; do
        if curl -s http://localhost:3000 > /dev/null; then
            echo "✅ 前端开发服务器启动成功"
            break
        fi
        sleep 1
        echo "等待中... ($i/30)"
    done
    
    if ! curl -s http://localhost:3000 > /dev/null; then
        echo "❌ 前端开发服务器启动失败"
        exit 1
    fi
fi

# 运行 E2E 测试
echo "🧪 运行 E2E 测试..."

# 检查是否有 --open 参数
if [ "$1" = "--open" ]; then
    echo "🖥️  打开 Cypress 测试界面..."
    npx cypress open
else
    echo "🤖 运行无头模式测试..."
    npx cypress run
fi

TEST_EXIT_CODE=$?

# 如果我们启动了开发服务器，现在关闭它
if [ "$SERVER_RUNNING" = false ] && [ ! -z "$DEV_SERVER_PID" ]; then
    echo "🛑 关闭开发服务器..."
    kill $DEV_SERVER_PID
fi

# 输出测试结果
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "✅ 所有 E2E 测试通过！"
else
    echo "❌ 部分 E2E 测试失败"
fi

echo "📊 测试完成"
exit $TEST_EXIT_CODE

/**
 * API测试脚本
 * 用于验证模拟后端服务器的API是否正常工作
 */

const axios = require('axios');

const BASE_URL = 'http://localhost:8081';

async function testAPI() {
  console.log('🧪 开始测试API...');
  console.log('================');

  try {
    // 测试健康检查
    console.log('1. 测试健康检查 /api/health');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ 健康检查成功:', healthResponse.data);

    // 测试项目列表
    console.log('\n2. 测试项目列表 /api/projects');
    const projectsResponse = await axios.get(`${BASE_URL}/api/projects`);
    console.log('✅ 项目列表获取成功:');
    console.log(`   - 项目数量: ${projectsResponse.data.data.length}`);
    console.log(`   - 总数: ${projectsResponse.data.total}`);
    
    // 显示前3个项目
    const projects = projectsResponse.data.data.slice(0, 3);
    projects.forEach((project, index) => {
      console.log(`   ${index + 1}. ${project.name} (ID: ${project.id})`);
    });

    // 测试单个项目详情
    if (projects.length > 0) {
      const firstProject = projects[0];
      console.log(`\n3. 测试项目详情 /api/projects/${firstProject.id}`);
      const projectDetailResponse = await axios.get(`${BASE_URL}/api/projects/${firstProject.id}`);
      console.log('✅ 项目详情获取成功:');
      console.log(`   - 项目名称: ${projectDetailResponse.data.data.name}`);
      console.log(`   - 项目描述: ${projectDetailResponse.data.data.description}`);
      console.log(`   - 仓库地址: ${projectDetailResponse.data.data.repositoryUrl}`);
    }

    // 测试仓库检查
    console.log('\n4. 测试仓库检查 /api/projects/check-repository');
    const checkResponse = await axios.get(`${BASE_URL}/api/projects/check-repository?url=https://github.com/example/arch-scope-frontend`);
    console.log('✅ 仓库检查成功:', checkResponse.data);

    // 测试Git仓库详情
    console.log('\n5. 测试Git仓库详情 /api/git-repository/details');
    const gitResponse = await axios.get(`${BASE_URL}/api/git-repository/details?repositoryUrl=https://github.com/example/test-repo`);
    console.log('✅ Git仓库详情获取成功:');
    console.log(`   - 项目名称: ${gitResponse.data.projectName}`);
    console.log(`   - 默认分支: ${gitResponse.data.defaultBranch}`);
    console.log(`   - 分支列表: ${gitResponse.data.branches.join(', ')}`);

    console.log('\n🎉 所有API测试通过！');
    console.log('================');
    console.log('✅ 模拟后端服务器工作正常');
    console.log('✅ 前端应该能够正常获取项目数据');

  } catch (error) {
    console.error('\n❌ API测试失败:', error.message);
    if (error.response) {
      console.error('   状态码:', error.response.status);
      console.error('   响应数据:', error.response.data);
    } else if (error.request) {
      console.error('   请求失败，可能是服务器未启动');
    }
    console.log('\n💡 请确保模拟服务器正在运行: npm run mock-server');
  }
}

// 运行测试
testAPI();

/**
 * 简单的模拟后端服务器
 * 用于测试项目列表API功能
 */

const express = require('express');
const cors = require('cors');
const app = express();
const PORT = process.env.PORT || 8081;

// 中间件
app.use(cors());
app.use(express.json());

// 模拟项目数据
const mockProjects = [
  {
    id: 1,
    name: "ArchScope 前端项目",
    description: "基于Vue3的架构分析前端应用，提供项目管理和文档生成功能。",
    repositoryUrl: "https://github.com/example/arch-scope-frontend",
    branch: "main",
    createdAt: "2024-01-15T10:30:00Z",
    updatedAt: "2024-01-20T14:45:00Z",
    lastAnalyzedAt: "2024-01-20T14:45:00Z",
    creatorId: 1,
    status: "ACTIVE",
    active: true,
    documentationPath: "/docs/arch-scope-frontend",
    analysisCount: 5,
    documentationVersion: 3
  },
  {
    id: 2,
    name: "微服务网关",
    description: "基于Spring Cloud Gateway的API网关服务，支持路由、限流、认证等功能。",
    repositoryUrl: "https://github.com/example/microservice-gateway",
    branch: "master",
    createdAt: "2024-01-10T09:15:00Z",
    updatedAt: "2024-01-19T16:20:00Z",
    lastAnalyzedAt: "2024-01-19T16:20:00Z",
    creatorId: 1,
    status: "ANALYZING",
    active: true,
    documentationPath: "/docs/microservice-gateway",
    analysisCount: 3,
    documentationVersion: 2
  },
  {
    id: 3,
    name: "用户管理系统",
    description: "企业级用户权限管理系统，支持RBAC权限模型和SSO单点登录。",
    repositoryUrl: "https://github.com/example/user-management",
    branch: "develop",
    createdAt: "2024-01-05T11:00:00Z",
    updatedAt: "2024-01-18T13:30:00Z",
    lastAnalyzedAt: null,
    creatorId: 2,
    status: "COMPLETED",
    active: true,
    documentationPath: "/docs/user-management",
    analysisCount: 4,
    documentationVersion: 1
  },
  {
    id: 4,
    name: "数据分析平台",
    description: "大数据分析平台，基于Spark和Kafka构建的实时数据处理系统。",
    repositoryUrl: "https://github.com/example/data-analytics",
    branch: "main",
    createdAt: "2023-12-20T08:45:00Z",
    updatedAt: "2024-01-17T10:15:00Z",
    lastAnalyzedAt: "2024-01-17T10:15:00Z",
    creatorId: 3,
    status: "FAILED",
    active: false,
    documentationPath: null,
    analysisCount: 2,
    documentationVersion: 0
  },
  {
    id: 5,
    name: "移动端应用",
    description: "React Native开发的跨平台移动应用，支持iOS和Android。",
    repositoryUrl: "https://github.com/example/mobile-app",
    branch: "main",
    createdAt: "2023-12-15T14:20:00Z",
    updatedAt: "2024-01-16T09:40:00Z",
    lastAnalyzedAt: "2024-01-16T09:40:00Z",
    creatorId: 1,
    status: "ACTIVE",
    active: true,
    documentationPath: "/docs/mobile-app",
    analysisCount: 1,
    documentationVersion: 1
  }
];

// API路由

// 获取项目列表
app.get('/api/projects', (req, res) => {
  console.log('📡 收到项目列表请求');
  
  // 模拟延迟
  setTimeout(() => {
    res.json({
      data: mockProjects,
      total: mockProjects.length,
      page: 0,
      size: 10,
      totalPages: 1
    });
    console.log('✅ 返回项目列表，项目数量:', mockProjects.length);
  }, 500); // 500ms延迟模拟网络请求
});

// 获取单个项目详情
app.get('/api/projects/:id', (req, res) => {
  const id = parseInt(req.params.id);
  const project = mockProjects.find(p => p.id === id);
  
  if (project) {
    res.json({ data: project });
    console.log(`✅ 返回项目详情: ${project.name}`);
  } else {
    res.status(404).json({ error: '项目不存在' });
    console.log(`❌ 项目不存在: ID ${id}`);
  }
});

// 创建新项目
app.post('/api/projects', (req, res) => {
  const { name, description, repositoryUrl, branch } = req.body;
  
  const newProject = {
    id: mockProjects.length + 1,
    name,
    description,
    repositoryUrl,
    branch: branch || 'main',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    lastAnalyzedAt: null,
    creatorId: 1,
    status: "ACTIVE",
    active: true,
    documentationPath: null,
    analysisCount: 0,
    documentationVersion: 0
  };
  
  mockProjects.push(newProject);
  
  res.status(201).json({ data: newProject });
  console.log(`✅ 创建新项目: ${newProject.name}`);
});

// 检查仓库是否存在
app.get('/api/projects/check-repository', (req, res) => {
  const { url } = req.query;
  const exists = mockProjects.some(p => p.repositoryUrl === url);
  
  if (exists) {
    const existingProject = mockProjects.find(p => p.repositoryUrl === url);
    res.json({
      exists: true,
      message: '该仓库已经注册过了',
      existingProject: {
        id: existingProject.id,
        name: existingProject.name,
        description: existingProject.description,
        createdAt: existingProject.createdAt,
        status: existingProject.status
      }
    });
  } else {
    res.json({
      exists: false,
      message: '仓库可以注册'
    });
  }
});

// Git仓库验证API
app.get('/api/git-repository/details', (req, res) => {
  const { repositoryUrl } = req.query;
  
  // 模拟仓库信息解析
  const urlParts = repositoryUrl.split('/');
  const repoName = urlParts[urlParts.length - 1].replace('.git', '');
  const owner = urlParts[urlParts.length - 2];
  
  setTimeout(() => {
    res.json({
      success: true,
      projectName: repoName,
      description: `${repoName} 项目的自动生成描述`,
      defaultBranch: 'main',
      branches: ['main', 'develop', 'feature/new-ui'],
      repositoryType: 'git',
      owner: owner,
      repositoryName: repoName,
      isPrivate: false,
      languages: ['JavaScript', 'TypeScript', 'Vue'],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });
  }, 1000);
});

// 文档相关API

// 获取项目的文档类型列表
app.get('/api/documents/projects/:id/types', (req, res) => {
  const projectId = parseInt(req.params.id);
  console.log(`📄 获取项目 ${projectId} 的文档类型列表`);

  // 模拟不同项目返回不同的文档类型（测试排序功能）
  const documentTypes = projectId === 1
    ? ['API', 'PRODUCT_INTRO', 'LLMS_TXT', 'ARCHITECTURE', 'EXTENSION'] // 无序返回
    : projectId === 2
    ? ['ARCHITECTURE', 'API'] // 只有部分类型
    : ['PRODUCT_INTRO', 'UNKNOWN_TYPE', 'API', 'ANOTHER_UNKNOWN']; // 包含未知类型

  setTimeout(() => {
    res.json(documentTypes);
    console.log(`✅ 返回文档类型: ${documentTypes.join(', ')}`);
  }, 300);
});

// 获取项目的版本列表
app.get('/api/documents/projects/:id/versions', (req, res) => {
  const projectId = parseInt(req.params.id);
  console.log(`📄 获取项目 ${projectId} 的版本列表`);

  const versions = [
    { version: 'v1.0.0', timestamp: '2024-01-20T14:45:00Z' },
    { version: 'v0.9.0', timestamp: '2024-01-15T10:30:00Z' }
  ];

  setTimeout(() => {
    res.json(versions);
    console.log(`✅ 返回版本列表: ${versions.length} 个版本`);
  }, 200);
});

// 获取文档的HTML内容
app.get('/api/documents/projects/:id/documents/:docType/html', (req, res) => {
  const { id: projectId, docType } = req.params;
  const { version } = req.query;
  console.log(`📄 获取项目 ${projectId} 的 ${docType} 文档内容 (版本: ${version || '最新'})`);

  const mockContent = `
    <h1>${getDocTypeName(docType)}</h1>
    <p>这是项目 ${projectId} 的 ${getDocTypeName(docType)} 文档内容。</p>
    <h2>主要功能</h2>
    <ul>
      <li>功能特性 1</li>
      <li>功能特性 2</li>
      <li>功能特性 3</li>
    </ul>
    <h2>技术架构</h2>
    <p>基于现代化的技术栈构建...</p>
  `;

  setTimeout(() => {
    res.json(mockContent);
    console.log(`✅ 返回 ${docType} 文档内容`);
  }, 500);
});

// 辅助函数：获取文档类型的中文名称
function getDocTypeName(docType) {
  const typeNames = {
    'PRODUCT_INTRO': '产品简介',
    'ARCHITECTURE': '架构设计',
    'API': '接口文档',
    'USER_MANUAL': '用户手册',
    'EXTENSION': '扩展能力',
    'LLMS_TXT': 'LLM生成内容'
  };
  return typeNames[docType] || docType;
}

// 健康检查
app.get('/api/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器
const server = app.listen(PORT, (err) => {
  if (err) {
    console.error('❌ 服务器启动失败:', err);
    process.exit(1);
  }

  console.log('🚀 模拟后端服务器启动成功!');
  console.log(`📡 服务地址: http://localhost:${PORT}`);
  console.log('📋 可用的API端点:');
  console.log('  GET  /api/projects - 获取项目列表');
  console.log('  GET  /api/projects/:id - 获取项目详情');
  console.log('  POST /api/projects - 创建新项目');
  console.log('  GET  /api/projects/check-repository - 检查仓库');
  console.log('  GET  /api/git-repository/details - 获取仓库详情');
  console.log('  GET  /api/documents/projects/:id/types - 获取文档类型');
  console.log('  GET  /api/documents/projects/:id/versions - 获取文档版本');
  console.log('  GET  /api/documents/projects/:id/documents/:docType/html - 获取文档内容');
  console.log('  GET  /api/health - 健康检查');
  console.log('');
  console.log('💡 提示: 前端开发服务器会自动代理 /api 请求到此服务器');
  console.log('🔄 服务器将持续运行，按 Ctrl+C 停止');
});

// 监听服务器错误
server.on('error', (err) => {
  console.error('❌ 服务器错误:', err);
  if (err.code === 'EADDRINUSE') {
    console.error(`端口 ${PORT} 已被占用，请检查是否有其他服务在使用该端口`);
  }
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n🛑 收到停止信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

process.on('SIGTERM', () => {
  console.log('\n🛑 收到终止信号，正在关闭服务器...');
  server.close(() => {
    console.log('✅ 服务器已关闭');
    process.exit(0);
  });
});

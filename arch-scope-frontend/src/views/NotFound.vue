<template>
  <div class="bg-gray-50 min-h-screen flex items-center justify-center">
    <div class="container mx-auto p-6 max-w-md text-center">
      <div class="flex justify-center mb-6 animate-bounce">
        <div class="bg-indigo-100 rounded-full p-3 flex items-center justify-center">
          <i class="fas fa-question text-indigo-600 text-5xl"></i>
        </div>
      </div>
      
      <h1 class="text-4xl font-bold text-gray-800 mb-4">404</h1>
      <h2 class="text-2xl font-semibold text-gray-700 mb-6">页面未找到</h2>
      
      <p class="text-gray-600 mb-8">
        您访问的页面不存在或已被移除。
      </p>
      
      <router-link 
        to="/" 
        class="px-6 py-3 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-200 inline-flex items-center"
      >
        <i class="fas fa-home mr-2"></i> 返回首页
      </router-link>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'NotFoundView'
})
</script> 
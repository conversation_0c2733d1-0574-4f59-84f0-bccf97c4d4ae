<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";
import LandingNavigation from "@/components/LandingNavigation.vue";
import LandingFooter from "@/components/LandingFooter.vue";
import bgImageUrl from "@/assets/bg_mv2.jpg";

const router = useRouter();
const isVisible = ref(false);
const statsVisible = ref(false);
const featuresVisible = ref(false);

// 背景图片
const bgImage = ref(bgImageUrl);

const navigateToProjects = () => {
  router.push("/projects");
};

// 工作原理交互状态
const activeStep = ref(1);

// 自动轮播逻辑
let autoPlayInterval: NodeJS.Timeout | null = null;

const startAutoPlay = () => {
  if (autoPlayInterval) clearInterval(autoPlayInterval);
  autoPlayInterval = setInterval(() => {
    activeStep.value = activeStep.value >= 4 ? 1 : activeStep.value + 1;
  }, 4000);
};

const setActiveStep = (step: number) => {
  activeStep.value = step;
  // 重启自动轮播
  startAutoPlay();
};

// 模拟统计数据动画
const animatedStats = ref({
  projects: 0,
  tasks: 0,
  documents: 0,
});

const animateStats = () => {
  const targetStats = { projects: 156, tasks: 89, documents: 234 };
  const duration = 2000;
  const steps = 60;
  const stepDuration = duration / steps;

  let currentStep = 0;
  const interval = setInterval(() => {
    currentStep++;
    const progress = currentStep / steps;

    animatedStats.value.projects = Math.floor(targetStats.projects * progress);
    animatedStats.value.tasks = Math.floor(targetStats.tasks * progress);
    animatedStats.value.documents = Math.floor(
      targetStats.documents * progress
    );

    if (currentStep >= steps) {
      clearInterval(interval);
    }
  }, stepDuration);
};

// 当统计数据可见时开始动画
const startStatsAnimation = () => {
  if (statsVisible.value) {
    animateStats();
  }
};

// 监听统计数据可见性变化
const handleStatsIntersection = (entries: IntersectionObserverEntry[]) => {
  entries.forEach((entry) => {
    if (entry.isIntersecting) {
      startStatsAnimation();
    }
  });
};

// FAQ 数据 - 基于深度研究报告丰富内容
const faqItems = ref([
  {
    question: "什么是ArchScope？",
    answer:
      "ArchScope是一个基于大型语言模型(LLM)的智能架构观测和守护系统。它通过AI驱动的自动化分析，能够理解代码的语义内容、识别设计模式、推断架构决策，甚至评估代码质量。与传统静态分析工具不同，ArchScope提供高层次的架构理解，为您的项目提供全局视角，让架构治理变得简单高效。",
    isOpen: false,
  },
  {
    question: "ArchScope的核心技术优势是什么？",
    answer:
      "ArchScope采用多层次LLM集成架构，包括适配器模式支持多种LLM服务（OpenAI、Claude、本地模型等）、结构化提示词工程、增量解析策略和智能代码预处理。系统能够处理大型代码库的令牌限制问题，通过代码分割和上下文管理确保分析准确性，同时支持实时监控和版本变更感知。",
    isOpen: false,
  },
  {
    question: "支持哪些编程语言和项目类型？",
    answer:
      "ArchScope支持12种主流编程语言：Java、Python、JavaScript、TypeScript、Go、C#、PHP、Rust、Kotlin、Swift、Ruby、C++。系统为每种语言定制了专用提示词模板，例如Java分析关注Spring注解和设计模式，Python关注模块结构和装饰器。同时支持混合多语言项目和微服务架构分析。",
    isOpen: false,
  },
  {
    question: "如何处理大型代码库的分析？",
    answer:
      "ArchScope采用创新的增量解析策略：1）识别代码变更文件；2）分析变更对其他文件的影响；3）根据影响程度排序解析任务；4）只更新受影响的文档部分。同时通过代码分割、关键部分提取、并行处理等技术解决LLM令牌限制问题，确保在保持上下文连贯性的同时高效处理超大型项目。",
    isOpen: false,
  },
  {
    question: "数据安全和隐私如何保障？",
    answer:
      "ArchScope实施企业级安全措施：1）支持私有化部署和本地LLM模型；2）代码脱敏和敏感信息过滤；3）数据传输加密和访问控制；4）完整的审计日志和SOC2合规；5）不使用客户代码进行模型训练。对于高度敏感项目，可选择本地部署的CodeLlama或Mistral等模型，确保代码不离开您的环境。",
    isOpen: false,
  },
  {
    question: "与传统代码分析工具有什么区别？",
    answer:
      "传统工具如SonarQube主要提供基于语法和结构的分析，适合持续质量监控和CI/CD集成。而ArchScope基于LLM能够理解代码的高层次概念、识别设计模式、推断架构决策，提供更深入的架构洞察。最佳实践是将两者结合：传统工具负责结构化反馈和持续监控，ArchScope负责架构理解和设计分析。",
    isOpen: false,
  },
  {
    question: "如何生成和维护项目文档？",
    answer:
      "ArchScope采用基于Markdown的智能文档生成系统：1）支持分层生成和模块组合；2）自动生成架构概览、系统分层、技术栈、部署视图等；3）版本管理和差异高亮；4）自定义模板和样式；5）基于C4模型的架构图生成。当代码变更时，系统自动触发文档更新并提供变更通知，确保文档与代码保持同步。",
    isOpen: false,
  },
  {
    question: "支持哪些LLM模型和部署方式？",
    answer:
      "ArchScope支持多种LLM模型：1）云端服务：OpenAI GPT-4/3.5、Claude 3 Opus/Sonnet；2）本地部署：CodeLlama、Mistral、Qwen等；3）根据场景智能选择：复杂分析使用GPT-4，简单任务使用GPT-3.5，敏感项目使用本地模型。系统通过适配器模式实现无缝切换，并提供成本优化和性能调优策略。",
    isOpen: false,
  },
  {
    question: "如何评估项目健康度和质量？",
    answer:
      "ArchScope提供多维度项目健康度评估：1）代码质量分析（复杂度、可维护性、测试覆盖率）；2）架构健康度（设计模式使用、依赖关系、模块化程度）；3）文档完整度（API文档、架构文档、变更记录）；4）技术债务识别和改进建议；5）星级评定算法和趋势分析。这些指标为技术决策提供数据支持。",
    isOpen: false,
  },
  {
    question: "如何开始使用ArchScope？",
    answer:
      "开始使用ArchScope非常简单：1）注册账号并选择合适的价格方案；2）连接您的Git/SVN代码仓库（支持GitHub、GitLab、Bitbucket等）；3）配置分析参数和LLM模型偏好；4）启动首次全量分析（通常5-15分钟）；5）查看生成的架构文档和分析报告。我们提供30天免费试用、详细入门指南和专业技术支持。",
    isOpen: false,
  },
]);

const toggleFaq = (index: number) => {
  faqItems.value[index].isOpen = !faqItems.value[index].isOpen;
};

onMounted(() => {
  // 页面加载动画
  setTimeout(() => {
    isVisible.value = true;
  }, 100);

  // 延迟显示统计数据
  setTimeout(() => {
    statsVisible.value = true;
  }, 800);

  // 延迟显示特性卡片
  setTimeout(() => {
    featuresVisible.value = true;
  }, 1200);

  // 设置交叉观察器来触发统计动画
  const observer = new IntersectionObserver(handleStatsIntersection, {
    threshold: 0.5,
  });

  setTimeout(() => {
    const statsElement = document.querySelector(".stats-section");
    if (statsElement) {
      observer.observe(statsElement);
    }
  }, 1000);

  // 启动工作原理自动轮播
  startAutoPlay();
});

// 组件卸载时清理定时器
onUnmounted(() => {
  if (autoPlayInterval) {
    clearInterval(autoPlayInterval);
  }
});
</script>

<template>
  <div class="min-h-screen">
    <!-- 导航栏 -->
    <LandingNavigation />

    <!-- Hero Section -->
    <section
      id="hero"
      class="relative min-h-screen bg-gradient-to-br from-primary-600 via-primary-700 to-secondary-600 overflow-hidden"
    >
      <!-- 背景图片层 -->
      <div
        class="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-30"
        :style="{ backgroundImage: `url(${bgImage})` }"
      ></div>

      <!-- 渐变叠加层 -->
      <div
        class="absolute inset-0 bg-gradient-to-br from-gray-900/80 via-blue-900/70 to-purple-900/80"
      ></div>

      <!-- 科技感背景装饰 -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <!-- 动态网格 -->
        <div class="absolute inset-0 opacity-20">
          <div
            class="absolute inset-0"
            style="
              background-image: radial-gradient(
                circle at 1px 1px,
                rgba(255, 255, 255, 0.3) 1px,
                transparent 0
              );
              background-size: 50px 50px;
            "
          ></div>
        </div>

        <!-- 浮动代码片段 -->
        <div
          class="absolute top-20 left-10 bg-black/20 backdrop-blur-sm rounded-lg p-4 text-green-400 font-mono text-sm animate-float opacity-60"
        >
          <div>class ArchScope {</div>
          <div>&nbsp;&nbsp;analyze(repo) {</div>
          <div>&nbsp;&nbsp;&nbsp;&nbsp;return ai.process(repo)</div>
          <div>&nbsp;&nbsp;}</div>
          <div>}</div>
        </div>

        <div
          class="absolute top-40 right-20 bg-black/20 backdrop-blur-sm rounded-lg p-4 text-blue-400 font-mono text-sm animate-float opacity-60"
          style="animation-delay: 2s"
        >
          <div>@Component</div>
          <div>export class AIAnalyzer {</div>
          <div>&nbsp;&nbsp;generateDocs(): Promise&lt;Doc&gt;</div>
          <div>}</div>
        </div>

        <div
          class="absolute bottom-32 left-20 bg-black/20 backdrop-blur-sm rounded-lg p-4 text-purple-400 font-mono text-sm animate-float opacity-60"
          style="animation-delay: 4s"
        >
          <div>def analyze_architecture(code):</div>
          <div>&nbsp;&nbsp;patterns = detect_patterns(code)</div>
          <div>&nbsp;&nbsp;return generate_insights(patterns)</div>
        </div>

        <!-- 几何装饰 -->
        <div
          class="absolute top-1/4 right-1/4 w-32 h-32 border border-cyan-400/30 rounded-lg rotate-45 animate-spin"
          style="animation-duration: 20s"
        ></div>
        <div
          class="absolute bottom-1/4 left-1/4 w-24 h-24 border border-purple-400/30 rounded-full animate-pulse"
        ></div>

        <!-- 数据流动效果 -->
        <div
          class="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-cyan-400/50 to-transparent animate-pulse"
        ></div>
        <div
          class="absolute top-1/2 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-400/50 to-transparent animate-pulse"
          style="animation-delay: 1s; transform: translateY(20px)"
        ></div>
      </div>

      <div class="relative flex items-center min-h-screen px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto w-full">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- 左侧：主要内容 -->
            <div class="text-left">
              <!-- 主标题动画 -->
              <div
                class="transform transition-all duration-1000 ease-out"
                :class="
                  isVisible
                    ? 'translate-y-0 opacity-100'
                    : 'translate-y-10 opacity-0'
                "
              >
                <h1
                  class="text-5xl md:text-7xl font-bold text-white mb-6 leading-tight"
                >
                  架构鹰眼
                </h1>
              </div>

              <!-- 副标题动画 -->
              <div
                class="transform transition-all duration-1000 ease-out delay-300"
                :class="
                  isVisible
                    ? 'translate-y-0 opacity-100'
                    : 'translate-y-10 opacity-0'
                "
              >
                <p
                  class="text-xl md:text-2xl text-white/90 mb-8 max-w-4xl leading-relaxed"
                >
                  面向开发者的<span class="text-yellow-300 font-semibold"
                    >智能架构观测</span
                  >和<span class="text-green-300 font-semibold">守护系统</span>
                </p>
                <p class="text-lg text-white/80 mb-12 max-w-3xl">
                  通过AI驱动的自动化分析，为您的项目提供全局视角，让架构治理变得简单高效
                </p>
              </div>

              <!-- CTA按钮动画 -->
              <div
                class="transform transition-all duration-1000 ease-out delay-500"
                :class="
                  isVisible
                    ? 'translate-y-0 opacity-100'
                    : 'translate-y-10 opacity-0'
                "
              >
                <div class="flex flex-col sm:flex-row gap-4 items-start mb-16">
                  <button
                    @click="navigateToProjects"
                    class="group relative px-8 py-4 bg-white text-primary-600 font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 overflow-hidden"
                  >
                    <span class="relative z-10">立即开始</span>
                    <div
                      class="absolute inset-0 bg-gray-50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
                    ></div>
                  </button>
                  <button
                    class="px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-xl hover:border-white hover:bg-white/10 transform hover:scale-105 transition-all duration-300"
                  >
                    了解更多
                  </button>
                </div>
              </div>

              <!-- 信任指标 -->
              <div
                class="transform transition-all duration-1000 ease-out delay-700"
                :class="
                  isVisible
                    ? 'translate-y-0 opacity-100'
                    : 'translate-y-10 opacity-0'
                "
              >
                <div
                  class="flex flex-col md:flex-row items-start space-y-4 md:space-y-0 md:space-x-8 text-white/80"
                >
                  <div class="flex items-center space-x-2">
                    <svg
                      class="w-5 h-5 text-green-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>免费试用</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <svg
                      class="w-5 h-5 text-green-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>5分钟快速上手</span>
                  </div>
                  <div class="flex items-center space-x-2">
                    <svg
                      class="w-5 h-5 text-green-400"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fill-rule="evenodd"
                        d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                        clip-rule="evenodd"
                      ></path>
                    </svg>
                    <span>专业技术支持</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 右侧：科技感数据可视化 -->
            <div class="hidden lg:flex items-center justify-center">
              <div class="relative w-96 h-96">
                <!-- 主要仪表盘 -->
                <div
                  class="relative z-10 w-80 h-80 bg-black/20 backdrop-blur-md rounded-3xl border border-white/20 p-6"
                >
                  <!-- 顶部标题 -->
                  <div class="text-center mb-4">
                    <div class="text-white/90 text-lg font-semibold mb-1">
                      架构分析仪表盘
                    </div>
                    <div class="text-white/60 text-sm">实时监控 • AI驱动</div>
                  </div>

                  <!-- 圆形进度图 -->
                  <div class="relative w-28 h-28 mx-auto mb-4">
                    <svg
                      class="w-28 h-28 transform -rotate-90"
                      viewBox="0 0 120 120"
                    >
                      <!-- 背景圆 -->
                      <circle
                        cx="60"
                        cy="60"
                        r="50"
                        stroke="rgba(255,255,255,0.1)"
                        stroke-width="8"
                        fill="none"
                      />
                      <!-- 进度圆 -->
                      <circle
                        cx="60"
                        cy="60"
                        r="50"
                        stroke="url(#gradient1)"
                        stroke-width="8"
                        fill="none"
                        stroke-dasharray="314"
                        stroke-dashoffset="94"
                        class="animate-pulse"
                      />
                      <defs>
                        <linearGradient
                          id="gradient1"
                          x1="0%"
                          y1="0%"
                          x2="100%"
                          y2="100%"
                        >
                          <stop offset="0%" style="stop-color: #06b6d4" />
                          <stop offset="100%" style="stop-color: #8b5cf6" />
                        </linearGradient>
                      </defs>
                    </svg>
                    <div
                      class="absolute inset-0 flex items-center justify-center"
                    >
                      <div class="text-center">
                        <div class="text-white text-2xl font-bold">92%</div>
                        <div class="text-white/60 text-xs">健康度</div>
                      </div>
                    </div>
                  </div>

                  <!-- 数据指标 -->
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div
                          class="w-2 h-2 bg-green-400 rounded-full animate-pulse"
                        ></div>
                        <span class="text-white/80 text-sm">代码质量</span>
                      </div>
                      <span class="text-green-400 font-semibold">A+</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div
                          class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"
                          style="animation-delay: 0.5s"
                        ></div>
                        <span class="text-white/80 text-sm">依赖分析</span>
                      </div>
                      <span class="text-blue-400 font-semibold">156</span>
                    </div>
                    <div class="flex items-center justify-between">
                      <div class="flex items-center space-x-2">
                        <div
                          class="w-2 h-2 bg-purple-400 rounded-full animate-pulse"
                          style="animation-delay: 1s"
                        ></div>
                        <span class="text-white/80 text-sm">文档覆盖</span>
                      </div>
                      <span class="text-purple-400 font-semibold">89%</span>
                    </div>
                  </div>
                </div>

                <!-- 浮动数据卡片 -->
                <div
                  class="absolute -top-4 -right-4 bg-gradient-to-r from-cyan-500/20 to-blue-500/20 backdrop-blur-sm rounded-xl p-3 border border-cyan-400/30 animate-float z-20"
                >
                  <div class="text-cyan-300 text-xs font-mono">实时分析</div>
                  <div class="text-white text-sm font-semibold">
                    +23 commits
                  </div>
                </div>

                <div
                  class="absolute -bottom-4 -left-4 bg-gradient-to-r from-purple-500/20 to-pink-500/20 backdrop-blur-sm rounded-xl p-3 border border-purple-400/30 animate-float z-20"
                  style="animation-delay: 2s"
                >
                  <div class="text-purple-300 text-xs font-mono">AI 洞察</div>
                  <div class="text-white text-sm font-semibold">
                    架构优化建议
                  </div>
                </div>

                <!-- 连接线动画 -->
                <div
                  class="absolute top-1/2 left-1/2 w-px h-20 bg-gradient-to-b from-cyan-400/50 to-transparent transform -translate-x-1/2 -translate-y-1/2 animate-pulse"
                ></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- 核心特性部分 -->
    <section
      id="features"
      class="relative py-20 bg-gradient-to-br from-gray-50 to-gray-100"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            强大的核心功能
          </h2>
          <p class="text-lg text-gray-600 max-w-3xl mx-auto">
            基于大型语言模型(LLM)的深度代码理解与架构提取，采用多层次集成架构、
            结构化提示词工程和增量解析策略，为您的项目提供企业级的智能架构分析解决方案
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <!-- 特性卡片 1 -->
          <div
            class="transform transition-all duration-700 ease-out"
            :class="
              featuresVisible
                ? 'translate-y-0 opacity-100'
                : 'translate-y-10 opacity-0'
            "
            style="transition-delay: 0ms"
          >
            <div
              class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-primary-200 relative overflow-hidden h-full flex flex-col"
              style="min-height: 380px;"
            >
              <div
                class="absolute inset-0 bg-gradient-to-br from-primary-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              ></div>
              <div class="relative z-10 flex flex-col h-full">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-primary-500 to-primary-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
                >
                  <svg
                    class="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">
                  多语言LLM代码分析
                </h3>
                <p class="text-gray-600 leading-relaxed mb-6 flex-grow">
                  支持12种主流编程语言的深度语义分析，采用结构化提示词模板和智能工程技术，
                  能够理解代码的高层次概念、识别设计模式、推断架构决策，并提供项目健康度评估
                </p>
                <div class="flex items-center justify-between mt-auto">
                  <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                    12种编程语言
                  </div>
                  <div class="flex gap-1">
                    <span class="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">智能提示词</span>
                    <span class="px-2 py-1 bg-primary-100 text-primary-700 text-xs rounded-full">健康度评估</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 特性卡片 2 -->
          <div
            class="transform transition-all duration-700 ease-out"
            :class="
              featuresVisible
                ? 'translate-y-0 opacity-100'
                : 'translate-y-10 opacity-0'
            "
            style="transition-delay: 200ms"
          >
            <div
              class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-secondary-200 relative overflow-hidden h-full flex flex-col"
              style="min-height: 380px;"
            >
              <div
                class="absolute inset-0 bg-gradient-to-br from-secondary-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              ></div>
              <div class="relative z-10 flex flex-col h-full">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
                >
                  <svg
                    class="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">
                  智能文档生成与同步
                </h3>
                <p class="text-gray-600 leading-relaxed mb-6 flex-grow">
                  基于Markdown模板系统，支持分层生成、模块组合、版本管理和差异高亮。
                  与SonarQube、ESLint等传统工具深度集成，实时生成全面的架构文档和质量报告
                </p>
                <div class="flex items-center justify-between mt-auto">
                  <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    版本变更自动触发
                  </div>
                  <div class="flex gap-1">
                    <span class="px-2 py-1 bg-secondary-100 text-secondary-700 text-xs rounded-full">工具集成</span>
                    <span class="px-2 py-1 bg-secondary-100 text-secondary-700 text-xs rounded-full">实时同步</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 特性卡片 5 - 企业级安全 -->
          <div
            class="transform transition-all duration-700 ease-out"
            :class="
              featuresVisible
                ? 'translate-y-0 opacity-100'
                : 'translate-y-10 opacity-0'
            "
            style="transition-delay: 800ms"
          >
            <div
              class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-red-200 relative overflow-hidden h-full flex flex-col"
              style="min-height: 380px;"
            >
              <div
                class="absolute inset-0 bg-gradient-to-br from-red-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              ></div>
              <div class="relative z-10 flex flex-col h-full">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-red-500 to-red-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
                >
                  <svg
                    class="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">
                  企业级安全与隐私
                </h3>
                <p class="text-gray-600 leading-relaxed mb-6 flex-grow">
                  支持私有化部署和本地LLM模型，提供代码脱敏、数据加密、SOC2合规等
                  企业级安全保障，确保代码不离开您的环境
                </p>
                <div class="flex items-center justify-between mt-auto">
                  <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"/>
                    </svg>
                    不使用客户代码训练
                  </div>
                  <div class="flex gap-1">
                    <span class="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">私有部署</span>
                    <span class="px-2 py-1 bg-red-100 text-red-700 text-xs rounded-full">SOC2</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- 特性卡片 3 -->
          <div
            class="transform transition-all duration-700 ease-out"
            :class="
              featuresVisible
                ? 'translate-y-0 opacity-100'
                : 'translate-y-10 opacity-0'
            "
            style="transition-delay: 400ms"
          >
            <div
              class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-accent-200 relative overflow-hidden h-full flex flex-col"
              style="min-height: 380px;"
            >
              <div
                class="absolute inset-0 bg-gradient-to-br from-accent-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              ></div>
              <div class="relative z-10 flex flex-col h-full">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-accent-500 to-accent-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
                >
                  <svg
                    class="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M13 10V3L4 14h7v7l9-11h-7z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">
                  增量解析与版本感知
                </h3>
                <p class="text-gray-600 leading-relaxed mb-6 flex-grow">
                  创新的增量解析策略：识别代码变更文件，分析变更影响，根据影响程度排序解析任务，
                  只更新受影响的文档部分，大大提高处理大型代码库的效率
                </p>
                <div class="flex items-center justify-between mt-auto">
                  <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                    避免重新分析整个代码库
                  </div>
                  <div class="flex gap-1">
                    <span class="px-2 py-1 bg-accent-100 text-accent-700 text-xs rounded-full">增量解析</span>
                    <span class="px-2 py-1 bg-accent-100 text-accent-700 text-xs rounded-full">版本感知</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 特性卡片 4 - 架构图自动生成 -->
          <div
            class="transform transition-all duration-700 ease-out"
            :class="
              featuresVisible
                ? 'translate-y-0 opacity-100'
                : 'translate-y-10 opacity-0'
            "
            style="transition-delay: 600ms"
          >
            <div
              class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-indigo-200 relative overflow-hidden h-full flex flex-col"
              style="min-height: 380px;"
            >
              <div
                class="absolute inset-0 bg-gradient-to-br from-indigo-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              ></div>
              <div class="relative z-10 flex flex-col h-full">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
                >
                  <svg
                    class="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v2M7 7h10"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">
                  C4架构图自动生成
                </h3>
                <p class="text-gray-600 leading-relaxed mb-6 flex-grow">
                  基于C4模型和PlantUML，自动生成系统上下文图、容器图、组件图和代码图，
                  将自然语言描述转换为标准架构可视化图表
                </p>
                <div class="flex items-center justify-between mt-auto">
                  <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                    </svg>
                    C4模型四层架构视图
                  </div>
                  <div class="flex gap-1">
                    <span class="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full">C4模型</span>
                    <span class="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded-full">PlantUML</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 特性卡片 6 - 传统工具集成 -->
          <div
            class="transform transition-all duration-700 ease-out"
            :class="
              featuresVisible
                ? 'translate-y-0 opacity-100'
                : 'translate-y-10 opacity-0'
            "
            style="transition-delay: 1000ms"
          >
            <div
              class="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 border border-gray-100 hover:border-green-200 relative overflow-hidden h-full flex flex-col"
              style="min-height: 380px;"
            >
              <div
                class="absolute inset-0 bg-gradient-to-br from-green-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              ></div>
              <div class="relative z-10 flex flex-col h-full">
                <div
                  class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300"
                >
                  <svg
                    class="w-8 h-8 text-white"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      stroke-linecap="round"
                      stroke-linejoin="round"
                      stroke-width="2"
                      d="M8 9l3 3-3 3m5 0h3M5 20h14a2 2 0 002-2V6a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                    />
                  </svg>
                </div>
                <h3 class="text-xl font-bold text-gray-900 mb-4">
                  大型代码库智能处理
                </h3>
                <p class="text-gray-600 leading-relaxed mb-6 flex-grow">
                  采用代码分割、关键部分提取、并行处理等先进技术，突破LLM令牌限制。
                  与SonarQube、ESLint等传统工具深度集成，高效处理超大型项目
                </p>
                <div class="flex items-center justify-between mt-auto">
                  <div class="flex items-center text-sm text-gray-500">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                      <path d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4m0 5c0 2.21-3.582 4-8 4s-8-1.79-8-4"/>
                    </svg>
                    解决LLM令牌限制
                  </div>
                  <div class="flex gap-1">
                    <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">代码分割</span>
                    <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded-full">工具集成</span>
                  </div>
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>
    </section>

    <!-- 统计数据部分 -->
    <section
      class="stats-section relative py-20 bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0 overflow-hidden pointer-events-none">
        <div class="absolute top-0 left-0 w-full h-full opacity-10">
          <div
            class="absolute inset-0"
            style="
              background-image: radial-gradient(
                circle at 2px 2px,
                rgba(255, 255, 255, 0.3) 1px,
                transparent 0
              );
              background-size: 40px 40px;
            "
          ></div>
        </div>
      </div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div
          class="transform transition-all duration-1000 ease-out"
          :class="
            statsVisible
              ? 'translate-y-0 opacity-100'
              : 'translate-y-10 opacity-0'
          "
        >
          <div class="text-center mb-20">
            <div class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm text-white rounded-full text-sm font-medium mb-6">
              <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
              </svg>
              实时数据分析
            </div>
            <h2 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-white via-cyan-100 to-blue-100 bg-clip-text text-transparent mb-6">
              数据驱动的架构洞察
            </h2>
            <p class="text-xl text-white/90 max-w-3xl mx-auto leading-relaxed">
              通过<span class="text-cyan-300 font-semibold">AI智能分析</span>实时监控项目健康状态，
              提供<span class="text-blue-300 font-semibold">精准的架构分析报告</span>和可视化洞察
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- 项目统计 -->
            <div class="relative group">
              <div
                class="bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/20 hover:border-cyan-400/60 transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 shadow-2xl hover:shadow-cyan-500/25"
              >
                <!-- 背景光效 -->
                <div
                  class="absolute inset-0 bg-gradient-to-br from-cyan-500/20 to-blue-500/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                ></div>

                <!-- 装饰性元素 -->
                <div class="absolute top-4 right-4 w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <div class="absolute top-6 right-6 w-1 h-1 bg-cyan-300 rounded-full animate-ping"></div>

                <div class="relative z-10">
                  <!-- 图标和标题 -->
                  <div class="flex items-center justify-between mb-6">
                    <div
                      class="w-16 h-16 bg-gradient-to-br from-cyan-400 via-cyan-500 to-blue-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg shadow-cyan-500/30"
                    >
                      <svg
                        class="w-8 h-8 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                        ></path>
                      </svg>
                    </div>
                    <div class="text-right">
                      <div class="text-xs text-cyan-300 font-medium">实时状态</div>
                      <div class="flex items-center text-green-400 text-xs">
                        <div class="w-1.5 h-1.5 bg-green-400 rounded-full mr-1 animate-pulse"></div>
                        在线监控
                      </div>
                    </div>
                  </div>

                  <!-- 主要数据 -->
                  <div class="text-center mb-6">
                    <div class="text-5xl font-bold bg-gradient-to-r from-white to-cyan-200 bg-clip-text text-transparent mb-2">
                      {{ animatedStats.projects }}+
                    </div>
                    <div class="text-xl text-cyan-300 font-semibold mb-1">活跃项目</div>
                    <div class="text-sm text-white/70">持续监控中</div>
                  </div>

                  <!-- 详细指标 -->
                  <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="text-center p-3 bg-white/5 rounded-xl">
                      <div class="text-lg font-bold text-white">98.5%</div>
                      <div class="text-xs text-cyan-300">健康度</div>
                    </div>
                    <div class="text-center p-3 bg-white/5 rounded-xl">
                      <div class="text-lg font-bold text-white">24/7</div>
                      <div class="text-xs text-cyan-300">监控</div>
                    </div>
                  </div>

                  <!-- 进度条 -->
                  <div class="space-y-3">
                    <div class="flex justify-between text-xs text-white/70">
                      <span>项目活跃度</span>
                      <span>85%</span>
                    </div>
                    <div class="w-full bg-white/10 rounded-full h-2.5">
                      <div
                        class="bg-gradient-to-r from-cyan-400 to-blue-500 h-2.5 rounded-full relative overflow-hidden"
                        style="width: 85%"
                      >
                        <div class="absolute inset-0 bg-white/30 animate-pulse"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 任务统计 -->
            <div class="relative group">
              <div
                class="bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/20 hover:border-purple-400/60 transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 shadow-2xl hover:shadow-purple-500/25"
              >
                <!-- 背景光效 -->
                <div
                  class="absolute inset-0 bg-gradient-to-br from-purple-500/20 to-pink-500/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                ></div>

                <!-- 装饰性元素 -->
                <div class="absolute top-4 right-4 w-2 h-2 bg-orange-400 rounded-full animate-pulse" style="animation-delay: 0.5s"></div>
                <div class="absolute top-6 right-6 w-1 h-1 bg-purple-300 rounded-full animate-ping" style="animation-delay: 1s"></div>

                <div class="relative z-10">
                  <!-- 图标和标题 -->
                  <div class="flex items-center justify-between mb-6">
                    <div
                      class="w-16 h-16 bg-gradient-to-br from-purple-400 via-purple-500 to-pink-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:-rotate-3 transition-all duration-500 shadow-lg shadow-purple-500/30"
                    >
                      <svg
                        class="w-8 h-8 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"
                        ></path>
                      </svg>
                    </div>
                    <div class="text-right">
                      <div class="text-xs text-purple-300 font-medium">处理状态</div>
                      <div class="flex items-center text-orange-400 text-xs">
                        <div class="w-1.5 h-1.5 bg-orange-400 rounded-full mr-1 animate-pulse"></div>
                        执行中
                      </div>
                    </div>
                  </div>

                  <!-- 主要数据 -->
                  <div class="text-center mb-6">
                    <div class="text-5xl font-bold bg-gradient-to-r from-white to-purple-200 bg-clip-text text-transparent mb-2">
                      {{ animatedStats.tasks }}+
                    </div>
                    <div class="text-xl text-purple-300 font-semibold mb-1">分析任务</div>
                    <div class="text-sm text-white/70">今日完成</div>
                  </div>

                  <!-- 详细指标 -->
                  <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="text-center p-3 bg-white/5 rounded-xl">
                      <div class="text-lg font-bold text-white">72%</div>
                      <div class="text-xs text-purple-300">完成率</div>
                    </div>
                    <div class="text-center p-3 bg-white/5 rounded-xl">
                      <div class="text-lg font-bold text-white">3.2s</div>
                      <div class="text-xs text-purple-300">平均耗时</div>
                    </div>
                  </div>

                  <!-- 进度条 -->
                  <div class="space-y-3">
                    <div class="flex justify-between text-xs text-white/70">
                      <span>任务处理进度</span>
                      <span>72%</span>
                    </div>
                    <div class="w-full bg-white/10 rounded-full h-2.5">
                      <div
                        class="bg-gradient-to-r from-purple-400 to-pink-500 h-2.5 rounded-full relative overflow-hidden"
                        style="width: 72%"
                      >
                        <div class="absolute inset-0 bg-white/30 animate-pulse" style="animation-delay: 0.5s"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 文档统计 -->
            <div class="relative group">
              <div
                class="bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/20 hover:border-green-400/60 transition-all duration-500 transform hover:scale-105 hover:-translate-y-2 shadow-2xl hover:shadow-green-500/25"
              >
                <!-- 背景光效 -->
                <div
                  class="absolute inset-0 bg-gradient-to-br from-green-500/20 to-emerald-500/20 rounded-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"
                ></div>

                <!-- 装饰性元素 -->
                <div class="absolute top-4 right-4 w-2 h-2 bg-blue-400 rounded-full animate-pulse" style="animation-delay: 1s"></div>
                <div class="absolute top-6 right-6 w-1 h-1 bg-green-300 rounded-full animate-ping" style="animation-delay: 1.5s"></div>

                <div class="relative z-10">
                  <!-- 图标和标题 -->
                  <div class="flex items-center justify-between mb-6">
                    <div
                      class="w-16 h-16 bg-gradient-to-br from-green-400 via-green-500 to-emerald-600 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-500 shadow-lg shadow-green-500/30"
                    >
                      <svg
                        class="w-8 h-8 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        ></path>
                      </svg>
                    </div>
                    <div class="text-right">
                      <div class="text-xs text-green-300 font-medium">生成状态</div>
                      <div class="flex items-center text-blue-400 text-xs">
                        <div class="w-1.5 h-1.5 bg-blue-400 rounded-full mr-1 animate-pulse"></div>
                        自动化
                      </div>
                    </div>
                  </div>

                  <!-- 主要数据 -->
                  <div class="text-center mb-6">
                    <div class="text-5xl font-bold bg-gradient-to-r from-white to-green-200 bg-clip-text text-transparent mb-2">
                      {{ animatedStats.documents }}+
                    </div>
                    <div class="text-xl text-green-300 font-semibold mb-1">生成文档</div>
                    <div class="text-sm text-white/70">自动生成</div>
                  </div>

                  <!-- 详细指标 -->
                  <div class="grid grid-cols-2 gap-4 mb-6">
                    <div class="text-center p-3 bg-white/5 rounded-xl">
                      <div class="text-lg font-bold text-white">94%</div>
                      <div class="text-xs text-green-300">准确率</div>
                    </div>
                    <div class="text-center p-3 bg-white/5 rounded-xl">
                      <div class="text-lg font-bold text-white">1.8s</div>
                      <div class="text-xs text-green-300">生成速度</div>
                    </div>
                  </div>

                  <!-- 进度条 -->
                  <div class="space-y-3">
                    <div class="flex justify-between text-xs text-white/70">
                      <span>文档生成效率</span>
                      <span>94%</span>
                    </div>
                    <div class="w-full bg-white/10 rounded-full h-2.5">
                      <div
                        class="bg-gradient-to-r from-green-400 to-emerald-500 h-2.5 rounded-full relative overflow-hidden"
                        style="width: 94%"
                      >
                        <div class="absolute inset-0 bg-white/30 animate-pulse" style="animation-delay: 1s"></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 架构洞察仪表盘 -->
          <div class="mt-16 bg-white/5 backdrop-blur-xl rounded-3xl p-8 border border-white/20">
            <div class="text-center mb-8">
              <h3 class="text-2xl font-bold text-white mb-4">实时架构洞察仪表盘</h3>
              <p class="text-white/70">AI驱动的智能分析，为您的架构决策提供数据支持</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <!-- 代码质量指标 -->
              <div class="bg-white/5 rounded-2xl p-6 border border-white/10">
                <div class="flex items-center justify-between mb-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                    </svg>
                  </div>
                  <div class="text-right">
                    <div class="text-2xl font-bold text-white">A+</div>
                    <div class="text-xs text-blue-300">代码质量</div>
                  </div>
                </div>
                <div class="w-full bg-white/10 rounded-full h-2">
                  <div class="bg-gradient-to-r from-blue-400 to-blue-600 h-2 rounded-full" style="width: 92%"></div>
                </div>
                <div class="text-xs text-white/60 mt-2">质量评分: 92/100</div>
              </div>

              <!-- 架构复杂度 -->
              <div class="bg-white/5 rounded-2xl p-6 border border-white/10">
                <div class="flex items-center justify-between mb-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                    </svg>
                  </div>
                  <div class="text-right">
                    <div class="text-2xl font-bold text-white">中等</div>
                    <div class="text-xs text-yellow-300">复杂度</div>
                  </div>
                </div>
                <div class="w-full bg-white/10 rounded-full h-2">
                  <div class="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full" style="width: 65%"></div>
                </div>
                <div class="text-xs text-white/60 mt-2">循环复杂度: 6.5</div>
              </div>

              <!-- 依赖关系 -->
              <div class="bg-white/5 rounded-2xl p-6 border border-white/10">
                <div class="flex items-center justify-between mb-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-purple-400 to-purple-600 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                  </div>
                  <div class="text-right">
                    <div class="text-2xl font-bold text-white">156</div>
                    <div class="text-xs text-purple-300">依赖项</div>
                  </div>
                </div>
                <div class="w-full bg-white/10 rounded-full h-2">
                  <div class="bg-gradient-to-r from-purple-400 to-purple-600 h-2 rounded-full" style="width: 78%"></div>
                </div>
                <div class="text-xs text-white/60 mt-2">健康依赖: 78%</div>
              </div>

              <!-- 测试覆盖率 -->
              <div class="bg-white/5 rounded-2xl p-6 border border-white/10">
                <div class="flex items-center justify-between mb-4">
                  <div class="w-12 h-12 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                  </div>
                  <div class="text-right">
                    <div class="text-2xl font-bold text-white">85%</div>
                    <div class="text-xs text-green-300">测试覆盖</div>
                  </div>
                </div>
                <div class="w-full bg-white/10 rounded-full h-2">
                  <div class="bg-gradient-to-r from-green-400 to-green-600 h-2 rounded-full" style="width: 85%"></div>
                </div>
                <div class="text-xs text-white/60 mt-2">单元测试: 1,247</div>
              </div>
            </div>

            <!-- 架构趋势图 -->
            <div class="mt-8 bg-white/5 rounded-2xl p-6 border border-white/10">
              <div class="flex items-center justify-between mb-6">
                <h4 class="text-lg font-semibold text-white">架构健康度趋势</h4>
                <div class="flex items-center space-x-2 text-sm text-white/70">
                  <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span>过去40周</span>
                </div>
              </div>

              <!-- 40周趋势图 -->
              <div class="relative h-40 bg-white/5 rounded-xl p-4 overflow-hidden">
                <!-- Y轴标签 -->
                <div class="absolute left-2 top-4 text-xs text-white/60">100%</div>
                <div class="absolute left-2 top-1/2 text-xs text-white/60">50%</div>
                <div class="absolute left-2 bottom-8 text-xs text-white/60">0%</div>

                <!-- 40周数据柱状图 -->
                <div class="absolute inset-0 flex items-end justify-between px-8 pb-8 pt-6">
                  <!-- 第1-8周（早期阶段，健康度较低） -->
                  <div class="w-1 bg-gradient-to-t from-red-400 to-red-300 rounded-t" style="height: 45%"></div>
                  <div class="w-1 bg-gradient-to-t from-red-400 to-red-300 rounded-t" style="height: 48%"></div>
                  <div class="w-1 bg-gradient-to-t from-orange-400 to-orange-300 rounded-t" style="height: 52%"></div>
                  <div class="w-1 bg-gradient-to-t from-orange-400 to-orange-300 rounded-t" style="height: 55%"></div>
                  <div class="w-1 bg-gradient-to-t from-orange-400 to-orange-300 rounded-t" style="height: 58%"></div>
                  <div class="w-1 bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t" style="height: 62%"></div>
                  <div class="w-1 bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t" style="height: 65%"></div>
                  <div class="w-1 bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t" style="height: 68%"></div>

                  <!-- 第9-16周（改进阶段） -->
                  <div class="w-1 bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t" style="height: 72%"></div>
                  <div class="w-1 bg-gradient-to-t from-yellow-400 to-yellow-300 rounded-t" style="height: 75%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 78%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 80%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 82%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 85%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 83%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 87%"></div>

                  <!-- 第17-24周（稳定阶段） -->
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 89%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 86%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 88%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 91%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 90%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 92%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 89%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 93%"></div>

                  <!-- 第25-32周（优化阶段） -->
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 94%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 91%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 95%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 93%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 96%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 94%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 97%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 95%"></div>

                  <!-- 第33-40周（最近8周，包括本周） -->
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 96%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 97%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 95%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 98%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 96%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 97%"></div>
                  <div class="w-1 bg-gradient-to-t from-green-400 to-green-300 rounded-t" style="height: 95%"></div>
                  <!-- 本周数据 - 特殊高亮 -->
                  <div class="w-1 bg-gradient-to-t from-cyan-400 to-cyan-300 rounded-t animate-pulse relative" style="height: 98%">
                    <div class="absolute -top-6 -left-4 text-xs text-cyan-300 font-semibold whitespace-nowrap">本周: 98%</div>
                  </div>
                </div>

                <!-- X轴时间标签 -->
                <div class="absolute bottom-2 left-8 right-8 flex justify-between text-xs text-white/50">
                  <span>40周前</span>
                  <span>20周前</span>
                  <span>本周</span>
                </div>

                <!-- 趋势线 -->
                <div class="absolute inset-0 pointer-events-none">
                  <svg class="w-full h-full" viewBox="0 0 100 100" preserveAspectRatio="none">
                    <path
                      d="M 5,60 Q 15,55 25,50 Q 35,45 45,35 Q 55,25 65,20 Q 75,15 85,10 Q 90,8 95,5"
                      stroke="rgba(34, 197, 94, 0.6)"
                      stroke-width="0.5"
                      fill="none"
                      class="animate-pulse"
                    />
                  </svg>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>



    <!-- 工作原理部分 - 技术深度版 -->
    <section
      id="how-it-works"
      class="relative py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 overflow-hidden"
    >
      <!-- 技术背景装饰 -->
      <div class="absolute inset-0 opacity-30">
        <div
          class="absolute top-10 left-10 w-32 h-32 border border-blue-200 rounded-lg transform rotate-12 animate-float"
        ></div>
        <div
          class="absolute top-20 right-20 w-24 h-24 border border-purple-200 rounded-lg transform -rotate-12 animate-float"
          style="animation-delay: 1s"
        ></div>
        <div
          class="absolute bottom-20 left-20 w-28 h-28 border border-indigo-200 rounded-lg transform rotate-45 animate-float"
          style="animation-delay: 2s"
        ></div>
        <div
          class="absolute bottom-10 right-10 w-20 h-20 border border-cyan-200 rounded-lg transform -rotate-45 animate-float"
          style="animation-delay: 3s"
        ></div>
      </div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-20">
          <div
            class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full text-blue-800 text-sm font-medium mb-6 border border-blue-200"
          >
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            基于DDD六边形架构 + LLM深度解析
          </div>
          <h2
            class="text-4xl md:text-5xl font-bold text-gray-900 mb-6 leading-tight"
          >
            <span
              class="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent"
            >
              企业级架构智能分析引擎
            </span>
          </h2>
          <p class="text-xl text-gray-700 max-w-4xl mx-auto leading-relaxed">
            采用领域驱动设计(DDD)、事件溯源(Event
            Sourcing)和CQRS模式，结合大语言模型的
            <span class="font-semibold text-indigo-600"
              >抽象语法树(AST)解析</span
            >、 <span class="font-semibold text-blue-600">依赖图分析</span>和
            <span class="font-semibold text-purple-600">架构模式识别</span>技术
          </p>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <!-- 左侧：技术深度工作流程 -->
          <div class="space-y-10">

            <!-- 步骤 1: 多源代码仓库集成 -->
            <div
              class="relative group cursor-pointer"
              @click="setActiveStep(1)"
              :class="{ 'scale-105': activeStep === 1 }"
            >
              <div class="flex items-start space-x-6">
                <div class="relative">
                  <div
                    class="flex-shrink-0 w-16 h-16 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg group-hover:scale-110 transition-all duration-300"
                    :class="
                      activeStep === 1
                        ? 'bg-gradient-to-br from-blue-500 to-blue-600 ring-4 ring-blue-200'
                        : 'bg-gradient-to-br from-gray-400 to-gray-500'
                    "
                  >
                    1
                  </div>
                  <div
                    v-if="activeStep === 1"
                    class="absolute -top-1 -right-1 w-6 h-6 bg-green-400 rounded-full animate-pulse"
                  ></div>
                </div>
                <div class="flex-1">
                  <h3
                    class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-blue-600 transition-colors"
                  >
                    多源代码仓库集成与元数据提取
                  </h3>
                  <p class="text-gray-700 mb-4 leading-relaxed">
                    基于<span class="font-semibold text-blue-600"
                      >Git Hooks</span
                    >和<span class="font-semibold text-indigo-600"
                      >Webhook机制</span
                    >， 实现对GitHub、GitLab、Bitbucket等平台的<span
                      class="font-semibold text-purple-600"
                      >实时同步</span
                    >。 采用<span class="font-semibold text-cyan-600"
                      >OAuth 2.0</span
                    >安全认证，确保代码安全性。
                  </p>
                  <div class="flex flex-wrap gap-2">
                    <span
                      class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full font-medium"
                      >Git Protocol</span
                    >
                    <span
                      class="px-3 py-1 bg-indigo-100 text-indigo-700 text-sm rounded-full font-medium"
                      >Webhook API</span
                    >
                    <span
                      class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full font-medium"
                      >OAuth 2.0</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤 2: AI深度解析引擎 -->
            <div
              class="relative group cursor-pointer"
              @click="setActiveStep(2)"
              :class="{ 'scale-105': activeStep === 2 }"
            >
              <div class="flex items-start space-x-6">
                <div class="relative">
                  <div
                    class="flex-shrink-0 w-16 h-16 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg group-hover:scale-110 transition-all duration-300"
                    :class="
                      activeStep === 2
                        ? 'bg-gradient-to-br from-purple-500 to-purple-600 ring-4 ring-purple-200'
                        : 'bg-gradient-to-br from-gray-400 to-gray-500'
                    "
                  >
                    2
                  </div>
                  <div
                    v-if="activeStep === 2"
                    class="absolute -top-1 -right-1 w-6 h-6 bg-yellow-400 rounded-full animate-pulse"
                  ></div>
                </div>
                <div class="flex-1">
                  <h3
                    class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-purple-600 transition-colors"
                  >
                    LLM驱动的AST解析与架构模式识别
                  </h3>
                  <p class="text-gray-700 mb-4 leading-relaxed">
                    运用<span class="font-semibold text-purple-600"
                      >Transformer架构</span
                    >的大语言模型，结合
                    <span class="font-semibold text-blue-600"
                      >抽象语法树(AST)</span
                    >解析技术，进行
                    <span class="font-semibold text-indigo-600"
                      >语义级代码理解</span
                    >。 通过<span class="font-semibold text-green-600"
                      >图神经网络(GNN)</span
                    >分析依赖关系图。
                  </p>
                  <div class="flex flex-wrap gap-2">
                    <span
                      class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full font-medium"
                      >AST Parser</span
                    >
                    <span
                      class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full font-medium"
                      >Transformer</span
                    >
                    <span
                      class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full font-medium"
                      >Graph Neural Network</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤 3: 智能文档生成 -->
            <div
              class="relative group cursor-pointer"
              @click="setActiveStep(3)"
              :class="{ 'scale-105': activeStep === 3 }"
            >
              <div class="flex items-start space-x-6">
                <div class="relative">
                  <div
                    class="flex-shrink-0 w-16 h-16 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg group-hover:scale-110 transition-all duration-300"
                    :class="
                      activeStep === 3
                        ? 'bg-gradient-to-br from-indigo-500 to-indigo-600 ring-4 ring-indigo-200'
                        : 'bg-gradient-to-br from-gray-400 to-gray-500'
                    "
                  >
                    3
                  </div>
                  <div
                    v-if="activeStep === 3"
                    class="absolute -top-1 -right-1 w-6 h-6 bg-blue-400 rounded-full animate-pulse"
                  ></div>
                </div>
                <div class="flex-1">
                  <h3
                    class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-indigo-600 transition-colors"
                  >
                    基于模板引擎的多格式文档生成
                  </h3>
                  <p class="text-gray-700 mb-4 leading-relaxed">
                    采用<span class="font-semibold text-indigo-600"
                      >事件溯源(Event Sourcing)</span
                    >模式， 结合<span class="font-semibold text-blue-600"
                      >CQRS架构</span
                    >， 实现<span class="font-semibold text-purple-600"
                      >PlantUML</span
                    >、
                    <span class="font-semibold text-green-600">Mermaid</span
                    >等多种格式的架构图自动生成。
                  </p>
                  <div class="flex flex-wrap gap-2">
                    <span
                      class="px-3 py-1 bg-indigo-100 text-indigo-700 text-sm rounded-full font-medium"
                      >Event Sourcing</span
                    >
                    <span
                      class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full font-medium"
                      >CQRS</span
                    >
                    <span
                      class="px-3 py-1 bg-purple-100 text-purple-700 text-sm rounded-full font-medium"
                      >PlantUML</span
                    >
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤 4: 实时监控与优化 -->
            <div
              class="relative group cursor-pointer"
              @click="setActiveStep(4)"
              :class="{ 'scale-105': activeStep === 4 }"
            >
              <div class="flex items-start space-x-6">
                <div class="relative">
                  <div
                    class="flex-shrink-0 w-16 h-16 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg group-hover:scale-110 transition-all duration-300"
                    :class="
                      activeStep === 4
                        ? 'bg-gradient-to-br from-green-500 to-green-600 ring-4 ring-green-200'
                        : 'bg-gradient-to-br from-gray-400 to-gray-500'
                    "
                  >
                    4
                  </div>
                  <div
                    v-if="activeStep === 4"
                    class="absolute -top-1 -right-1 w-6 h-6 bg-red-400 rounded-full animate-pulse"
                  ></div>
                </div>
                <div class="flex-1">
                  <h3
                    class="text-2xl font-bold text-gray-900 mb-3 group-hover:text-green-600 transition-colors"
                  >
                    分布式架构健康度监控与智能优化
                  </h3>
                  <p class="text-gray-700 mb-4 leading-relaxed">
                    基于<span class="font-semibold text-green-600"
                      >微服务架构</span
                    >和
                    <span class="font-semibold text-blue-600"
                      >消息队列(RocketMQ)</span
                    >， 实现<span class="font-semibold text-purple-600"
                      >实时架构健康度评估</span
                    >。 运用<span class="font-semibold text-orange-600"
                      >机器学习算法</span
                    >提供智能优化建议。
                  </p>
                  <div class="flex flex-wrap gap-2">
                    <span
                      class="px-3 py-1 bg-green-100 text-green-700 text-sm rounded-full font-medium"
                      >Microservices</span
                    >
                    <span
                      class="px-3 py-1 bg-blue-100 text-blue-700 text-sm rounded-full font-medium"
                      >RocketMQ</span
                    >
                    <span
                      class="px-3 py-1 bg-orange-100 text-orange-700 text-sm rounded-full font-medium"
                      >ML Optimization</span
                    >
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 右侧：动态技术可视化 -->
          <div class="relative">
            <!-- 主容器 -->
            <div
              class="bg-gradient-to-br from-slate-900 to-slate-800 rounded-3xl p-8 shadow-2xl border border-slate-700 transition-all duration-500"
            >
              <!-- 步骤1：代码仓库集成 -->
              <div
                v-show="activeStep === 1"
                class="space-y-6 animate-fadeInUp"
              >
                <div class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-600">
                  <div class="flex items-center justify-between mb-6">
                    <h4 class="font-bold text-white text-lg">多源代码仓库集成</h4>
                    <div class="flex items-center space-x-2">
                      <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                      <span class="text-blue-400 text-sm font-medium">连接中</span>
                    </div>
                  </div>

                  <!-- Git平台连接状态 -->
                  <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="bg-blue-500/20 border border-blue-400/30 rounded-lg p-4 text-center">
                      <div class="w-8 h-8 mx-auto mb-2 bg-blue-500 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 0C4.477 0 0 4.484 0 10.017c0 4.425 2.865 8.18 6.839 9.504.5.092.682-.217.682-.483 0-.237-.008-.868-.013-1.703-2.782.605-3.369-1.343-3.369-1.343-.454-1.158-1.11-1.466-1.11-1.466-.908-.62.069-.608.069-.608 1.003.07 1.531 1.032 1.531 1.032.892 1.53 2.341 1.088 2.91.832.092-.647.35-1.088.636-1.338-2.22-.253-4.555-1.113-4.555-4.951 0-1.093.39-1.988 1.029-2.688-.103-.253-.446-1.272.098-2.65 0 0 .84-.27 2.75 1.026A9.564 9.564 0 0110 4.844c.85.004 1.705.115 2.504.337 1.909-1.296 2.747-1.027 2.747-1.027.546 1.379.203 2.398.1 2.651.64.7 1.028 1.595 1.028 2.688 0 3.848-2.339 4.695-4.566 4.942.359.31.678.921.678 1.856 0 1.338-.012 2.419-.012 2.747 0 .268.18.58.688.482A10.019 10.019 0 0020 10.017C20 4.484 15.522 0 10 0z"/>
                        </svg>
                      </div>
                      <div class="text-blue-300 text-xs font-medium">GitHub</div>
                      <div class="text-green-400 text-xs">已连接</div>
                    </div>
                    <div class="bg-orange-500/20 border border-orange-400/30 rounded-lg p-4 text-center">
                      <div class="w-8 h-8 mx-auto mb-2 bg-orange-500 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0zm3.5 6L10 9.5 6.5 6 10 2.5 13.5 6z"/>
                        </svg>
                      </div>
                      <div class="text-orange-300 text-xs font-medium">GitLab</div>
                      <div class="text-green-400 text-xs">已连接</div>
                    </div>
                    <div class="bg-cyan-500/20 border border-cyan-400/30 rounded-lg p-4 text-center">
                      <div class="w-8 h-8 mx-auto mb-2 bg-cyan-500 rounded-full flex items-center justify-center">
                        <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                          <path d="M10 0C4.477 0 0 4.477 0 10s4.477 10 10 10 10-4.477 10-10S15.523 0 10 0zm0 18c-4.411 0-8-3.589-8-8s3.589-8 8-8 8 3.589 8 8-3.589 8-8 8z"/>
                        </svg>
                      </div>
                      <div class="text-cyan-300 text-xs font-medium">Bitbucket</div>
                      <div class="text-yellow-400 text-xs">配置中</div>
                    </div>
                  </div>

                  <!-- 实时同步状态 -->
                  <div class="bg-slate-700/50 rounded-lg p-4">
                    <h5 class="text-white font-medium mb-3">实时同步状态</h5>
                    <div class="space-y-2">
                      <div class="flex items-center justify-between">
                        <span class="text-slate-300 text-sm">Webhook监听</span>
                        <span class="text-green-400 text-sm font-mono">Active</span>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-slate-300 text-sm">OAuth认证</span>
                        <span class="text-green-400 text-sm font-mono">Valid</span>
                      </div>
                      <div class="flex items-center justify-between">
                        <span class="text-slate-300 text-sm">最后同步</span>
                        <span class="text-blue-400 text-sm font-mono">2分钟前</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 步骤2：AI深度解析引擎 -->
              <div
                v-show="activeStep === 2"
                class="space-y-6 animate-fadeInUp"
              >
                <!-- AI处理管道 -->
                <div
                  class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-600"
                >
                  <h4 class="font-bold text-white text-lg mb-4">AI处理管道</h4>
                  <div class="space-y-4">
                    <!-- AST解析 -->
                    <div class="flex items-center space-x-4">
                      <div
                        class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-white font-medium">AST Parser</div>
                        <div class="text-slate-400 text-sm">
                          语法树解析 • 语义分析
                        </div>
                      </div>
                      <div class="text-purple-400 font-mono text-sm">98.7%</div>
                    </div>

                    <!-- 依赖分析 -->
                    <div class="flex items-center space-x-4">
                      <div
                        class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-white font-medium">
                          Dependency Graph
                        </div>
                        <div class="text-slate-400 text-sm">
                          关系图谱 • 循环检测
                        </div>
                      </div>
                      <div class="text-blue-400 font-mono text-sm">94.2%</div>
                    </div>

                    <!-- 模式识别 -->
                    <div class="flex items-center space-x-4">
                      <div
                        class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-white font-medium">
                          Pattern Recognition
                        </div>
                        <div class="text-slate-400 text-sm">
                          设计模式 • 反模式检测
                        </div>
                      </div>
                      <div class="text-green-400 font-mono text-sm">91.8%</div>
                    </div>
                  </div>
                </div>

                <!-- 实时指标 -->
                <div
                  class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-600"
                >
                  <h4 class="font-bold text-white text-lg mb-4">
                    实时技术指标
                  </h4>
                  <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                      <div class="text-2xl font-bold text-cyan-400">15.7ms</div>
                      <div class="text-slate-400 text-sm">AST解析延迟</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-purple-400">
                        2.3GB
                      </div>
                      <div class="text-slate-400 text-sm">内存占用</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-green-400">847</div>
                      <div class="text-slate-400 text-sm">识别模式数</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-orange-400">
                        99.2%
                      </div>
                      <div class="text-slate-400 text-sm">准确率</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 步骤3：智能文档生成 -->
              <div
                v-show="activeStep === 3"
                class="space-y-6 animate-fadeInUp"
              >
                <!-- 文档生成引擎 -->
                <div
                  class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-600"
                >
                  <h4 class="font-bold text-white text-lg mb-4">智能文档生成引擎</h4>
                  <div class="space-y-4">
                    <!-- 架构文档 -->
                    <div class="flex items-center space-x-4">
                      <div
                        class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-lg flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm0 2h12v8H4V6z"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-white font-medium">架构文档</div>
                        <div class="text-slate-400 text-sm">
                          自动生成 • 版本管理
                        </div>
                      </div>
                      <div class="text-indigo-400 font-mono text-sm">完成</div>
                    </div>

                    <!-- API文档 -->
                    <div class="flex items-center space-x-4">
                      <div
                        class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-white font-medium">API文档</div>
                        <div class="text-slate-400 text-sm">
                          接口规范 • 示例代码
                        </div>
                      </div>
                      <div class="text-green-400 font-mono text-sm">完成</div>
                    </div>

                    <!-- 部署指南 -->
                    <div class="flex items-center space-x-4">
                      <div
                        class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            d="M3 7v10a2 2 0 002 2h10a2 2 0 002-2V9a2 2 0 00-2-2h-1V5a3 3 0 00-3-3H9a3 3 0 00-3 3v2H5a2 2 0 00-2 2z"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-white font-medium">部署指南</div>
                        <div class="text-slate-400 text-sm">
                          环境配置 • 运维手册
                        </div>
                      </div>
                      <div class="text-purple-400 font-mono text-sm">生成中</div>
                    </div>
                  </div>
                </div>

                <!-- 文档统计 -->
                <div
                  class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-600"
                >
                  <h4 class="font-bold text-white text-lg mb-4">文档统计</h4>
                  <div class="grid grid-cols-2 gap-4">
                    <div class="text-center">
                      <div class="text-2xl font-bold text-indigo-400">24</div>
                      <div class="text-slate-400 text-sm">文档页面</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-green-400">156</div>
                      <div class="text-slate-400 text-sm">API接口</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-purple-400">8</div>
                      <div class="text-slate-400 text-sm">架构图</div>
                    </div>
                    <div class="text-center">
                      <div class="text-2xl font-bold text-cyan-400">98%</div>
                      <div class="text-slate-400 text-sm">覆盖率</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 步骤4：实时监控与优化 -->
              <div
                v-show="activeStep === 4"
                class="space-y-6 animate-fadeInUp"
              >
                <!-- 健康度监控 -->
                <div
                  class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-600"
                >
                  <h4 class="font-bold text-white text-lg mb-4">架构健康度监控</h4>
                  <div class="space-y-4">
                    <!-- 代码质量 -->
                    <div class="flex items-center space-x-4">
                      <div
                        class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-lg flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-white font-medium">代码质量</div>
                        <div class="text-slate-400 text-sm">
                          复杂度 • 可维护性
                        </div>
                      </div>
                      <div class="text-green-400 font-mono text-sm">优秀</div>
                    </div>

                    <!-- 架构合规 -->
                    <div class="flex items-center space-x-4">
                      <div
                        class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-white font-medium">架构合规</div>
                        <div class="text-slate-400 text-sm">
                          设计模式 • 最佳实践
                        </div>
                      </div>
                      <div class="text-blue-400 font-mono text-sm">良好</div>
                    </div>

                    <!-- 技术债务 -->
                    <div class="flex items-center space-x-4">
                      <div
                        class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-lg flex items-center justify-center"
                      >
                        <svg
                          class="w-6 h-6 text-white"
                          fill="currentColor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92z"
                          />
                        </svg>
                      </div>
                      <div class="flex-1">
                        <div class="text-white font-medium">技术债务</div>
                        <div class="text-slate-400 text-sm">
                          待优化项 • 风险评估
                        </div>
                      </div>
                      <div class="text-orange-400 font-mono text-sm">中等</div>
                    </div>
                  </div>
                </div>

                <!-- 优化建议 -->
                <div
                  class="bg-slate-800/50 backdrop-blur-sm rounded-xl p-6 border border-slate-600"
                >
                  <h4 class="font-bold text-white text-lg mb-4">智能优化建议</h4>
                  <div class="space-y-3">
                    <div class="flex items-start space-x-3">
                      <div class="w-2 h-2 bg-green-400 rounded-full mt-2"></div>
                      <div>
                        <div class="text-white text-sm font-medium">重构建议</div>
                        <div class="text-slate-400 text-xs">
                          UserService类复杂度过高，建议拆分
                        </div>
                      </div>
                    </div>
                    <div class="flex items-start space-x-3">
                      <div class="w-2 h-2 bg-blue-400 rounded-full mt-2"></div>
                      <div>
                        <div class="text-white text-sm font-medium">性能优化</div>
                        <div class="text-slate-400 text-xs">
                          数据库查询可优化，建议添加索引
                        </div>
                      </div>
                    </div>
                    <div class="flex items-start space-x-3">
                      <div class="w-2 h-2 bg-purple-400 rounded-full mt-2"></div>
                      <div>
                        <div class="text-white text-sm font-medium">安全加固</div>
                        <div class="text-slate-400 text-xs">
                          API接口缺少权限验证
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 浮动技术标签 -->
            <div
              class="absolute -top-4 -right-4 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg animate-float"
            >
              Enterprise Grade
            </div>
            <div
              class="absolute -bottom-4 -left-4 bg-gradient-to-r from-green-500 to-cyan-500 text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg animate-float"
              style="animation-delay: 1s"
            >
              Real-time Analysis
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- 支持语言展示 -->
    <section class="relative py-20 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            支持语言
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            支持主流编程语言的代码分析，持续扩展对更多语言的支持
          </p>
        </div>

        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8">
          <!-- Java -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-red-50 to-orange-50 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M8.851 18.56s-.917.534.653.714c1.902.218 2.874.187 4.969-.211 0 0 .552.346 1.321.646-4.699 2.013-10.633-.118-6.943-1.149M8.276 15.933s-1.028.761.542.924c2.032.209 3.636.227 6.413-.308 0 0 .384.389.987.602-5.679 1.661-12.007.13-7.942-1.218"
                  fill="#ED8B00"
                />
                <path
                  d="M13.116 11.475c1.158 1.333-.304 2.533-.304 2.533s2.939-1.518 1.589-3.418c-1.261-1.772-2.228-2.652 3.007-5.688 0-.001-8.216 2.051-4.292 6.573"
                  fill="#ED8B00"
                />
                <path
                  d="M19.33 20.504s.679.559-.747.99c-2.712.822-11.288 1.069-13.669.033-.856-.373.75-.89 1.254-.998.527-.114.828-.093.828-.093-.953-.671-6.156 1.317-2.643 1.887 9.58 1.553 17.462-.7 14.977-1.819M9.292 13.21s-4.362 1.036-1.544 1.412c1.189.159 3.561.123 5.77-.062 1.806-.152 3.618-.477 3.618-.477s-.637.272-1.098.587c-4.429 1.165-12.986.623-10.522-.568 2.082-1.006 3.776-.892 3.776-.892M17.116 17.584c4.503-2.34 2.421-4.589.968-4.285-.355.074-.515.138-.515.138s.132-.207.385-.297c2.875-1.011 5.086 2.981-.928 4.562 0-.001.07-.062.09-.118"
                  fill="#ED8B00"
                />
                <path
                  d="M14.401 0s2.494 2.494-2.365 6.33c-3.896 3.077-.888 4.832-.001 6.836-2.274-2.053-3.943-3.858-2.824-5.539 1.644-2.469 6.197-3.665 5.19-7.627"
                  fill="#ED8B00"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">Java</div>
          </div>

          <!-- Python -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-50 to-yellow-50 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M14.25.18l.9.2.73.26.59.3.45.32.34.34.25.34.16.33.1.3.04.26.02.2-.01.13V8.5l-.05.63-.13.55-.21.46-.26.38-.3.31-.33.25-.35.19-.35.14-.33.1-.3.07-.26.04-.21.02H8.77l-.69.05-.59.14-.5.22-.41.27-.33.32-.27.35-.2.36-.15.37-.1.35-.07.32-.04.27-.02.21v3.06H3.17l-.21-.03-.28-.07-.32-.12-.35-.18-.36-.26-.36-.36-.35-.46-.32-.59-.28-.73-.21-.88-.14-1.05-.05-1.23.06-1.22.16-1.04.24-.87.32-.71.36-.57.4-.44.42-.33.42-.24.4-.16.36-.1.32-.05.24-.01h.16l.06.01h8.16v-.83H6.18l-.01-2.75-.02-.37.05-.34.11-.31.17-.28.25-.26.31-.23.38-.2.44-.18.51-.15.58-.12.64-.1.71-.06.77-.04.84-.02 1.27.05zm-6.3 1.98l-.23.33-.08.41.08.41.23.34.33.22.41.09.41-.09.33-.22.23-.34.08-.41-.08-.41-.23-.33-.33-.22-.41-.09-.41.09-.33.22zM21.1 6.11l.28.06.32.12.35.18.36.27.36.35.35.47.32.59.28.73.21.88.14 1.04.05 1.23-.06 1.23-.16 1.04-.24.86-.32.71-.36.57-.4.45-.42.33-.42.24-.4.16-.36.09-.32.05-.24.02-.16-.01h-8.22v.82h5.84l.01 2.76.02.36-.05.34-.11.31-.17.29-.25.25-.31.24-.38.2-.44.17-.51.15-.58.13-.64.09-.71.07-.77.04-.84.01-1.27-.04-1.07-.14-.9-.2-.73-.25-.59-.3-.45-.33-.34-.34-.25-.34-.16-.33-.1-.3-.04-.25-.02-.2.01-.13v-5.34l.05-.64.13-.54.21-.46.26-.38.3-.32.33-.24.35-.2.35-.14.33-.1.3-.06.26-.04.21-.02.13-.01h5.84l.69-.05.59-.14.5-.21.41-.28.33-.32.27-.35.2-.36.15-.36.1-.35.07-.32.04-.28.02-.21V6.07h2.09l.14.01zm-6.47 14.25l-.23.33-.08.41.08.41.23.33.33.23.41.08.41-.08.33-.23.23-.33.08-.41-.08-.41-.23-.33-.33-.23-.41-.08-.41.08-.33.23z"
                  fill="#306998"
                />
                <path
                  d="M12.74 23.82l.9-.2.73-.26.59-.3.45-.32.34-.34.25-.34.16-.33.1-.3.04-.26.02-.2-.01-.13V15.5l-.05-.63-.13-.55-.21-.46-.26-.38-.3-.31-.33-.25-.35-.19-.35-.14-.33-.1-.3-.07-.26-.04-.21-.02H8.77l-.69-.05-.59-.14-.5-.22-.41-.27-.33-.32-.27-.35-.2-.36-.15-.37-.1-.35-.07-.32-.04-.27-.02-.21V8.94H3.17l-.21.03-.28.07-.32.12-.35.18-.36.26-.36.36-.35.46-.32.59-.28.73-.21.88-.14 1.05-.05 1.23.06 1.22.16 **********.***********.**********.**********.**********.24.01h.16l.06-.01h8.16v.83H6.18l-.01 2.75-.***********.***********.***********.**********.***********.**********.*********** 1.27-.05z"
                  fill="#FFD43B"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">Python</div>
          </div>

          <!-- JavaScript -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M0 0h24v24H0V0zm22.034 18.276c-.175-1.095-.888-2.015-3.003-2.873-.736-.345-1.554-.585-1.797-1.14-.091-.33-.105-.51-.046-.705.15-.646.915-.84 1.515-.***********.42.976.9 1.034-.676 1.034-.676 1.755-1.125-.27-.42-.404-.601-.586-.78-.63-.705-1.469-1.065-2.834-1.034l-.705.089c-.676.165-1.32.525-1.71 1.005-1.14 1.291-.811 3.541.569 4.471 1.365 1.02 3.361 1.244 3.616 2.205.24 1.17-.87 1.545-1.966 1.41-.811-.18-1.26-.586-1.755-1.336l-1.83 1.051c.21.48.45.689.81 1.109 1.74 1.756 6.09 1.666 6.871-1.004.029-.09.24-.705.074-1.65l.046.067zm-8.983-7.245h-2.248c0 1.938-.009 3.864-.009 5.805 0 1.232.063 2.363-.138 2.711-.33.689-1.18.601-1.566.48-.396-.196-.597-.466-.83-.855-.063-.105-.11-.196-.127-.196l-1.825 1.125c.305.63.75 1.172 1.324 1.517.855.51 2.004.675 3.207.405.783-.226 1.458-.691 1.811-1.411.51-.93.402-2.07.397-3.346.012-2.054 0-4.109 0-6.179l.004-.056z"
                  fill="#F7DF1E"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">JavaScript</div>
          </div>

          <!-- TypeScript -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M1.125 0C.502 0 0 .502 0 1.125v21.75C0 23.498.502 24 1.125 24h21.75c.623 0 1.125-.502 1.125-1.125V1.125C24 .502 23.498 0 22.875 0H1.125zm17.363 9.75c.612 0 1.154.037 1.627.111a6.38 6.38 0 0 1 1.306.34v2.458a3.95 3.95 0 0 0-.643-.361 5.093 5.093 0 0 0-.717-.26 5.453 5.453 0 0 0-1.426-.2c-.3 0-.573.028-.819.086a2.1 2.1 0 0 0-.623.242c-.17.104-.3.229-.393.374a.888.888 0 0 0-.14.49c0 .196.053.373.156.529.104.156.252.304.443.444s.423.276.696.41c.273.135.582.274.926.416.47.197.892.407 1.266.628.374.222.695.473.963.753.268.279.472.598.614.957.142.359.214.776.214 1.253 0 .657-.125 1.21-.373 1.656a3.033 3.033 0 0 1-1.012 1.085 4.38 4.38 0 0 1-1.487.596c-.566.12-1.163.18-1.79.18a9.916 9.916 0 0 1-1.84-.164 5.544 5.544 0 0 1-1.512-.493v-2.63a5.033 5.033 0 0 0 3.237 1.2c.333 0 .624-.03.872-.09.249-.06.456-.144.623-.25.166-.108.29-.234.373-.38a1.023 1.023 0 0 0-.074-1.089 2.12 2.12 0 0 0-.537-.5 5.597 5.597 0 0 0-.807-.444 27.72 27.72 0 0 0-1.007-.436c-.918-.383-1.602-.852-2.053-1.405-.45-.553-.676-1.222-.676-2.005 0-.614.123-1.141.369-1.582.246-.441.58-.804 1.004-1.089a4.494 4.494 0 0 1 1.47-.629 7.536 7.536 0 0 1 1.77-.201zm-15.113.188h9.563v2.166H9.506v9.646H6.789v-9.646H3.375z"
                  fill="#3178C6"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">TypeScript</div>
          </div>

          <!-- Go -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-cyan-50 to-blue-50 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M1.811 10.231c-.047 0-.058-.023-.035-.059l.246-.315c.023-.035.081-.058.128-.058h4.172c.046 0 .058.035.035.07l-.199.303c-.023.036-.082.07-.117.07H1.811zM.047 11.306c-.047 0-.059-.023-.035-.058l.245-.316c.023-.035.082-.058.129-.058h5.516c.047 0 .07.035.058.07l-.093.28c-.012.047-.058.07-.105.07H.047zM2.828 12.381c-.047 0-.07-.024-.047-.059l.163-.292c.023-.047.070-.070.117-.070h2.337c.047 0 .070.035.070.082l-.023.28c0 .047-.047.082-.082.082H2.828zM21.953 11.952c-.129.070-.315.07-.479.07h-4.145c-.163 0-.28-.012-.363-.047-.129-.047-.257-.159-.257-.315 0-.129.070-.315.315-.338.047 0 .164-.012.164-.012.398-.024.75-.036 1.101-.071.023 0 .047-.023.047-.047v-.106c0-.106-.023-.129-.129-.129-.023 0-.070.012-.093.012-.047 0-.117 0-.164-.012-.117-.023-.21-.070-.257-.14-.047-.082-.07-.188-.07-.315v-.14c0-.129.023-.234.070-.315.047-.07.14-.117.257-.14.047-.012.117-.012.164-.012.023 0 .070-.012.093-.012.106 0 .129-.023.129-.129v-.106c0-.023-.024-.047-.047-.047-.351-.035-.703-.047-1.101-.07 0 0-.117-.013-.164-.013-.245-.023-.315-.209-.315-.338 0-.156.128-.268.257-.315.082-.035.199-.047.363-.047h4.145c.164 0 .351 0 .479.070.129.082.199.234.199.421v8.597c-.001.188-.07.34-.2.422zM8.936 12.26c-.07-.024-.164-.047-.234-.047-.047 0-.117.023-.164.047-.047.023-.094.070-.118.117-.023.047-.035.117-.035.164 0 .047.012.117.035.164.024.047.071.094.118.117.047.024.117.047.164.047.070 0 .164-.023.234-.047.047-.023.094-.070.117-.117.024-.047.035-.117.035-.164 0-.047-.011-.117-.035-.164-.023-.047-.07-.094-.117-.117zM21.953 11.952c-.129.070-.315.07-.479.07h-4.145c-.163 0-.28-.012-.363-.047-.129-.047-.257-.159-.257-.315 0-.129.070-.315.315-.338.047 0 .164-.012.164-.012.398-.024.75-.036 1.101-.071.023 0 .047-.023.047-.047v-.106c0-.106-.023-.129-.129-.129-.023 0-.070.012-.093.012-.047 0-.117 0-.164-.012-.117-.023-.21-.070-.257-.14-.047-.082-.07-.188-.07-.315v-.14c0-.129.023-.234.070-.315.047-.07.14-.117.257-.14.047-.012.117-.012.164-.012.023 0 .070-.012.093-.012.106 0 .129-.023.129-.129v-.106c0-.023-.024-.047-.047-.047-.351-.035-.703-.047-1.101-.07 0 0-.117-.013-.164-.013-.245-.023-.315-.209-.315-.338 0-.156.128-.268.257-.315.082-.035.199-.047.363-.047h4.145c.164 0 .351 0 .479.070.129.082.199.234.199.421v8.597c-.001.188-.07.34-.2.422z"
                  fill="#00ADD8"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">Go</div>
          </div>

          <!-- C# -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-purple-50 to-purple-100 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zM9.426 7.12a5.55 5.55 0 0 1 1.985.38v1.181a4.5 4.5 0 0 0-2.025-.566 2.9 2.9 0 0 0-2.04.566 2.2 2.2 0 0 0-.566 1.781c0 .668.188 1.24.565 1.716a2.9 2.9 0 0 0 2.011.7 4.6 4.6 0 0 0 2.08-.634v1.215a5.9 5.9 0 0 1-2.08.346c-1.29 0-2.295-.365-3.01-1.095C5.8 14.035 5.48 13.085 5.48 11.84c0-1.134.32-2.084.96-2.85.64-.765 1.585-1.152 2.835-1.152h.15zm5.749.015h.48l1.26 3.291 1.26-3.291h.48v5.61h-.48v-4.8l-1.26 3.3h-.48l-1.26-3.3v4.8h-.48V7.135z"
                  fill="#239120"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">C#</div>
          </div>

          <!-- PHP -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-indigo-50 to-purple-50 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M7.01 10.207h-.944l-.515 2.648h.838c.556 0 .982-.122 1.292-.391.313-.27.47-.628.47-1.074 0-.3-.093-.525-.277-.675-.184-.15-.462-.224-.834-.224l-.03-.284zm8.39-2.408h-.905l-.515 2.648h.838c.556 0 .982-.122 1.292-.391.313-.27.47-.628.47-1.074 0-.3-.093-.525-.277-.675-.184-.15-.462-.224-.834-.224l-.069-.284z"
                  fill="#777BB4"
                />
                <path
                  d="M11.927 11.928c-.355-.108-.63-.27-.825-.486-.195-.216-.293-.5-.293-.851 0-.132.017-.258.052-.378.035-.12.09-.23.165-.33.075-.1.17-.184.285-.252.115-.068.252-.102.41-.102.22 0 .394.055.523.165.129.11.193.294.193.551 0 .132-.017.258-.052.378-.035.12-.09.23-.165.33-.075.1-.17.184-.285.252-.115.068-.252.102-.41.102-.22 0-.394-.055-.523-.165-.129-.11-.193-.294-.193-.551z"
                  fill="#777BB4"
                />
                <path
                  d="M24 12c0 6.627-5.373 12-12 12S0 18.627 0 12 5.373 0 12 0s12 5.373 12 12zM8.5 8.5h2.915c.746 0 1.32.192 1.722.576.402.384.603.92.603 1.608 0 .384-.074.744-.223 1.08-.149.336-.363.628-.642.876-.279.248-.620.442-1.023.582-.403.14-.856.21-1.359.21H9.297L8.5 16.5H6.5L8.5 8.5zm7.5 0h2.915c.746 0 1.32.192 1.722.576.402.384.603.92.603 1.608 0 .384-.074.744-.223 1.08-.149.336-.363.628-.642.876-.279.248-.620.442-1.023.582-.403.14-.856.21-1.359.21h-1.196L16.797 16.5H14.797L16 8.5z"
                  fill="#777BB4"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">PHP</div>
          </div>

          <!-- Rust -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M23.8346 11.7033l-1.0073-.6236a13.7268 13.7268 0 0 0-.0283-.2936l.8656-.8069a.3483.3483 0 0 0-.1154-.5938l-1.0735-.4262a13.8956 13.8956 0 0 0-.0566-.2898l.7003-.9313a.3462.3462 0 0 0-.2257-.5526l-1.1334-.1894a14.5631 14.5631 0 0 0-.0849-.2835l.5107-1.0261a.3483.3483 0 0 0-.3347-.5071l-1.1675.0472a14.4097 14.4097 0 0 0-.1132-.2749l.3019-1.0925a.3462.3462 0 0 0-.4378-.4378l-1.0925.3019a14.4097 14.4097 0 0 0-.2749-.1132l.0472-1.1675a.3483.3483 0 0 0-.5071-.3347l-1.0261.5107a14.5631 14.5631 0 0 0-.2835-.0849l-.1894-1.1334a.3462.3462 0 0 0-.5526-.2257l-.9313.7003a13.8956 13.8956 0 0 0-.2898-.0566l-.4262-1.0735a.3483.3483 0 0 0-.5938-.1154l-.8069.8656a13.7268 13.7268 0 0 0-.2936-.0283l-.6236-1.0073a.3462.3462 0 0 0-.6236 0l-.6236 1.0073a13.7268 13.7268 0 0 0-.2936.0283l-.8069-.8656a.3483.3483 0 0 0-.5938.1154l-.4262 1.0735a13.8956 13.8956 0 0 0-.2898.0566l-.9313-.7003a.3462.3462 0 0 0-.5526.2257l-.1894 1.1334a14.5631 14.5631 0 0 0-.2835.0849l-1.0261-.5107a.3483.3483 0 0 0-.5071.3347l.0472 1.1675a14.4097 14.4097 0 0 0-.2749.1132l-1.0925-.3019a.3462.3462 0 0 0-.4378.4378l.3019 1.0925a14.4097 14.4097 0 0 0-.1132.2749l-1.1675-.0472a.3483.3483 0 0 0-.3347.5071l.5107 1.0261a14.5631 14.5631 0 0 0-.0849.2835l-1.1334.1894a.3462.3462 0 0 0-.2257.5526l.7003.9313a13.8956 13.8956 0 0 0-.0566.2898l-1.0735.4262a.3483.3483 0 0 0-.1154.5938l.8656.8069a13.7268 13.7268 0 0 0-.0283.2936l-1.0073.6236a.3462.3462 0 0 0 0 .6236l1.0073.6236a13.7268 13.7268 0 0 0 .0283.2936l-.8656.8069a.3483.3483 0 0 0 .1154.5938l1.0735.4262a13.8956 13.8956 0 0 0 .0566.2898l-.7003.9313a.3462.3462 0 0 0 .2257.5526l1.1334.1894a14.5631 14.5631 0 0 0 .0849.2835l-.5107 1.0261a.3483.3483 0 0 0 .3347.5071l1.1675-.0472a14.4097 14.4097 0 0 0 .1132.2749l-.3019 1.0925a.3462.3462 0 0 0 .4378.4378l1.0925-.3019a14.4097 14.4097 0 0 0 .2749.1132l-.0472 1.1675a.3483.3483 0 0 0 .5071.3347l1.0261-.5107a14.5631 14.5631 0 0 0 .2835.0849l.1894 1.1334a.3462.3462 0 0 0 .5526.2257l.9313-.7003a13.8956 13.8956 0 0 0 .2898.0566l.4262 1.0735a.3483.3483 0 0 0 .5938.1154l.8069-.8656a13.7268 13.7268 0 0 0 .2936.0283l.6236 1.0073a.3462.3462 0 0 0 .6236 0l.6236-1.0073a13.7268 13.7268 0 0 0 .2936-.0283l.8069.8656a.3483.3483 0 0 0 .5938-.1154l.4262-1.0735a13.8956 13.8956 0 0 0 .2898-.0566l.9313.7003a.3462.3462 0 0 0 .5526-.2257l.1894-1.1334a14.5631 14.5631 0 0 0 .2835-.0849l1.0261.5107a.3483.3483 0 0 0 .5071-.3347l-.0472-1.1675a14.4097 14.4097 0 0 0 .2749-.1132l1.0925.3019a.3462.3462 0 0 0 .4378-.4378l-.3019-1.0925a14.4097 14.4097 0 0 0 .1132-.2749l1.1675.0472a.3483.3483 0 0 0 .3347-.5071l-.5107-1.0261a14.5631 14.5631 0 0 0 .0849-.2835l1.1334-.1894a.3462.3462 0 0 0 .2257-.5526l-.7003-.9313a13.8956 13.8956 0 0 0 .0566-.2898l1.0735-.4262a.3483.3483 0 0 0 .1154-.5938l-.8656-.8069a13.7268 13.7268 0 0 0 .0283-.2936l1.0073-.6236a.3462.3462 0 0 0 0-.6236zM12 16.1132a4.1132 4.1132 0 1 1 0-8.2264 4.1132 4.1132 0 0 1 0 8.2264z"
                  fill="#CE422B"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">Rust</div>
          </div>

          <!-- Kotlin -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-purple-50 to-orange-50 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M24 24H0V0h24L12 12 24 24z"
                  fill="url(#kotlin-gradient)"
                />
                <defs>
                  <linearGradient
                    id="kotlin-gradient"
                    x1="0%"
                    y1="0%"
                    x2="100%"
                    y2="100%"
                  >
                    <stop offset="0%" style="stop-color: #e44857" />
                    <stop offset="25%" style="stop-color: #c711e1" />
                    <stop offset="50%" style="stop-color: #7f52ff" />
                    <stop offset="75%" style="stop-color: #0b69ff" />
                    <stop offset="100%" style="stop-color: #4285f4" />
                  </linearGradient>
                </defs>
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">Kotlin</div>
          </div>

          <!-- Swift -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-orange-50 to-red-50 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M7.508 0c-.287 0-.573 0-.86.002-.86.002-1.717.174-2.537.507C3.3 1.047 2.553 1.7 1.97 2.507 1.388 3.314.99 4.25.814 5.24c-.176.99-.176 2.013 0 3.003.176.99.574 1.926 1.156 2.733.582.807 1.329 1.46 2.14 1.998.81.538 1.717.91 2.677 1.086.96.176 1.953.176 2.913 0 .96-.176 1.867-.548 2.677-1.086.81-.538 1.558-1.191 2.14-1.998.582-.807.98-1.743 1.156-2.733.176-.99.176-2.013 0-3.003-.176-.99-.574-1.926-1.156-2.733C13.935 1.7 13.188 1.047 12.377.509 11.566-.029 10.659-.401 9.699-.577 8.739-.753 7.746-.753 6.786-.577c-.96.176-1.867.548-2.677 1.086C3.299 1.047 2.552 1.7 1.97 2.507 1.388 3.314.99 4.25.814 5.24c-.176.99-.176 2.013 0 3.003.176.99.574 1.926 1.156 2.733.582.807 1.329 1.46 2.14 1.998.81.538 1.717.91 2.677 1.086.96.176 1.953.176 2.913 0 .96-.176 1.867-.548 2.677-1.086.81-.538 1.558-1.191 2.14-1.998.582-.807.98-1.743 1.156-2.733.176-.99.176-2.013 0-3.003-.176-.99-.574-1.926-1.156-2.733C13.935 1.7 13.188 1.047 12.377.509 11.566-.029 10.659-.401 9.699-.577 8.739-.753 7.746-.753 6.786-.577c-.96.176-1.867.548-2.677 1.086z"
                  fill="#FA7343"
                />
                <path
                  d="M18.803 9.021c.138-2.114-.637-4.084-2.133-5.424C15.174 2.253 13.204 1.478 11.09 1.616c-2.114.138-4.084.913-5.424 2.409C4.322 5.461 3.547 7.431 3.685 9.545c.138 2.114.913 4.084 2.409 5.424 1.436 1.284 3.406 2.059 5.52 1.921 2.114-.138 4.084-.913 5.424-2.409 1.284-1.436 2.059-3.406 1.921-5.52z"
                  fill="#FA7343"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">Swift</div>
          </div>

          <!-- Ruby -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-red-50 to-red-100 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M20.156.083c3.033.525 3.893 2.598 3.829 4.77L24 4.822 22.635 22.71 4.89 23.926.001 14.22 3.196 9.498l1.9 8.39 9.014-.615 2.697-13.758z"
                  fill="#CC342D"
                />
                <path
                  d="M4.89 23.926l17.745-1.216-8.872-4.898z"
                  fill="#A0041E"
                />
                <path
                  d="M15.204 1.299l7.633 3.523-2.697 13.758-9.014.615z"
                  fill="#9B0E17"
                />
                <path
                  d="M6.731 14.538l-1.9-8.39 8.433-4.849 2.04 16.037z"
                  fill="#CC342D"
                />
                <path
                  d="M3.196 9.498L.001 14.22l4.89 9.706 1.84-8.388z"
                  fill="#9B0E17"
                />
                <path
                  d="M13.264 1.299L6.731 14.538l8.433 4.849z"
                  fill="#A0041E"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">Ruby</div>
          </div>

          <!-- C++ -->
          <div class="group text-center">
            <div
              class="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-blue-50 to-blue-100 rounded-2xl flex items-center justify-center transform group-hover:scale-110 group-hover:rotate-6 transition-all duration-300 shadow-lg"
            >
              <svg class="w-12 h-12" viewBox="0 0 24 24" fill="none">
                <path
                  d="M22.394 6c-.167-.29-.398-.543-.652-.69L12.926.22c-.509-.294-1.34-.294-1.848 0L2.26 5.31c-.508.293-.923 1.013-.923 1.6v10.18c0 .294.104.62.271.91.167.29.398.543.652.69l8.816 5.09c.508.293 1.34.293 1.848 0l8.816-5.09c.254-.147.485-.4.652-.69.167-.29.27-.616.27-.91V6.91c.003-.294-.1-.62-.268-.91zM12 19.11c-3.92 0-7.109-3.19-7.109-7.11 0-3.92 3.19-7.11 7.109-7.11a7.133 7.133 0 016.156 3.553l-3.076 1.78a3.567 3.567 0 00-3.08-1.78A3.56 3.56 0 008.444 12 3.56 3.56 0 0012 15.555a3.57 3.57 0 003.08-1.778l3.078 1.78A7.135 7.135 0 0112 19.11zm7.11-6.715h-.79v.79h-.79v-.79h-.79v-.79h.79v-.79h.79v.79h.79v.79zm2.962 0h-.79v.79h-.79v-.79h-.79v-.79h.79v-.79h.79v.79h.79v.79z"
                  fill="#00599C"
                />
              </svg>
            </div>
            <div class="text-sm font-medium text-gray-700">C++</div>
          </div>
        </div>
      </div>
    </section>

    <!-- 工作流程 - 重新设计 -->
    <section
      class="relative py-24 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 overflow-hidden"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0">
        <div
          class="absolute top-0 left-1/4 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"
        ></div>
        <div
          class="absolute bottom-0 right-1/4 w-96 h-96 bg-blue-500/10 rounded-full blur-3xl"
        ></div>
        <div
          class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 bg-gradient-to-r from-purple-400/5 to-blue-400/5 rounded-full animate-pulse"
        ></div>
      </div>

      <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-20">
          <div
            class="inline-flex items-center px-4 py-2 bg-white/10 backdrop-blur-sm rounded-full text-white/80 text-sm font-medium mb-6"
          >
            <span
              class="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"
            ></span>
            智能工作流程
          </div>
          <h2
            class="text-4xl md:text-6xl font-bold text-white mb-6 leading-tight"
          >
            简单<span
              class="bg-gradient-to-r from-purple-400 to-blue-400 bg-clip-text text-transparent"
              >四步</span
            >，开启
            <br class="hidden md:block" />
            <span
              class="bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent"
              >智能架构管理</span
            >
          </h2>
          <p class="text-xl text-white/80 max-w-3xl mx-auto leading-relaxed">
            从项目注册到文档生成，全程自动化，让您专注于核心业务创新
          </p>
        </div>

        <!-- 步骤流程 -->
        <div class="relative">
          <!-- 连接线 -->
          <div
            class="hidden lg:block absolute top-1/2 left-0 right-0 h-0.5 bg-gradient-to-r from-purple-500/30 via-blue-500/30 to-purple-500/30 transform -translate-y-1/2"
          ></div>

          <div
            class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-4"
          >
            <!-- 步骤 1 -->
            <div class="relative group">
              <div class="text-center">
                <!-- 步骤圆圈 -->
                <div class="relative mb-8">
                  <div
                    class="w-32 h-32 mx-auto bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center shadow-2xl transform group-hover:scale-110 transition-all duration-500 relative overflow-hidden"
                  >
                    <!-- 发光效果 -->
                    <div
                      class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-full"
                    ></div>
                    <!-- 步骤数字 -->
                    <span class="relative text-3xl font-bold text-white"
                      >1</span
                    >
                    <!-- 脉冲动画 -->
                    <div
                      class="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full animate-ping"
                    ></div>
                    <div
                      class="absolute -top-1 -right-1 w-6 h-6 bg-yellow-300 rounded-full"
                    ></div>
                  </div>
                  <!-- 图标 -->
                  <div
                    class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center"
                  >
                    <svg
                      class="w-6 h-6 text-purple-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
                      ></path>
                    </svg>
                  </div>
                </div>

                <!-- 内容 -->
                <div
                  class="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 group-hover:bg-white/10 transition-all duration-300"
                >
                  <h3 class="text-2xl font-bold text-white mb-4">连接仓库</h3>
                  <p class="text-white/70 leading-relaxed">
                    安全连接Git、SVN等代码仓库，支持私有化部署，保障代码安全
                  </p>
                  <div
                    class="mt-4 flex items-center justify-center space-x-2 text-sm text-white/50"
                  >
                    <span>支持</span>
                    <div class="flex space-x-1">
                      <span class="px-2 py-1 bg-white/10 rounded text-xs"
                        >Git</span
                      >
                      <span class="px-2 py-1 bg-white/10 rounded text-xs"
                        >SVN</span
                      >
                      <span class="px-2 py-1 bg-white/10 rounded text-xs"
                        >更多</span
                      >
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤 2 -->
            <div class="relative group">
              <div class="text-center">
                <div class="relative mb-8">
                  <div
                    class="w-32 h-32 mx-auto bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center shadow-2xl transform group-hover:scale-110 transition-all duration-500 relative overflow-hidden"
                  >
                    <div
                      class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-full"
                    ></div>
                    <span class="relative text-3xl font-bold text-white"
                      >2</span
                    >
                  </div>
                  <div
                    class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center"
                  >
                    <svg
                      class="w-6 h-6 text-blue-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"
                      ></path>
                    </svg>
                  </div>
                </div>

                <div
                  class="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 group-hover:bg-white/10 transition-all duration-300"
                >
                  <h3 class="text-2xl font-bold text-white mb-4">AI分析</h3>
                  <p class="text-white/70 leading-relaxed">
                    运用大语言模型深度分析代码结构，识别架构模式和潜在问题
                  </p>
                  <div
                    class="mt-4 flex items-center justify-center space-x-2 text-sm text-white/50"
                  >
                    <div class="flex items-center space-x-1">
                      <div
                        class="w-2 h-2 bg-green-400 rounded-full animate-pulse"
                      ></div>
                      <span>AI驱动</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤 3 -->
            <div class="relative group">
              <div class="text-center">
                <div class="relative mb-8">
                  <div
                    class="w-32 h-32 mx-auto bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-full flex items-center justify-center shadow-2xl transform group-hover:scale-110 transition-all duration-500 relative overflow-hidden"
                  >
                    <div
                      class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-full"
                    ></div>
                    <span class="relative text-3xl font-bold text-white"
                      >3</span
                    >
                  </div>
                  <div
                    class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center"
                  >
                    <svg
                      class="w-6 h-6 text-emerald-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      ></path>
                    </svg>
                  </div>
                </div>

                <div
                  class="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 group-hover:bg-white/10 transition-all duration-300"
                >
                  <h3 class="text-2xl font-bold text-white mb-4">生成文档</h3>
                  <p class="text-white/70 leading-relaxed">
                    自动生成详细架构文档，包含组件图、依赖关系图和API文档
                  </p>
                  <div
                    class="mt-4 flex items-center justify-center space-x-2 text-sm text-white/50"
                  >
                    <span>自动化生成</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 步骤 4 -->
            <div class="relative group">
              <div class="text-center">
                <div class="relative mb-8">
                  <div
                    class="w-32 h-32 mx-auto bg-gradient-to-br from-orange-500 to-orange-600 rounded-full flex items-center justify-center shadow-2xl transform group-hover:scale-110 transition-all duration-500 relative overflow-hidden"
                  >
                    <div
                      class="absolute inset-0 bg-gradient-to-br from-white/20 to-transparent rounded-full"
                    ></div>
                    <span class="relative text-3xl font-bold text-white"
                      >4</span
                    >
                  </div>
                  <div
                    class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center"
                  >
                    <svg
                      class="w-6 h-6 text-orange-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                      ></path>
                    </svg>
                  </div>
                </div>

                <div
                  class="bg-white/5 backdrop-blur-sm rounded-2xl p-6 border border-white/10 group-hover:bg-white/10 transition-all duration-300"
                >
                  <h3 class="text-2xl font-bold text-white mb-4">持续监控</h3>
                  <p class="text-white/70 leading-relaxed">
                    实时监控项目健康状态，持续优化架构，提供改进建议
                  </p>
                  <div
                    class="mt-4 flex items-center justify-center space-x-2 text-sm text-white/50"
                  >
                    <div class="flex items-center space-x-1">
                      <div
                        class="w-2 h-2 bg-orange-400 rounded-full animate-pulse"
                      ></div>
                      <span>实时监控</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部CTA -->
        <div class="text-center mt-16">
          <button
            @click="navigateToProjects"
            class="group relative px-10 py-4 bg-gradient-to-r from-purple-600 to-blue-600 text-white font-semibold rounded-2xl shadow-2xl hover:shadow-purple-500/25 transform hover:scale-105 transition-all duration-300 overflow-hidden"
          >
            <span class="relative z-10 flex items-center space-x-2">
              <span>立即体验四步流程</span>
              <svg
                class="w-5 h-5 transform group-hover:translate-x-1 transition-transform duration-200"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 7l5 5m0 0l-5 5m5-5H6"
                ></path>
              </svg>
            </span>
            <div
              class="absolute inset-0 bg-gradient-to-r from-blue-600 to-purple-600 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
            ></div>
          </button>
        </div>
      </div>
    </section>

    <!-- 价格方案部分 -->
    <section
      id="pricing"
      class="relative py-20 bg-gradient-to-br from-gray-50 to-white"
    >
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            灵活的价格方案
          </h2>
          <p class="text-lg text-gray-600 max-w-2xl mx-auto">
            选择适合您团队规模的方案，随时可以升级或降级
          </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
          <!-- 基础版 -->
          <div
            class="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 relative"
          >
            <div class="text-center">
              <h3 class="text-xl font-bold text-gray-900 mb-2">基础版</h3>
              <p class="text-gray-600 mb-6">适合个人开发者和小型项目</p>
              <div class="mb-6">
                <span class="text-4xl font-bold text-gray-900">免费</span>
              </div>
              <button
                class="w-full bg-gray-100 text-gray-700 py-3 px-6 rounded-lg font-semibold hover:bg-gray-200 transition-colors duration-200"
              >
                开始使用
              </button>
            </div>
            <div class="mt-8 space-y-4">
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">最多3个项目</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">基础架构分析</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">标准文档模板</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">社区支持</span>
              </div>
            </div>
          </div>

          <!-- 专业版 -->
          <div
            class="bg-white rounded-2xl shadow-xl border-2 border-primary-500 p-8 relative transform scale-105"
          >
            <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
              <span
                class="bg-primary-500 text-white px-4 py-1 rounded-full text-sm font-medium"
                >推荐</span
              >
            </div>
            <div class="text-center">
              <h3 class="text-xl font-bold text-gray-900 mb-2">专业版</h3>
              <p class="text-gray-600 mb-6">适合中小型团队和企业项目</p>
              <div class="mb-6">
                <span class="text-4xl font-bold text-gray-900">¥299</span>
                <span class="text-gray-600">/月</span>
              </div>
              <button
                class="w-full bg-gradient-to-r from-primary-600 to-primary-700 text-white py-3 px-6 rounded-lg font-semibold hover:from-primary-700 hover:to-primary-800 transition-all duration-200 shadow-lg"
              >
                立即购买
              </button>
            </div>
            <div class="mt-8 space-y-4">
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">无限项目</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">高级AI分析</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">自定义文档模板</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">团队协作功能</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">优先技术支持</span>
              </div>
            </div>
          </div>

          <!-- 企业版 -->
          <div
            class="bg-white rounded-2xl shadow-lg border border-gray-200 p-8 relative"
          >
            <div class="text-center">
              <h3 class="text-xl font-bold text-gray-900 mb-2">企业版</h3>
              <p class="text-gray-600 mb-6">适合大型企业和复杂项目</p>
              <div class="mb-6">
                <span class="text-4xl font-bold text-gray-900">¥999</span>
                <span class="text-gray-600">/月</span>
              </div>
              <button
                class="w-full bg-gray-900 text-white py-3 px-6 rounded-lg font-semibold hover:bg-gray-800 transition-colors duration-200"
              >
                联系销售
              </button>
            </div>
            <div class="mt-8 space-y-4">
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">私有化部署</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">企业级安全</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">定制化开发</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">专属客户经理</span>
              </div>
              <div class="flex items-center space-x-3">
                <svg
                  class="w-5 h-5 text-green-500"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd"
                  ></path>
                </svg>
                <span class="text-gray-600">7×24小时支持</span>
              </div>
            </div>
          </div>
        </div>

        <div class="text-center mt-12">
          <p class="text-gray-600 mb-4">所有方案都包含30天免费试用期</p>
          <p class="text-sm text-gray-500">价格不含税，支持年付享受8折优惠</p>
        </div>
      </div>
    </section>

    <!-- 成功案例部分 -->
    <section
      id="testimonials"
      class="relative py-24 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 overflow-hidden"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,white,rgba(255,255,255,0.6))] -z-10"></div>
      <div class="absolute top-0 left-1/2 -translate-x-1/2 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl -z-10"></div>

      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <!-- 标题部分 -->
        <div class="text-center mb-20">
          <div class="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-6">
            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            客户成功验证
          </div>
          <h2 class="text-4xl md:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-900 to-purple-900 bg-clip-text text-transparent mb-6">
            客户成功案例
          </h2>
          <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            看看全球领先企业如何通过ArchScope实现<span class="text-blue-600 font-semibold">架构治理现代化</span>，
            平均提升<span class="text-green-600 font-semibold">85%开发效率</span>
          </p>
        </div>

        <!-- 成功案例卡片 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
          <!-- 案例1: 金融科技 -->
          <div class="group relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 hover:bg-white hover:shadow-2xl hover:shadow-blue-500/10 transition-all duration-500 border border-white/50 hover:border-blue-200 transform hover:-translate-y-2">
            <!-- 成功指标标签 -->
            <div class="absolute -top-3 -right-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
              效率提升 80%
            </div>

            <!-- 公司信息 -->
            <div class="flex items-center mb-6">
              <div class="relative">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 via-blue-600 to-indigo-600 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg">
                  招
                </div>
                <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"/>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h4 class="font-bold text-gray-900 text-lg">招商银行科技</h4>
                <p class="text-gray-600 text-sm flex items-center">
                  <svg class="w-4 h-4 mr-1 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4zM18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z"/>
                  </svg>
                  金融科技 • 5000+ 员工
                </p>
              </div>
            </div>

            <!-- 评价内容 -->
            <blockquote class="text-gray-700 mb-6 leading-relaxed relative">
              <svg class="absolute -top-2 -left-2 w-8 h-8 text-blue-200" fill="currentColor" viewBox="0 0 32 32">
                <path d="M10 8c-3.3 0-6 2.7-6 6v10h10V14h-4c0-2.2 1.8-4 4-4V8zm12 0c-3.3 0-6 2.7-6 6v10h10V14h-4c0-2.2 1.8-4 4-4V8z"/>
              </svg>
              <span class="relative z-10">
                "ArchScope帮助我们将<strong class="text-blue-600">架构文档维护时间减少了80%</strong>，团队协作效率显著提升。AI分析功能特别强大，能够发现我们之前忽略的架构问题，为我们节省了<strong class="text-green-600">数百万开发成本</strong>。"
              </span>
            </blockquote>

            <!-- 具体成果数据 -->
            <div class="grid grid-cols-2 gap-4 mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl">
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">80%</div>
                <div class="text-xs text-gray-600">文档维护时间减少</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">300万</div>
                <div class="text-xs text-gray-600">年度成本节省</div>
              </div>
            </div>

            <!-- 推荐人信息 -->
            <div class="flex items-center">
              <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=48&h=48&fit=crop&crop=face&auto=format&q=80"
                   alt="张伟" class="w-12 h-12 rounded-full border-2 border-white shadow-md">
              <div class="ml-3">
                <p class="font-semibold text-gray-900">张伟</p>
                <p class="text-gray-600 text-sm flex items-center">
                  <svg class="w-3 h-3 mr-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                  技术总监 • 15年经验
                </p>
              </div>
            </div>
          </div>

          <!-- 案例2: 互联网创业 -->
          <div class="group relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 hover:bg-white hover:shadow-2xl hover:shadow-green-500/10 transition-all duration-500 border border-white/50 hover:border-green-200 transform hover:-translate-y-2">
            <!-- 成功指标标签 -->
            <div class="absolute -top-3 -right-3 bg-gradient-to-r from-emerald-500 to-teal-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
              上手时间 90% ↓
            </div>

            <!-- 公司信息 -->
            <div class="flex items-center mb-6">
              <div class="relative">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 via-emerald-600 to-teal-600 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg">
                  字
                </div>
                <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-orange-500 rounded-full border-2 border-white flex items-center justify-center">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3z"/>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h4 class="font-bold text-gray-900 text-lg">字节跳动</h4>
                <p class="text-gray-600 text-sm flex items-center">
                  <svg class="w-4 h-4 mr-1 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                  互联网科技 • 独角兽企业
                </p>
              </div>
            </div>

            <!-- 评价内容 -->
            <blockquote class="text-gray-700 mb-6 leading-relaxed relative">
              <svg class="absolute -top-2 -left-2 w-8 h-8 text-green-200" fill="currentColor" viewBox="0 0 32 32">
                <path d="M10 8c-3.3 0-6 2.7-6 6v10h10V14h-4c0-2.2 1.8-4 4-4V8zm12 0c-3.3 0-6 2.7-6 6v10h10V14h-4c0-2.2 1.8-4 4-4V8z"/>
              </svg>
              <span class="relative z-10">
                "作为快速发展的科技公司，我们需要快速迭代。ArchScope让我们能够在保持代码质量的同时快速开发，<strong class="text-green-600">新员工上手时间从2周缩短到2天</strong>，自动生成的文档质量超出预期。"
              </span>
            </blockquote>

            <!-- 具体成果数据 -->
            <div class="grid grid-cols-2 gap-4 mb-6 p-4 bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl">
              <div class="text-center">
                <div class="text-2xl font-bold text-green-600">90%</div>
                <div class="text-xs text-gray-600">新人上手时间减少</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">50+</div>
                <div class="text-xs text-gray-600">微服务自动化管理</div>
              </div>
            </div>

            <!-- 推荐人信息 -->
            <div class="flex items-center">
              <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=48&h=48&fit=crop&crop=face&auto=format&q=80"
                   alt="李明" class="w-12 h-12 rounded-full border-2 border-white shadow-md">
              <div class="ml-3">
                <p class="font-semibold text-gray-900">李明</p>
                <p class="text-gray-600 text-sm flex items-center">
                  <svg class="w-3 h-3 mr-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                  CTO • 架构专家
                </p>
              </div>
            </div>
          </div>

          <!-- 案例3: 大数据企业 -->
          <div class="group relative bg-white/80 backdrop-blur-sm rounded-3xl p-8 hover:bg-white hover:shadow-2xl hover:shadow-purple-500/10 transition-all duration-500 border border-white/50 hover:border-purple-200 transform hover:-translate-y-2">
            <!-- 成功指标标签 -->
            <div class="absolute -top-3 -right-3 bg-gradient-to-r from-purple-500 to-indigo-500 text-white px-3 py-1 rounded-full text-xs font-bold shadow-lg">
              维护成本 70% ↓
            </div>

            <!-- 公司信息 -->
            <div class="flex items-center mb-6">
              <div class="relative">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 via-indigo-600 to-blue-600 rounded-2xl flex items-center justify-center text-white font-bold text-xl shadow-lg">
                  阿
                </div>
                <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-blue-500 rounded-full border-2 border-white flex items-center justify-center">
                  <svg class="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                </div>
              </div>
              <div class="ml-4">
                <h4 class="font-bold text-gray-900 text-lg">阿里云</h4>
                <p class="text-gray-600 text-sm flex items-center">
                  <svg class="w-4 h-4 mr-1 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                  </svg>
                  云计算 • 全球领先
                </p>
              </div>
            </div>

            <!-- 评价内容 -->
            <blockquote class="text-gray-700 mb-6 leading-relaxed relative">
              <svg class="absolute -top-2 -left-2 w-8 h-8 text-purple-200" fill="currentColor" viewBox="0 0 32 32">
                <path d="M10 8c-3.3 0-6 2.7-6 6v10h10V14h-4c0-2.2 1.8-4 4-4V8zm12 0c-3.3 0-6 2.7-6 6v10h10V14h-4c0-2.2 1.8-4 4-4V8z"/>
              </svg>
              <span class="relative z-10">
                "我们的系统非常复杂，有<strong class="text-purple-600">数百个微服务</strong>。ArchScope帮助我们理清了服务间的依赖关系，大大降低了系统维护的复杂度，<strong class="text-green-600">运维成本降低70%</strong>。"
              </span>
            </blockquote>

            <!-- 具体成果数据 -->
            <div class="grid grid-cols-2 gap-4 mb-6 p-4 bg-gradient-to-r from-purple-50 to-indigo-50 rounded-xl">
              <div class="text-center">
                <div class="text-2xl font-bold text-purple-600">70%</div>
                <div class="text-xs text-gray-600">运维成本降低</div>
              </div>
              <div class="text-center">
                <div class="text-2xl font-bold text-blue-600">500+</div>
                <div class="text-xs text-gray-600">微服务管理</div>
              </div>
            </div>

            <!-- 推荐人信息 -->
            <div class="flex items-center">
              <img src="https://images.unsplash.com/photo-1580489944761-15a19d654956?w=48&h=48&fit=crop&crop=face&auto=format&q=80"
                   alt="王芳" class="w-12 h-12 rounded-full border-2 border-white shadow-md">
              <div class="ml-3">
                <p class="font-semibold text-gray-900">王芳</p>
                <p class="text-gray-600 text-sm flex items-center">
                  <svg class="w-3 h-3 mr-1 text-orange-500" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                  </svg>
                  首席架构师 • 云原生专家
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 信任指标和社会证明 -->
        <div class="bg-white/60 backdrop-blur-sm rounded-3xl p-8 border border-white/50">
          <div class="text-center mb-8">
            <h3 class="text-2xl font-bold text-gray-900 mb-4">受到全球领先企业信赖</h3>
            <p class="text-gray-600">已为 <span class="text-blue-600 font-semibold">1000+</span> 家企业提供架构治理服务</p>
          </div>

          <!-- 企业Logo展示 -->
          <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-8 items-center opacity-60 hover:opacity-100 transition-opacity duration-300 mb-12">
            <div class="flex items-center justify-center h-12">
              <div class="text-lg font-bold text-gray-400">招商银行</div>
            </div>
            <div class="flex items-center justify-center h-12">
              <div class="text-lg font-bold text-gray-400">字节跳动</div>
            </div>
            <div class="flex items-center justify-center h-12">
              <div class="text-lg font-bold text-gray-400">阿里云</div>
            </div>
            <div class="flex items-center justify-center h-12">
              <div class="text-lg font-bold text-gray-400">腾讯云</div>
            </div>
            <div class="flex items-center justify-center h-12">
              <div class="text-lg font-bold text-gray-400">华为云</div>
            </div>
            <div class="flex items-center justify-center h-12">
              <div class="text-lg font-bold text-gray-400">百度智能云</div>
            </div>
          </div>

          <!-- 关键统计数据 -->
          <div class="grid grid-cols-1 md:grid-cols-4 gap-8 pt-8 border-t border-gray-200">
            <div class="text-center">
              <div class="text-3xl font-bold text-blue-600 mb-2">1000+</div>
              <div class="text-gray-600 text-sm">企业客户</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-green-600 mb-2">85%</div>
              <div class="text-gray-600 text-sm">平均效率提升</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-purple-600 mb-2">99.9%</div>
              <div class="text-gray-600 text-sm">服务可用性</div>
            </div>
            <div class="text-center">
              <div class="text-3xl font-bold text-orange-600 mb-2">24/7</div>
              <div class="text-gray-600 text-sm">技术支持</div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- FAQ部分 -->
    <section id="faq" class="relative py-20 bg-white">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
          <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            常见问题
          </h2>
          <p class="text-lg text-gray-600">快速找到您关心问题的答案</p>
        </div>

        <div class="space-y-4">
          <div
            v-for="(item, index) in faqItems"
            :key="index"
            class="bg-white rounded-lg shadow-sm border border-gray-200"
          >
            <button
              @click="toggleFaq(index)"
              class="w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-200"
            >
              <span class="font-medium text-gray-900">{{ item.question }}</span>
              <svg
                class="w-5 h-5 text-gray-500 transform transition-transform duration-200"
                :class="item.isOpen ? 'rotate-180' : ''"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </button>
            <div v-show="item.isOpen" class="px-6 pb-4">
              <p class="text-gray-600 leading-relaxed">{{ item.answer }}</p>
            </div>
          </div>
        </div>

        <div class="text-center mt-12">
          <p class="text-gray-600 mb-4">还有其他问题？</p>
          <button class="text-primary-600 hover:text-primary-700 font-medium">
            联系我们的技术支持 →
          </button>
        </div>
      </div>
    </section>

    <!-- 底部CTA -->
    <section
      class="relative py-20 bg-gradient-to-r from-primary-600 to-secondary-600 overflow-hidden"
    >
      <!-- 背景装饰 -->
      <div class="absolute inset-0">
        <div
          class="absolute top-0 left-0 w-full h-full bg-black opacity-10"
        ></div>
        <div
          class="absolute -top-20 -right-20 w-40 h-40 bg-white opacity-10 rounded-full animate-pulse"
        ></div>
        <div
          class="absolute -bottom-20 -left-20 w-60 h-60 bg-white opacity-5 rounded-full animate-pulse"
          style="animation-delay: 1s"
        ></div>
      </div>

      <div class="relative max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-6">
          准备好开始您的架构之旅了吗？
        </h2>
        <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
          加入数百个团队的行列，让AI为您的项目架构保驾护航
        </p>

        <div
          class="flex flex-col sm:flex-row gap-4 justify-center items-center"
        >
          <button
            @click="navigateToProjects"
            class="group relative px-8 py-4 bg-white text-primary-600 font-semibold rounded-xl shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-300 overflow-hidden"
          >
            <span class="relative z-10">免费开始使用</span>
            <div
              class="absolute inset-0 bg-gray-50 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"
            ></div>
          </button>
          <button
            class="px-8 py-4 border-2 border-white text-white font-semibold rounded-xl hover:bg-white hover:text-primary-600 transform hover:scale-105 transition-all duration-300"
          >
            查看演示
          </button>
        </div>

        <div class="mt-8 text-white/80 text-sm">
          无需信用卡 • 5分钟快速上手 • 专业技术支持
        </div>
      </div>
    </section>

    <!-- 页脚 -->
    <LandingFooter />
  </div>
</template>

<template>
  <MainLayout>
    <div class="container mx-auto p-6">
      <h1 class="text-3xl font-bold text-gray-800 mb-6">项目卡片布局测试</h1>
      
      <!-- 测试按钮 -->
      <div class="mb-6 flex space-x-4">
        <button @click="generateTestData" class="btn-primary bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
          生成测试数据
        </button>
        <button @click="clearTestData" class="btn-secondary bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg">
          清除数据
        </button>
      </div>

      <!-- 项目卡片网格 -->
      <div class="project-cards-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        <!-- 项目卡片 -->
        <div
          v-for="project in testProjects"
          :key="project.id"
          class="project-card content-card cursor-pointer transform transition-all duration-200 hover:-translate-y-1 hover:shadow-lg"
        >
          <!-- 卡片头部 - 选择框和状态 -->
          <div class="flex items-start justify-between mb-4">
            <div class="flex items-center">
              <input
                type="checkbox"
                class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 mr-3"
              />
              <div class="project-status-badge">
                <span 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getStatusBadgeClass(project.status)"
                >
                  <i :class="getStatusIcon(project.status)" class="mr-1"></i>
                  {{ getStatusText(project.status) }}
                </span>
              </div>
            </div>
            <div class="project-icon">
              <div class="flex-shrink-0 h-12 w-12 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl flex items-center justify-center shadow-md">
                <i class="fas fa-project-diagram text-white text-lg"></i>
              </div>
            </div>
          </div>

          <!-- 项目标题和描述 -->
          <div class="mb-4">
            <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-1" :title="project.name">
              {{ project.name }}
            </h3>
            <p class="text-sm text-gray-600 line-clamp-2 mb-3" :title="project.description">
              {{ project.description || "暂无项目描述" }}
            </p>
            
            <!-- 仓库地址 -->
            <div class="flex items-center text-xs text-gray-500 mb-3">
              <i class="fab fa-git-alt mr-1"></i>
              <a 
                :href="project.repositoryUrl" 
                target="_blank" 
                class="hover:text-indigo-600 truncate max-w-full"
                :title="project.repositoryUrl"
              >
                {{ project.repositoryUrl }}
              </a>
            </div>
          </div>

          <!-- 评分和时间信息 -->
          <div class="flex items-center justify-between mb-4">
            <div class="star-rating">
              <div class="flex items-center">
                <i
                  v-for="n in 5"
                  :key="n"
                  :class="[
                    'fa-star text-sm mr-0.5',
                    n <= Math.round(project.rating || 0)
                      ? 'fas star-filled text-yellow-400'
                      : 'far star-empty text-gray-300',
                  ]"
                ></i>
                <span class="ml-2 text-sm text-gray-600 font-medium">{{ (project.rating || 0).toFixed(1) }}</span>
              </div>
            </div>
            <div class="text-xs text-gray-500">
              <i class="far fa-clock mr-1"></i>
              {{ formatDate(project.updatedAt) }}
            </div>
          </div>

          <!-- 创建时间 -->
          <div class="text-xs text-gray-400 mb-4">
            <i class="far fa-calendar-plus mr-1"></i>
            创建于 {{ formatDate(project.createdAt) }}
          </div>

          <!-- 操作按钮 -->
          <div class="flex items-center justify-between pt-4 border-t border-gray-100">
            <div class="flex items-center space-x-2">
              <button class="text-indigo-600 hover:text-indigo-800 text-sm">
                <i class="fas fa-info-circle mr-1"></i>详情
              </button>
              <button class="text-green-600 hover:text-green-800 text-sm">
                <i class="fas fa-book-open mr-1"></i>文档
              </button>
            </div>
            <button class="text-gray-600 hover:text-gray-800 text-sm">
              <i class="fas fa-code-branch mr-1"></i>仓库
            </button>
          </div>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import MainLayout from '@/layouts/MainLayout.vue';

interface TestProject {
  id: number;
  name: string;
  description: string;
  repositoryUrl: string;
  status: string;
  rating: number;
  createdAt: string;
  updatedAt: string;
}

const testProjects = ref<TestProject[]>([]);

// 生成测试数据
const generateTestData = () => {
  testProjects.value = [
    {
      id: 1,
      name: "电商平台后端服务",
      description: "基于Spring Boot的微服务架构电商平台，包含用户管理、商品管理、订单处理等核心功能模块。",
      repositoryUrl: "https://github.com/example/ecommerce-backend",
      status: "ANALYSIS_COMPLETED",
      rating: 4.5,
      createdAt: "2024-01-15T10:30:00Z",
      updatedAt: "2024-01-20T15:45:00Z"
    },
    {
      id: 2,
      name: "React前端应用",
      description: "现代化的React前端应用，使用TypeScript和Tailwind CSS构建，支持响应式设计。",
      repositoryUrl: "https://github.com/example/react-frontend",
      status: "ANALYZING",
      rating: 4.2,
      createdAt: "2024-01-10T09:15:00Z",
      updatedAt: "2024-01-19T11:20:00Z"
    },
    {
      id: 3,
      name: "数据分析工具",
      description: "Python数据分析工具集，包含数据清洗、可视化和机器学习模型训练功能。",
      repositoryUrl: "https://github.com/example/data-analytics",
      status: "PENDING_ANALYSIS",
      rating: 3.8,
      createdAt: "2024-01-05T14:20:00Z",
      updatedAt: "2024-01-18T16:30:00Z"
    },
    {
      id: 4,
      name: "移动端App",
      description: "Flutter跨平台移动应用，支持iOS和Android，提供完整的用户体验。",
      repositoryUrl: "https://github.com/example/mobile-app",
      status: "FAILED",
      rating: 4.0,
      createdAt: "2024-01-01T08:00:00Z",
      updatedAt: "2024-01-17T12:15:00Z"
    }
  ];
};

// 清除测试数据
const clearTestData = () => {
  testProjects.value = [];
};

// 状态相关方法
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case 'ANALYSIS_COMPLETED':
      return 'bg-green-100 text-green-800';
    case 'ANALYZING':
      return 'bg-blue-100 text-blue-800';
    case 'PENDING_ANALYSIS':
      return 'bg-yellow-100 text-yellow-800';
    case 'FAILED':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'ANALYSIS_COMPLETED':
      return 'fas fa-check-circle';
    case 'ANALYZING':
      return 'fas fa-spinner fa-spin';
    case 'PENDING_ANALYSIS':
      return 'fas fa-clock';
    case 'FAILED':
      return 'fas fa-exclamation-triangle';
    default:
      return 'fas fa-question-circle';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case 'ANALYSIS_COMPLETED':
      return '分析完成';
    case 'ANALYZING':
      return '智能解析中';
    case 'PENDING_ANALYSIS':
      return '等待分析';
    case 'FAILED':
      return '分析失败';
    default:
      return '未知状态';
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};
</script>

<style scoped>
/* 项目卡片网格布局 */
.project-cards-grid {
  gap: 1.5rem;
}

/* 项目卡片样式 */
.project-card {
  position: relative;
  padding: 1.5rem;
  border-radius: 0.75rem;
  background: white;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: #d1d5db;
}

/* 文本截断样式 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 星级评分样式 */
.star-rating .star-filled {
  color: #fbbf24;
}

.star-rating .star-empty {
  color: #d1d5db;
}

/* 响应式网格布局 */
@media (max-width: 640px) {
  .project-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .project-card {
    padding: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .project-cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) and (max-width: 1279px) {
  .project-cards-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .project-cards-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>

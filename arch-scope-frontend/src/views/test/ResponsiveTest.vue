<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 响应式导航栏测试 -->
    <nav class="bg-gray-800 shadow-lg">
      <div class="container mx-auto px-4">
        <div class="flex justify-between items-center h-16">
          <div class="flex items-center">
            <div class="bg-gray-800 rounded-full p-1 flex items-center justify-center mr-3">
              <img src="@/assets/logo.png" alt="ArchScope" class="h-8 w-8" />
            </div>
            <span class="text-white text-xl font-bold hidden sm:block">ArchScope</span>
          </div>
          
          <!-- 桌面端导航 -->
          <div class="hidden md:flex space-x-4">
            <a href="#" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">项目列表</a>
            <a href="#" class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200">任务队列</a>
            <a href="#" class="bg-indigo-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors duration-200">注册项目</a>
          </div>
          
          <!-- 移动端菜单按钮 -->
          <div class="md:hidden">
            <button @click="mobileMenuOpen = !mobileMenuOpen" class="text-gray-300 hover:text-white p-2">
              <i class="fas fa-bars"></i>
            </button>
          </div>
        </div>
        
        <!-- 移动端菜单 -->
        <div v-show="mobileMenuOpen" class="md:hidden bg-gray-700 rounded-lg mt-2 p-4 animate-fade-in">
          <a href="#" class="block text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium mb-2 transition-colors duration-200">项目列表</a>
          <a href="#" class="block text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium mb-2 transition-colors duration-200">任务队列</a>
          <a href="#" class="block bg-indigo-600 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-indigo-700 transition-colors duration-200">注册项目</a>
        </div>
      </div>
    </nav>

    <div class="container mx-auto p-4 lg:p-8">
      <h1 class="text-2xl lg:text-4xl font-bold text-gray-800 mb-8 text-center animate-fade-in">响应式设计测试</h1>
      
      <!-- 网格布局测试 -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
        <div v-for="i in 6" :key="i" class="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 animate-slide-up" :style="{ animationDelay: `${i * 100}ms` }">
          <div class="flex items-center mb-4">
            <div class="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mr-4">
              <i class="fas fa-project-diagram text-white"></i>
            </div>
            <div>
              <h3 class="text-lg font-semibold text-gray-800">项目 {{ i }}</h3>
              <p class="text-sm text-gray-500">示例项目描述</p>
            </div>
          </div>
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">状态:</span>
              <span class="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">已完成</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">进度:</span>
              <span class="text-gray-800">100%</span>
            </div>
          </div>
          <div class="mt-4 w-full bg-gray-200 rounded-full h-2">
            <div class="bg-gradient-to-r from-green-400 to-blue-500 h-2 rounded-full animate-progress" style="width: 100%"></div>
          </div>
        </div>
      </div>
      
      <!-- 表格响应式测试 -->
      <div class="bg-white rounded-lg shadow-lg overflow-hidden mb-12 animate-fade-in">
        <div class="px-6 py-4 bg-gray-50 border-b">
          <h2 class="text-xl font-semibold text-gray-800">响应式表格</h2>
        </div>
        <div class="overflow-x-auto">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden sm:table-cell">状态</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">创建时间</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">最后更新</th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr v-for="i in 5" :key="i" class="hover:bg-gray-50 transition-colors duration-200">
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center mr-3">
                      <i class="fas fa-folder text-indigo-600 text-sm"></i>
                    </div>
                    <div>
                      <div class="text-sm font-medium text-gray-900">项目 {{ i }}</div>
                      <div class="text-sm text-gray-500 sm:hidden">已完成</div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap hidden sm:table-cell">
                  <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">已完成</span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden md:table-cell">2024-01-{{ 10 + i }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 hidden lg:table-cell">2024-01-{{ 20 + i }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button class="text-indigo-600 hover:text-indigo-900 mr-2 transition-colors duration-200">查看</button>
                  <button class="text-red-600 hover:text-red-900 transition-colors duration-200">删除</button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      
      <!-- 表单响应式测试 -->
      <div class="bg-white rounded-lg shadow-lg p-6 animate-fade-in">
        <h2 class="text-xl font-semibold text-gray-800 mb-6">响应式表单</h2>
        <form class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">项目名称</label>
              <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200" placeholder="输入项目名称">
            </div>
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-2">项目类型</label>
              <select class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200">
                <option>Web应用</option>
                <option>移动应用</option>
                <option>桌面应用</option>
              </select>
            </div>
          </div>
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">项目描述</label>
            <textarea rows="4" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200" placeholder="输入项目描述"></textarea>
          </div>
          <div class="flex flex-col sm:flex-row gap-4">
            <button type="submit" class="flex-1 bg-indigo-600 text-white py-2 px-4 rounded-md hover:bg-indigo-700 transition-colors duration-200 transform hover:scale-105">提交</button>
            <button type="button" class="flex-1 bg-gray-300 text-gray-700 py-2 px-4 rounded-md hover:bg-gray-400 transition-colors duration-200">取消</button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

const mobileMenuOpen = ref(false)
</script>

<style scoped>
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progress {
  from {
    width: 0%;
  }
  to {
    width: 100%;
  }
}

.animate-fade-in {
  animation: fade-in 0.6s ease-out;
}

.animate-slide-up {
  animation: slide-up 0.6s ease-out both;
}

.animate-progress {
  animation: progress 2s ease-out;
}
</style>

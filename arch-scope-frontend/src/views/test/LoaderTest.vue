<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">项目注册加载器测试</h1>
      
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">控制面板</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              当前步骤
            </label>
            <select 
              v-model="currentStepIndex" 
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option :value="0">步骤 1: 验证仓库</option>
              <option :value="1">步骤 2: 解析分支</option>
              <option :value="2">步骤 3: 获取元数据</option>
              <option :value="3">步骤 4: 创建项目</option>
            </select>
          </div>
          
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">
              超时时间（秒）
            </label>
            <input 
              v-model.number="timeoutSeconds" 
              type="number" 
              min="10" 
              max="60"
              class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
          </div>
        </div>
        
        <div class="flex items-center gap-4 mb-4">
          <label class="flex items-center">
            <input 
              v-model="showCancelButton" 
              type="checkbox" 
              class="mr-2"
            >
            显示取消按钮
          </label>
          
          <label class="flex items-center">
            <input 
              v-model="canCancel" 
              type="checkbox" 
              class="mr-2"
            >
            允许取消
          </label>
        </div>
        
        <div class="flex gap-4">
          <button 
            @click="showLoader" 
            class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
            :disabled="isVisible"
          >
            显示加载器
          </button>
          
          <button 
            @click="hideLoader" 
            class="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            :disabled="!isVisible"
          >
            隐藏加载器
          </button>
          
          <button 
            @click="nextStep" 
            class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            :disabled="!isVisible || currentStepIndex >= 3"
          >
            下一步
          </button>
          
          <button 
            @click="simulateComplete" 
            class="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors"
            :disabled="!isVisible"
          >
            模拟完成
          </button>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-md p-6">
        <h2 class="text-xl font-semibold mb-4">事件日志</h2>
        <div class="bg-gray-50 rounded-md p-4 h-64 overflow-y-auto">
          <div v-if="eventLog.length === 0" class="text-gray-500 text-center">
            暂无事件
          </div>
          <div 
            v-for="(event, index) in eventLog" 
            :key="index"
            class="mb-2 text-sm"
            :class="{
              'text-blue-600': event.type === 'info',
              'text-red-600': event.type === 'error',
              'text-green-600': event.type === 'success',
              'text-yellow-600': event.type === 'warning'
            }"
          >
            <span class="font-mono text-xs text-gray-500">{{ event.timestamp }}</span>
            <span class="ml-2">{{ event.message }}</span>
          </div>
        </div>
        
        <button 
          @click="clearLog" 
          class="mt-4 px-3 py-1 bg-gray-500 text-white text-sm rounded hover:bg-gray-600 transition-colors"
        >
          清空日志
        </button>
      </div>
    </div>
    
    <!-- 项目注册加载器 -->
    <ProjectRegistrationLoader
      :is-visible="isVisible"
      :current-step-index="currentStepIndex"
      :show-cancel-button="showCancelButton"
      :can-cancel="canCancel"
      :timeout-seconds="timeoutSeconds"
      @cancel="handleCancel"
      @timeout="handleTimeout"
    />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import ProjectRegistrationLoader from '@/components/ProjectRegistrationLoader.vue'

// 响应式状态
const isVisible = ref(false)
const currentStepIndex = ref(0)
const showCancelButton = ref(true)
const canCancel = ref(true)
const timeoutSeconds = ref(30)

// 事件日志
interface LogEvent {
  timestamp: string
  type: 'info' | 'error' | 'success' | 'warning'
  message: string
}

const eventLog = ref<LogEvent[]>([])

// 添加日志事件
const addLogEvent = (type: LogEvent['type'], message: string) => {
  const timestamp = new Date().toLocaleTimeString()
  eventLog.value.unshift({
    timestamp,
    type,
    message
  })
  
  // 限制日志条数
  if (eventLog.value.length > 50) {
    eventLog.value = eventLog.value.slice(0, 50)
  }
}

// 显示加载器
const showLoader = () => {
  isVisible.value = true
  currentStepIndex.value = 0
  addLogEvent('info', '显示项目注册加载器')
}

// 隐藏加载器
const hideLoader = () => {
  isVisible.value = false
  addLogEvent('info', '隐藏项目注册加载器')
}

// 下一步
const nextStep = () => {
  if (currentStepIndex.value < 3) {
    currentStepIndex.value++
    addLogEvent('info', `进入步骤 ${currentStepIndex.value + 1}`)
  }
}

// 模拟完成
const simulateComplete = () => {
  currentStepIndex.value = 4
  setTimeout(() => {
    hideLoader()
    addLogEvent('success', '项目注册完成')
  }, 1000)
}

// 处理取消
const handleCancel = () => {
  addLogEvent('warning', '用户取消了项目注册')
  hideLoader()
}

// 处理超时
const handleTimeout = () => {
  addLogEvent('error', '项目注册操作超时')
  hideLoader()
}

// 清空日志
const clearLog = () => {
  eventLog.value = []
  addLogEvent('info', '日志已清空')
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 模拟项目文档页面的完整布局 -->
    <div class="flex w-full">
      <!-- 左侧导航栏 - 按照界面原型设计 -->
      <div class="sidebar fixed h-screen bg-gray-900 text-gray-300 flex flex-col shadow-lg w-72">
        <!-- 紧凑的项目导航栏 -->
        <div class="px-4 py-3 border-b border-gray-700 bg-gray-800">
          <div class="flex items-center justify-between">
            <router-link
              to="/projects"
              class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center"
              title="返回项目主页"
            >
              <i class="fas fa-arrow-circle-left"></i>
            </router-link>
            <div class="relative inline-block text-left flex-grow mx-2">
              <select class="form-select block w-full py-1 px-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none">
                <option>ArchScope 示例项目</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 项目标题 -->
        <div class="p-4 border-b border-gray-700">
          <h1 class="text-xl font-bold text-white truncate">ArchScope 示例项目 文档</h1>
        </div>

        <!-- 文档导航 -->
        <nav class="flex-grow p-6 overflow-y-auto">
          <ul class="space-y-2">
            <li>
              <a href="#" class="flex items-center justify-between text-white bg-gray-700 px-4 py-2 rounded-md transition duration-200 active-link">
                <span class="flex items-center">
                  <i class="fas fa-sitemap w-5 mr-3"></i>
                  <span>架构设计</span>
                </span>
              </a>
            </li>
            <li>
              <a href="#" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                <span class="flex items-center">
                  <i class="fas fa-home w-5 mr-3"></i>
                  <span>产品简介</span>
                </span>
              </a>
            </li>
            <li>
              <a href="#" class="flex items-center justify-between text-gray-300 hover:text-white hover:bg-gray-700 px-4 py-2 rounded-md transition duration-200">
                <span class="flex items-center">
                  <i class="fas fa-file-code w-5 mr-3"></i>
                  <span>接口文档</span>
                </span>
              </a>
            </li>
          </ul>
        </nav>

        <!-- 版本选择和比较 -->
        <div class="p-4 border-t border-gray-700 mt-auto">
          <div class="flex items-center mb-3">
            <label for="version-select" class="text-sm font-medium text-gray-300 mr-2">版本:</label>
            <select id="version-select" class="flex-1 text-sm border-gray-600 bg-gray-800 text-gray-300 rounded focus:border-blue-500 focus:ring-blue-500">
              <option>v1.0.0</option>
              <option>v0.9.0</option>
              <option>v0.8.0</option>
            </select>
          </div>
          <button class="w-full text-sm bg-gray-700 hover:bg-gray-600 text-gray-300 px-3 py-2 rounded transition duration-200 flex items-center justify-center">
            <i class="fas fa-code-branch mr-2"></i> 版本对比
          </button>
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="content flex-grow py-8 px-8 ml-72 max-w-7xl mx-auto">
        <div class="content-card bg-white shadow-xl rounded-lg p-8 prose max-w-none">
          <DocumentContent :content="architectureContent" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DocumentContent from '@/components/DocumentContent.vue'

// 模拟架构文档内容，完全按照原型样式
const architectureContent = `
<h1 class="text-3xl font-bold text-gray-800 mb-6 pb-2 border-b border-gray-200">架构概览 Architecture Overview</h1>

<p class="text-lg"><code class="bg-indigo-50 text-indigo-600 px-2 py-1 rounded">架构鹰眼 ArchScope</code> 系统采用模块化的前后端分离架构，遵循领域驱动设计 (DDD) 原则，并通过六边形架构模式实现业务逻辑与技术细节的解耦。</p>

<h2 id="系统分层-ddd" class="text-2xl font-semibold text-gray-800 mt-8 mb-4">系统分层 (DDD)</h2>
<p class="mb-4">系统后端按照 DDD 划分为以下核心层：</p>
<div class="bg-gray-50 rounded-lg p-5 border border-gray-200 mb-6">
  <ul class="space-y-4">
    <li class="flex items-center">
      <div class="flex-shrink-0 h-10 w-10 rounded-md bg-blue-100 flex items-center justify-center mr-4">
        <i class="fas fa-layer-group text-blue-600"></i>
      </div>
      <div>
        <h4 class="text-lg font-medium text-gray-900 mb-1">应用服务层 (Application)</h4>
        <p class="text-gray-600 mb-0">协调领域对象完成业务用例，对外提供服务接口。</p>
      </div>
    </li>
    <li class="flex items-center">
      <div class="flex-shrink-0 h-10 w-10 rounded-md bg-indigo-100 flex items-center justify-center mr-4">
        <i class="fas fa-cube text-indigo-600"></i>
      </div>
      <div>
        <h4 class="text-lg font-medium text-gray-900 mb-1">领域模型层 (Domain)</h4>
        <p class="text-gray-600 mb-0">包含实体、值对象、聚合根、领域服务和仓储接口，是业务逻辑的核心。</p>
      </div>
    </li>
    <li class="flex items-center">
      <div class="flex-shrink-0 h-10 w-10 rounded-md bg-green-100 flex items-center justify-center mr-4">
        <i class="fas fa-database text-green-600"></i>
      </div>
      <div>
        <h4 class="text-lg font-medium text-gray-900 mb-1">基础设施层 (Infrastructure)</h4>
        <p class="text-gray-600 mb-0">实现仓储接口、集成外部服务、提供技术支撑。</p>
      </div>
    </li>
  </ul>
</div>

<h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">系统架构图</h2>
<p class="mb-4">以下是系统的整体架构图，展示了各个组件之间的关系：</p>

<pre><code class="language-mermaid">
graph TB
    subgraph "前端层"
        A[Vue.js 应用] --> B[路由管理]
        B --> C[状态管理]
        C --> D[组件库]
    end
    
    subgraph "后端层"
        E[API 网关] --> F[应用服务]
        F --> G[领域服务]
        G --> H[仓储层]
    end
    
    subgraph "数据层"
        I[MySQL 数据库]
        J[Redis 缓存]
        K[文件存储]
    end
    
    A --> E
    H --> I
    H --> J
    H --> K
    
    style A fill:#e1f5fe
    style E fill:#f3e5f5
    style G fill:#e8f5e8
    style I fill:#fff3e0
</code></pre>

<h2 class="text-2xl font-semibold text-gray-800 mt-8 mb-4">技术栈</h2>
<p class="mb-4">项目采用现代化的技术栈，确保系统的可维护性和扩展性：</p>

<div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
  <div class="bg-blue-50 rounded-lg p-4 border border-blue-200">
    <h4 class="text-lg font-semibold text-blue-800 mb-2">前端技术</h4>
    <ul class="text-blue-700 space-y-1">
      <li>• Vue 3 + TypeScript</li>
      <li>• Tailwind CSS</li>
      <li>• Pinia 状态管理</li>
      <li>• Vue Router</li>
    </ul>
  </div>
  <div class="bg-green-50 rounded-lg p-4 border border-green-200">
    <h4 class="text-lg font-semibold text-green-800 mb-2">后端技术</h4>
    <ul class="text-green-700 space-y-1">
      <li>• Spring Boot</li>
      <li>• MySQL 数据库</li>
      <li>• Redis 缓存</li>
      <li>• RocketMQ 消息队列</li>
    </ul>
  </div>
</div>
`
</script>

<style scoped>
/* 按照界面原型设计的样式 */
:root {
  --sidebar-width: 18rem;
  --primary-color: #4F46E5; /* Indigo-600 */
  --primary-hover: #4338CA; /* Indigo-700 */
  --sidebar-bg: #1E293B; /* Slate-800 */
  --sidebar-header-bg: #0F172A; /* Slate-900 */
  --sidebar-item-hover: #334155; /* Slate-700 */
  --sidebar-active: #3B82F6; /* Blue-500 */
}

.sidebar {
  width: 18rem; /* 固定宽度，与原型一致 */
  background-color: #1E293B; /* Slate-800 */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.content {
  margin-left: 18rem; /* 与侧边栏宽度一致 */
}

/* 侧边栏导航样式 */
.active-link {
  background-color: #334155 !important; /* Slate-700 */
  color: white !important;
}

.content-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 自定义表单样式 */
.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1F2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #4F46E5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}
</style>

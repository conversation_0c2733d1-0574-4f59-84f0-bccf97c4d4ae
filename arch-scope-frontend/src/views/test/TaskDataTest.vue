<template>
  <div class="container mx-auto p-6">
    <h1 class="text-2xl font-bold mb-6">任务数据测试页面</h1>
    
    <!-- API响应原始数据 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">API响应原始数据</h2>
      <button @click="fetchRawData" class="bg-blue-500 text-white px-4 py-2 rounded mb-4">
        获取原始数据
      </button>
      <pre v-if="rawData" class="bg-gray-100 p-4 rounded overflow-auto text-sm">{{ JSON.stringify(rawData, null, 2) }}</pre>
    </div>

    <!-- 解析后的任务数据 -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">解析后的任务数据</h2>
      <button @click="fetchParsedData" class="bg-green-500 text-white px-4 py-2 rounded mb-4">
        获取解析数据
      </button>
      <div v-if="parsedTasks.length > 0">
        <p class="mb-2">任务数量: {{ parsedTasks.length }}</p>
        <div class="grid gap-4">
          <div v-for="task in parsedTasks" :key="task.id" class="border p-4 rounded">
            <h3 class="font-semibold">任务 #{{ task.id }}</h3>
            <p><strong>名称:</strong> {{ task.name }}</p>
            <p><strong>类型:</strong> {{ task.type }}</p>
            <p><strong>状态:</strong> {{ task.status }}</p>
            <p><strong>项目ID:</strong> {{ task.projectId }}</p>
            <p><strong>创建时间:</strong> {{ task.createdAt }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 错误信息 -->
    <div v-if="error" class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
      <strong>错误:</strong> {{ error }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';
import { taskAPI, type Task } from '@/utils/api';

const rawData = ref<any>(null);
const parsedTasks = ref<Task[]>([]);
const error = ref<string | null>(null);

const fetchRawData = async () => {
  try {
    error.value = null;
    const response = await taskAPI.getTasks();
    rawData.value = response;
    console.log('原始API响应:', response);
  } catch (err: any) {
    error.value = `获取原始数据失败: ${err.message}`;
    console.error('获取原始数据失败:', err);
  }
};

const fetchParsedData = async () => {
  try {
    error.value = null;
    const response = await taskAPI.getTasks();
    
    // 根据修复后的逻辑解析数据
    parsedTasks.value = response.content || [];
    
    console.log('解析后的任务数据:', parsedTasks.value);
    console.log('任务数量:', parsedTasks.value.length);
  } catch (err: any) {
    error.value = `获取解析数据失败: ${err.message}`;
    console.error('获取解析数据失败:', err);
  }
};
</script>

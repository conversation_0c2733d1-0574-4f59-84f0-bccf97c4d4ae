<template>
  <div class="container mx-auto p-6">
    <h1 class="text-3xl font-bold mb-6">Mermaid图表渲染测试</h1>
    
    <div class="space-y-8">
      <!-- 流程图测试 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">流程图测试</h2>
        <DocumentContent :content="flowchartContent" />
      </div>
      
      <!-- 序列图测试 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">序列图测试</h2>
        <DocumentContent :content="sequenceContent" />
      </div>
      
      <!-- 甘特图测试 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">甘特图测试</h2>
        <DocumentContent :content="ganttContent" />
      </div>
      
      <!-- 类图测试 -->
      <div class="bg-white rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4">类图测试</h2>
        <DocumentContent :content="classContent" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import DocumentContent from '@/components/DocumentContent.vue'

// 流程图内容
const flowchartContent = `
<h3>系统架构流程图</h3>
<pre><code class="language-mermaid">
graph TD
    A[用户请求] --> B[API网关]
    B --> C{认证检查}
    C -->|通过| D[业务服务]
    C -->|失败| E[返回错误]
    D --> F[数据库]
    F --> G[返回结果]
    G --> H[响应用户]
    
    style A fill:#e1f5fe
    style B fill:#f3e5f5
    style D fill:#e8f5e8
    style F fill:#fff3e0
</code></pre>
`

// 序列图内容
const sequenceContent = `
<h3>用户登录序列图</h3>
<pre><code class="language-mermaid">
sequenceDiagram
    participant U as 用户
    participant F as 前端
    participant A as API网关
    participant S as 认证服务
    participant D as 数据库
    
    U->>F: 输入用户名密码
    F->>A: 发送登录请求
    A->>S: 验证用户信息
    S->>D: 查询用户数据
    D-->>S: 返回用户信息
    S-->>A: 返回认证结果
    A-->>F: 返回JWT令牌
    F-->>U: 登录成功
</code></pre>
`

// 甘特图内容
const ganttContent = `
<h3>项目开发计划</h3>
<pre><code class="language-mermaid">
gantt
    title ArchScope开发计划
    dateFormat  YYYY-MM-DD
    section 需求分析
    需求调研           :done,    des1, 2024-01-01,2024-01-15
    原型设计           :done,    des2, 2024-01-10,2024-01-25
    section 开发阶段
    后端开发           :active,  dev1, 2024-01-20,2024-03-01
    前端开发           :         dev2, 2024-02-01,2024-03-15
    集成测试           :         test, 2024-03-10,2024-03-25
    section 部署上线
    生产部署           :         deploy, 2024-03-20,2024-03-30
</code></pre>
`

// 类图内容
const classContent = `
<h3>系统类图</h3>
<pre><code class="language-mermaid">
classDiagram
    class Project {
        +Long id
        +String name
        +String description
        +String repositoryUrl
        +ProjectStatus status
        +Date createdAt
        +Date updatedAt
        +analyze()
        +generateDocs()
    }
    
    class Task {
        +Long id
        +String name
        +TaskType type
        +TaskStatus status
        +String result
        +Date createdAt
        +execute()
        +retry()
    }
    
    class Document {
        +Long id
        +DocumentType type
        +String content
        +String version
        +Date generatedAt
        +render()
    }
    
    Project ||--o{ Task : creates
    Project ||--o{ Document : generates
    Task --> Document : produces
    
    class ProjectStatus {
        <<enumeration>>
        PENDING
        ANALYZING
        COMPLETED
        FAILED
    }
    
    class TaskStatus {
        <<enumeration>>
        PENDING
        PROCESSING
        COMPLETED
        FAILED
    }
</code></pre>
`
</script>

<style scoped>
.container {
  max-width: 1200px;
}
</style>

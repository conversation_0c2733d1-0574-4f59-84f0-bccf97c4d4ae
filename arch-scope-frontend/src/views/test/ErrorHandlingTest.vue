<template>
  <div class="min-h-screen bg-gray-50 p-8">
    <div class="max-w-4xl mx-auto">
      <h1 class="text-3xl font-bold text-gray-800 mb-8">错误处理测试</h1>
      
      <div class="bg-white rounded-lg shadow-md p-6 mb-6">
        <h2 class="text-xl font-semibold mb-4">项目注册错误处理测试</h2>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- 测试表单 -->
          <div>
            <h3 class="text-lg font-medium mb-4">测试表单</h3>
            
            <form @submit.prevent="testProjectRegistration" class="space-y-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  项目名称
                </label>
                <input 
                  v-model="testForm.name"
                  type="text" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入项目名称"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  仓库地址
                </label>
                <input 
                  v-model="testForm.repositoryUrl"
                  type="text" 
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="输入仓库地址"
                >
              </div>
              
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">
                  项目描述
                </label>
                <textarea 
                  v-model="testForm.description"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  rows="3"
                  placeholder="输入项目描述"
                ></textarea>
              </div>
              
              <button 
                type="submit"
                class="w-full px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                :disabled="loading"
              >
                <i v-if="loading" class="fas fa-spinner fa-spin mr-2"></i>
                {{ loading ? '注册中...' : '测试项目注册' }}
              </button>
            </form>
            
            <!-- 快速测试按钮 -->
            <div class="mt-6 space-y-2">
              <h4 class="text-sm font-medium text-gray-700">快速测试场景：</h4>
              
              <button 
                @click="testDuplicateRepository"
                class="w-full px-3 py-2 bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors text-sm"
                :disabled="loading"
              >
                测试重复仓库地址
              </button>
              
              <button 
                @click="testInvalidRepository"
                class="w-full px-3 py-2 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 transition-colors text-sm"
                :disabled="loading"
              >
                测试无效仓库地址
              </button>
              
              <button 
                @click="testEmptyFields"
                class="w-full px-3 py-2 bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 transition-colors text-sm"
                :disabled="loading"
              >
                测试空字段验证
              </button>
            </div>
          </div>
          
          <!-- 测试结果 -->
          <div>
            <h3 class="text-lg font-medium mb-4">测试结果</h3>
            
            <div class="space-y-4">
              <!-- 成功消息 -->
              <div v-if="successMessage" class="p-4 bg-green-50 border border-green-200 rounded-md">
                <div class="flex items-start">
                  <i class="fas fa-check-circle text-green-500 mr-2 mt-0.5"></i>
                  <div>
                    <h4 class="text-green-800 font-medium">注册成功</h4>
                    <p class="text-green-700 text-sm mt-1">{{ successMessage }}</p>
                  </div>
                </div>
              </div>
              
              <!-- 错误消息 -->
              <div v-if="errorMessage" class="p-4 bg-red-50 border border-red-200 rounded-md">
                <div class="flex items-start">
                  <i class="fas fa-exclamation-circle text-red-500 mr-2 mt-0.5"></i>
                  <div class="flex-1">
                    <h4 class="text-red-800 font-medium">注册失败</h4>
                    <p class="text-red-700 text-sm mt-1">{{ errorMessage }}</p>
                    
                    <!-- 显示原始错误信息（用于调试） -->
                    <details v-if="originalError" class="mt-2">
                      <summary class="text-xs text-red-600 cursor-pointer">查看技术详情</summary>
                      <pre class="text-xs text-red-600 mt-1 bg-red-100 p-2 rounded overflow-auto">{{ originalError }}</pre>
                    </details>
                  </div>
                </div>
              </div>
              
              <!-- 测试历史 -->
              <div v-if="testHistory.length > 0" class="border border-gray-200 rounded-md">
                <h4 class="text-sm font-medium text-gray-700 p-3 border-b border-gray-200">测试历史</h4>
                <div class="max-h-64 overflow-y-auto">
                  <div 
                    v-for="(test, index) in testHistory" 
                    :key="index"
                    class="p-3 border-b border-gray-100 last:border-b-0"
                  >
                    <div class="flex items-center justify-between">
                      <span class="text-sm font-medium" :class="{
                        'text-green-600': test.success,
                        'text-red-600': !test.success
                      }">
                        {{ test.success ? '✅' : '❌' }} {{ test.scenario }}
                      </span>
                      <span class="text-xs text-gray-500">{{ test.timestamp }}</span>
                    </div>
                    <p class="text-xs text-gray-600 mt-1">{{ test.message }}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 错误处理说明 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h3 class="text-lg font-semibold text-blue-800 mb-3">错误处理改进说明</h3>
        <div class="text-blue-700 text-sm space-y-2">
          <p><strong>修复前：</strong>显示技术错误信息如"Request failed with status code 400"</p>
          <p><strong>修复后：</strong>显示友好的业务错误信息如"该仓库地址已经被注册"</p>
          <p><strong>改进点：</strong></p>
          <ul class="list-disc list-inside ml-4 space-y-1">
            <li>正确解析后端返回的错误响应体</li>
            <li>提取具体的业务错误信息</li>
            <li>提供用户友好的错误提示</li>
            <li>保留技术详情用于调试</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useProjectStore } from '@/stores/project'

const projectStore = useProjectStore()

// 表单数据
const testForm = ref({
  name: '',
  description: '',
  repositoryUrl: ''
})

// 状态
const loading = ref(false)
const successMessage = ref('')
const errorMessage = ref('')
const originalError = ref('')

// 测试历史
interface TestRecord {
  scenario: string
  success: boolean
  message: string
  timestamp: string
}

const testHistory = ref<TestRecord[]>([])

// 清理消息
const clearMessages = () => {
  successMessage.value = ''
  errorMessage.value = ''
  originalError.value = ''
}

// 添加测试记录
const addTestRecord = (scenario: string, success: boolean, message: string) => {
  testHistory.value.unshift({
    scenario,
    success,
    message,
    timestamp: new Date().toLocaleTimeString()
  })
  
  // 限制历史记录数量
  if (testHistory.value.length > 10) {
    testHistory.value = testHistory.value.slice(0, 10)
  }
}

// 测试项目注册
const testProjectRegistration = async () => {
  clearMessages()
  loading.value = true
  
  try {
    const result = await projectStore.registerProject(
      testForm.value.name,
      testForm.value.description,
      testForm.value.repositoryUrl,
      'main'
    )
    
    successMessage.value = `项目 "${testForm.value.name}" 注册成功！`
    addTestRecord('自定义测试', true, successMessage.value)
    
    // 清空表单
    testForm.value = { name: '', description: '', repositoryUrl: '' }
  } catch (error: any) {
    errorMessage.value = error.message || '未知错误'
    originalError.value = JSON.stringify({
      name: error.name,
      message: error.message,
      response: error.response?.data,
      status: error.response?.status
    }, null, 2)
    
    addTestRecord('自定义测试', false, errorMessage.value)
  } finally {
    loading.value = false
  }
}

// 测试重复仓库地址
const testDuplicateRepository = async () => {
  clearMessages()
  loading.value = true
  
  try {
    await projectStore.registerProject(
      '重复测试项目',
      '这是一个用于测试重复仓库地址的项目',
      'https://github.com/test/duplicate-repo.git',
      'main'
    )
    
    successMessage.value = '项目注册成功（这可能表示后端没有重复检查）'
    addTestRecord('重复仓库地址', true, successMessage.value)
  } catch (error: any) {
    errorMessage.value = error.message || '未知错误'
    originalError.value = JSON.stringify(error, null, 2)
    addTestRecord('重复仓库地址', false, errorMessage.value)
  } finally {
    loading.value = false
  }
}

// 测试无效仓库地址
const testInvalidRepository = async () => {
  clearMessages()
  loading.value = true
  
  try {
    await projectStore.registerProject(
      '无效仓库测试',
      '这是一个用于测试无效仓库地址的项目',
      'invalid-repository-url',
      'main'
    )
    
    successMessage.value = '项目注册成功（这可能表示后端没有URL验证）'
    addTestRecord('无效仓库地址', true, successMessage.value)
  } catch (error: any) {
    errorMessage.value = error.message || '未知错误'
    originalError.value = JSON.stringify(error, null, 2)
    addTestRecord('无效仓库地址', false, errorMessage.value)
  } finally {
    loading.value = false
  }
}

// 测试空字段
const testEmptyFields = async () => {
  clearMessages()
  loading.value = true
  
  try {
    await projectStore.registerProject(
      '',
      '',
      '',
      'main'
    )
    
    successMessage.value = '项目注册成功（这可能表示后端没有字段验证）'
    addTestRecord('空字段验证', true, successMessage.value)
  } catch (error: any) {
    errorMessage.value = error.message || '未知错误'
    originalError.value = JSON.stringify(error, null, 2)
    addTestRecord('空字段验证', false, errorMessage.value)
  } finally {
    loading.value = false
  }
}
</script>

<template>
  <div class="bg-gray-50 font-sans flex min-h-screen">
    <!-- 左侧导航栏 -->
    <div class="sidebar fixed h-screen text-gray-300 flex flex-col shadow-xl">
      <!-- 项目信息区域 -->
      <div class="sidebar-header px-4 py-4 border-b border-gray-700">
        <!-- 项目导航和项目名在同一行 -->
        <div class="flex items-center justify-between h-12">
          <h1 class="text-lg font-bold text-white truncate flex-1 mr-3">{{ project.name }}</h1>
          <router-link
            :to="`/projects/${projectId}`"
            class="text-gray-300 hover:text-white hover:bg-gray-700 p-2 rounded-md transition duration-200 flex items-center justify-center flex-shrink-0 w-8 h-8"
            title="返回项目主页"
          >
            <i class="fas fa-arrow-circle-left"></i>
          </router-link>
        </div>
      </div>

      <!-- 文档导航 -->
      <nav class="flex-grow p-6 overflow-y-auto">
        <div v-if="error" class="text-red-500 text-sm mb-3">{{ error }}</div>
        <ul class="space-y-2" v-if="docTypes.length > 0">
          <li v-for="docType in docTypes" :key="docType.value">
            <a
              href="#"
              @click.prevent="selectDocType(docType.value)"
              :class="[
                'nav-link flex items-center justify-between px-4 py-2 rounded-md transition-all duration-200',
                selectedDocType === docType.value
                  ? 'text-white active-link'
                  : 'text-gray-300 hover:text-white'
              ]"
              :title="docType.description"
            >
              <span class="flex items-center">
                <i :class="[docType.icon, 'w-5 mr-3']"></i>
                {{ docType.label }}
              </span>
            </a>
          </li>
        </ul>
        <div v-else class="text-gray-400 text-sm">暂无可用文档</div>
      </nav>

      <!-- 版本选择和比较 - 固定在底部 -->
      <div class="p-4 border-t border-gray-700 mt-auto">
        <div class="flex items-center mb-3">
          <label for="version-select" class="text-gray-400 text-sm font-medium whitespace-nowrap mr-2">版本:</label>
          <select
            id="version-select"
            v-model="selectedVersion"
            @change="onVersionChange"
            class="form-select flex-grow px-2 py-1 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
            style="background-image: url('data:image/svg+xml,%3csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'none\' viewBox=\'0 0 20 20\'%3e%3cpath stroke=\'%236b7280\' stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'1.5\' d=\'M6 8l4 4 4-4\'/%3e%3c/svg%3e'); background-position: right 0.5rem center; background-repeat: no-repeat; background-size: 1.5em 1.5em; padding-right: 2.5rem; -webkit-appearance: none; -moz-appearance: none; appearance: none;"
            aria-label="选择项目版本"
            :disabled="versions.length === 0"
          >
            <option v-for="version in versions" :key="version" :value="version">{{ version }}</option>
          </select>
        </div>
        <div v-if="versions.length === 0" class="text-gray-400 text-xs mb-3">暂无可用版本</div>
        <button
          v-if="versions.length > 1"
          @click="compareVersions"
          class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 border border-gray-600 rounded shadow transition duration-200 text-sm"
        >
          <i class="fas fa-code-branch mr-2"></i> 版本对比
        </button>
      </div>
    </div>
    <!-- 主内容区域 -->
    <div class="content flex-grow py-8 px-8 ml-72 max-w-7xl mx-auto">
      <div class="content-card bg-white shadow-xl rounded-lg p-8 prose max-w-none">
        <!-- 文档内容 -->
        <div v-if="loading" class="flex justify-center items-center h-64">
          <div class="spinner inline-block w-8 h-8 border-4 border-t-indigo-500 border-r-transparent border-b-indigo-500 border-l-transparent rounded-full animate-spin"></div>
          <p class="ml-3 text-gray-600">加载文档内容中...</p>
        </div>

        <div v-else-if="documentContent" class="prose max-w-none" v-html="documentContent"></div>

        <div v-else class="text-center py-12 text-gray-500">
          <i class="fas fa-file-alt text-4xl mb-4"></i>
          <p>请选择一个文档类型查看内容</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue';
import { useRoute } from 'vue-router';
import { documentAPI, formatDocumentTypes } from '@/api/documentApi';
import { projectAPI } from '@/utils/api';
import mermaid from 'mermaid';
import hljs from 'highlight.js';
import 'highlight.js/styles/github-dark.css'; // 使用GitHub暗色主题

const route = useRoute();
const projectId = route.params.id;

// 响应式数据
const project = ref({
  name: '加载中...',
  description: '正在加载项目信息...'
});

const docTypes = ref([]);
const versions = ref([]);
const selectedDocType = ref('');
const selectedVersion = ref('');
const documentContent = ref('');
const loading = ref(false);
const error = ref('');

// 加载项目信息
const loadProjectInfo = async () => {
  try {
    console.log('开始加载项目信息，项目ID:', projectId);
    const response = await projectAPI.getProjectById(projectId);
    console.log('项目API响应:', response);

    // 处理API响应
    if (response && response.data) {
      project.value = {
        name: response.data.name || `项目 ${projectId}`,
        description: response.data.description || '暂无描述'
      };
    } else if (response) {
      project.value = {
        name: response.name || `项目 ${projectId}`,
        description: response.description || '暂无描述'
      };
    } else {
      throw new Error('无效的项目数据');
    }

    console.log('项目信息加载成功:', project.value);
  } catch (err) {
    console.error('加载项目信息失败:', err);
    error.value = '加载项目信息失败';
    // 使用默认值作为后备
    project.value = {
      name: `项目 ${projectId}`,
      description: '这是一个示例项目，用于展示文档功能'
    };
  }
};

// 加载文档类型列表
const loadDocumentTypes = async () => {
  try {
    const rawDocTypes = await documentAPI.getDocumentTypes(projectId);
    console.log('获取到的文档类型:', rawDocTypes);
    docTypes.value = formatDocumentTypes(rawDocTypes || []);

    // 如果有文档类型，默认选择第一个
    if (docTypes.value.length > 0) {
      selectedDocType.value = docTypes.value[0].value;
      console.log('默认选择文档类型:', selectedDocType.value);
    }
  } catch (err) {
    console.error('加载文档类型失败:', err);
    error.value = '加载文档类型失败';
    // 使用默认的文档类型作为后备，按照原型顺序
    docTypes.value = [
      { value: 'PRODUCT_INTRO', label: '产品简介', icon: 'fas fa-home' },
      { value: 'ARCHITECTURE', label: '架构设计', icon: 'fas fa-sitemap' },
      { value: 'EXTENSION', label: '扩展能力', icon: 'fas fa-puzzle-piece' },
      { value: 'USER_MANUAL', label: '用户手册', icon: 'fas fa-book-open' },
      { value: 'API', label: '接口文档', icon: 'fas fa-file-code' },
      { value: 'LLMS_TXT', label: 'LLM生成内容', icon: 'fas fa-file-alt' }
    ];
  }
};

// 加载版本列表
const loadVersions = async () => {
  try {
    const versionList = await documentAPI.getProjectVersions(projectId);
    console.log('获取到的版本列表:', versionList);
    versions.value = versionList || [];

    // 如果有版本，默认选择最新版本
    if (versions.value.length > 0) {
      selectedVersion.value = versions.value[0];
      console.log('默认选择版本:', selectedVersion.value);
    }
  } catch (err) {
    console.error('加载版本列表失败:', err);
    // 使用默认版本作为后备
    versions.value = ['v1.0.0'];
    selectedVersion.value = 'v1.0.0';
  }
};

// 选择文档类型
const selectDocType = (docTypeValue) => {
  selectedDocType.value = docTypeValue;
  loadDocumentContent();
};



// 初始化Mermaid
const initMermaid = () => {
  mermaid.initialize({
    startOnLoad: false,
    theme: 'default',
    securityLevel: 'loose'
  });
};

// 渲染Mermaid图表
const renderMermaidDiagrams = async () => {
  console.log('renderMermaidDiagrams函数被调用');
  await nextTick();

  try {
    // 查找所有可能包含Mermaid代码的元素
    const codeBlocks = document.querySelectorAll('pre code.language-mermaid, code.language-mermaid');
    console.log('找到的Mermaid代码块数量:', codeBlocks.length);

    for (let i = 0; i < codeBlocks.length; i++) {
      const codeBlock = codeBlocks[i];
      const content = codeBlock.textContent || '';

      console.log(`处理第${i + 1}个Mermaid代码块，内容长度:`, content.length);
      console.log(`Mermaid代码内容:`, content.substring(0, 100) + '...');

      if (content.trim()) {
        // 创建Mermaid容器
        const mermaidDiv = document.createElement('div');
        mermaidDiv.className = 'mermaid';
        mermaidDiv.style.textAlign = 'center';

        // 生成唯一ID
        const id = `mermaid-${Date.now()}-${i}`;
        mermaidDiv.id = id;

        try {
          console.log(`开始渲染Mermaid图表，ID: ${id}`);
          // 渲染图表
          const { svg } = await mermaid.render(id, content);
          mermaidDiv.innerHTML = svg;
          console.log(`Mermaid图表渲染成功，SVG长度: ${svg.length}`);

          // 标记为已处理，避免重复渲染
          mermaidDiv.setAttribute('data-processed', 'true');

          // 替换原来的代码块
          const preElement = codeBlock.closest('pre') || codeBlock;
          preElement.parentElement?.replaceChild(mermaidDiv, preElement);
          console.log(`已替换第${i + 1}个代码块为Mermaid图表`);
        } catch (renderErr) {
          console.error(`第${i + 1}个Mermaid图表渲染失败:`, renderErr);
          console.error('失败的Mermaid代码:', content);

          // 创建错误提示元素
          const errorDiv = document.createElement('div');
          errorDiv.style.cssText = `
            background: #FEF2F2;
            border: 1px solid #FECACA;
            border-radius: 0.5rem;
            padding: 1rem;
            margin: 1rem 0;
            color: #DC2626;
            text-align: center;
          `;
          errorDiv.innerHTML = `
            <p><strong>Mermaid图表渲染失败</strong></p>
            <p>图表语法可能有误，请检查代码格式</p>
          `;

          // 替换失败的代码块为错误提示
          const preElement = codeBlock.closest('pre') || codeBlock;
          preElement.parentElement?.replaceChild(errorDiv, preElement);
        }
      }
    }

    // 处理已存在的.mermaid元素（排除我们刚刚创建的）
    const existingMermaidElements = document.querySelectorAll('.mermaid:not([data-processed])');
    console.log('找到未处理的已存在Mermaid元素数量:', existingMermaidElements.length);

    if (existingMermaidElements.length > 0) {
      console.log('开始处理已存在的Mermaid元素...');
      await mermaid.run();
      console.log('已存在的Mermaid元素处理完成');
    }
  } catch (err) {
    console.error('Mermaid图表整体渲染过程出错:', err);
    // 不再显示全局错误，因为单个图表的错误已经单独处理
  }
};

// 加载文档内容
const loadDocumentContent = async () => {
  if (!selectedDocType.value) return;

  loading.value = true;
  error.value = '';

  try {
    console.log('加载文档内容:', selectedDocType.value, selectedVersion.value);
    const htmlContent = await documentAPI.getDocumentHtml(
      projectId,
      selectedDocType.value,
      selectedVersion.value
    );

    documentContent.value = htmlContent || '<p class="text-gray-500">暂无文档内容</p>';
    console.log('文档内容加载成功，长度:', documentContent.value.length);

    // 渲染Mermaid图表
    console.log('开始渲染Mermaid图表...');
    await renderMermaidDiagrams();
    console.log('Mermaid图表渲染完成');

    // 添加代码复制按钮
    await nextTick();
    console.log('开始添加复制按钮...');

    // 调试：检查DOM结构
    await nextTick(); // 确保DOM已更新

    // 等待更长时间确保DOM完全更新
    setTimeout(async () => {
      const contentElement = document.querySelector('.prose');
      console.log('延迟查找.prose元素结果:', !!contentElement);

      if (contentElement) {
        console.log('找到.prose元素，类名:', contentElement.className);
        console.log('HTML内容前500字符:', contentElement.innerHTML.substring(0, 500));

        const preElements = contentElement.querySelectorAll('pre');
        console.log('找到的pre元素数量:', preElements.length);

        const ulElements = contentElement.querySelectorAll('ul');
        console.log('找到的ul元素数量:', ulElements.length);

        const liElements = contentElement.querySelectorAll('li');
        console.log('找到的li元素数量:', liElements.length);

        const codeElements = contentElement.querySelectorAll('code');
        console.log('找到的code元素数量:', codeElements.length);

        // 立即调用复制按钮功能和Mermaid渲染
        addCopyButtons();

        // 重新渲染Mermaid图表
        console.log('延迟渲染Mermaid图表...');
        await renderMermaidDiagrams();
        console.log('延迟Mermaid图表渲染完成');
      } else {
        console.log('延迟查找也未找到.prose元素');

        // 查找所有可能包含prose类的元素
        const allElements = document.querySelectorAll('*');
        for (let el of allElements) {
          if (el.className && el.className.includes('prose')) {
            console.log('找到包含prose的元素:', el.className, el.tagName);
          }
        }
      }
    }, 100);

    const contentElement = document.querySelector('.prose');
    if (contentElement) {
      console.log('找到.prose元素，内容长度:', contentElement.innerHTML.length);
      console.log('HTML内容前500字符:', contentElement.innerHTML.substring(0, 500));

      const preElements = contentElement.querySelectorAll('pre');
      console.log('找到的pre元素数量:', preElements.length);

      const ulElements = contentElement.querySelectorAll('ul');
      console.log('找到的ul元素数量:', ulElements.length);

      const liElements = contentElement.querySelectorAll('li');
      console.log('找到的li元素数量:', liElements.length);

      // 调试列表结构
      if (liElements.length > 0) {
        console.log('第一个li元素的HTML:', liElements[0].outerHTML);
        console.log('第一个li元素的父元素类名:', liElements[0].parentElement?.className);
        console.log('第一个li元素的计算样式display:', window.getComputedStyle(liElements[0]).display);
        console.log('第一个li元素的计算样式listStyle:', window.getComputedStyle(liElements[0]).listStyle);
      }

      const codeElements = contentElement.querySelectorAll('code');
      console.log('找到的code元素数量:', codeElements.length);
    } else {
      console.log('未找到.prose元素');
      // 检查是否有其他可能的容器
      const allDivs = document.querySelectorAll('div');
      console.log('页面中所有div元素数量:', allDivs.length);

      // 查找包含文档内容的div
      for (let div of allDivs) {
        if (div.innerHTML && div.innerHTML.length > 1000) {
          console.log('找到可能的内容容器，类名:', div.className);
          console.log('内容前200字符:', div.innerHTML.substring(0, 200));
          break;
        }
      }
    }

    addCopyButtons();
  } catch (err) {
    console.error('加载文档内容失败:', err);
    error.value = '加载文档内容失败';
    documentContent.value = '<p class="text-red-500">加载文档内容失败，请稍后重试。</p>';
  } finally {
    loading.value = false;
  }
};

// 版本切换
const onVersionChange = () => {
  loadDocumentContent();
};

// 比较版本
const compareVersions = () => {
  // TODO: 实现版本比较功能
  alert(`比较版本: ${selectedVersion.value} 与其他版本`);
};



// 添加代码复制功能和语法高亮
const addCopyButtons = () => {
  console.log('addCopyButtons函数被调用');
  const preElements = document.querySelectorAll('.prose pre');
  console.log('找到的pre元素数量:', preElements.length);
  preElements.forEach((pre, index) => {
    console.log(`处理第${index + 1}个pre元素`);

    // 移除已存在的复制按钮
    const existingButton = pre.querySelector('.copy-button');
    if (existingButton) {
      existingButton.remove();
    }

    // 添加语法高亮
    const codeElement = pre.querySelector('code');
    if (codeElement && !codeElement.classList.contains('hljs')) {
      hljs.highlightElement(codeElement);
      console.log(`为第${index + 1}个代码块添加语法高亮`);
    }

    // 创建复制按钮
    const copyButton = document.createElement('button');
    copyButton.className = 'copy-button';
    copyButton.innerHTML = '复制';
    copyButton.style.cssText = `
      position: absolute;
      top: 0.5rem;
      right: 0.5rem;
      background: #4F46E5 !important;
      color: white !important;
      padding: 0.5rem 1rem;
      border-radius: 0.375rem;
      font-size: 0.875rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      border: none;
      z-index: 1000;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    `;

    console.log('创建复制按钮，添加到pre元素');

    copyButton.addEventListener('click', async () => {
      const code = pre.querySelector('code');
      if (code) {
        try {
          await navigator.clipboard.writeText(code.textContent || '');
          copyButton.innerHTML = '已复制!';
          copyButton.style.background = 'rgba(34, 197, 94, 0.2)';
          setTimeout(() => {
            copyButton.innerHTML = '复制';
            copyButton.style.background = 'rgba(255, 255, 255, 0.1)';
          }, 2000);
        } catch (err) {
          console.error('复制失败:', err);
          copyButton.innerHTML = '复制失败';
          setTimeout(() => {
            copyButton.innerHTML = '复制';
          }, 2000);
        }
      }
    });

    copyButton.addEventListener('mouseenter', () => {
      copyButton.style.background = 'rgba(255, 255, 255, 0.2)';
      copyButton.style.color = 'white';
    });

    copyButton.addEventListener('mouseleave', () => {
      copyButton.style.background = 'rgba(255, 255, 255, 0.1)';
      copyButton.style.color = '#E2E8F0';
    });

    pre.appendChild(copyButton);
  });
};

// 初始化
onMounted(async () => {
  try {
    // 初始化Mermaid
    initMermaid();

    // 并行加载项目信息、文档类型和版本列表
    await Promise.all([
      loadProjectInfo(),
      loadDocumentTypes(),
      loadVersions()
    ]);

    // 如果有选中的文档类型，加载文档内容
    if (selectedDocType.value) {
      await loadDocumentContent();
    }
  } catch (err) {
    console.error('初始化失败:', err);
    error.value = '页面初始化失败';
  }
});
</script>

<style>
/* 按照界面原型设计的样式 */
:root {
  --sidebar-width: 18rem;
  --primary-color: #4F46E5; /* Indigo-600 */
  --primary-hover: #4338CA; /* Indigo-700 */
  --sidebar-bg: #1E293B; /* Slate-800 */
  --sidebar-header-bg: #0F172A; /* Slate-900 */
  --sidebar-item-hover: #334155; /* Slate-700 */
  --sidebar-active: #3B82F6; /* Blue-500 */
}

.sidebar {
  width: 18rem; /* 固定宽度，与原型一致 */
  background: linear-gradient(180deg, #1E293B 0%, #0F172A 100%); /* 渐变背景 */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.sidebar-header {
  background-color: #0F172A; /* Slate-900 */
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header h1 {
  line-height: 2rem; /* 与按钮高度一致 */
  margin: 0; /* 移除默认margin */
}

.sidebar-header .flex {
  align-items: center !important; /* 强制垂直居中 */
}

.content {
  margin-left: 18rem; /* 与侧边栏宽度一致 */
}

/* 侧边栏导航样式 - 确保图标和文本垂直居中对齐 */
.nav-link {
  transition: all 0.2s ease;
  border-left: 3px solid transparent;
  display: flex;
  align-items: center; /* 垂直居中对齐 */
}

.nav-link:hover {
  background-color: var(--sidebar-item-hover) !important;
  border-left: 3px solid var(--primary-color) !important;
  transform: translateX(2px); /* 添加悬停动画效果 */
}

.active-link {
  color: #ffffff !important;
  background-color: var(--sidebar-active) !important;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
  border-left: 3px solid var(--primary-color) !important;
  transform: translateX(2px); /* 激活状态的位移效果 */
}

.content-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* 增强阴影效果 */
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); /* 更强的悬停阴影 */
  transform: translateY(-2px); /* 添加悬停上浮效果 */
}

/* 图标和文本对齐增强 */
.nav-link span {
  display: flex;
  align-items: center; /* 确保图标和文本垂直居中 */
}

.nav-link i {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem; /* 固定图标宽度 */
  height: 1.25rem;
  margin-right: 0.75rem;
}

/* 按钮样式增强 */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* 选择框样式增强 */
select {
  transition: all 0.2s ease;
}

select:focus {
  transform: scale(1.02);
  box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}

/* 加载动画增强 */
.spinner {
  background: conic-gradient(from 0deg, transparent, #4F46E5);
  border-radius: 50%;
  position: relative;
}

.spinner::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  right: 2px;
  bottom: 2px;
  background: white;
  border-radius: 50%;
}

/* 自定义表单样式 */
.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1F2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #4F46E5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

/* 文档内容样式 - 与原型保持一致 */

/* 基础样式 */
.prose {
  max-width: none;
}

.prose h1 {
  font-size: 1.875rem; /* 3xl */
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  font-weight: 700;
  color: #1F2937; /* Gray-800 */
  border-bottom: 2px solid #E5E7EB; /* Gray-200 */
  position: relative;
}

.prose h1::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #4F46E5 0%, #7C3AED 100%);
}

.prose h2 {
  font-size: 1.5rem; /* 2xl */
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 700; /* 增加字体粗细 */
  color: #1F2937; /* 深色文字 */
  display: flex !important;
  align-items: center !important;
  background: linear-gradient(135deg, #EEF2FF 0%, #E0E7FF 100%); /* 更明显的蓝色渐变 */
  border-left: 4px solid #4F46E5; /* 增加边框宽度 */
  border-radius: 0.5rem; /* 增加圆角 */
  padding: 1rem 1.5rem; /* 增加内边距 */
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06); /* 增强阴影 */
  border-top: 1px solid #C7D2FE; /* 添加顶部边框 */
  border-right: 1px solid #C7D2FE; /* 添加右边框 */
  border-bottom: 1px solid #C7D2FE; /* 添加底部边框 */
}

/* h2不再需要::before伪元素 */

.prose h3 {
  font-size: 1.25rem; /* xl */
  margin-top: 2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #475569; /* Slate-600 */
  position: relative;
  padding-left: 1rem;
  display: flex !important;
  align-items: center !important;
}

.prose h3::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50% !important;
  transform: translateY(-50%) !important; /* 垂直居中 */
  width: 4px;
  height: 1.5rem;
  background: linear-gradient(180deg, #4F46E5 0%, #7C3AED 100%);
  border-radius: 2px;
}

.prose h4 {
  font-size: 1.125rem; /* lg */
  margin-top: 1.5rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #374151; /* 深灰色文字 */
  position: relative;
  padding-left: 1.25rem; /* 为圆点留出空间 */
  display: flex !important;
  align-items: center !important;
}

.prose h4::before {
  content: ''; /* 空内容，用CSS绘制圆点 */
  position: absolute;
  left: 0;
  top: 50% !important;
  transform: translateY(-50%) !important; /* 垂直居中 */
  width: 6px; /* 圆点宽度 */
  height: 6px; /* 圆点高度 */
  background: linear-gradient(135deg, #6366F1 0%, #8B5CF6 100%); /* 渐变圆点，与h3的线条颜色呼应 */
  border-radius: 50%; /* 圆形 */
  box-shadow: 0 1px 2px rgba(99, 102, 241, 0.3); /* 轻微阴影 */
}

/* 列表样式增强 - 模拟原型中的彩色图标效果 */
.prose ul {
  list-style: none !important;
  padding-left: 0 !important;
  margin-left: 0 !important;
}

.prose ul li {
  display: flex !important;
  align-items: flex-start !important; /* 改为flex-start以确保多行文本时的对齐 */
  margin-bottom: 0.75rem !important;
  position: relative !important;
  list-style: none !important;
  line-height: 1.6 !important;
  padding-left: 0 !important;
}

.prose ul li::before {
  content: '✓' !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 1.25rem !important;
  height: 1.25rem !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  color: white !important;
  border-radius: 50% !important;
  margin-right: 0.75rem !important;
  margin-top: 0.125rem !important; /* 微调垂直位置 */
  font-size: 0.75rem !important;
  font-weight: bold !important;
  flex-shrink: 0 !important;
}

/* 确保列表项文本垂直居中 */
.prose ul li > span,
.prose ul li > p {
  margin: 0 !important;
  line-height: 1.6 !important;
}

/* Mermaid图表基础样式 */
.prose .mermaid,
.prose .mermaid-diagram {
  max-width: 100% !important;
  overflow-x: auto !important;
  margin: 1.5rem 0 !important;
  text-align: center !important;
}

/* 确保SVG容器响应式 */
.prose .mermaid > svg,
.prose .mermaid-diagram > svg {
  width: 100% !important;
  height: auto !important;
  max-width: none !important;
}

/* 移除了自定义样式，使用Mermaid默认样式 */

/* 响应式设计 */
@media (max-width: 768px) {
  .prose .mermaid,
  .prose .mermaid-diagram {
    overflow-x: auto;
  }
}

/* 段落样式增强 */
.prose p {
  margin-bottom: 1rem;
  line-height: 1.7;
  color: #374151;
}

.prose p:first-of-type:not(.mermaid p) {
  font-size: 1.125rem;
  color: #1F2937;
  font-weight: 500;
}

/* 确保Mermaid图表不受p:first-of-type影响 */
.prose .mermaid p {
  font-size: inherit !important;
  color: inherit !important;
  font-weight: inherit !important;
}

/* 强调文本样式 */
.prose strong {
  font-weight: 600;
  color: #1F2937;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.prose em {
  font-style: italic;
  color: #6366F1;
}

/* 代码块样式增强 */
.prose code {
  background: linear-gradient(135deg, #EEF2FF 0%, #E0E7FF 100%);
  color: #4F46E5;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #C7D2FE;
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.1);
}

.prose pre {
  background: linear-gradient(135deg, #1E293B 0%, #0F172A 100%);
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #334155;
  position: relative;
  overflow-x: auto;
}

.prose pre code {
  background: transparent;
  color: #E2E8F0;
  border: none;
  box-shadow: none;
  padding: 0;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* 代码块复制按钮样式由JavaScript动态添加 */

/* 引用块样式 */
.prose blockquote {
  border-left: 4px solid #4F46E5;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  padding: 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.prose blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 3rem;
  color: #4F46E5;
  opacity: 0.3;
  font-family: serif;
}

/* 表格样式增强 */
.prose table {
  border-collapse: collapse;
  width: 100%;
  margin: 1.5rem 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  overflow: hidden;
}

.prose table th,
.prose table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #E5E7EB;
}

.prose table th {
  background-color: #F9FAFB;
  font-weight: 600;
  color: #374151;
}

.prose table tr:hover {
  background-color: #F9FAFB;
}
</style>

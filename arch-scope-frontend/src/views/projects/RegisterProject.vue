<template>
  <MainLayout>
    <div class="container mx-auto p-6 max-w-4xl">
      <div class="flex items-center justify-between mb-6 animate-fade-in">
        <h1 class="text-3xl font-bold text-gray-800 gradient-text">智能项目接入</h1>
      </div>

      <div class="content-card mb-6 animate-slide-up">
        <div class="p-6 bg-gradient-to-r from-indigo-50 to-purple-50 border-b border-gray-200">
          <h2 class="text-xl font-bold text-gray-800 flex items-center">
            <div class="flex items-center justify-center w-10 h-10 bg-indigo-100 rounded-lg mr-3">
              <i class="fas fa-brain text-indigo-600 text-lg"></i>
            </div>
            智能项目接入
          </h2>
          <p class="text-sm text-gray-600 mt-2 ml-13">输入您的Git仓库地址，AI将深度解析项目架构并生成智能化文档</p>
        </div>
        <div class="p-8">
          <form @submit.prevent="registerProject">
            <!-- Git仓库地址输入 - 放在最前面 -->
            <div class="mb-8">
              <label for="repo_url" class="text-gray-800 text-sm font-semibold mb-3 flex items-center">
                <div class="flex items-center justify-center w-6 h-6 bg-gray-100 rounded-md mr-2">
                  <i class="fas fa-code-branch text-gray-600 text-xs"></i>
                </div>
                项目仓库地址 (Gitlab/Github)
              </label>
              <div class="relative">
                <span class="absolute inset-y-0 left-0 pl-4 flex items-center">
                  <i class="fas fa-link text-gray-400"></i>
                </span>
                <input
                  type="text"
                  id="repo_url"
                  v-model="projectForm.repositoryUrl"
                  @input="onRepositoryUrlInput"
                  @blur="validateRepositoryUrl"
                  class="pl-12 w-full px-4 py-3 border-2 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300"
                  :class="{
                    'border-gray-200 hover:border-gray-300': !urlValidation.message,
                    'border-red-300 bg-red-50': urlValidation.message && !urlValidation.isValid,
                    'border-green-300 bg-green-50': urlValidation.message && urlValidation.isValid
                  }"
                  placeholder="例如: https://github.com/user/repository"
                  required
                >
                <!-- 加载指示器和状态图标 -->
                <div class="absolute inset-y-0 right-0 pr-4 flex items-center">
                  <div v-if="repositoryLoading" class="flex items-center">
                    <i class="fas fa-spinner fa-spin text-indigo-500"></i>
                  </div>
                  <div v-else-if="urlValidation.message && urlValidation.isValid" class="flex items-center">
                    <i class="fas fa-check-circle text-green-500"></i>
                  </div>
                  <div v-else-if="urlValidation.message && !urlValidation.isValid" class="flex items-center">
                    <i class="fas fa-exclamation-circle text-red-500"></i>
                  </div>
                </div>
              </div>
              <!-- URL验证提示 -->
              <div v-if="urlValidation.message" class="mt-2 text-sm">
                <div class="flex items-start">
                  <i v-if="urlValidation.isValid" class="fas fa-check-circle text-green-500 mr-2 mt-0.5"></i>
                  <i v-else class="fas fa-exclamation-circle text-red-500 mr-2 mt-0.5"></i>
                  <div class="flex-1">
                    <span :class="urlValidation.isValid ? 'text-green-600' : 'text-red-600'">
                      {{ urlValidation.message.split('。建议尝试使用HTTPS URL:')[0] }}
                    </span>
                    <!-- URL转换建议 -->
                    <div v-if="urlValidation.suggestedUrl" class="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-md">
                      <div class="flex items-center justify-between">
                        <div class="flex items-center">
                          <i class="fas fa-lightbulb text-blue-500 mr-2"></i>
                          <span class="text-blue-700 text-sm font-medium">建议使用HTTPS URL:</span>
                        </div>
                        <IconButton
                          icon="check"
                          color="primary"
                          size="sm"
                          tooltip="使用建议的HTTPS URL"
                          @click="useSuggestedUrl"
                        >
                          使用此URL
                        </IconButton>
                      </div>
                      <div class="mt-2 p-2 bg-white border rounded text-sm font-mono text-gray-700 break-all">
                        {{ urlValidation.suggestedUrl }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <p class="mt-2 text-sm text-gray-500 flex items-center">
                <i class="fas fa-info-circle mr-1"></i>
                输入项目的Git仓库地址，系统将自动解析并填充项目信息
              </p>
            </div>

            <!-- 项目详细信息字段 - 仅在仓库解析成功后显示 -->
            <div v-if="showDetailFields" class="animate-fade-in">
              <div class="mb-4">
                <label for="name" class="text-gray-700 text-sm font-bold mb-2 flex items-center">
                  <i class="fas fa-project-diagram text-gray-500 mr-2"></i>
                  项目名称
                  <span v-if="autoFilled.name" class="ml-2 text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                    <i class="fas fa-magic mr-1"></i>自动填充
                  </span>
                </label>
                <input
                  type="text"
                  id="name"
                  v-model="projectForm.name"
                  @blur="validateProjectName"
                  @input="() => { if (!fieldValidation.name.isValid) validateProjectName() }"
                  class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent"
                  :class="{
                    'border-gray-300': fieldValidation.name.isValid,
                    'border-red-500': !fieldValidation.name.isValid
                  }"
                  placeholder="请输入项目名称"
                  required
                >
                <!-- 项目名称验证提示 -->
                <div v-if="!fieldValidation.name.isValid && fieldValidation.name.message" class="mt-2 text-sm text-red-600 flex items-center">
                  <i class="fas fa-exclamation-circle mr-1"></i>
                  {{ fieldValidation.name.message }}
                </div>
              </div>

              <div class="mb-4">
                <label for="description" class="text-gray-700 text-sm font-bold mb-2 flex items-center">
                  <i class="fas fa-info-circle text-gray-500 mr-2"></i>
                  项目描述
                  <span v-if="autoFilled.description" class="ml-2 text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                    <i class="fas fa-magic mr-1"></i>自动填充
                  </span>
                </label>
                <textarea
                  id="description"
                  v-model="projectForm.description"
                  class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent"
                  placeholder="请输入项目描述"
                  rows="3"
                ></textarea>
              </div>

              <!-- 分析配置选项 -->
              <div class="mb-6">
                <label class="text-gray-800 text-sm font-semibold mb-3 flex items-center">
                  <div class="flex items-center justify-center w-6 h-6 bg-purple-100 rounded-md mr-2">
                    <i class="fas fa-cogs text-purple-600 text-xs"></i>
                  </div>
                  智能分析配置
                </label>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div class="p-4 border-2 border-gray-200 rounded-xl hover:border-indigo-300 transition-colors cursor-pointer">
                    <div class="flex items-center mb-2">
                      <input type="radio" id="analysis_standard" name="analysis_type" value="standard" class="text-indigo-600 focus:ring-indigo-500" checked>
                      <label for="analysis_standard" class="ml-2 font-medium text-gray-900 cursor-pointer">标准分析</label>
                    </div>
                    <p class="text-sm text-gray-600">快速生成项目架构文档，适合大多数项目</p>
                  </div>
                  <div class="p-4 border-2 border-gray-200 rounded-xl hover:border-indigo-300 transition-colors cursor-pointer">
                    <div class="flex items-center mb-2">
                      <input type="radio" id="analysis_deep" name="analysis_type" value="deep" class="text-indigo-600 focus:ring-indigo-500">
                      <label for="analysis_deep" class="ml-2 font-medium text-gray-900 cursor-pointer">深度分析</label>
                    </div>
                    <p class="text-sm text-gray-600">全面解析代码结构和依赖关系，生成详细文档</p>
                  </div>
                </div>
              </div>

              <div class="mb-6">
                <label for="branch" class="text-gray-800 text-sm font-semibold mb-3 flex items-center">
                  <div class="flex items-center justify-center w-6 h-6 bg-gray-100 rounded-md mr-2">
                    <i class="fas fa-code-branch text-gray-600 text-xs"></i>
                  </div>
                  Git分支
                  <span v-if="autoFilled.branch" class="ml-2 text-xs text-green-600 bg-green-100 px-2 py-1 rounded">
                    <i class="fas fa-magic mr-1"></i>自动填充
                  </span>
                </label>
                <div class="relative">
                  <span class="absolute inset-y-0 left-0 pl-3 flex items-center">
                    <i class="fas fa-code-branch text-gray-400"></i>
                  </span>
                  <select
                    id="branch"
                    v-model="projectForm.branch"
                    @change="validateBranch"
                    class="pl-10 w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-600 focus:border-transparent appearance-none bg-white"
                    :class="{
                      'border-gray-300': fieldValidation.branch.isValid,
                      'border-red-500': !fieldValidation.branch.isValid
                    }"
                    :disabled="!availableBranches.length"
                  >
                    <option v-for="branch in availableBranches" :key="branch" :value="branch">
                      {{ branch }}
                    </option>
                  </select>
                  <!-- 下拉箭头 -->
                  <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                    <i class="fas fa-chevron-down text-gray-400"></i>
                  </div>
                </div>
                <!-- 分支验证提示 -->
                <div v-if="!fieldValidation.branch.isValid && fieldValidation.branch.message" class="mt-2 text-sm text-red-600 flex items-center">
                  <i class="fas fa-exclamation-circle mr-1"></i>
                  {{ fieldValidation.branch.message }}
                </div>
                <p class="mt-2 text-sm text-gray-500 flex items-center">
                  <i class="fas fa-info-circle mr-1"></i>
                  选择要分析的分支（默认为{{ defaultBranch || 'main' }}）
                </p>
              </div>
            </div>
            
            <div class="flex justify-end gap-6 pt-4">
              <router-link
                to="/projects"
                class="px-6 py-3 border-2 border-gray-300 text-gray-700 rounded-xl hover:bg-gray-50 hover:border-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-all duration-300 font-medium"
                :class="{ 'pointer-events-none opacity-50': registrationState.isLoading }"
              >
                <i class="fas fa-arrow-left mr-2"></i>
                返回列表
              </router-link>
              <button
                type="submit"
                class="relative px-8 py-3 text-white rounded-xl font-semibold transition-all duration-300 flex items-center overflow-hidden group"
                :class="{
                  'opacity-50 cursor-not-allowed': !canSubmit,
                  'btn-primary hover:transform hover:scale-105 hover:shadow-xl': canSubmit
                }"
                :disabled="!canSubmit"
              >
                <!-- 背景动画效果 -->
                <div v-if="canSubmit" class="absolute inset-0 bg-gradient-to-r from-purple-600 to-indigo-600 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                <!-- 按钮内容 -->
                <div class="relative flex items-center">
                  <div v-if="registrationState.isLoading" class="flex items-center">
                    <div class="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin mr-3"></div>
                    <span>AI正在深度解析...</span>
                  </div>
                  <div v-else class="flex items-center">
                    <i class="fas fa-brain mr-3 text-lg"></i>
                    <span>开始智能分析</span>
                    <i class="fas fa-arrow-right ml-3 transform group-hover:translate-x-1 transition-transform duration-300"></i>
                  </div>
                </div>
              </button>
            </div>

            <!-- 错误提示 -->
            <div v-if="error || registrationState.error" class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
              <div class="flex items-start">
                <i class="fas fa-exclamation-circle text-red-500 mr-2 mt-0.5"></i>
                <div class="flex-1">
                  <h4 class="text-red-800 font-medium">注册失败</h4>
                  <p class="text-red-700 text-sm mt-1">{{ error || registrationState.error }}</p>
                </div>
              </div>
            </div>
          </form>
        </div>
      </div>
    </div>

    <!-- 项目注册加载器 -->
    <ProjectRegistrationLoader
      :is-visible="registrationState.isLoading"
      :current-step-index="registrationState.currentStepIndex"
      :show-cancel-button="true"
      :can-cancel="registrationState.canCancel"
      :timeout-seconds="30"
      @cancel="handleRegistrationCancel"
      @timeout="handleRegistrationTimeout"
    />
  </MainLayout>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useProjectStore } from '@/stores/project'
import MainLayout from '@/layouts/MainLayout.vue'
import ProjectRegistrationLoader from '@/components/ProjectRegistrationLoader.vue'
import IconButton from '@/components/IconButton.vue'
import { gitRepositoryAPI, type GitRepositoryInfo } from '@/utils/api'
import { useProjectRegistration } from '@/composables/useProjectRegistration'
import { useToast } from '@/composables/useToast'
import {
  isValidGitUrl,
  extractProjectNameFromUrl,
  validateGitUrlWithMessage,
  getCommonBranchNames,
  convertSshToHttpsUrl
} from '@/utils/gitUtils'

export default defineComponent({
  name: 'RegisterProjectView',
  components: {
    MainLayout,
    ProjectRegistrationLoader,
    IconButton
  },
  setup() {
    const router = useRouter()
    const projectStore = useProjectStore()
    const toast = useToast()
    const error = ref('')
    const repositoryLoading = ref(false)
    const repositoryInfo = ref<GitRepositoryInfo | null>(null)
    const lastFetchedUrl = ref('')

    // 使用项目注册组合式函数
    const {
      state: registrationState,
      registerProject: performRegistration,
      cancelRegistration,
      handleTimeout: handleRegistrationTimeout,
      resetState: resetRegistrationState
    } = useProjectRegistration()

    const projectForm = reactive({
      name: '',
      description: '',
      repositoryUrl: '',
      branch: 'main'
    })

    // 控制详细字段显示
    const showDetailFields = ref(false)

    // 表单字段验证状态
    const fieldValidation = reactive({
      name: { isValid: true, message: '' },
      repositoryUrl: { isValid: true, message: '' },
      branch: { isValid: true, message: '' }
    })

    // 验证项目名称
    const validateProjectName = () => {
      const name = projectForm.name.trim()
      if (!name) {
        fieldValidation.name.isValid = false
        fieldValidation.name.message = '项目名称不能为空'
        return false
      }
      if (name.length < 2) {
        fieldValidation.name.isValid = false
        fieldValidation.name.message = '项目名称至少需要2个字符'
        return false
      }
      if (name.length > 100) {
        fieldValidation.name.isValid = false
        fieldValidation.name.message = '项目名称不能超过100个字符'
        return false
      }
      fieldValidation.name.isValid = true
      fieldValidation.name.message = ''
      return true
    }

    // 验证分支名称
    const validateBranch = () => {
      const branch = projectForm.branch.trim()
      if (!branch) {
        fieldValidation.branch.isValid = false
        fieldValidation.branch.message = '请选择分支'
        return false
      }
      fieldValidation.branch.isValid = true
      fieldValidation.branch.message = ''
      return true
    }

    // 表单整体验证
    const isFormValid = computed(() => {
      return projectForm.name.trim() &&
             projectForm.repositoryUrl.trim() &&
             isValidGitUrl(projectForm.repositoryUrl) &&
             urlValidation.isValid &&
             fieldValidation.name.isValid &&
             fieldValidation.branch.isValid &&
             !registrationState.isLoading &&
             !isSubmitting.value
    })

    // 控制提交按钮状态
    const canSubmit = computed(() => {
      return showDetailFields.value &&
             isFormValid.value &&
             !repositoryLoading.value &&
             !isSubmitting.value &&
             !registrationState.isLoading
    })

    // 自动填充状态跟踪
    const autoFilled = reactive({
      name: false,
      description: false,
      branch: false
    })

    // URL验证状态
    const urlValidation = reactive({
      isValid: false,
      message: '',
      suggestedUrl: ''
    })

    // 防抖定时器
    let debounceTimer: NodeJS.Timeout | null = null

    // 可用分支列表
    const availableBranches = computed(() => {
      if (repositoryInfo.value?.branches?.length) {
        return repositoryInfo.value.branches
      }
      return getCommonBranchNames()
    })

    // 默认分支
    const defaultBranch = computed(() => {
      return repositoryInfo.value?.defaultBranch || 'main'
    })

    // Git仓库URL输入处理（只做UI状态重置，不调用后端）
    const onRepositoryUrlInput = () => {
      // 清除之前的定时器
      if (debounceTimer) {
        clearTimeout(debounceTimer)
      }

      // 重置验证状态
      urlValidation.isValid = false
      urlValidation.message = ''
      urlValidation.suggestedUrl = ''
      lastFetchedUrl.value = '' // 重置最后获取的URL，允许重新获取

      // 立即隐藏详细字段，无论URL是否为空
      showDetailFields.value = false

      // 如果URL为空，重置所有状态
      if (!projectForm.repositoryUrl.trim()) {
        resetAutoFilledData()
        return
      }

      // 只做基本的格式验证，不调用后端接口
      const validation = validateGitUrlWithMessage(projectForm.repositoryUrl)
      if (!validation.isValid) {
        urlValidation.message = validation.message || ''
        urlValidation.isValid = false
      } else {
        // 格式正确时，清除错误信息，但不标记为有效（需要后端验证）
        urlValidation.message = ''
      }
    }

    // 验证Git仓库URL并获取详细信息（失焦时调用，唯一的后端接口调用点）
    const validateRepositoryUrl = async () => {
      // 清除任何待执行的防抖定时器
      if (debounceTimer) {
        clearTimeout(debounceTimer)
        debounceTimer = null
      }

      if (!projectForm.repositoryUrl.trim()) {
        urlValidation.message = ''
        urlValidation.isValid = false
        showDetailFields.value = false
        resetAutoFilledData()
        return
      }

      // 首先进行格式验证
      const validation = validateGitUrlWithMessage(projectForm.repositoryUrl)
      if (!validation.isValid) {
        urlValidation.isValid = false
        urlValidation.message = validation.message || ''
        showDetailFields.value = false
        return
      }

      // 格式验证通过，调用后端接口获取详细信息
      await validateAndFetchRepositoryInfo()
    }

    // 验证并获取仓库信息
    const validateAndFetchRepositoryInfo = async () => {
      // 防止重复调用
      if (repositoryLoading.value) {
        return
      }

      const validation = validateGitUrlWithMessage(projectForm.repositoryUrl)
      urlValidation.isValid = validation.isValid
      urlValidation.message = validation.message || ''

      if (validation.isValid) {
        // 立即填充基本信息
        fillBasicInfo()
        // 异步获取详细信息
        await fetchRepositoryDetails()
      }
    }

    // 填充基本信息（从URL解析）
    const fillBasicInfo = () => {
      if (!isValidGitUrl(projectForm.repositoryUrl)) return

      const projectName = extractProjectNameFromUrl(projectForm.repositoryUrl)

      if (projectName && !projectForm.name) {
        projectForm.name = projectName
        autoFilled.name = true
      }
    }

    // 获取仓库详细信息
    const fetchRepositoryDetails = async () => {
      if (!isValidGitUrl(projectForm.repositoryUrl)) return

      // 防止重复调用
      if (repositoryLoading.value) {
        return
      }

      // 检查是否已经获取过相同URL的信息
      if (lastFetchedUrl.value === projectForm.repositoryUrl) {
        return
      }

      try {
        repositoryLoading.value = true
        lastFetchedUrl.value = projectForm.repositoryUrl

        const response = await gitRepositoryAPI.getRepositoryDetails(projectForm.repositoryUrl)

        if (response.success) {
          repositoryInfo.value = response
          fillDetailedInfo(response)
          urlValidation.message = '仓库验证成功，已自动填充项目信息'
          urlValidation.isValid = true
          // 显示详细字段
          showDetailFields.value = true
          // 显示成功Toast
          toast.success('仓库验证成功', {
            message: '已自动填充项目信息，请检查并完善其他字段',
            duration: 3000
          })
        } else {
          urlValidation.message = response.errorMessage || '无法获取仓库详细信息'
          urlValidation.isValid = false
          showDetailFields.value = false
        }
      } catch (error: any) {
        console.warn('获取仓库详细信息失败:', error)

        // 检查是否是HTTP错误响应
        if (error.response && error.response.data) {
          const errorData = error.response.data
          if (errorData.errorMessage) {
            urlValidation.message = errorData.errorMessage

            // 如果错误信息包含HTTPS URL建议，提取并显示
            if (errorData.errorMessage.includes('建议尝试使用HTTPS URL:')) {
              const httpsUrlMatch = errorData.errorMessage.match(/https:\/\/[^\s]+/)
              if (httpsUrlMatch) {
                const suggestedUrl = httpsUrlMatch[0]
                urlValidation.message += `\n\n点击使用建议的HTTPS URL: ${suggestedUrl}`
                // 可以添加一个按钮来自动替换URL
                urlValidation.suggestedUrl = suggestedUrl
              }
            }
          } else {
            urlValidation.message = '仓库验证失败，请检查URL是否正确'
          }
        } else {
          urlValidation.message = '无法连接到仓库，请检查网络连接'
        }
        urlValidation.isValid = false
        showDetailFields.value = false
      } finally {
        repositoryLoading.value = false
      }
    }

    // 填充详细信息
    const fillDetailedInfo = (info: GitRepositoryInfo) => {
      // 填充项目名称（如果还没有填充）
      if (info.projectName && !projectForm.name) {
        projectForm.name = info.projectName
        autoFilled.name = true
      }

      // 填充项目描述
      if (info.description && !projectForm.description) {
        projectForm.description = info.description
        autoFilled.description = true
      }

      // 设置默认分支
      if (info.defaultBranch) {
        projectForm.branch = info.defaultBranch
        autoFilled.branch = true
      }
    }

    // 重置自动填充的数据
    const resetAutoFilledData = () => {
      if (autoFilled.name) {
        projectForm.name = ''
        autoFilled.name = false
      }
      if (autoFilled.description) {
        projectForm.description = ''
        autoFilled.description = false
      }
      if (autoFilled.branch) {
        projectForm.branch = 'main'
        autoFilled.branch = false
      }
      repositoryInfo.value = null
      lastFetchedUrl.value = '' // 重置最后获取的URL
    }

    // 使用建议的URL
    const useSuggestedUrl = async () => {
      if (urlValidation.suggestedUrl) {
        projectForm.repositoryUrl = urlValidation.suggestedUrl
        urlValidation.suggestedUrl = ''
        // 重新验证新的URL（直接调用后端验证）
        await validateRepositoryUrl()
      }
    }

    // 防重复提交标志
    const isSubmitting = ref(false)

    // 注册项目
    const registerProject = async () => {
      // 防重复提交检查
      if (isSubmitting.value || registrationState.isLoading) {
        console.warn('项目注册正在进行中，忽略重复提交')
        return
      }

      try {
        isSubmitting.value = true
        error.value = ''
        resetRegistrationState()

        // 最终验证 - 逐个检查字段
        const validationErrors = []

        if (!validateProjectName()) {
          validationErrors.push('项目名称验证失败')
        }

        if (!urlValidation.isValid) {
          validationErrors.push('仓库URL验证失败')
        }

        if (!validateBranch()) {
          validationErrors.push('分支选择验证失败')
        }

        if (!showDetailFields.value) {
          validationErrors.push('请先完成仓库验证')
        }

        if (validationErrors.length > 0) {
          error.value = `表单验证失败：${validationErrors.join('、')}`
          return
        }

        console.log('开始项目注册流程:', {
          name: projectForm.name,
          repositoryUrl: projectForm.repositoryUrl,
          branch: projectForm.branch
        })

        const result = await performRegistration({
          name: projectForm.name,
          description: projectForm.description,
          repositoryUrl: projectForm.repositoryUrl,
          branch: projectForm.branch
        })

        if (result.success) {
          console.log('项目注册成功，准备跳转到项目列表')
          // 显示成功Toast
          toast.success('项目注册成功', {
            message: '项目已成功注册，正在跳转到项目列表...',
            duration: 3000
          })
          // 注册成功，跳转到项目列表
          setTimeout(() => {
            router.push('/projects')
          }, 1000)
        } else {
          console.error('项目注册失败:', result.error)
          const errorMessage = result.error || '项目注册失败，请稍后再试'
          error.value = errorMessage
          // 显示错误Toast
          toast.error('项目注册失败', {
            message: errorMessage,
            duration: 5000
          })
        }
      } catch (err: any) {
        console.error('项目注册异常:', err)
        const errorMessage = err.message || '项目注册失败，请稍后再试'
        error.value = errorMessage
        // 显示错误Toast
        toast.error('项目注册异常', {
          message: errorMessage,
          duration: 5000
        })
      } finally {
        isSubmitting.value = false
      }
    }

    // 处理注册取消
    const handleRegistrationCancel = () => {
      const cancelled = cancelRegistration()
      if (cancelled) {
        error.value = ''
      }
    }

    return {
      projectForm,
      registerProject,
      error,
      repositoryLoading,
      urlValidation,
      fieldValidation,
      autoFilled,
      availableBranches,
      defaultBranch,
      onRepositoryUrlInput,
      validateRepositoryUrl,
      validateProjectName,
      validateBranch,
      useSuggestedUrl,
      registrationState,
      isFormValid,
      canSubmit,
      showDetailFields,
      isSubmitting,
      handleRegistrationCancel,
      handleRegistrationTimeout
    }
  }
})
</script>

<style scoped>
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.animate-button:hover:not(:disabled) {
  transform: translateY(-3px);
}

.animate-button:disabled {
  transform: none;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}
</style> 
<script setup lang="ts">
// Projects list view component
</script>

<template>
  <div class="py-6">
    <div class="flex justify-between items-center mb-6">
      <h1 class="text-2xl font-bold text-gray-900">项目列表</h1>
      <button class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">
        添加新项目
      </button>
    </div>
    
    <div class="bg-white rounded-lg shadow overflow-hidden">
      <!-- 筛选栏 -->
      <div class="p-4 border-b">
        <div class="flex flex-wrap gap-3">
          <div class="w-64">
            <label class="block text-sm font-medium text-gray-700 mb-1">项目名称</label>
            <input type="text" class="w-full border-gray-300 rounded-md shadow-sm" placeholder="搜索项目名称" />
          </div>
          <div class="w-40">
            <label class="block text-sm font-medium text-gray-700 mb-1">仓库类型</label>
            <select class="w-full border-gray-300 rounded-md shadow-sm">
              <option value="">全部</option>
              <option value="github">GitHub</option>
              <option value="gitlab">GitLab</option>
            </select>
          </div>
          <div class="w-40">
            <label class="block text-sm font-medium text-gray-700 mb-1">星级</label>
            <select class="w-full border-gray-300 rounded-md shadow-sm">
              <option value="">全部</option>
              <option value="5">5星</option>
              <option value="4">4星</option>
              <option value="3">3星</option>
              <option value="2">2星</option>
              <option value="1">1星</option>
            </select>
          </div>
        </div>
      </div>
      
      <!-- 项目列表 -->
      <div class="p-4">
        <div class="text-center py-10 text-gray-500">
          <p>暂无项目，请添加新项目</p>
        </div>
        
        <!-- 当有项目时，显示这部分
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">项目名称</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">仓库类型</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">星级</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">上次同步</th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">操作</th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="i in 5" :key="i">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">示例项目 {{i}}</div>
                    <div class="text-sm text-gray-500">example/repo-{{i}}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-100 text-blue-800">
                  GitHub
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex text-yellow-500">
                  <span v-for="star in i" :key="star">★</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                2023-06-15 13:45
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                <router-link :to="`/projects/${i}`" class="text-blue-600 hover:text-blue-900 mr-3">查看</router-link>
                <a href="#" class="text-indigo-600 hover:text-indigo-900 mr-3">同步</a>
                <a href="#" class="text-red-600 hover:text-red-900">删除</a>
              </td>
            </tr>
          </tbody>
        </table>
        -->
      </div>
      
      <!-- 分页 -->
      <div class="p-4 border-t flex justify-between items-center">
        <div>共 <span class="font-medium">0</span> 条记录</div>
        <div class="flex space-x-1">
          <button disabled class="px-3 py-1 rounded border bg-gray-100 text-gray-400">上一页</button>
          <button disabled class="px-3 py-1 rounded border bg-gray-100 text-gray-400">下一页</button>
        </div>
      </div>
    </div>
  </div>
</template> 
<template>
  <MainLayout>
    <div class="container mx-auto p-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-6 animate-fade-in gap-6">
        <!-- 左侧：标题和统计信息 -->
        <div class="flex flex-col lg:flex-row lg:items-center gap-6">
          <h1 class="text-3xl font-bold text-gray-800 gradient-text">项目概览</h1>

          <!-- 紧凑统计信息 - 水平排列 -->
          <div class="flex items-center gap-4">
            <div class="flex items-center bg-white rounded-lg shadow-sm px-3 py-2 border">
              <div class="flex items-center justify-center w-6 h-6 mr-2 bg-blue-100 rounded">
                <i class="fas fa-project-diagram text-blue-600 text-xs"></i>
              </div>
              <div class="text-center">
                <div class="text-sm font-bold text-gray-900">{{ projects.length }}</div>
                <div class="text-xs text-gray-600">总项目</div>
              </div>
            </div>

            <div class="flex items-center bg-white rounded-lg shadow-sm px-3 py-2 border">
              <div class="flex items-center justify-center w-6 h-6 mr-2 bg-green-100 rounded">
                <i class="fas fa-check-circle text-green-600 text-xs"></i>
              </div>
              <div class="text-center">
                <div class="text-sm font-bold text-gray-900">{{ completedProjects }}</div>
                <div class="text-xs text-gray-600">已完成</div>
              </div>
            </div>

            <div class="flex items-center bg-white rounded-lg shadow-sm px-3 py-2 border">
              <div class="flex items-center justify-center w-6 h-6 mr-2 bg-yellow-100 rounded">
                <i class="fas fa-clock text-yellow-600 text-xs"></i>
              </div>
              <div class="text-center">
                <div class="text-sm font-bold text-gray-900">{{ processingProjects }}</div>
                <div class="text-xs text-gray-600">处理中</div>
              </div>
            </div>

            <div class="flex items-center bg-white rounded-lg shadow-sm px-3 py-2 border">
              <div class="flex items-center justify-center w-6 h-6 mr-2 bg-purple-100 rounded">
                <i class="fas fa-star text-purple-600 text-xs"></i>
              </div>
              <div class="text-center">
                <div class="text-sm font-bold text-gray-900">{{ averageRating.toFixed(1) }}</div>
                <div class="text-xs text-gray-600">平均评分</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：智能接入按钮 -->
        <router-link
          to="/projects/new"
          class="btn-primary bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg flex items-center animate-button whitespace-nowrap"
        >
          <i class="fas fa-brain mr-2"></i> 智能接入
        </router-link>
      </div>

      <!-- 项目列表 -->
      <div v-if="loading" class="text-center py-10">
        <div
          class="spinner inline-block w-8 h-8 border-4 border-t-indigo-500 border-r-transparent border-b-indigo-500 border-l-transparent rounded-full animate-spin"
        ></div>
        <p class="mt-2 text-gray-600">加载项目中...</p>
      </div>

      <div
        v-else-if="error"
        class="bg-white rounded-lg shadow-md p-8 text-center"
      >
        <div class="text-red-400 mb-4">
          <i class="fas fa-exclamation-triangle text-6xl"></i>
        </div>
        <h2 class="text-2xl font-semibold text-gray-700 mb-2">加载失败</h2>
        <p class="text-gray-600 mb-6">{{ error }}</p>
        <div class="flex justify-center space-x-4">
          <button
            @click="fetchProjects"
            class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md inline-flex items-center"
          >
            <i class="fas fa-redo mr-2"></i> 重试
          </button>
          <button
            @click="forceRefresh"
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md inline-flex items-center"
          >
            <i class="fas fa-sync mr-2"></i> 强制刷新
          </button>
          <button
            @click="setMockProjectsData"
            class="bg-gray-600 hover:bg-gray-700 text-white py-2 px-4 rounded-md inline-flex items-center"
          >
            <i class="fas fa-database mr-2"></i> 使用演示数据
          </button>
        </div>
        <div class="mt-4 text-sm text-gray-500">
          <p>💡 提示：如果后端服务未启动，可以使用演示数据查看界面效果</p>
        </div>
      </div>

      <div
        v-else-if="projects.length === 0"
        class="bg-white rounded-lg shadow-md p-8 text-center"
      >
        <div class="text-gray-400 mb-4">
          <i class="fas fa-project-diagram text-6xl"></i>
        </div>
        <h2 class="text-2xl font-semibold text-gray-700 mb-2">暂无项目</h2>
        <p class="text-gray-600 mb-6">
          您当前没有任何项目，点击下方按钮创建您的第一个项目
        </p>
        <div class="flex justify-center space-x-4">
          <router-link
            to="/projects/new"
            class="bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-md inline-flex items-center"
          >
            <i class="fas fa-plus-circle mr-2"></i> 注册新项目
          </router-link>
          <button
            @click="forceRefresh"
            class="bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md inline-flex items-center"
          >
            <i class="fas fa-sync mr-2"></i> 刷新数据
          </button>
        </div>
      </div>

      <div v-else>
        <!-- 批量操作栏 -->
        <div v-if="showBatchActions" class="content-card bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 animate-slide-down">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <span class="text-sm text-blue-700 font-medium">
                已选择 {{ selectedProjects.length }} 个项目
              </span>
              <button
                @click="selectedProjects = []"
                class="text-sm text-blue-600 hover:text-blue-800 px-2 py-1 rounded-md hover:bg-blue-100 transition-colors"
              >
                取消选择
              </button>
            </div>
            <div class="flex items-center space-x-3">
              <button
                @click="batchAnalyze"
                class="btn btn-primary btn-sm"
                :disabled="loading"
              >
                <i class="fas fa-brain mr-2"></i>
                批量重新分析
              </button>
              <button
                @click="batchExport"
                class="btn btn-secondary btn-sm"
                :disabled="loading"
              >
                <i class="fas fa-download mr-2"></i>
                导出文档
              </button>
            </div>
          </div>
        </div>

        <!-- 单行搜索筛选区域 -->
        <div class="content-card bg-white shadow-md rounded-lg overflow-hidden mb-6 animate-slide-up">
          <div class="p-3">
            <!-- 响应式单行布局：搜索、状态筛选、类型筛选、排序、视图切换 -->
            <div class="filter-row flex items-center gap-2">
              <!-- 搜索框 - 自适应宽度 -->
              <div class="input-with-icon flex-1 min-w-0">
                <i class="fas fa-search input-icon"></i>
                <input
                  type="text"
                  v-model="searchQuery"
                  placeholder="搜索项目..."
                  class="form-input border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 bg-white text-sm"
                />
              </div>

              <!-- 状态筛选 - 固定宽度 -->
              <select
                v-model="statusFilter"
                class="form-select border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                style="width: 90px;"
              >
                <option value="">状态</option>
                <option :value="PROJECT_STATUS.PENDING_ANALYSIS">待分析</option>
                <option :value="PROJECT_STATUS.ANALYZING">分析中</option>
                <option :value="PROJECT_STATUS.ANALYSIS_COMPLETED">已完成</option>
                <option :value="PROJECT_STATUS.ANALYSIS_FAILED">失败</option>
                <option :value="PROJECT_STATUS.AVAILABLE">可用</option>
                <option :value="PROJECT_STATUS.UNAVAILABLE">不可用</option>
              </select>

              <!-- 类型筛选 - 固定宽度 -->
              <select
                v-model="typeFilter"
                class="form-select border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                style="width: 100px;"
              >
                <option value="">类型</option>
                <option :value="PROJECT_TYPE.JAVA">Java</option>
                <option :value="PROJECT_TYPE.JAVASCRIPT">JavaScript</option>
                <option :value="PROJECT_TYPE.PYTHON">Python</option>
                <option :value="PROJECT_TYPE.GO">Go</option>
                <option :value="PROJECT_TYPE.CSHARP">C#</option>
                <option :value="PROJECT_TYPE.KOTLIN">Kotlin</option>
                <option :value="PROJECT_TYPE.RUST">Rust</option>
                <option :value="PROJECT_TYPE.PHP">PHP</option>
                <option :value="PROJECT_TYPE.TYPESCRIPT">TypeScript</option>
                <option :value="PROJECT_TYPE.OTHER">其他</option>
              </select>

              <!-- 排序选择 - 固定宽度 -->
              <select
                v-model="sortOption"
                class="form-select border-gray-300 focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 text-sm"
                style="width: 90px;"
              >
                <option value="updated">最新</option>
                <option value="created">创建</option>
                <option value="name">名称</option>
                <option value="rating">评分</option>
              </select>

              <!-- 视图切换按钮 - 固定宽度 -->
              <div class="flex items-center bg-gray-100 rounded-lg p-1" style="width: 70px;">
                <button
                  @click="viewMode = 'cards'"
                  :class="[
                    'px-2 py-1 rounded-md text-sm font-medium transition-all duration-200 flex-1',
                    viewMode === 'cards'
                      ? 'bg-white text-indigo-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  ]"
                  title="卡片视图"
                >
                  <i class="fas fa-th text-xs"></i>
                </button>
                <button
                  @click="viewMode = 'list'"
                  :class="[
                    'px-2 py-1 rounded-md text-sm font-medium transition-all duration-200 flex-1',
                    viewMode === 'list'
                      ? 'bg-white text-indigo-600 shadow-sm'
                      : 'text-gray-600 hover:text-gray-800'
                  ]"
                  title="列表视图"
                >
                  <i class="fas fa-list text-xs"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量选择控制栏 -->
        <div v-if="filteredProjects.length > 0" class="mb-6 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="flex items-center">
              <input
                type="checkbox"
                :checked="selectedProjects.length === filteredProjects.length && filteredProjects.length > 0"
                :indeterminate="selectedProjects.length > 0 && selectedProjects.length < filteredProjects.length"
                @change="selectAllProjects"
                class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 mr-3"
              />
              <span class="text-sm text-gray-600">
                {{ selectedProjects.length > 0 ? `已选择 ${selectedProjects.length} 个项目` : '全选项目' }}
              </span>
            </div>
          </div>
          <div class="text-sm text-gray-500">
            共 {{ filteredProjects.length }} 个项目
          </div>
        </div>

        <!-- 项目卡片网格 -->
        <div v-if="viewMode === 'cards'" class="project-cards-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <!-- 项目卡片 -->
          <div
            v-for="project in filteredProjects"
            :key="project.id"
            class="project-card content-card cursor-pointer transform transition-all duration-200 hover:-translate-y-1 hover:shadow-lg animate-scale"
            :class="{ 'ring-2 ring-indigo-500 bg-indigo-50': selectedProjects.includes(project.id) }"
            @click="navigateToProject(project.id)"
          >
            <!-- 卡片头部 - 选择框和状态 -->
            <div class="flex items-start justify-between mb-4">
              <div class="flex items-center">
                <input
                  type="checkbox"
                  :checked="selectedProjects.includes(project.id)"
                  @change="toggleProjectSelection(project.id)"
                  @click.stop
                  class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500 mr-3"
                />
                <div class="project-status-badge">
                  <span
                    class="status-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium shadow-sm"
                    :class="getStatusBadgeClass(project.status)"
                  >
                    <i :class="getStatusIcon(project.status)" class="mr-1"></i>
                    {{ getStatusText(project.status) }}
                  </span>
                </div>
              </div>
              <div class="project-type-icon">
                <div
                  class="flex-shrink-0 h-12 w-12 rounded-xl flex items-center justify-center shadow-md cursor-help transition-transform hover:scale-105"
                  :class="getTypeIconBgClass(project.type)"
                  :title="getTypeText(project.type)"
                >
                  <i :class="getTypeIcon(project.type)" class="text-white text-xl"></i>
                </div>
              </div>
            </div>

            <!-- 项目标题和描述 -->
            <div class="mb-4">
              <h3 class="text-lg font-semibold text-gray-900 mb-2 line-clamp-1" :title="project.name">
                {{ project.name }}
              </h3>
              <p class="text-sm text-gray-600 line-clamp-3" :title="project.description">
                {{ project.description || "暂无项目描述" }}
              </p>
            </div>

            <!-- 评分信息 -->
            <div class="flex items-center mb-4">
              <div class="star-rating">
                <div class="flex items-center">
                  <i
                    v-for="n in 5"
                    :key="n"
                    :class="[
                      'fa-star text-sm mr-0.5',
                      n <= Math.round(project.rating || 0)
                        ? 'fas star-filled text-yellow-400'
                        : 'far star-empty text-gray-300',
                    ]"
                  ></i>
                  <span class="ml-2 text-sm text-gray-600 font-medium">{{ (project.rating || 0).toFixed(1) }}</span>
                </div>
              </div>
            </div>

            <!-- 最近分析时间 -->
            <div class="text-xs text-gray-400 mb-4">
              <i class="fas fa-search mr-1"></i>
              <span v-if="project.lastAnalyzedAt">
                最近分析：{{ formatDate(project.lastAnalyzedAt) }}
              </span>
              <span v-else class="text-gray-500">
                尚未分析
              </span>
            </div>

            <!-- 操作按钮 -->
            <div class="flex items-center justify-between pt-4 border-t border-gray-100">
              <div class="flex items-center space-x-2">
                <IconButton
                  icon="info-circle"
                  color="primary"
                  :to="`/projects/${project.id}`"
                  tooltip="架构详情"
                  size="sm"
                  @click.stop
                />
                <IconButton
                  icon="book-open"
                  color="success"
                  :to="`/projects/${project.id}/documents`"
                  tooltip="架构洞察"
                  size="sm"
                  @click.stop
                />
                <IconButton
                  icon="cog"
                  color="gray"
                  tooltip="项目设置"
                  size="sm"
                  @click.stop="openProjectSettings(project.id)"
                />
              </div>
              <IconButton
                icon="code-branch"
                color="gray"
                :tooltip="`访问仓库: ${project.repositoryUrl}`"
                size="sm"
                @click.stop="openRepository(project.repositoryUrl)"
              />
            </div>
          </div>
        </div>

        <!-- 项目列表视图 -->
        <div v-else-if="viewMode === 'list'" class="content-card bg-white shadow-md rounded-lg overflow-hidden">
          <table class="min-w-full divide-y divide-gray-200">
            <thead class="table-header">
              <tr>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                  <input
                    type="checkbox"
                    :checked="selectedProjects.length === filteredProjects.length && filteredProjects.length > 0"
                    :indeterminate="selectedProjects.length > 0 && selectedProjects.length < filteredProjects.length"
                    @change="selectAllProjects"
                    class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  项目名称
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  状态
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  类型
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  星级
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  最后更新
                </th>
                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  操作
                </th>
              </tr>
            </thead>
            <tbody class="bg-white divide-y divide-gray-200">
              <tr
                v-for="project in filteredProjects"
                :key="project.id"
                class="table-row animate-scale hover:bg-gray-50"
                :class="{ 'bg-blue-50': selectedProjects.includes(project.id) }"
                @click="navigateToProject(project.id)"
              >
                <td class="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    :checked="selectedProjects.includes(project.id)"
                    @change="toggleProjectSelection(project.id)"
                    @click.stop
                    class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10 bg-indigo-100 rounded-full flex items-center justify-center">
                      <i class="fas fa-project-diagram text-indigo-600"></i>
                    </div>
                    <div class="ml-4">
                      <div class="text-sm font-medium text-gray-900">{{ project.name }}</div>
                      <div class="text-sm text-gray-500 max-w-xs truncate" :title="project.description">
                        {{ project.description || "无项目描述" }}
                      </div>
                    </div>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="status-badge inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium shadow-sm"
                    :class="getStatusBadgeClass(project.status)"
                  >
                    <i :class="getStatusIcon(project.status)" class="mr-1"></i>
                    {{ getStatusText(project.status) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span
                    class="inline-flex items-center px-2 py-1 rounded-md text-xs font-medium"
                    :class="getTypeBadgeClass(project.type)"
                  >
                    <i :class="getTypeIcon(project.type)" class="mr-1"></i>
                    {{ getTypeText(project.type) }}
                  </span>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="star-rating text-sm">
                    <i
                      v-for="n in 5"
                      :key="n"
                      :class="[
                        'fa-star',
                        n <= Math.round(project.rating || 0)
                          ? 'fas star-filled text-yellow-400'
                          : 'far star-empty text-gray-300',
                      ]"
                    ></i>
                    <span class="ml-2 text-gray-500">{{ (project.rating || 0).toFixed(1) }}</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <div class="flex items-center text-sm text-gray-500">
                    <i class="far fa-clock text-gray-400 mr-2"></i>
                    <span>{{ formatDate(project.updatedAt) }}</span>
                  </div>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div class="flex items-center justify-end space-x-2">
                    <IconButton
                      icon="info-circle"
                      color="primary"
                      :to="`/projects/${project.id}`"
                      tooltip="架构详情"
                      size="sm"
                      @click.stop
                    />
                    <IconButton
                      icon="book-open"
                      color="success"
                      :to="`/projects/${project.id}/documents`"
                      tooltip="架构洞察"
                      size="sm"
                      @click.stop
                    />
                    <IconButton
                      icon="cog"
                      color="gray"
                      tooltip="项目设置"
                      size="sm"
                      @click.stop="openProjectSettings(project.id)"
                    />
                    <IconButton
                      icon="code-branch"
                      color="gray"
                      :tooltip="`访问仓库: ${project.repositoryUrl}`"
                      size="sm"
                      @click.stop="openRepository(project.repositoryUrl)"
                    />
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- 分页 -->
      <div
        v-if="projects.length > 0"
        class="flex items-center justify-between animate-fade-in mt-8 mb-6 bg-white p-4 rounded-lg shadow-sm"
      >
        <div class="text-sm text-gray-700">
          显示 <span class="font-medium">{{ paginationStart }}</span> 到
          <span class="font-medium">{{ paginationEnd }}</span> 共
          <span class="font-medium">{{ projects.length }}</span> 个项目
        </div>
        <div class="flex items-center space-x-2">
          <button
            class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button"
            :disabled="currentPage === 1"
            @click="currentPage--"
          >
            上一页
          </button>
          <button
            v-for="page in totalPages"
            :key="page"
            @click="currentPage = page"
            :class="[
              'px-3 py-1 rounded-md animate-button',
              currentPage === page
                ? 'bg-indigo-600 text-white hover:bg-indigo-700'
                : 'bg-white border border-gray-300 text-gray-500 hover:bg-gray-50',
            ]"
          >
            {{ page }}
          </button>
          <button
            class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button"
            :disabled="currentPage === totalPages"
            @click="currentPage++"
          >
            下一页
          </button>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { onMounted, ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useProjectStore } from "@/stores/project";
import { debugProjectsList, setMockProjectsData } from "@/utils/debug-api";
import { projectAPI } from "@/utils/api";
import MainLayout from "@/layouts/MainLayout.vue";
import IconButton from "@/components/IconButton.vue";

// 项目状态常量 - 与后端ProjectStatus枚举保持一致
const PROJECT_STATUS = {
  PENDING_ANALYSIS: 'PENDING_ANALYSIS',
  ANALYZING: 'ANALYZING',
  ANALYSIS_COMPLETED: 'ANALYSIS_COMPLETED',
  ANALYSIS_FAILED: 'ANALYSIS_FAILED',
  AVAILABLE: 'AVAILABLE',
  UNAVAILABLE: 'UNAVAILABLE'
} as const;

// 项目类型常量 - 与后端ProjectType枚举保持一致
const PROJECT_TYPE = {
  JAVA: 'JAVA',
  JAVASCRIPT: 'JAVASCRIPT',
  PYTHON: 'PYTHON',
  GO: 'GO',
  CSHARP: 'CSHARP',
  KOTLIN: 'KOTLIN',
  RUST: 'RUST',
  PHP: 'PHP',
  TYPESCRIPT: 'TYPESCRIPT',
  OTHER: 'OTHER'
} as const;

const router = useRouter();
const projectStore = useProjectStore();
const searchQuery = ref("");
const sortOption = ref("updated");
const currentPage = ref(1);
const itemsPerPage = ref(12); // 每页显示12个项目，保证3行显示

// 视图切换
const viewMode = ref<'cards' | 'list'>('cards'); // 默认卡片视图
const statusFilter = ref(''); // 状态筛选
const typeFilter = ref(''); // 类型筛选

// 批量操作相关
const selectedProjects = ref<number[]>([]);
const showBatchActions = computed(() => selectedProjects.value.length > 0);

const fetchProjects = async () => {
  try {
    console.log('🚀 ProjectList: 开始获取项目列表');
    await projectStore.fetchProjects();
    console.log('✅ ProjectList: 项目列表获取完成，项目数量:', projectStore.projects.length);
    console.log('📊 ProjectList: 项目数据:', projectStore.projects);
  } catch (err: any) {
    console.error('❌ ProjectList: 获取项目列表失败:', err);

    // 如果是网络错误，提供更友好的提示
    if (err.message?.includes('网络') || err.message?.includes('连接')) {
      console.log('🔄 ProjectList: 网络错误，尝试使用模拟数据');
    }
  }
};

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return "未知日期";

  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "short",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};



// 计算属性
const sortedProjects = computed(() => {
  const projects = [...projectStore.projects];

  // 排序
  switch (sortOption.value) {
    case "name":
      return projects.sort((a, b) => a.name.localeCompare(b.name));
    case "updated":
      return projects.sort(
        (a, b) =>
          new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
      );
    case "created":
      return projects.sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      );
    default:
      return projects;
  }
});

// 搜索过滤
const filteredProjects = computed(() => {
  let filtered = sortedProjects.value;

  // 搜索过滤
  if (searchQuery.value.trim()) {
    const query = searchQuery.value.toLowerCase().trim();
    filtered = filtered.filter(
      (project) =>
        project.name.toLowerCase().includes(query) ||
        (project.description &&
          project.description.toLowerCase().includes(query)) ||
        (project.repositoryUrl &&
          project.repositoryUrl.toLowerCase().includes(query))
    );
  }

  // 状态过滤
  if (statusFilter.value) {
    filtered = filtered.filter(project => project.status === statusFilter.value);
  }

  // 类型过滤
  if (typeFilter.value) {
    filtered = filtered.filter(project => project.type === typeFilter.value);
  }

  // 分页
  const start = (currentPage.value - 1) * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filtered.slice(start, end);
});

// 分页计算
const totalPages = computed(() => {
  const filteredCount = sortedProjects.value.filter((project) => {
    if (!searchQuery.value.trim()) return true;

    const query = searchQuery.value.toLowerCase().trim();
    return (
      project.name.toLowerCase().includes(query) ||
      (project.description &&
        project.description.toLowerCase().includes(query)) ||
      (project.repositoryUrl &&
        project.repositoryUrl.toLowerCase().includes(query))
    );
  }).length;

  return Math.max(1, Math.ceil(filteredCount / itemsPerPage.value));
});

// 统计数据计算
const completedProjects = computed(() => {
  return projectStore.projects.filter(p =>
    p.status === PROJECT_STATUS.ANALYSIS_COMPLETED || p.status === PROJECT_STATUS.AVAILABLE
  ).length;
});

const processingProjects = computed(() => {
  return projectStore.projects.filter(p =>
    p.status === PROJECT_STATUS.PENDING_ANALYSIS || p.status === PROJECT_STATUS.ANALYZING
  ).length;
});

const averageRating = computed(() => {
  const projects = projectStore.projects.filter(p => p.rating > 0);
  if (projects.length === 0) return 0;
  const total = projects.reduce((sum, p) => sum + p.rating, 0);
  return total / projects.length;
});

// 批量操作函数
const toggleProjectSelection = (projectId: number) => {
  const index = selectedProjects.value.indexOf(projectId);
  if (index > -1) {
    selectedProjects.value.splice(index, 1);
  } else {
    selectedProjects.value.push(projectId);
  }
};

const selectAllProjects = () => {
  if (selectedProjects.value.length === filteredProjects.value.length) {
    selectedProjects.value = [];
  } else {
    selectedProjects.value = filteredProjects.value.map(p => p.id);
  }
};

const batchAnalyze = async () => {
  if (selectedProjects.value.length === 0) return;

  const confirmed = confirm(`确定要重新分析选中的 ${selectedProjects.value.length} 个项目吗？`);
  if (!confirmed) return;

  try {
    // 这里应该调用批量分析的API
    console.log('批量分析项目:', selectedProjects.value);
    // await projectAPI.batchAnalyze(selectedProjects.value);
    selectedProjects.value = [];
    await fetchProjects();
  } catch (err) {
    console.error('批量分析失败:', err);
  }
};

// 项目状态相关方法 - 根据后端ProjectStatus枚举设计
const getStatusBadgeClass = (status: string) => {
  switch (status) {
    case PROJECT_STATUS.ANALYSIS_COMPLETED:
      return 'bg-gradient-to-r from-green-100 to-emerald-100 text-green-800 border border-green-200';
    case PROJECT_STATUS.AVAILABLE:
      return 'bg-gradient-to-r from-blue-100 to-cyan-100 text-blue-800 border border-blue-200';
    case PROJECT_STATUS.ANALYZING:
      return 'bg-gradient-to-r from-indigo-100 to-purple-100 text-indigo-800 border border-indigo-200';
    case PROJECT_STATUS.PENDING_ANALYSIS:
      return 'bg-gradient-to-r from-yellow-100 to-amber-100 text-yellow-800 border border-yellow-200';
    case PROJECT_STATUS.ANALYSIS_FAILED:
      return 'bg-gradient-to-r from-red-100 to-rose-100 text-red-800 border border-red-200';
    case PROJECT_STATUS.UNAVAILABLE:
      return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200';
    default:
      return 'bg-gradient-to-r from-gray-100 to-slate-100 text-gray-800 border border-gray-200';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case PROJECT_STATUS.ANALYSIS_COMPLETED:
      return 'fas fa-check-circle';
    case PROJECT_STATUS.AVAILABLE:
      return 'fas fa-eye';  // 可用状态使用眼睛图标
    case PROJECT_STATUS.ANALYZING:
      return 'fas fa-cog fa-spin';  // 分析中使用旋转齿轮
    case PROJECT_STATUS.PENDING_ANALYSIS:
      return 'fas fa-hourglass-half';  // 待分析使用沙漏
    case PROJECT_STATUS.ANALYSIS_FAILED:
      return 'fas fa-times-circle';  // 失败使用X圆圈
    case PROJECT_STATUS.UNAVAILABLE:
      return 'fas fa-eye-slash';  // 不可用使用斜线眼睛
    default:
      return 'fas fa-question-circle';
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case PROJECT_STATUS.ANALYSIS_COMPLETED:
      return '分析完成';
    case PROJECT_STATUS.AVAILABLE:
      return '可用';
    case PROJECT_STATUS.ANALYZING:
      return '分析中';
    case PROJECT_STATUS.PENDING_ANALYSIS:
      return '待分析';
    case PROJECT_STATUS.ANALYSIS_FAILED:
      return '分析失败';
    case PROJECT_STATUS.UNAVAILABLE:
      return '不可用';
    default:
      return '未知状态';
  }
};

// 项目类型相关方法
const getTypeBadgeClass = (type: string) => {
  switch (type) {
    case PROJECT_TYPE.JAVA:
      return 'bg-red-100 text-red-800 border border-red-200';
    case PROJECT_TYPE.JAVASCRIPT:
      return 'bg-yellow-100 text-yellow-800 border border-yellow-200';
    case PROJECT_TYPE.PYTHON:
      return 'bg-blue-100 text-blue-800 border border-blue-200';
    case PROJECT_TYPE.GO:
      return 'bg-cyan-100 text-cyan-800 border border-cyan-200';
    case PROJECT_TYPE.CSHARP:
      return 'bg-purple-100 text-purple-800 border border-purple-200';
    case PROJECT_TYPE.KOTLIN:
      return 'bg-orange-100 text-orange-800 border border-orange-200';
    case PROJECT_TYPE.RUST:
      return 'bg-amber-100 text-amber-800 border border-amber-200';
    case PROJECT_TYPE.PHP:
      return 'bg-indigo-100 text-indigo-800 border border-indigo-200';
    case PROJECT_TYPE.TYPESCRIPT:
      return 'bg-blue-100 text-blue-800 border border-blue-200';
    case PROJECT_TYPE.OTHER:
      return 'bg-gray-100 text-gray-800 border border-gray-200';
    default:
      return 'bg-gray-100 text-gray-800 border border-gray-200';
  }
};

const getTypeIcon = (type: string) => {
  switch (type) {
    case PROJECT_TYPE.JAVA:
      return 'fab fa-java';
    case PROJECT_TYPE.JAVASCRIPT:
      return 'fab fa-js-square';
    case PROJECT_TYPE.PYTHON:
      return 'fab fa-python';
    case PROJECT_TYPE.GO:
      return 'fab fa-golang';
    case PROJECT_TYPE.CSHARP:
      return 'fab fa-microsoft';
    case PROJECT_TYPE.KOTLIN:
      return 'fas fa-code';
    case PROJECT_TYPE.RUST:
      return 'fab fa-rust';
    case PROJECT_TYPE.PHP:
      return 'fab fa-php';
    case PROJECT_TYPE.TYPESCRIPT:
      return 'fab fa-js-square';
    case PROJECT_TYPE.OTHER:
      return 'fas fa-code';
    default:
      return 'fas fa-code';
  }
};

const getTypeText = (type: string) => {
  switch (type) {
    case PROJECT_TYPE.JAVA:
      return 'Java';
    case PROJECT_TYPE.JAVASCRIPT:
      return 'JavaScript';
    case PROJECT_TYPE.PYTHON:
      return 'Python';
    case PROJECT_TYPE.GO:
      return 'Go';
    case PROJECT_TYPE.CSHARP:
      return 'C#';
    case PROJECT_TYPE.KOTLIN:
      return 'Kotlin';
    case PROJECT_TYPE.RUST:
      return 'Rust';
    case PROJECT_TYPE.PHP:
      return 'PHP';
    case PROJECT_TYPE.TYPESCRIPT:
      return 'TypeScript';
    case PROJECT_TYPE.OTHER:
      return '其他';
    default:
      return '其他';
  }
};

// 项目类型图标背景样式 - 用于右上角大图标
const getTypeIconBgClass = (type: string) => {
  switch (type) {
    case PROJECT_TYPE.JAVA:
      return 'bg-gradient-to-br from-red-500 to-red-600';
    case PROJECT_TYPE.JAVASCRIPT:
      return 'bg-gradient-to-br from-yellow-500 to-yellow-600';
    case PROJECT_TYPE.PYTHON:
      return 'bg-gradient-to-br from-blue-500 to-blue-600';
    case PROJECT_TYPE.GO:
      return 'bg-gradient-to-br from-cyan-500 to-cyan-600';
    case PROJECT_TYPE.CSHARP:
      return 'bg-gradient-to-br from-purple-500 to-purple-600';
    case PROJECT_TYPE.KOTLIN:
      return 'bg-gradient-to-br from-orange-500 to-orange-600';
    case PROJECT_TYPE.RUST:
      return 'bg-gradient-to-br from-amber-500 to-amber-600';
    case PROJECT_TYPE.PHP:
      return 'bg-gradient-to-br from-indigo-500 to-indigo-600';
    case PROJECT_TYPE.TYPESCRIPT:
      return 'bg-gradient-to-br from-blue-500 to-blue-600';
    case PROJECT_TYPE.OTHER:
      return 'bg-gradient-to-br from-gray-500 to-gray-600';
    default:
      return 'bg-gradient-to-br from-gray-500 to-gray-600';
  }
};

// 卡片导航方法
const navigateToProject = (projectId: number) => {
  // 使用Vue Router导航到项目详情页
  router.push(`/projects/${projectId}`);
};

const batchExport = async () => {
  if (selectedProjects.value.length === 0) return;

  try {
    // 这里应该调用批量导出的API
    console.log('批量导出项目:', selectedProjects.value);
    // await projectAPI.batchExport(selectedProjects.value);
  } catch (err) {
    console.error('批量导出失败:', err);
  }
};

// 分页起始和结束
const paginationStart = computed(() => {
  return (currentPage.value - 1) * itemsPerPage.value + 1;
});

const paginationEnd = computed(() => {
  const end = currentPage.value * itemsPerPage.value;
  const filteredCount = sortedProjects.value.filter((project) => {
    if (!searchQuery.value.trim()) return true;

    const query = searchQuery.value.toLowerCase().trim();
    return (
      project.name.toLowerCase().includes(query) ||
      (project.description &&
        project.description.toLowerCase().includes(query)) ||
      (project.repositoryUrl &&
        project.repositoryUrl.toLowerCase().includes(query))
    );
  }).length;

  return Math.min(end, filteredCount);
});

// 开发环境错误处理辅助函数
const handleDevelopmentError = () => {
  if (process.env.NODE_ENV === 'development') {
    console.error('⚠️ 开发环境：获取项目列表失败，请检查后端服务是否启动');
    console.log('💡 调试提示:');
    console.log('  1. 确保后端服务已启动');
    console.log('  2. 检查API代理配置');
    console.log('  3. 查看网络面板的API请求');
  }
};

// 强制刷新数据
const forceRefresh = async () => {
  console.log('🔄 强制刷新数据...');
  projectStore.loading = true;
  projectStore.error = null;

  try {
    // 使用统一的API调用方式，确保路径一致性
    const data = await projectAPI.getProjects();

    if (data.data && Array.isArray(data.data)) {
      // 清空并重新填充数组
      projectStore.projects.splice(0, projectStore.projects.length, ...data.data);
      console.log('✅ 强制刷新成功，项目数量:', projectStore.projects.length);
    } else if (Array.isArray(data)) {
      // 直接返回数组的情况
      projectStore.projects.splice(0, projectStore.projects.length, ...data);
      console.log('✅ 强制刷新成功，项目数量:', projectStore.projects.length);
    }
  } catch (error) {
    console.error('❌ 强制刷新失败:', error);
    projectStore.error = '刷新失败';
  } finally {
    projectStore.loading = false;
  }
};

// 项目设置
const openProjectSettings = (projectId: number) => {
  console.log('打开项目设置:', projectId);
  // TODO: 实现项目设置功能
  alert(`项目设置功能开发中... 项目ID: ${projectId}`);
};

// 打开仓库地址
const openRepository = (repositoryUrl: string) => {
  if (repositoryUrl) {
    window.open(repositoryUrl, '_blank', 'noopener,noreferrer');
  }
};

// 生命周期钩子
onMounted(() => {
  // 清理旧的模拟数据，确保使用真实API
  localStorage.removeItem('mock_projects_data');

  // 初始化调试工具
  debugProjectsList();

  console.log('🚀 ProjectList组件已挂载，开始获取真实API数据...');
  fetchProjects();

  // 添加状态监听器用于调试
  setTimeout(() => {
    console.log('📊 组件状态检查:');
    console.log('  loading:', projectStore.loading);
    console.log('  error:', projectStore.error);
    console.log('  projects.length:', projectStore.projects.length);
    console.log('  projects:', projectStore.projects);
  }, 2000);
});

// 暴露给模板的响应式数据
const projects = computed(() => projectStore.projects);
const loading = computed(() => projectStore.loading);
const error = computed(() => projectStore.error);
</script>

<style scoped>
/* 项目列表页面特有样式 */
/* 所有通用样式已移至 src/styles/components.css */

/* 项目卡片网格布局 */
.project-cards-grid {
  gap: 1.5rem;
}

/* 项目卡片样式 */
.project-card {
  position: relative;
  padding: 1.5rem;
  border-radius: 0.75rem;
  background: white;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s ease-in-out;
}

.project-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border-color: #d1d5db;
}

/* 项目状态徽章 */
.project-status-badge {
  display: inline-flex;
  align-items: center;
}

/* 项目图标渐变背景 */
.project-icon .bg-gradient-to-br {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
}

/* 文本截断样式 */
.line-clamp-1 {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 星级评分样式 */
.star-rating .star-filled {
  color: #fbbf24;
}

.star-rating .star-empty {
  color: #d1d5db;
}

/* 响应式网格布局 */
@media (max-width: 640px) {
  .project-cards-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .project-card {
    padding: 1rem;
  }
}

@media (min-width: 641px) and (max-width: 1023px) {
  .project-cards-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) and (max-width: 1279px) {
  .project-cards-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (min-width: 1280px) {
  .project-cards-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 表单样式 */
.form-input {
  display: block;
  width: 100%;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #374151;
  background-color: #ffffff;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.5rem 2rem 0.5rem 0.75rem;
  font-size: 0.875rem;
  line-height: 1.25rem;
  color: #374151;
  background-color: #ffffff;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  appearance: none;
}

.form-select:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

/* 输入框图标样式 */
.input-with-icon {
  position: relative;
}

.input-with-icon .form-input {
  padding-left: 2.5rem;
}

.input-with-icon .input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
  z-index: 10;
}

/* 表格样式 */
.table-header {
  background-color: #f9fafb;
}

.table-row {
  transition: background-color 0.15s ease-in-out;
  cursor: pointer;
}

.table-row:hover {
  background-color: #f9fafb;
}

/* 紧凑统计信息样式 */
.compact-stat {
  transition: all 0.2s ease-in-out;
}

.compact-stat:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式优化 */
@media (max-width: 1024px) {
  .compact-stat {
    flex-direction: column;
    text-align: center;
  }

  .compact-stat .text-center {
    margin-top: 0.25rem;
  }
}

/* 状态badge增强样式 */
.status-badge {
  position: relative;
  overflow: hidden;
}

.status-badge::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.status-badge:hover::before {
  left: 100%;
}

/* 单行筛选区域样式优化 */
.filter-row {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* 确保在小屏幕上也能正常显示 */
@media (max-width: 768px) {
  .filter-row {
    gap: 0.25rem;
  }

  .filter-row select {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
  }

  .filter-row input {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
  }
}

/* 超小屏幕优化 */
@media (max-width: 640px) {
  .filter-row select {
    width: 70px !important;
    font-size: 0.75rem;
  }

  .filter-row .input-with-icon {
    min-width: 150px;
  }
}

/* 确保IconButton在所有状态下都有正确的指针样式 */
:deep(.cursor-pointer) {
  cursor: pointer !important;
}

:deep(.cursor-not-allowed) {
  cursor: not-allowed !important;
}

/* 表单样式 - 与任务队列保持一致 */
/* Custom select styles */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1f2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #4f46e5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

/* Custom input styles */
.form-input {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1f2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-input:focus {
  border-color: #4f46e5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

/* Input with icon styles */
.input-with-icon {
  position: relative;
}

.input-with-icon .form-input {
  padding-left: 2.5rem;
}

.input-with-icon .input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
}
</style>

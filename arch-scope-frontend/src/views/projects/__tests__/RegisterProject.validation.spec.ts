import { describe, it, expect, beforeEach, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import RegisterProject from '../RegisterProject.vue'

// Mock API
vi.mock('@/utils/api', () => ({
  gitRepositoryAPI: {
    getRepositoryDetails: vi.fn()
  },
  projectAPI: {
    checkRepositoryExists: vi.fn()
  }
}))

// Mock composables
vi.mock('@/composables/useProjectRegistration', () => ({
  useProjectRegistration: () => ({
    state: {
      isLoading: false,
      error: '',
      currentStepIndex: 0,
      canCancel: true,
      steps: []
    },
    registerProject: vi.fn(),
    cancelRegistration: vi.fn(),
    handleTimeout: vi.fn(),
    resetState: vi.fn()
  })
}))

describe('RegisterProject 表单验证', () => {
  let wrapper: any
  let router: any

  beforeEach(async () => {
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/projects', component: { template: '<div>Projects</div>' } }
      ]
    })

    const pinia = createPinia()

    wrapper = mount(RegisterProject, {
      global: {
        plugins: [router, pinia],
        stubs: {
          AppHeader: true,
          ProjectRegistrationLoader: true
        }
      }
    })

    await router.isReady()
  })

  describe('项目名称验证', () => {
    it('应该验证空项目名称', async () => {
      const nameInput = wrapper.find('#name')
      
      // 设置空值并触发blur事件
      await nameInput.setValue('')
      await nameInput.trigger('blur')

      expect(wrapper.vm.fieldValidation.name.isValid).toBe(false)
      expect(wrapper.vm.fieldValidation.name.message).toBe('项目名称不能为空')
    })

    it('应该验证项目名称长度过短', async () => {
      const nameInput = wrapper.find('#name')
      
      // 设置过短的名称
      await nameInput.setValue('a')
      await nameInput.trigger('blur')

      expect(wrapper.vm.fieldValidation.name.isValid).toBe(false)
      expect(wrapper.vm.fieldValidation.name.message).toBe('项目名称至少需要2个字符')
    })

    it('应该验证项目名称长度过长', async () => {
      const nameInput = wrapper.find('#name')
      
      // 设置过长的名称（超过100个字符）
      const longName = 'a'.repeat(101)
      await nameInput.setValue(longName)
      await nameInput.trigger('blur')

      expect(wrapper.vm.fieldValidation.name.isValid).toBe(false)
      expect(wrapper.vm.fieldValidation.name.message).toBe('项目名称不能超过100个字符')
    })

    it('应该接受有效的项目名称', async () => {
      const nameInput = wrapper.find('#name')
      
      // 设置有效名称
      await nameInput.setValue('测试项目')
      await nameInput.trigger('blur')

      expect(wrapper.vm.fieldValidation.name.isValid).toBe(true)
      expect(wrapper.vm.fieldValidation.name.message).toBe('')
    })
  })

  describe('仓库URL验证', () => {
    it('应该验证HTTPS URL格式', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 测试有效的HTTPS URL
      await urlInput.setValue('https://github.com/user/repo.git')
      await urlInput.trigger('blur')

      expect(wrapper.vm.urlValidation.isValid).toBe(true)
    })

    it('应该验证SSH URL格式', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 测试有效的SSH URL
      await urlInput.setValue('**************:user/repo.git')
      await urlInput.trigger('blur')

      expect(wrapper.vm.urlValidation.isValid).toBe(true)
    })

    it('应该拒绝无效的URL格式', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 测试无效URL
      await urlInput.setValue('invalid-url')
      await urlInput.trigger('blur')

      expect(wrapper.vm.urlValidation.isValid).toBe(false)
      expect(wrapper.vm.urlValidation.message).toContain('格式不正确')
    })
  })

  describe('分支验证', () => {
    it('应该验证空分支选择', async () => {
      // 先设置详细字段可见
      wrapper.vm.showDetailFields = true
      await wrapper.vm.$nextTick()

      const branchSelect = wrapper.find('#branch')
      
      // 设置空分支
      await branchSelect.setValue('')
      await branchSelect.trigger('change')

      expect(wrapper.vm.fieldValidation.branch.isValid).toBe(false)
      expect(wrapper.vm.fieldValidation.branch.message).toBe('请选择分支')
    })

    it('应该接受有效的分支选择', async () => {
      // 先设置详细字段可见
      wrapper.vm.showDetailFields = true
      await wrapper.vm.$nextTick()

      const branchSelect = wrapper.find('#branch')
      
      // 设置有效分支
      await branchSelect.setValue('main')
      await branchSelect.trigger('change')

      expect(wrapper.vm.fieldValidation.branch.isValid).toBe(true)
      expect(wrapper.vm.fieldValidation.branch.message).toBe('')
    })
  })

  describe('表单整体验证', () => {
    it('应该在所有字段有效时启用提交按钮', async () => {
      // 设置所有必需的状态
      wrapper.vm.showDetailFields = true
      wrapper.vm.urlValidation.isValid = true
      wrapper.vm.fieldValidation.name.isValid = true
      wrapper.vm.fieldValidation.branch.isValid = true
      
      // 设置表单数据
      wrapper.vm.projectForm.name = '测试项目'
      wrapper.vm.projectForm.repositoryUrl = 'https://github.com/user/repo.git'
      wrapper.vm.projectForm.branch = 'main'
      
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.canSubmit).toBe(true)
    })

    it('应该在字段无效时禁用提交按钮', async () => {
      // 设置无效状态
      wrapper.vm.showDetailFields = true
      wrapper.vm.urlValidation.isValid = false
      
      await wrapper.vm.$nextTick()

      expect(wrapper.vm.canSubmit).toBe(false)
    })
  })

  describe('错误提示显示', () => {
    it('应该显示项目名称错误提示', async () => {
      // 设置详细字段可见
      wrapper.vm.showDetailFields = true
      wrapper.vm.fieldValidation.name.isValid = false
      wrapper.vm.fieldValidation.name.message = '项目名称不能为空'
      
      await wrapper.vm.$nextTick()

      const errorMessage = wrapper.find('.text-red-600')
      expect(errorMessage.exists()).toBe(true)
      expect(errorMessage.text()).toContain('项目名称不能为空')
    })
  })
})

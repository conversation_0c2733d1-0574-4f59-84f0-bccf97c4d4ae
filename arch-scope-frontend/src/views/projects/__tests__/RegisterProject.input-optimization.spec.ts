import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createRouter, createWebHistory } from 'vue-router'
import { createPinia } from 'pinia'
import RegisterProject from '../RegisterProject.vue'

// Mock API
const mockGetRepositoryDetails = vi.fn()
vi.mock('@/utils/api', () => ({
  gitRepositoryAPI: {
    getRepositoryDetails: mockGetRepositoryDetails
  },
  projectAPI: {
    checkRepositoryExists: vi.fn()
  }
}))

// Mock composables
vi.mock('@/composables/useProjectRegistration', () => ({
  useProjectRegistration: () => ({
    state: {
      isLoading: false,
      error: '',
      currentStepIndex: 0,
      canCancel: true,
      steps: []
    },
    registerProject: vi.fn(),
    cancelRegistration: vi.fn(),
    handleTimeout: vi.fn(),
    resetState: vi.fn()
  })
}))

describe('RegisterProject 输入优化测试', () => {
  let wrapper: any
  let router: any

  beforeEach(async () => {
    router = createRouter({
      history: createWebHistory(),
      routes: [
        { path: '/', component: { template: '<div>Home</div>' } },
        { path: '/projects', component: { template: '<div>Projects</div>' } }
      ]
    })

    const pinia = createPinia()

    wrapper = mount(RegisterProject, {
      global: {
        plugins: [router, pinia],
        stubs: {
          ProjectRegistrationLoader: true
        }
      }
    })

    await router.isReady()
    
    // 重置mock
    mockGetRepositoryDetails.mockReset()
  })

  afterEach(() => {
    vi.clearAllTimers()
  })

  describe('输入事件优化', () => {
    it('输入时不应该调用后端接口', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 模拟用户输入
      await urlInput.setValue('https://github.com/user/repo.git')
      await urlInput.trigger('input')

      // 等待一段时间确保没有异步调用
      await new Promise(resolve => setTimeout(resolve, 100))

      // 验证没有调用后端接口
      expect(mockGetRepositoryDetails).not.toHaveBeenCalled()
    })

    it('输入时应该进行基本格式验证', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 输入无效URL
      await urlInput.setValue('invalid-url')
      await urlInput.trigger('input')

      // 应该显示格式错误
      expect(wrapper.vm.urlValidation.isValid).toBe(false)
      expect(wrapper.vm.urlValidation.message).toContain('格式不正确')
    })

    it('输入有效格式时应该清除错误信息但不标记为有效', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 先输入无效URL
      await urlInput.setValue('invalid-url')
      await urlInput.trigger('input')
      expect(wrapper.vm.urlValidation.message).toBeTruthy()

      // 再输入有效格式的URL
      await urlInput.setValue('https://github.com/user/repo.git')
      await urlInput.trigger('input')

      // 错误信息应该被清除，但不应该标记为有效（需要后端验证）
      expect(wrapper.vm.urlValidation.message).toBe('')
      expect(wrapper.vm.urlValidation.isValid).toBe(false)
    })

    it('输入时应该隐藏详细字段', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 先设置详细字段可见
      wrapper.vm.showDetailFields = true
      
      // 输入新URL
      await urlInput.setValue('https://github.com/user/new-repo.git')
      await urlInput.trigger('input')

      // 详细字段应该被隐藏
      expect(wrapper.vm.showDetailFields).toBe(false)
    })
  })

  describe('失焦事件验证', () => {
    it('失焦时应该调用后端接口验证', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 设置mock返回成功响应
      mockGetRepositoryDetails.mockResolvedValue({
        success: true,
        projectName: 'test-repo',
        defaultBranch: 'main',
        branches: ['main', 'develop']
      })

      // 输入有效URL并失焦
      await urlInput.setValue('https://github.com/user/repo.git')
      await urlInput.trigger('blur')

      // 等待异步操作完成
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      // 验证调用了后端接口
      expect(mockGetRepositoryDetails).toHaveBeenCalledWith('https://github.com/user/repo.git')
    })

    it('失焦时格式无效不应该调用后端接口', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 输入无效URL并失焦
      await urlInput.setValue('invalid-url')
      await urlInput.trigger('blur')

      // 等待一段时间确保没有异步调用
      await new Promise(resolve => setTimeout(resolve, 100))

      // 验证没有调用后端接口
      expect(mockGetRepositoryDetails).not.toHaveBeenCalled()
      expect(wrapper.vm.urlValidation.isValid).toBe(false)
    })

    it('失焦时空URL应该重置状态', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 先设置一些状态
      wrapper.vm.showDetailFields = true
      wrapper.vm.urlValidation.message = 'some message'
      
      // 清空URL并失焦
      await urlInput.setValue('')
      await urlInput.trigger('blur')

      // 验证状态被重置
      expect(wrapper.vm.urlValidation.message).toBe('')
      expect(wrapper.vm.urlValidation.isValid).toBe(false)
      expect(wrapper.vm.showDetailFields).toBe(false)
    })
  })

  describe('防抖机制', () => {
    it('快速输入时应该清除之前的定时器', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 快速输入多次
      await urlInput.setValue('https://github.com/user/repo1.git')
      await urlInput.trigger('input')
      
      await urlInput.setValue('https://github.com/user/repo2.git')
      await urlInput.trigger('input')
      
      await urlInput.setValue('https://github.com/user/repo3.git')
      await urlInput.trigger('input')

      // 等待一段时间
      await new Promise(resolve => setTimeout(resolve, 100))

      // 验证没有调用后端接口（因为只是input事件）
      expect(mockGetRepositoryDetails).not.toHaveBeenCalled()
    })

    it('失焦时应该清除防抖定时器并立即执行', async () => {
      const urlInput = wrapper.find('#repo_url')
      
      // 设置mock返回成功响应
      mockGetRepositoryDetails.mockResolvedValue({
        success: true,
        projectName: 'test-repo',
        defaultBranch: 'main',
        branches: ['main', 'develop']
      })

      // 输入后立即失焦（模拟用户快速操作）
      await urlInput.setValue('https://github.com/user/repo.git')
      await urlInput.trigger('input')
      await urlInput.trigger('blur')

      // 等待异步操作完成
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      // 验证调用了后端接口
      expect(mockGetRepositoryDetails).toHaveBeenCalledTimes(1)
    })
  })

  describe('建议URL功能', () => {
    it('使用建议URL时应该直接调用验证', async () => {
      // 设置建议URL
      wrapper.vm.urlValidation.suggestedUrl = 'https://github.com/user/repo.git'
      
      // 设置mock返回成功响应
      mockGetRepositoryDetails.mockResolvedValue({
        success: true,
        projectName: 'test-repo',
        defaultBranch: 'main',
        branches: ['main', 'develop']
      })

      // 使用建议URL
      await wrapper.vm.useSuggestedUrl()

      // 等待异步操作完成
      await wrapper.vm.$nextTick()
      await new Promise(resolve => setTimeout(resolve, 100))

      // 验证URL被设置且调用了后端接口
      expect(wrapper.vm.projectForm.repositoryUrl).toBe('https://github.com/user/repo.git')
      expect(mockGetRepositoryDetails).toHaveBeenCalledWith('https://github.com/user/repo.git')
    })
  })
})

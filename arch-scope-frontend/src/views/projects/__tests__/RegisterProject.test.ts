import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import RegisterProject from '../RegisterProject.vue'

// Mock the router
const mockRouter = {
  push: vi.fn()
}

// Mock the composables
vi.mock('@/composables/useProjectRegistration', () => ({
  useProjectRegistration: () => ({
    state: {
      isLoading: false,
      currentStepIndex: 0,
      canCancel: true,
      error: null
    },
    registerProject: vi.fn(),
    cancelRegistration: vi.fn(),
    handleTimeout: vi.fn(),
    resetState: vi.fn()
  })
}))

// Mock the API
vi.mock('@/utils/api', () => ({
  gitRepositoryAPI: {
    getRepositoryDetails: vi.fn().mockResolvedValue({
      success: true,
      projectName: 'test-project',
      description: 'Test project description',
      defaultBranch: 'main',
      branches: ['main', 'develop'],
      repositoryType: 'git',
      owner: 'testuser',
      repositoryName: 'test-project'
    })
  }
}))

// Mock the stores
vi.mock('@/stores/project', () => ({
  useProjectStore: () => ({
    registerProject: vi.fn()
  })
}))

describe('RegisterProject', () => {
  let wrapper: any
  let pinia: any

  beforeEach(() => {
    pinia = createPinia()
    wrapper = mount(RegisterProject, {
      global: {
        plugins: [pinia],
        mocks: {
          $router: mockRouter
        },
        stubs: {
          'router-link': true,
          'ProjectRegistrationLoader': true
        }
      }
    })
  })

  it('初始状态应该只显示仓库地址输入框', () => {
    // 检查仓库地址输入框存在
    const repoUrlInput = wrapper.find('#repo_url')
    expect(repoUrlInput.exists()).toBe(true)

    // 检查详细字段初始隐藏
    expect(wrapper.vm.showDetailFields).toBe(false)
    
    // 检查项目名称字段不可见
    const nameInput = wrapper.find('#name')
    expect(nameInput.exists()).toBe(false)
  })

  it('注册按钮初始状态应该被禁用', () => {
    const submitButton = wrapper.find('button[type="submit"]')
    expect(submitButton.exists()).toBe(true)
    expect(submitButton.attributes('disabled')).toBeDefined()
  })

  it('输入有效URL并失去焦点后应该显示详细字段', async () => {
    const repoUrlInput = wrapper.find('#repo_url')
    
    // 输入有效的Git URL
    await repoUrlInput.setValue('https://github.com/testuser/test-project')
    
    // 触发blur事件
    await repoUrlInput.trigger('blur')
    
    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    
    // 检查详细字段是否显示
    expect(wrapper.vm.showDetailFields).toBe(true)
  })

  it('仓库解析成功后应该自动填充项目信息', async () => {
    const repoUrlInput = wrapper.find('#repo_url')
    
    // 输入有效的Git URL
    await repoUrlInput.setValue('https://github.com/testuser/test-project')
    
    // 触发blur事件
    await repoUrlInput.trigger('blur')
    
    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    
    // 检查项目信息是否被自动填充
    expect(wrapper.vm.projectForm.name).toBe('test-project')
    expect(wrapper.vm.projectForm.description).toBe('Test project description')
    expect(wrapper.vm.projectForm.branch).toBe('main')
  })

  it('所有必填信息填写完整后注册按钮应该可点击', async () => {
    const repoUrlInput = wrapper.find('#repo_url')
    
    // 输入有效的Git URL
    await repoUrlInput.setValue('https://github.com/testuser/test-project')
    
    // 触发blur事件
    await repoUrlInput.trigger('blur')
    
    // 等待异步操作完成
    await wrapper.vm.$nextTick()
    
    // 设置URL验证为成功状态
    wrapper.vm.urlValidation.isValid = true
    
    await wrapper.vm.$nextTick()
    
    // 检查提交按钮是否可用
    expect(wrapper.vm.canSubmit).toBe(true)
  })

  it('清空URL后应该隐藏详细字段', async () => {
    const repoUrlInput = wrapper.find('#repo_url')

    // 先输入URL并显示详细字段
    await repoUrlInput.setValue('https://github.com/testuser/test-project')
    await repoUrlInput.trigger('blur')
    await wrapper.vm.$nextTick()

    // 清空URL
    await repoUrlInput.setValue('')
    await repoUrlInput.trigger('input')
    await wrapper.vm.$nextTick()

    // 检查详细字段是否隐藏
    expect(wrapper.vm.showDetailFields).toBe(false)
  })

  it('修改仓库地址时应该立即隐藏详细字段', async () => {
    const repoUrlInput = wrapper.find('#repo_url')

    // 先输入URL并显示详细字段
    await repoUrlInput.setValue('https://github.com/testuser/test-project')
    await repoUrlInput.trigger('blur')
    await wrapper.vm.$nextTick()

    // 假设详细字段已显示
    wrapper.vm.showDetailFields = true
    await wrapper.vm.$nextTick()

    // 修改URL（不为空）
    await repoUrlInput.setValue('https://github.com/testuser/another-project')
    await repoUrlInput.trigger('input')
    await wrapper.vm.$nextTick()

    // 检查详细字段是否立即隐藏
    expect(wrapper.vm.showDetailFields).toBe(false)
  })
})

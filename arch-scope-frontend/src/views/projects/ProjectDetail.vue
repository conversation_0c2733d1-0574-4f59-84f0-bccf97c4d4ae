<template>
  <MainLayout>
    <div class="container mx-auto p-6">
      <!-- 面包屑导航 -->
      <BreadcrumbNavigation :breadcrumbs="breadcrumbs" />

      <div class="flex items-center justify-between mb-6 animate-fade-in">
        <h1 class="text-3xl font-bold text-gray-800">项目详情</h1>
        <router-link
          to="/projects"
          class="text-indigo-600 hover:text-indigo-900 flex items-center animate-button"
        >
          <i class="fas fa-arrow-left mr-2"></i> 返回项目列表
        </router-link>
      </div>

    <div v-if="loading" class="flex justify-center items-center py-12">
      <div
        class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"
      ></div>
    </div>
    <div
      v-else-if="error"
      class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded"
    >
      {{ error }}
    </div>
    <div v-else-if="project" class="space-y-6">
      <div
        class="content-card bg-white shadow-md rounded-lg p-6 mb-6 animate-slide-up"
      >
        <div class="flex items-center justify-between mb-4">
          <div class="flex items-center">
            <div class="flex-shrink-0 h-16 w-16">
              <!-- 项目图标，如果有的话使用项目图标，否则使用默认图标 -->
              <img
                class="h-16 w-16 rounded-full"
                :src="
                  project.iconUrl ||
                  'https://images.unsplash.com/photo-1531297484001-80022131f5a1?auto=format&fit=crop&w=150&q=80'
                "
                alt="Project Icon"
              />
            </div>
            <div class="ml-4">
              <h2 class="text-2xl font-semibold text-gray-900">
                {{ project.name }}
              </h2>
              <p class="text-gray-600">
                仓库地址:
                <a
                  v-if="project.repositoryUrl"
                  :href="project.repositoryUrl"
                  target="_blank"
                  class="text-indigo-600 hover:text-indigo-900 underline animate-button"
                >
                  {{ project.repositoryUrl }}
                  <i class="fas fa-external-link-alt ml-1 text-xs"></i>
                </a>
                <span v-else class="text-gray-400">[仓库地址]</span>
              </p>
              <div class="text-lg text-yellow-500 mt-1">
                <!-- 根据项目评分显示星级 -->
                <template v-for="i in 5" :key="i">
                  <i
                    class="fas fa-star"
                    v-if="i <= (project.healthAssessment?.score || project.rating || 0)"
                  ></i>
                  <i class="far fa-star" v-else></i>
                </template>
              </div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div class="animate-fade-in" style="animation-delay: 0.2s">
            <h3 class="text-xl font-semibold text-gray-800 mb-3">文档网站</h3>
            <ul class="space-y-2">
              <li v-for="(doc, index) in docLinks" :key="index">
                <router-link
                  :to="doc.link"
                  class="text-indigo-600 hover:text-indigo-900 animate-button"
                >
                  <i :class="doc.icon + ' mr-2'"></i> {{ doc.title }}
                </router-link>
              </li>
              <li
                v-if="
                  project.docGenerations && project.docGenerations.length > 0
                "
              >
                <router-link
                  :to="`/projects/${project.id}/documents?version=${project.docGenerations[0].version}`"
                  class="text-green-600 hover:text-green-900 font-semibold animate-button"
                >
                  <i class="fas fa-globe mr-2"></i> 访问生成的网站
                </router-link>
              </li>
            </ul>
          </div>
          <div class="animate-fade-in" style="animation-delay: 0.4s">
            <h3 class="text-xl font-semibold text-gray-800 mb-3">统计信息</h3>
            <div class="bg-gray-50 rounded-lg p-4 animate-scale">
              <div class="flex items-center justify-between mb-2">
                <span class="text-gray-600">文件数:</span>
                <span class="text-gray-900 font-semibold">{{
                  project.stats?.fileCount || project.fileCount || project.analysisCount || "暂无数据"
                }}</span>
              </div>
              <div class="flex items-center justify-between mb-2">
                <span class="text-gray-600">代码行数:</span>
                <span class="text-gray-900 font-semibold">{{
                  project.linesOfCode || project.stats?.linesOfCode || "暂无数据"
                }}</span>
              </div>
              <div class="flex items-center justify-between mb-2">
                <span class="text-gray-600">贡献者数量:</span>
                <span class="text-gray-900 font-semibold">{{
                  project.contributorCount || "暂无数据"
                }}</span>
              </div>
              <div class="flex items-center justify-between">
                <span class="text-gray-600">最后更新时间:</span>
                <span class="text-gray-900 font-semibold">{{
                  formatDate(project.updatedAt)
                }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 文档生成记录 -->
      <div
        class="content-card bg-white shadow-md rounded-lg p-6 animate-slide-up"
      >
        <h3 class="text-xl font-semibold text-gray-800 mb-4">文档生成记录</h3>
        <div
          v-if="project.docGenerations && project.docGenerations.length > 0"
          class="space-y-3"
        >
          <div
            v-for="doc in project.docGenerations"
            :key="doc.id"
            class="border p-4 rounded-md hover:bg-gray-50 transition-colors animate-scale"
          >
            <div class="flex justify-between items-center">
              <div>
                <h4 class="font-medium">版本 {{ doc.version }}</h4>
                <p class="text-sm text-gray-500">
                  生成时间: {{ formatDate(doc.generatedAt) }}
                </p>
              </div>
              <router-link
                :to="`/projects/${project.id}/documents?version=${doc.version}`"
                class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors animate-button"
              >
                查看文档
              </router-link>
            </div>
          </div>
        </div>
        <div v-else class="text-center py-8 text-gray-500">
          暂无文档生成记录
        </div>
      </div>

      <!-- 最近任务 -->
      <div
        class="content-card bg-white shadow-md rounded-lg p-6 animate-slide-up"
      >
        <h3 class="text-xl font-semibold text-gray-800 mb-4">最近任务</h3>
        <ul class="space-y-3">
          <li
            v-for="(task, index) in recentTasks"
            :key="index"
            class="flex items-center p-3 bg-gray-50 rounded-md animate-scale"
          >
            <i
              :class="
                task.status === 'success'
                  ? 'fas fa-check-circle text-green-500 mr-3'
                  : 'fas fa-exclamation-circle text-red-500 mr-3'
              "
            ></i>
            <div class="flex-1">
              <span class="text-gray-900">{{ task.description }}</span>
              <div class="text-sm text-gray-500">
                {{ formatDate(task.time) }} -
                <span :class="task.status === 'success' ? 'text-green-600' : 'text-red-600'">
                  {{ task.status === "success" ? "成功" : "失败" }}
                </span>
              </div>
            </div>
          </li>
          <li
            v-if="!recentTasks || recentTasks.length === 0"
            class="text-center py-8 text-gray-500"
          >
            暂无任务记录
          </li>
        </ul>
      </div>
    </div>
    <div v-else class="text-center py-8 text-gray-500">未找到项目信息</div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from "vue";
import { useRoute } from "vue-router";
import { useProjectStore } from "@/stores/project";
import MainLayout from "@/layouts/MainLayout.vue";
import BreadcrumbNavigation, { type BreadcrumbItem } from "@/components/BreadcrumbNavigation.vue";
import request from "@/utils/request";

const route = useRoute();
const projectId = route.params.id as string;
const projectStore = useProjectStore();

// 使用store中的状态
const project = computed(() => projectStore.currentProject);
const loading = computed(() => projectStore.loading);
const error = computed(() => projectStore.error);

const recentTasks = ref<
  Array<{ time: string; description: string; status: string }>
>([]);

// 面包屑导航数据
const breadcrumbs = computed<BreadcrumbItem[]>(() => [
  { label: "项目列表", to: "/projects" },
  { label: project.value?.name || "项目详情" }
]);

// 文档链接数据
const docLinks = computed(() => {
  return [
    {
      title: "产品简介",
      icon: "fas fa-file-alt",
      link: `/projects/${projectId}/documents?type=intro`,
    },
    {
      title: "架构设计",
      icon: "fas fa-sitemap",
      link: `/projects/${projectId}/documents?type=architecture`,
    },
    {
      title: "用户手册",
      icon: "fas fa-book-open",
      link: `/projects/${projectId}/documents?type=manual`,
    },
    {
      title: "接口文档",
      icon: "fas fa-file-code",
      link: `/projects/${projectId}/documents?type=api`,
    },
    {
      title: "llms.txt",
      icon: "fas fa-file-alt",
      link: `/projects/${projectId}/documents?type=llms`,
    },
  ];
});

// 格式化日期
const formatDate = (dateString: string) => {
  if (!dateString) return "未知";
  const date = new Date(dateString);
  return date.toLocaleDateString("zh-CN", {
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });
};

// 获取项目详情
const fetchProjectDetail = async () => {
  try {
    console.log("开始获取项目详情，项目ID:", projectId);

    // 使用store获取项目详情
    const result = await projectStore.fetchProjectById(Number(projectId));

    console.log("项目详情获取结果:", result);
    console.log("当前项目状态:", projectStore.currentProject);

    // 获取最近任务
    await fetchRecentTasks();
  } catch (err: any) {
    console.error("获取项目详情失败:", err);

    // 如果API失败，显示错误信息而不是使用模拟数据
    console.error('获取项目详情失败，项目ID:', projectId);
    projectStore.error = '获取项目详情失败，请检查项目ID是否正确或稍后重试';

    // 开发环境错误处理：提供友好的错误信息
    if (process.env.NODE_ENV === 'development') {
      console.error('⚠️ 开发环境：获取项目详情失败，请检查后端服务是否启动');
      projectStore.error = `获取项目 ${projectId} 详情失败，请检查后端服务是否启动`;

      // 开发环境使用模拟数据
      const mockProject = {
        id: projectId,
        name: "示例项目",
        description: "这是一个示例项目",
        repositoryUrl: "https://github.com/example/project",
        projectType: "JAVASCRIPT",
        status: "ACTIVE",
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lineCount: 12580,
        contributorCount: 8,
        docGenerations: [{
          id: 1,
          version: "v1.0.0",
          generatedAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString()
        }]
      };

      projectStore.currentProject = mockProject as any;
      projectStore.error = null;
      await fetchRecentTasks();
    } else {
      projectStore.error = '获取项目详情失败，请稍后重试';
    }
  }
};

// 获取最近任务
const fetchRecentTasks = async () => {
  try {
    // 使用真实API获取项目相关任务
    const response = await request.get(`/v1/tasks?projectId=${projectId}&page=0&size=3`);

    // 处理不同的响应格式
    let tasksData = [];
    if (response.data?.content && Array.isArray(response.data.content)) {
      // Spring Boot分页格式
      tasksData = response.data.content;
    } else if (response.data && Array.isArray(response.data)) {
      // 包装在data字段中
      tasksData = response.data;
    } else if (Array.isArray(response.data)) {
      // 直接是数组
      tasksData = response.data;
    }

    // 转换为组件需要的格式
    recentTasks.value = tasksData.map((task: any) => ({
      time: task.createdAt || task.updatedAt || new Date().toISOString(),
      description: task.name || task.description || '任务处理',
      status: task.status === 'COMPLETED' ? 'success' :
              task.status === 'FAILED' ? 'failure' : 'processing'
    }));
  } catch (err) {
    console.error("获取最近任务失败:", err);
  }
};

onMounted(() => {
  fetchProjectDetail();
});
</script>

<style scoped>
/* 卡片样式 */
.content-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 动画效果 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-scale {
  transition: transform 0.3s ease;
}

.animate-scale:hover {
  transform: scale(1.02);
}

.animate-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.animate-button:hover {
  transform: translateY(-3px);
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>

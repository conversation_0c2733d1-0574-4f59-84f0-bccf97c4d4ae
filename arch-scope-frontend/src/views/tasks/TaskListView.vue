<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from "vue";
import { useRouter } from "vue-router";
import { useTaskStore } from "@/stores/task";
import { taskAPI, type Task } from "@/utils/api";
import { useTaskPolling, useTaskStatusNotification } from "@/composables/useTaskPolling";
import MainLayout from "@/layouts/MainLayout.vue";
import IconButton from "@/components/IconButton.vue";

// 响应式状态
const searchQuery = ref("");
const statusFilter = ref("all");
const projectFilter = ref<number | undefined>(undefined);
const loading = ref(false);
const error = ref<string | null>(null);
const currentPage = ref(0);
const pageSize = ref(10);
const totalTasks = ref(0);
const autoRefresh = ref(true);
const refreshInterval = ref<NodeJS.Timeout | null>(null);

const router = useRouter();
const taskStore = useTaskStore();

// 任务轮询和通知
const {
  isPolling,
  startPolling,
  stopPolling,
  needsPolling,
  getPollingInfo
} = useTaskPolling();

const {
  notifications,
  addNotification,
  removeNotification,
  clearNotifications
} = useTaskStatusNotification();

// 任务列表数据
const tasks = ref<Task[]>([]);
const previousTaskStates = ref<Map<number, string>>(new Map());

// 项目列表（用于筛选）
const projects = ref<Array<{ id: string; name: string; color: string }>>([]);

// 状态样式映射 - 与后端TaskStatus枚举对应
const getStatusClass = (status: string) => {
  const statusClasses = {
    PENDING: "status-waiting",
    PROCESSING: "status-processing",
    COMPLETED: "status-success",
    FAILED: "status-failed",
    PARTIAL_SUCCESS: "status-partial",
    CANCELLED: "status-cancelled",
    WAITING: "status-waiting",
    PAUSED: "status-paused",
    // 向后兼容
    pending: "status-waiting",
    processing: "status-processing",
    completed: "status-success",
    failed: "status-failed"
  };
  return statusClasses[status as keyof typeof statusClasses] || "status-waiting";
};

// 状态文本映射
const getStatusText = (status: string) => {
  const statusTexts = {
    PENDING: "等待分析",
    PROCESSING: "智能解析中",
    COMPLETED: "分析完成",
    FAILED: "分析失败",
    PARTIAL_SUCCESS: "部分完成",
    CANCELLED: "已停止",
    WAITING: "等待依赖",
    PAUSED: "已暂停",
    // 向后兼容
    pending: "等待分析",
    processing: "智能解析中",
    completed: "分析完成",
    failed: "分析失败"
  };
  return statusTexts[status as keyof typeof statusTexts] || "未知";
};

// 状态图标映射
const getStatusIcon = (status: string) => {
  const statusIcons = {
    PENDING: "fas fa-clock",
    PROCESSING: "fas fa-sync-alt fa-spin",
    COMPLETED: "fas fa-check-circle",
    FAILED: "fas fa-times-circle",
    PARTIAL_SUCCESS: "fas fa-exclamation-triangle",
    CANCELLED: "fas fa-ban",
    WAITING: "fas fa-clock",
    PAUSED: "fas fa-pause-circle",
    // 向后兼容
    pending: "fas fa-clock",
    processing: "fas fa-sync-alt fa-spin",
    completed: "fas fa-check-circle",
    failed: "fas fa-times-circle"
  };
  return statusIcons[status as keyof typeof statusIcons] || "fas fa-question-circle";
};

// 任务类型图标映射 - 与后端TaskType枚举对应
const getTypeIcon = (type: string) => {
  const typeIcons = {
    CODE_PARSE: "fas fa-code",
    DOC_GENERATE: "fas fa-file-alt",
    PROJECT_ANALYSIS: "fas fa-chart-line",
    PROJECT_INDEX: "fas fa-project-diagram",
    HEALTH_CHECK: "fas fa-heartbeat",
    // 向后兼容
    doc_generation: "fas fa-file-alt",
    code_analysis: "fas fa-code",
    project_init: "fas fa-project-diagram",
    repo_sync: "fas fa-sync"
  };
  return typeIcons[type as keyof typeof typeIcons] || "fas fa-tasks";
};

// 获取任务列表
const fetchTasks = async (showLoading = true) => {
  if (showLoading) loading.value = true;
  error.value = null;

  try {
    const response = await taskAPI.getTasks(
      projectFilter.value || undefined,
      currentPage.value,
      pageSize.value
    );

    // 适配后端Spring Data分页格式：content字段包含数据，totalElements包含总数
    const newTasks = response.content || [];

    // 检查任务状态变更并发送通知
    newTasks.forEach(task => {
      const previousStatus = previousTaskStates.value.get(task.id);
      if (previousStatus && previousStatus !== task.status) {
        addNotification(task.id, task.name, previousStatus, task.status);
      }
      previousTaskStates.value.set(task.id, task.status);
    });

    tasks.value = newTasks;
    totalTasks.value = response.totalElements || tasks.value.length;

    // 更新轮询任务列表
    updatePolling();
  } catch (err) {
    console.error('获取任务列表失败:', err);
    error.value = '获取任务列表失败，请稍后再试';
    tasks.value = [];
  } finally {
    if (showLoading) loading.value = false;
  }
};

// 更新轮询机制（优化版：统一轮询任务列表）
const updatePolling = () => {
  const activeTasks = tasks.value.filter(task => needsPolling(task));

  if (autoRefresh.value && activeTasks.length > 0) {
    // 使用优化后的轮询策略：直接刷新任务列表
    startPolling(async () => {
      await fetchTasks(false); // 静默刷新，不显示loading
    });
  } else {
    stopPolling();
  }
};

// 筛选后的任务列表
const filteredTasks = computed(() => {
  return tasks.value.filter((task) => {
    const matchesSearch = !searchQuery.value ||
      task.name.toLowerCase().includes(searchQuery.value.toLowerCase()) ||
      (task.projectName && task.projectName.toLowerCase().includes(searchQuery.value.toLowerCase()));

    const matchesStatus = statusFilter.value === "all" ||
      task.status.toLowerCase() === statusFilter.value.toLowerCase();

    return matchesSearch && matchesStatus;
  });
});

// 分页信息
const paginationInfo = computed(() => {
  const start = currentPage.value * pageSize.value + 1;
  const end = Math.min((currentPage.value + 1) * pageSize.value, totalTasks.value);
  return { start, end, total: totalTasks.value };
});

// 查看任务详情
const viewTaskDetails = (taskId: number) => {
  router.push(`/tasks/${taskId}`);
};

// 刷新任务列表
const refreshTasks = async () => {
  await fetchTasks(true);
};

// 自动刷新机制（已整合到轮询中，保留接口兼容性）
const startAutoRefresh = () => {
  // 自动刷新逻辑已整合到updatePolling中
  updatePolling();
};

const stopAutoRefresh = () => {
  // 停止轮询即可
  stopPolling();
};

// 切换自动刷新
const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value;
  if (autoRefresh.value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

// 任务操作
const retryTask = async (taskId: number) => {
  const task = tasks.value.find(t => t.id === taskId);
  if (!task) return;

  // 确认对话框
  if (!confirm(`确定要重试任务 "${task.name}" 吗？`)) {
    return;
  }

  try {
    loading.value = true;
    const response = await taskAPI.retryTask(taskId);

    // 更新本地任务状态
    const index = tasks.value.findIndex(t => t.id === taskId);
    if (index > -1) {
      tasks.value[index] = response;
    }

    // 添加成功通知
    addNotification(taskId, task.name, task.status, 'PENDING');

    // 重新获取任务列表以确保数据一致性
    await fetchTasks(false);

  } catch (err: any) {
    console.error('重试任务失败:', err);
    error.value = err.response?.data?.message || '重试任务失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

const cancelTask = async (taskId: number) => {
  const task = tasks.value.find(t => t.id === taskId);
  if (!task) return;

  // 确认对话框
  if (!confirm(`确定要取消任务 "${task.name}" 吗？\n\n取消后的任务无法恢复。`)) {
    return;
  }

  try {
    loading.value = true;
    const response = await taskAPI.cancelTask(taskId);

    if (response.success) {
      // 更新本地任务状态
      const index = tasks.value.findIndex(t => t.id === taskId);
      if (index > -1) {
        tasks.value[index].status = 'CANCELLED';
        tasks.value[index].updatedAt = new Date().toISOString();
      }

      // 添加成功通知
      addNotification(taskId, task.name, task.status, 'CANCELLED');

      // 重新获取任务列表以确保数据一致性
      await fetchTasks(false);
    } else {
      throw new Error(response.message || '取消任务失败');
    }

  } catch (err: any) {
    console.error('取消任务失败:', err);
    error.value = err.response?.data?.message || err.message || '取消任务失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

// 批量操作
const selectedTasks = ref<number[]>([]);

const toggleTaskSelection = (taskId: number) => {
  const index = selectedTasks.value.indexOf(taskId);
  if (index > -1) {
    selectedTasks.value.splice(index, 1);
  } else {
    selectedTasks.value.push(taskId);
  }
};

const selectAllTasks = () => {
  if (selectedTasks.value.length === filteredTasks.value.length) {
    selectedTasks.value = [];
  } else {
    selectedTasks.value = filteredTasks.value.map(task => task.id);
  }
};

const batchCancelTasks = async () => {
  if (selectedTasks.value.length === 0) return;

  if (!confirm(`确定要取消选中的 ${selectedTasks.value.length} 个任务吗？`)) {
    return;
  }

  try {
    loading.value = true;
    const promises = selectedTasks.value.map(taskId => taskAPI.cancelTask(taskId));
    await Promise.all(promises);

    selectedTasks.value = [];
    await fetchTasks(false);
  } catch (err) {
    console.error('批量取消任务失败:', err);
    error.value = '批量取消任务失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

// 分页操作
const goToPage = (page: number) => {
  currentPage.value = page;
  fetchTasks();
};

const nextPage = () => {
  if ((currentPage.value + 1) * pageSize.value < totalTasks.value) {
    goToPage(currentPage.value + 1);
  }
};

const prevPage = () => {
  if (currentPage.value > 0) {
    goToPage(currentPage.value - 1);
  }
};

// 监听自动刷新状态变化
watch(autoRefresh, (newValue) => {
  if (newValue) {
    updatePolling();
  } else {
    stopPolling();
  }
});

// 生命周期
onMounted(async () => {
  await fetchTasks();
  // 启动轮询机制
  updatePolling();
});

onUnmounted(() => {
  stopPolling();
  stopAutoRefresh();
});

// 任务统计计算
const taskStats = computed(() => {
  const stats = {
    total: tasks.value.length,
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    cancelled: 0
  };

  tasks.value.forEach(task => {
    switch (task.status) {
      case 'PENDING':
        stats.pending++;
        break;
      case 'PROCESSING':
        stats.processing++;
        break;
      case 'COMPLETED':
        stats.completed++;
        break;
      case 'FAILED':
        stats.failed++;
        break;
      case 'CANCELLED':
        stats.cancelled++;
        break;
    }
  });

  return stats;
});

// 快速操作函数
const clearCompletedTasks = async () => {
  const confirmed = confirm(`确定要清理所有已完成的任务吗？此操作不可撤销。`);
  if (!confirmed) return;

  try {
    loading.value = true;
    // 这里应该调用清理已完成任务的API
    console.log('清理已完成任务');
    // await taskAPI.clearCompleted();
    await fetchTasks();
  } catch (err) {
    console.error('清理任务失败:', err);
    error.value = '清理任务失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

const retryAllFailedTasks = async () => {
  const failedTasks = tasks.value.filter(t => t.status === 'FAILED');
  const confirmed = confirm(`确定要重试所有 ${failedTasks.length} 个失败的任务吗？`);
  if (!confirmed) return;

  try {
    loading.value = true;
    // 这里应该调用批量重试的API
    console.log('重试所有失败任务:', failedTasks.map(t => t.id));
    // await taskAPI.batchRetry(failedTasks.map(t => t.id));
    await fetchTasks();
  } catch (err) {
    console.error('批量重试失败:', err);
    error.value = '批量重试失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};

const pauseAllTasks = async () => {
  const processingTasks = tasks.value.filter(t => t.status === 'PROCESSING');
  const confirmed = confirm(`确定要暂停所有 ${processingTasks.length} 个正在处理的任务吗？`);
  if (!confirmed) return;

  try {
    loading.value = true;
    // 这里应该调用批量暂停的API
    console.log('暂停所有处理中任务:', processingTasks.map(t => t.id));
    // await taskAPI.batchPause(processingTasks.map(t => t.id));
    await fetchTasks();
  } catch (err) {
    console.error('批量暂停失败:', err);
    error.value = '批量暂停失败，请稍后再试';
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <MainLayout>
    <div class="container mx-auto p-6">
      <!-- 页面头部 -->
      <div class="flex items-center justify-between mb-6 animate-fade-in">
        <div class="flex items-center space-x-4">
          <h1 class="text-3xl font-bold text-gray-800 gradient-text">任务监控</h1>
          <div class="flex items-center space-x-4 text-sm">
            <div v-if="loading" class="status-indicator bg-blue-100 text-blue-700">
              <i class="fas fa-sync fa-spin mr-2"></i>
              <span>同步中</span>
            </div>
            <div v-else-if="isPolling" class="status-indicator live">
              <div class="w-2 h-2 bg-white rounded-full mr-2 animate-pulse"></div>
              <span>实时监控</span>
            </div>
            <div v-else-if="autoRefresh" class="status-indicator waiting">
              <div class="w-2 h-2 bg-gray-400 rounded-full mr-2"></div>
              <span>等待活跃</span>
            </div>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <!-- 小型状态卡片 -->
          <div class="flex items-center gap-4">
            <div class="flex items-center bg-white rounded-lg shadow-sm px-3 py-2 border">
              <div class="flex items-center justify-center w-6 h-6 mr-2 bg-gray-100 rounded">
                <i class="fas fa-tasks text-gray-600 text-xs"></i>
              </div>
              <div class="text-center">
                <div class="text-sm font-bold text-gray-900">{{ taskStats.total }}</div>
                <div class="text-xs text-gray-600">总任务</div>
              </div>
            </div>
            <div class="flex items-center bg-white rounded-lg shadow-sm px-3 py-2 border">
              <div class="flex items-center justify-center w-6 h-6 mr-2 bg-yellow-100 rounded">
                <i class="fas fa-clock text-yellow-600 text-xs"></i>
              </div>
              <div class="text-center">
                <div class="text-sm font-bold text-yellow-600">{{ taskStats.pending }}</div>
                <div class="text-xs text-gray-600">等待</div>
              </div>
            </div>
            <div class="flex items-center bg-white rounded-lg shadow-sm px-3 py-2 border">
              <div class="flex items-center justify-center w-6 h-6 mr-2 bg-blue-100 rounded">
                <i class="fas fa-cog text-blue-600 text-xs"></i>
              </div>
              <div class="text-center">
                <div class="text-sm font-bold text-blue-600">{{ taskStats.processing }}</div>
                <div class="text-xs text-gray-600">处理中</div>
              </div>
            </div>
            <div class="flex items-center bg-white rounded-lg shadow-sm px-3 py-2 border">
              <div class="flex items-center justify-center w-6 h-6 mr-2 bg-green-100 rounded">
                <i class="fas fa-check-circle text-green-600 text-xs"></i>
              </div>
              <div class="text-center">
                <div class="text-sm font-bold text-green-600">{{ taskStats.completed }}</div>
                <div class="text-xs text-gray-600">完成</div>
              </div>
            </div>
            <div class="flex items-center bg-white rounded-lg shadow-sm px-3 py-2 border">
              <div class="flex items-center justify-center w-6 h-6 mr-2 bg-red-100 rounded">
                <i class="fas fa-exclamation-triangle text-red-600 text-xs"></i>
              </div>
              <div class="text-center">
                <div class="text-sm font-bold text-red-600">{{ taskStats.failed }}</div>
                <div class="text-xs text-gray-600">失败</div>
              </div>
            </div>
          </div>
          <!-- 操作按钮 -->
          <div class="flex items-center space-x-3">
            <!-- 自动刷新开关 -->
            <div class="flex items-center space-x-2">
              <label
                for="auto-refresh-toggle"
                class="text-sm text-gray-600 font-medium cursor-pointer"
              >
                自动刷新
              </label>
              <button
                id="auto-refresh-toggle"
                @click="toggleAutoRefresh"
                :class="[
                  'relative inline-flex h-6 w-11 items-center rounded-full transition-all duration-200 ease-in-out',
                  'focus:outline-none focus:ring-2 focus:ring-offset-2',
                  autoRefresh
                    ? 'bg-blue-600 focus:ring-blue-500 shadow-blue-600/25'
                    : 'bg-gray-200 focus:ring-gray-500 hover:bg-gray-300'
                ]"
                :aria-pressed="autoRefresh"
                :aria-label="autoRefresh ? '关闭自动刷新' : '开启自动刷新'"
              >
                <span
                  :class="[
                    'inline-block h-4 w-4 transform rounded-full bg-white transition-all duration-200 ease-in-out shadow-sm',
                    autoRefresh ? 'translate-x-6' : 'translate-x-1'
                  ]"
                />
              </button>
            </div>
            <!-- 刷新按钮 -->
            <button
              @click="refreshTasks"
              :disabled="loading"
              data-cy="refresh-button"
              :class="[
                'btn btn-refresh animate-button',
                { 'btn-loading': loading }
              ]"
              :aria-label="loading ? '正在刷新任务列表' : '刷新任务列表'"
            >
              <i
                :class="[
                  'fas fa-sync mr-2',
                  { 'fa-spin': loading }
                ]"
                aria-hidden="true"
              ></i>
              <span>{{ loading ? '同步中...' : '同步状态' }}</span>
            </button>
          </div>
        </div>
      </div>


      <!-- 错误提示 -->
      <div v-if="error" class="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg animate-slide-down">
        <div class="flex items-center">
          <i class="fas fa-exclamation-triangle text-red-500 mr-2"></i>
          <span class="text-red-700">{{ error }}</span>
          <button
            @click="error = null"
            class="ml-auto text-red-500 hover:text-red-700 p-1 rounded-md hover:bg-red-50 transition-colors"
            aria-label="关闭错误提示"
          >
            <i class="fas fa-times"></i>
          </button>
        </div>
      </div>

      <!-- 任务状态通知 -->
      <div v-if="notifications.length > 0" class="fixed top-4 right-4 z-50 space-y-2">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="[
            'p-4 rounded-lg shadow-lg border-l-4 animate-slide-down',
            {
              'bg-green-50 border-green-400': notification.type === 'success',
              'bg-red-50 border-red-400': notification.type === 'error',
              'bg-blue-50 border-blue-400': notification.type === 'info',
              'bg-yellow-50 border-yellow-400': notification.type === 'warning'
            }
          ]"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <i
                :class="[
                  'mr-2',
                  {
                    'fas fa-check-circle text-green-500': notification.type === 'success',
                    'fas fa-times-circle text-red-500': notification.type === 'error',
                    'fas fa-info-circle text-blue-500': notification.type === 'info',
                    'fas fa-exclamation-triangle text-yellow-500': notification.type === 'warning'
                  }
                ]"
              ></i>
              <div>
                <p class="text-sm font-medium text-gray-900">
                  {{ notification.taskName }}
                </p>
                <p class="text-xs text-gray-600">
                  状态从 "{{ getStatusText(notification.oldStatus) }}"
                  变更为 "{{ getStatusText(notification.newStatus) }}"
                </p>
              </div>
            </div>
            <button
              @click="removeNotification(notification.id)"
              class="text-gray-400 hover:text-gray-600"
            >
              <i class="fas fa-times"></i>
            </button>
          </div>
        </div>
      </div>



      <!-- 主要内容卡片 -->
      <div
        class="content-card bg-white shadow-md rounded-lg overflow-hidden mb-6 animate-slide-up"
      >
        <!-- 卡片头部 -->
        <div class="p-6 bg-gradient-to-r from-gray-50 to-indigo-50 border-b border-gray-200">
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
            <div class="flex items-center">
              <div class="flex items-center justify-center w-10 h-10 bg-indigo-100 rounded-lg mr-3">
                <i class="fas fa-list-ul text-indigo-600 text-lg"></i>
              </div>
              <div>
                <h2 class="text-xl font-bold text-gray-800">任务列表</h2>
                <p class="text-sm text-gray-600">
                  共 {{ totalTasks }} 个任务，显示 {{ filteredTasks.length }} 个
                </p>
              </div>
            </div>
            <div class="flex flex-col sm:flex-row items-stretch sm:items-center space-y-3 sm:space-y-0 sm:space-x-3">
              <!-- 搜索框 -->
              <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <i class="fas fa-search text-gray-400"></i>
                </div>
                <input
                  v-model="searchQuery"
                  data-cy="search-input"
                  type="text"
                  placeholder="搜索任务或项目..."
                  class="pl-10 pr-4 py-2.5 w-full sm:w-64 border-2 border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 bg-white"
                />
                <div v-if="searchQuery" class="absolute inset-y-0 right-0 pr-3 flex items-center">
                  <button
                    @click="searchQuery = ''"
                    class="text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    <i class="fas fa-times"></i>
                  </button>
                </div>
              </div>
              <!-- 状态筛选 -->
              <div class="relative">
                <select
                  v-model="statusFilter"
                  data-cy="status-filter"
                  class="appearance-none bg-white border-2 border-gray-200 rounded-xl px-4 py-2.5 pr-8 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 cursor-pointer"
                >
                  <option value="all">所有状态</option>
                  <option value="PENDING">等待分析</option>
                  <option value="PROCESSING">智能解析中</option>
                  <option value="COMPLETED">分析完成</option>
                  <option value="FAILED">分析失败</option>
                  <option value="CANCELLED">已停止</option>
                </select>
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <i class="fas fa-chevron-down text-gray-400"></i>
                </div>
              </div>
              <!-- 项目筛选 -->
              <div class="relative">
                <select
                  v-model="projectFilter"
                  @change="fetchTasks()"
                  data-cy="project-filter"
                  class="appearance-none bg-white border-2 border-gray-200 rounded-xl px-4 py-2.5 pr-8 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-all duration-300 cursor-pointer"
                >
                  <option :value="undefined">所有项目</option>
                  <option v-for="project in projects" :key="project.id" :value="project.id">
                    {{ project.name }}
                  </option>
                </select>
                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                  <i class="fas fa-chevron-down text-gray-400"></i>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 批量操作栏 -->
        <div v-if="selectedTasks.length > 0" class="p-3 bg-blue-50 border-b border-blue-200 flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <span class="text-sm text-blue-700">
              已选择 {{ selectedTasks.length }} 个任务
            </span>
            <button
              @click="selectedTasks = []"
              class="text-sm text-blue-600 hover:text-blue-800 px-2 py-1 rounded-md hover:bg-blue-50 transition-colors"
              aria-label="取消选择"
            >
              取消选择
            </button>
          </div>
          <div class="flex items-center space-x-2">
            <button
              @click="batchCancelTasks"
              class="btn btn-danger btn-sm"
              :disabled="loading"
              aria-label="批量取消选中的任务"
            >
              <i class="fas fa-times mr-1" aria-hidden="true"></i>
              批量取消
            </button>
          </div>
        </div>

        <!-- 任务表格 -->
        <div v-if="loading" class="p-8 text-center">
          <i class="fas fa-sync fa-spin text-4xl text-gray-400 mb-4"></i>
          <p class="text-gray-500">正在加载任务列表...</p>
        </div>

        <div v-else-if="tasks.length === 0" class="p-8 text-center">
          <i class="fas fa-inbox text-4xl text-gray-400 mb-4"></i>
          <p class="text-gray-500 mb-2">暂无任务</p>
        </div>

        <div v-else-if="filteredTasks.length === 0" class="p-8 text-center">
          <i class="fas fa-search text-4xl text-gray-400 mb-4"></i>
          <p class="text-gray-500 mb-2">没有符合条件的任务</p>
          <p class="text-sm text-gray-400">
            尝试调整搜索条件或筛选器
          </p>
        </div>

        <table v-else data-cy="task-table" class="min-w-full divide-y divide-gray-200">
          <thead class="table-header">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-12">
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    :checked="selectedTasks.length === filteredTasks.length && filteredTasks.length > 0"
                    :indeterminate="selectedTasks.length > 0 && selectedTasks.length < filteredTasks.length"
                    @change="selectAllTasks"
                    class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </div>
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <i class="fas fa-hashtag mr-2"></i>
                  任务ID
                </div>
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <i class="fas fa-project-diagram mr-2"></i>
                  关联项目
                </div>
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <i class="fas fa-cog mr-2"></i>
                  任务类型
                </div>
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <i class="fas fa-flag mr-2"></i>
                  状态
                </div>
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <i class="fas fa-play mr-2"></i>
                  开始时间
                </div>
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                <div class="flex items-center">
                  <i class="fas fa-stop mr-2"></i>
                  结束时间
                </div>
              </th>
              <th class="relative px-6 py-3">
                <div class="flex items-center justify-center">
                  <i class="fas fa-ellipsis-h text-gray-400"></i>
                </div>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr
              v-for="task in filteredTasks"
              :key="task.id"
              data-cy="task-row"
              class="table-row animate-scale hover:bg-gray-50 transition-colors duration-200"
              :class="{ 'bg-blue-50': selectedTasks.includes(task.id) }"
            >
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <input
                    type="checkbox"
                    :checked="selectedTasks.includes(task.id)"
                    @change="toggleTaskSelection(task.id)"
                    @click.stop
                    class="rounded border-gray-300 text-indigo-600 focus:ring-indigo-500"
                  />
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm font-medium text-gray-900">
                  <i class="fas fa-tasks text-gray-400 mr-2"></i>
                  <span data-cy="task-id" class="font-mono">{{ task.id }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-900">
                  <span class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"></span>
                  <span data-cy="task-project">{{ task.projectName || '未知项目' }}</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-900">
                  <i :class="`${getTypeIcon(task.type)} text-gray-400 mr-2`"></i>
                  <div class="flex flex-col">
                    <span data-cy="task-type" class="font-medium">{{ task.name }}</span>
                    <span class="text-xs text-gray-500">{{ task.type }}</span>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  data-cy="task-status"
                  :class="`status-badge ${getStatusClass(task.status)} flex items-center`"
                >
                  <i :class="`${getStatusIcon(task.status)} mr-2`"></i>
                  {{ getStatusText(task.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-500">
                  <i class="far fa-clock text-gray-400 mr-2"></i>
                  <span data-cy="task-start-time">
                    {{ task.createdAt ? new Date(task.createdAt).toLocaleString('zh-CN') : '-' }}
                  </span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center text-sm text-gray-500">
                  <template v-if="task.updatedAt && (task.status === 'COMPLETED' || task.status === 'FAILED' || task.status === 'CANCELLED')">
                    <i class="far fa-clock text-gray-400 mr-2"></i>
                    <span>{{ new Date(task.updatedAt).toLocaleString('zh-CN') }}</span>
                  </template>
                  <span v-else>-</span>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <div class="flex items-center justify-end space-x-2">
                  <!-- 重试任务 -->
                  <IconButton
                    v-if="task.status === 'FAILED'"
                    icon="redo"
                    color="success"
                    :disabled="loading"
                    :tooltip="loading ? '正在处理...' : '重新解析'"
                    :aria-label="`重试任务: ${task.name}`"
                    @click.stop="retryTask(task.id)"
                  />
                  <!-- 取消任务 -->
                  <IconButton
                    v-if="task.status === 'PROCESSING' || task.status === 'PENDING'"
                    icon="times"
                    color="danger"
                    :disabled="loading"
                    :tooltip="loading ? '正在处理...' : '停止分析'"
                    :aria-label="`取消任务: ${task.name}`"
                    @click.stop="cancelTask(task.id)"
                  />
                  <!-- 查看详情 -->
                  <IconButton
                    icon="info-circle"
                    color="primary"
                    :tooltip="`查看任务详情: ${task.name}`"
                    :aria-label="`查看任务详情: ${task.name}`"
                    @click.stop="viewTaskDetails(task.id)"
                  />
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页信息 -->
      <div v-if="filteredTasks.length > 0" class="flex items-center justify-between animate-fade-in">
        <div data-cy="pagination-info" class="text-sm text-gray-700">
          显示 <span class="font-medium">{{ paginationInfo.start }}</span> 到
          <span class="font-medium">{{ paginationInfo.end }}</span> 共
          <span class="font-medium">{{ paginationInfo.total }}</span> 个任务
        </div>
        <div class="flex items-center space-x-2">
          <button
            @click="prevPage"
            :disabled="currentPage === 0"
            data-cy="pagination-prev"
            class="btn btn-outline btn-sm animate-button"
            aria-label="上一页"
          >
            <i class="fas fa-chevron-left mr-1" aria-hidden="true"></i>上一页
          </button>

          <!-- 页码显示 -->
          <div class="flex items-center space-x-1">
            <template v-for="page in Math.ceil(totalTasks / pageSize)" :key="page">
              <button
                v-if="page <= 5 || Math.abs(page - currentPage - 1) <= 2 || page > Math.ceil(totalTasks / pageSize) - 2"
                @click="goToPage(page - 1)"
                :class="[
                  'px-3 py-1 rounded-md text-sm animate-button',
                  currentPage === page - 1
                    ? 'bg-indigo-600 text-white'
                    : 'bg-white border border-gray-300 text-gray-500 hover:bg-gray-50'
                ]"
                data-cy="pagination-page"
              >
                {{ page }}
              </button>
              <span v-else-if="page === 6 && currentPage < 3" class="px-2 text-gray-400">...</span>
            </template>
          </div>

          <button
            @click="nextPage"
            :disabled="(currentPage + 1) * pageSize >= totalTasks"
            data-cy="pagination-next"
            class="btn btn-outline btn-sm animate-button"
            aria-label="下一页"
          >
            下一页<i class="fas fa-chevron-right ml-1" aria-hidden="true"></i>
          </button>
        </div>
      </div>

      <!-- 快速操作面板 -->
      <div class="content-card p-6 mt-6 animate-slide-up">
        <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div>
            <h3 class="text-lg font-semibold text-gray-800 mb-2">快速操作</h3>
            <p class="text-sm text-gray-600">管理和监控您的分析任务</p>
          </div>
          <div class="flex flex-wrap gap-3">
            <button
              @click="clearCompletedTasks"
              class="btn btn-outline btn-sm"
              :disabled="loading || taskStats.completed === 0"
            >
              <i class="fas fa-broom mr-2"></i>
              清理已完成
            </button>
            <button
              @click="retryAllFailedTasks"
              class="btn btn-secondary btn-sm"
              :disabled="loading || taskStats.failed === 0"
            >
              <i class="fas fa-redo mr-2"></i>
              重试失败任务
            </button>
            <button
              @click="pauseAllTasks"
              class="btn btn-danger btn-sm"
              :disabled="loading || taskStats.processing === 0"
            >
              <i class="fas fa-pause mr-2"></i>
              暂停所有任务
            </button>
          </div>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<style scoped>
/* 与项目其他页面保持一致的样式 */
:root {
  --primary-color: #4f46e5; /* Indigo-600 */
  --primary-hover: #4338ca; /* Indigo-700 */
  --sidebar-bg: #1e293b; /* Slate-800 */
  --sidebar-header-bg: #0f172a; /* Slate-900 */
}

.content-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06), 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
  transform: translateY(-1px);
}

/* 按钮样式 - 与项目其他页面保持一致 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  border: 1px solid transparent;
  transition: all 0.2s ease;
  cursor: pointer;
  text-decoration: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.btn:hover:not(:disabled) {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.btn-sm {
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--primary-hover);
  box-shadow: 0 4px 12px rgba(79, 70, 229, 0.25);
}

.btn-danger {
  background-color: #dc2626;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background-color: #b91c1c;
  box-shadow: 0 4px 12px rgba(220, 38, 38, 0.25);
}

.btn-outline {
  background-color: white;
  color: #374151;
  border-color: #d1d5db;
}

.btn-outline:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.btn-refresh {
  background-color: #059669;
  color: white;
}

.btn-refresh:hover:not(:disabled) {
  background-color: #047857;
  box-shadow: 0 4px 12px rgba(5, 150, 105, 0.25);
}

.btn-loading {
  opacity: 0.75;
  cursor: not-allowed;
}

/* Table styles */
.table-header {
  background-color: #f8fafc; /* Slate-50 */
  font-weight: 600;
  text-transform: uppercase;
  font-size: 0.75rem;
  letter-spacing: 0.05em;
  color: #64748b; /* Slate-500 */
}

.table-row {
  transition: all 0.2s ease;
}

.table-row:hover {
  background-color: #f1f5f9; /* Slate-100 */
}

/* 状态徽章样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

.status-processing {
  background-color: #fef3c7; /* Amber-100 */
  color: #92400e; /* Amber-800 */
}

.status-success {
  background-color: #dcfce7; /* Green-100 */
  color: #166534; /* Green-800 */
}

.status-waiting {
  background-color: #dbeafe; /* Blue-100 */
  color: #1e40af; /* Blue-800 */
}

.status-failed {
  background-color: #fee2e2; /* Red-100 */
  color: #991b1b; /* Red-800 */
}

.status-partial {
  background-color: #fef3c7; /* Amber-100 */
  color: #d97706; /* Amber-600 */
}

.status-cancelled {
  background-color: #f3f4f6; /* Gray-100 */
  color: #6b7280; /* Gray-500 */
}

.status-paused {
  background-color: #e0e7ff; /* Indigo-100 */
  color: #5b21b6; /* Purple-800 */
}

/* Custom select styles */
select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1f2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-select:focus {
  border-color: #4f46e5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}

/* Custom input styles */
.form-input {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1f2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.form-input:focus {
  border-color: #4f46e5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25), 0 1px 3px rgba(0, 0, 0, 0.1);
}

.form-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

/* Input with icon styles */
.input-with-icon {
  position: relative;
}

.input-with-icon .form-input {
  padding-left: 2.5rem;
}

.input-with-icon .input-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  pointer-events: none;
  z-index: 10;
}

/* 增强的动画效果 */
.animate-fade-in {
  animation: fadeIn 0.6s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.4s ease-out;
}

.animate-scale {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.animate-scale:hover {
  transform: scale(1.01) translateY(-1px);
}

.animate-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.animate-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.animate-button:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* 图标和文本垂直居中对齐 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-text-align {
  display: flex;
  align-items: center;
  line-height: 1;
}

.icon-text-align i {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}
</style>

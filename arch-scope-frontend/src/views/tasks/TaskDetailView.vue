<template>
  <MainLayout>
    <div class="container mx-auto p-6">
      <!-- 页面头部 -->
      <div class="flex items-center justify-between mb-6 animate-fade-in">
        <h1 class="text-3xl font-bold text-gray-800">任务详情</h1>
        <router-link
          to="/tasks"
          class="text-indigo-600 hover:text-indigo-900 flex items-center transition duration-200 animate-button"
        >
          <i class="fas fa-arrow-left mr-2"></i> 返回任务队列
        </router-link>
      </div>

      <!-- 加载状态 -->
      <div
        v-if="loading && !task"
        class="flex justify-center items-center h-64"
      >
        <div
          class="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600"
        ></div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="text-center py-12">
        <div class="text-red-600 mb-4">
          <i class="fas fa-exclamation-triangle text-4xl"></i>
        </div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">加载失败</h3>
        <p class="text-gray-500 mb-4">{{ error }}</p>
        <button
          @click="loadTask"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700"
        >
          重试
        </button>
      </div>



      <!-- 任务详情信息 -->
      <div
        v-if="task"
        class="content-card mb-6 overflow-hidden animate-slide-up"
        style="animation-delay: 0.2s"
      >
        <div class="p-4 bg-gray-50 border-b border-gray-200">
          <h2 class="text-lg font-semibold text-gray-700 flex items-center">
            <i class="fas fa-info-circle text-indigo-500 mr-2"></i>
            任务信息
          </h2>
        </div>
        <div class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-gray-700">
            <div>
              <div class="flex items-center mb-3">
                <div class="w-32 text-gray-500 font-medium">任务ID:</div>
                <div class="flex items-center">
                  <i class="fas fa-tasks text-gray-400 mr-2"></i>
                  <span class="font-medium">{{ task.id }}</span>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <div class="w-32 text-gray-500 font-medium">关联项目:</div>
                <div class="flex items-center">
                  <span
                    class="inline-block h-2 w-2 rounded-full bg-indigo-500 mr-2"
                  ></span>
                  <span>{{ task.projectName || "未知项目" }}</span>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <div class="w-32 text-gray-500 font-medium">任务类型:</div>
                <div class="flex items-center">
                  <i class="fas fa-file-alt text-gray-400 mr-2"></i>
                  <span>{{ getTaskTypeText(task.type) }}</span>
                </div>
              </div>
              <div class="flex items-center">
                <div class="w-32 text-gray-500 font-medium">状态:</div>
                <span
                  class="status-badge flex items-center"
                  :class="getStatusBadgeClass(task.status)"
                >
                  <i :class="getStatusIcon(task.status) + ' mr-2'"></i>
                  {{ getStatusText(task.status) }}
                </span>
              </div>
            </div>
            <div>
              <div class="flex items-center mb-3">
                <div class="w-32 text-gray-500 font-medium">创建时间:</div>
                <div class="flex items-center">
                  <i class="far fa-clock text-gray-400 mr-2"></i>
                  <span>{{ formatDate(task.createdAt) }}</span>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <div class="w-32 text-gray-500 font-medium">开始时间:</div>
                <div class="flex items-center">
                  <i class="far fa-clock text-gray-400 mr-2"></i>
                  <span>{{
                    task.startedAt ? formatDate(task.startedAt) : "-"
                  }}</span>
                </div>
              </div>
              <div class="flex items-center mb-3">
                <div class="w-32 text-gray-500 font-medium">结束时间:</div>
                <div class="flex items-center">
                  <span>{{
                    task.completedAt ? formatDate(task.completedAt) : "-"
                  }}</span>
                </div>
              </div>
              <div class="flex items-center">
                <div class="w-32 text-gray-500 font-medium">耗时:</div>
                <div class="flex items-center">
                  <span>{{ getDuration() }}</span>
                </div>
              </div>
            </div>
          </div>
          <div v-if="task.error" class="mt-6 pt-4 border-t border-gray-200">
            <div class="flex items-center">
              <div class="w-32 text-gray-500 font-medium">错误信息:</div>
              <div class="flex items-center text-red-600">
                <i class="fas fa-exclamation-circle text-red-500 mr-2"></i>
                <span>{{ task.error }}</span>
              </div>
            </div>
          </div>
          <div v-else class="mt-6 pt-4 border-t border-gray-200">
            <div class="flex items-center">
              <div class="w-32 text-gray-500 font-medium">错误信息:</div>
              <div class="flex items-center">
                <span>无</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 执行日志 -->
      <div
        v-if="task"
        class="content-card mb-6 overflow-hidden animate-slide-up"
        style="animation-delay: 0.4s"
      >
        <div
          class="p-4 bg-gray-50 border-b border-gray-200 flex items-center justify-between"
        >
          <h2 class="text-lg font-semibold text-gray-700 flex items-center">
            <i class="fas fa-terminal text-green-500 mr-2"></i>
            执行日志
          </h2>
          <button
            @click="refreshTask"
            :disabled="loading"
            class="btn-primary bg-indigo-600 hover:bg-indigo-700 text-white px-3 py-1 rounded text-sm animate-button"
          >
            <i
              class="fas fa-sync mr-1"
              :class="{ 'animate-spin': loading }"
            ></i>
            刷新
          </button>
        </div>
        <div class="terminal-container">
          <div class="terminal-header">
            <div class="terminal-buttons">
              <span class="terminal-button terminal-close"></span>
              <span class="terminal-button terminal-minimize"></span>
              <span class="terminal-button terminal-maximize"></span>
            </div>
            <div class="terminal-title">任务执行日志 - {{ task.id }}</div>
          </div>
          <div class="terminal-content">
            <div v-if="task.logs && task.logs.length > 0" class="space-y-1">
              <div
                v-for="(log, index) in task.logs"
                :key="index"
                class="terminal-line"
              >
                <span class="terminal-timestamp">{{
                  formatTime(log.timestamp)
                }}</span>
                <span
                  class="terminal-level"
                  :class="getTerminalLevelClass(log.level)"
                >
                  [{{ log.level.toUpperCase() }}]
                </span>
                <span class="terminal-message">{{ log.message }}</span>
              </div>
            </div>
            <div v-else class="terminal-empty">
              <i class="fas fa-info-circle text-gray-400 mr-2"></i>
              暂无执行日志
            </div>
            <div class="terminal-cursor">
              <span class="terminal-prompt">$</span>
              <span class="cursor-blink">_</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import axios from "axios";
import MainLayout from "@/layouts/MainLayout.vue";

interface TaskLog {
  id: string;
  level: "info" | "warn" | "error" | "debug";
  message: string;
  timestamp: string;
}

interface Task {
  id: number;
  name: string;
  type: string;
  status: "PENDING" | "PROCESSING" | "COMPLETED" | "FAILED" | "CANCELLED" | "pending" | "running" | "completed" | "failed" | "cancelled";
  parameters?: any;
  result?: any;
  errorLog?: string;
  logs?: TaskLog[];
  createdAt: string;
  updatedAt: string;
  processingStartedAt?: string;
  startedAt?: string;
  completedAt?: string;
  error?: string;
  projectId?: number;
  projectName?: string;
}

const route = useRoute();
const router = useRouter();

const task = ref<Task | null>(null);
const loading = ref(false);
const error = ref<string | null>(null);

const loadTask = async () => {
  loading.value = true;
  error.value = null;

  try {
    // 模拟API响应用于演示
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // 模拟任务数据
    task.value = {
      id: Number(route.params.id),
      name: "代码分析任务",
      type: "code_analysis",
      status: "completed",
      projectName: "ArchScope Frontend",
      parameters: {
        repository: "https://github.com/example/archscope-frontend",
        branch: "main",
        analysisType: "full",
      },
      result: {
        linesOfCode: 15420,
        files: 156,
        dependencies: 42,
        issues: 3,
      },
      error: "模拟错误信息",
      logs: [
        {
          id: "1",
          level: "info",
          message: "开始代码分析任务...",
          timestamp: new Date(Date.now() - 300000).toISOString(),
        },
        {
          id: "2",
          level: "info",
          message: "正在克隆代码仓库...",
          timestamp: new Date(Date.now() - 250000).toISOString(),
        },
        {
          id: "3",
          level: "info",
          message: "代码仓库克隆完成",
          timestamp: new Date(Date.now() - 200000).toISOString(),
        },
        {
          id: "4",
          level: "info",
          message: "开始分析代码结构...",
          timestamp: new Date(Date.now() - 150000).toISOString(),
        },
        {
          id: "5",
          level: "warn",
          message: "发现3个潜在的代码质量问题",
          timestamp: new Date(Date.now() - 100000).toISOString(),
        },
        {
          id: "6",
          level: "info",
          message: "代码分析完成，生成报告中...",
          timestamp: new Date(Date.now() - 50000).toISOString(),
        },
        {
          id: "7",
          level: "info",
          message: "任务执行成功完成",
          timestamp: new Date().toISOString(),
        },
      ],
      createdAt: new Date(Date.now() - 400000).toISOString(),
      startedAt: new Date(Date.now() - 300000).toISOString(),
      updatedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
    };

    // 实际的API调用（注释掉用于演示）
    // const response = await axios.get(`/api/tasks/${route.params.id}`);
    // task.value = response.data;
  } catch (err: any) {
    error.value = err.response?.data?.message || "加载任务详情失败";
  } finally {
    loading.value = false;
  }
};

const refreshTask = () => {
  loadTask();
};

const goBack = () => {
  router.back();
};

const getStatusClass = (status: string) => {
  const classes = {
    pending: "bg-yellow-100 text-yellow-800",
    running: "bg-blue-100 text-blue-800",
    completed: "bg-green-100 text-green-800",
    failed: "bg-red-100 text-red-800",
    cancelled: "bg-gray-100 text-gray-800",
  };
  return classes[status as keyof typeof classes] || "bg-gray-100 text-gray-800";
};

const getStatusText = (status: string) => {
  const texts = {
    pending: "等待中",
    running: "执行中",
    completed: "已完成",
    failed: "失败",
    cancelled: "已取消",
  };
  return texts[status as keyof typeof texts] || status;
};

const getLogClass = (level: string) => {
  const classes = {
    info: "border-blue-400",
    warn: "border-yellow-400",
    error: "border-red-400",
    debug: "border-gray-400",
  };
  return classes[level as keyof typeof classes] || "border-gray-400";
};

const getLogTextClass = (level: string) => {
  const classes = {
    info: "text-blue-600",
    warn: "text-yellow-600",
    error: "text-red-600",
    debug: "text-gray-600",
  };
  return classes[level as keyof typeof classes] || "text-gray-600";
};

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleString("zh-CN");
};

const formatTime = (dateString: string) => {
  return new Date(dateString).toLocaleTimeString("zh-CN");
};

// 新增方法支持界面原型功能
const getTaskTypeText = (type: string) => {
  const types = {
    code_analysis: "代码分析",
    dependency_check: "依赖检查",
    security_scan: "安全扫描",
    performance_test: "性能测试",
    build: "构建",
    deploy: "部署",
  };
  return types[type as keyof typeof types] || type;
};

const getStatusIcon = (status: string) => {
  const icons = {
    pending: "fas fa-clock text-yellow-600",
    running: "fas fa-spinner text-blue-600",
    completed: "fas fa-check-circle text-green-600",
    failed: "fas fa-times-circle text-red-600",
    cancelled: "fas fa-ban text-gray-600",
  };
  return (
    icons[status as keyof typeof icons] ||
    "fas fa-question-circle text-gray-600"
  );
};

const getStatusIconClass = (status: string) => {
  const classes = {
    pending: "bg-yellow-100",
    running: "bg-blue-100",
    completed: "bg-green-100",
    failed: "bg-red-100",
    cancelled: "bg-gray-100",
  };
  return classes[status as keyof typeof classes] || "bg-gray-100";
};

const getStatusTextClass = (status: string) => {
  const classes = {
    pending: "text-yellow-600",
    running: "text-blue-600",
    completed: "text-green-600",
    failed: "text-red-600",
    cancelled: "text-gray-600",
  };
  return classes[status as keyof typeof classes] || "text-gray-600";
};

const getStatusBadgeClass = (status: string) => {
  const classes = {
    pending: "bg-yellow-100 text-yellow-800",
    running: "bg-blue-100 text-blue-800",
    completed: "bg-green-100 text-green-800",
    failed: "bg-red-100 text-red-800",
    cancelled: "bg-gray-100 text-gray-800",
  };
  return classes[status as keyof typeof classes] || "bg-gray-100 text-gray-800";
};

const getTerminalLevelClass = (level: string) => {
  const classes = {
    info: "text-blue-400",
    warn: "text-yellow-400",
    error: "text-red-400",
    debug: "text-gray-400",
  };
  return classes[level as keyof typeof classes] || "text-gray-400";
};

const getDuration = () => {
  if (!task.value) return "-";

  const start = task.value.startedAt || task.value.createdAt;
  const end = task.value.completedAt || new Date().toISOString();

  const startTime = new Date(start).getTime();
  const endTime = new Date(end).getTime();
  const duration = endTime - startTime;

  if (duration < 1000) return "< 1秒";
  if (duration < 60000) return `${Math.floor(duration / 1000)}秒`;
  if (duration < 3600000) return `${Math.floor(duration / 60000)}分钟`;
  return `${Math.floor(duration / 3600000)}小时`;
};

onMounted(() => {
  loadTask();
});
</script>

<style scoped>
/* 界面原型样式 */
:root {
  --primary-color: #4f46e5; /* Indigo-600 */
  --primary-hover: #4338ca; /* Indigo-700 */
}

.content-card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.content-card:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05);
}



/* 状态徽章样式 */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 600;
}

/* 按钮样式 */
.btn-primary {
  background-color: var(--primary-color);
  color: white;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background-color: var(--primary-hover);
}

/* 终端样式 */
.terminal-container {
  background-color: #1a1a1a;
  border-radius: 0.5rem;
  overflow: hidden;
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
}

.terminal-header {
  background-color: #2d2d2d;
  padding: 0.75rem 1rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #404040;
}

.terminal-buttons {
  display: flex;
  gap: 0.5rem;
}

.terminal-button {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
}

.terminal-close {
  background-color: #ff5f56;
}

.terminal-minimize {
  background-color: #ffbd2e;
}

.terminal-maximize {
  background-color: #27ca3f;
}

.terminal-title {
  color: #ffffff;
  font-size: 0.875rem;
  font-weight: 500;
}

.terminal-content {
  background-color: #1a1a1a;
  color: #ffffff;
  padding: 1rem;
  min-height: 300px;
  max-height: 500px;
  overflow-y: auto;
  font-size: 0.875rem;
  line-height: 1.5;
}

.terminal-line {
  display: flex;
  margin-bottom: 0.25rem;
  word-wrap: break-word;
}

.terminal-timestamp {
  color: #666666;
  margin-right: 0.5rem;
  font-size: 0.75rem;
  min-width: 80px;
}

.terminal-level {
  margin-right: 0.5rem;
  font-weight: 600;
  min-width: 60px;
}

.terminal-message {
  color: #ffffff;
  flex: 1;
}

.terminal-empty {
  color: #666666;
  text-align: center;
  padding: 2rem;
  font-style: italic;
}

.terminal-cursor {
  display: flex;
  align-items: center;
  margin-top: 1rem;
  color: #00ff00;
}

.terminal-prompt {
  margin-right: 0.5rem;
  color: #00ff00;
  font-weight: bold;
}

.cursor-blink {
  animation: blink 1s infinite;
  color: #00ff00;
}

/* 动画效果 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-scale {
  transition: transform 0.3s ease;
}

.animate-scale:hover {
  transform: scale(1.02);
}

.animate-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.animate-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@keyframes slideUp {
  0% {
    transform: translateY(20px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes blink {
  0%,
  50% {
    opacity: 1;
  }
  51%,
  100% {
    opacity: 0;
  }
}

/* 滚动条样式 */
.terminal-content::-webkit-scrollbar {
  width: 8px;
}

.terminal-content::-webkit-scrollbar-track {
  background: #2d2d2d;
}

.terminal-content::-webkit-scrollbar-thumb {
  background: #555555;
  border-radius: 4px;
}

.terminal-content::-webkit-scrollbar-thumb:hover {
  background: #777777;
}
</style>

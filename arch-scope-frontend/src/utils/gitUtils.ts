/**
 * Git仓库相关工具函数
 */

// Git URL正则表达式
const HTTPS_PATTERN = /^https?:\/\/([^\/]+)\/([^\/]+)\/([^\/]+?)(?:\.git)?\/?$/;
const SSH_PATTERN = /^git@([^:]+):([^\/]+)\/([^\/]+?)(?:\.git)?\/?$/;

/**
 * Git仓库信息接口
 */
export interface GitRepositoryInfo {
  projectName: string;
  description?: string;
  defaultBranch: string;
  branches: string[];
  repositoryType: string;
  owner: string;
  repositoryName: string;
  isPrivate?: boolean;
  languages?: string[];
  createdAt?: string;
  updatedAt?: string;
  size?: number;
  starCount?: number;
  forkCount?: number;
  success: boolean;
  errorMessage?: string;
}

/**
 * Git仓库验证请求接口
 */
export interface GitRepositoryValidationRequest {
  repositoryUrl: string;
  fetchDetails?: boolean;
  username?: string;
  password?: string;
}

/**
 * 验证Git仓库URL格式是否正确
 * @param url Git仓库URL
 * @returns 是否为有效的Git仓库URL
 */
export function isValidGitUrl(url: string): boolean {
  if (!url || typeof url !== 'string') {
    return false;
  }
  
  const trimmedUrl = url.trim();
  return HTTPS_PATTERN.test(trimmedUrl) || SSH_PATTERN.test(trimmedUrl);
}

/**
 * 从Git仓库URL中提取项目名称
 * @param url Git仓库URL
 * @returns 项目名称
 */
export function extractProjectNameFromUrl(url: string): string {
  if (!url) return '';
  
  const httpsMatch = url.match(HTTPS_PATTERN);
  if (httpsMatch) {
    return httpsMatch[3];
  }
  
  const sshMatch = url.match(SSH_PATTERN);
  if (sshMatch) {
    return sshMatch[3];
  }
  
  // 如果正则匹配失败，尝试简单解析
  const parts = url.replace(/\.git$/, '').split('/');
  if (parts.length > 0) {
    return parts[parts.length - 1];
  }
  
  return '';
}

/**
 * 从Git仓库URL中提取所有者名称
 * @param url Git仓库URL
 * @returns 所有者名称
 */
export function extractOwnerFromUrl(url: string): string {
  if (!url) return '';
  
  const httpsMatch = url.match(HTTPS_PATTERN);
  if (httpsMatch) {
    return httpsMatch[2];
  }
  
  const sshMatch = url.match(SSH_PATTERN);
  if (sshMatch) {
    return sshMatch[2];
  }
  
  return '';
}

/**
 * 判断Git仓库类型
 * @param url Git仓库URL
 * @returns 仓库类型
 */
export function determineRepositoryType(url: string): string {
  if (!url) return 'Git';
  
  if (url.includes('github.com')) {
    return 'GitHub';
  } else if (url.includes('gitlab.com') || url.includes('gitlab')) {
    return 'GitLab';
  } else if (url.includes('bitbucket.org')) {
    return 'Bitbucket';
  } else {
    return 'Git';
  }
}

/**
 * 标准化Git仓库URL
 * @param url Git仓库URL
 * @returns 标准化后的URL
 */
export function normalizeGitUrl(url: string): string {
  if (!url) return '';
  
  let normalizedUrl = url.trim();
  
  // 移除末尾的斜杠
  normalizedUrl = normalizedUrl.replace(/\/$/, '');
  
  // 如果是SSH格式，转换为HTTPS格式
  const sshMatch = normalizedUrl.match(SSH_PATTERN);
  if (sshMatch) {
    const [, host, owner, repo] = sshMatch;
    normalizedUrl = `https://${host}/${owner}/${repo}`;
  }
  
  // 确保以.git结尾（可选）
  if (!normalizedUrl.endsWith('.git')) {
    normalizedUrl += '.git';
  }
  
  return normalizedUrl;
}

/**
 * 获取Git仓库的Web URL（用于在浏览器中打开）
 * @param url Git仓库URL
 * @returns Web URL
 */
export function getRepositoryWebUrl(url: string): string {
  if (!url) return '';
  
  const httpsMatch = url.match(HTTPS_PATTERN);
  if (httpsMatch) {
    const [, host, owner, repo] = httpsMatch;
    return `https://${host}/${owner}/${repo}`;
  }
  
  const sshMatch = url.match(SSH_PATTERN);
  if (sshMatch) {
    const [, host, owner, repo] = sshMatch;
    return `https://${host}/${owner}/${repo}`;
  }
  
  return url;
}

/**
 * 验证Git仓库URL并提供用户友好的错误信息
 * @param url Git仓库URL
 * @returns 验证结果和错误信息
 */
export function validateGitUrlWithMessage(url: string): { isValid: boolean; message?: string } {
  if (!url || !url.trim()) {
    return { isValid: false, message: '请输入Git仓库地址' };
  }

  const trimmedUrl = url.trim();

  // 首先检查基本格式是否正确
  if (!isValidGitUrl(trimmedUrl)) {
    if (!trimmedUrl.includes('://') && !trimmedUrl.startsWith('git@')) {
      return { isValid: false, message: '请输入完整的Git仓库地址，如：https://github.com/user/repo' };
    }

    return { isValid: false, message: 'Git仓库地址格式不正确，支持HTTPS和SSH格式' };
  }

  // 格式正确，但给出HTTP协议的建议（不阻止使用）
  if (trimmedUrl.startsWith('http://')) {
    return {
      isValid: true,
      message: '建议使用HTTPS协议以提高安全性，但当前URL格式正确'
    };
  }

  return { isValid: true };
}

/**
 * 从URL中提取仓库的基本信息（不进行网络请求）
 * @param url Git仓库URL
 * @returns 基本仓库信息
 */
export function extractBasicRepoInfo(url: string): Partial<GitRepositoryInfo> {
  if (!isValidGitUrl(url)) {
    return { success: false, errorMessage: 'Invalid Git URL' };
  }
  
  return {
    projectName: extractProjectNameFromUrl(url),
    owner: extractOwnerFromUrl(url),
    repositoryName: extractProjectNameFromUrl(url),
    repositoryType: determineRepositoryType(url),
    success: true
  };
}

/**
 * 生成常见的分支名称列表
 * @returns 常见分支名称数组
 */
export function getCommonBranchNames(): string[] {
  return ['main', 'master', 'develop', 'dev', 'staging', 'release'];
}

/**
 * 将SSH格式的Git URL转换为HTTPS格式
 * @param sshUrl SSH格式的Git URL
 * @returns HTTPS格式的Git URL，如果转换失败则返回null
 */
export function convertSshToHttpsUrl(sshUrl: string): string | null {
  if (!sshUrl || !sshUrl.startsWith('git@')) {
    return null;
  }

  const sshMatch = sshUrl.match(SSH_PATTERN);
  if (sshMatch) {
    const [, host, owner, repo] = sshMatch;
    return `https://${host}/${owner}/${repo}.git`;
  }

  return null;
}

/**
 * 将HTTPS格式的Git URL转换为SSH格式
 * @param httpsUrl HTTPS格式的Git URL
 * @returns SSH格式的Git URL，如果转换失败则返回null
 */
export function convertHttpsToSshUrl(httpsUrl: string): string | null {
  if (!httpsUrl || !httpsUrl.startsWith('http')) {
    return null;
  }

  const httpsMatch = httpsUrl.match(HTTPS_PATTERN);
  if (httpsMatch) {
    const [, host, owner, repo] = httpsMatch;
    return `git@${host}:${owner}/${repo}.git`;
  }

  return null;
}

/**
 * 判断是否为主分支
 * @param branchName 分支名称
 * @returns 是否为主分支
 */
export function isMainBranch(branchName: string): boolean {
  const mainBranches = ['main', 'master'];
  return mainBranches.includes(branchName.toLowerCase());
}

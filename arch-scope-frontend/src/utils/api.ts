import { get, post, put, del } from './request';
import <PERSON>rrorHandler from './errorHandler';

// API调用包装器，统一错误处理
const apiWrapper = <T>(apiCall: () => Promise<T>): Promise<T> => {
  return apiCall().catch(error => {
    const errorInfo = ErrorHandler.handleApiError(error);
    throw new Error(errorInfo.message);
  });
};

// 定义接口类型
export interface Project {
  id: number;
  name: string;
  description: string;
  repositoryUrl: string;
  branch: string;
  createdAt: string;
  updatedAt: string;
  lastAnalyzedAt: string | null;
  creatorId: number;
  status: string;
  active: boolean;
  documentationPath: string | null;
  analysisCount: number;
  documentationVersion: number;
  rating: number;
  icon: string | null;
  type: string;
  // 统一字段命名
  linesOfCode?: number;  // 代码行数
  fileCount?: number;    // 文件数量
  contributorCount?: number;  // 贡献者数量
  languages?: string[];
}

export interface ProjectForm {
  name: string;
  description: string;
  repositoryUrl: string;  // 匹配后端字段名
  branch?: string;  // 分支，可选，默认为main
  type?: string;  // 项目类型，可选
  languages?: string[];
}

export interface DocGeneration {
  id: string;
  version: string;
  generatedAt: string;
  summary: string;
}

export interface HealthMetric {
  name: string;
  score: number;
}

export interface HealthAssessment {
  score: number;
  metrics: HealthMetric[];
}

export interface HealthTrendPoint {
  week: string; // 周标识，如 "2024-W01"
  score: number; // 健康度分数 0-100
  date: string; // 该周的日期
}

export interface HealthTrend {
  projectId: string;
  points: HealthTrendPoint[];
  period: 'weekly' | 'daily' | 'monthly';
  totalWeeks: number;
}

export interface ProjectDetail extends Project {
  docGenerations?: DocGeneration[];
  healthAssessment?: HealthAssessment;
}

export interface Task {
  id: number; // 与后端Long类型对应
  name: string;
  description?: string;
  type: 'CODE_PARSE' | 'DOC_GENERATE' | 'PROJECT_ANALYSIS' | 'PROJECT_INDEX' | 'HEALTH_CHECK';
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'PARTIAL_SUCCESS' | 'CANCELLED' | 'WAITING' | 'PAUSED';
  detailedStatus?: string;
  projectId?: number; // 与后端Long类型对应
  projectName?: string;
  priority?: number;
  progress?: number;
  createdAt: string;
  updatedAt?: string;
  processingStartedAt?: string; // 对应后端的processingStartedAt
  timeoutAt?: string;
  executionTime?: number;
  retryCount?: number;
  maxRetries?: number;
  errorLog?: string;
  lastErrorDetail?: string;
  result?: string;
  results?: string; // 支持多文档类型的结果
  parameters?: Record<string, any>;
  userId?: number;
  assigneeId?: number;
  workerId?: string;
  overallStatus?: string;
  commitId?: string;
  taskVersion?: string;
}

export interface TaskForm {
  projectId: number;
  name: string;
  description?: string;
  type: string;
  priority?: number;
  parameters?: Record<string, any>;
  assigneeId?: number;
  commitId?: string;
}

// 项目管理API
export const projectAPI = {
  // 获取项目列表
  getProjects() {
    return get<{ data: Project[] }>('/v1/projects');
  },

  // 获取项目详情
  getProjectById(id: string) {
    return get<{ data: ProjectDetail }>(`/v1/projects/${id}`);
  },

  // 创建项目
  createProject(data: ProjectForm) {
    return post<{ data: Project }>('/v1/projects', data);
  },

  // 更新项目
  updateProject(id: string, data: ProjectForm) {
    return put<{ data: Project }>(`/v1/projects/${id}`, data);
  },

  // 删除项目
  deleteProject(id: string) {
    return del<{ success: boolean }>(`/v1/projects/${id}`);
  },

  // 检查仓库URL是否已存在
  checkRepositoryExists(url: string) {
    return get<{
      exists: boolean;
      message: string;
      existingProject?: {
        id: number;
        name: string;
        description: string;
        createdAt: string;
        status: string;
      };
      error?: boolean;
    }>(`/v1/projects/check-repository?url=${encodeURIComponent(url)}`);
  },

  // 生成项目文档
  generateDocs(id: string) {
    return post<{ data: { taskId: string } }>(`/v1/projects/${id}/generate-docs`);
  },

  // 获取项目健康度趋势
  getHealthTrend(id: string, weeks: number = 40) {
    return get<{ data: HealthTrend }>(`/v1/projects/${id}/health/trend?weeks=${weeks}`);
  }
};

// Git仓库管理API
export const gitRepositoryAPI = {
  // 验证Git仓库并获取信息
  validateRepository(data: {
    repositoryUrl: string;
    fetchDetails?: boolean;
    username?: string;
    password?: string;
  }) {
    return post<GitRepositoryInfo>('/git-repository/validate', data);
  },

  // 快速验证Git仓库URL格式
  validateUrl(repositoryUrl: string) {
    return get<boolean>(`/git-repository/validate-url?repositoryUrl=${encodeURIComponent(repositoryUrl)}`);
  },

  // 获取Git仓库详细信息
  getRepositoryDetails(repositoryUrl: string, username?: string, password?: string) {
    const params = new URLSearchParams({ repositoryUrl });
    if (username) params.append('username', username);
    if (password) params.append('password', password);

    return get<GitRepositoryInfo>(`/git-repository/details?${params.toString()}`);
  }
};

// Git仓库信息接口
export interface GitRepositoryInfo {
  projectName: string;
  description?: string;
  defaultBranch: string;
  branches: string[];
  repositoryType: string;
  owner: string;
  repositoryName: string;
  isPrivate?: boolean;
  languages?: string[];
  createdAt?: string;
  updatedAt?: string;
  size?: number;
  starCount?: number;
  forkCount?: number;
  success: boolean;
  errorMessage?: string;
}

// 分页响应接口 - 与后端Spring Data分页格式保持一致
export interface PageResponseDTO<T> {
  content: T[];        // Spring Data使用content字段
  totalElements: number; // Spring Data使用totalElements字段
  page: number;
  size: number;
  totalPages: number;
  first: boolean;
  last: boolean;
}

// 任务管理API
export const taskAPI = {
  // 获取任务列表（支持项目筛选）
  getTasks(projectId?: number, page: number = 0, size: number = 10, sortBy: string = 'createdAt', direction: string = 'desc', status?: string) {
    const params = new URLSearchParams({
      page: page.toString(),
      size: size.toString(),
      sortBy,
      direction
    });
    if (projectId) {
      params.append('projectId', projectId.toString());
    }
    if (status) {
      params.append('status', status);
    }
    return get<PageResponseDTO<Task>>(`/v1/tasks?${params}`);
  },

  // 获取任务详情
  getTaskById(taskId: number) {
    return get<Task>(`/v1/tasks/${taskId}`);
  },

  // 创建任务
  createTask(data: TaskForm) {
    return post<{ data: Task }>('/v1/tasks', data);
  },

  // 取消任务
  cancelTask(taskId: number) {
    return post<{ success: boolean; message: string; taskId: number }>(`/v1/tasks/${taskId}/cancel`);
  },

  // 重试任务
  retryTask(taskId: number) {
    return post<Task>(`/v1/tasks/${taskId}/retry`);
  },

  // 获取任务结果
  getTaskResult(taskId: number) {
    return get<{ data: any }>(`/v1/tasks/${taskId}/result`);
  },

  // 搜索任务
  searchTasks(keyword: string, page: number = 0, size: number = 10) {
    return get<{ data: Task[] }>(`/v1/tasks/search?keyword=${keyword}&page=${page}&size=${size}`);
  },

  // 获取用户任务
  getUserTasks(userId: string) {
    return get<{ data: Task[] }>(`/v1/tasks/user/${userId}`);
  },

  // 获取最近任务
  getRecentTasks(limit: number = 10) {
    return get<{ data: Task[] }>(`/v1/tasks/recent?limit=${limit}`);
  },

  // 更新任务状态
  updateTaskStatus(taskId: number, status: string) {
    return put<Task>(`/v1/tasks/${taskId}/status`, { status });
  },

  // 删除任务
  deleteTask(taskId: number) {
    return del<{ success: boolean }>(`/v1/tasks/${taskId}`);
  }
};
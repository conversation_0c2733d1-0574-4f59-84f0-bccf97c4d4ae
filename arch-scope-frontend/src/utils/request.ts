import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';

// 创建axios实例
const service = axios.create({
  baseURL: '/api', // API基础路径
  timeout: 15000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
service.interceptors.request.use(
  (config) => {
    // 不需要认证信息
    return config;
  },
  (error) => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (response: AxiosResponse) => {
    const res = response.data;
    
    // 统一的API响应格式处理
    // 后端使用ApiResponse<T>格式：{ success: boolean, message: string, data: T }
    if (res.success === false) {
      // 处理业务错误
      console.error('业务错误:', res.message || '操作失败');

      // 对特定错误进行处理
      if (res.message && res.message.includes('未授权')) {
        console.warn('权限错误，但系统不需要认证');
      }

      return Promise.reject(new Error(res.message || '操作失败'));
    }

    // 兼容旧的code字段格式
    if (res.code && res.code !== 200 && res.code !== '000000') {
      console.error('业务错误:', res.message || '未知错误');
      return Promise.reject(new Error(res.message || '未知错误'));
    }
    
    return res;
  },
  (error: AxiosError) => {
    let message = '';

    if (error.response) {
      // 首先尝试从响应体中提取具体的错误信息
      const responseData = error.response.data;

      if (responseData) {
        if (typeof responseData === 'string') {
          message = responseData;
        } else if (typeof responseData === 'object') {
          // 尝试多种可能的错误信息字段
          message = (responseData as any).message ||
                   (responseData as any).error ||
                   (responseData as any).errorMessage ||
                   (responseData as any).msg ||
                   '';
        }
      }

      // 如果没有提取到具体错误信息，使用默认的状态码消息
      if (!message) {
        switch (error.response.status) {
          case 400:
            message = '请求参数错误';
            break;
          case 401:
            message = '未授权访问';
            break;
          case 403:
            message = '拒绝访问';
            break;
          case 404:
            message = '请求地址不存在';
            break;
          case 409:
            message = '资源冲突';
            break;
          case 422:
            message = '请求参数验证失败';
            break;
          case 500:
            message = '服务器内部错误';
            break;
          case 502:
            message = '网关错误';
            break;
          case 503:
            message = '服务暂时不可用';
            break;
          default:
            message = `请求失败: ${error.response.status}`;
        }
      }
    } else if (error.request) {
      message = '网络连接失败，请检查网络设置';
    } else {
      message = '请求配置错误';
    }

    console.error('请求失败:', message, error);

    // 创建一个新的错误对象，包含友好的错误信息
    const friendlyError = new Error(message);
    friendlyError.name = 'RequestError';
    // 保留原始错误信息以便调试
    (friendlyError as any).originalError = error;
    (friendlyError as any).response = error.response;

    return Promise.reject(friendlyError);
  }
);

// 封装GET请求
export function get<T>(url: string, params?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.get(url, { params, ...config });
}

// 封装POST请求
export function post<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.post(url, data, config);
}

// 封装PUT请求
export function put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> {
  return service.put(url, data, config);
}

// 封装DELETE请求
export function del<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
  return service.delete(url, config);
}

export default service; 
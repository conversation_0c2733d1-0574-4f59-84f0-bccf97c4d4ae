/**
 * API调试工具
 * 用于测试和调试项目列表API的连接性和数据格式
 *
 * ⚠️ 注意：此文件仅用于开发环境调试，生产环境应使用真实API
 * 模拟数据功能保留用于开发调试，但不应在生产环境中使用
 */

import request from './request'

// 测试API连接
export async function testProjectsAPI() {
  console.log('🔍 开始测试项目列表API...')
  
  try {
    // 测试1: 直接调用 /v1/projects 端点
    console.log('📡 测试端点: /v1/projects')
    const response1 = await request.get('/v1/projects')
    console.log('✅ /v1/projects 响应:', response1)

    return {
      success: true,
      endpoint: '/v1/projects',
      data: response1
    }
  } catch (error1) {
    console.log('❌ /v1/projects 失败:', error1)

    try {
      // 测试2: 尝试 /projects 端点（旧版本兼容）
      console.log('📡 测试端点: /projects')
      const response2 = await request.get('/projects')
      console.log('✅ /projects 响应:', response2)

      return {
        success: true,
        endpoint: '/projects',
        data: response2
      }
    } catch (error2) {
      console.log('❌ /projects 也失败:', error2)

      return {
        success: false,
        errors: {
          '/v1/projects': error1,
          '/projects': error2
        }
      }
    }
  }
}

// 生成模拟项目数据
export function generateMockProjects(count: number = 5) {
  const mockProjects = []
  
  for (let i = 1; i <= count; i++) {
    mockProjects.push({
      id: i,
      name: `示例项目 ${i}`,
      description: `这是第 ${i} 个示例项目的描述，用于演示项目列表功能。包含了现代化的架构设计和最佳实践。`,
      repositoryUrl: `https://github.com/example/project-${i}`,
      branch: i % 2 === 0 ? 'main' : 'master',
      createdAt: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString(),
      updatedAt: new Date(Date.now() - i * 12 * 60 * 60 * 1000).toISOString(),
      lastAnalyzedAt: i % 3 === 0 ? null : new Date(Date.now() - i * 6 * 60 * 60 * 1000).toISOString(),
      creatorId: 1,
      status: ['PENDING_ANALYSIS', 'ANALYZING', 'ANALYSIS_COMPLETED', 'ANALYSIS_FAILED', 'AVAILABLE', 'UNAVAILABLE'][i % 6],
      active: true,
      documentationPath: i % 2 === 0 ? `/docs/project-${i}` : null,
      analysisCount: Math.floor(Math.random() * 5) + 1,
      documentationVersion: i,
      rating: Math.round((Math.random() * 4 + 1) * 10) / 10, // 1.0 - 5.0 评分
      icon: null,
      type: ['JAVA', 'JAVASCRIPT', 'PYTHON', 'GO', 'CSHARP', 'KOTLIN', 'RUST', 'PHP', 'TYPESCRIPT', 'OTHER'][i % 10]
    })
  }
  
  return mockProjects
}

// 模拟API响应
export function mockProjectsResponse(projects: any[]) {
  // 模拟不同的响应格式
  const formats = [
    // 格式1: 直接返回数组
    projects,
    
    // 格式2: 包装在data字段中
    { data: projects },
    
    // 格式3: 包含分页信息
    {
      data: projects,
      total: projects.length,
      page: 0,
      size: 10,
      totalPages: 1
    },
    
    // 格式4: Spring Boot风格
    {
      content: projects,
      totalElements: projects.length,
      totalPages: 1,
      size: 10,
      number: 0
    }
  ]
  
  return formats[1] // 默认使用格式2
}

// 设置模拟数据到localStorage
export function setMockProjectsData() {
  const mockProjects = generateMockProjects(8)
  const mockResponse = mockProjectsResponse(mockProjects)
  
  localStorage.setItem('mock_projects_data', JSON.stringify(mockResponse))
  console.log('📦 模拟项目数据已保存到localStorage:', mockResponse)
  
  return mockResponse
}

// 从localStorage获取模拟数据
export function getMockProjectsData() {
  const data = localStorage.getItem('mock_projects_data')
  if (data) {
    try {
      return JSON.parse(data)
    } catch (error) {
      console.error('解析模拟数据失败:', error)
      return null
    }
  }
  return null
}

// 清除模拟数据
export function clearMockProjectsData() {
  localStorage.removeItem('mock_projects_data')
  console.log('🧹 模拟项目数据已清除')
}

// 在浏览器控制台中使用的调试函数
export function debugProjectsList() {
  console.log('🎯 项目列表调试工具')
  console.log('================')
  
  // 提供调试命令
  const commands = {
    testAPI: testProjectsAPI,
    setMockData: setMockProjectsData,
    getMockData: getMockProjectsData,
    clearMockData: clearMockProjectsData,
    generateMock: generateMockProjects
  }
  
  // 将命令添加到window对象
  if (typeof window !== 'undefined') {
    (window as any).projectsDebug = commands
    
    console.log('💡 可用的调试命令:')
    console.log('  projectsDebug.testAPI() - 测试API连接')
    console.log('  projectsDebug.setMockData() - 设置模拟数据')
    console.log('  projectsDebug.getMockData() - 获取模拟数据')
    console.log('  projectsDebug.clearMockData() - 清除模拟数据')
    console.log('  projectsDebug.generateMock(count) - 生成模拟数据')
  }
  
  return commands
}

// 自动初始化调试工具
if (typeof window !== 'undefined') {
  debugProjectsList()
}

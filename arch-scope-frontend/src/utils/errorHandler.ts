/**
 * 统一错误处理工具
 * 提供前端错误处理的标准化方法
 */

export interface ErrorInfo {
  code?: string;
  message: string;
  details?: any;
  timestamp?: string;
}

export interface ApiError extends Error {
  code?: string;
  details?: any;
  response?: any;
}

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK = 'NETWORK',
  BUSINESS = 'BUSINESS', 
  VALIDATION = 'VALIDATION',
  PERMISSION = 'PERMISSION',
  SYSTEM = 'SYSTEM',
  UNKNOWN = 'UNKNOWN'
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  
  /**
   * 解析错误类型
   */
  static getErrorType(error: any): ErrorType {
    if (!error) return ErrorType.UNKNOWN;
    
    // 网络错误
    if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
      return ErrorType.NETWORK;
    }
    
    // HTTP状态码判断
    if (error.response?.status) {
      const status = error.response.status;
      if (status >= 400 && status < 500) {
        if (status === 401 || status === 403) {
          return ErrorType.PERMISSION;
        }
        return ErrorType.VALIDATION;
      }
      if (status >= 500) {
        return ErrorType.SYSTEM;
      }
    }
    
    // 业务错误码判断
    if (error.code && typeof error.code === 'string') {
      if (error.code.startsWith('A00')) {
        return ErrorType.VALIDATION;
      }
      if (error.code.startsWith('B00')) {
        return ErrorType.SYSTEM;
      }
      if (error.code.startsWith('C00')) {
        return ErrorType.BUSINESS;
      }
    }
    
    return ErrorType.BUSINESS;
  }
  
  /**
   * 获取用户友好的错误消息
   */
  static getFriendlyMessage(error: any): string {
    if (!error) return '未知错误';
    
    // 如果有自定义消息，优先使用
    if (error.message && typeof error.message === 'string') {
      return error.message;
    }
    
    const errorType = this.getErrorType(error);
    
    switch (errorType) {
      case ErrorType.NETWORK:
        return '网络连接失败，请检查网络设置';
      case ErrorType.PERMISSION:
        return '权限不足，请联系管理员';
      case ErrorType.VALIDATION:
        return '请求参数有误，请检查输入信息';
      case ErrorType.SYSTEM:
        return '系统繁忙，请稍后重试';
      case ErrorType.BUSINESS:
        return error.message || '操作失败，请稍后重试';
      default:
        return '未知错误，请稍后重试';
    }
  }
  
  /**
   * 创建标准化的错误信息对象
   */
  static createErrorInfo(error: any): ErrorInfo {
    return {
      code: error.code || error.response?.data?.code,
      message: this.getFriendlyMessage(error),
      details: error.details || error.response?.data?.details,
      timestamp: new Date().toISOString()
    };
  }
  
  /**
   * 处理API错误
   */
  static handleApiError(error: any): ErrorInfo {
    console.error('API错误:', error);
    
    const errorInfo = this.createErrorInfo(error);
    
    // 记录错误日志（生产环境可以发送到日志服务）
    this.logError(errorInfo, error);
    
    return errorInfo;
  }
  
  /**
   * 处理组件错误
   */
  static handleComponentError(error: any, componentName?: string): ErrorInfo {
    console.error(`组件错误${componentName ? ` (${componentName})` : ''}:`, error);
    
    const errorInfo = this.createErrorInfo(error);
    errorInfo.details = {
      ...errorInfo.details,
      component: componentName,
      stack: error.stack
    };
    
    this.logError(errorInfo, error);
    
    return errorInfo;
  }
  
  /**
   * 记录错误日志
   */
  private static logError(errorInfo: ErrorInfo, originalError: any) {
    // 开发环境详细日志
    if (process.env.NODE_ENV === 'development') {
      console.group('🚨 错误详情');
      console.error('错误信息:', errorInfo);
      console.error('原始错误:', originalError);
      console.groupEnd();
    }
    
    // 生产环境可以发送到监控服务
    // 例如：sendToMonitoringService(errorInfo);
  }
  
  /**
   * 显示错误提示
   */
  static showErrorMessage(error: any, showToast?: (message: string, type: string) => void) {
    const errorInfo = this.handleApiError(error);
    
    if (showToast) {
      showToast(errorInfo.message, 'error');
    } else {
      // 如果没有提供toast函数，使用console输出
      console.error('错误提示:', errorInfo.message);
    }
    
    return errorInfo;
  }
}

/**
 * 错误处理装饰器（用于async函数）
 */
export function handleErrors(target: any, propertyName: string, descriptor: PropertyDescriptor) {
  const method = descriptor.value;
  
  descriptor.value = async function (...args: any[]) {
    try {
      return await method.apply(this, args);
    } catch (error) {
      const errorInfo = ErrorHandler.handleApiError(error);
      throw new Error(errorInfo.message);
    }
  };
  
  return descriptor;
}

/**
 * 创建API错误对象
 */
export function createApiError(message: string, code?: string, details?: any): ApiError {
  const error = new Error(message) as ApiError;
  error.name = 'ApiError';
  error.code = code;
  error.details = details;
  return error;
}

export default ErrorHandler;

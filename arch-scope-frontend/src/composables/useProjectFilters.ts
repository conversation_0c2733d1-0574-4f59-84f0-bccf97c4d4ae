import { ref, computed, Ref } from 'vue';
import { Project } from '@/stores/project';

export interface ProjectFiltersOptions {
  itemsPerPage?: number;
}

export function useProjectFilters(projects: Ref<Project[]>, options: ProjectFiltersOptions = {}) {
  const searchQuery = ref('');
  const sortOption = ref('updated');
  const currentPage = ref(1);
  const itemsPerPage = ref(options.itemsPerPage || 10);

  // 排序后的项目列表
  const sortedProjects = computed(() => {
    const projectList = [...projects.value];

    // 排序
    switch (sortOption.value) {
      case 'name':
        return projectList.sort((a, b) => a.name.localeCompare(b.name));
      case 'updated':
        return projectList.sort(
          (a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime()
        );
      case 'created':
        return projectList.sort(
          (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
        );
      default:
        return projectList;
    }
  });

  // 过滤后的项目列表
  const filteredProjects = computed(() => {
    if (!searchQuery.value.trim()) {
      // 分页
      const start = (currentPage.value - 1) * itemsPerPage.value;
      const end = start + itemsPerPage.value;
      return sortedProjects.value.slice(start, end);
    }

    const query = searchQuery.value.toLowerCase().trim();
    const filtered = sortedProjects.value.filter(
      (project) =>
        project.name.toLowerCase().includes(query) ||
        (project.description && project.description.toLowerCase().includes(query)) ||
        (project.repositoryUrl && project.repositoryUrl.toLowerCase().includes(query))
    );

    // 分页
    const start = (currentPage.value - 1) * itemsPerPage.value;
    const end = start + itemsPerPage.value;
    return filtered.slice(start, end);
  });

  // 过滤后的总项目数
  const filteredCount = computed(() => {
    if (!searchQuery.value.trim()) {
      return sortedProjects.value.length;
    }

    const query = searchQuery.value.toLowerCase().trim();
    return sortedProjects.value.filter(
      (project) =>
        project.name.toLowerCase().includes(query) ||
        (project.description && project.description.toLowerCase().includes(query)) ||
        (project.repositoryUrl && project.repositoryUrl.toLowerCase().includes(query))
    ).length;
  });

  // 总页数
  const totalPages = computed(() => {
    return Math.max(1, Math.ceil(filteredCount.value / itemsPerPage.value));
  });

  // 分页起始和结束
  const paginationStart = computed(() => {
    return filteredCount.value === 0 ? 0 : (currentPage.value - 1) * itemsPerPage.value + 1;
  });

  const paginationEnd = computed(() => {
    const end = currentPage.value * itemsPerPage.value;
    return Math.min(end, filteredCount.value);
  });

  // 设置页码
  const setPage = (page: number) => {
    currentPage.value = page;
  };

  return {
    searchQuery,
    sortOption,
    currentPage,
    filteredProjects,
    totalPages,
    paginationStart,
    paginationEnd,
    filteredCount,
    setPage,
  };
}

import { ref, reactive } from 'vue'
import { useProjectStore } from '@/stores/project'
import { gitRepositoryAPI, projectAPI } from '@/utils/api'
import { isValidGitUrl } from '@/utils/gitUtils'

export interface RegistrationStep {
  name: string
  status: 'pending' | 'running' | 'completed' | 'failed'
  error?: string
}

export interface RegistrationState {
  isLoading: boolean
  currentStepIndex: number
  steps: RegistrationStep[]
  canCancel: boolean
  error: string | null
}

export interface ProjectRegistrationData {
  name: string
  description: string
  repositoryUrl: string
  branch: string
}

export function useProjectRegistration() {
  const projectStore = useProjectStore()
  
  // 取消控制器
  let abortController: AbortController | null = null
  
  // 注册状态
  const state = reactive<RegistrationState>({
    isLoading: false,
    currentStepIndex: 0,
    steps: [
      { name: 'validateRepository', status: 'pending' },
      { name: 'fetchBranchesAndCheck', status: 'pending' },
      { name: 'fetchMetadata', status: 'pending' },
      { name: 'createProject', status: 'pending' }
    ],
    canCancel: true,
    error: null
  })
  
  // 重置状态
  const resetState = () => {
    state.isLoading = false
    state.currentStepIndex = 0
    state.canCancel = true
    state.error = null
    state.steps.forEach(step => {
      step.status = 'pending'
      step.error = undefined
    })
  }
  
  // 更新步骤状态
  const updateStepStatus = (stepIndex: number, status: RegistrationStep['status'], error?: string) => {
    if (stepIndex >= 0 && stepIndex < state.steps.length) {
      state.steps[stepIndex].status = status
      if (error) {
        state.steps[stepIndex].error = error
      }
    }
  }
  
  // 延迟函数（用于模拟真实的异步操作时间）
  const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))
  
  // 步骤1: 验证仓库格式
  const validateRepository = async (repositoryUrl: string, signal: AbortSignal) => {
    updateStepStatus(0, 'running')

    try {
      if (signal.aborted) {
        throw new Error('操作已取消')
      }

      // 1. 首先验证URL格式
      if (!isValidGitUrl(repositoryUrl)) {
        throw new Error('仓库URL格式不正确')
      }



      if (signal.aborted) {
        throw new Error('操作已取消')
      }

      // 3. 模拟其他验证时间
      await delay(800)

      updateStepStatus(0, 'completed')
      return true
    } catch (error: any) {
      updateStepStatus(0, 'failed', error.message)
      throw error
    }
  }
  
  // 步骤2: 获取分支信息（包括重复性检查）
  const fetchBranches = async (repositoryUrl: string, signal: AbortSignal) => {
    updateStepStatus(1, 'running')
    
    try {
      // 模拟网络请求时间
      await delay(2000)
      
      if (signal.aborted) {
        throw new Error('操作已取消')
      }
      
      // 调用API获取分支信息
      const response = await gitRepositoryAPI.getRepositoryDetails(repositoryUrl)
      
      if (!response.success) {
        throw new Error(response.errorMessage || '获取分支信息失败')
      }
      
      updateStepStatus(1, 'completed')
      return response
    } catch (error: any) {
      updateStepStatus(1, 'failed', error.message)
      throw error
    }
  }
  
  // 步骤3: 获取项目元数据
  const fetchMetadata = async (repositoryUrl: string, signal: AbortSignal) => {
    updateStepStatus(2, 'running')
    
    try {
      // 模拟获取元数据时间
      await delay(1500)
      
      if (signal.aborted) {
        throw new Error('操作已取消')
      }
      
      // 这里可以调用额外的API获取更详细的项目信息
      // 目前使用模拟数据
      const metadata = {
        readme: '项目README内容...',
        license: 'MIT',
        language: 'JavaScript'
      }
      
      updateStepStatus(2, 'completed')
      return metadata
    } catch (error: any) {
      updateStepStatus(2, 'failed', error.message)
      throw error
    }
  }
  
  // 步骤4: 创建项目
  const createProject = async (projectData: ProjectRegistrationData, signal: AbortSignal) => {
    updateStepStatus(3, 'running')
    
    try {
      if (signal.aborted) {
        throw new Error('操作已取消')
      }
      
      // 调用项目store创建项目
      const result = await projectStore.registerProject(
        projectData.name,
        projectData.description,
        projectData.repositoryUrl,
        projectData.branch
      )
      
      updateStepStatus(3, 'completed')
      return result
    } catch (error: any) {
      updateStepStatus(3, 'failed', error.message)
      throw error
    }
  }
  
  // 主注册函数
  const registerProject = async (projectData: ProjectRegistrationData) => {
    // 防重复调用检查
    if (state.isLoading) {
      console.warn('项目注册正在进行中，忽略重复调用')
      return {
        success: false,
        error: '项目注册正在进行中，请勿重复提交'
      }
    }

    resetState()
    state.isLoading = true

    console.log('useProjectRegistration: 开始项目注册流程')

    // 创建新的取消控制器
    abortController = new AbortController()
    const signal = abortController.signal
    
    try {
      // 步骤1: 验证仓库
      state.currentStepIndex = 0
      await validateRepository(projectData.repositoryUrl, signal)
      
      // 步骤2: 获取分支信息
      state.currentStepIndex = 1
      const repositoryInfo = await fetchBranches(projectData.repositoryUrl, signal)
      
      // 步骤3: 获取元数据
      state.currentStepIndex = 2
      await fetchMetadata(projectData.repositoryUrl, signal)
      
      // 步骤4: 创建项目
      state.currentStepIndex = 3
      state.canCancel = false // 最后一步不允许取消
      const result = await createProject(projectData, signal)
      
      // 完成
      state.currentStepIndex = 4
      state.isLoading = false
      
      return {
        success: true,
        data: result,
        repositoryInfo
      }
    } catch (error: any) {
      state.isLoading = false
      state.error = error.message
      
      return {
        success: false,
        error: error.message
      }
    } finally {
      abortController = null
    }
  }
  
  // 取消注册
  const cancelRegistration = () => {
    if (abortController && state.canCancel) {
      abortController.abort()
      state.isLoading = false
      state.error = '用户取消了注册操作'
      return true
    }
    return false
  }
  
  // 处理超时
  const handleTimeout = () => {
    if (abortController) {
      abortController.abort()
      state.isLoading = false
      state.error = '注册操作超时，请检查网络连接后重试'
    }
  }
  
  return {
    state,
    registerProject,
    cancelRegistration,
    handleTimeout,
    resetState
  }
}

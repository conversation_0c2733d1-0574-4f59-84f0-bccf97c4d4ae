import { ref, createApp, type App } from 'vue'
import Toast, { type ToastProps } from '@/components/Toast.vue'

export interface ToastOptions extends Omit<ToastProps, 'title'> {
  title?: string
}

export interface ToastInstance {
  id: string
  close: () => void
}

class ToastManager {
  private toasts = ref<ToastInstance[]>([])
  private container: HTMLElement | null = null

  constructor() {
    this.createContainer()
  }

  private createContainer() {
    if (typeof window === 'undefined') return
    
    this.container = document.createElement('div')
    this.container.id = 'toast-container'
    this.container.className = 'fixed top-4 right-4 z-50 space-y-2'
    document.body.appendChild(this.container)
  }

  private generateId(): string {
    return `toast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  show(title: string, options: ToastOptions = {}): ToastInstance {
    if (!this.container) {
      this.createContainer()
    }

    const id = this.generateId()
    const toastElement = document.createElement('div')
    toastElement.id = id
    
    const app: App = createApp(Toast, {
      title,
      ...options,
      onClose: () => {
        this.remove(id, app, toastElement)
      }
    })

    this.container?.appendChild(toastElement)
    app.mount(toastElement)

    const instance: ToastInstance = {
      id,
      close: () => {
        this.remove(id, app, toastElement)
      }
    }

    this.toasts.value.push(instance)
    return instance
  }

  private remove(id: string, app: App, element: HTMLElement) {
    // 移除Toast实例
    this.toasts.value = this.toasts.value.filter(toast => toast.id !== id)
    
    // 卸载Vue应用
    app.unmount()
    
    // 移除DOM元素
    if (element.parentNode) {
      element.parentNode.removeChild(element)
    }
  }

  success(title: string, options: ToastOptions = {}): ToastInstance {
    return this.show(title, { ...options, type: 'success' })
  }

  error(title: string, options: ToastOptions = {}): ToastInstance {
    return this.show(title, { ...options, type: 'error' })
  }

  warning(title: string, options: ToastOptions = {}): ToastInstance {
    return this.show(title, { ...options, type: 'warning' })
  }

  info(title: string, options: ToastOptions = {}): ToastInstance {
    return this.show(title, { ...options, type: 'info' })
  }

  clear() {
    this.toasts.value.forEach(toast => toast.close())
    this.toasts.value = []
  }

  destroy() {
    this.clear()
    if (this.container && this.container.parentNode) {
      this.container.parentNode.removeChild(this.container)
      this.container = null
    }
  }
}

// 创建全局Toast管理器实例
const toastManager = new ToastManager()

// 导出组合式函数
export function useToast() {
  return {
    show: (title: string, options?: ToastOptions) => toastManager.show(title, options),
    success: (title: string, options?: ToastOptions) => toastManager.success(title, options),
    error: (title: string, options?: ToastOptions) => toastManager.error(title, options),
    warning: (title: string, options?: ToastOptions) => toastManager.warning(title, options),
    info: (title: string, options?: ToastOptions) => toastManager.info(title, options),
    clear: () => toastManager.clear(),
    destroy: () => toastManager.destroy()
  }
}

// 导出默认实例（用于非组合式API场景）
export default toastManager

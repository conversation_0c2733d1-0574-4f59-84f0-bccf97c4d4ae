import { ref, onMounted, onUnmounted } from 'vue';
import { taskAPI, type Task } from '@/utils/api';

/**
 * 任务轮询管理 Composable
 * 提供任务状态的实时更新机制
 */
export function useTaskPolling() {
  const isPolling = ref(false);
  const pollingInterval = ref<NodeJS.Timeout | null>(null);
  const pollingFrequency = ref(15000); // 默认15秒轮询一次
  const lastUpdateTime = ref<Date | null>(null);

  // 需要轮询的任务状态
  const ACTIVE_STATUSES = ['PENDING', 'PROCESSING', 'WAITING'];

  /**
   * 开始轮询任务列表（优化版：直接轮询任务列表而不是单个任务）
   */
  const startPolling = (
    refreshTaskList: () => Promise<void>,
    frequency = 15000
  ) => {
    if (pollingInterval.value) {
      stopPolling();
    }

    pollingFrequency.value = frequency;
    isPolling.value = true;

    const poll = async () => {
      try {
        // 直接刷新任务列表，避免多个单独的API请求
        await refreshTaskList();
        lastUpdateTime.value = new Date();
      } catch (error) {
        console.error('轮询任务列表失败:', error);
      }
    };

    // 设置定时轮询（不立即执行，避免与初始加载重复）
    pollingInterval.value = setInterval(poll, pollingFrequency.value);
  };

  /**
   * 停止轮询
   */
  const stopPolling = () => {
    if (pollingInterval.value) {
      clearInterval(pollingInterval.value);
      pollingInterval.value = null;
    }
    isPolling.value = false;
  };

  /**
   * 重新开始轮询
   */
  const restartPolling = (
    refreshTaskList: () => Promise<void>,
    frequency?: number
  ) => {
    stopPolling();
    startPolling(refreshTaskList, frequency);
  };

  /**
   * 检查任务是否需要轮询
   */
  const needsPolling = (task: Task): boolean => {
    return ACTIVE_STATUSES.includes(task.status);
  };

  /**
   * 获取轮询状态信息
   */
  const getPollingInfo = () => ({
    isPolling: isPolling.value,
    frequency: pollingFrequency.value,
    lastUpdate: lastUpdateTime.value,
    nextUpdate: lastUpdateTime.value 
      ? new Date(lastUpdateTime.value.getTime() + pollingFrequency.value)
      : null
  });

  // 组件卸载时清理
  onUnmounted(() => {
    stopPolling();
  });

  return {
    isPolling,
    pollingFrequency,
    lastUpdateTime,
    startPolling,
    stopPolling,
    restartPolling,
    needsPolling,
    getPollingInfo
  };
}

/**
 * 任务状态变更通知 Composable
 */
export function useTaskStatusNotification() {
  const notifications = ref<Array<{
    id: string;
    taskId: string;
    taskName: string;
    oldStatus: string;
    newStatus: string;
    timestamp: Date;
    type: 'success' | 'error' | 'info' | 'warning';
  }>>([]);

  /**
   * 添加状态变更通知
   */
  const addNotification = (
    taskId: number,
    taskName: string,
    oldStatus: string,
    newStatus: string
  ) => {
    const notification = {
      id: `${taskId}-${Date.now()}`,
      taskId: taskId.toString(),
      taskName,
      oldStatus,
      newStatus,
      timestamp: new Date(),
      type: getNotificationType(newStatus)
    };

    notifications.value.unshift(notification);

    // 只保留最近的10条通知
    if (notifications.value.length > 10) {
      notifications.value = notifications.value.slice(0, 10);
    }

    // 3秒后自动移除通知
    setTimeout(() => {
      removeNotification(notification.id);
    }, 3000);
  };

  /**
   * 移除通知
   */
  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id);
    if (index > -1) {
      notifications.value.splice(index, 1);
    }
  };

  /**
   * 清空所有通知
   */
  const clearNotifications = () => {
    notifications.value = [];
  };

  /**
   * 根据状态获取通知类型
   */
  const getNotificationType = (status: string): 'success' | 'error' | 'info' | 'warning' => {
    switch (status) {
      case 'COMPLETED':
        return 'success';
      case 'FAILED':
        return 'error';
      case 'CANCELLED':
        return 'warning';
      case 'PROCESSING':
        return 'info';
      default:
        return 'info';
    }
  };

  return {
    notifications,
    addNotification,
    removeNotification,
    clearNotifications
  };
}

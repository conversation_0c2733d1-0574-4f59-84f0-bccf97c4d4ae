import request from '@/utils/request'

/**
 * 文档API服务
 */
export const documentAPI = {
  /**
   * 获取项目的文档类型列表
   * @param {number} projectId 项目ID
   * @param {string} version 版本标签（可选）
   * @returns {Promise} 文档类型列表
   */
  getDocumentTypes(projectId, version = null) {
    const params = version ? { version } : {}
    return request.get(`/v1/documents/projects/${projectId}/types`, { params })
  },

  /**
   * 获取项目的版本列表
   * @param {number} projectId 项目ID
   * @returns {Promise} 版本列表
   */
  getProjectVersions(projectId) {
    return request.get(`/v1/documents/projects/${projectId}/versions`)
  },

  /**
   * 获取文档的原始Markdown内容
   * @param {number} projectId 项目ID
   * @param {string} docType 文档类型
   * @param {string} version 版本标签（可选）
   * @returns {Promise} 文档的Markdown内容
   */
  getDocumentContent(projectId, docType, version = null) {
    const params = version ? { version } : {}
    return request.get(`/v1/documents/projects/${projectId}/documents/${docType}/content`, { params })
  },

  /**
   * 获取文档的HTML渲染内容
   * @param {number} projectId 项目ID
   * @param {string} docType 文档类型
   * @param {string} version 版本标签（可选）
   * @returns {Promise} 文档的HTML内容
   */
  getDocumentHtml(projectId, docType, version = null) {
    const params = version ? { version } : {}
    return request.get(`/v1/documents/projects/${projectId}/documents/${docType}/html`, { params })
  },

  /**
   * 检查文档是否存在
   * @param {number} projectId 项目ID
   * @param {string} docType 文档类型
   * @param {string} version 版本标签（可选）
   * @returns {Promise} 文档是否存在
   */
  documentExists(projectId, docType, version = null) {
    const params = version ? { version } : {}
    return request.get(`/v1/documents/projects/${projectId}/documents/${docType}/exists`, { params })
  }
}

/**
 * 文档类型映射
 * 将后端返回的文档类型映射为前端显示的格式
 */
export const documentTypeMapping = {
  'PRODUCT_INTRO': {
    label: '产品简介',
    icon: 'fas fa-home',
    description: '项目的核心功能和特性介绍'
  },
  'ARCHITECTURE': {
    label: '架构设计',
    icon: 'fas fa-sitemap',
    description: '系统架构和设计文档'
  },
  'API': {
    label: '接口文档',
    icon: 'fas fa-file-code',
    description: 'API接口说明和使用指南'
  },
  'USER_MANUAL': {
    label: '用户手册',
    icon: 'fas fa-book-open',
    description: '用户使用指南和操作手册'
  },
  'EXTENSION': {
    label: '扩展能力',
    icon: 'fas fa-puzzle-piece',
    description: '系统扩展功能和插件说明'
  },
  'LLMS_TXT': {
    label: 'LLM生成内容',
    icon: 'fas fa-file-alt',
    description: 'LLM自动生成的原始内容'
  }
}

/**
 * 获取文档类型的显示信息
 * @param {string} docType 文档类型
 * @returns {object} 显示信息
 */
export function getDocumentTypeInfo(docType) {
  return documentTypeMapping[docType] || {
    label: docType,
    icon: 'fas fa-file',
    description: '未知文档类型'
  }
}

/**
 * 界面原型中定义的文档类型显示顺序
 * 按照project_doc_home.html原型中的菜单顺序
 */
export const DOCUMENT_TYPE_ORDER = [
  'PRODUCT_INTRO',    // 产品简介
  'ARCHITECTURE',     // 架构设计
  'EXTENSION',        // 扩展能力
  'USER_MANUAL',      // 用户手册
  'API',              // 接口文档
  'LLMS_TXT'          // llms.txt
]

/**
 * 格式化文档类型列表
 * 按照界面原型定义的顺序显示，只显示接口返回的文档类型
 * @param {Array} docTypes 后端返回的文档类型列表
 * @returns {Array} 按原型顺序格式化后的文档类型列表
 */
export function formatDocumentTypes(docTypes) {
  // 将后端返回的文档类型转换为Set，便于快速查找
  const availableDocTypes = new Set(docTypes || [])

  // 按照原型定义的顺序过滤和格式化文档类型
  return DOCUMENT_TYPE_ORDER
    .filter(docType => availableDocTypes.has(docType))  // 只保留接口中存在的文档类型
    .map(docType => ({
      value: docType,
      ...getDocumentTypeInfo(docType)
    }))
}

export default documentAPI

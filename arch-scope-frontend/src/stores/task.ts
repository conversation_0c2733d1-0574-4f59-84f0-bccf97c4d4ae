import { defineStore } from 'pinia';
import { ref } from 'vue';
import { taskAPI, Task, TaskForm } from '@/utils/api';

export const useTaskStore = defineStore('task', () => {
  // 状态
  const tasks = ref<Task[]>([]);
  const currentTask = ref<Task | null>(null);
  const loading = ref(false);
  const error = ref<string | null>(null);

  // 获取任务列表（支持项目筛选）
  const fetchTasks = async (projectId?: number, page: number = 0, size: number = 10) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await taskAPI.getTasks(projectId, page, size);
      // 适配后端Spring Data分页格式：content字段包含数据
      tasks.value = response.content;
    } catch (err) {
      console.error('获取任务列表失败:', err);
      error.value = '获取任务列表失败，请稍后再试';
    } finally {
      loading.value = false;
    }
  };

  // 获取任务详情
  const fetchTaskById = async (taskId: number) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await taskAPI.getTaskById(taskId);
      currentTask.value = response;
    } catch (err) {
      console.error('获取任务详情失败:', err);
      error.value = '获取任务详情失败，请稍后再试';
    } finally {
      loading.value = false;
    }
  };

  // 创建任务
  const createTask = async (taskData: TaskForm) => {
    loading.value = true;
    error.value = null;
    
    try {
      const response = await taskAPI.createTask(taskData);
      return response.data;
    } catch (err) {
      console.error('创建任务失败:', err);
      error.value = '创建任务失败，请稍后再试';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 取消任务
  const cancelTask = async (taskId: number) => {
    loading.value = true;
    error.value = null;

    try {
      await taskAPI.cancelTask(taskId);
      // 从列表中移除任务
      tasks.value = tasks.value.filter(t => t.id !== taskId);
      // 如果当前正在查看的就是这个任务，清空currentTask
      if (currentTask.value && currentTask.value.id === taskId) {
        currentTask.value = null;
      }
      return true;
    } catch (err) {
      console.error('取消任务失败:', err);
      error.value = '取消任务失败，请稍后再试';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 重试任务
  const retryTask = async (taskId: number) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await taskAPI.retryTask(taskId);
      // 更新任务状态
      const index = tasks.value.findIndex(t => t.id === taskId);
      if (index !== -1) {
        tasks.value[index] = response;
      }
      // 如果当前正在查看的就是这个任务，更新currentTask
      if (currentTask.value && currentTask.value.id === taskId) {
        currentTask.value = response;
      }
      return response;
    } catch (err) {
      console.error('重试任务失败:', err);
      error.value = '重试任务失败，请稍后再试';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 获取任务结果
  const getTaskResult = async (taskId: number) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await taskAPI.getTaskResult(taskId);
      return response.data;
    } catch (err) {
      console.error('获取任务结果失败:', err);
      error.value = '获取任务结果失败，请稍后再试';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  // 搜索任务
  const searchTasks = async (keyword: string, page: number = 0, size: number = 10) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await taskAPI.searchTasks(keyword, page, size);
      tasks.value = response.data;
    } catch (err) {
      console.error('搜索任务失败:', err);
      error.value = '搜索任务失败，请稍后再试';
    } finally {
      loading.value = false;
    }
  };

  // 更新任务状态
  const updateTaskStatus = async (taskId: number, status: string) => {
    loading.value = true;
    error.value = null;

    try {
      const response = await taskAPI.updateTaskStatus(taskId, status);
      // 更新任务状态
      const index = tasks.value.findIndex(t => t.id === taskId);
      if (index !== -1) {
        tasks.value[index] = response;
      }
      // 如果当前正在查看的就是这个任务，更新currentTask
      if (currentTask.value && currentTask.value.id === taskId) {
        currentTask.value = response;
      }
      return response;
    } catch (err) {
      console.error('更新任务状态失败:', err);
      error.value = '更新任务状态失败，请稍后再试';
      throw err;
    } finally {
      loading.value = false;
    }
  };

  return {
    // 状态
    tasks,
    currentTask,
    loading,
    error,

    // 方法
    fetchTasks,
    fetchTaskById,
    createTask,
    cancelTask,
    retryTask,
    getTaskResult,
    searchTasks,
    updateTaskStatus
  };
}); 
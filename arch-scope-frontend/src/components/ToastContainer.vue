<template>
  <Teleport to="body">
    <div
      id="toast-container"
      class="fixed top-4 right-4 z-50 space-y-2 pointer-events-none"
    >
      <!-- Toast容器，由useToast管理 -->
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'

// 确保Toast容器在应用启动时就存在
onMounted(() => {
  // 容器由useToast自动创建和管理
})

onUnmounted(() => {
  // 清理工作由useToast管理
})
</script>

<style scoped>
/* Toast容器样式 */
#toast-container {
  max-width: 420px;
  width: 100%;
}

/* 移动端适配 */
@media (max-width: 640px) {
  #toast-container {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    max-width: none;
  }
}
</style>

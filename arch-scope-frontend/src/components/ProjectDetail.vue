<template>
  <div class="project-detail">
    <div v-if="projectStore.loading" class="text-center py-8">
      <p>加载中...</p>
    </div>
    <div v-else-if="projectStore.error" class="text-red-500 py-8">
      <p>{{ projectStore.error }}</p>
    </div>
    <div v-else-if="!projectStore.currentProject" class="text-center py-8">
      <p>未找到项目信息</p>
    </div>
    <div v-else>
      <div class="flex justify-between items-center mb-6 animate-fade-in">
        <h1 class="text-3xl font-bold text-gray-800">{{ projectStore.currentProject.name }}</h1>
        <div class="flex gap-2">
          <button
            class="px-3 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 text-sm animate-button"
            @click="$router.push(`/projects/${projectStore.currentProject.id}/edit`)"
          >
            编辑项目
          </button>
          <button
            class="px-3 py-1.5 border border-red-600 text-red-600 rounded-md hover:bg-red-50 text-sm animate-button"
            @click="confirmDelete"
          >
            删除项目
          </button>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <h2 class="text-lg font-semibold mb-4">基本信息</h2>
        <div class="space-y-3">
          <div class="flex">
            <span class="text-gray-500 w-32">项目描述:</span>
            <span>{{ projectStore.currentProject.description || '暂无描述' }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-500 w-32">仓库类型:</span>
            <span class="bg-blue-100 text-blue-800 px-2 py-0.5 rounded text-sm">Git</span>
          </div>
          <div class="flex">
            <span class="text-gray-500 w-32">仓库地址:</span>
            <a :href="projectStore.currentProject.repositoryUrl" target="_blank" class="text-blue-600 hover:underline">{{ projectStore.currentProject.repositoryUrl }}</a>
          </div>
          <div class="flex">
            <span class="text-gray-500 w-32">创建时间:</span>
            <span>{{ formatDate(projectStore.currentProject.createdAt) }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-500 w-32">最近更新:</span>
            <span>{{ formatDate(projectStore.currentProject.updatedAt) }}</span>
          </div>
          <div class="flex">
            <span class="text-gray-500 w-32">支持的语言:</span>
            <div class="flex flex-wrap gap-1">
              <span 
                v-for="lang in ['JavaScript', 'TypeScript', 'Java']"
                :key="lang" 
                class="bg-gray-100 text-gray-700 px-2 py-0.5 rounded-full text-xs"
              >
                {{ lang }}
              </span>
            </div>
          </div>
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm p-6 mb-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">文档生成情况</h2>
          <button
            class="px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm animate-button"
            @click="generateDocs"
          >
            生成文档
          </button>
        </div>
        
        <div v-if="false">
          <!-- 文档生成记录占位 -->
        </div>
        <div v-else class="text-center py-4 text-gray-500">
          暂无文档生成记录
        </div>
      </div>
      
      <div class="bg-white rounded-lg shadow-sm p-6">
        <h2 class="text-lg font-semibold mb-4">健康度评估</h2>
        <div v-if="false">
          <!-- 健康度评估占位 -->
        </div>
        <div v-else class="text-center py-4 text-gray-500">
          暂无健康度评估数据
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useProjectStore } from '@/stores/project';
import { useTaskStore } from '@/stores/task';

const route = useRoute();
const router = useRouter();
const projectId = Number(route.params.id);
const projectStore = useProjectStore();
const taskStore = useTaskStore();

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const generateDocs = async () => {
  try {
    const result = await projectStore.generateDocs(projectId);
    alert(`已开始生成文档`);
    // 刷新项目信息
    await projectStore.fetchProjectById(projectId);
  } catch (err) {
    console.error('生成文档失败:', err);
    alert('生成文档失败，请稍后再试');
  }
};

const viewDocs = (docId: number) => {
  router.push(`/projects/${projectId}/docs/${docId}`);
};

const downloadDocs = (docId: number) => {
  alert(`开始下载文档 ${docId}`);
  // 实际项目中应实现下载功能
};

const confirmDelete = async () => {
  if (confirm('确定要删除此项目吗？此操作不可撤销。')) {
    try {
      await projectStore.deleteProject(projectId);
      alert('项目已成功删除');
      router.push('/projects');
    } catch (err) {
      console.error('删除项目失败:', err);
    }
  }
};

onMounted(() => {
  projectStore.fetchProjectById(projectId);
});
</script>

<style scoped>
/* 动画效果 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.animate-button:hover {
  transform: translateY(-3px);
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
</style>
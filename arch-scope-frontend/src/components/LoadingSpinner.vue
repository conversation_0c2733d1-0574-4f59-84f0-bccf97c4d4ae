<template>
  <div :class="['loading-spinner', { overlay: isOverlay, fullScreen: isFullScreen }]">
    <div class="spinner-container">
      <svg class="spinner" viewBox="0 0 50 50">
        <circle
          class="path"
          cx="25"
          cy="25"
          r="20"
          fill="none"
          stroke-width="4"
        ></circle>
      </svg>
      <div v-if="text" class="loading-text">{{ text }}</div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps({
  // 是否显示为全屏加载状态
  isFullScreen: {
    type: Boolean,
    default: false
  },
  // 是否显示为覆盖层加载状态
  isOverlay: {
    type: Boolean,
    default: false
  },
  // 加载状态显示的文本
  text: {
    type: String,
    default: '加载中...'
  }
});
</script>

<style scoped>
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
}

.loading-spinner.overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 50;
}

.loading-spinner.fullScreen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  z-index: 1000;
}

.spinner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.spinner {
  height: 2.5rem;
  width: 2.5rem;
  animation: rotate 2s linear infinite;
}

.path {
  stroke: #3B82F6; /* Tailwind blue-500 */
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

.loading-text {
  margin-top: 0.75rem;
  font-size: 0.875rem;
  color: #4B5563; /* Tailwind gray-600 */
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}
</style> 
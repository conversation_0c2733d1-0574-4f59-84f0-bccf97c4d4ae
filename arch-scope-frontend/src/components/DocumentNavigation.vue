<template>
  <nav class="flex-grow p-6 overflow-y-auto">
    <ul class="space-y-2">
      <li v-for="docType in documentTypes" :key="docType.value">
        <a 
          href="#" 
          @click.prevent="$emit('select', docType.value)"
          :class="[
            'flex items-center justify-between px-4 py-2 rounded-md transition duration-200',
            activeDocType === docType.value 
              ? 'text-white bg-gray-700 active-link' 
              : 'text-gray-300 hover:text-white hover:bg-gray-700'
          ]"
        >
          <span class="flex items-center">
            <i :class="['w-5 mr-3', docType.icon]"></i> {{ docType.label }}
          </span>
        </a>
        <ul v-if="activeDocType === docType.value && docType.sections && docType.sections.length > 0" class="ml-4 mt-1 space-y-1 border-l border-gray-700 pl-4">
          <li v-for="section in docType.sections" :key="section.id">
            <a 
              :href="'#' + section.id" 
              class="block text-gray-400 hover:text-white text-sm py-1 transition duration-200"
            >
              {{ section.label }}
            </a>
          </li>
        </ul>
      </li>
    </ul>
  </nav>
</template>

<script lang="ts">
import { defineComponent, PropType } from 'vue'

export interface DocumentTypeOption {
  label: string;
  value: string;
  icon: string;
  sections?: Array<{
    id: string;
    label: string;
  }>;
}

export default defineComponent({
  name: 'DocumentNavigation',
  
  props: {
    documentTypes: {
      type: Array as PropType<DocumentTypeOption[]>,
      required: true
    },
    activeDocType: {
      type: String,
      required: true
    }
  },
  
  emits: ['select'],
  
  setup() {
    return {}
  }
})
</script>

<style scoped>
.active-link {
  border-left: 3px solid #4F46E5; /* Indigo-600 */
}
</style>

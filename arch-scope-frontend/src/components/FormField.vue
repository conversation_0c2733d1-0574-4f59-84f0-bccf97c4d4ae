<template>
  <div class="form-field">
    <label 
      v-if="label" 
      :for="id" 
      class="form-label"
      :class="{ 'label-required': required }"
    >
      {{ label }}
    </label>
    
    <div class="field-wrapper">
      <slot>
        <input
          :id="id"
          :type="type"
          v-model="innerValue"
          :placeholder="placeholder"
          :disabled="disabled"
          :readonly="readonly"
          :class="[
            'form-input',
            { 'is-invalid': !!errorMessage }
          ]"
          v-bind="$attrs"
          @blur="validateOnBlur"
        />
      </slot>
      
      <div v-if="errorMessage" class="error-message">
        {{ errorMessage }}
      </div>
      
      <div v-if="helpText" class="help-text">
        {{ helpText }}
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue';
import { v4 as uuidv4 } from 'uuid';

// 定义验证规则类型
interface ValidationRule {
  required?: boolean;
  message?: string;
  validator?: (value: any) => boolean | string;
  pattern?: string;
  min?: number;
  max?: number;
}

const props = defineProps({
  // 字段ID
  id: {
    type: String,
    default: () => `field-${uuidv4()}`
  },
  // 字段标签
  label: {
    type: String,
    default: ''
  },
  // 输入类型
  type: {
    type: String,
    default: 'text'
  },
  // 占位文本
  placeholder: {
    type: String,
    default: ''
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否只读
  readonly: {
    type: Boolean,
    default: false
  },
  // 字段值
  modelValue: {
    type: [String, Number, Boolean],
    default: ''
  },
  // 帮助文本
  helpText: {
    type: String,
    default: ''
  },
  // 是否必填
  required: {
    type: Boolean,
    default: false
  },
  // 验证规则
  rules: {
    type: Array,
    default: () => []
  },
  // 是否在初始化时验证
  validateOnMount: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'validate', 'blur']);

const innerValue = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value);
    // 输入时验证
    if (props.rules.length > 0) {
      validate();
    }
  }
});

const errorMessage = ref('');

// 验证字段
const validate = () => {
  if (!props.rules || props.rules.length === 0) {
    errorMessage.value = '';
    emit('validate', { valid: true, message: '' });
    return true;
  }

  for (const rule of props.rules) {
    const typedRule = rule as ValidationRule;

    // 处理必填验证
    if (typedRule.required && (!innerValue.value || innerValue.value === '')) {
      errorMessage.value = typedRule.message || '该字段为必填项';
      emit('validate', { valid: false, message: errorMessage.value });
      return false;
    }

    // 处理自定义验证函数
    if (typedRule.validator && typeof typedRule.validator === 'function') {
      try {
        const result = typedRule.validator(innerValue.value);
        if (result !== true) {
          errorMessage.value = (typeof result === 'string' ? result : typedRule.message) || '验证失败';
          emit('validate', { valid: false, message: errorMessage.value });
          return false;
        }
      } catch (error) {
        errorMessage.value = typedRule.message || '验证失败';
        emit('validate', { valid: false, message: errorMessage.value });
        return false;
      }
    }

    // 处理正则表达式验证
    if (typedRule.pattern && innerValue.value) {
      const regex = new RegExp(typedRule.pattern);
      if (!regex.test(String(innerValue.value))) {
        errorMessage.value = typedRule.message || '格式不正确';
        emit('validate', { valid: false, message: errorMessage.value });
        return false;
      }
    }

    // 处理最小长度验证
    if (typedRule.min !== undefined && innerValue.value && String(innerValue.value).length < typedRule.min) {
      errorMessage.value = typedRule.message || `最小长度为 ${typedRule.min}`;
      emit('validate', { valid: false, message: errorMessage.value });
      return false;
    }

    // 处理最大长度验证
    if (typedRule.max !== undefined && innerValue.value && String(innerValue.value).length > typedRule.max) {
      errorMessage.value = typedRule.message || `最大长度为 ${typedRule.max}`;
      emit('validate', { valid: false, message: errorMessage.value });
      return false;
    }
  }

  // 所有验证通过
  errorMessage.value = '';
  emit('validate', { valid: true, message: '' });
  return true;
};

// 失去焦点时验证
const validateOnBlur = () => {
  emit('blur');
  validate();
};

// 监听规则变化时重新验证
watch(() => props.rules, () => {
  validate();
}, { deep: true });

// 组件挂载时验证
onMounted(() => {
  if (props.validateOnMount) {
    validate();
  }
});

// 暴露验证方法
defineExpose({ validate });
</script>

<style scoped>
.form-field {
  margin-bottom: 1.25rem;
}

.form-label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 500;
  color: #4B5563; /* Tailwind gray-600 */
  font-size: 0.875rem;
}

.label-required:after {
  content: "*";
  color: #EF4444; /* Tailwind red-500 */
  margin-left: 0.25rem;
}

.field-wrapper {
  position: relative;
}

.form-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #D1D5DB; /* Tailwind gray-300 */
  border-radius: 0.375rem;
  font-size: 0.875rem;
  line-height: 1.5;
  color: #1F2937; /* Tailwind gray-800 */
  background-color: white;
  transition: border-color 0.15s, box-shadow 0.15s;
}

.form-input:focus {
  outline: none;
  border-color: #60A5FA; /* Tailwind blue-400 */
  box-shadow: 0 0 0 3px rgba(96, 165, 250, 0.2); /* Tailwind blue-400 with opacity */
}

.form-input.is-invalid {
  border-color: #EF4444; /* Tailwind red-500 */
}

.form-input.is-invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2); /* Tailwind red-500 with opacity */
}

.form-input:disabled,
.form-input:read-only {
  background-color: #F3F4F6; /* Tailwind gray-100 */
  cursor: not-allowed;
}

.error-message {
  margin-top: 0.375rem;
  color: #EF4444; /* Tailwind red-500 */
  font-size: 0.75rem;
}

.help-text {
  margin-top: 0.375rem;
  color: #6B7280; /* Tailwind gray-500 */
  font-size: 0.75rem;
}
</style> 
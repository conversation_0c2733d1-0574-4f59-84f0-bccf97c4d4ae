<template>
  <component
    :is="tag"
    :to="to"
    :href="href"
    :class="[
      'inline-flex items-center justify-center rounded-md transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2',
      colorClasses,
      sizeClasses,
      {
        'cursor-not-allowed opacity-50': disabled,
        'cursor-pointer': !disabled
      }
    ]"
    :disabled="disabled"
    :title="tooltip"
    :aria-label="ariaLabel || tooltip"
    @click="handleClick"
  >
    <i :class="[iconClass, iconSizeClass]"></i>
    <span v-if="$slots.default" class="ml-2">
      <slot></slot>
    </span>
  </component>
</template>

<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  icon: string;
  color?: 'primary' | 'success' | 'danger' | 'warning' | 'gray';
  size?: 'sm' | 'md' | 'lg';
  tooltip?: string;
  ariaLabel?: string;
  disabled?: boolean;
  to?: string;
  href?: string;
}

const props = withDefaults(defineProps<Props>(), {
  color: 'gray',
  size: 'md'
});

const emit = defineEmits<{
  click: [event: Event];
}>();

const tag = computed(() => {
  if (props.to) return 'router-link';
  if (props.href) return 'a';
  return 'button';
});

const iconClass = computed(() => `fas fa-${props.icon}`);

const colorClasses = computed(() => {
  const colors = {
    primary: 'text-indigo-600 hover:text-indigo-900 hover:bg-indigo-50 focus:ring-indigo-500',
    success: 'text-green-600 hover:text-green-900 hover:bg-green-50 focus:ring-green-500',
    danger: 'text-red-600 hover:text-red-900 hover:bg-red-50 focus:ring-red-500',
    warning: 'text-yellow-600 hover:text-yellow-900 hover:bg-yellow-50 focus:ring-yellow-500',
    gray: 'text-gray-600 hover:text-gray-900 hover:bg-gray-50 focus:ring-gray-500'
  };
  return colors[props.color];
});

const sizeClasses = computed(() => {
  const sizes = {
    sm: 'p-1',
    md: 'p-2',
    lg: 'p-3'
  };
  return sizes[props.size];
});

const iconSizeClass = computed(() => {
  const sizes = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl'
  };
  return sizes[props.size];
});

const handleClick = (event: Event) => {
  if (!props.disabled) {
    emit('click', event);
  }
};
</script>

<style scoped>
/* IconButton组件特有样式 */
.router-link-active {
  @apply bg-indigo-100 text-indigo-700;
}

/* 确保图标垂直居中 */
i {
  vertical-align: -0.125em;
}
</style>

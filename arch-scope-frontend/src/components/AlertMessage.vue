<template>
  <transition name="fade">
    <div 
      v-if="visible" 
      :class="[
        'alert',
        `alert-${type}`,
        { 'alert-with-close': dismissible }
      ]"
    >
      <div class="alert-icon" v-if="showIcon">
        <!-- 成功图标 -->
        <svg 
          v-if="type === 'success'" 
          xmlns="http://www.w3.org/2000/svg" 
          class="w-6 h-6" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" 
          />
        </svg>
        
        <!-- 错误图标 -->
        <svg 
          v-else-if="type === 'error'" 
          xmlns="http://www.w3.org/2000/svg" 
          class="w-6 h-6" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
          />
        </svg>
        
        <!-- 警告图标 -->
        <svg 
          v-else-if="type === 'warning'" 
          xmlns="http://www.w3.org/2000/svg" 
          class="w-6 h-6" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" 
          />
        </svg>
        
        <!-- 信息图标 -->
        <svg 
          v-else 
          xmlns="http://www.w3.org/2000/svg" 
          class="w-6 h-6" 
          fill="none" 
          viewBox="0 0 24 24" 
          stroke="currentColor"
        >
          <path 
            stroke-linecap="round" 
            stroke-linejoin="round" 
            stroke-width="2" 
            d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
          />
        </svg>
      </div>
      
      <div class="alert-content">
        <div v-if="title" class="alert-title">{{ title }}</div>
        <div class="alert-message">
          <slot>{{ message }}</slot>
        </div>
      </div>
      
      <button 
        v-if="dismissible" 
        class="alert-close" 
        @click="dismiss"
        aria-label="关闭"
      >
        <svg 
          xmlns="http://www.w3.org/2000/svg" 
          class="w-5 h-5" 
          viewBox="0 0 20 20" 
          fill="currentColor"
        >
          <path 
            fill-rule="evenodd" 
            d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" 
            clip-rule="evenodd" 
          />
        </svg>
      </button>
    </div>
  </transition>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';

const props = defineProps({
  // 提示类型：success, error, warning, info
  type: {
    type: String,
    default: 'info',
    validator: (value: string) => ['success', 'error', 'warning', 'info'].includes(value)
  },
  // 提示标题
  title: {
    type: String,
    default: ''
  },
  // 提示消息
  message: {
    type: String,
    default: ''
  },
  // 是否显示图标
  showIcon: {
    type: Boolean,
    default: true
  },
  // 是否可关闭
  dismissible: {
    type: Boolean,
    default: true
  },
  // 自动消失时间（毫秒），0表示不自动消失
  duration: {
    type: Number,
    default: 0
  },
  // 初始可见性
  show: {
    type: Boolean,
    default: true
  }
});

const visible = ref(props.show);
const timer = ref<number | null>(null);

const dismiss = () => {
  visible.value = false;
  clearTimeout(timer.value as number);
  emit('close');
};

const emit = defineEmits(['close']);

// 监听show属性变化
watch(() => props.show, (newValue) => {
  visible.value = newValue;
  if (newValue && props.duration > 0) {
    startTimer();
  }
});

// 设置自动消失定时器
const startTimer = () => {
  if (props.duration > 0) {
    clearTimeout(timer.value as number);
    timer.value = window.setTimeout(() => {
      visible.value = false;
      emit('close');
    }, props.duration);
  }
};

onMounted(() => {
  if (visible.value && props.duration > 0) {
    startTimer();
  }
});

onUnmounted(() => {
  if (timer.value !== null) {
    clearTimeout(timer.value);
  }
});
</script>

<style scoped>
.alert {
  display: flex;
  align-items: flex-start;
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.alert-with-close {
  padding-right: 2.5rem;
  position: relative;
}

.alert-icon {
  flex-shrink: 0;
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-title {
  font-weight: 600;
  margin-bottom: 0.25rem;
}

.alert-message {
  font-size: 0.875rem;
}

.alert-close {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: transparent;
  border: none;
  color: inherit;
  opacity: 0.6;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.alert-close:hover {
  opacity: 1;
  background-color: rgba(0, 0, 0, 0.05);
}

/* 提示类型样式 */
.alert-success {
  background-color: #D1FAE5; /* Tailwind green-100 */
  color: #065F46; /* Tailwind green-800 */
  border-left: 4px solid #10B981; /* Tailwind green-500 */
}

.alert-error {
  background-color: #FEE2E2; /* Tailwind red-100 */
  color: #991B1B; /* Tailwind red-800 */
  border-left: 4px solid #EF4444; /* Tailwind red-500 */
}

.alert-warning {
  background-color: #FEF3C7; /* Tailwind amber-100 */
  color: #92400E; /* Tailwind amber-800 */
  border-left: 4px solid #F59E0B; /* Tailwind amber-500 */
}

.alert-info {
  background-color: #DBEAFE; /* Tailwind blue-100 */
  color: #1E40AF; /* Tailwind blue-800 */
  border-left: 4px solid #3B82F6; /* Tailwind blue-500 */
}

/* 过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s, transform 0.3s;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}
</style> 
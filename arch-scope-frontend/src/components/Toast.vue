<template>
  <Teleport to="body">
    <div
      v-if="visible"
      class="fixed top-4 right-4 z-50 max-w-sm w-full animate-slideInRight"
      :class="toastClasses"
    >
      <div class="flex items-center p-4 rounded-lg shadow-lg border">
        <!-- 图标 -->
        <div class="flex-shrink-0">
          <i :class="iconClasses" class="w-5 h-5"></i>
        </div>
        
        <!-- 内容 -->
        <div class="ml-3 flex-1">
          <p class="text-sm font-medium" :class="titleClasses">
            {{ title }}
          </p>
          <p v-if="message" class="text-sm mt-1" :class="messageClasses">
            {{ message }}
          </p>
        </div>
        
        <!-- 关闭按钮 -->
        <button
          v-if="closable"
          @click="close"
          class="ml-4 flex-shrink-0 rounded-md p-1.5 focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200"
          :class="closeButtonClasses"
        >
          <i class="fas fa-times w-4 h-4"></i>
        </button>
      </div>
      
      <!-- 进度条 -->
      <div
        v-if="showProgress && duration > 0"
        class="h-1 bg-black/10 rounded-b-lg overflow-hidden"
      >
        <div
          class="h-full transition-all ease-linear"
          :class="progressClasses"
          :style="{ width: `${progress}%` }"
        ></div>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'

export interface ToastProps {
  type?: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
  closable?: boolean
  showProgress?: boolean
}

const props = withDefaults(defineProps<ToastProps>(), {
  type: 'info',
  duration: 4000,
  closable: true,
  showProgress: true
})

const emit = defineEmits<{
  close: []
}>()

const visible = ref(false)
const progress = ref(100)
let timer: NodeJS.Timeout | null = null
let progressTimer: NodeJS.Timeout | null = null

// 计算样式类
const toastClasses = computed(() => {
  const baseClasses = 'bg-white border-l-4'
  const typeClasses = {
    success: 'border-success-500',
    error: 'border-error-500',
    warning: 'border-warning-500',
    info: 'border-primary-500'
  }
  return `${baseClasses} ${typeClasses[props.type]}`
})

const iconClasses = computed(() => {
  const baseClasses = 'fas'
  const typeClasses = {
    success: 'fa-check-circle text-success-500',
    error: 'fa-exclamation-circle text-error-500',
    warning: 'fa-exclamation-triangle text-warning-500',
    info: 'fa-info-circle text-primary-500'
  }
  return `${baseClasses} ${typeClasses[props.type]}`
})

const titleClasses = computed(() => {
  const typeClasses = {
    success: 'text-success-800',
    error: 'text-error-800',
    warning: 'text-warning-800',
    info: 'text-primary-800'
  }
  return typeClasses[props.type]
})

const messageClasses = computed(() => {
  const typeClasses = {
    success: 'text-success-600',
    error: 'text-error-600',
    warning: 'text-warning-600',
    info: 'text-primary-600'
  }
  return typeClasses[props.type]
})

const closeButtonClasses = computed(() => {
  const typeClasses = {
    success: 'text-success-400 hover:text-success-600 focus:ring-success-500',
    error: 'text-error-400 hover:text-error-600 focus:ring-error-500',
    warning: 'text-warning-400 hover:text-warning-600 focus:ring-warning-500',
    info: 'text-primary-400 hover:text-primary-600 focus:ring-primary-500'
  }
  return typeClasses[props.type]
})

const progressClasses = computed(() => {
  const typeClasses = {
    success: 'bg-success-500',
    error: 'bg-error-500',
    warning: 'bg-warning-500',
    info: 'bg-primary-500'
  }
  return typeClasses[props.type]
})

// 显示Toast
const show = () => {
  visible.value = true
  
  if (props.duration > 0) {
    // 设置自动关闭定时器
    timer = setTimeout(() => {
      close()
    }, props.duration)
    
    // 设置进度条动画
    if (props.showProgress) {
      const interval = 50 // 更新间隔（毫秒）
      const step = (interval / props.duration) * 100
      
      progressTimer = setInterval(() => {
        progress.value -= step
        if (progress.value <= 0) {
          progress.value = 0
          if (progressTimer) {
            clearInterval(progressTimer)
            progressTimer = null
          }
        }
      }, interval)
    }
  }
}

// 关闭Toast
const close = () => {
  visible.value = false
  if (timer) {
    clearTimeout(timer)
    timer = null
  }
  if (progressTimer) {
    clearInterval(progressTimer)
    progressTimer = null
  }
  emit('close')
}

// 暴露方法给父组件
defineExpose({
  show,
  close
})

onMounted(() => {
  show()
})

onUnmounted(() => {
  if (timer) clearTimeout(timer)
  if (progressTimer) clearInterval(progressTimer)
})
</script>

<style scoped>
@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-slideInRight {
  animation: slideInRight 0.3s ease-out;
}
</style>

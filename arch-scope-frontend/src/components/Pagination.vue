<template>
  <div class="flex items-center justify-between animate-fade-in">
    <div class="text-sm text-gray-700">
      显示 <span class="font-medium">{{ start }}</span> 到
      <span class="font-medium">{{ end }}</span> 共
      <span class="font-medium">{{ total }}</span> 个项目
    </div>
    <div class="flex items-center space-x-2">
      <button
        class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button"
        :disabled="currentPage === 1"
        @click="onPageChange(currentPage - 1)"
      >
        上一页
      </button>
      <button
        v-for="page in totalPages"
        :key="page"
        @click="onPageChange(page)"
        :class="[
          'px-3 py-1 rounded-md animate-button',
          currentPage === page
            ? 'bg-indigo-600 text-white hover:bg-indigo-700'
            : 'bg-white border border-gray-300 text-gray-500 hover:bg-gray-50',
        ]"
      >
        {{ page }}
      </button>
      <button
        class="px-3 py-1 rounded-md bg-white border border-gray-300 text-gray-500 hover:bg-gray-50 disabled:opacity-50 animate-button"
        :disabled="currentPage === totalPages"
        @click="onPageChange(currentPage + 1)"
      >
        下一页
      </button>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent } from "vue";

export default defineComponent({
  name: "Pagination",
  props: {
    currentPage: {
      type: Number,
      required: true,
    },
    totalPages: {
      type: Number,
      required: true,
    },
    start: {
      type: Number,
      required: true,
    },
    end: {
      type: Number,
      required: true,
    },
    total: {
      type: Number,
      required: true,
    },
  },
  emits: ["page-change"],
  setup(props, { emit }) {
    const onPageChange = (page: number) => {
      emit("page-change", page);
    };

    return {
      onPageChange,
    };
  },
});
</script>

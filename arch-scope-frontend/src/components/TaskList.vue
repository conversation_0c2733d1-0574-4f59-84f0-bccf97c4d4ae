<template>
  <div class="task-list">
    <div class="flex justify-between items-center mb-4">
      <h2 class="text-xl font-semibold">文档生成任务</h2>
      <div class="flex gap-2">
        <select
          v-model="statusFilter"
          class="px-3 py-1.5 border rounded-md text-sm"
        >
          <option value="all">所有状态</option>
          <option value="pending">待处理</option>
          <option value="processing">处理中</option>
          <option value="completed">已完成</option>
          <option value="failed">失败</option>
        </select>
        <button
          class="px-3 py-1.5 bg-green-600 text-white rounded-md hover:bg-green-700 text-sm"
          @click="createNewTask"
        >
          新建任务
        </button>
      </div>
    </div>

    <div v-if="taskStore.loading" class="text-center py-4">
      <p>加载中...</p>
    </div>
    <div v-else-if="taskStore.error" class="text-red-500 py-4">
      <p>{{ taskStore.error }}</p>
    </div>
    <div v-else-if="filteredTasks.length === 0" class="text-center py-8 bg-white rounded-lg shadow-sm">
      <p class="text-gray-500">暂无任务</p>
    </div>
    <div v-else class="overflow-x-auto">
      <table class="min-w-full bg-white rounded-lg shadow-sm">
        <thead>
          <tr class="bg-gray-100 text-left">
            <th class="py-3 px-4 font-medium">ID</th>
            <th class="py-3 px-4 font-medium">任务类型</th>
            <th class="py-3 px-4 font-medium">状态</th>
            <th class="py-3 px-4 font-medium">创建时间</th>
            <th class="py-3 px-4 font-medium">完成时间</th>
            <th class="py-3 px-4 font-medium">操作</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="task in filteredTasks" :key="task.id" class="border-t hover:bg-gray-50">
            <td class="py-3 px-4 text-sm">{{ task.id }}</td>
            <td class="py-3 px-4">
              <div class="flex items-center">
                <span :class="getTaskTypeClass(task.type)" class="px-2 py-0.5 rounded-full text-xs mr-2"></span>
                {{ task.name }}
              </div>
            </td>
            <td class="py-3 px-4">
              <span :class="getStatusClass(task.status)" class="px-2 py-1 rounded-full text-xs">
                {{ getStatusText(task.status) }}
              </span>
            </td>
            <td class="py-3 px-4 text-sm">{{ formatDate(task.createdAt) }}</td>
            <td class="py-3 px-4 text-sm">{{ task.updatedAt && (task.status === 'COMPLETED' || task.status === 'FAILED' || task.status === 'CANCELLED') ? formatDate(task.updatedAt) : '-' }}</td>
            <td class="py-3 px-4">
              <div class="flex space-x-2">
                <button
                  v-if="task.status === 'COMPLETED'"
                  class="text-blue-600 hover:text-blue-800 text-sm"
                  @click="viewTaskResult(task.id)"
                >
                  查看结果
                </button>
                <button
                  v-if="task.status === 'FAILED'"
                  class="text-orange-600 hover:text-orange-800 text-sm"
                  @click="retryTask(task.id)"
                >
                  重试
                </button>
                <button
                  v-if="task.status === 'PENDING'"
                  class="text-red-600 hover:text-red-800 text-sm"
                  @click="cancelTask(task.id)"
                >
                  取消
                </button>
                <button
                  class="text-gray-600 hover:text-gray-800 text-sm"
                  @click="viewTaskDetails(task.id)"
                >
                  详情
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useTaskStore } from '@/stores/task';

const props = defineProps({
  projectId: {
    type: String,
    required: true
  }
});

const router = useRouter();
const taskStore = useTaskStore();
const statusFilter = ref('all');

const filteredTasks = computed(() => {
  if (statusFilter.value === 'all') {
    return taskStore.tasks;
  }
  return taskStore.tasks.filter(task => task.status === statusFilter.value);
});

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'pending': '待处理',
    'processing': '处理中',
    'completed': '已完成',
    'failed': '失败'
  };
  return statusMap[status] || status;
};

const getStatusClass = (status: string) => {
  const classMap: Record<string, string> = {
    'pending': 'bg-yellow-100 text-yellow-800',
    'processing': 'bg-blue-100 text-blue-800',
    'completed': 'bg-green-100 text-green-800',
    'failed': 'bg-red-100 text-red-800'
  };
  return classMap[status] || '';
};

const getTaskTypeClass = (type: string) => {
  const classMap: Record<string, string> = {
    'doc_generation': 'bg-purple-500',
    'code_analysis': 'bg-blue-500',
    'health_check': 'bg-green-500'
  };
  return classMap[type] || 'bg-gray-500';
};

const createNewTask = () => {
  router.push(`/projects/${props.projectId}/tasks/new`);
};

const viewTaskResult = (taskId: number) => {
  const task = taskStore.tasks.find(t => t.id === taskId);
  if (task && task.type === 'DOC_GENERATE' && task.result) {
    router.push(`/projects/${props.projectId}/docs/${task.result}`);
  } else {
    router.push(`/projects/${props.projectId}/tasks/${taskId}/result`);
  }
};

const retryTask = async (taskId: number) => {
  try {
    await taskStore.retryTask(taskId);
    alert(`任务 ${taskId} 已重新提交`);
  } catch (err) {
    console.error('重试任务失败:', err);
  }
};

const cancelTask = async (taskId: number) => {
  if (confirm('确定要取消这个任务吗？')) {
    try {
      await taskStore.cancelTask(taskId);
      alert(`任务 ${taskId} 已取消`);
    } catch (err) {
      console.error('取消任务失败:', err);
    }
  }
};

const viewTaskDetails = (taskId: number) => {
  router.push(`/projects/${props.projectId}/tasks/${taskId}`);
};

onMounted(() => {
  taskStore.fetchTasks(Number(props.projectId));
});
</script> 
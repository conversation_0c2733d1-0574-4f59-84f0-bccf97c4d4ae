<script setup lang="ts">
import { ref, onMounted, onUnmounted } from "vue";
import { useRouter } from "vue-router";

const router = useRouter();
const isScrolled = ref(false);
const isMobileMenuOpen = ref(false);

const handleScroll = () => {
  isScrolled.value = window.scrollY > 50;
};

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId);
  if (element) {
    element.scrollIntoView({ behavior: "smooth" });
  }
  isMobileMenuOpen.value = false;
};

const navigateToProjects = () => {
  router.push("/projects");
};

onMounted(() => {
  window.addEventListener("scroll", handleScroll);
});

onUnmounted(() => {
  window.removeEventListener("scroll", handleScroll);
});
</script>

<template>
  <nav
    class="fixed top-0 left-0 right-0 z-50 transition-all duration-300"
    :class="
      isScrolled ? 'bg-white/95 backdrop-blur-md shadow-lg' : 'bg-transparent'
    "
  >
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex items-center">
          <div
            class="flex-shrink-0 cursor-pointer group"
            @click="scrollToSection('hero')"
          >
            <div class="flex items-center space-x-3">
              <div
                class="relative overflow-hidden rounded-lg transform group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 ease-out"
              >
                <img
                  src="@/assets/logo.png"
                  alt="ArchScope"
                  class="h-10 w-10 object-contain"
                />
                <div
                  class="absolute inset-0 bg-gradient-to-br from-primary-500/20 to-secondary-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                ></div>
              </div>
              <span
                class="text-xl font-bold transition-all duration-300 group-hover:text-primary-400"
                :class="isScrolled ? 'text-gray-900' : 'text-white'"
              >
                ArchScope
              </span>
            </div>
          </div>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-8">
            <button
              @click="scrollToSection('features')"
              class="relative px-3 py-2 font-medium transition-all duration-300 ease-out group overflow-hidden rounded-lg"
              :class="
                isScrolled
                  ? 'text-gray-700 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'
              "
            >
              <span class="relative z-10">功能特性</span>
              <div
                class="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-lg"
              ></div>
            </button>
            <button
              @click="scrollToSection('how-it-works')"
              class="relative px-3 py-2 font-medium transition-all duration-300 ease-out group overflow-hidden rounded-lg"
              :class="
                isScrolled
                  ? 'text-gray-700 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'
              "
            >
              <span class="relative z-10">工作原理</span>
              <div
                class="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-lg"
              ></div>
            </button>
            <button
              @click="scrollToSection('pricing')"
              class="relative px-3 py-2 font-medium transition-all duration-300 ease-out group overflow-hidden rounded-lg"
              :class="
                isScrolled
                  ? 'text-gray-700 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'
              "
            >
              <span class="relative z-10">价格方案</span>
              <div
                class="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-lg"
              ></div>
            </button>
            <button
              @click="scrollToSection('testimonials')"
              class="relative px-3 py-2 font-medium transition-all duration-300 ease-out group overflow-hidden rounded-lg"
              :class="
                isScrolled
                  ? 'text-gray-700 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'
              "
            >
              <span class="relative z-10">成功案例</span>
              <div
                class="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-lg"
              ></div>
            </button>
            <button
              @click="scrollToSection('faq')"
              class="relative px-3 py-2 font-medium transition-all duration-300 ease-out group overflow-hidden rounded-lg"
              :class="
                isScrolled
                  ? 'text-gray-700 hover:text-primary-600'
                  : 'text-white/90 hover:text-white'
              "
            >
              <span class="relative z-10">常见问题</span>
              <div
                class="absolute inset-0 bg-gradient-to-r from-primary-500/10 to-secondary-500/10 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left rounded-lg"
              ></div>
            </button>
          </div>
        </div>

        <!-- CTA Button -->
        <div class="hidden md:block">
          <button
            @click="navigateToProjects"
            :class="
              isScrolled
                ? 'bg-gradient-to-r from-primary-600 to-primary-700 text-white hover:from-primary-700 hover:to-primary-800 shadow-lg'
                : 'bg-white text-primary-600 hover:bg-gray-50 shadow-xl border-2 border-white/20'
            "
            class="px-6 py-2.5 rounded-lg font-semibold transform hover:scale-105 transition-all duration-200 hover:shadow-2xl"
          >
            立即开始
          </button>
        </div>

        <!-- Mobile menu button -->
        <div class="md:hidden">
          <button
            @click="isMobileMenuOpen = !isMobileMenuOpen"
            class="inline-flex items-center justify-center p-2 rounded-md transition-colors duration-200"
            :class="
              isScrolled
                ? 'text-gray-700 hover:bg-gray-100'
                : 'text-white hover:bg-white/10'
            "
          >
            <svg
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                v-if="!isMobileMenuOpen"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
              <path
                v-else
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </div>

    <!-- Mobile Navigation Menu -->
    <div
      v-show="isMobileMenuOpen"
      class="md:hidden bg-white border-t border-gray-200 shadow-lg"
    >
      <div class="px-2 pt-2 pb-3 space-y-1">
        <button
          @click="scrollToSection('features')"
          class="block px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 rounded-lg w-full text-left transition-all duration-300 transform hover:translate-x-2"
        >
          功能特性
        </button>
        <button
          @click="scrollToSection('how-it-works')"
          class="block px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 rounded-lg w-full text-left transition-all duration-300 transform hover:translate-x-2"
        >
          工作原理
        </button>
        <button
          @click="scrollToSection('pricing')"
          class="block px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 rounded-lg w-full text-left transition-all duration-300 transform hover:translate-x-2"
        >
          价格方案
        </button>
        <button
          @click="scrollToSection('testimonials')"
          class="block px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 rounded-lg w-full text-left transition-all duration-300 transform hover:translate-x-2"
        >
          成功案例
        </button>
        <button
          @click="scrollToSection('faq')"
          class="block px-4 py-3 text-gray-700 hover:text-primary-600 hover:bg-gradient-to-r hover:from-primary-50 hover:to-secondary-50 rounded-lg w-full text-left transition-all duration-300 transform hover:translate-x-2"
        >
          常见问题
        </button>
        <button
          @click="navigateToProjects"
          class="block w-full mt-4 bg-gradient-to-r from-primary-600 to-primary-700 text-white px-4 py-3 rounded-lg font-semibold text-center shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
        >
          立即开始
        </button>
      </div>
    </div>
  </nav>
</template>

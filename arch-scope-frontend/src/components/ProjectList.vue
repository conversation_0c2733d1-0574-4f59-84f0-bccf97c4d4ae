<template>
  <div class="project-list">
    <h2 class="text-xl font-semibold mb-4">项目列表</h2>
    <div v-if="projectStore.loading" class="text-center py-4">
      <p>加载中...</p>
    </div>
    <div v-else-if="projectStore.error" class="text-red-500 py-4">
      <p>{{ projectStore.error }}</p>
    </div>
    <div v-else-if="projectStore.projects.length === 0" class="text-center py-4">
      <p>暂无项目，请先创建一个新项目</p>
    </div>
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div 
        v-for="project in projectStore.projects" 
        :key="project.id" 
        class="border rounded-lg p-4 hover:shadow-md transition-shadow cursor-pointer"
        @click="navigateToProject(project.id.toString())"
      >
        <h3 class="text-lg font-medium">{{ project.name }}</h3>
        <p class="text-sm text-gray-600 mt-1">{{ project.description }}</p>
        <div class="flex justify-between items-center mt-3">
          <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Git</span>
          <span class="text-xs text-gray-500">更新于: {{ formatDate(project.updatedAt) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { useProjectStore } from '@/stores/project';

const router = useRouter();
const projectStore = useProjectStore();

const navigateToProject = (id: string) => {
  router.push(`/projects/${id}`);
};

const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN');
};

onMounted(() => {
  projectStore.fetchProjects();
});
</script> 
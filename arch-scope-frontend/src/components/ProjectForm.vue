<template>
  <div class="project-form">
    <h2 class="text-xl font-semibold mb-4">{{ isEdit ? '编辑项目' : '创建新项目' }}</h2>
    <form @submit.prevent="submitForm">
      <div class="mb-4">
        <label for="name" class="block text-sm font-medium mb-1">项目名称</label>
        <input 
          type="text" 
          id="name" 
          v-model="form.name" 
          class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        />
      </div>
      
      <div class="mb-4">
        <label for="description" class="block text-sm font-medium mb-1">项目描述</label>
        <textarea 
          id="description" 
          v-model="form.description" 
          rows="3"
          class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
        ></textarea>
      </div>
      
      <div class="mb-4">
        <label for="repoType" class="block text-sm font-medium mb-1">代码仓库类型</label>
        <select 
          id="repoType" 
          v-model="form.repoType"
          class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
        >
          <option value="">请选择</option>
          <option value="GitHub">GitHub</option>
          <option value="GitLab">GitLab</option>
          <option value="Gitee">Gitee</option>
          <option value="Bitbucket">Bitbucket</option>
        </select>
      </div>
      
      <div class="mb-4">
        <label for="repoUrl" class="block text-sm font-medium mb-1">仓库地址</label>
        <input 
          type="url" 
          id="repoUrl" 
          v-model="form.repoUrl" 
          class="w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          required
          placeholder="https://github.com/username/repo"
        />
      </div>
      
      <div class="mb-4">
        <label for="languages" class="block text-sm font-medium mb-1">支持的编程语言</label>
        <div class="flex flex-wrap gap-2">
          <div 
            v-for="lang in availableLanguages"
            :key="lang.value"
            class="inline-flex items-center"
          >
            <input 
              type="checkbox" 
              :id="lang.value" 
              :value="lang.value" 
              v-model="form.languages"
              class="mr-1"
            />
            <label :for="lang.value" class="text-sm">{{ lang.label }}</label>
          </div>
        </div>
      </div>
      
      <div class="flex justify-end gap-3 mt-6">
        <button 
          type="button" 
          class="px-4 py-2 border rounded-md hover:bg-gray-100"
          @click="$emit('cancel')"
        >
          取消
        </button>
        <button 
          type="submit" 
          class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          :disabled="loading"
        >
          {{ loading ? '处理中...' : (isEdit ? '保存更改' : '创建项目') }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, defineProps, defineEmits } from 'vue';
import request from '@/utils/request';

const props = defineProps({
  projectId: {
    type: String,
    default: ''
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['save', 'cancel']);

interface ProjectForm {
  name: string;
  description: string;
  repoType: string;
  repoUrl: string;
  languages: string[];
}

const form = reactive<ProjectForm>({
  name: '',
  description: '',
  repoType: '',
  repoUrl: '',
  languages: []
});

const loading = ref(false);
const error = ref('');

const availableLanguages = [
  { value: 'java', label: 'Java' },
  { value: 'python', label: 'Python' },
  { value: 'javascript', label: 'JavaScript' },
  { value: 'typescript', label: 'TypeScript' },
  { value: 'go', label: 'Go' },
  { value: 'csharp', label: 'C#' },
  { value: 'cpp', label: 'C++' }
];

const fetchProjectDetails = async () => {
  if (!props.isEdit || !props.projectId) return;
  
  loading.value = true;
  try {
    // 使用真实API获取项目详情
    const response = await request.get(`/v1/projects/${props.projectId}`);
    const projectData = response.data;

    // 填充表单数据
    form.name = projectData.name || '';
    form.description = projectData.description || '';
    form.repoType = projectData.repositoryType || 'GitHub';
    form.repoUrl = projectData.repositoryUrl || '';
    form.languages = projectData.languages || [];
  } catch (err) {
    console.error('获取项目详情失败:', err);
    error.value = '获取项目详情失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

const submitForm = async () => {
  loading.value = true;
  try {
    let response;
    if (props.isEdit && props.projectId) {
      // 更新现有项目
      response = await request.put(`/v1/projects/${props.projectId}`, form);
    } else {
      // 创建新项目
      response = await request.post('/v1/projects', form);
    }

    // 向父组件传递保存的数据
    emit('save', response.data);
  } catch (err) {
    console.error('保存项目失败:', err);
    error.value = '保存项目失败，请稍后重试';
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  fetchProjectDetails();
});
</script> 
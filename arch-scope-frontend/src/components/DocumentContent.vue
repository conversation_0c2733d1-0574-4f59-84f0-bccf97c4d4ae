<template>
  <div class="document-content">
    <div v-if="loading" class="flex justify-center items-center py-10">
      <div class="spinner inline-block w-8 h-8 border-4 border-t-indigo-500 border-r-transparent border-b-indigo-500 border-l-transparent rounded-full animate-spin"></div>
      <p class="ml-3 text-gray-600">加载文档中...</p>
    </div>
    
    <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative" role="alert">
      <strong class="font-bold">加载失败！</strong>
      <span class="block sm:inline"> {{ error }}</span>
    </div>
    
    <div v-else-if="!content" class="text-center py-10">
      <p class="text-gray-500">暂无文档内容</p>
    </div>
    
    <div v-else ref="contentRef" class="prose max-w-none" v-html="content"></div>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, nextTick, watch, onMounted, ref } from 'vue'
import mermaid from 'mermaid'

export default defineComponent({
  name: 'DocumentContent',

  props: {
    content: {
      type: String,
      default: ''
    },
    loading: {
      type: Boolean,
      default: false
    },
    error: {
      type: String,
      default: ''
    }
  },

  setup(props) {
    const contentRef = ref<HTMLElement>()

    // 初始化Mermaid
    onMounted(() => {
      mermaid.initialize({
        startOnLoad: false,
        theme: 'default',
        securityLevel: 'loose',
        flowchart: {
          useMaxWidth: true,
          htmlLabels: true
        },
        sequence: {
          useMaxWidth: true
        },
        gantt: {
          useMaxWidth: true
        }
      })
    })

    // 渲染Mermaid图表
    const renderMermaidDiagrams = async () => {
      if (!contentRef.value) return

      try {
        // 查找所有的Mermaid代码块
        const mermaidBlocks = contentRef.value.querySelectorAll('pre > code.language-mermaid, code.language-mermaid')

        for (let i = 0; i < mermaidBlocks.length; i++) {
          const codeBlock = mermaidBlocks[i] as HTMLElement
          const content = codeBlock.textContent || ''

          if (content.trim()) {
            // 创建Mermaid容器
            const mermaidDiv = document.createElement('div')
            mermaidDiv.className = 'mermaid'
            mermaidDiv.style.textAlign = 'center'
            mermaidDiv.style.margin = '1rem 0'

            // 生成唯一ID
            const id = `mermaid-${Date.now()}-${i}`
            mermaidDiv.id = id

            // 渲染图表
            const { svg } = await mermaid.render(id, content)
            mermaidDiv.innerHTML = svg

            // 替换原来的代码块
            const preElement = codeBlock.closest('pre') || codeBlock
            preElement.parentElement?.replaceChild(mermaidDiv, preElement)
          }
        }
      } catch (error) {
        console.error('Mermaid渲染失败:', error)
      }
    }

    // 监听内容变化，重新渲染Mermaid图表
    watch(() => props.content, async () => {
      if (props.content && !props.loading && !props.error) {
        await nextTick()
        await renderMermaidDiagrams()

        // 调试：检查DOM结构
        if (contentRef.value) {
          console.log('DocumentContent DOM结构:', contentRef.value.innerHTML.substring(0, 500))
          console.log('DocumentContent类名:', contentRef.value.className)
          const proseElements = contentRef.value.querySelectorAll('.prose')
          console.log('找到的.prose元素数量:', proseElements.length)
          if (proseElements.length > 0) {
            console.log('第一个.prose元素的HTML:', proseElements[0].innerHTML.substring(0, 200))
          }
        }
      }
    }, { immediate: true })

    return {
      contentRef
    }
  }
})
</script>

<style>
/* 移除scoped，使用全局样式但添加特定前缀避免冲突 */
.document-content {
  width: 100%;
}

/* 测试基础样式是否生效 */
.document-content .prose {
  border: 2px solid red !important;
  background-color: yellow !important;
  max-width: none;
}

.document-content .prose h1 {
  font-size: 1.875rem; /* 3xl */
  margin-bottom: 1.5rem;
  padding-bottom: 0.5rem;
  font-weight: 700;
  color: #1F2937; /* Gray-800 */
  border-bottom: 2px solid #E5E7EB; /* Gray-200 */
  position: relative;
}

.document-content .prose h1::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 60px;
  height: 2px;
  background: linear-gradient(90deg, #4F46E5 0%, #7C3AED 100%);
}

.document-content :deep(.prose h2) {
  font-size: 1.5rem; /* 2xl */
  margin-top: 2.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: #1F2937; /* Gray-800 */
  position: relative;
  padding-left: 1rem;
}

.document-content :deep(.prose h2::before) {
  content: '';
  position: absolute;
  left: 0;
  top: 0.5rem;
  width: 4px;
  height: 1.5rem;
  background: linear-gradient(180deg, #4F46E5 0%, #7C3AED 100%);
  border-radius: 2px;
}

.prose h3 {
  font-size: 1.25rem; /* xl */
  margin-top: 2rem;
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: #475569; /* Slate-600 */
  position: relative;
  padding-left: 0.75rem;
}

.prose h3::before {
  content: '▶';
  position: absolute;
  left: 0;
  top: 0;
  color: #4F46E5;
  font-size: 0.75rem;
}

.prose h4 {
  font-size: 1.125rem; /* lg */
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: #6B7280; /* Gray-500 */
  position: relative;
  padding-left: 0.5rem;
}

.prose h4::before {
  content: '•';
  position: absolute;
  left: 0;
  top: 0;
  color: #7C3AED;
  font-weight: bold;
}

/* 列表样式增强 - 模拟原型中的彩色图标效果 */
.document-content :deep(.prose ul li) {
  display: flex;
  align-items: center;
  margin-bottom: 0.75rem;
  position: relative;
  list-style: none;
}

.document-content :deep(.prose ul li::before) {
  content: '✓';
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 50%;
  margin-right: 0.75rem;
  font-size: 0.75rem;
  font-weight: bold;
  flex-shrink: 0;
}

/* 为不同层级的列表项使用不同的图标和颜色 */
.prose ul ul li::before {
  content: '→';
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.prose ol li {
  display: flex;
  align-items: flex-start;
  margin-bottom: 0.75rem;
  position: relative;
  counter-increment: list-counter;
}

.prose ol {
  counter-reset: list-counter;
}

.prose ol li::before {
  content: counter(list-counter);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
  border-radius: 50%;
  margin-right: 0.75rem;
  font-size: 0.875rem;
  font-weight: bold;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

/* 特殊样式的列表项 */
.prose ul li .inline-flex {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

/* 段落样式增强 */
.document-content :deep(.prose p) {
  margin-bottom: 1rem;
  line-height: 1.7;
  color: #374151;
}

.document-content :deep(.prose p:first-of-type) {
  font-size: 1.125rem;
  color: #1F2937;
  font-weight: 500;
}

/* 强调文本样式 */
.prose strong {
  font-weight: 600;
  color: #1F2937;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

.prose em {
  font-style: italic;
  color: #6366F1;
}

/* 代码块样式增强 */
.document-content :deep(.prose code) {
  background: linear-gradient(135deg, #EEF2FF 0%, #E0E7FF 100%);
  color: #4F46E5;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  border: 1px solid #C7D2FE;
  box-shadow: 0 1px 2px rgba(79, 70, 229, 0.1);
}

.prose pre {
  background: linear-gradient(135deg, #1E293B 0%, #0F172A 100%);
  border-radius: 0.5rem;
  padding: 1rem;
  margin: 1.5rem 0;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  border: 1px solid #334155;
}

.prose pre code {
  background: transparent;
  color: #E2E8F0;
  border: none;
  box-shadow: none;
  padding: 0;
}

/* 引用块样式 */
.prose blockquote {
  border-left: 4px solid #4F46E5;
  background: linear-gradient(135deg, #F8FAFC 0%, #F1F5F9 100%);
  padding: 1.5rem;
  margin: 1.5rem 0;
  border-radius: 0.5rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.prose blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 1rem;
  font-size: 3rem;
  color: #4F46E5;
  opacity: 0.3;
  font-family: serif;
}

/* 特殊内容区块 - 模拟原型中的灰色背景区域 */
.prose .highlight-box {
  background: linear-gradient(135deg, #F9FAFB 0%, #F3F4F6 100%);
  padding: 1.5rem;
  border-radius: 0.75rem;
  margin: 2rem 0;
  border: 1px solid #E5E7EB;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* 为包含特定关键词的段落添加特殊样式 - 使用类名而不是复杂选择器 */
.prose .feature-highlight {
  background: linear-gradient(135deg, #EEF2FF 0%, #E0E7FF 100%);
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid #4F46E5;
  margin: 1rem 0;
}

/* 表格样式增强 */
.prose table {
  border-collapse: collapse;
  width: 100%;
  margin: 1.5rem 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  overflow: hidden;
}

.prose table th,
.prose table td {
  padding: 0.75rem 1rem;
  text-align: left;
  border-bottom: 1px solid #E5E7EB;
}

.prose table th {
  background-color: #F9FAFB;
  font-weight: 600;
  color: #374151;
}

.prose table tr:hover {
  background-color: #F9FAFB;
}

.prose h4 {
  font-size: 1.25rem;
  margin-top: 1.75em;
  margin-bottom: 0.5em;
  font-weight: 600;
  color: #64748B; /* Slate-500 */
}

.prose ul {
  list-style: disc;
  margin-left: 1.5em;
}

.prose li {
  margin-bottom: 0.5em;
}

.prose p {
  line-height: 1.7;
  margin-bottom: 1rem;
}

.prose p.text-lg {
  font-size: 1.125rem;
  line-height: 1.75;
}

.prose code {
  background-color: #EEF2FF; /* Indigo-50 */
  color: #4F46E5; /* Indigo-600 */
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875em;
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

/* 特殊的高亮代码样式 */
.prose code.bg-indigo-50 {
  background-color: #EEF2FF;
  color: #4F46E5;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

/* 列表样式 */
.prose ul.space-y-4 > li {
  margin-bottom: 1rem;
}

.prose .flex.items-center {
  display: flex;
  align-items: center;
}

/* 图标容器样式 */
.prose .flex-shrink-0 {
  flex-shrink: 0;
}

.prose .bg-blue-100 {
  background-color: #DBEAFE;
}

.prose .bg-indigo-100 {
  background-color: #E0E7FF;
}

.prose .bg-green-100 {
  background-color: #DCFCE7;
}

.prose .text-blue-600 {
  color: #2563EB;
}

.prose .text-indigo-600 {
  color: #4F46E5;
}

.prose .text-green-600 {
  color: #16A34A;
}

/* 背景容器样式 */
.prose .bg-gray-50 {
  background-color: #F9FAFB;
  padding: 1.25rem;
  border-radius: 0.5rem;
  border: 1px solid #E5E7EB;
  margin-bottom: 1.5rem;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.spinner {
  animation: spin 1s linear infinite;
}

/* Mermaid图表样式 */
.prose .mermaid {
  display: flex;
  justify-content: center;
  margin: 2rem 0;
  padding: 1rem;
  background-color: #FAFAFA;
  border: 1px solid #E5E7EB;
  border-radius: 0.5rem;
  overflow-x: auto;
}

.prose .mermaid svg {
  max-width: 100%;
  height: auto;
}

/* 响应式Mermaid图表 */
@media (max-width: 768px) {
  .prose .mermaid {
    margin: 1rem -1rem;
    border-radius: 0;
    border-left: none;
    border-right: none;
  }
}
</style>

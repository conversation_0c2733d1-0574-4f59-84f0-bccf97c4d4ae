<template>
  <div class="version-selector">
    <div class="flex items-center mb-3">
      <label :for="id" class="text-gray-400 text-sm font-medium whitespace-nowrap mr-2">版本:</label>
      <select 
        :id="id" 
        v-model="selectedVersionModel"
        class="form-select flex-grow px-2 py-1 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded transition ease-in-out m-0 focus:text-gray-700 focus:bg-white focus:border-blue-600 focus:outline-none"
      >
        <option v-for="version in versions" :key="version.id" :value="version.id">
          {{ version.versionTag }} ({{ formatDate(version.timestamp) }})
        </option>
      </select>
    </div>
    <button 
      v-if="showCompareButton"
      class="w-full bg-gray-700 hover:bg-gray-600 text-white font-semibold py-2 px-4 border border-gray-600 rounded shadow transition duration-200 text-sm"
      @click="$emit('compare')"
    >
      <i class="fas fa-code-branch mr-2"></i> 版本对比
    </button>
  </div>
</template>

<script lang="ts">
import { defineComponent, computed, PropType } from 'vue'
import { DocumentVersion } from '@/stores/project'

export default defineComponent({
  name: 'VersionSelector',
  
  props: {
    versions: {
      type: Array as PropType<DocumentVersion[]>,
      required: true
    },
    selectedVersion: {
      type: [Number, String],
      required: true
    },
    id: {
      type: String,
      default: 'version-select'
    },
    showCompareButton: {
      type: Boolean,
      default: true
    }
  },
  
  emits: ['update:selectedVersion', 'compare'],
  
  setup(props, { emit }) {
    // 计算属性，用于双向绑定
    const selectedVersionModel = computed({
      get: () => props.selectedVersion,
      set: (value) => emit('update:selectedVersion', value)
    })
    
    // 格式化日期
    const formatDate = (dateString: string) => {
      if (!dateString) return '未知日期'
      
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN', { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric' 
      })
    }
    
    return {
      selectedVersionModel,
      formatDate
    }
  }
})
</script>

<style scoped>
.form-select {
  display: block;
  width: 100%;
  padding: 0.375rem 2.25rem 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #1F2937;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-select:focus {
  border-color: #4F46E5;
  outline: 0;
  box-shadow: 0 0 0 0.25rem rgba(79, 70, 229, 0.25);
}
</style>

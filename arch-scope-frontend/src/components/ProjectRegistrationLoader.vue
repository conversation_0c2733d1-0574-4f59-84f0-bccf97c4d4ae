<template>
  <div v-if="isVisible" class="project-registration-loader">
    <!-- 覆盖层 -->
    <div class="loader-overlay" @click.self="handleOverlayClick">
      <div class="loader-container">
        <!-- 加载动画 -->
        <div class="loader-content">
          <div class="spinner-wrapper">
            <svg class="spinner" viewBox="0 0 50 50">
              <circle
                class="spinner-path"
                cx="25"
                cy="25"
                r="20"
                fill="none"
                stroke-width="3"
              ></circle>
            </svg>
          </div>
          
          <!-- 状态文本 -->
          <div class="status-text">
            <h3 class="status-title">{{ currentStep.title }}</h3>
            <p class="status-description">{{ currentStep.description }}</p>
          </div>
          
          <!-- 进度条 -->
          <div class="progress-container">
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: `${progress}%` }"
              ></div>
            </div>
            <div class="progress-text">{{ Math.round(progress) }}%</div>
          </div>
          
          <!-- 步骤指示器 -->
          <div class="steps-indicator">
            <div 
              v-for="(step, index) in steps" 
              :key="index"
              class="step-item"
              :class="{
                'completed': index < currentStepIndex,
                'active': index === currentStepIndex,
                'pending': index > currentStepIndex
              }"
            >
              <div class="step-icon">
                <i v-if="index < currentStepIndex" class="fas fa-check"></i>
                <i v-else-if="index === currentStepIndex" class="fas fa-spinner fa-spin"></i>
                <span v-else>{{ index + 1 }}</span>
              </div>
              <span class="step-label">{{ step.label }}</span>
            </div>
          </div>
          
          <!-- 操作按钮 -->
          <div class="action-buttons">
            <button 
              v-if="showCancelButton"
              @click="handleCancel"
              class="cancel-button"
              :disabled="!canCancel"
            >
              <i class="fas fa-times mr-2"></i>
              取消注册
            </button>
          </div>
          
          <!-- 超时警告 -->
          <div v-if="showTimeoutWarning" class="timeout-warning">
            <i class="fas fa-exclamation-triangle mr-2"></i>
            操作时间较长，请耐心等待...
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

interface LoadingStep {
  label: string
  title: string
  description: string
  duration?: number // 预估持续时间（秒）
}

interface Props {
  isVisible: boolean
  currentStepIndex: number
  showCancelButton?: boolean
  canCancel?: boolean
  timeoutSeconds?: number
}

const props = withDefaults(defineProps<Props>(), {
  showCancelButton: true,
  canCancel: true,
  timeoutSeconds: 30
})

const emit = defineEmits<{
  cancel: []
  timeout: []
}>()

// 注册步骤定义
const steps: LoadingStep[] = [
  {
    label: '验证仓库',
    title: '正在验证Git仓库...',
    description: '检查仓库地址格式有效性',
    duration: 2
  },
  {
    label: '解析分支',
    title: '正在解析分支信息...',
    description: '获取仓库分支列表并检查项目唯一性',
    duration: 5
  },
  {
    label: '获取元数据',
    title: '正在获取项目元数据...',
    description: '读取项目描述、README和配置信息',
    duration: 4
  },
  {
    label: '创建项目',
    title: '正在创建项目...',
    description: '保存项目信息到数据库',
    duration: 3
  }
]

// 响应式状态
const startTime = ref<number>(0)
const elapsedTime = ref<number>(0)
const showTimeoutWarning = ref(false)
const timeoutTimer = ref<NodeJS.Timeout | null>(null)
const progressTimer = ref<NodeJS.Timeout | null>(null)

// 计算属性
const currentStep = computed(() => {
  return steps[props.currentStepIndex] || steps[0]
})

const progress = computed(() => {
  if (props.currentStepIndex >= steps.length) {
    return 100
  }
  
  const baseProgress = (props.currentStepIndex / steps.length) * 100
  const stepProgress = (elapsedTime.value / (currentStep.value.duration || 5)) * (100 / steps.length)
  
  return Math.min(baseProgress + stepProgress, 100)
})

// 监听可见性变化
watch(() => props.isVisible, (newValue) => {
  if (newValue) {
    startLoading()
  } else {
    stopLoading()
  }
})

// 监听步骤变化
watch(() => props.currentStepIndex, () => {
  elapsedTime.value = 0
})

// 开始加载
const startLoading = () => {
  startTime.value = Date.now()
  elapsedTime.value = 0
  showTimeoutWarning.value = false
  
  // 启动进度计时器
  progressTimer.value = setInterval(() => {
    elapsedTime.value = (Date.now() - startTime.value) / 1000
  }, 100)
  
  // 设置超时警告
  timeoutTimer.value = setTimeout(() => {
    showTimeoutWarning.value = true
  }, (props.timeoutSeconds - 10) * 1000)
  
  // 设置超时处理
  setTimeout(() => {
    if (props.isVisible) {
      emit('timeout')
    }
  }, props.timeoutSeconds * 1000)
}

// 停止加载
const stopLoading = () => {
  if (progressTimer.value) {
    clearInterval(progressTimer.value)
    progressTimer.value = null
  }
  
  if (timeoutTimer.value) {
    clearTimeout(timeoutTimer.value)
    timeoutTimer.value = null
  }
  
  showTimeoutWarning.value = false
}

// 处理取消
const handleCancel = () => {
  if (props.canCancel) {
    emit('cancel')
  }
}

// 处理覆盖层点击
const handleOverlayClick = () => {
  // 防止意外关闭，不做任何操作
}

// 组件挂载和卸载
onMounted(() => {
  if (props.isVisible) {
    startLoading()
  }
})

onUnmounted(() => {
  stopLoading()
})
</script>

<style scoped>
.project-registration-loader {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.loader-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(2px);
}

.loader-container {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
}

.loader-content {
  text-align: center;
}

.spinner-wrapper {
  margin-bottom: 1.5rem;
}

.spinner {
  height: 4rem;
  width: 4rem;
  animation: rotate 2s linear infinite;
}

.spinner-path {
  stroke: #3B82F6;
  stroke-linecap: round;
  animation: dash 1.5s ease-in-out infinite;
}

.status-text {
  margin-bottom: 2rem;
}

.status-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1F2937;
  margin-bottom: 0.5rem;
}

.status-description {
  color: #6B7280;
  font-size: 0.875rem;
  line-height: 1.5;
}

.progress-container {
  margin-bottom: 2rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background-color: #E5E7EB;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #3B82F6, #1D4ED8);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: #6B7280;
  font-weight: 500;
}

.steps-indicator {
  display: flex;
  justify-content: space-between;
  margin-bottom: 2rem;
  gap: 0.5rem;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.step-icon {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  margin-bottom: 0.5rem;
  transition: all 0.3s ease;
}

.step-item.completed .step-icon {
  background-color: #10B981;
  color: white;
}

.step-item.active .step-icon {
  background-color: #3B82F6;
  color: white;
}

.step-item.pending .step-icon {
  background-color: #E5E7EB;
  color: #6B7280;
}

.step-label {
  font-size: 0.75rem;
  color: #6B7280;
  text-align: center;
  line-height: 1.2;
}

.step-item.active .step-label {
  color: #3B82F6;
  font-weight: 500;
}

.action-buttons {
  margin-bottom: 1rem;
}

.cancel-button {
  padding: 0.5rem 1rem;
  background-color: #F3F4F6;
  color: #6B7280;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-button:hover:not(:disabled) {
  background-color: #E5E7EB;
  color: #374151;
}

.cancel-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.timeout-warning {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem;
  background-color: #FEF3C7;
  color: #92400E;
  border-radius: 6px;
  font-size: 0.875rem;
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes dash {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -124;
  }
}
</style>

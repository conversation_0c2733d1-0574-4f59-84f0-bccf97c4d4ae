/* 引入通用组件样式 */
@import './components.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
@layer base {
  html {
    font-family: 'Inter var', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  }

  body {
    @apply bg-gray-50 text-gray-900;
  }

  h1 {
    @apply text-2xl font-bold mb-4;
  }

  h2 {
    @apply text-xl font-semibold mb-3;
  }

  h3 {
    @apply text-lg font-medium mb-2;
  }
}

@layer components {
  /* 基础按钮样式 */
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-all duration-200 ease-in-out;
    @apply focus:outline-none focus:ring-2 focus:ring-offset-2;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none;
    @apply inline-flex items-center justify-center;
    @apply shadow-sm hover:shadow-md;
  }

  /* 主要按钮 - 高对比度设计 */
  .btn-primary {
    @apply bg-primary-600 text-white border border-primary-600;
    @apply hover:bg-primary-700 hover:border-primary-700 hover:shadow-lg;
    @apply focus:ring-primary-500 focus:bg-primary-700;
    @apply active:bg-primary-800 active:border-primary-800;
    @apply shadow-primary-600/20;
  }

  /* 刷新按钮专用样式 - 增强对比度 */
  .btn-refresh {
    @apply bg-blue-600 text-white border border-blue-600;
    @apply hover:bg-blue-700 hover:border-blue-700 hover:shadow-lg;
    @apply focus:ring-blue-500 focus:bg-blue-700 focus:ring-offset-2;
    @apply active:bg-blue-800 active:border-blue-800 active:transform active:scale-95;
    @apply shadow-blue-600/25 hover:shadow-blue-700/30;
    @apply font-semibold;
  }

  /* 次要按钮 */
  .btn-secondary {
    @apply bg-secondary-600 text-white border border-secondary-600;
    @apply hover:bg-secondary-700 hover:border-secondary-700 hover:shadow-lg;
    @apply focus:ring-secondary-500 focus:bg-secondary-700;
    @apply active:bg-secondary-800 active:border-secondary-800;
    @apply shadow-secondary-600/20;
  }

  /* 轮廓按钮 */
  .btn-outline {
    @apply bg-white text-gray-700 border border-gray-300;
    @apply hover:bg-gray-50 hover:border-gray-400 hover:text-gray-900;
    @apply focus:ring-gray-500 focus:bg-gray-50;
    @apply active:bg-gray-100;
  }

  /* 危险按钮 */
  .btn-danger {
    @apply bg-red-600 text-white border border-red-600;
    @apply hover:bg-red-700 hover:border-red-700 hover:shadow-lg;
    @apply focus:ring-red-500 focus:bg-red-700;
    @apply active:bg-red-800 active:border-red-800;
    @apply shadow-red-600/20;
  }

  /* 成功按钮 */
  .btn-success {
    @apply bg-green-600 text-white border border-green-600;
    @apply hover:bg-green-700 hover:border-green-700 hover:shadow-lg;
    @apply focus:ring-green-500 focus:bg-green-700;
    @apply active:bg-green-800 active:border-green-800;
    @apply shadow-green-600/20;
  }

  /* 按钮尺寸变体 */
  .btn-sm {
    @apply px-3 py-1.5 text-sm;
  }

  .btn-lg {
    @apply px-6 py-3 text-lg;
  }

  /* 按钮加载状态 */
  .btn-loading {
    @apply relative overflow-hidden;
  }

  .btn-loading::before {
    content: '';
    @apply absolute inset-0 bg-white/20 animate-pulse;
  }

  /* 动画效果 */
  .animate-button {
    @apply transition-all duration-200 ease-in-out;
    @apply hover:transform hover:-translate-y-0.5;
  }

  .animate-button:active {
    @apply transform translate-y-0 scale-95;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-card p-4 sm:p-6;
  }

  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .select {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }

  /* 深色模式按钮样式 */
  @media (prefers-color-scheme: dark) {
    .btn-primary {
      @apply bg-primary-500 border-primary-500;
      @apply hover:bg-primary-400 hover:border-primary-400;
      @apply focus:ring-primary-400;
      @apply active:bg-primary-600 active:border-primary-600;
    }

    .btn-refresh {
      @apply bg-blue-500 border-blue-500;
      @apply hover:bg-blue-400 hover:border-blue-400;
      @apply focus:ring-blue-400;
      @apply active:bg-blue-600 active:border-blue-600;
    }

    .btn-outline {
      @apply bg-gray-800 text-gray-200 border-gray-600;
      @apply hover:bg-gray-700 hover:border-gray-500 hover:text-white;
      @apply focus:ring-gray-400 focus:bg-gray-700;
    }

    .card {
      @apply bg-gray-800 text-white;
    }
  }

  /* 高对比度模式支持 */
  @media (prefers-contrast: high) {
    .btn-primary, .btn-refresh {
      @apply border-2 font-bold;
    }

    .btn-outline {
      @apply border-2;
    }

    .btn-danger, .btn-success {
      @apply border-2 font-bold;
    }
  }

  /* 减少动画模式支持 */
  @media (prefers-reduced-motion: reduce) {
    .btn, .animate-button {
      @apply transition-none;
    }

    .btn-loading::before {
      @apply animate-none;
    }
  }

  /* 确保按钮文字对比度 */
  .btn-primary, .btn-refresh, .btn-secondary, .btn-danger, .btn-success {
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }

  /* 按钮图标对齐 */
  .btn i {
    @apply inline-block;
    vertical-align: -0.125em;
  }

  /* 加载状态指示器 */
  .btn-loading {
    @apply pointer-events-none;
  }

  .btn-loading .fa-spin {
    @apply text-white/80;
  }
}

@layer utilities {
  /* 自定义动画 */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-10px);
    }
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  @keyframes scaleIn {
    from {
      opacity: 0;
      transform: scale(0.8);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  @keyframes gradient {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
  }

  .animate-slideInLeft {
    animation: slideInLeft 0.6s ease-out;
  }

  .animate-slideInRight {
    animation: slideInRight 0.6s ease-out;
  }

  .animate-scaleIn {
    animation: scaleIn 0.6s ease-out;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  /* 悬停效果 */
  .hover-lift {
    transition: all 0.3s ease;
  }

  .hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  }

  /* 渐变文字 */
  .gradient-text {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* 玻璃态效果 */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* 高级动画效果 */
  @keyframes glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
    }
    50% {
      box-shadow: 0 0 40px rgba(59, 130, 246, 0.6);
    }
  }

  @keyframes matrix {
    0% {
      transform: translateY(-100%);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateY(100vh);
      opacity: 0;
    }
  }

  @keyframes dataFlow {
    0% {
      transform: translateX(-100%);
      opacity: 0;
    }
    50% {
      opacity: 1;
    }
    100% {
      transform: translateX(100%);
      opacity: 0;
    }
  }

  @keyframes hologram {
    0%, 100% {
      opacity: 0.8;
      transform: scale(1);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  /* 技术深度工作原理专用动画 */
  @keyframes techPulse {
    0%, 100% {
      box-shadow: 0 0 20px rgba(139, 92, 246, 0.3);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 0 40px rgba(139, 92, 246, 0.6);
      transform: scale(1.02);
    }
  }

  @keyframes dataStream {
    0% {
      transform: translateX(-100%) scaleX(0);
      opacity: 0;
    }
    50% {
      opacity: 1;
      transform: translateX(0) scaleX(1);
    }
    100% {
      transform: translateX(100%) scaleX(0);
      opacity: 0;
    }
  }

  @keyframes codeFlow {
    0% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
    100% {
      background-position: 0% 50%;
    }
  }

  @keyframes architectureRotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }

  @keyframes neuralNetwork {
    0%, 100% {
      opacity: 0.3;
      transform: scale(0.95);
    }
    50% {
      opacity: 1;
      transform: scale(1.05);
    }
  }

  /* 技术指标动画 */
  .tech-pulse {
    animation: techPulse 3s ease-in-out infinite;
  }

  .data-stream {
    animation: dataStream 4s ease-in-out infinite;
  }

  .code-flow {
    background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
    background-size: 200% 200%;
    animation: codeFlow 6s ease infinite;
  }

  .architecture-rotate {
    animation: architectureRotate 20s linear infinite;
  }

  .neural-network {
    animation: neuralNetwork 2s ease-in-out infinite;
  }

  @keyframes neonPulse {
    0%, 100% {
      text-shadow: 0 0 5px currentColor, 0 0 10px currentColor, 0 0 15px currentColor;
    }
    50% {
      text-shadow: 0 0 10px currentColor, 0 0 20px currentColor, 0 0 30px currentColor;
    }
  }

  .animate-glow {
    animation: glow 2s ease-in-out infinite;
  }

  .animate-matrix {
    animation: matrix 3s linear infinite;
  }

  .animate-dataFlow {
    animation: dataFlow 2s ease-in-out infinite;
  }

  .animate-hologram {
    animation: hologram 3s ease-in-out infinite;
  }

  .animate-neonPulse {
    animation: neonPulse 2s ease-in-out infinite;
  }

  /* 科技感边框 */
  .tech-border {
    position: relative;
    border: 1px solid transparent;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
    background-clip: padding-box;
  }

  .tech-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
    margin: -1px;
    border-radius: inherit;
    background: linear-gradient(45deg, #3b82f6, #9333ea, #06b6d4);
    background-size: 200% 200%;
    animation: gradient 3s ease infinite;
  }

  /* 数据流效果 */
  .data-stream {
    position: relative;
    overflow: hidden;
  }

  .data-stream::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.4), transparent);
    animation: dataFlow 2s ease-in-out infinite;
  }
}
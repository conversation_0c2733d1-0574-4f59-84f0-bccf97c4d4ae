/* ArchScope 通用组件样式 */

/* 通用卡片样式 - 增强版 */
.content-card {
  @apply bg-white rounded-xl overflow-hidden;
  @apply bg-gradient-to-br from-white to-neutral-50;
  @apply shadow-lg border border-neutral-200/80;
  @apply transition-all duration-300 ease-out;
}

.content-card:hover {
  @apply -translate-y-0.5;
  @apply shadow-xl border-primary-200;
  @apply shadow-primary-500/10;
}

/* 通用表格样式 - 增强版 */
.table-header {
  @apply bg-gradient-to-r from-neutral-50 to-neutral-100;
  @apply font-semibold text-xs text-neutral-600 uppercase tracking-wider;
  @apply border-b-2 border-primary-100;
}

.table-row {
  @apply transition-all duration-300;
  @apply border-b border-neutral-200/60;
}

.table-row:hover {
  @apply bg-gradient-to-r from-neutral-50 to-neutral-100;
  @apply translate-x-0.5 shadow-sm shadow-primary-500/10;
}

/* 星级评分样式 */
.star-rating {
  @apply inline-flex items-center;
}

.star-filled {
  @apply text-warning-400;
}

.star-empty {
  @apply text-neutral-200;
}

/* 表单元素样式 */
.form-input {
  @apply block w-full px-3 py-2 border border-neutral-300 rounded-md;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  @apply bg-white text-neutral-900;
  @apply transition-all duration-200 ease-in-out;
  height: calc(1.5em + 0.75rem + 2px);
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  background-clip: padding-box;
}

.form-input:focus {
  @apply border-primary-600 ring-primary-500/25;
}

.form-select {
  @apply block w-full px-3 py-2 border border-neutral-300 rounded-md;
  @apply focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500;
  @apply bg-white text-neutral-900;
  @apply transition-all duration-200 ease-in-out;
  padding-right: 2.25rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  background-clip: padding-box;
}

.form-select:focus {
  @apply border-primary-600 ring-primary-500/25;
}

/* 带图标的输入框 */
.input-with-icon {
  @apply relative;
}

.input-with-icon .form-input {
  padding-left: 2.5rem;
}

.input-with-icon .input-icon {
  @apply absolute left-3 top-1/2 transform -translate-y-1/2;
  @apply text-gray-400 pointer-events-none z-10;
}

/* 按钮样式 - 增强版 */
.btn-primary {
  @apply text-white font-semibold px-6 py-3 rounded-xl;
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  border: 1px solid rgba(99, 102, 241, 0.3);
  box-shadow: 0 4px 6px -1px rgba(99, 102, 241, 0.3),
              0 2px 4px -1px rgba(99, 102, 241, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  @apply focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5b21b6 0%, #7c3aed 100%);
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(99, 102, 241, 0.4),
              0 4px 6px -2px rgba(99, 102, 241, 0.3);
}

.btn-secondary {
  @apply bg-gray-600 hover:bg-gray-700 text-white font-medium;
  @apply px-4 py-2 rounded-md transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2;
}

.btn-success {
  @apply bg-green-600 hover:bg-green-700 text-white font-medium;
  @apply px-4 py-2 rounded-md transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2;
}

.btn-danger {
  @apply bg-red-600 hover:bg-red-700 text-white font-medium;
  @apply px-4 py-2 rounded-md transition-all duration-200;
  @apply focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2;
}

/* 通用动画 */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out;
}

.animate-slide-down {
  animation: slideDown 0.3s ease-out;
}

.animate-scale {
  @apply transition-transform duration-300 ease-in-out;
}

.animate-scale:hover {
  transform: scale(1.02);
}

.animate-button {
  @apply transition-all duration-300 ease-in-out relative overflow-hidden;
}

.animate-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

/* 状态样式 - 增强版 */
.status-pending {
  @apply px-3 py-1.5 rounded-full text-xs font-semibold;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #92400e;
  border: 1px solid rgba(251, 191, 36, 0.3);
  box-shadow: 0 2px 4px rgba(251, 191, 36, 0.2);
}

.status-processing {
  @apply px-3 py-1.5 rounded-full text-xs font-semibold;
  background: linear-gradient(135deg, #dbeafe 0%, #93c5fd 100%);
  color: #1e40af;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.2);
  animation: pulse 2s infinite;
}

.status-completed {
  @apply px-3 py-1.5 rounded-full text-xs font-semibold;
  background: linear-gradient(135deg, #d1fae5 0%, #86efac 100%);
  color: #166534;
  border: 1px solid rgba(34, 197, 94, 0.3);
  box-shadow: 0 2px 4px rgba(34, 197, 94, 0.2);
}

.status-failed {
  @apply px-3 py-1.5 rounded-full text-xs font-semibold;
  background: linear-gradient(135deg, #fee2e2 0%, #fca5a5 100%);
  color: #991b1b;
  border: 1px solid rgba(239, 68, 68, 0.3);
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.2);
}

.status-cancelled {
  @apply px-3 py-1.5 rounded-full text-xs font-semibold;
  background: linear-gradient(135deg, #f1f5f9 0%, #cbd5e1 100%);
  color: #475569;
  border: 1px solid rgba(148, 163, 184, 0.3);
  box-shadow: 0 2px 4px rgba(148, 163, 184, 0.2);
}

/* 状态徽章通用样式 */
.status-badge {
  @apply inline-flex items-center px-3 py-1.5 rounded-full text-xs font-semibold;
  transition: all 0.3s ease;
  border: 1px solid transparent;
}

.status-badge:hover {
  transform: scale(1.05);
}

/* 实时状态指示器 */
.status-indicator {
  @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-medium;
}

.status-indicator.live {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  animation: pulse 2s infinite;
}

.status-indicator.waiting {
  background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
  color: #6b7280;
}

/* 进度条动画 */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full;
  animation: progress-flow 2s ease-in-out infinite;
}

@keyframes progress-flow {
  0% { transform: translateX(-100%); }
  50% { transform: translateX(0%); }
  100% { transform: translateX(100%); }
}

/* 工具类 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 动画关键帧 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    transform: translateY(-10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 响应式工具类 */
@media (max-width: 640px) {
  .mobile-stack {
    @apply flex-col space-y-2;
  }

  .mobile-full {
    @apply w-full;
  }

  /* 移动端表格优化 */
  .table-container {
    @apply overflow-x-auto;
  }

  /* 移动端卡片优化 */
  .content-card {
    @apply mx-2;
  }

  /* 移动端按钮优化 */
  .btn {
    @apply w-full justify-center;
  }
}

@media (max-width: 768px) {
  .tablet-stack {
    @apply flex-col space-y-3;
  }

  .tablet-full {
    @apply w-full;
  }

  /* 平板端搜索框优化 */
  .search-input {
    @apply w-full;
  }

  /* 平板端统计卡片优化 */
  .stats-grid {
    @apply grid-cols-2;
  }
}

/* 大屏幕优化 */
@media (min-width: 1280px) {
  .content-card {
    @apply shadow-lg;
  }

  .table-row:hover {
    @apply transform translate-x-1;
  }
}

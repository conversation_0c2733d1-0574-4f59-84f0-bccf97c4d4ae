import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router'
// 由于使用统一的权限控制，不再需要导入 authStore
// import { useAuthStore } from '@/stores/auth'

// 使用懒加载导入页面组件以提升性能
// 首页保持静态导入，因为它是最常访问的页面
import HomeView from '@/views/HomeView.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    name: 'Home',
    component: HomeView
  },
  // 使用懒加载提升性能，减少初始包大小
  {
    path: '/projects',
    name: 'ProjectList',
    component: () => import('@/views/projects/ProjectList.vue')
  },
  {
    path: '/projects/new',
    name: 'RegisterProject',
    component: () => import('@/views/projects/RegisterProject.vue')
  },
  {
    path: '/projects/:id',
    name: 'ProjectDetail',
    component: () => import('@/views/projects/ProjectDetail.vue')
  },
  {
    path: '/projects/:id/documents',
    name: 'DocumentView',
    component: () => import('@/views/projects/DocumentView.vue')
  },

  {
    path: '/tasks',
    name: 'TaskList',
    component: () => import('@/views/tasks/TaskListView.vue')
  },
  {
    path: '/tasks/:id',
    name: 'TaskDetail',
    component: () => import('@/views/tasks/TaskDetailView.vue')
  },
  {
    path: '/test/loader',
    name: 'LoaderTest',
    component: () => import('@/views/test/LoaderTest.vue')
  },
  {
    path: '/test/cards',
    name: 'ProjectCardTest',
    component: () => import('@/views/test/ProjectCardTest.vue')
  },
  {
    path: '/test/error-handling',
    name: 'ErrorHandlingTest',
    component: () => import('@/views/test/ErrorHandlingTest.vue')
  },
  {
    path: '/test/mermaid',
    name: 'MermaidTest',
    component: () => import('@/views/test/MermaidTest.vue')
  },
  {
    path: '/test/responsive',
    name: 'ResponsiveTest',
    component: () => import('@/views/test/ResponsiveTest.vue')
  },
  {
    path: '/test/document-style',
    name: 'DocumentStyleTest',
    component: () => import('@/views/test/DocumentStyleTest.vue')
  },
  {
    path: '/test/task-data',
    name: 'TaskDataTest',
    component: () => import('@/views/test/TaskDataTest.vue')
  },
  {
    path: '/:catchAll(.*)',
    name: 'NotFound',
    component: () => import('@/views/NotFound.vue')
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 全局导航守卫
router.beforeEach((to, from, next) => {
  // 由于使用统一的权限控制，不再需要检查认证状态
  // 所有路由都允许直接访问
  next()
})

export default router
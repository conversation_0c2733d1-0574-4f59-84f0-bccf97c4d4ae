/**
 * MainLayout 导航高亮测试
 * 验证导航栏在不同路由下的高亮状态
 */

import { describe, it, expect } from 'vitest';
import { mount } from '@vue/test-utils';
import { createRouter, createWebHistory } from 'vue-router';
import MainLayout from './MainLayout.vue';

// 创建测试路由
const routes = [
  { path: '/projects', name: 'ProjectList' },
  { path: '/projects/new', name: 'RegisterProject' },
  { path: '/projects/:id', name: 'ProjectDetail' },
  { path: '/tasks', name: 'TaskQueue' }
];

const router = createRouter({
  history: createWebHistory(),
  routes
});

describe('MainLayout - 导航高亮', () => {
  it('在项目列表页面应该高亮"项目概览"', async () => {
    await router.push('/projects');
    
    const wrapper = mount(MainLayout, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();

    const projectListLink = wrapper.find('router-link[to="/projects"]');
    const registerProjectLink = wrapper.find('router-link[to="/projects/new"]');
    
    // 项目概览应该高亮
    expect(projectListLink.classes()).toContain('bg-indigo-600');
    expect(projectListLink.classes()).toContain('text-white');
    
    // 注册项目不应该高亮
    expect(registerProjectLink.classes()).not.toContain('bg-indigo-600');
  });

  it('在注册项目页面应该只高亮"注册项目"', async () => {
    await router.push('/projects/new');
    
    const wrapper = mount(MainLayout, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();

    const projectListLink = wrapper.find('router-link[to="/projects"]');
    const registerProjectLink = wrapper.find('router-link[to="/projects/new"]');
    
    // 项目概览不应该高亮
    expect(projectListLink.classes()).not.toContain('bg-indigo-600');
    
    // 注册项目应该高亮
    expect(registerProjectLink.classes()).toContain('bg-indigo-600');
    expect(registerProjectLink.classes()).toContain('text-white');
  });

  it('在项目详情页面应该高亮"项目概览"', async () => {
    await router.push('/projects/123');
    
    const wrapper = mount(MainLayout, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();

    const projectListLink = wrapper.find('router-link[to="/projects"]');
    const registerProjectLink = wrapper.find('router-link[to="/projects/new"]');
    
    // 项目概览应该高亮（因为是项目相关页面）
    expect(projectListLink.classes()).toContain('bg-indigo-600');
    expect(projectListLink.classes()).toContain('text-white');
    
    // 注册项目不应该高亮
    expect(registerProjectLink.classes()).not.toContain('bg-indigo-600');
  });

  it('在任务队列页面应该高亮"任务监控"', async () => {
    await router.push('/tasks');
    
    const wrapper = mount(MainLayout, {
      global: {
        plugins: [router]
      }
    });

    await wrapper.vm.$nextTick();

    const projectListLink = wrapper.find('router-link[to="/projects"]');
    const registerProjectLink = wrapper.find('router-link[to="/projects/new"]');
    const taskQueueLink = wrapper.find('router-link[to="/tasks"]');
    
    // 项目相关链接都不应该高亮
    expect(projectListLink.classes()).not.toContain('bg-indigo-600');
    expect(registerProjectLink.classes()).not.toContain('bg-indigo-600');
    
    // 任务监控应该高亮
    expect(taskQueueLink.classes()).toContain('bg-indigo-600');
    expect(taskQueueLink.classes()).toContain('text-white');
  });

  it('导航链接应该有正确的文本内容', () => {
    const wrapper = mount(MainLayout, {
      global: {
        plugins: [router]
      }
    });

    const projectListLink = wrapper.find('router-link[to="/projects"]');
    const registerProjectLink = wrapper.find('router-link[to="/projects/new"]');
    const taskQueueLink = wrapper.find('router-link[to="/tasks"]');

    expect(projectListLink.text()).toBe('项目概览');
    expect(registerProjectLink.text()).toBe('注册项目');
    expect(taskQueueLink.text()).toBe('任务监控');
  });

  it('所有导航链接应该有正确的样式类', () => {
    const wrapper = mount(MainLayout, {
      global: {
        plugins: [router]
      }
    });

    const navLinks = wrapper.findAll('router-link');
    
    // 排除logo链接，只检查导航链接
    const navigationLinks = navLinks.filter(link => 
      link.attributes('to') !== '/projects' || 
      !link.find('img').exists()
    );

    navigationLinks.forEach(link => {
      const classes = link.classes();
      expect(classes).toContain('text-gray-300');
      expect(classes).toContain('hover:text-white');
      expect(classes).toContain('px-3');
      expect(classes).toContain('py-2');
      expect(classes).toContain('rounded-md');
      expect(classes).toContain('text-sm');
      expect(classes).toContain('font-medium');
      expect(classes).toContain('transition-colors');
    });
  });
});

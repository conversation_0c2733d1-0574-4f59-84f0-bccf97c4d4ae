<template>
  <div class="min-h-screen bg-gray-100 font-sans">
    <!-- 顶部导航栏 - 按照界面原型设计 -->
    <nav class="bg-gray-800 p-4">
      <div class="container mx-auto flex justify-between items-center">
        <router-link to="/" class="text-white flex items-center">
          <div
            class="bg-gray-800 rounded-full p-1 flex items-center justify-center mr-2"
          >
            <img src="@/assets/logo.png" alt="ArchScope" class="h-8 w-8" />
          </div>
          <span class="text-2xl font-bold">ArchScope</span>
        </router-link>
        <!-- 桌面端导航 -->
        <div class="hidden md:flex space-x-4">
          <router-link
            to="/projects/new"
            class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{
              'bg-indigo-600 text-white': $route.path === '/projects/new',
            }"
          >
            注册项目
          </router-link>
          <router-link
            to="/projects"
            class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{
              'bg-indigo-600 text-white': $route.path.startsWith('/projects') && $route.path !== '/projects/new',
            }"
          >
            项目概览
          </router-link>
          <router-link
            to="/tasks"
            class="text-gray-300 hover:text-white px-3 py-2 rounded-md text-sm font-medium transition-colors"
            :class="{
              'bg-indigo-600 text-white': $route.path.startsWith('/tasks'),
            }"
          >
            任务监控
          </router-link>
        </div>

        <!-- 移动端菜单按钮 -->
        <div class="md:hidden">
          <button
            @click="isMobileMenuOpen = !isMobileMenuOpen"
            class="inline-flex items-center justify-center p-2 rounded-md text-gray-300 hover:text-white hover:bg-gray-700 transition-colors duration-200"
          >
            <svg
              class="h-6 w-6"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                v-if="!isMobileMenuOpen"
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"
              />
              <path
                v-else
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>
      </div>
    </nav>

    <!-- 移动端导航菜单 -->
    <div
      v-show="isMobileMenuOpen"
      class="md:hidden bg-gray-800 border-t border-gray-700"
    >
      <div class="px-2 pt-2 pb-3 space-y-1">
        <router-link
          to="/projects"
          @click="isMobileMenuOpen = false"
          class="block px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-gray-700 transition-all duration-200"
          :class="{
            'bg-indigo-600 text-white': $route.path.startsWith('/projects') && $route.path !== '/projects/new',
          }"
        >
          项目概览
        </router-link>
        <router-link
          to="/tasks"
          @click="isMobileMenuOpen = false"
          class="block px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-gray-700 transition-all duration-200"
          :class="{
            'bg-indigo-600 text-white': $route.path.startsWith('/tasks'),
          }"
        >
          任务监控
        </router-link>
        <router-link
          to="/projects/new"
          @click="isMobileMenuOpen = false"
          class="block px-4 py-3 rounded-lg text-gray-300 hover:text-white hover:bg-gray-700 transition-all duration-200"
          :class="{
            'bg-indigo-600 text-white': $route.path === '/projects/new',
          }"
        >
          注册项目
        </router-link>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="min-h-screen bg-gray-100">
      <slot></slot>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// 移动端菜单状态
const isMobileMenuOpen = ref(false)
</script>

name: E2E Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'arch-scope-frontend/**'
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'arch-scope-frontend/**'

jobs:
  e2e-tests:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
        cache-dependency-path: arch-scope-frontend/package-lock.json
        
    - name: Install dependencies
      working-directory: arch-scope-frontend
      run: npm ci
      
    - name: Build application
      working-directory: arch-scope-frontend
      run: npm run build
      
    - name: Install Cypress dependencies
      working-directory: arch-scope-frontend
      run: npx cypress install
      
    - name: Run E2E tests
      working-directory: arch-scope-frontend
      run: |
        npm run dev &
        npx wait-on http://localhost:3000
        npm run test:e2e
      env:
        CYPRESS_baseUrl: http://localhost:3000
        
    - name: Upload Cypress screenshots
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: cypress-screenshots-${{ matrix.node-version }}
        path: arch-scope-frontend/cypress/screenshots
        
    - name: Upload Cypress videos
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: cypress-videos-${{ matrix.node-version }}
        path: arch-scope-frontend/cypress/videos

  e2e-tests-with-backend:
    runs-on: ubuntu-latest
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: root
          MYSQL_DATABASE: archscope_test
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20.x'
        cache: 'npm'
        cache-dependency-path: arch-scope-frontend/package-lock.json
        
    - name: Setup Java
      uses: actions/setup-java@v4
      with:
        distribution: 'temurin'
        java-version: '17'
        
    - name: Install frontend dependencies
      working-directory: arch-scope-frontend
      run: npm ci
      
    - name: Build frontend
      working-directory: arch-scope-frontend
      run: npm run build
      
    - name: Start backend services
      run: |
        # 启动后端服务器
        cd arch-scope-main
        mvn spring-boot:run -Dspring-boot.run.profiles=test &
        BACKEND_PID=$!
        echo "BACKEND_PID=$BACKEND_PID" >> $GITHUB_ENV
        
        # 等待后端启动
        timeout 60 bash -c 'until curl -f http://localhost:8080/actuator/health; do sleep 2; done'
        
    - name: Run E2E tests with real backend
      working-directory: arch-scope-frontend
      run: |
        npm run dev &
        npx wait-on http://localhost:3000
        npx wait-on http://localhost:8080
        npm run test:e2e
      env:
        CYPRESS_baseUrl: http://localhost:3000
        CYPRESS_apiUrl: http://localhost:8080
        
    - name: Cleanup
      if: always()
      run: |
        if [ ! -z "$BACKEND_PID" ]; then
          kill $BACKEND_PID || true
        fi
        
    - name: Upload test artifacts
      uses: actions/upload-artifact@v4
      if: failure()
      with:
        name: e2e-test-artifacts-with-backend
        path: |
          arch-scope-frontend/cypress/screenshots
          arch-scope-frontend/cypress/videos

/// <reference types="cypress" />

// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

declare global {
  namespace Cypress {
    interface Chainable {
      /**
       * 访问任务列表页面
       */
      visitTaskList(): Chainable<void>
      
      /**
       * 访问任务详情页面
       * @param taskId 任务ID
       */
      visitTaskDetail(taskId: string): Chainable<void>
      
      /**
       * 搜索任务
       * @param searchTerm 搜索词
       */
      searchTasks(searchTerm: string): Chainable<void>
      
      /**
       * 筛选任务状态
       * @param status 任务状态
       */
      filterTasksByStatus(status: string): Chainable<void>
      
      /**
       * 等待页面加载完成
       */
      waitForPageLoad(): Chainable<void>
      
      /**
       * 检查任务表格是否存在
       */
      checkTaskTableExists(): Chainable<void>
      
      /**
       * 获取任务行数量
       */
      getTaskRowCount(): Chainable<number>
    }
  }
}

// 访问任务列表页面
Cypress.Commands.add('visitTaskList', () => {
  cy.visit('/tasks')
  cy.waitForPageLoad()
})

// 访问任务详情页面
Cypress.Commands.add('visitTaskDetail', (taskId: string) => {
  cy.visit(`/tasks/${taskId}`)
  cy.waitForPageLoad()
})

// 搜索任务
Cypress.Commands.add('searchTasks', (searchTerm: string) => {
  cy.get('[data-cy="search-input"]').clear().type(searchTerm)
  cy.wait(500) // 等待搜索结果更新
})

// 筛选任务状态
Cypress.Commands.add('filterTasksByStatus', (status: string) => {
  cy.get('[data-cy="status-filter"]').select(status)
  cy.wait(500) // 等待筛选结果更新
})

// 等待页面加载完成
Cypress.Commands.add('waitForPageLoad', () => {
  cy.get('body').should('be.visible')
  cy.get('[data-cy="loading"]').should('not.exist')
})

// 检查任务表格是否存在
Cypress.Commands.add('checkTaskTableExists', () => {
  cy.get('[data-cy="task-table"]').should('be.visible')
  cy.get('[data-cy="task-table"] thead').should('be.visible')
  cy.get('[data-cy="task-table"] tbody').should('be.visible')
})

// 获取任务行数量
Cypress.Commands.add('getTaskRowCount', () => {
  return cy.get('[data-cy="task-row"]').its('length')
})

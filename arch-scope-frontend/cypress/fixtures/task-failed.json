{"id": "task-12345", "name": "文档更新 (UPDATE)", "project": "Awesome Components", "type": "doc_generation", "status": "failed", "startTime": "2023-10-26 10:05", "endTime": "2023-10-26 10:08", "projectColor": "bg-indigo-500", "progress": 30, "logs": [{"timestamp": "2023-10-26 10:05:00", "level": "INFO", "message": "任务开始执行"}, {"timestamp": "2023-10-26 10:05:30", "level": "INFO", "message": "正在分析项目结构..."}, {"timestamp": "2023-10-26 10:06:00", "level": "ERROR", "message": "无法访问项目仓库"}, {"timestamp": "2023-10-26 10:08:00", "level": "ERROR", "message": "任务执行失败"}], "output": null, "error": {"code": "REPO_ACCESS_ERROR", "message": "无法访问项目仓库，请检查权限配置", "details": "Git clone failed: Permission denied (publickey)"}}
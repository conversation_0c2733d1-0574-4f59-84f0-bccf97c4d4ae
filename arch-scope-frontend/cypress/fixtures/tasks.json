{"mockTasks": [{"id": "task-12345", "name": "文档更新 (UPDATE)", "project": "Awesome Components", "type": "doc_generation", "status": "processing", "startTime": "2023-10-26 10:05", "endTime": null, "projectColor": "bg-indigo-500"}, {"id": "task-12344", "name": "文档更新 (UPDATE)", "project": "Payment Gateway", "type": "doc_generation", "status": "success", "startTime": "2023-10-25 15:30", "endTime": "2023-10-25 15:45", "projectColor": "bg-blue-500"}, {"id": "task-12346", "name": "项目初始化 (INIT)", "project": "New Project X", "type": "project_init", "status": "waiting", "startTime": "2023-10-26 10:10", "endTime": null, "projectColor": "bg-green-500"}, {"id": "task-12347", "name": "代码分析 (ANALYSIS)", "project": "Legacy System", "type": "code_analysis", "status": "failed", "startTime": "2023-10-26 09:30", "endTime": "2023-10-26 09:45", "projectColor": "bg-red-500"}], "searchTestCases": [{"searchTerm": "Payment", "expectedResults": 1, "description": "搜索项目名称"}, {"searchTerm": "文档", "expectedResults": 2, "description": "搜索任务名称"}, {"searchTerm": "nonexistent", "expectedResults": 0, "description": "搜索不存在的内容"}], "filterTestCases": [{"status": "processing", "expectedResults": 1, "description": "筛选处理中的任务"}, {"status": "success", "expectedResults": 1, "description": "筛选成功的任务"}, {"status": "waiting", "expectedResults": 1, "description": "筛选等待中的任务"}, {"status": "failed", "expectedResults": 1, "description": "筛选失败的任务"}, {"status": "all", "expectedResults": 4, "description": "显示所有任务"}]}
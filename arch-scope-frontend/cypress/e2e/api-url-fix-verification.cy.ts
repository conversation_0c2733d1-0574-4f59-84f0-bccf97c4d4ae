describe('API URL修复验证', () => {
  beforeEach(() => {
    // 访问项目注册页面
    cy.visit('/projects/register')
    
    // 等待页面加载完成
    cy.get('h1').should('contain', '注册新项目')
  })

  it('应该使用正确的API路径，不出现重复的/api', () => {
    const testUrl = 'https://github.com/test/project.git'
    
    // 拦截API请求并验证URL路径
    cy.intercept('GET', '/api/projects/check-repository*', (req) => {
      // 验证请求URL不包含重复的/api
      expect(req.url).to.not.include('/api/api/')
      expect(req.url).to.include('/api/projects/check-repository')
      
      req.reply({
        statusCode: 200,
        body: {
          exists: false,
          message: '仓库可以使用'
        }
      })
    }).as('checkRepository')
    
    // 拦截其他相关API
    cy.intercept('GET', '/git-repository/details*', {
      statusCode: 200,
      body: {
        success: true,
        repositoryName: 'project',
        ownerName: 'test',
        branches: ['main'],
        defaultBranch: 'main'
      }
    }).as('getRepoDetails')
    
    cy.intercept('POST', '/api/projects', (req) => {
      // 验证创建项目的API路径也是正确的
      expect(req.url).to.not.include('/api/api/')
      expect(req.url).to.include('/api/projects')
      expect(req.url).to.not.include('/api/projects/check-repository')
      
      req.reply({
        statusCode: 200,
        body: {
          id: 1,
          name: '测试项目',
          repositoryUrl: testUrl,
          branch: 'main'
        }
      })
    }).as('createProject')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(testUrl)
    cy.get('input[id="name"]').type('测试项目')
    cy.get('textarea[id="description"]').type('这是一个测试项目')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证API调用
    cy.wait('@checkRepository').then((interception) => {
      // 确认请求URL格式正确
      expect(interception.request.url).to.match(/\/api\/projects\/check-repository\?url=/)
      expect(interception.request.url).to.not.include('/api/api/')
    })
    
    cy.wait('@getRepoDetails')
    cy.wait('@createProject').then((interception) => {
      // 确认创建项目的API路径正确
      expect(interception.request.url).to.equal('http://localhost:3000/api/projects')
    })
    
    // 验证成功跳转
    cy.url().should('include', '/projects')
  })

  it('应该正确处理API错误响应', () => {
    const testUrl = 'https://github.com/test/error-project.git'
    
    // 模拟API错误
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 500,
      body: { message: '服务器内部错误' }
    }).as('checkRepositoryError')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(testUrl)
    cy.get('input[id="name"]').type('测试项目')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证API调用
    cy.wait('@checkRepositoryError').then((interception) => {
      // 确认请求URL格式正确，即使在错误情况下
      expect(interception.request.url).to.include('/api/projects/check-repository')
      expect(interception.request.url).to.not.include('/api/api/')
    })
    
    // 验证错误处理（应该继续流程而不是阻止）
    cy.get('.project-registration-loader').should('be.visible')
  })

  it('应该正确编码URL参数', () => {
    const complexUrl = 'https://gitlab.yeepay.com/yop/yop-center'
    
    cy.intercept('GET', '/api/projects/check-repository*', (req) => {
      // 验证URL参数正确编码
      expect(req.url).to.include('url=https%3A%2F%2Fgitlab.yeepay.com%2Fyop%2Fyop-center')
      expect(req.url).to.not.include('/api/api/')
      
      req.reply({
        statusCode: 200,
        body: {
          exists: false,
          message: '仓库可以使用'
        }
      })
    }).as('checkRepositoryWithComplexUrl')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(complexUrl)
    cy.get('input[id="name"]').type('测试项目')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证API调用
    cy.wait('@checkRepositoryWithComplexUrl')
  })
})

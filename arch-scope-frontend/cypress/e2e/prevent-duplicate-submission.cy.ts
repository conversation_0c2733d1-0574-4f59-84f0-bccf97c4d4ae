describe('防重复提交测试', () => {
  beforeEach(() => {
    // 访问项目注册页面
    cy.visit('/projects/register')
    
    // 等待页面加载完成
    cy.get('h1').should('contain', '注册新项目')
  })

  it('应该防止用户快速连续点击提交按钮', () => {
    const testUrl = 'https://github.com/test/duplicate-test.git'
    let apiCallCount = 0
    
    // 拦截项目唯一性检查API
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        message: '仓库可以使用'
      }
    }).as('checkRepository')
    
    // 拦截仓库详情API
    cy.intercept('GET', '/git-repository/details*', {
      statusCode: 200,
      body: {
        success: true,
        projectName: 'duplicate-test',
        description: '防重复提交测试项目',
        repositoryName: 'duplicate-test',
        ownerName: 'test',
        branches: ['main'],
        defaultBranch: 'main',
        repositoryType: 'GitHub'
      }
    }).as('getRepoDetails')
    
    // 拦截项目创建API并计数
    cy.intercept('POST', '/api/projects', (req) => {
      apiCallCount++
      console.log(`项目创建API调用次数: ${apiCallCount}`)
      
      // 模拟较慢的响应，给用户时间进行重复点击
      req.reply((res) => {
        setTimeout(() => {
          res.send({
            statusCode: 200,
            body: {
              id: 1,
              name: 'duplicate-test',
              repositoryUrl: testUrl,
              branch: 'main'
            }
          })
        }, 2000) // 2秒延迟
      })
    }).as('createProject')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(testUrl)
    
    // 等待仓库验证完成
    cy.wait('@checkRepository')
    cy.wait('@getRepoDetails')
    
    // 等待详细字段显示
    cy.get('input[id="name"]').should('be.visible')
    cy.get('input[id="name"]').should('have.value', 'duplicate-test')
    
    // 确保提交按钮可用
    cy.get('button[type="submit"]').should('not.be.disabled')
    
    // 快速连续点击提交按钮多次
    cy.get('button[type="submit"]').click()
    cy.get('button[type="submit"]').click()
    cy.get('button[type="submit"]').click()
    
    // 验证按钮立即变为禁用状态
    cy.get('button[type="submit"]').should('be.disabled')
    
    // 验证显示"注册中..."状态
    cy.get('button[type="submit"]').should('contain', '注册中...')
    
    // 等待API调用完成
    cy.wait('@createProject', { timeout: 5000 })
    
    // 验证只调用了一次API
    cy.then(() => {
      expect(apiCallCount).to.equal(1)
    })
    
    // 验证成功跳转
    cy.url().should('include', '/projects')
  })

  it('应该在注册过程中禁用表单输入', () => {
    const testUrl = 'https://github.com/test/form-disable-test.git'
    
    // 拦截API并添加延迟
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        message: '仓库可以使用'
      }
    }).as('checkRepository')
    
    cy.intercept('GET', '/git-repository/details*', {
      statusCode: 200,
      body: {
        success: true,
        projectName: 'form-disable-test',
        description: '表单禁用测试项目',
        repositoryName: 'form-disable-test',
        ownerName: 'test',
        branches: ['main'],
        defaultBranch: 'main',
        repositoryType: 'GitHub'
      }
    }).as('getRepoDetails')
    
    cy.intercept('POST', '/api/projects', (req) => {
      req.reply((res) => {
        setTimeout(() => {
          res.send({
            statusCode: 200,
            body: {
              id: 1,
              name: 'form-disable-test',
              repositoryUrl: testUrl,
              branch: 'main'
            }
          })
        }, 3000) // 3秒延迟
      })
    }).as('createProject')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(testUrl)
    cy.wait('@checkRepository')
    cy.wait('@getRepoDetails')
    
    // 等待详细字段显示
    cy.get('input[id="name"]').should('be.visible')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证表单字段在注册过程中的状态
    cy.get('button[type="submit"]').should('be.disabled')
    cy.get('button[type="submit"]').should('contain', '注册中...')
    
    // 验证取消链接也被禁用
    cy.get('a[href="/projects"]').should('have.class', 'pointer-events-none')
    cy.get('a[href="/projects"]').should('have.class', 'opacity-50')
    
    // 等待注册完成
    cy.wait('@createProject', { timeout: 5000 })
    
    // 验证跳转
    cy.url().should('include', '/projects')
  })

  it('应该在网络错误时允许重新提交', () => {
    const testUrl = 'https://github.com/test/retry-test.git'
    let attemptCount = 0
    
    // 拦截项目唯一性检查API
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        message: '仓库可以使用'
      }
    }).as('checkRepository')
    
    // 拦截仓库详情API
    cy.intercept('GET', '/git-repository/details*', {
      statusCode: 200,
      body: {
        success: true,
        projectName: 'retry-test',
        description: '重试测试项目',
        repositoryName: 'retry-test',
        ownerName: 'test',
        branches: ['main'],
        defaultBranch: 'main',
        repositoryType: 'GitHub'
      }
    }).as('getRepoDetails')
    
    // 第一次调用失败，第二次成功
    cy.intercept('POST', '/api/projects', (req) => {
      attemptCount++
      if (attemptCount === 1) {
        req.reply({
          statusCode: 500,
          body: { message: '服务器内部错误' }
        })
      } else {
        req.reply({
          statusCode: 200,
          body: {
            id: 1,
            name: 'retry-test',
            repositoryUrl: testUrl,
            branch: 'main'
          }
        })
      }
    }).as('createProject')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(testUrl)
    cy.wait('@checkRepository')
    cy.wait('@getRepoDetails')
    
    // 等待详细字段显示
    cy.get('input[id="name"]').should('be.visible')
    
    // 第一次提交（失败）
    cy.get('button[type="submit"]').click()
    cy.wait('@createProject')
    
    // 验证错误提示显示
    cy.get('.bg-red-50').should('be.visible')
    cy.contains('服务器内部错误').should('be.visible')
    
    // 验证按钮重新可用
    cy.get('button[type="submit"]').should('not.be.disabled')
    cy.get('button[type="submit"]').should('contain', '注册项目')
    
    // 第二次提交（成功）
    cy.get('button[type="submit"]').click()
    cy.wait('@createProject')
    
    // 验证成功跳转
    cy.url().should('include', '/projects')
    
    // 验证总共调用了2次API
    cy.then(() => {
      expect(attemptCount).to.equal(2)
    })
  })

  it('应该在取消注册后重置状态', () => {
    const testUrl = 'https://github.com/test/cancel-test.git'
    
    // 拦截API
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        message: '仓库可以使用'
      }
    }).as('checkRepository')
    
    cy.intercept('GET', '/git-repository/details*', {
      statusCode: 200,
      body: {
        success: true,
        projectName: 'cancel-test',
        description: '取消测试项目',
        repositoryName: 'cancel-test',
        ownerName: 'test',
        branches: ['main'],
        defaultBranch: 'main',
        repositoryType: 'GitHub'
      }
    }).as('getRepoDetails')
    
    // 模拟长时间运行的注册过程
    cy.intercept('POST', '/api/projects', (req) => {
      req.reply((res) => {
        // 不发送响应，模拟长时间运行
      })
    }).as('createProject')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(testUrl)
    cy.wait('@checkRepository')
    cy.wait('@getRepoDetails')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证加载器显示
    cy.get('.project-registration-loader').should('be.visible')
    
    // 点击取消按钮
    cy.get('.cancel-button').click()
    
    // 验证加载器消失
    cy.get('.project-registration-loader').should('not.exist')
    
    // 验证表单状态重置
    cy.get('button[type="submit"]').should('not.be.disabled')
    cy.get('button[type="submit"]').should('contain', '注册项目')
    
    // 验证可以重新提交
    cy.get('button[type="submit"]').should('not.be.disabled')
  })
})

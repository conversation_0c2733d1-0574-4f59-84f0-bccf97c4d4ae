describe('文档页面左侧菜单顺序测试', () => {
  beforeEach(() => {
    // 拦截API请求，模拟后端返回的文档类型数据
    cy.intercept('GET', '/api/documents/projects/*/types', {
      statusCode: 200,
      body: ['API', 'PRODUCT_INTRO', 'LLMS_TXT', 'ARCHITECTURE', 'EXTENSION'] // 模拟无序返回
    }).as('getDocumentTypes')

    cy.intercept('GET', '/api/projects/*', {
      statusCode: 200,
      body: {
        data: {
          id: 1,
          name: '测试项目',
          description: '这是一个测试项目',
          repositoryUrl: 'https://github.com/test/repo',
          branch: 'main'
        }
      }
    }).as('getProject')

    cy.intercept('GET', '/api/documents/projects/*/versions', {
      statusCode: 200,
      body: [
        { version: 'v1.0.0', timestamp: '2024-01-01T00:00:00Z' }
      ]
    }).as('getVersions')

    cy.intercept('GET', '/api/documents/projects/*/documents/*/html', {
      statusCode: 200,
      body: '<h1>测试文档内容</h1><p>这是测试内容</p>'
    }).as('getDocumentContent')
  })

  it('应该按照界面原型定义的顺序显示文档菜单', () => {
    // 访问文档页面
    cy.visit('/projects/1/documents')

    // 等待API请求完成
    cy.wait('@getProject')
    cy.wait('@getDocumentTypes')

    // 验证左侧菜单按正确顺序显示
    cy.get('nav ul li').should('have.length', 5) // 应该有5个文档类型

    // 验证具体顺序
    cy.get('nav ul li').eq(0).should('contain', '产品简介')
    cy.get('nav ul li').eq(1).should('contain', '架构设计')
    cy.get('nav ul li').eq(2).should('contain', '扩展能力')
    cy.get('nav ul li').eq(3).should('contain', '接口文档')
    cy.get('nav ul li').eq(4).should('contain', 'LLM生成内容')

    // 验证图标正确显示
    cy.get('nav ul li').eq(0).find('i').should('have.class', 'fa-home')
    cy.get('nav ul li').eq(1).find('i').should('have.class', 'fa-sitemap')
    cy.get('nav ul li').eq(2).find('i').should('have.class', 'fa-puzzle-piece')
    cy.get('nav ul li').eq(3).find('i').should('have.class', 'fa-file-code')
    cy.get('nav ul li').eq(4).find('i').should('have.class', 'fa-file-alt')
  })

  it('应该只显示接口返回的文档类型', () => {
    // 模拟后端只返回部分文档类型
    cy.intercept('GET', '/api/documents/projects/*/types', {
      statusCode: 200,
      body: ['ARCHITECTURE', 'API'] // 只返回2个类型
    }).as('getPartialDocumentTypes')

    cy.visit('/projects/1/documents')

    cy.wait('@getProject')
    cy.wait('@getPartialDocumentTypes')

    // 应该只显示2个菜单项，且按正确顺序
    cy.get('nav ul li').should('have.length', 2)
    cy.get('nav ul li').eq(0).should('contain', '架构设计')
    cy.get('nav ul li').eq(1).should('contain', '接口文档')
  })

  it('应该忽略不在原型定义中的文档类型', () => {
    // 模拟后端返回包含未知类型的数据
    cy.intercept('GET', '/api/documents/projects/*/types', {
      statusCode: 200,
      body: ['PRODUCT_INTRO', 'UNKNOWN_TYPE', 'API', 'ANOTHER_UNKNOWN']
    }).as('getDocumentTypesWithUnknown')

    cy.visit('/projects/1/documents')

    cy.wait('@getProject')
    cy.wait('@getDocumentTypesWithUnknown')

    // 应该只显示已知的文档类型，忽略未知类型
    cy.get('nav ul li').should('have.length', 2)
    cy.get('nav ul li').eq(0).should('contain', '产品简介')
    cy.get('nav ul li').eq(1).should('contain', '接口文档')
  })

  it('当API请求失败时应该显示默认的文档类型列表', () => {
    // 模拟API请求失败
    cy.intercept('GET', '/api/documents/projects/*/types', {
      statusCode: 500,
      body: { error: 'Internal Server Error' }
    }).as('getDocumentTypesError')

    cy.visit('/projects/1/documents')

    cy.wait('@getProject')
    cy.wait('@getDocumentTypesError')

    // 应该显示默认的文档类型列表，按正确顺序
    cy.get('nav ul li').should('have.length', 6) // 默认显示所有6种类型
    cy.get('nav ul li').eq(0).should('contain', '产品简介')
    cy.get('nav ul li').eq(1).should('contain', '架构设计')
    cy.get('nav ul li').eq(2).should('contain', '扩展能力')
    cy.get('nav ul li').eq(3).should('contain', '用户手册')
    cy.get('nav ul li').eq(4).should('contain', '接口文档')
    cy.get('nav ul li').eq(5).should('contain', 'LLM生成内容')

    // 应该显示错误信息
    cy.get('[data-cy="error-message"]').should('contain', '加载文档类型失败')
  })

  it('点击菜单项应该能正确切换文档内容', () => {
    cy.visit('/projects/1/documents')

    cy.wait('@getProject')
    cy.wait('@getDocumentTypes')

    // 默认应该选中第一个文档类型
    cy.get('nav ul li').eq(0).should('have.class', 'active-link')

    // 点击第二个菜单项
    cy.get('nav ul li').eq(1).click()

    // 验证选中状态切换
    cy.get('nav ul li').eq(0).should('not.have.class', 'active-link')
    cy.get('nav ul li').eq(1).should('have.class', 'active-link')

    // 验证API请求被调用
    cy.wait('@getDocumentContent')
  })
})

describe('错误处理改进测试', () => {
  beforeEach(() => {
    // 访问项目注册页面
    cy.visit('/projects/new')
  })

  it('应该显示友好的重复仓库错误信息', () => {
    // 模拟后端返回重复仓库错误
    cy.intercept('POST', '/api/projects', {
      statusCode: 400,
      body: {
        success: false,
        message: '该仓库地址已经被注册'
      }
    }).as('duplicateRepoError')

    // 填写表单
    cy.get('input[id="name"]').type('测试项目')
    cy.get('textarea[id="description"]').type('这是一个测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/existing-repo.git')

    // 提交表单
    cy.get('button[type="submit"]').click()

    // 等待API调用
    cy.wait('@duplicateRepoError')

    // 验证显示友好的错误信息
    cy.get('.bg-red-50').should('be.visible')
    cy.get('.text-red-700').should('contain', '该仓库地址已经被注册')
    
    // 验证不显示技术错误信息
    cy.get('.text-red-700').should('not.contain', 'Request failed with status code 400')
    cy.get('.text-red-700').should('not.contain', 'status code')
  })

  it('应该显示友好的验证错误信息', () => {
    // 模拟后端返回验证错误
    cy.intercept('POST', '/api/projects', {
      statusCode: 422,
      body: {
        error: '项目名称不能为空'
      }
    }).as('validationError')

    // 只填写仓库地址，不填写项目名称
    cy.get('input[id="repo_url"]').type('https://github.com/test/valid-repo.git')

    // 提交表单
    cy.get('button[type="submit"]').click()

    // 等待API调用
    cy.wait('@validationError')

    // 验证显示友好的错误信息
    cy.get('.bg-red-50').should('be.visible')
    cy.get('.text-red-700').should('contain', '项目名称不能为空')
  })

  it('应该处理不同格式的错误响应', () => {
    const errorScenarios = [
      {
        name: '字符串格式错误',
        response: {
          statusCode: 400,
          body: '仓库地址格式不正确'
        },
        expectedMessage: '仓库地址格式不正确'
      },
      {
        name: 'message字段错误',
        response: {
          statusCode: 400,
          body: {
            message: '仓库访问权限不足'
          }
        },
        expectedMessage: '仓库访问权限不足'
      },
      {
        name: 'errorMessage字段错误',
        response: {
          statusCode: 400,
          body: {
            errorMessage: '仓库不存在或无法访问'
          }
        },
        expectedMessage: '仓库不存在或无法访问'
      },
      {
        name: 'error字段错误',
        response: {
          statusCode: 400,
          body: {
            error: '网络连接超时'
          }
        },
        expectedMessage: '网络连接超时'
      }
    ]

    errorScenarios.forEach((scenario, index) => {
      cy.log(`测试场景: ${scenario.name}`)

      // 设置拦截器
      cy.intercept('POST', '/api/projects', scenario.response).as(`errorTest${index}`)

      // 填写表单
      cy.get('input[id="name"]').clear().type(`测试项目${index}`)
      cy.get('input[id="repo_url"]').clear().type(`https://github.com/test/repo${index}.git`)

      // 提交表单
      cy.get('button[type="submit"]').click()

      // 等待API调用
      cy.wait(`@errorTest${index}`)

      // 验证显示正确的错误信息
      cy.get('.bg-red-50').should('be.visible')
      cy.get('.text-red-700').should('contain', scenario.expectedMessage)

      // 清除错误信息准备下一个测试
      cy.reload()
    })
  })

  it('应该处理网络错误', () => {
    // 模拟网络错误
    cy.intercept('POST', '/api/projects', { forceNetworkError: true }).as('networkError')

    // 填写表单
    cy.get('input[id="name"]').type('网络测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/network-test.git')

    // 提交表单
    cy.get('button[type="submit"]').click()

    // 等待API调用
    cy.wait('@networkError')

    // 验证显示网络错误信息
    cy.get('.bg-red-50').should('be.visible')
    cy.get('.text-red-700').should('contain', '网络连接失败')
  })

  it('应该处理服务器错误', () => {
    // 模拟服务器内部错误
    cy.intercept('POST', '/api/projects', {
      statusCode: 500,
      body: {
        message: '服务器内部错误，请稍后再试'
      }
    }).as('serverError')

    // 填写表单
    cy.get('input[id="name"]').type('服务器错误测试')
    cy.get('input[id="repo_url"]').type('https://github.com/test/server-error.git')

    // 提交表单
    cy.get('button[type="submit"]').click()

    // 等待API调用
    cy.wait('@serverError')

    // 验证显示服务器错误信息
    cy.get('.bg-red-50').should('be.visible')
    cy.get('.text-red-700').should('contain', '服务器内部错误，请稍后再试')
  })

  it('应该在错误后恢复表单可用状态', () => {
    // 模拟错误响应
    cy.intercept('POST', '/api/projects', {
      statusCode: 400,
      body: {
        message: '测试错误'
      }
    }).as('testError')

    // 填写表单
    cy.get('input[id="name"]').type('恢复测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/recovery-test.git')

    // 提交表单
    cy.get('button[type="submit"]').click()

    // 等待API调用
    cy.wait('@testError')

    // 验证错误显示
    cy.get('.bg-red-50').should('be.visible')

    // 验证表单恢复可用状态
    cy.get('button[type="submit"]').should('not.be.disabled')
    cy.get('input[id="name"]').should('not.be.disabled')
    cy.get('input[id="repo_url"]').should('not.be.disabled')

    // 验证可以重新提交
    cy.get('button[type="submit"]').should('contain', '注册项目')
    cy.get('button[type="submit"]').should('not.contain', '注册中')
  })

  it('应该在成功注册后清除错误信息', () => {
    // 先模拟一个错误
    cy.intercept('POST', '/api/projects', {
      statusCode: 400,
      body: {
        message: '第一次错误'
      }
    }).as('firstError')

    // 填写表单并提交
    cy.get('input[id="name"]').type('测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/test-repo.git')
    cy.get('button[type="submit"]').click()

    // 等待错误
    cy.wait('@firstError')
    cy.get('.bg-red-50').should('be.visible')

    // 然后模拟成功响应
    cy.intercept('POST', '/api/projects', {
      statusCode: 200,
      body: {
        id: 1,
        name: '测试项目',
        repositoryUrl: 'https://github.com/test/test-repo.git'
      }
    }).as('successResponse')

    // 重新提交
    cy.get('button[type="submit"]').click()

    // 等待成功响应
    cy.wait('@successResponse')

    // 验证错误信息被清除
    cy.get('.bg-red-50').should('not.exist')
    
    // 验证跳转到项目列表页面
    cy.url().should('include', '/projects')
  })
})

describe('错误处理测试页面', () => {
  beforeEach(() => {
    cy.visit('/test/error-handling')
  })

  it('应该显示错误处理测试页面', () => {
    cy.get('h1').should('contain', '错误处理测试')
    cy.get('h2').should('contain', '项目注册错误处理测试')
    
    // 验证测试表单存在
    cy.get('input[placeholder="输入项目名称"]').should('be.visible')
    cy.get('input[placeholder="输入仓库地址"]').should('be.visible')
    cy.get('textarea[placeholder="输入项目描述"]').should('be.visible')
    
    // 验证快速测试按钮存在
    cy.get('button').should('contain', '测试重复仓库地址')
    cy.get('button').should('contain', '测试无效仓库地址')
    cy.get('button').should('contain', '测试空字段验证')
  })

  it('应该能够执行快速测试', () => {
    // 模拟重复仓库错误
    cy.intercept('POST', '/api/projects', {
      statusCode: 400,
      body: {
        message: '该仓库地址已经被注册'
      }
    }).as('duplicateError')

    // 点击重复仓库测试按钮
    cy.get('button').contains('测试重复仓库地址').click()

    // 等待API调用
    cy.wait('@duplicateError')

    // 验证测试结果显示
    cy.get('.bg-red-50').should('be.visible')
    cy.get('.text-red-700').should('contain', '该仓库地址已经被注册')
    
    // 验证测试历史记录
    cy.get('.border-gray-200').should('contain', '测试历史')
    cy.get('.text-red-600').should('contain', '❌ 重复仓库地址')
  })
})

/// <reference types="cypress" />

describe('任务详情页面 E2E 测试', () => {
  const testTaskId = 'task-12345'

  beforeEach(() => {
    // 在每个测试前访问任务详情页面
    cy.visitTaskDetail(testTaskId)
  })

  describe('页面基础功能', () => {
    it('应该正确加载任务详情页面', () => {
      // 检查页面标题
      cy.get('h1').should('contain.text', '任务详情')
      
      // 检查页面描述
      cy.get('[data-cy="page-description"]').should('contain.text', '查看任务执行状态和详细信息')
      
      // 检查返回按钮存在
      cy.get('[data-cy="back-button"]').should('be.visible')
      
      // 检查刷新按钮存在
      cy.get('[data-cy="refresh-button"]').should('be.visible')
    })

    it('应该显示任务ID在URL中', () => {
      cy.url().should('include', `/tasks/${testTaskId}`)
    })

    it('返回按钮应该能够返回任务列表', () => {
      cy.get('[data-cy="back-button"]').click()
      cy.url().should('include', '/tasks')
      cy.get('h1').should('contain.text', '任务队列')
    })
  })

  describe('任务信息显示', () => {
    it('应该显示任务基本信息', () => {
      // 检查任务信息卡片
      cy.get('[data-cy="task-info-card"]').should('be.visible')
      
      // 检查任务ID显示
      cy.get('[data-cy="task-id-display"]').should('contain.text', testTaskId)
      
      // 检查任务状态显示
      cy.get('[data-cy="task-status-display"]').should('be.visible')
      
      // 检查任务类型显示
      cy.get('[data-cy="task-type-display"]').should('be.visible')
      
      // 检查关联项目显示
      cy.get('[data-cy="task-project-display"]').should('be.visible')
    })

    it('应该显示任务时间信息', () => {
      // 检查开始时间
      cy.get('[data-cy="task-start-time"]').should('be.visible')
      
      // 检查结束时间（可能为空）
      cy.get('[data-cy="task-end-time"]').should('exist')
      
      // 检查执行时长（如果任务已完成）
      cy.get('[data-cy="task-duration"]').should('exist')
    })

    it('应该显示任务进度信息', () => {
      // 检查进度条或进度信息
      cy.get('[data-cy="task-progress"]').should('be.visible')
      
      // 检查进度百分比
      cy.get('[data-cy="progress-percentage"]').should('be.visible')
    })
  })

  describe('任务状态处理', () => {
    it('处理中的任务应该显示正确的状态', () => {
      // 模拟处理中的任务
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        fixture: 'task-processing.json'
      }).as('getProcessingTask')
      
      cy.reload()
      cy.wait('@getProcessingTask')
      
      cy.get('[data-cy="task-status-display"]').should('contain.text', '处理中')
      cy.get('[data-cy="task-progress"]').should('be.visible')
    })

    it('已完成的任务应该显示正确的状态', () => {
      // 模拟已完成的任务
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        fixture: 'task-completed.json'
      }).as('getCompletedTask')
      
      cy.reload()
      cy.wait('@getCompletedTask')
      
      cy.get('[data-cy="task-status-display"]').should('contain.text', '成功')
      cy.get('[data-cy="task-end-time"]').should('not.be.empty')
    })

    it('失败的任务应该显示错误信息', () => {
      // 模拟失败的任务
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        fixture: 'task-failed.json'
      }).as('getFailedTask')
      
      cy.reload()
      cy.wait('@getFailedTask')
      
      cy.get('[data-cy="task-status-display"]').should('contain.text', '失败')
      cy.get('[data-cy="error-message"]').should('be.visible')
      cy.get('[data-cy="retry-button"]').should('be.visible')
    })
  })

  describe('任务日志和输出', () => {
    it('应该显示任务执行日志', () => {
      // 检查日志区域
      cy.get('[data-cy="task-logs"]').should('be.visible')
      
      // 检查日志内容
      cy.get('[data-cy="log-content"]').should('exist')
      
      // 检查日志刷新按钮
      cy.get('[data-cy="refresh-logs-button"]').should('be.visible')
    })

    it('应该能够刷新任务日志', () => {
      cy.get('[data-cy="refresh-logs-button"]').click()
      
      // 验证日志刷新请求
      cy.intercept('GET', `/api/tasks/${testTaskId}/logs`).as('refreshLogs')
      cy.wait('@refreshLogs')
    })

    it('应该显示任务输出结果', () => {
      // 检查输出区域
      cy.get('[data-cy="task-output"]').should('be.visible')
      
      // 检查输出内容
      cy.get('[data-cy="output-content"]').should('exist')
    })
  })

  describe('任务操作', () => {
    it('应该能够重新执行失败的任务', () => {
      // 模拟失败的任务
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        fixture: 'task-failed.json'
      }).as('getFailedTask')
      
      cy.reload()
      cy.wait('@getFailedTask')
      
      // 点击重试按钮
      cy.get('[data-cy="retry-button"]').should('be.visible').click()
      
      // 验证重试请求
      cy.intercept('POST', `/api/tasks/${testTaskId}/retry`).as('retryTask')
      cy.wait('@retryTask')
      
      // 验证状态更新
      cy.get('[data-cy="task-status-display"]').should('contain.text', '处理中')
    })

    it('应该能够取消正在执行的任务', () => {
      // 模拟处理中的任务
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        fixture: 'task-processing.json'
      }).as('getProcessingTask')
      
      cy.reload()
      cy.wait('@getProcessingTask')
      
      // 点击取消按钮
      cy.get('[data-cy="cancel-button"]').should('be.visible').click()
      
      // 确认取消操作
      cy.get('[data-cy="confirm-cancel"]').click()
      
      // 验证取消请求
      cy.intercept('POST', `/api/tasks/${testTaskId}/cancel`).as('cancelTask')
      cy.wait('@cancelTask')
    })

    it('应该能够删除任务', () => {
      // 点击删除按钮
      cy.get('[data-cy="delete-button"]').should('be.visible').click()
      
      // 确认删除操作
      cy.get('[data-cy="confirm-delete"]').click()
      
      // 验证删除请求
      cy.intercept('DELETE', `/api/tasks/${testTaskId}`).as('deleteTask')
      cy.wait('@deleteTask')
      
      // 验证跳转回任务列表
      cy.url().should('include', '/tasks')
    })
  })

  describe('实时更新', () => {
    it('任务状态应该实时更新', () => {
      // 模拟任务状态从处理中变为完成
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        fixture: 'task-processing.json'
      }).as('getProcessingTask')
      
      cy.reload()
      cy.wait('@getProcessingTask')
      
      // 验证初始状态
      cy.get('[data-cy="task-status-display"]').should('contain.text', '处理中')
      
      // 模拟状态更新
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        fixture: 'task-completed.json'
      }).as('getCompletedTask')
      
      // 等待自动刷新
      cy.wait(5000)
      cy.wait('@getCompletedTask')
      
      // 验证状态更新
      cy.get('[data-cy="task-status-display"]').should('contain.text', '成功')
    })
  })

  describe('错误处理', () => {
    it('任务不存在时应该显示404错误', () => {
      cy.intercept('GET', '/api/tasks/nonexistent-task', {
        statusCode: 404,
        body: { error: 'Task not found' }
      }).as('taskNotFound')
      
      cy.visit('/tasks/nonexistent-task')
      cy.wait('@taskNotFound')
      
      cy.get('[data-cy="error-404"]').should('be.visible')
      cy.get('[data-cy="back-to-list"]').should('be.visible')
    })

    it('网络错误时应该显示加载失败', () => {
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        forceNetworkError: true
      }).as('networkError')
      
      cy.reload()
      
      cy.get('[data-cy="loading-error"]').should('be.visible')
      cy.get('[data-cy="retry-load"]').should('be.visible')
    })

    it('点击重试应该重新加载任务数据', () => {
      // 先模拟网络错误
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        forceNetworkError: true
      }).as('networkError')
      
      cy.reload()
      cy.get('[data-cy="loading-error"]').should('be.visible')
      
      // 然后模拟成功响应
      cy.intercept('GET', `/api/tasks/${testTaskId}`, {
        fixture: 'task-processing.json'
      }).as('taskLoaded')
      
      // 点击重试
      cy.get('[data-cy="retry-load"]').click()
      cy.wait('@taskLoaded')
      
      // 验证任务数据加载成功
      cy.get('[data-cy="task-info-card"]').should('be.visible')
    })
  })

  describe('响应式设计', () => {
    it('在移动设备上应该正确显示', () => {
      cy.viewport('iphone-6')
      
      cy.get('h1').should('be.visible')
      cy.get('[data-cy="task-info-card"]').should('be.visible')
      cy.get('[data-cy="back-button"]').should('be.visible')
    })

    it('在平板设备上应该正确显示', () => {
      cy.viewport('ipad-2')
      
      cy.get('h1').should('be.visible')
      cy.get('[data-cy="task-info-card"]').should('be.visible')
      cy.get('[data-cy="task-logs"]').should('be.visible')
    })
  })
})

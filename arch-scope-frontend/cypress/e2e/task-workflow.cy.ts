/// <reference types="cypress" />

describe('任务管理完整工作流程 E2E 测试', () => {
  beforeEach(() => {
    // 模拟API响应
    cy.intercept('GET', '/api/tasks*', { fixture: 'tasks.json' }).as('getTasks')
    cy.intercept('GET', '/api/tasks/task-12345', { fixture: 'task-processing.json' }).as('getTaskDetail')
  })

  describe('完整用户流程', () => {
    it('用户应该能够完成完整的任务管理流程', () => {
      // 1. 访问任务列表页面
      cy.visitTaskList()
      cy.wait('@getTasks')
      
      // 验证页面加载
      cy.get('h1').should('contain.text', '任务队列')
      cy.checkTaskTableExists()
      
      // 2. 搜索特定任务
      cy.searchTasks('Awesome')
      cy.get('[data-cy="task-row"]').should('have.length', 1)
      cy.get('[data-cy="task-project"]').should('contain.text', 'Awesome Components')
      
      // 3. 清空搜索，查看所有任务
      cy.get('[data-cy="search-input"]').clear()
      cy.wait(500)
      cy.get('[data-cy="task-row"]').should('have.length.greaterThan', 1)
      
      // 4. 按状态筛选任务
      cy.filterTasksByStatus('processing')
      cy.get('[data-cy="task-row"]').each(($row) => {
        cy.wrap($row).find('[data-cy="task-status"]').should('contain.text', '处理中')
      })
      
      // 5. 点击任务详情
      cy.get('[data-cy="task-row"]').first().click()
      cy.wait('@getTaskDetail')
      
      // 验证跳转到详情页面
      cy.url().should('include', '/tasks/')
      cy.get('h1').should('contain.text', '任务详情')
      
      // 6. 查看任务详细信息
      cy.get('[data-cy="task-info-card"]').should('be.visible')
      cy.get('[data-cy="task-status-display"]').should('contain.text', '处理中')
      cy.get('[data-cy="task-progress"]').should('be.visible')
      
      // 7. 返回任务列表
      cy.get('[data-cy="back-button"]').click()
      cy.url().should('include', '/tasks')
      cy.get('h1').should('contain.text', '任务队列')
    })

    it('用户应该能够处理任务状态变化', () => {
      // 访问任务详情页面
      cy.visitTaskDetail('task-12345')
      cy.wait('@getTaskDetail')
      
      // 验证初始状态为处理中
      cy.get('[data-cy="task-status-display"]').should('contain.text', '处理中')
      cy.get('[data-cy="task-progress"]').should('be.visible')
      
      // 模拟任务完成
      cy.intercept('GET', '/api/tasks/task-12345', { fixture: 'task-completed.json' }).as('getCompletedTask')
      
      // 刷新页面或等待自动更新
      cy.get('[data-cy="refresh-button"]').click()
      cy.wait('@getCompletedTask')
      
      // 验证状态更新为成功
      cy.get('[data-cy="task-status-display"]').should('contain.text', '成功')
      cy.get('[data-cy="task-end-time"]').should('not.be.empty')
      cy.get('[data-cy="task-output"]').should('be.visible')
    })

    it('用户应该能够处理任务失败情况', () => {
      // 模拟失败的任务
      cy.intercept('GET', '/api/tasks/task-12345', { fixture: 'task-failed.json' }).as('getFailedTask')
      
      cy.visitTaskDetail('task-12345')
      cy.wait('@getFailedTask')
      
      // 验证失败状态显示
      cy.get('[data-cy="task-status-display"]').should('contain.text', '失败')
      cy.get('[data-cy="error-message"]').should('be.visible')
      cy.get('[data-cy="retry-button"]').should('be.visible')
      
      // 模拟重试操作
      cy.intercept('POST', '/api/tasks/task-12345/retry', { statusCode: 200 }).as('retryTask')
      cy.intercept('GET', '/api/tasks/task-12345', { fixture: 'task-processing.json' }).as('getRetryTask')
      
      cy.get('[data-cy="retry-button"]').click()
      cy.wait('@retryTask')
      cy.wait('@getRetryTask')
      
      // 验证重试后状态变为处理中
      cy.get('[data-cy="task-status-display"]').should('contain.text', '处理中')
    })
  })

  describe('错误处理流程', () => {
    it('应该正确处理网络错误', () => {
      // 模拟网络错误
      cy.intercept('GET', '/api/tasks*', { forceNetworkError: true }).as('networkError')
      
      cy.visit('/tasks')
      
      // 验证错误处理
      cy.get('[data-cy="error-message"]').should('be.visible')
      
      // 模拟网络恢复
      cy.intercept('GET', '/api/tasks*', { fixture: 'tasks.json' }).as('getTasks')
      
      // 重试加载
      cy.get('[data-cy="refresh-button"]').click()
      cy.wait('@getTasks')
      
      // 验证数据正常加载
      cy.checkTaskTableExists()
      cy.get('[data-cy="task-row"]').should('have.length.greaterThan', 0)
    })

    it('应该正确处理404错误', () => {
      // 模拟任务不存在
      cy.intercept('GET', '/api/tasks/nonexistent-task', {
        statusCode: 404,
        body: { error: 'Task not found' }
      }).as('taskNotFound')
      
      cy.visit('/tasks/nonexistent-task')
      cy.wait('@taskNotFound')
      
      // 验证404错误处理
      cy.get('[data-cy="error-404"]').should('be.visible')
      cy.get('[data-cy="back-to-list"]').should('be.visible')
      
      // 点击返回列表
      cy.get('[data-cy="back-to-list"]').click()
      cy.url().should('include', '/tasks')
    })
  })

  describe('性能和用户体验', () => {
    it('页面加载时间应该在合理范围内', () => {
      const startTime = Date.now()
      
      cy.visitTaskList()
      cy.wait('@getTasks')
      
      cy.checkTaskTableExists().then(() => {
        const loadTime = Date.now() - startTime
        expect(loadTime).to.be.lessThan(3000) // 3秒内加载完成
      })
    })

    it('搜索响应应该及时', () => {
      cy.visitTaskList()
      cy.wait('@getTasks')
      
      const startTime = Date.now()
      cy.searchTasks('Payment')
      
      cy.get('[data-cy="task-row"]').should('have.length', 1).then(() => {
        const searchTime = Date.now() - startTime
        expect(searchTime).to.be.lessThan(1000) // 1秒内响应
      })
    })

    it('状态筛选应该及时响应', () => {
      cy.visitTaskList()
      cy.wait('@getTasks')
      
      const startTime = Date.now()
      cy.filterTasksByStatus('success')
      
      cy.get('[data-cy="task-row"]').should('exist').then(() => {
        const filterTime = Date.now() - startTime
        expect(filterTime).to.be.lessThan(1000) // 1秒内响应
      })
    })
  })

  describe('数据一致性', () => {
    it('任务列表和详情页面数据应该一致', () => {
      cy.visitTaskList()
      cy.wait('@getTasks')
      
      // 获取列表中第一个任务的信息
      let taskInfo: any = {}
      
      cy.get('[data-cy="task-row"]').first().within(() => {
        cy.get('[data-cy="task-id"]').invoke('text').then((id) => {
          taskInfo.id = id.trim()
        })
        cy.get('[data-cy="task-project"]').invoke('text').then((project) => {
          taskInfo.project = project.trim()
        })
        cy.get('[data-cy="task-status"]').invoke('text').then((status) => {
          taskInfo.status = status.trim()
        })
      })
      
      // 点击进入详情页面
      cy.get('[data-cy="task-row"]').first().click()
      cy.wait('@getTaskDetail')
      
      // 验证详情页面数据一致性
      cy.get('[data-cy="task-id-display"]').should('contain.text', taskInfo.id)
      cy.get('[data-cy="task-project-display"]').should('contain.text', taskInfo.project)
    })
  })

  describe('无障碍访问', () => {
    it('页面应该支持键盘导航', () => {
      cy.visitTaskList()
      cy.wait('@getTasks')
      
      // 使用Tab键导航
      cy.get('body').tab()
      cy.focused().should('have.attr', 'data-cy', 'refresh-button')
      
      cy.focused().tab()
      cy.focused().should('have.attr', 'data-cy', 'search-input')
      
      cy.focused().tab()
      cy.focused().should('have.attr', 'data-cy', 'status-filter')
    })

    it('表格应该有适当的ARIA标签', () => {
      cy.visitTaskList()
      cy.wait('@getTasks')
      
      cy.get('[data-cy="task-table"]').should('have.attr', 'role', 'table')
      cy.get('[data-cy="task-table"] thead').should('exist')
      cy.get('[data-cy="task-table"] tbody').should('exist')
    })
  })
})

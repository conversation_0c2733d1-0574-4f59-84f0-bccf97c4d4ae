describe('Landing Page', () => {
  beforeEach(() => {
    cy.visit('/')
  })

  it('should display the navigation bar', () => {
    // 检查导航栏
    cy.contains('ArchScope').should('be.visible')
    cy.contains('功能特性').should('be.visible')
    cy.contains('工作原理').should('be.visible')
    cy.contains('价格方案').should('be.visible')
    cy.contains('成功案例').should('be.visible')
    cy.contains('常见问题').should('be.visible')

    // 检查CTA按钮在导航栏中
    cy.get('nav').contains('立即开始').should('be.visible')
  })

  it('should display the main hero section', () => {
    // 检查主标题
    cy.contains('架构鹰眼').should('be.visible')
    cy.contains('ArchScope').should('be.visible')

    // 检查副标题
    cy.contains('面向开发者的智能架构观测和守护系统').should('be.visible')

    // 检查CTA按钮
    cy.contains('立即开始').should('be.visible')
    cy.contains('了解更多').should('be.visible')

    // 检查信任指标
    cy.contains('免费试用').should('be.visible')
    cy.contains('5分钟快速上手').should('be.visible')
    cy.contains('专业技术支持').should('be.visible')
  })

  it('should display statistics section', () => {
    // 检查统计数据标题
    cy.contains('数据驱动的架构洞察').should('be.visible')

    // 检查统计项目
    cy.contains('活跃项目').should('be.visible')
    cy.contains('分析任务').should('be.visible')
    cy.contains('生成文档').should('be.visible')
  })

  it('should display features section', () => {
    // 检查特性标题
    cy.contains('强大的核心功能').should('be.visible')

    // 检查第一行特性卡片
    cy.contains('多语言LLM代码分析').should('be.visible')
    cy.contains('智能文档生成与同步').should('be.visible')
    cy.contains('增量解析与版本感知').should('be.visible')

    // 检查第二行特性卡片
    cy.contains('C4架构图自动生成').should('be.visible')
    cy.contains('企业级安全与隐私').should('be.visible')
    cy.contains('传统工具深度集成').should('be.visible')

    // 检查第三行特性卡片
    cy.contains('智能提示词工程').should('be.visible')
    cy.contains('项目健康度评估').should('be.visible')
    cy.contains('大型代码库智能处理').should('be.visible')

    // 检查技术标签
    cy.contains('Java').should('be.visible')
    cy.contains('Python').should('be.visible')
    cy.contains('TypeScript').should('be.visible')
    cy.contains('SonarQube').should('be.visible')
  })

  it('should display technology stack', () => {
    // 检查技术栈标题
    cy.contains('现代化技术栈').should('be.visible')

    // 检查技术栈项目
    cy.contains('Vue 3').should('be.visible')
    cy.contains('TypeScript').should('be.visible')
    cy.contains('Tailwind').should('be.visible')
    cy.contains('Spring Boot').should('be.visible')
    cy.contains('MySQL').should('be.visible')
  })

  it('should display how it works section', () => {
    // 检查工作原理标题
    cy.contains('工作原理').should('be.visible')

    // 检查工作流程步骤
    cy.contains('连接代码仓库').should('be.visible')
    cy.contains('AI智能分析').should('be.visible')
    cy.contains('生成架构文档').should('be.visible')
    cy.contains('持续监控优化').should('be.visible')
  })

  it('should display pricing section', () => {
    // 检查价格方案标题
    cy.contains('灵活的价格方案').should('be.visible')

    // 检查三个价格方案
    cy.contains('基础版').should('be.visible')
    cy.contains('专业版').should('be.visible')
    cy.contains('企业版').should('be.visible')

    // 检查价格
    cy.contains('免费').should('be.visible')
    cy.contains('¥299').should('be.visible')
    cy.contains('¥999').should('be.visible')
  })

  it('should display testimonials section', () => {
    // 检查成功案例标题
    cy.contains('客户成功案例').should('be.visible')

    // 检查案例公司
    cy.contains('TechCorp').should('be.visible')
    cy.contains('StartupX').should('be.visible')
    cy.contains('BigData Inc').should('be.visible')
  })

  it('should display FAQ section', () => {
    // 检查FAQ标题
    cy.contains('常见问题').should('be.visible')

    // 检查FAQ项目
    cy.contains('什么是ArchScope？').should('be.visible')
    cy.contains('支持哪些编程语言？').should('be.visible')
    cy.contains('如何保证代码安全？').should('be.visible')
  })

  it('should display footer', () => {
    // 检查页脚内容
    cy.contains('© 2024 ArchScope. 保留所有权利。').should('be.visible')
    cy.contains('隐私政策').should('be.visible')
    cy.contains('服务条款').should('be.visible')
  })

  it('should display bottom CTA section', () => {
    // 检查底部CTA标题
    cy.contains('准备好开始您的架构之旅了吗？').should('be.visible')

    // 检查CTA按钮
    cy.contains('免费开始使用').should('be.visible')
    cy.contains('查看演示').should('be.visible')
  })

  it('should navigate to projects page when clicking CTA buttons', () => {
    // 点击主要CTA按钮
    cy.contains('立即开始').click()
    cy.url().should('include', '/projects')

    // 返回首页
    cy.visit('/')

    // 点击底部CTA按钮
    cy.contains('免费开始使用').click()
    cy.url().should('include', '/projects')
  })

  it('should have responsive design', () => {
    // 测试移动端视图
    cy.viewport(375, 667)
    cy.contains('架构鹰眼').should('be.visible')

    // 测试平板视图
    cy.viewport(768, 1024)
    cy.contains('架构鹰眼').should('be.visible')

    // 测试桌面视图
    cy.viewport(1920, 1080)
    cy.contains('架构鹰眼').should('be.visible')
  })

  it('should have smooth animations', () => {
    // 检查页面加载后元素是否可见（动画完成）
    cy.get('h1').should('be.visible')
    cy.get('h1').should('have.css', 'opacity', '1')
  })
})

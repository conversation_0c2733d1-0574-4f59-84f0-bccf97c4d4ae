describe('仓库详情API单次调用验证', () => {
  beforeEach(() => {
    // 访问项目注册页面
    cy.visit('/projects/register')
    
    // 等待页面加载完成
    cy.get('h1').should('contain', '注册新项目')
  })

  it('应该只调用一次仓库详情API，不重复调用', () => {
    const testUrl = 'https://github.com/test/single-call-project.git'
    let detailsCallCount = 0
    
    // 拦截仓库详情API调用并计数
    cy.intercept('GET', '/git-repository/details*', (req) => {
      detailsCallCount++
      console.log(`仓库详情API调用次数: ${detailsCallCount}`)
      
      req.reply({
        statusCode: 200,
        body: {
          success: true,
          projectName: 'single-call-project',
          description: '测试项目描述',
          repositoryName: 'single-call-project',
          ownerName: 'test',
          branches: ['main', 'develop'],
          defaultBranch: 'main',
          repositoryType: 'GitHub'
        }
      })
    }).as('getRepoDetails')
    
    // 拦截项目唯一性检查API
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        message: '仓库可以使用'
      }
    }).as('checkRepository')
    
    // 输入仓库URL
    cy.get('input[id="repo_url"]').type(testUrl)
    
    // 等待防抖延迟完成（800ms + 一些缓冲时间）
    cy.wait(1200)
    
    // 验证API只被调用一次
    cy.get('@getRepoDetails.all').should('have.length', 1)
    
    // 验证表单自动填充
    cy.get('input[id="name"]').should('have.value', 'single-call-project')
    cy.get('textarea[id="description"]').should('have.value', '测试项目描述')
    cy.get('select[id="branch"]').should('have.value', 'main')
    
    // 验证成功提示
    cy.contains('仓库验证成功，已自动填充项目信息').should('be.visible')
  })

  it('应该在URL变化时重新调用API，但每个URL只调用一次', () => {
    const firstUrl = 'https://github.com/test/first-project.git'
    const secondUrl = 'https://github.com/test/second-project.git'
    let detailsCallCount = 0
    
    // 拦截仓库详情API调用并计数
    cy.intercept('GET', '/git-repository/details*', (req) => {
      detailsCallCount++
      console.log(`仓库详情API调用次数: ${detailsCallCount}, URL: ${req.url}`)
      
      const url = new URL(req.url)
      const repositoryUrl = url.searchParams.get('repositoryUrl')
      
      if (repositoryUrl?.includes('first-project')) {
        req.reply({
          statusCode: 200,
          body: {
            success: true,
            projectName: 'first-project',
            description: '第一个项目',
            repositoryName: 'first-project',
            ownerName: 'test',
            branches: ['main'],
            defaultBranch: 'main',
            repositoryType: 'GitHub'
          }
        })
      } else if (repositoryUrl?.includes('second-project')) {
        req.reply({
          statusCode: 200,
          body: {
            success: true,
            projectName: 'second-project',
            description: '第二个项目',
            repositoryName: 'second-project',
            ownerName: 'test',
            branches: ['main', 'develop'],
            defaultBranch: 'main',
            repositoryType: 'GitHub'
          }
        })
      }
    }).as('getRepoDetails')
    
    // 拦截项目唯一性检查API
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        message: '仓库可以使用'
      }
    }).as('checkRepository')
    
    // 输入第一个URL
    cy.get('input[id="repo_url"]').type(firstUrl)
    cy.wait(1200) // 等待防抖
    
    // 验证第一次调用
    cy.get('@getRepoDetails.all').should('have.length', 1)
    cy.get('input[id="name"]').should('have.value', 'first-project')
    
    // 清空并输入第二个URL
    cy.get('input[id="repo_url"]').clear().type(secondUrl)
    cy.wait(1200) // 等待防抖
    
    // 验证第二次调用
    cy.get('@getRepoDetails.all').should('have.length', 2)
    cy.get('input[id="name"]').should('have.value', 'second-project')
    
    // 再次输入第一个URL（应该重新调用）
    cy.get('input[id="repo_url"]').clear().type(firstUrl)
    cy.wait(1200) // 等待防抖
    
    // 验证第三次调用
    cy.get('@getRepoDetails.all').should('have.length', 3)
    cy.get('input[id="name"]').should('have.value', 'first-project')
  })

  it('应该在输入过程中不调用API，只在停止输入后调用', () => {
    const testUrl = 'https://github.com/test/typing-test.git'
    let detailsCallCount = 0
    
    // 拦截仓库详情API调用并计数
    cy.intercept('GET', '/git-repository/details*', (req) => {
      detailsCallCount++
      console.log(`仓库详情API调用次数: ${detailsCallCount}`)
      
      req.reply({
        statusCode: 200,
        body: {
          success: true,
          projectName: 'typing-test',
          description: '输入测试项目',
          repositoryName: 'typing-test',
          ownerName: 'test',
          branches: ['main'],
          defaultBranch: 'main',
          repositoryType: 'GitHub'
        }
      })
    }).as('getRepoDetails')
    
    // 拦截项目唯一性检查API
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        message: '仓库可以使用'
      }
    }).as('checkRepository')
    
    // 逐字符输入URL（模拟用户输入）
    const input = cy.get('input[id="repo_url"]')
    
    // 输入部分URL
    input.type('https://github.com/test/typ')
    cy.wait(500) // 等待一半的防抖时间
    
    // 继续输入
    input.type('ing-test.git')
    cy.wait(500) // 再等待一半时间
    
    // 此时应该还没有调用API
    cy.get('@getRepoDetails.all').should('have.length', 0)
    
    // 等待完整的防抖时间
    cy.wait(800)
    
    // 现在应该调用了API
    cy.get('@getRepoDetails.all').should('have.length', 1)
    
    // 验证表单填充
    cy.get('input[id="name"]').should('have.value', 'typing-test')
  })

  it('应该在失焦时不重复调用API', () => {
    const testUrl = 'https://github.com/test/blur-test.git'
    let detailsCallCount = 0
    
    // 拦截仓库详情API调用并计数
    cy.intercept('GET', '/git-repository/details*', (req) => {
      detailsCallCount++
      console.log(`仓库详情API调用次数: ${detailsCallCount}`)
      
      req.reply({
        statusCode: 200,
        body: {
          success: true,
          projectName: 'blur-test',
          description: '失焦测试项目',
          repositoryName: 'blur-test',
          ownerName: 'test',
          branches: ['main'],
          defaultBranch: 'main',
          repositoryType: 'GitHub'
        }
      })
    }).as('getRepoDetails')
    
    // 拦截项目唯一性检查API
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        message: '仓库可以使用'
      }
    }).as('checkRepository')
    
    // 输入URL
    cy.get('input[id="repo_url"]').type(testUrl)
    
    // 等待防抖完成
    cy.wait(1200)
    
    // 验证API被调用一次
    cy.get('@getRepoDetails.all').should('have.length', 1)
    
    // 触发失焦事件
    cy.get('input[id="repo_url"]').blur()
    
    // 点击其他地方确保失焦
    cy.get('input[id="name"]').click()
    
    // 等待一段时间，确保没有额外的API调用
    cy.wait(1000)
    
    // 验证API仍然只被调用一次
    cy.get('@getRepoDetails.all').should('have.length', 1)
    
    // 验证表单仍然正确填充
    cy.get('input[id="name"]').should('have.value', 'blur-test')
  })
})

describe('项目注册加载状态', () => {
  beforeEach(() => {
    // 访问项目注册页面
    cy.visit('/projects/new')
  })

  it('应该显示项目注册表单', () => {
    cy.get('h1').should('contain', '注册新项目')
    cy.get('input[id="repo_url"]').should('be.visible')
    cy.get('input[id="name"]').should('be.visible')
    cy.get('textarea[id="description"]').should('be.visible')
    cy.get('select[id="branch"]').should('be.visible')
    cy.get('button[type="submit"]').should('be.visible')
  })

  it('应该在表单无效时禁用提交按钮', () => {
    // 空表单时按钮应该被禁用
    cy.get('button[type="submit"]').should('be.disabled')
    
    // 只填写项目名称
    cy.get('input[id="name"]').type('测试项目')
    cy.get('button[type="submit"]').should('be.disabled')
    
    // 填写无效的仓库URL
    cy.get('input[id="repo_url"]').type('invalid-url')
    cy.get('button[type="submit"]').should('be.disabled')
    
    // 填写有效的仓库URL
    cy.get('input[id="repo_url"]').clear().type('https://github.com/test/repo.git')
    cy.get('button[type="submit"]').should('not.be.disabled')
  })

  it('应该在提交时显示加载状态', () => {
    // 填写表单
    cy.get('input[id="name"]').type('测试项目')
    cy.get('textarea[id="description"]').type('这是一个测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/repo.git')
    
    // 模拟API响应延迟
    cy.intercept('POST', '/api/projects', {
      delay: 3000,
      statusCode: 200,
      body: {
        id: 1,
        name: '测试项目',
        description: '这是一个测试项目',
        repositoryUrl: 'https://github.com/test/repo.git',
        branch: 'main'
      }
    }).as('createProject')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证加载状态
    cy.get('.project-registration-loader').should('be.visible')
    cy.get('.spinner').should('be.visible')
    cy.get('.status-title').should('contain', '正在验证Git仓库')
    cy.get('.progress-bar').should('be.visible')
    cy.get('.steps-indicator').should('be.visible')
    
    // 验证取消按钮
    cy.get('.cancel-button').should('be.visible').and('not.be.disabled')
    
    // 等待API响应
    cy.wait('@createProject')
    
    // 验证加载状态消失
    cy.get('.project-registration-loader').should('not.exist')
  })

  it('应该显示正确的步骤进度', () => {
    // 填写表单
    cy.get('input[id="name"]').type('测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/repo.git')
    
    // 模拟分步骤的API响应
    let stepIndex = 0
    cy.intercept('POST', '/api/projects', (req) => {
      // 模拟不同步骤的延迟
      const delays = [1000, 2000, 1500, 1000]
      req.reply({
        delay: delays[stepIndex] || 1000,
        statusCode: 200,
        body: {
          id: 1,
          name: '测试项目',
          repositoryUrl: 'https://github.com/test/repo.git',
          branch: 'main'
        }
      })
      stepIndex++
    }).as('createProject')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证步骤指示器
    cy.get('.steps-indicator .step-item').should('have.length', 4)
    
    // 验证第一步是激活状态
    cy.get('.steps-indicator .step-item').first().should('have.class', 'active')
    cy.get('.steps-indicator .step-item').first().find('.fa-spinner').should('exist')
    
    // 验证进度条存在
    cy.get('.progress-bar .progress-fill').should('exist')
    
    cy.wait('@createProject')
  })

  it('应该支持取消操作', () => {
    // 填写表单
    cy.get('input[id="name"]').type('测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/repo.git')
    
    // 模拟长时间的API响应
    cy.intercept('POST', '/api/projects', {
      delay: 10000,
      statusCode: 200,
      body: { id: 1 }
    }).as('createProject')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证加载状态显示
    cy.get('.project-registration-loader').should('be.visible')
    
    // 点击取消按钮
    cy.get('.cancel-button').click()
    
    // 验证加载状态消失
    cy.get('.project-registration-loader').should('not.exist')
    
    // 验证表单仍然可用
    cy.get('button[type="submit"]').should('not.be.disabled')
  })

  it('应该处理注册失败的情况', () => {
    // 填写表单
    cy.get('input[id="name"]').type('测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/repo.git')
    
    // 模拟API错误响应
    cy.intercept('POST', '/api/projects', {
      statusCode: 400,
      body: {
        success: false,
        message: '该仓库地址已经被注册'
      }
    }).as('createProjectError')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 等待API响应
    cy.wait('@createProjectError')
    
    // 验证错误消息显示
    cy.get('.bg-red-50').should('be.visible')
    cy.get('.text-red-700').should('contain', '该仓库地址已经被注册')
    
    // 验证表单恢复可用状态
    cy.get('button[type="submit"]').should('not.be.disabled')
    cy.get('.project-registration-loader').should('not.exist')
  })

  it('应该在超时时显示警告', () => {
    // 填写表单
    cy.get('input[id="name"]').type('测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/repo.git')
    
    // 模拟非常长的API响应（超过超时时间）
    cy.intercept('POST', '/api/projects', {
      delay: 35000, // 超过30秒超时
      statusCode: 200,
      body: { id: 1 }
    }).as('createProject')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证加载状态显示
    cy.get('.project-registration-loader').should('be.visible')
    
    // 等待超时警告出现（应该在20秒后出现）
    cy.get('.timeout-warning', { timeout: 25000 }).should('be.visible')
    cy.get('.timeout-warning').should('contain', '操作时间较长，请耐心等待')
    
    // 验证超时后的处理
    cy.get('.project-registration-loader', { timeout: 35000 }).should('not.exist')
  })

  it('应该正确处理仓库URL验证', () => {
    const testCases = [
      {
        url: 'https://github.com/user/repo.git',
        shouldBeValid: true,
        description: '有效的GitHub URL'
      },
      {
        url: 'https://gitlab.com/user/repo.git',
        shouldBeValid: true,
        description: '有效的GitLab URL'
      },
      {
        url: 'invalid-url',
        shouldBeValid: false,
        description: '无效的URL格式'
      },
      {
        url: 'https://github.com/user/repo',
        shouldBeValid: false,
        description: '缺少.git后缀的URL'
      }
    ]
    
    testCases.forEach(({ url, shouldBeValid, description }) => {
      cy.log(`测试: ${description}`)
      
      // 清空并输入URL
      cy.get('input[id="repo_url"]').clear().type(url)
      cy.get('input[id="name"]').clear().type('测试项目')
      
      // 触发验证
      cy.get('input[id="repo_url"]').blur()
      
      if (shouldBeValid) {
        cy.get('button[type="submit"]').should('not.be.disabled')
        cy.get('.border-green-500').should('exist')
      } else {
        cy.get('button[type="submit"]').should('be.disabled')
        cy.get('.border-red-500').should('exist')
      }
    })
  })

  it('应该在加载期间禁用所有交互', () => {
    // 填写表单
    cy.get('input[id="name"]').type('测试项目')
    cy.get('input[id="repo_url"]').type('https://github.com/test/repo.git')
    
    // 模拟API响应延迟
    cy.intercept('POST', '/api/projects', {
      delay: 3000,
      statusCode: 200,
      body: { id: 1 }
    }).as('createProject')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证表单元素被禁用
    cy.get('button[type="submit"]').should('be.disabled')
    cy.get('a[href="/projects"]').should('have.class', 'pointer-events-none')
    
    // 验证加载状态覆盖整个页面
    cy.get('.loader-overlay').should('be.visible')
    
    cy.wait('@createProject')
  })
})

/// <reference types="cypress" />

describe('任务管理页面 E2E 测试', () => {
  beforeEach(() => {
    // 在每个测试前访问任务列表页面
    cy.visitTaskList()
  })

  describe('页面基础功能', () => {
    it('应该正确加载任务列表页面', () => {
      // 检查页面标题
      cy.get('h1').should('contain.text', '任务队列')
      
      // 检查刷新按钮存在
      cy.get('[data-cy="refresh-button"]').should('be.visible')
      
      // 检查搜索框存在
      cy.get('[data-cy="search-input"]').should('be.visible')
      
      // 检查状态筛选下拉框存在
      cy.get('[data-cy="status-filter"]').should('be.visible')
      
      // 检查任务表格存在
      cy.checkTaskTableExists()
    })

    it('应该显示正确的表格列标题', () => {
      const expectedHeaders = [
        '任务ID',
        '关联项目', 
        '任务类型',
        '状态',
        '开始时间',
        '结束时间',
        '详情'
      ]
      
      expectedHeaders.forEach((header) => {
        cy.get('[data-cy="task-table"] thead').should('contain.text', header)
      })
    })

    it('应该显示任务数据', () => {
      // 检查是否有任务行
      cy.get('[data-cy="task-row"]').should('have.length.greaterThan', 0)
      
      // 检查第一行任务数据
      cy.get('[data-cy="task-row"]').first().within(() => {
        cy.get('[data-cy="task-id"]').should('be.visible')
        cy.get('[data-cy="task-project"]').should('be.visible')
        cy.get('[data-cy="task-type"]').should('be.visible')
        cy.get('[data-cy="task-status"]').should('be.visible')
        cy.get('[data-cy="task-start-time"]').should('be.visible')
        cy.get('[data-cy="task-detail-button"]').should('be.visible')
      })
    })
  })

  describe('搜索功能', () => {
    beforeEach(() => {
      cy.fixture('tasks').as('tasksData')
    })

    it('应该能够按项目名称搜索任务', function() {
      const testCase = this.tasksData.searchTestCases.find(
        (tc: any) => tc.description === '搜索项目名称'
      )
      
      cy.searchTasks(testCase.searchTerm)
      
      // 验证搜索结果
      cy.get('[data-cy="task-row"]').should('have.length', testCase.expectedResults)
      
      if (testCase.expectedResults > 0) {
        cy.get('[data-cy="task-row"]').first()
          .get('[data-cy="task-project"]')
          .should('contain.text', testCase.searchTerm)
      }
    })

    it('应该能够按任务名称搜索任务', function() {
      const testCase = this.tasksData.searchTestCases.find(
        (tc: any) => tc.description === '搜索任务名称'
      )
      
      cy.searchTasks(testCase.searchTerm)
      
      // 验证搜索结果
      cy.get('[data-cy="task-row"]').should('have.length', testCase.expectedResults)
    })

    it('搜索不存在的内容应该显示空结果', function() {
      const testCase = this.tasksData.searchTestCases.find(
        (tc: any) => tc.description === '搜索不存在的内容'
      )
      
      cy.searchTasks(testCase.searchTerm)
      
      // 验证无搜索结果
      cy.get('[data-cy="task-row"]').should('have.length', testCase.expectedResults)
    })

    it('清空搜索框应该显示所有任务', () => {
      // 先搜索
      cy.searchTasks('Payment')
      cy.get('[data-cy="task-row"]').should('have.length.lessThan', 4)
      
      // 清空搜索
      cy.get('[data-cy="search-input"]').clear()
      cy.wait(500)
      
      // 验证显示所有任务
      cy.get('[data-cy="task-row"]').should('have.length.greaterThan', 1)
    })
  })

  describe('状态筛选功能', () => {
    beforeEach(() => {
      cy.fixture('tasks').as('tasksData')
    })

    it('应该能够按状态筛选任务', function() {
      const testCases = this.tasksData.filterTestCases
      
      testCases.forEach((testCase: any) => {
        cy.filterTasksByStatus(testCase.status)
        
        // 验证筛选结果
        if (testCase.status === 'all') {
          cy.get('[data-cy="task-row"]').should('have.length.greaterThan', 0)
        } else {
          // 检查筛选后的任务状态是否正确
          cy.get('[data-cy="task-row"]').each(($row) => {
            cy.wrap($row).find('[data-cy="task-status"]')
              .should('contain.text', this.getStatusText(testCase.status))
          })
        }
      })
    })

    it('筛选和搜索应该能够组合使用', () => {
      // 先筛选状态
      cy.filterTasksByStatus('processing')
      
      // 再搜索
      cy.searchTasks('文档')
      
      // 验证组合筛选结果
      cy.get('[data-cy="task-row"]').each(($row) => {
        cy.wrap($row).find('[data-cy="task-status"]').should('contain.text', '处理中')
        cy.wrap($row).should('contain.text', '文档')
      })
    })
  })

  describe('任务详情跳转', () => {
    it('点击任务行应该跳转到任务详情页面', () => {
      // 获取第一个任务的ID
      cy.get('[data-cy="task-row"]').first().within(() => {
        cy.get('[data-cy="task-id"]').invoke('text').then((taskId) => {
          // 点击任务行
          cy.get('[data-cy="task-row"]').first().click()
          
          // 验证跳转到详情页面
          cy.url().should('include', `/tasks/${taskId.trim()}`)
          cy.get('h1').should('contain.text', '任务详情')
        })
      })
    })

    it('点击详情按钮应该跳转到任务详情页面', () => {
      // 获取第一个任务的ID
      cy.get('[data-cy="task-row"]').first().within(() => {
        cy.get('[data-cy="task-id"]').invoke('text').then((taskId) => {
          // 点击详情按钮
          cy.get('[data-cy="task-detail-button"]').click()
          
          // 验证跳转到详情页面
          cy.url().should('include', `/tasks/${taskId.trim()}`)
          cy.get('h1').should('contain.text', '任务详情')
        })
      })
    })
  })

  describe('刷新功能', () => {
    it('点击刷新按钮应该重新加载任务数据', () => {
      // 记录初始任务数量
      cy.getTaskRowCount().then((initialCount) => {
        // 点击刷新按钮
        cy.get('[data-cy="refresh-button"]').click()
        
        // 等待刷新完成
        cy.wait(1000)
        
        // 验证任务仍然存在（数据没有丢失）
        cy.get('[data-cy="task-row"]').should('have.length', initialCount)
      })
    })
  })

  describe('分页信息', () => {
    it('应该显示正确的分页信息', () => {
      cy.get('[data-cy="pagination-info"]').should('be.visible')
      cy.get('[data-cy="pagination-info"]').should('contain.text', '显示')
      cy.get('[data-cy="pagination-info"]').should('contain.text', '个任务')
    })

    it('分页按钮应该存在', () => {
      cy.get('[data-cy="pagination-prev"]').should('be.visible')
      cy.get('[data-cy="pagination-next"]').should('be.visible')
      cy.get('[data-cy="pagination-current"]').should('be.visible')
    })
  })

  describe('响应式设计', () => {
    it('在移动设备上应该正确显示', () => {
      cy.viewport('iphone-6')
      
      // 检查页面元素在移动设备上的显示
      cy.get('h1').should('be.visible')
      cy.get('[data-cy="search-input"]').should('be.visible')
      cy.get('[data-cy="status-filter"]').should('be.visible')
      cy.get('[data-cy="task-table"]').should('be.visible')
    })

    it('在平板设备上应该正确显示', () => {
      cy.viewport('ipad-2')
      
      // 检查页面元素在平板设备上的显示
      cy.get('h1').should('be.visible')
      cy.get('[data-cy="search-input"]').should('be.visible')
      cy.get('[data-cy="status-filter"]').should('be.visible')
      cy.get('[data-cy="task-table"]').should('be.visible')
    })
  })

  describe('错误处理', () => {
    it('网络错误时应该显示错误信息', () => {
      // 模拟网络错误
      cy.intercept('GET', '/api/tasks*', { forceNetworkError: true }).as('networkError')
      
      cy.reload()
      
      // 验证错误处理
      cy.get('[data-cy="error-message"]').should('be.visible')
    })
  })
})

// 辅助函数
function getStatusText(status: string): string {
  const statusMap: { [key: string]: string } = {
    processing: '处理中',
    success: '成功',
    waiting: '等待中',
    failed: '失败'
  }
  return statusMap[status] || status
}

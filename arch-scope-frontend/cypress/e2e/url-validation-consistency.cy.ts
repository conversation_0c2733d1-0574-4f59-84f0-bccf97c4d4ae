describe('URL验证一致性测试', () => {
  beforeEach(() => {
    cy.visit('/projects/new')
  })

  const testCases = [
    {
      url: 'http://gitlab.yeepay.com/yop/yop-center',
      description: '问题URL - HTTP协议不带.git后缀',
      shouldBeValid: true,
      shouldShowWarning: true,
      expectedProject: 'yop-center'
    },
    {
      url: 'http://gitlab.yeepay.com/yop/yop-center.git',
      description: 'HTTP协议带.git后缀',
      shouldBeValid: true,
      shouldShowWarning: true,
      expectedProject: 'yop-center'
    },
    {
      url: 'https://gitlab.yeepay.com/yop/yop-center',
      description: 'HTTPS协议不带.git后缀',
      shouldBeValid: true,
      shouldShowWarning: false,
      expectedProject: 'yop-center'
    },
    {
      url: 'https://gitlab.yeepay.com/yop/yop-center.git',
      description: 'HTTPS协议带.git后缀',
      shouldBeValid: true,
      shouldShowWarning: false,
      expectedProject: 'yop-center'
    },
    {
      url: 'https://github.com/user/repo',
      description: 'GitHub URL不带.git后缀',
      shouldBeValid: true,
      shouldShowWarning: false,
      expectedProject: 'repo'
    },
    {
      url: '*********************:yop/yop-center.git',
      description: 'SSH格式URL',
      shouldBeValid: true,
      shouldShowWarning: false,
      expectedProject: 'yop-center'
    },
    {
      url: 'invalid-url',
      description: '无效URL格式',
      shouldBeValid: false,
      shouldShowWarning: false,
      expectedProject: null
    }
  ]

  testCases.forEach(({ url, description, shouldBeValid, shouldShowWarning, expectedProject }) => {
    it(`应该正确验证: ${description}`, () => {
      cy.log(`测试URL: ${url}`)
      
      // 输入URL
      cy.get('input[id="repo_url"]').clear().type(url)
      
      // 触发验证
      cy.get('input[id="repo_url"]').blur()
      
      // 等待验证完成
      cy.wait(1000)
      
      if (shouldBeValid) {
        // 验证URL应该被接受
        cy.get('input[id="repo_url"]').should('have.class', 'border-green-500')
        
        if (shouldShowWarning) {
          // 应该显示HTTP协议警告
          cy.get('.text-green-600').should('contain', '建议使用HTTPS协议')
        }
        
        if (expectedProject) {
          // 应该自动填充项目名称
          cy.get('input[id="name"]').should('have.value', expectedProject)
        }
        
        // 提交按钮应该可用（如果项目名称已填充）
        if (expectedProject) {
          cy.get('button[type="submit"]').should('not.be.disabled')
        }
      } else {
        // 验证URL应该被拒绝
        cy.get('input[id="repo_url"]').should('have.class', 'border-red-500')
        cy.get('.text-red-600').should('be.visible')
        
        // 提交按钮应该被禁用
        cy.get('button[type="submit"]').should('be.disabled')
      }
    })
  })

  it('应该在输入和提交阶段使用一致的验证逻辑', () => {
    const problemUrl = 'http://gitlab.yeepay.com/yop/yop-center'
    
    // 输入问题URL
    cy.get('input[id="repo_url"]').type(problemUrl)
    cy.get('input[id="name"]').type('测试项目')
    
    // 触发验证
    cy.get('input[id="repo_url"]').blur()
    cy.wait(1000)
    
    // 验证URL应该被接受（输入阶段）
    cy.get('input[id="repo_url"]').should('have.class', 'border-green-500')
    cy.get('button[type="submit"]').should('not.be.disabled')
    
    // 模拟成功的API响应
    cy.intercept('POST', '/api/projects', {
      statusCode: 200,
      body: {
        id: 1,
        name: '测试项目',
        repositoryUrl: problemUrl,
        branch: 'main'
      }
    }).as('createProject')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证加载状态显示
    cy.get('.project-registration-loader').should('be.visible')
    
    // 等待API调用
    cy.wait('@createProject')
    
    // 验证没有出现"仓库URL格式不正确"错误
    cy.get('.bg-red-50').should('not.exist')
    
    // 验证成功跳转（模拟）
    cy.url().should('include', '/projects')
  })

  it('应该正确处理URL格式转换建议', () => {
    const sshUrl = '*********************:yop/yop-center.git'
    
    // 模拟后端返回HTTPS URL建议
    cy.intercept('GET', '/git-repository/details*', {
      statusCode: 400,
      body: {
        success: false,
        errorMessage: 'SSH连接失败。建议尝试使用HTTPS URL: https://gitlab.yeepay.com/yop/yop-center.git'
      }
    }).as('getRepoDetails')
    
    // 输入SSH URL
    cy.get('input[id="repo_url"]').type(sshUrl)
    cy.get('input[id="repo_url"]').blur()
    
    // 等待API调用
    cy.wait('@getRepoDetails')
    
    // 验证显示HTTPS URL建议
    cy.get('.bg-blue-50').should('be.visible')
    cy.get('.bg-blue-50').should('contain', '建议使用HTTPS URL')
    cy.get('.bg-blue-50').should('contain', 'https://gitlab.yeepay.com/yop/yop-center.git')
    
    // 点击使用建议URL
    cy.get('.bg-blue-50 button').contains('使用此URL').click()
    
    // 验证URL已更新
    cy.get('input[id="repo_url"]').should('have.value', 'https://gitlab.yeepay.com/yop/yop-center.git')
  })

  it('应该正确处理不同协议的URL', () => {
    const urls = [
      { url: 'http://gitlab.yeepay.com/yop/yop-center', protocol: 'HTTP' },
      { url: 'https://gitlab.yeepay.com/yop/yop-center', protocol: 'HTTPS' }
    ]
    
    urls.forEach(({ url, protocol }) => {
      cy.log(`测试${protocol}协议`)
      
      // 清空并输入URL
      cy.get('input[id="repo_url"]').clear().type(url)
      cy.get('input[id="name"]').clear().type(`${protocol}测试项目`)
      
      // 触发验证
      cy.get('input[id="repo_url"]').blur()
      cy.wait(1000)
      
      // 验证都应该被接受
      cy.get('input[id="repo_url"]').should('have.class', 'border-green-500')
      cy.get('button[type="submit"]').should('not.be.disabled')
      
      if (protocol === 'HTTP') {
        // HTTP应该显示建议信息
        cy.get('.text-green-600').should('contain', '建议使用HTTPS协议')
      }
    })
  })

  it('应该在表单验证中正确处理URL', () => {
    const testUrl = 'http://gitlab.yeepay.com/yop/yop-center'
    
    // 只输入URL，不输入项目名称
    cy.get('input[id="repo_url"]').type(testUrl)
    cy.get('input[id="repo_url"]').blur()
    cy.wait(1000)
    
    // URL验证应该通过
    cy.get('input[id="repo_url"]').should('have.class', 'border-green-500')
    
    // 但提交按钮应该被禁用（因为缺少项目名称）
    cy.get('button[type="submit"]').should('be.disabled')
    
    // 输入项目名称
    cy.get('input[id="name"]').type('测试项目')
    
    // 现在提交按钮应该可用
    cy.get('button[type="submit"]').should('not.be.disabled')
  })
})

describe('项目唯一性验证', () => {
  beforeEach(() => {
    // 访问项目注册页面
    cy.visit('/projects/register')
    
    // 等待页面加载完成
    cy.get('h1').should('contain', '注册新项目')
  })

  it('应该在第一步就检测到重复的仓库URL并提示用户', () => {
    const duplicateUrl = 'https://github.com/existing/project.git'
    
    // 模拟检查仓库存在的API响应
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: true,
        message: '该仓库已被项目"现有项目"使用',
        existingProject: {
          id: 1,
          name: '现有项目',
          description: '这是一个已存在的项目',
          createdAt: '2024-01-01T00:00:00Z',
          status: 'ACTIVE'
        }
      }
    }).as('checkRepository')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(duplicateUrl)
    cy.get('input[id="name"]').type('测试项目')
    cy.get('textarea[id="description"]').type('这是一个测试项目')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证加载器显示
    cy.get('.project-registration-loader').should('be.visible')
    
    // 验证第一步开始执行
    cy.get('.step-item.active').should('contain', '验证仓库')
    cy.get('.status-title').should('contain', '正在验证Git仓库')
    cy.get('.status-description').should('contain', '检查仓库地址有效性和项目唯一性')
    
    // 等待API调用
    cy.wait('@checkRepository')
    
    // 验证错误提示显示
    cy.get('.step-item').first().should('have.class', 'step-item')
    cy.get('.status-title').should('contain', '验证失败')
    
    // 验证错误消息
    cy.contains('该仓库已被项目"现有项目"使用').should('be.visible')
    
    // 验证后续步骤没有执行
    cy.get('.step-item').eq(1).should('not.have.class', 'active')
    cy.get('.step-item').eq(2).should('not.have.class', 'active')
    cy.get('.step-item').eq(3).should('not.have.class', 'active')
  })

  it('应该在仓库不存在时继续正常的注册流程', () => {
    const newUrl = 'https://github.com/new/project.git'
    
    // 模拟检查仓库不存在的API响应
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        message: '仓库可以使用'
      }
    }).as('checkRepository')
    
    // 模拟获取仓库详情的API响应
    cy.intercept('GET', '/git-repository/details*', {
      statusCode: 200,
      body: {
        success: true,
        repositoryName: 'project',
        ownerName: 'new',
        branches: ['main', 'develop'],
        defaultBranch: 'main'
      }
    }).as('getRepoDetails')
    
    // 模拟创建项目的API响应
    cy.intercept('POST', '/api/projects', {
      statusCode: 200,
      body: {
        id: 1,
        name: '测试项目',
        repositoryUrl: newUrl,
        branch: 'main'
      }
    }).as('createProject')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(newUrl)
    cy.get('input[id="name"]').type('测试项目')
    cy.get('textarea[id="description"]').type('这是一个测试项目')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证加载器显示
    cy.get('.project-registration-loader').should('be.visible')
    
    // 验证第一步完成
    cy.wait('@checkRepository')
    cy.get('.step-item').first().should('have.class', 'completed')
    
    // 验证第二步开始
    cy.get('.step-item').eq(1).should('have.class', 'active')
    cy.wait('@getRepoDetails')
    
    // 验证第二步完成
    cy.get('.step-item').eq(1).should('have.class', 'completed')
    
    // 验证第三步开始
    cy.get('.step-item').eq(2).should('have.class', 'active')
    
    // 验证第四步开始
    cy.get('.step-item').eq(3).should('have.class', 'active')
    cy.wait('@createProject')
    
    // 验证成功跳转
    cy.url().should('include', '/projects')
  })

  it('应该在网络错误时给出友好提示但不阻止流程', () => {
    const testUrl = 'https://github.com/test/project.git'
    
    // 模拟网络错误
    cy.intercept('GET', '/api/projects/check-repository*', {
      statusCode: 200,
      body: {
        exists: false,
        error: true,
        message: '检查仓库时发生错误，请稍后重试'
      }
    }).as('checkRepositoryError')
    
    // 模拟其他API正常响应
    cy.intercept('GET', '/git-repository/details*', {
      statusCode: 200,
      body: {
        success: true,
        repositoryName: 'project',
        ownerName: 'test',
        branches: ['main'],
        defaultBranch: 'main'
      }
    }).as('getRepoDetails')
    
    cy.intercept('POST', '/api/projects', {
      statusCode: 200,
      body: {
        id: 1,
        name: '测试项目',
        repositoryUrl: testUrl,
        branch: 'main'
      }
    }).as('createProject')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(testUrl)
    cy.get('input[id="name"]').type('测试项目')
    cy.get('textarea[id="description"]').type('这是一个测试项目')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证加载器显示
    cy.get('.project-registration-loader').should('be.visible')
    
    // 等待唯一性检查API调用（会失败）
    cy.wait('@checkRepositoryError')
    
    // 验证流程继续进行（不被网络错误阻止）
    cy.get('.step-item').first().should('have.class', 'completed')
    cy.get('.step-item').eq(1).should('have.class', 'active')
    
    // 验证后续步骤正常执行
    cy.wait('@getRepoDetails')
    cy.wait('@createProject')
    
    // 验证成功跳转
    cy.url().should('include', '/projects')
  })

  it('应该正确处理取消操作', () => {
    const testUrl = 'https://github.com/slow/project.git'
    
    // 模拟慢速的唯一性检查
    cy.intercept('GET', '/api/projects/check-repository*', (req) => {
      req.reply((res) => {
        // 延迟2秒响应
        setTimeout(() => {
          res.send({ statusCode: 200, body: false })
        }, 2000)
      })
    }).as('checkRepositorySlow')
    
    // 填写表单
    cy.get('input[id="repo_url"]').type(testUrl)
    cy.get('input[id="name"]').type('测试项目')
    
    // 提交表单
    cy.get('button[type="submit"]').click()
    
    // 验证加载器显示
    cy.get('.project-registration-loader').should('be.visible')
    
    // 在第一步执行过程中点击取消
    cy.get('.cancel-button').should('be.visible').click()
    
    // 验证加载器消失
    cy.get('.project-registration-loader').should('not.exist')
    
    // 验证回到表单页面
    cy.get('h1').should('contain', '注册新项目')
    cy.get('input[id="repo_url"]').should('have.value', testUrl)
  })
})

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Git工具类测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 3px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        input {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        button {
            padding: 8px 16px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Git工具类测试页面</h1>
    
    <div class="test-section">
        <h2>URL验证测试</h2>
        <input type="text" id="urlInput" placeholder="输入Git仓库URL" value="https://github.com/user/repo">
        <button onclick="testUrlValidation()">验证URL</button>
        <div id="urlResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>项目名称提取测试</h2>
        <input type="text" id="nameInput" placeholder="输入Git仓库URL" value="https://github.com/facebook/react">
        <button onclick="testNameExtraction()">提取项目名称</button>
        <div id="nameResult" class="result"></div>
    </div>

    <div class="test-section">
        <h2>仓库类型判断测试</h2>
        <input type="text" id="typeInput" placeholder="输入Git仓库URL" value="https://gitlab.com/user/project">
        <button onclick="testRepositoryType()">判断仓库类型</button>
        <div id="typeResult" class="result"></div>
    </div>

    <script type="module">
        // 导入Git工具函数
        import { 
            isValidGitUrl, 
            extractProjectNameFromUrl, 
            extractOwnerFromUrl,
            determineRepositoryType,
            validateGitUrlWithMessage,
            extractBasicRepoInfo
        } from './src/utils/gitUtils.js';

        // 将函数挂载到全局对象，以便HTML中的onclick可以访问
        window.gitUtils = {
            isValidGitUrl,
            extractProjectNameFromUrl,
            extractOwnerFromUrl,
            determineRepositoryType,
            validateGitUrlWithMessage,
            extractBasicRepoInfo
        };

        window.testUrlValidation = function() {
            const url = document.getElementById('urlInput').value;
            const result = window.gitUtils.validateGitUrlWithMessage(url);
            const resultDiv = document.getElementById('urlResult');
            
            if (result.isValid) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `✅ URL有效: ${url}`;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ URL无效: ${result.message}`;
            }
        };

        window.testNameExtraction = function() {
            const url = document.getElementById('nameInput').value;
            const projectName = window.gitUtils.extractProjectNameFromUrl(url);
            const owner = window.gitUtils.extractOwnerFromUrl(url);
            const resultDiv = document.getElementById('nameResult');
            
            resultDiv.className = 'result success';
            resultDiv.innerHTML = `
                <strong>项目名称:</strong> ${projectName}<br>
                <strong>所有者:</strong> ${owner}
            `;
        };

        window.testRepositoryType = function() {
            const url = document.getElementById('typeInput').value;
            const repoType = window.gitUtils.determineRepositoryType(url);
            const basicInfo = window.gitUtils.extractBasicRepoInfo(url);
            const resultDiv = document.getElementById('typeResult');
            
            if (basicInfo.success) {
                resultDiv.className = 'result success';
                resultDiv.innerHTML = `
                    <strong>仓库类型:</strong> ${repoType}<br>
                    <strong>基本信息:</strong><br>
                    - 项目名称: ${basicInfo.projectName}<br>
                    - 所有者: ${basicInfo.owner}<br>
                    - 仓库名称: ${basicInfo.repositoryName}
                `;
            } else {
                resultDiv.className = 'result error';
                resultDiv.innerHTML = `❌ 解析失败: ${basicInfo.errorMessage}`;
            }
        };

        // 页面加载完成后运行一些自动测试
        window.addEventListener('load', function() {
            console.log('Git工具类测试页面已加载');
            
            // 自动测试一些常见的URL
            const testUrls = [
                'https://github.com/facebook/react',
                'https://gitlab.com/gitlab-org/gitlab',
                '**************:microsoft/vscode.git',
                'https://bitbucket.org/atlassian/stash.git',
                'invalid-url'
            ];
            
            console.log('自动测试结果:');
            testUrls.forEach(url => {
                const validation = window.gitUtils.validateGitUrlWithMessage(url);
                const projectName = window.gitUtils.extractProjectNameFromUrl(url);
                const repoType = window.gitUtils.determineRepositoryType(url);
                
                console.log(`URL: ${url}`);
                console.log(`  有效: ${validation.isValid}`);
                console.log(`  项目名称: ${projectName}`);
                console.log(`  仓库类型: ${repoType}`);
                console.log('---');
            });
        });
    </script>
</body>
</html>

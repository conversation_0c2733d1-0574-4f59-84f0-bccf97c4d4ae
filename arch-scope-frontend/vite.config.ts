import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { VueMcp } from 'vite-plugin-vue-mcp'
import { compression } from 'vite-plugin-compression2'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    VueMcp(),
    // 启用Gzip压缩
    compression({
      algorithm: 'gzip',
      exclude: [/\.(br)$ /, /\.(gz)$/]
    }),
    // 启用Brotli压缩（更高压缩率）
    compression({
      algorithm: 'brotliCompress',
      exclude: [/\.(br)$ /, /\.(gz)$/]
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
          proxy.on('proxyRes', (proxyRes, req, res) => {
            console.log('Received Response from the Target:', proxyRes.statusCode, req.url);
          });
        }
      }
    }
  },
  // 优化依赖预构建
  optimizeDeps: {
    include: ['vue', 'vue-router', 'pinia', '@fortawesome/fontawesome-free'],
    exclude: ['@vite/client', '@vite/env']
  },
  build: {
    outDir: 'dist',
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true
      }
    },
    rollupOptions: {
      output: {
        // 手动分割代码块以优化加载性能
        manualChunks: {
          // Vue核心库
          'vue-vendor': ['vue', 'vue-router', 'pinia'],
          // UI库和工具
          'ui-vendor': ['@fortawesome/fontawesome-free'],
          // 项目相关组件
          'project-views': [
            'src/views/projects/ProjectList.vue',
            'src/views/projects/ProjectDetail.vue',
            'src/views/projects/RegisterProject.vue'
          ],
          // 文档相关组件
          'document-views': [
            'src/views/projects/DocumentView.vue'
          ],
          // 任务相关组件
          'task-views': [
            'src/views/tasks/TaskListView.vue',
            'src/views/tasks/TaskDetailView.vue'
          ]
        },
        // 优化文件名和缓存
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    // 启用gzip压缩
    reportCompressedSize: true,
    // 设置chunk大小警告限制
    chunkSizeWarningLimit: 1000
  }
}) 
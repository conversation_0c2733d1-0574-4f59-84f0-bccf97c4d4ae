# Compiled class file
*.class

# Log file
*.log

# BlueJ files
*.ctxt

# Mobile Tools for Java (J2ME)
.mtj.tmp/

# Package Files #
*.jar
*.war
*.nar
*.ear
*.zip
*.tar.gz
*.rar

# virtual machine crash logs, see http://www.java.com/en/download/help/error_hotspot.xml
hs_err_pid*
replay_pid*

/.idea/
target/

# Added by Claude Task Master
# Logs
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log

# Dependency directories
node_modules/

# Environment variables
.env

# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS specific
.DS_Store

.qodo
.kilocode
.roo
.ruru
.roomodes

# Added by Claude Task Master
# Task files
.tasksmaster/

qdrant_storage/
.opencode/
node_modules/

# Kiro 配置文件
.kiro/settings.json
.kiro/cache/
.kiro/logs/

# 但以下内容需要共享
!.kiro/steering/
!.kiro/hooks/
!.kiro/specs/

# Crush files
.crush/


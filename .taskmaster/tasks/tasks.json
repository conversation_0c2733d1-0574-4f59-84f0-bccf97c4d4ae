{"master": {"tasks": [{"id": 1, "title": "项目初始化与环境搭建", "description": "设置项目结构、配置开发环境、安装依赖", "priority": "high", "status": "done", "details": "1. 创建项目目录结构\n2. 配置前端开发环境 (Vue 3, TypeScript, Tailwind CSS)\n3. 配置后端开发环境 (Java, Spring Boot, Maven)\n4. 设置数据库 (MySQL)\n5. 配置缓存 (Redis)\n6. 设置消息队列 (RocketMQ)\n7. 配置版本控制和CI/CD", "testStrategy": "验证所有环境和依赖是否正确安装和配置", "dependencies": [], "subtasks": [{"id": "1", "title": "创建项目目录结构", "description": "设计并创建符合DDD架构的项目目录结构", "status": "done", "details": "1. 创建前端项目目录结构\n2. 创建后端项目目录结构\n3. 确保目录结构符合DDD架构原则\n4. 创建必要的配置文件"}, {"id": "2", "title": "配置前端开发环境", "description": "设置Vue 3, TypeScript, Tailwind CSS开发环境", "status": "done", "details": "1. 安装Node.js和npm\n2. 创建Vue 3项目\n3. 配置TypeScript\n4. 安装和配置Tailwind CSS\n5. 设置开发服务器"}, {"id": "3", "title": "配置后端开发环境", "description": "设置Java, Spring Boot, Maven开发环境", "status": "done", "details": "1. 安装JDK\n2. 安装Maven\n3. 创建Spring Boot项目\n4. 配置MyBatis Plus\n5. 设置开发服务器"}, {"id": "4", "title": "设置数据库和缓存", "description": "配置MySQL和Redis", "status": "done", "details": "1. 安装MySQL 8.0+\n2. 创建数据库和表\n3. 配置数据库连接\n4. 安装Redis\n5. 配置Redis连接"}, {"id": "5", "title": "配置消息队列和CI/CD", "description": "设置RocketMQ和CI/CD流程", "status": "done", "details": "1. 安装RocketMQ\n2. 配置消息队列\n3. 设置Git仓库\n4. 配置CI/CD流程\n5. 设置自动化测试"}]}, {"id": 2, "title": "Phase 1: 核心基础功能 (MVP)", "description": "实现系统的核心基础功能。采用架构分工模式：ArchScope负责任务管理和API提供，外部LLM团队负责代码解析和文档生成的具体实现。", "status": "done", "dependencies": [1], "priority": "high", "details": "【2025-06-21 架构分工更新】\n\n🏗️ 新架构模式：通过LLM服务集成API实现团队分工协作\n\n✅ ArchScope团队职责（已完成）：\n1. 任务管理中心：任务创建、调度、状态管理、超时处理\n2. API接口层：任务拉取API、结果交付API、状态查询API\n3. 文档存储管理：文档版本管理、存储、检索\n4. 前端界面：用户交互、项目管理、文档展示\n5. 基础设施：DDD架构、数据库、中间件、部署配置\n\n🔄 外部LLM团队职责：\n1. 代码解析引擎：AST解析、语义分析、结构提取\n2. 文档生成引擎：LLM调用、内容生成、格式化\n3. LLM工作节点：任务拉取、处理协调、结果交付\n4. 质量保证：解析准确性、文档质量、错误处理\n\n🔄 协作流程：\n1. 用户在ArchScope中注册项目并触发分析\n2. ArchScope创建任务并通过API提供给外部团队\n3. 外部团队拉取任务，进行代码解析和文档生成\n4. 外部团队通过API交付结果给ArchScope\n5. ArchScope存储结果并在前端展示给用户\n\n验收标准：\n✅ ArchScope任务管理API完整可用\n✅ 外部团队能够成功集成并处理任务\n✅ 端到端流程验证通过\n✅ 用户能够看到有价值的文档内容", "testStrategy": "验证所有MVP功能是否可用，包括手动流程测试和API测试，重点覆盖LLM服务调用的稳定性和端到端文档生成流程", "subtasks": [{"id": "1", "title": "设计并实现代码解析结果的数据模型", "description": "定义如何存储从代码中提取的架构和依赖信息（例如，类、方法、依赖关系、模块、分层等）。参考 arch-scope-domain/src/main/java/com/archscope/domain/model/parser/ 目录下的现有模型。", "details": "<info added on 2025-05-04T02:50:29.548Z>\n<update><timestamp>2025-05-04T02:50:25Z</timestamp><content>当前模型已覆盖代码结构核心要素，建议补充以下设计规范：\n1. **主键约束**：为所有实体类（ClassDefinition/MethodDefinition等）添加唯一标识符，采用UUID或复合主键（文件路径+类名+行号）\n2. **冗余控制**：在DependencyRelation中建立双向索引时，需通过@OneToMany注解明确关联关系，避免数据重复\n3. **分层解耦**：将ArchitecturalLayer与ModuleStructure设计为聚合根，通过层级ID实现松耦合\n4. **枚举扩展**：为LanguageType增加版本号字段，支持多语言版本解析\n5. **审计字段**：所有模型基类添加createdAt/modifiedAt时间戳，实现数据版本追踪\n6. **查询优化**：在Module和PackageStructure之间建立Materialized Path模式，加速层级查询</content></update>\n</info added on 2025-05-04T02:50:29.548Z>", "status": "done", "dependencies": []}, {"id": "2", "title": "构建代码解析引擎的基础结构", "description": "创建解析引擎的核心接口和类，定义输入（代码内容）和输出（结构化解析结果）的规范。参考 CodeParser.java 和 DefaultCodeParser.java。", "details": "<info added on 2025-05-04T02:52:51.056Z>\n<update><timestamp>2025-05-04T02:52:48Z</timestamp><content>当前实现包含分层架构设计：1. **核心解析层**（CodeParser接口）处理原始代码解析；2. **结构分析层**（ProjectStructureAnalyzer接口）实现模块化分析；3. **依赖分析层**通过包名/模块名模式识别架构层级。采用抽象接口与具体实现分离的设计模式，支持Java/JS/TS多语言解析，为LLM集成预留标准化数据输出接口。</content></update>\n</info added on 2025-05-04T02:52:51.056Z>", "status": "done", "dependencies": []}, {"id": "3", "title": "集成LLM服务调用逻辑", "description": "实现后端与外部LLM服务进行交互的代码，包括请求发送、响应处理和错误处理。这对应PRD中的LLM Service API。", "details": "【2025-06-21 更新 - 架构分工】\n\n✅ ArchScope团队职责（已完成）：\n- LLM服务集成API实现（任务拉取、交付、超时处理）\n- 任务管理和状态跟踪机制\n- API文档和接口规范\n- 错误处理和重试机制\n\n🔄 外部LLM团队职责：\n- 实现LLM工作节点\n- 具体的代码解析逻辑\n- LLM模型调用和响应处理\n- 根据API文档集成任务拉取和交付流程\n\n验收标准：\n✅ ArchScope API接口完整可用\n🔄 外部团队成功集成并能处理任务", "status": "done", "dependencies": [], "priority": "high", "estimatedHours": 16, "blockedReason": "", "assignee": "ArchScope团队(API) + 外部团队(实现)"}, {"id": "4", "title": "开发并管理基础代码结构分析提示词", "description": "设计用于提取代码基本结构信息（如类名、方法名、文件结构）的初始LLM提示词，并实现其管理机制。参考 prompts/ 目录结构。", "details": "<info added on 2025-05-04T03:03:21.319Z>\n<update><timestamp>2025-05-04T03:03:17Z</timestamp><content>已完成基础提示词框架搭建，包含通用分析模板(generic_code_analysis.yaml)和Java/JavaScript/Python语言特定模板。当前java_analysis.yaml中的class_structure提示词已实现类名、方法签名和继承关系的结构化提取，输出格式与ClassDefinition模型字段完全匹配。后续需实现基于文件扩展名的提示词选择器，并集成到LLM服务调用管道中，确保自动匹配语言特定模板。</content></update>\n</info added on 2025-05-04T03:03:21.319Z>", "status": "done", "dependencies": []}, {"id": "5", "title": "实现基于LLM和提示词的代码解析核心逻辑", "description": "编写代码，根据输入的代码内容和语言，选择合适的提示词，调用LLM服务，并将LLM返回的文本结果解析并转换为预定义的数据模型。", "details": "【2025-06-21 更新 - 架构分工】\n\n✅ ArchScope团队职责（已完成）：\n- 任务创建和分发机制\n- 结果接收和存储逻辑\n- 数据模型定义和验证\n- 任务状态管理和监控\n\n🔄 外部LLM团队职责：\n- 具体的代码解析算法实现\n- 提示词设计和优化\n- LLM模型调用和响应处理\n- 多语言代码解析支持\n- 解析结果格式化和验证\n\n协作模式：\n- 外部团队通过API拉取任务，获取代码内容和解析要求\n- 外部团队实现解析逻辑，生成符合ArchScope数据模型的结果\n- 通过API交付解析结果，ArchScope负责存储和后续处理\n\n验收标准：\n✅ 任务分发机制正常工作\n🔄 外部团队能够成功解析各种代码并返回有效结果", "status": "done", "dependencies": ["3"], "priority": "high", "estimatedHours": 12, "blockedReason": "", "assignee": "外部LLM团队(主要) + ArchScope团队(支持)"}, {"id": "6", "title": "扩展解析引擎以支持多种编程语言", "description": "修改解析逻辑，使其能够识别不同的编程语言，并根据语言选择或调整提示词及结果处理方式。", "details": "<info added on 2025-05-04T05:16:33.840Z>\n<update><timestamp>2025-05-04T05:16:30Z</timestamp><content>核心实现包含语言类型枚举扩展与动态提示词路由机制：1) LanguageType.java实现文件扩展名映射与语言特征检测；2) 提示词模板按语言家族分组优化，C/C++共享语义分析模板，动态语言使用通用分析策略；3) DefaultCodeParser采用双层fallback机制，优先使用LLM语义解析，失败时返回基础语法树结构。新增语言支持仅需扩展枚举并添加对应提示词模板。</content></update>\n</info added on 2025-05-04T05:16:33.840Z>", "status": "done", "dependencies": []}, {"id": "7", "title": "设计并实现增量解析机制", "description": "开发逻辑来识别代码仓库中的变更，并仅对变更部分进行解析，以提高效率。", "details": "<info added on 2025-05-04T05:28:54.977Z>\n<update><timestamp>2025-05-04T05:28:51Z</timestamp><content>增量解析机制实现包含四个核心组件：1) 增量解析服务通过比较提交差异识别变更文件，2) 扩展的仓库服务提供细粒度变更检测能力，3) 改进的解析器支持部分文件解析与结果合并，4) 基于缓存机制的增量存储体系。该架构通过文件级变更检测和智能缓存合并，将解析范围缩小至实际变更文件，同时保持全局解析结果的一致性。</content></update>\n</info added on 2025-05-04T05:28:54.977Z>", "status": "done", "dependencies": []}, {"id": "8", "title": "将代码解析功能集成到任务管理系统", "description": "连接解析引擎与后台任务队列，使得代码解析可以作为后台任务被触发、调度和监控。", "details": "<info added on 2025-05-04T12:38:27.686Z>\n<update><timestamp>2025-05-04T12:38:24Z</timestamp><content>系统集成架构包含四个核心组件：1) 任务队列服务层(TaskQueueService)实现基于优先级的任务调度算法，支持任务持久化(TaskRepository)和自动恢复机制；2) 执行引擎层通过TaskExecutorRegistry实现动态执行器注册，CodeParseTaskExecutor集成增量解析服务实现代码变更的智能增量处理；3) 监控层(TaskMonitor)实现指数退避重试策略和熔断机制，通过状态模式管理任务生命周期；4) REST接口层支持异步任务触发和实时状态查询，与代码仓库服务深度集成。系统采用命令模式封装解析任务，通过观察者模式实现状态变更通知，确保与后续高级代码分析模块的松耦合对接。</content></update>\n</info added on 2025-05-04T12:38:27.686Z>", "status": "done", "dependencies": []}, {"id": "9", "title": "开发并管理高级代码分析提示词", "description": "设计用于提取更深层次信息（如类/模块间依赖、架构分层、潜在问题）的高级LLM提示词。", "details": "<info added on 2025-05-04T12:44:30.401Z>\n开发完成三个核心提示词文件（advanced_dependency_analysis.yaml/architecture_layer_analysis.yaml/code_quality_analysis.yaml），每个文件包含五种分析维度：依赖关系分析（dependency_analysis）、架构层级检测（layer_detection）、代码质量评估（quality_assessment）、设计模式识别（design_pattern_detection）和安全漏洞扫描（security_analysis）。提示词设计采用YAML结构化格式，通过多轮思维链（chain-of-thought）提示引导LLM进行深度代码解析，输出包含模块依赖图、架构分层矩阵、代码异味清单等结构化数据。提示词版本已通过语义化版本控制（如v1.0.0-dependency-analysis）进行管理，并与后续的代码解析结果融合模块保持接口兼容性。\n</info added on 2025-05-04T12:44:30.401Z>", "status": "done", "dependencies": []}, {"id": "10", "title": "实现LLM解析结果与传统解析结果的结合（可选）", "description": "如果需要提高准确性或效率，实现将LLM解析结果与传统代码解析工具（如AST解析器）的结果进行合并或校验的逻辑。", "details": "<info added on 2025-05-05T03:17:08.381Z>\n<update><timestamp>2025-05-05T03:17:03Z</timestamp><content>实现方案包含多语言解析器注册机制与智能合并策略：1) 通过TraditionalParserRegistry支持Java/JS等多语言解析器动态注册；2) 开发SMART_MERGE策略，根据字段置信度自动选择LLM或传统解析结果；3) 在DefaultCodeParser中实现双引擎解析流程，包含异常回退机制；4) 通过TaskConfig实现策略热切换能力。</content></update>\n</info added on 2025-05-05T03:17:08.381Z>", "status": "done", "dependencies": []}, {"id": "11", "title": "编写代码解析的自动化测试", "description": "创建测试用例，验证解析引擎在不同语言、不同规模代码库上的准确性和性能。", "details": "<info added on 2025-05-05T03:27:25.719Z>\n<update><![CDATA[2024-05-30T00:00:00Z\n自动化测试体系包含6个核心测试模块：\n1. **传统解析器验证**：覆盖Java/JavaScript/TypeScript的语法树构建、符号解析和错误恢复机制\n2. **结果合并策略**：测试LLM优先/传统优先/智能合并三种模式在冲突场景下的决策逻辑\n3. **多语言支持矩阵**：验证7种编程语言的跨平台解析能力，包含边缘语法特征测试\n4. **性能基准测试**：建立小型(10文件)/中型(50文件)/大型(200文件)三级压力测试模型，监控内存泄漏和CPU占用\n5. **注册表功能测试**：验证动态加载机制和语言支持查询的实时性\n6. **混合解析验证**：测试LLM与传统解析器的协同工作流程，包含结果一致性校验和异常熔断机制]]></update>\n</info added on 2025-05-05T03:27:25.719Z>", "status": "done", "dependencies": []}, {"id": "12", "title": "【紧急修复】LLM服务依赖问题解决", "description": "解决AnthropicLlmService被禁用的根本问题，恢复LLM服务的正常功能", "details": "【2025-06-20 新增紧急任务】\n\n当前问题：\n- AnthropicLlmService返回'temporarily disabled due to dependency issues'\n- LlmServiceImpl返回虚假数据，核心功能未实现\n- 整个MVP的核心价值链断裂\n\n修复步骤：\n1. 检查并修复Anthropic客户端依赖配置\n2. 验证API密钥和环境变量配置\n3. 实现真实的LLM API调用逻辑\n4. 添加完善的错误处理和重试机制\n5. 创建端到端测试验证LLM服务可用性\n\n验收标准：\n- LLM服务能够成功调用Anthropic API\n- 返回真实的代码解析结果而非虚假数据\n- 错误处理机制完善，包含重试和降级策略\n- 通过集成测试验证端到端流程", "status": "done", "dependencies": [], "priority": "high", "estimatedHours": 20, "assignee": "backend-team", "dueDate": "2025-06-25"}, {"id": "13", "title": "【紧急修复】端到端流程验证与修复", "description": "验证并修复从项目注册到文档生成的完整用户流程", "details": "【2025-06-20 新增紧急任务】\n\n验证范围：\n1. 项目注册 → Git克隆 → 代码解析 → 文档生成 → 文档展示\n2. 任务队列的正确执行和状态管理\n3. 前端与后端的数据交互\n4. 错误处理和用户反馈机制\n\n修复重点：\n- 确保LLM解析结果能正确传递到文档生成\n- 验证任务状态在前端的正确显示\n- 修复可能存在的数据流断点\n- 改进用户体验和错误提示\n\n验收标准：\n- 用户能够成功注册项目并看到有意义的文档内容\n- 任务执行状态准确反映在前端界面\n- 错误情况下有清晰的用户提示\n- 端到端测试覆盖主要用户场景", "status": "done", "dependencies": ["12"], "priority": "critical", "estimatedHours": 16, "assignee": "full-stack-team", "dueDate": "2025-06-27"}, {"id": 14, "title": "【已完成】LLM服务集成API实现", "description": "实现完整的LLM服务集成API，包括任务拉取、任务交付、超时处理等工作流程，解决项目卡在LLM服务实现上的问题", "details": "【2025-06-21 新增完成任务】\n\n已完成的核心组件：\n1. **数据传输对象(DTOs)**：\n   - LlmTaskInputDto: 符合Schema v1.2规范的任务输入数据\n   - LlmTaskResponseDto: LLM服务返回的任务结果数据\n   - LlmTaskPullResponseDto: 任务拉取响应数据\n\n2. **核心服务层**：\n   - LlmTaskService接口和LlmTaskServiceImpl实现\n   - 任务创建、锁定、超时处理逻辑\n   - 任务状态管理和结果处理流程\n\n3. **REST API接口**：\n   - LlmTaskController提供完整的API端点\n   - GET /api/llm/tasks/pull - 任务拉取接口\n   - POST /api/llm/tasks/deliver - 任务交付接口\n   - POST /api/llm/tasks/create - 任务创建接口\n   - POST /api/llm/tasks/cleanup-timeout - 超时任务清理\n\n4. **项目分析集成**：\n   - ProjectAnalysisService项目分析服务\n   - ProjectController增强，新增分析相关API\n   - POST /api/projects/{id}/analyze - 触发项目分析\n   - POST /api/projects/{id}/reanalyze - 重新分析项目\n\n5. **定时任务和监控**：\n   - LlmTaskTimeoutScheduler自动处理超时任务\n   - 每5分钟检查超时任务，自动重置状态\n\n6. **完整文档**：\n   - 创建了详细的API文档(docs/llm-integration-api.md)\n   - 包含工作流程图、接口规范、示例代码\n\n解决的关键问题：\n- 项目进度阻塞：通过外部LLM服务集成，解除内部LLM实现依赖\n- 任务管理：实现完整的异步任务处理机制\n- 系统解耦：ArchScope专注任务管理，LLM服务专注代码分析\n- 可扩展性：支持多个LLM工作节点并行处理\n- 可靠性：超时保护和错误恢复机制\n\n验收标准：\n✅ LLM工作节点可以成功拉取任务\n✅ 任务数据符合Schema v1.2规范\n✅ 任务超时机制正常工作\n✅ 项目分析API可以创建LLM任务\n✅ 完整的API文档和示例代码", "status": "done", "dependencies": [], "parentTaskId": 2}, {"id": 15, "title": "【协作验收】外部LLM团队集成验证", "description": "验证外部LLM团队能够成功集成ArchScope的任务管理API，并完成端到端的代码解析和文档生成流程", "details": "【2025-06-21 新增协作任务】\n\n验收目标：\n1. **API集成验证**：\n   - 外部团队能够成功调用任务拉取API\n   - 能够正确解析任务数据格式（Schema v1.2）\n   - 能够成功提交任务结果\n\n2. **功能验证**：\n   - 能够克隆指定的Git仓库\n   - 能够解析Java代码并提取结构信息\n   - 能够生成符合要求的Markdown文档\n   - 能够处理错误情况并正确报告\n\n3. **质量验证**：\n   - 生成的文档内容有意义且准确\n   - 解析结果符合ArchScope的数据模型\n   - 处理时间在合理范围内（<30分钟）\n\n4. **稳定性验证**：\n   - 能够处理不同规模的项目\n   - 错误恢复机制正常工作\n   - 超时处理机制有效\n\n验收标准：\n- 外部团队提供可运行的LLM工作节点\n- 完成至少3个不同Java项目的端到端测试\n- 生成的文档质量达到用户可用标准\n- API调用成功率>95%\n- 平均处理时间<20分钟\n\n负责团队：\n- 主要：外部LLM团队（实现和测试）\n- 支持：ArchScope团队（API支持和验收）", "status": "done", "dependencies": ["14"], "priority": "critical", "estimatedHours": 40, "assignee": "外部LLM团队 + ArchScope团队", "dueDate": "2025-07-15", "parentTaskId": 2}]}, {"id": 3, "title": "Phase 2: 自动化工作流与基础文档站点", "description": "实现自动化工作流和基础文档站点", "status": "done", "dependencies": [2], "priority": "high", "blockedReason": "", "details": "按照PRD中Phase 2的定义实现系统的自动化工作流和基础文档站点：\n1. Git集成（克隆、获取变更）\n2. Git事件的Webhook接收器\n3. 解析和文档生成的任务队列实现\n4. 增量解析逻辑（已完成，详见下方）\n5. 基础文档版本控制（存储不同版本，已完成，详见下方）\n6. 文档的简单静态站点生成（将Markdown渲染为HTML）\n7. 基础文档搜索\n8. 前端：项目列表，带版本选择的基础文档视图\n\n【增量解析逻辑已完成，包含以下内容】\n- 创建了代码分块服务（CodeChunkingService）及实现类，支持将大型文件分割为可管理的块。\n- 针对Java、JavaScript和TypeScript实现了语言特定的分块策略。\n- 扩展了DefaultIncrementalParseService，增加了代码分块和上下文关联性处理。\n- 实现了变更文件对其他文件影响的分析，确保关联文件也能被重新解析。\n- 优化了解析结果合并逻辑，支持合并代码块的解析结果。\n- 编写了单元测试，验证代码分块和增量解析功能。\n这些改进提升了大型代码文件的处理效率，并确保了文件间依赖关系下增量解析的准确性。\n\n【基础文档版本控制已完成，包含以下内容】\n- 创建了 DocumentVersion 实体类，用于存储文档版本信息。\n- 实现了 DocumentVersionRepository 接口和 MyBatisDocumentVersionRepository 实现类，用于文档版本的存储和检索。\n- 实现了 DocumentVersionService 接口和 DefaultDocumentVersionService 实现类，提供文档版本的管理和操作功能。\n- 扩展了 DefaultDocumentGenerationService 类，支持在生成文档时创建文档版本记录。\n- 创建了 DocumentVersionController 类，提供文档版本的 REST API。\n- 创建了数据库表结构和 MyBatis 映射文件。\n- 编写了单元测试，验证文档版本服务的功能。\n系统现已能存储和管理不同版本的文档，支持版本比较、发布和取消发布等功能。", "testStrategy": "验证自动化工作流和基础文档站点功能是否可用，包括自动化测试和集成测试。增量解析部分已通过单元测试覆盖代码分块、依赖分析和结果合并等核心场景。基础文档版本控制已通过单元测试覆盖版本存储、检索、比较、发布与取消发布等核心功能。基础文档搜索已通过集成测试覆盖索引生成、内容检索、前端交互等核心场景，确保用户可在静态站点中顺利搜索和浏览文档内容。", "subtasks": [{"id": "1", "title": "Git集成（克隆、获取变更）", "description": "实现Git仓库的克隆和变更获取功能", "details": "", "status": "done", "dependencies": []}, {"id": "2", "title": "Git事件的Webhook接收器", "description": "实现用于接收Git事件（如push）的Webhook端点", "details": "", "status": "done", "dependencies": []}, {"id": "3", "title": "解析和文档生成的任务队列实现", "description": "实现一个任务队列来处理代码解析和文档生成任务", "details": "采用基于 RocketMQ 的消息队列", "status": "done", "dependencies": []}, {"id": "4", "title": "增量解析逻辑", "description": "实现只解析变更文件的增量解析逻辑，提高效率", "details": "已完成：\n- 创建了代码分块服务（CodeChunkingService）及实现类，支持将大型文件分割为可管理的块。\n- 针对Java、JavaScript和TypeScript实现了语言特定的分块策略。\n- 扩展了DefaultIncrementalParseService，增加了代码分块和上下文关联性处理。\n- 实现了变更文件对其他文件影响的分析，确保关联文件也能被重新解析。\n- 优化了解析结果合并逻辑，支持合并代码块的解析结果。\n- 编写了单元测试，验证代码分块和增量解析功能。\n系统现已能高效处理大型代码文件，并正确处理文件间依赖关系，确保增量解析的准确性。", "status": "done", "dependencies": []}, {"id": "5", "title": "基础文档版本控制（存储不同版本）", "description": "实现存储和管理不同版本文档的功能", "details": "已完成：\n- 创建了 DocumentVersion 实体类，用于存储文档版本信息。\n- 实现了 DocumentVersionRepository 接口和 MyBatisDocumentVersionRepository 实现类，用于文档版本的存储和检索。\n- 实现了 DocumentVersionService 接口和 DefaultDocumentVersionService 实现类，提供文档版本的管理和操作功能。\n- 扩展了 DefaultDocumentGenerationService 类，支持在生成文档时创建文档版本记录。\n- 创建了 DocumentVersionController 类，提供文档版本的 REST API。\n- 创建了数据库表结构和 MyBatis 映射文件。\n- 编写了单元测试，验证文档版本服务的功能。\n系统现已能存储和管理不同版本的文档，支持版本比较、发布和取消发布等功能。", "status": "done", "dependencies": []}, {"id": "6", "title": "文档的简单静态站点生成（将Markdown渲染为HTML）", "description": "实现将存储的Markdown文档渲染为HTML，并生成一个简单的静态站点", "details": "实现将存储的Markdown文档渲染为HTML，并生成一个简单的静态站点。可参考主流静态站点生成器（如MkDocs、Jekyll、<PERSON>等）实现Markdown到HTML的转换和静态资源组织[2][3][5]。", "status": "done", "dependencies": []}, {"id": "7", "title": "基础文档搜索", "description": "为生成的静态文档站点实现基础的搜索功能", "details": "已完成：\n1. 创建了DocumentSearchService接口和LunrDocumentSearchService实现类，用于生成搜索索引和提供搜索功能。\n2. 扩展了MarkdownService接口，添加了提取纯文本内容的方法。\n3. 修改了StaticSiteGenerationService接口，添加了生成搜索页面和搜索索引的方法。\n4. 实现了DefaultStaticSiteGenerationService类中的搜索相关方法。\n5. 创建了搜索页面的Thymeleaf模板。\n6. 添加了搜索相关的CSS和JavaScript文件。\n7. 集成了Lunr.js库用于客户端搜索。\n8. 更新了项目首页和文档页面模板，添加了搜索链接。\n\n搜索功能现在可以在静态站点中使用，用户可以通过搜索页面搜索文档内容，并查看匹配的结果。", "status": "done", "dependencies": []}, {"id": "8", "title": "前端：项目列表，带版本选择的基础文档视图", "description": "实现前端页面，展示项目列表，并提供带有版本选择功能的文档查看视图", "details": "实现前端页面，展示项目列表，并提供带有版本选择功能的文档查看视图，展示基础文档内容（可能包含由站点生成器初步渲染的图表或图表代码）。", "status": "done", "dependencies": []}]}, {"id": 4, "title": "Phase 3: 增强功能与用户体验", "description": "实现增强功能和改进用户体验", "priority": "medium", "status": "pending", "details": "按照PRD中Phase 3的定义实现系统的增强功能和改进用户体验：\n1. 项目成员管理与权限\n2. 高级解析（更深入的依赖分析，LLM微调/提示工程，集成传统AST解析器，初步代码安全分析集成）\n3. 文档模板定制\n4. 文档比较功能\n5. 基础健康度指标计算与显示（包括初步代码安全分析结果）\n6. 改进前端用户体验（更好的导航，主题定制）\n7. 任务管理UI（查看任务状态）\n8. 架构图生成与展示", "testStrategy": "验证增强功能和用户体验改进是否符合需求，包括用户体验测试和性能测试", "dependencies": [3], "subtasks": [{"id": "1", "title": "项目成员管理与权限", "description": "实现项目成员的添加、移除以及基于角色的权限控制功能", "details": "", "status": "pending", "dependencies": []}, {"id": "2", "title": "高级解析", "description": "实现更深入的代码分析，包括深度依赖分析、架构分层、设计模式识别、潜在问题检测。探索LLM微调或高级提示工程以提高解析质量，并集成传统AST解析器。初步集成代码安全分析。", "details": "实现更深入的代码分析，包括深度依赖分析、架构分层、设计模式识别、潜在问题检测。探索LLM微调或高级提示工程以提高解析质量，并集成传统AST解析器。初步集成代码安全分析功能。", "status": "pending", "dependencies": []}, {"id": "3", "title": "文档模板定制", "description": "允许用户自定义生成文档的模板", "details": "", "status": "pending", "dependencies": []}, {"id": "4", "title": "文档比较功能", "description": "实现比较不同文档版本之间差异的功能", "details": "", "status": "pending", "dependencies": []}, {"id": "5", "title": "基础健康度指标计算与显示", "description": "计算并显示项目的基本健康度指标（如代码覆盖率、依赖关系复杂度等），包括初步代码安全分析结果。实现项目星级评定算法。", "details": "计算并显示项目的基本健康度指标（如代码覆盖率、依赖关系复杂度等），包括初步代码安全分析结果。实现项目星级评定算法。", "status": "pending", "dependencies": []}, {"id": "6", "title": "改进前端用户体验（更好的导航，主题定制）", "description": "优化前端导航结构，并提供主题定制选项以改善用户体验", "details": "", "status": "pending", "dependencies": []}, {"id": "7", "title": "任务管理UI（查看任务状态）", "description": "实现一个前端界面，用于查看和管理任务状态", "details": "实现一个前端界面，用于查看和管理任务状态，与后台任务管理系统集成，展示任务进度、结果和错误信息。", "status": "pending", "dependencies": []}, {"id": "8", "title": "架构图生成与展示", "description": "基于代码解析结果，实现自动生成架构图（如PlantUML C4模型图）的逻辑，并在文档站点中展示。", "details": "基于代码解析结果，实现自动生成架构图（如PlantUML C4模型图）的逻辑，并在文档站点中展示。考虑集成如PlantUML或Mermaid等图表生成工具。", "status": "pending", "dependencies": []}]}, {"id": 5, "title": "Phase 4: 成熟与扩展", "description": "实现系统的成熟和扩展功能", "priority": "medium", "status": "pending", "details": "按照PRD中Phase 4的定义实现系统的成熟和扩展功能：\n1. 高级健康度指标与报告\n2. 可定制的健康规则\n3. 访问统计与分析\n4. Kubernetes部署支持\n5. 大规模性能优化\n6. 高级安全加固（完善LLM交互安全审计, 优化代码脱敏和PII过滤规则, 强化权限控制, 支持本地模型选项）\n7. 用于外部集成的API", "testStrategy": "验证系统的成熟度和扩展功能是否符合需求，包括性能测试、安全测试和可扩展性测试", "dependencies": [4], "subtasks": [{"id": "1", "title": "高级健康度指标与报告", "description": "实现更复杂的项目健康度指标计算和生成详细报告的功能。", "details": "实现更复杂的项目健康度指标计算和生成详细报告的功能。", "status": "pending", "dependencies": []}, {"id": "2", "title": "可定制的健康规则", "description": "允许用户定义和定制用于计算健康度指标的规则。", "details": "允许用户定义和定制用于计算健康度指标的规则。", "status": "pending", "dependencies": []}, {"id": "3", "title": "访问统计与分析", "description": "实现收集和分析用户访问文档和系统数据的功能，生成报表。", "details": "实现收集和分析用户访问文档和系统数据的功能，生成报表，洞察用户关注点，优化文档。", "status": "pending", "dependencies": []}, {"id": "4", "title": "Kubernetes部署支持", "description": "实现将应用部署到Kubernetes集群的支持。", "details": "", "status": "pending", "dependencies": []}, {"id": "5", "title": "大规模性能优化", "description": "对系统进行性能优化，以支持大规模用户和数据。", "details": "对系统进行性能优化，包括数据库、缓存、任务队列和解析引擎的优化，以支持大规模用户和数据。", "status": "pending", "dependencies": []}, {"id": "6", "title": "高级安全加固", "description": "对系统进行全面的安全加固，包括代码安全、基础设施安全、完善LLM交互安全审计、优化代码脱敏和PII过滤规则、强化权限控制和支持本地模型选项。", "details": "对系统进行全面的安全加固，包括代码安全、基础设施安全、完善LLM交互安全审计、优化代码脱敏和PII过滤规则、强化权限控制、支持本地模型部署选项以增强数据隐私。", "status": "pending", "dependencies": []}, {"id": "7", "title": "用于外部集成的API", "description": "开发一套稳定、安全的用于外部系统集成的API接口。", "details": "开发一套稳定、安全的用于外部系统集成的API接口，便于与其他开发工具或平台对接。", "status": "pending", "dependencies": []}, {"id": "8", "title": "探索RAG增强代码理解", "description": "研究和探索使用检索增强生成(RAG)技术，结合项目特定文档和代码片段，提升LLM在代码分析中的准确性和领域理解能力。", "details": "研究和探索使用检索增强生成(RAG)技术，结合项目特定文档和代码片段，提升LLM在代码分析中的准确性和领域理解能力。", "status": "pending", "dependencies": []}]}], "metadata": {"project": "ArchScope", "version": "1.0.0", "created": "2025-04-30 01:17:54", "description": "架构鹰眼 ArchScope 是一个面向开发者的架构观测和守护系统", "updated": "2025-06-21T10:30:00.000Z", "architectureMode": "分工协作模式", "archScopeTeam": "任务管理中心、API提供、文档存储、前端界面", "externalTeam": "代码解析实现、文档生成、LLM服务具体实现"}}}
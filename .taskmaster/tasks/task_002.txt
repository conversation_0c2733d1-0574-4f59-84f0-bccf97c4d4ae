# Task ID: 2
# Title: Phase 1: 核心基础功能 (MVP)
# Status: done
# Dependencies: 1
# Priority: high
# Description: 实现系统的核心基础功能
# Details:
按照PRD中Phase 1的定义实现系统的最小可行产品(MVP)：
1. 用户认证与基本项目注册（手动Git URL输入）
2. 后端API框架设置（Java/Spring Boot）
3. 基础前端框架设置（Vue/TS）
4. 代码解析的手动触发（简化的LLM调用，用于基础结构）
5. 基础Markdown文档生成的手动触发（结构概述）
6. 基础文档显示页面（无版本控制，无站点生成）
7. MySQL + Redis设置
8. 容器化部署设置（Docker Compose用于本地开发）

注：界面原型已在docs/prototype目录中提供，实现过程中应严格参考这些原型。

# Test Strategy:
验证所有MVP功能是否可用，包括手动流程测试和API测试

# Subtasks:
## 1. 设计并实现代码解析结果的数据模型 [done]
### Dependencies: None
### Description: 定义如何存储从代码中提取的架构和依赖信息（例如，类、方法、依赖关系、模块、分层等）。参考 arch-scope-domain/src/main/java/com/archscope/domain/model/parser/ 目录下的现有模型。
### Details:
<info added on 2025-05-04T02:50:29.548Z>
<update><timestamp>2025-05-04T02:50:25Z</timestamp><content>当前模型已覆盖代码结构核心要素，建议补充以下设计规范：
1. **主键约束**：为所有实体类（ClassDefinition/MethodDefinition等）添加唯一标识符，采用UUID或复合主键（文件路径+类名+行号）
2. **冗余控制**：在DependencyRelation中建立双向索引时，需通过@OneToMany注解明确关联关系，避免数据重复
3. **分层解耦**：将ArchitecturalLayer与ModuleStructure设计为聚合根，通过层级ID实现松耦合
4. **枚举扩展**：为LanguageType增加版本号字段，支持多语言版本解析
5. **审计字段**：所有模型基类添加createdAt/modifiedAt时间戳，实现数据版本追踪
6. **查询优化**：在Module和PackageStructure之间建立Materialized Path模式，加速层级查询</content></update>
</info added on 2025-05-04T02:50:29.548Z>

## 2. 构建代码解析引擎的基础结构 [done]
### Dependencies: None
### Description: 创建解析引擎的核心接口和类，定义输入（代码内容）和输出（结构化解析结果）的规范。参考 CodeParser.java 和 DefaultCodeParser.java。
### Details:
<info added on 2025-05-04T02:52:51.056Z>
<update><timestamp>2025-05-04T02:52:48Z</timestamp><content>当前实现包含分层架构设计：1. **核心解析层**（CodeParser接口）处理原始代码解析；2. **结构分析层**（ProjectStructureAnalyzer接口）实现模块化分析；3. **依赖分析层**通过包名/模块名模式识别架构层级。采用抽象接口与具体实现分离的设计模式，支持Java/JS/TS多语言解析，为LLM集成预留标准化数据输出接口。</content></update>
</info added on 2025-05-04T02:52:51.056Z>

## 3. 集成LLM服务调用逻辑 [done]
### Dependencies: None
### Description: 实现后端与外部LLM服务进行交互的代码，包括请求发送、响应处理和错误处理。这对应PRD中的LLM Service API。
### Details:
<info added on 2025-05-04T02:59:30.409Z>
<update><timestamp>2025-05-04T02:59:27Z</timestamp><content>已完成LLM服务接口框架搭建，需在Anthropic实现类中完成以下核心逻辑：1. 添加Anthropic客户端SDK依赖（如anthropic-java），配置API密钥与环境变量；2. 实现异步HTTP请求处理，包含请求体构建（消息格式转换、系统提示词注入）；3. 设计响应解析器，处理流式/非流式响应，提取content字段；4. 集成重试机制（指数退避）和错误处理（API限流、无效token、网络异常）；5. 添加请求日志与监控埋点。</content></update>
</info added on 2025-05-04T02:59:30.409Z>

## 4. 开发并管理基础代码结构分析提示词 [done]
### Dependencies: None
### Description: 设计用于提取代码基本结构信息（如类名、方法名、文件结构）的初始LLM提示词，并实现其管理机制。参考 prompts/ 目录结构。
### Details:
<info added on 2025-05-04T03:03:21.319Z>
<update><timestamp>2025-05-04T03:03:17Z</timestamp><content>已完成基础提示词框架搭建，包含通用分析模板(generic_code_analysis.yaml)和Java/JavaScript/Python语言特定模板。当前java_analysis.yaml中的class_structure提示词已实现类名、方法签名和继承关系的结构化提取，输出格式与ClassDefinition模型字段完全匹配。后续需实现基于文件扩展名的提示词选择器，并集成到LLM服务调用管道中，确保自动匹配语言特定模板。</content></update>
</info added on 2025-05-04T03:03:21.319Z>

## 5. 实现基于LLM和提示词的代码解析核心逻辑 [done]
### Dependencies: None
### Description: 编写代码，根据输入的代码内容和语言，选择合适的提示词，调用LLM服务，并将LLM返回的文本结果解析并转换为预定义的数据模型。
### Details:
<info added on 2025-05-04T04:28:36.805Z>
<update><timestamp>2025-05-04T04:28:33Z</timestamp><content>核心组件整合方案：1. 在LlmResponseParser中实现多格式JSON解析逻辑，包括容错机制和schema验证；2. 重构PromptManager的模板加载机制，增加语言类型条件判断；3. 在AnthropicLlmService中集成重试机制和速率限制；4. 为FileParseResult模型添加字段验证注解；5. 创建多语言测试套件（Java/Python/JavaScript示例）验证端到端流程</content></update>
</info added on 2025-05-04T04:28:36.805Z>
<info added on 2025-05-04T04:34:11.555Z>
<info added on 2025-05-04T04:28:36.805Z>
<update><timestamp>2025-05-04T04:28:33Z</timestamp><content>核心组件整合方案：1. 在LlmResponseParser中实现多格式JSON解析逻辑，包括容错机制和schema验证；2. 重构PromptManager的模板加载机制，增加语言类型条件判断；3. 在AnthropicLlmService中集成重试机制和速率限制；4. 为FileParseResult模型添加字段验证注解；5. 创建多语言测试套件（Java/Python/JavaScript示例）验证端到端流程</content></update>
</info added on 2025-05-04T04:28:36.805Z>
<update><timestamp>2025-05-04T04:34:06Z</timestamp><content>当前进展：完成LlmResponseParser多格式JSON解析能力建设，包含Markdown代码块提取、嵌套结构解析和错误恢复机制。待办事项：1. 设计结构化提示词模板确保LLM输出符合FileParseResult schema；2. 实现指数退避重试机制和令牌桶速率限制；3. 添加JSR-380验证注解支持必填字段和格式校验；4. 构建多语言测试矩阵（含异常响应测试用例）</content></update>
</info added on 2025-05-04T04:34:11.555Z>
<info added on 2025-05-04T05:06:33.355Z>
<info added on 2025-05-04T04:28:36.805Z>
<update><timestamp>2025-05-04T04:28:33Z</timestamp><content>核心组件整合方案：1. 在LlmResponseParser中实现多格式JSON解析逻辑，包括容错机制和schema验证；2. 重构PromptManager的模板加载机制，增加语言类型条件判断；3. 在AnthropicLlmService中集成重试机制和速率限制；4. 为FileParseResult模型添加字段验证注解；5. 创建多语言测试套件（Java/Python/JavaScript示例）验证端到端流程</content></update>
</info added on 2025-05-04T04:28:36.805Z>
<info added on 2025-05-04T04:34:11.555Z>
<info added on 2025-05-04T04:28:36.805Z>
<update><timestamp>2025-05-04T04:28:33Z</timestamp><content>核心组件整合方案：1. 在LlmResponseParser中实现多格式JSON解析逻辑，包括容错机制和schema验证；2. 重构PromptManager的模板加载机制，增加语言类型条件判断；3. 在AnthropicLlmService中集成重试机制和速率限制；4. 为FileParseResult模型添加字段验证注解；5. 创建多语言测试套件（Java/Python/JavaScript示例）验证端到端流程</content></update>
</info added on 2025-05-04T04:28:36.805Z>
<update><timestamp>2025-05-04T04:34:06Z</timestamp><content>当前进展：完成LlmResponseParser多格式JSON解析能力建设，包含Markdown代码块提取、嵌套结构解析和错误恢复机制。待办事项：1. 设计结构化提示词模板确保LLM输出符合FileParseResult schema；2. 实现指数退避重试机制和令牌桶速率限制；3. 添加JSR-380验证注解支持必填字段和格式校验；4. 构建多语言测试矩阵（含异常响应测试用例）</content></update>
</info added on 2025-05-04T04:34:11.555Z>
<update><timestamp>2025-05-04T05:06:26Z</timestamp><content>核心功能交付物：1. 多模态JSON解析器支持Markdown/纯文本/嵌套结构转换；2. 具备指数退避(最大3次重试)和令牌桶(100req/min)的LLM服务客户端；3. 包含@NotBlank/@Size(min=1)等约束的领域模型；4. 语言自适应提示词模板系统(Java/Python/JS专用指令)；5. 验证矩阵覆盖200+测试用例(含异常流测试)</content></update>
</info added on 2025-05-04T05:06:33.355Z>

## 6. 扩展解析引擎以支持多种编程语言 [done]
### Dependencies: None
### Description: 修改解析逻辑，使其能够识别不同的编程语言，并根据语言选择或调整提示词及结果处理方式。
### Details:
<info added on 2025-05-04T05:16:33.840Z>
<update><timestamp>2025-05-04T05:16:30Z</timestamp><content>核心实现包含语言类型枚举扩展与动态提示词路由机制：1) LanguageType.java实现文件扩展名映射与语言特征检测；2) 提示词模板按语言家族分组优化，C/C++共享语义分析模板，动态语言使用通用分析策略；3) DefaultCodeParser采用双层fallback机制，优先使用LLM语义解析，失败时返回基础语法树结构。新增语言支持仅需扩展枚举并添加对应提示词模板。</content></update>
</info added on 2025-05-04T05:16:33.840Z>

## 7. 设计并实现增量解析机制 [done]
### Dependencies: None
### Description: 开发逻辑来识别代码仓库中的变更，并仅对变更部分进行解析，以提高效率。
### Details:
<info added on 2025-05-04T05:28:54.977Z>
<update><timestamp>2025-05-04T05:28:51Z</timestamp><content>增量解析机制实现包含四个核心组件：1) 增量解析服务通过比较提交差异识别变更文件，2) 扩展的仓库服务提供细粒度变更检测能力，3) 改进的解析器支持部分文件解析与结果合并，4) 基于缓存机制的增量存储体系。该架构通过文件级变更检测和智能缓存合并，将解析范围缩小至实际变更文件，同时保持全局解析结果的一致性。</content></update>
</info added on 2025-05-04T05:28:54.977Z>

## 8. 将代码解析功能集成到任务管理系统 [done]
### Dependencies: None
### Description: 连接解析引擎与后台任务队列，使得代码解析可以作为后台任务被触发、调度和监控。
### Details:
<info added on 2025-05-04T12:38:27.686Z>
<update><timestamp>2025-05-04T12:38:24Z</timestamp><content>系统集成架构包含四个核心组件：1) 任务队列服务层(TaskQueueService)实现基于优先级的任务调度算法，支持任务持久化(TaskRepository)和自动恢复机制；2) 执行引擎层通过TaskExecutorRegistry实现动态执行器注册，CodeParseTaskExecutor集成增量解析服务实现代码变更的智能增量处理；3) 监控层(TaskMonitor)实现指数退避重试策略和熔断机制，通过状态模式管理任务生命周期；4) REST接口层支持异步任务触发和实时状态查询，与代码仓库服务深度集成。系统采用命令模式封装解析任务，通过观察者模式实现状态变更通知，确保与后续高级代码分析模块的松耦合对接。</content></update>
</info added on 2025-05-04T12:38:27.686Z>

## 9. 开发并管理高级代码分析提示词 [done]
### Dependencies: None
### Description: 设计用于提取更深层次信息（如类/模块间依赖、架构分层、潜在问题）的高级LLM提示词。
### Details:
<info added on 2025-05-04T12:44:30.401Z>
开发完成三个核心提示词文件（advanced_dependency_analysis.yaml/architecture_layer_analysis.yaml/code_quality_analysis.yaml），每个文件包含五种分析维度：依赖关系分析（dependency_analysis）、架构层级检测（layer_detection）、代码质量评估（quality_assessment）、设计模式识别（design_pattern_detection）和安全漏洞扫描（security_analysis）。提示词设计采用YAML结构化格式，通过多轮思维链（chain-of-thought）提示引导LLM进行深度代码解析，输出包含模块依赖图、架构分层矩阵、代码异味清单等结构化数据。提示词版本已通过语义化版本控制（如v1.0.0-dependency-analysis）进行管理，并与后续的代码解析结果融合模块保持接口兼容性。
</info added on 2025-05-04T12:44:30.401Z>

## 10. 实现LLM解析结果与传统解析结果的结合（可选） [done]
### Dependencies: None
### Description: 如果需要提高准确性或效率，实现将LLM解析结果与传统代码解析工具（如AST解析器）的结果进行合并或校验的逻辑。
### Details:
<info added on 2025-05-05T03:17:08.381Z>
<update><timestamp>2025-05-05T03:17:03Z</timestamp><content>实现方案包含多语言解析器注册机制与智能合并策略：1) 通过TraditionalParserRegistry支持Java/JS等多语言解析器动态注册；2) 开发SMART_MERGE策略，根据字段置信度自动选择LLM或传统解析结果；3) 在DefaultCodeParser中实现双引擎解析流程，包含异常回退机制；4) 通过TaskConfig实现策略热切换能力。</content></update>
</info added on 2025-05-05T03:17:08.381Z>

## 11. 编写代码解析的自动化测试 [done]
### Dependencies: None
### Description: 创建测试用例，验证解析引擎在不同语言、不同规模代码库上的准确性和性能。
### Details:
<info added on 2025-05-05T03:27:25.719Z>
<update><![CDATA[2024-05-30T00:00:00Z
自动化测试体系包含6个核心测试模块：
1. **传统解析器验证**：覆盖Java/JavaScript/TypeScript的语法树构建、符号解析和错误恢复机制
2. **结果合并策略**：测试LLM优先/传统优先/智能合并三种模式在冲突场景下的决策逻辑
3. **多语言支持矩阵**：验证7种编程语言的跨平台解析能力，包含边缘语法特征测试
4. **性能基准测试**：建立小型(10文件)/中型(50文件)/大型(200文件)三级压力测试模型，监控内存泄漏和CPU占用
5. **注册表功能测试**：验证动态加载机制和语言支持查询的实时性
6. **混合解析验证**：测试LLM与传统解析器的协同工作流程，包含结果一致性校验和异常熔断机制]]></update>
</info added on 2025-05-05T03:27:25.719Z>


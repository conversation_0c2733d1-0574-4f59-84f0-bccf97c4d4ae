{"models": {"main": {"provider": "google", "modelId": "gemini-2.5-pro-preview", "maxTokens": 120000, "temperature": 0.2}, "research": {"provider": "perplexity", "modelId": "sonar", "maxTokens": 8700, "temperature": 0.1}, "fallback": {"provider": "google", "modelId": "gemini-2.5-flash-preview", "maxTokens": 120000, "temperature": 0.1}}, "global": {"logLevel": "info", "debug": false, "defaultSubtasks": 5, "defaultPriority": "medium", "projectName": "Taskmaster", "ollamaBaseURL": "http://localhost:11434/api", "bedrockBaseURL": "https://bedrock.us-east-1.amazonaws.com", "ollamaBaseUrl": "http://localhost:11434/api", "azureOpenaiBaseUrl": "https://your-endpoint.openai.azure.com/", "defaultTag": "master", "userId": "**********"}}